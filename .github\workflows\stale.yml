# This workflow warns and then closes issues and PRs that have had no activity for a specified amount of time.
#
# You can adjust the behavior by modifying this file.
# For more information, see:
# https://github.com/actions/stale
name: Mark stale issues and pull requests

on:
  schedule:
  - cron: '35 8 * * *'

permissions:
  issues: write
  pull-requests: write

jobs:
  stale:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/stale@v9
      with:
        repo-token: ${{ secrets.GITHUB_TOKEN }}
        stale-issue-message: 'This issue has not seen any action for a while! Closing for now, but it can be reopened at a later date.'
        stale-pr-message: 'This PR has not seen any action for a while! Closing for now, but it can be reopened at a later date.'
        stale-issue-label: 'no-issue-activity'
        stale-pr-label: 'no-pr-activity'
        days-before-stale: 30 # Added parameter to control inactivity period
        days-before-close: 7  # Optional: Time after marking stale before closing
