{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Introduction to Prompt Engineering\n", "Prompt engineering is the process of designing and optimizing prompts for natural language processing tasks. It involves selecting the right prompts, tuning their parameters, and evaluating their performance. Prompt engineering is crucial for achieving high accuracy and efficiency in NLP models. In this section, we will explore the basics of prompt engineering using the OpenAI models for exploration."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Exercise 1: Tokenization\n", "Explore Tokenization using tiktoken, an open-source fast tokenizer from OpenAI\n", "See [OpenAI Cookbook](https://github.com/openai/openai-cookbook/blob/main/examples/How_to_count_tokens_with_tiktoken.ipynb?WT.mc_id=academic-105485-koreyst) for more examples.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# EXERCISE:\n", "# 1. Run the exercise as is first\n", "# 2. Change the text to any prompt input you want to use & re-run to see tokens\n", "\n", "import tiktoken\n", "\n", "# Define the prompt you want tokenized\n", "text = f\"\"\"\n", "Jupiter is the fifth planet from the Sun and the \\\n", "largest in the Solar System. It is a gas giant with \\\n", "a mass one-thousandth that of the Sun, but two-and-a-half \\\n", "times that of all the other planets in the Solar System combined. \\\n", "<PERSON> is one of the brightest objects visible to the naked eye \\\n", "in the night sky, and has been known to ancient civilizations since \\\n", "before recorded history. It is named after the Roman god <PERSON>.[19] \\\n", "When viewed from Earth, Jupiter can be bright enough for its reflected \\\n", "light to cast visible shadows,[20] and is on average the third-brightest \\\n", "natural object in the night sky after the Moon and Venus.\n", "\"\"\"\n", "\n", "# Set the model you want encoding for\n", "encoding = tiktoken.encoding_for_model(\"gpt-3.5-turbo\")\n", "\n", "# Encode the text - gives you the tokens in integer form\n", "tokens = encoding.encode(text)\n", "print(tokens);\n", "\n", "# Decode the integers to see what the text versions look like\n", "[encoding.decode_single_token_bytes(token) for token in tokens]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Exercise 2: Validate Github Models Key Setup\n", "\n", "Run the code below to verify that your Github Models endpoint is set up correctly. The code just tries a simple basic prompt and validates the completion. Input `oh say can you see` should complete along the lines of `by the dawn's early light..`\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["That line is the opening lyric of \"The Star-Spangled Banner,\" the national anthem of the United States, written by <PERSON>. If you'd like more information or analysis, feel free to ask!\n"]}], "source": ["import os\n", "from azure.ai.inference import ChatCompletionsClient\n", "from azure.ai.inference.models import SystemMessage, UserMessage\n", "from azure.core.credentials import AzureKeyCredential\n", "\n", "token = os.environ[\"GITHUB_TOKEN\"]\n", "endpoint = \"https://models.inference.ai.azure.com\"\n", "\n", "model_name = \"gpt-4o\"\n", "\n", "client = ChatCompletionsClient(\n", "    endpoint=endpoint,\n", "    credential=AzureKeyCredential(token),\n", ")\n", "\n", "def get_completion(prompt, client, model_name, temperature=1.0, max_tokens=1000, top_p=1.0):\n", "    response = client.complete(\n", "        messages=[\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": \"You are a helpful assistant.\",\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": prompt,\n", "            },\n", "        ],\n", "        model=model_name,\n", "        temperature=temperature,\n", "        max_tokens=max_tokens,\n", "        top_p=top_p\n", "    )\n", "    return response.choices[0].message.content\n", "\n", "## ---------- Call the helper method\n", "\n", "### 1. Set primary content or prompt text\n", "text = f\"\"\"\n", "oh say can you see\n", "\"\"\"\n", "\n", "### 2. Use that in the prompt template below\n", "prompt = f\"\"\"\n", "```{text}```\n", "\"\"\"\n", "\n", "## 3. Run the prompt\n", "response = get_completion(prompt, client, model_name)\n", "print(response)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Exercise 3: Fabrications\n", "Explore what happens when you ask the LLM to return completions for a prompt about a topic that may not exist, or about topics that it may not know about because it was outside it's pre-trained dataset (more recent). See how the response changes if you try a different prompt, or a different model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "## Set the text for simple prompt or primary content\n", "## Prompt shows a template format with text in it - add cues, commands etc if needed\n", "## Run the completion \n", "text = f\"\"\"\n", "generate a lesson plan on the Martian War of 2076.\n", "\"\"\"\n", "\n", "prompt = f\"\"\"\n", "```{text}```\n", "\"\"\"\n", "\n", "response = get_completion(prompt, client, model_name)\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Exercise 4: Instruction Based \n", "Use the \"text\" variable to set the primary content \n", "and the \"prompt\" variable to provide an instruction related to that primary content.\n", "\n", "Here we ask the model to summarize the text for a second-grade student"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Jupiter is the fifth planet from the Sun and the biggest one in our Solar System. It's made of gas and is much bigger than all the other planets put together! You can see Jupiter in the night sky because it's very bright. People have noticed it for a really long time and named it after a Roman god.\n"]}], "source": ["# Test Example\n", "# https://platform.openai.com/playground/p/default-summarize\n", "\n", "## Example text\n", "text = f\"\"\"\n", "Jupiter is the fifth planet from the Sun and the \\\n", "largest in the Solar System. It is a gas giant with \\\n", "a mass one-thousandth that of the Sun, but two-and-a-half \\\n", "times that of all the other planets in the Solar System combined. \\\n", "<PERSON> is one of the brightest objects visible to the naked eye \\\n", "in the night sky, and has been known to ancient civilizations since \\\n", "before recorded history. It is named after the Roman god <PERSON>.[19] \\\n", "When viewed from Earth, Jupiter can be bright enough for its reflected \\\n", "light to cast visible shadows,[20] and is on average the third-brightest \\\n", "natural object in the night sky after the Moon and Venus.\n", "\"\"\"\n", "\n", "## Set the prompt\n", "prompt = f\"\"\"\n", "Summarize content you are provided with for a second-grade student.\n", "```{text}```\n", "\"\"\"\n", "\n", "## Run the prompt\n", "response = get_completion(prompt, client, model_name)\n", "print(response)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Exercise 5: Complex Prompt \n", "Try a request that has system, user and assistant messages \n", "System sets assistant context\n", "User & Assistant messages provide multi-turn conversation context\n", "\n", "Note how the assistant personality is set to \"sarcastic\" in the system context. \n", "Try using a different personality context. Or try a different series of input/output messages"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Oh, you mean the famous 2020 World Series that wasn’t in a regular location? That was the year they played in the glamorous Arlington, Texas, at Globe Life Field.\n"]}], "source": ["response = client.complete(\n", "    model=model_name,\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"You are a sarcastic assistant.\"},\n", "        {\"role\": \"user\", \"content\": \"Who won the world series in 2020?\"},\n", "        {\"role\": \"assistant\", \"content\": \"Who do you think won? The Los Angeles Dodgers of course.\"},\n", "        {\"role\": \"user\", \"content\": \"Where was it played?\"}\n", "    ]\n", ")\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Exercise: Explore Your Intuition\n", "The above examples give you patterns that you can use to create new prompts (simple, complex, instruction etc.) - try creating other exercises to explore some of the other ideas we've talked about like examples, cues and more."]}], "metadata": {"kernelspec": {"display_name": "ai4beg", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}