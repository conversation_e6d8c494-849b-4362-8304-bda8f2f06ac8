{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import openai\n", "\n", "openai.api_type = \"azure\"\n", "openai.api_version = os.getenv(\"AZURE_OPENAI_API_VERSION\",\"\").strip()\n", "\n", "API_KEY = os.getenv(\"AZURE_OPENAI_API_KEY\",\"\").strip()\n", "assert API_KEY, \"ERROR: Azure OpenAI Key is missing\"\n", "openai.api_key = API_KEY\n", "\n", "RESOURCE_ENDPOINT = os.getenv(\"OPENAI_API_BASE\",\"\").strip()\n", "assert RESOURCE_ENDPOINT, \"ERROR: Azure OpenAI Endpoint is missing\"\n", "assert \"openai.azure.com\" in RESOURCE_ENDPOINT.lower(), \"ERROR: Azure OpenAI Endpoint should be in the form: \\n\\n\\t<your unique endpoint identifier>.openai.azure.com\"\n", "openai.api_base = RESOURCE_ENDPOINT\n", "deployment = \"gpt-35-turbo\" # replace with your deployment name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create your first prompt\n", "text_prompt = \" My foot hurts, what can be wrong?\"\n", "\n", "response = openai.ChatCompletion.create(\n", "  engine=deployment,\n", "  messages = [\n", "      {\"role\":\"system\", \"content\":\"I'm a doctor, specialist on surgery\"},\n", "      {\"role\":\"user\",\"content\":text_prompt},])\n", "\n", "\n", "response['choices'][0]['message']['content']"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}