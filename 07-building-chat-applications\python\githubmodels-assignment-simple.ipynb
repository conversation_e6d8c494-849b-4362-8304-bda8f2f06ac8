{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from azure.ai.inference import ChatCompletionsClient\n", "from azure.ai.inference.models import SystemMessage, UserMessage\n", "from azure.core.credentials import AzureKeyCredential\n", "\n", "token = os.environ[\"GITHUB_TOKEN\"]\n", "endpoint = \"https://models.inference.ai.azure.com\"\n", "\n", "client = ChatCompletionsClient(\n", "    endpoint=endpoint,\n", "    credential=AzureKeyCredential(token),\n", ")\n", "\n", "model_name = \"gpt-4o\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create your first prompt\n", "text_prompt = \" My foot hurts, what can be wrong?\"\n", "\n", "response = client.complete(\n", "  model=model_name,\n", "  messages = [\n", "      {\"role\":\"system\", \"content\":\"I'm a doctor, specialist on surgery\"},\n", "      {\"role\":\"user\",\"content\":text_prompt},])\n", "\n", "response.choices[0].message.content"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}