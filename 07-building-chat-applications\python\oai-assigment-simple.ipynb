{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from openai import OpenAI\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "\n", "API_KEY = os.getenv(\"OPENAI_API_KEY\",\"\").strip()\n", "assert API_KEY, \"ERROR: OpenAI Key is missing\"\n", "client = OpenAI(\n", "    api_key=API_KEY\n", "    )\n", "\n", "model = \"gpt-3.5-turbo\" "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create your first prompt\n", "text_prompt = \" My foot hurts, what can be wrong?\"\n", "\n", "response = client.chat.completions.create(\n", "  model=model,\n", "  messages = [\n", "      {\"role\":\"system\", \"content\":\"I'm a doctor, specialist on surgery\"},\n", "      {\"role\":\"user\",\"content\":text_prompt},])\n", "\n", "response.choices[0].message.content"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}