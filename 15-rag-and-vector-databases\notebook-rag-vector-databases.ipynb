{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Retrieval Augmented Generation (RAG) and Vector Databases"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: getenv in /usr/local/python/3.10.13/lib/python3.10/site-packages (0.2.0)\n", "Requirement already satisfied: openai==1.12.0 in /usr/local/python/3.10.13/lib/python3.10/site-packages (1.12.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /home/<USER>/.local/lib/python3.10/site-packages (from openai==1.12.0) (4.2.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /usr/local/python/3.10.13/lib/python3.10/site-packages (from openai==1.12.0) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /home/<USER>/.local/lib/python3.10/site-packages (from openai==1.12.0) (0.26.0)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in /usr/local/python/3.10.13/lib/python3.10/site-packages (from openai==1.12.0) (2.6.1)\n", "Requirement already satisfied: sniffio in /home/<USER>/.local/lib/python3.10/site-packages (from openai==1.12.0) (1.3.0)\n", "Requirement already satisfied: tqdm>4 in /usr/local/python/3.10.13/lib/python3.10/site-packages (from openai==1.12.0) (4.64.0)\n", "Requirement already satisfied: typing-extensions<5,>=4.7 in /home/<USER>/.local/lib/python3.10/site-packages (from openai==1.12.0) (4.9.0)\n", "Requirement already satisfied: idna>=2.8 in /home/<USER>/.local/lib/python3.10/site-packages (from anyio<5,>=3.5.0->openai==1.12.0) (3.6)\n", "Requirement already satisfied: exceptiongroup>=1.0.2 in /home/<USER>/.local/lib/python3.10/site-packages (from anyio<5,>=3.5.0->openai==1.12.0) (1.2.0)\n", "Requirement already satisfied: certifi in /home/<USER>/.local/lib/python3.10/site-packages (from httpx<1,>=0.23.0->openai==1.12.0) (2024.2.2)\n", "Requirement already satisfied: httpcore==1.* in /home/<USER>/.local/lib/python3.10/site-packages (from httpx<1,>=0.23.0->openai==1.12.0) (1.0.2)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /home/<USER>/.local/lib/python3.10/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai==1.12.0) (0.14.0)\n", "Requirement already satisfied: annotated-types>=0.4.0 in /usr/local/python/3.10.13/lib/python3.10/site-packages (from pydantic<3,>=1.9.0->openai==1.12.0) (0.6.0)\n", "Requirement already satisfied: pydantic-core==2.16.2 in /usr/local/python/3.10.13/lib/python3.10/site-packages (from pydantic<3,>=1.9.0->openai==1.12.0) (2.16.2)\n"]}], "source": ["!pip install getenv openai==1.12.0"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "import openai"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creating our Knowledge base\n", "\n", "Creating a Azure Cosmos DB database\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: azure-cosmos in /usr/local/python/3.10.13/lib/python3.10/site-packages (4.5.1)\n", "Requirement already satisfied: azure-core<2.0.0,>=1.23.0 in /usr/local/python/3.10.13/lib/python3.10/site-packages (from azure-cosmos) (1.30.0)\n", "Requirement already satisfied: requests>=2.21.0 in /home/<USER>/.local/lib/python3.10/site-packages (from azure-core<2.0.0,>=1.23.0->azure-cosmos) (2.31.0)\n", "Requirement already satisfied: six>=1.11.0 in /home/<USER>/.local/lib/python3.10/site-packages (from azure-core<2.0.0,>=1.23.0->azure-cosmos) (1.16.0)\n", "Requirement already satisfied: typing-extensions>=4.6.0 in /home/<USER>/.local/lib/python3.10/site-packages (from azure-core<2.0.0,>=1.23.0->azure-cosmos) (4.9.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /home/<USER>/.local/lib/python3.10/site-packages (from requests>=2.21.0->azure-core<2.0.0,>=1.23.0->azure-cosmos) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /home/<USER>/.local/lib/python3.10/site-packages (from requests>=2.21.0->azure-core<2.0.0,>=1.23.0->azure-cosmos) (3.6)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/python/3.10.13/lib/python3.10/site-packages (from requests>=2.21.0->azure-core<2.0.0,>=1.23.0->azure-cosmos) (2.0.7)\n", "Requirement already satisfied: certifi>=2017.4.17 in /home/<USER>/.local/lib/python3.10/site-packages (from requests>=2.21.0->azure-core<2.0.0,>=1.23.0->azure-cosmos) (2024.2.2)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install azure-cosmos"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["## create your cosmoss db on Azure CLI using the following commands\n", "## az login\n", "## az group create -n <resource-group-name> -l <location>\n", "## az cosmosdb create -n <cosmos-db-name> -r <resource-group-name>\n", "## az cosmosdb list-keys -n <cosmos-db-name> -g <resource-group-name>\n", "\n", "## Once done navigate to data explorer and create a new database and a new container\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from azure.cosmos import CosmosClient\n", "\n", "# Initialize Cosmos Client\n", "url = os.getenv('COSMOS_DB_ENDPOINT')\n", "key = os.getenv('COSMOS_DB_KEY')\n", "client = CosmosClient(url, credential=key)\n", "\n", "# Select database\n", "database_name = 'rag-cosmos-db'\n", "database = client.get_database_client(database_name)\n", "\n", "# Select container\n", "container_name = 'data'\n", "container = database.get_container_client(container_name)\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_25612/20051717.py:15: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df = df.append({'path': path, 'text': file_content}, ignore_index=True)\n", "/tmp/ipykernel_25612/20051717.py:15: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df = df.append({'path': path, 'text': file_content}, ignore_index=True)\n", "/tmp/ipykernel_25612/20051717.py:15: FutureWarning: The frame.append method is deprecated and will be removed from pandas in a future version. Use pandas.concat instead.\n", "  df = df.append({'path': path, 'text': file_content}, ignore_index=True)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>path</th>\n", "      <th>text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>data/own_framework.md</td>\n", "      <td># Introduction to Neural Networks. Multi-Layer...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>data/perceptron.md</td>\n", "      <td># Introduction to Neural Networks: Perceptron\\...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    path                                               text\n", "0     data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...\n", "1  data/own_framework.md  # Introduction to Neural Networks. Multi-Layer...\n", "2     data/perceptron.md  # Introduction to Neural Networks: Perceptron\\..."]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "# Initialize an empty DataFrame\n", "df = pd.DataFrame(columns=['path', 'text'])\n", "\n", "\n", "# splitting our data into chunks\n", "data_paths= [\"data/frameworks.md?WT.mc_id=academic-105485-koreyst\", \"data/own_framework.md?WT.mc_id=academic-105485-koreyst\", \"data/perceptron.md?WT.mc_id=academic-105485-koreyst\"]\n", "\n", "for path in data_paths:\n", "    with open(path, 'r', encoding='utf-8') as file:\n", "        file_content = file.read()\n", "\n", "    # Append the file path and text to the DataFrame\n", "    df = df.append({'path': path, 'text': file_content}, ignore_index=True)\n", "\n", "df.head()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>path</th>\n", "      <th>text</th>\n", "      <th>chunks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td>[# Neural Network Frameworks As we have learne...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>data/own_framework.md</td>\n", "      <td># Introduction to Neural Networks. Multi-Layer...</td>\n", "      <td>[# Introduction to Neural Networks. Multi-Laye...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>data/perceptron.md</td>\n", "      <td># Introduction to Neural Networks: Perceptron\\...</td>\n", "      <td>[# Introduction to Neural Networks: Perceptron...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    path                                               text  \\\n", "0     data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "1  data/own_framework.md  # Introduction to Neural Networks. Multi-Layer...   \n", "2     data/perceptron.md  # Introduction to Neural Networks: Perceptron\\...   \n", "\n", "                                              chunks  \n", "0  [# Neural Network Frameworks As we have learne...  \n", "1  [# Introduction to Neural Networks. Multi-Laye...  \n", "2  [# Introduction to Neural Networks: Perceptron...  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["def split_text(text, max_length, min_length):\n", "    words = text.split()\n", "    chunks = []\n", "    current_chunk = []\n", "\n", "    for word in words:\n", "        current_chunk.append(word)\n", "        if len(' '.join(current_chunk)) < max_length and len(' '.join(current_chunk)) > min_length:\n", "            chunks.append(' '.join(current_chunk))\n", "            current_chunk = []\n", "\n", "    # If the last chunk didn't reach the minimum length, add it anyway\n", "    if current_chunk:\n", "        chunks.append(' '.join(current_chunk))\n", "\n", "    return chunks\n", "\n", "# Assuming analyzed_df is a pandas DataFrame and 'output_content' is a column in that DataFrame\n", "splitted_df = df.copy()\n", "splitted_df['chunks'] = splitted_df['text'].apply(lambda x: split_text(x, 400, 300))\n", "\n", "splitted_df"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>path</th>\n", "      <th>text</th>\n", "      <th>chunks</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td># Neural Network Frameworks As we have learned...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td>descent optimization While the `numpy` library...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td>should give us the opportunity to compute grad...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td>those computations on GPUs is very important. ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td>API, there is also higher-level API, called Ke...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 path                                               text  \\\n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "\n", "                                              chunks  \n", "0  # Neural Network Frameworks As we have learned...  \n", "0  descent optimization While the `numpy` library...  \n", "0  should give us the opportunity to compute grad...  \n", "0  those computations on GPUs is very important. ...  \n", "0  API, there is also higher-level API, called Ke...  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Assuming 'chunks' is a column of lists in the DataFrame splitted_df, we will split the chunks into different rows\n", "flattened_df = splitted_df.explode('chunks')\n", "\n", "flattened_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Converting our text to embeddings\n", "\n", "Converting out text  to embeddings, and storing them in our database in chunks"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["openai.api_type = \"azure\"\n", "openai.api_key = os.getenv(\"AZURE_OPENAI_API_KEY\") \n", "openai.api_base = os.getenv(\"AZURE_OPENAI_ENDPOINT\") \n", "openai.api_version = \"2023-07-01-preview\"\n", "\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "client = OpenAI(api_key=os.getenv(\"AZURE_OPENAI_EMBEDDINGS_DEPLOYMENT\"))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["[-0.016977494582533836,\n", " 0.0028917337767779827,\n", " 0.025520483031868935,\n", " -0.03886381536722183,\n", " 0.006847951095551252,\n", " 0.003939266782253981,\n", " -0.006163155660033226,\n", " -0.0032409115228801966,\n", " -0.002920549362897873,\n", " -0.029344486072659492,\n", " 0.034931328147649765,\n", " 0.020408250391483307,\n", " 0.0015382464043796062,\n", " 0.003086663084104657,\n", " -0.014618001878261566,\n", " -0.010983842425048351,\n", " 0.02225244976580143,\n", " 0.009017598815262318,\n", " -0.02931736595928669,\n", " -0.02063877508044243,\n", " -0.03550086170434952,\n", " -0.003715521888807416,\n", " 0.01288906391710043,\n", " -0.034226194024086,\n", " -0.030429311096668243,\n", " -0.0014907853910699487,\n", " 0.015296016819775105,\n", " -0.04358280077576637,\n", " -0.007553086616098881,\n", " -0.014156951569020748,\n", " 0.01970311440527439,\n", " 0.01257039699703455,\n", " -0.012665319256484509,\n", " -0.015553662553429604,\n", " -0.004668132867664099,\n", " 0.011058423668146133,\n", " 0.0012356822844594717,\n", " 0.00818364042788744,\n", " -0.0005224952474236488,\n", " -0.00196624337695539,\n", " 0.04032832756638527,\n", " 0.011255048215389252,\n", " -0.009871897287666798,\n", " -0.00762766832485795,\n", " -0.0052071548998355865,\n", " 0.010685515590012074,\n", " -0.02524927631020546,\n", " -0.03335833549499512,\n", " -0.006258077919483185,\n", " 0.004135891329497099,\n", " 0.013024667277932167,\n", " 0.02397460862994194,\n", " -0.044043850153684616,\n", " -0.03303288668394089,\n", " -0.02108626440167427,\n", " 0.012129687704145908,\n", " -0.026347661390900612,\n", " 0.012760241515934467,\n", " 0.0245983824133873,\n", " -0.025845929980278015,\n", " 0.0023594920057803392,\n", " 0.019825156778097153,\n", " -0.021316789090633392,\n", " 0.003123953938484192,\n", " -0.0074107032269239426,\n", " -0.019499709829688072,\n", " -0.0014772251015529037,\n", " 0.025764567777514458,\n", " 0.003351088846102357,\n", " -0.016421521082520485,\n", " 0.0035663587041199207,\n", " 0.013248411938548088,\n", " -0.02557472325861454,\n", " 0.00017797891632653773,\n", " 0.015323137864470482,\n", " 0.00425115367397666,\n", " -0.013668781146407127,\n", " 0.0017543636495247483,\n", " -0.01627235859632492,\n", " 0.002678158925846219,\n", " 0.002840882632881403,\n", " -0.03026658669114113,\n", " -0.01352639775723219,\n", " 0.0014560370473191142,\n", " 0.0004065970715600997,\n", " -0.00403757905587554,\n", " 0.012068665586411953,\n", " 0.009912578389048576,\n", " 0.008475187234580517,\n", " -0.0051529137417674065,\n", " 0.04401673004031181,\n", " -0.0017255480634048581,\n", " 0.025127233937382698,\n", " 0.008570108562707901,\n", " 0.0071259369142353535,\n", " 0.010773657821118832,\n", " -0.012665319256484509,\n", " 0.006230957340449095,\n", " -0.004396927077323198,\n", " -0.010000720620155334,\n", " -0.012482254765927792,\n", " 0.017926715314388275,\n", " -0.014767165295779705,\n", " -0.01936410740017891,\n", " -0.02236093208193779,\n", " 0.0006835238309577107,\n", " 0.00395960733294487,\n", " 0.0005110537749715149,\n", " 0.010224465280771255,\n", " -0.001116182073019445,\n", " 0.019025098532438278,\n", " 0.02257789671421051,\n", " -0.009892238304018974,\n", " -0.024476340040564537,\n", " -0.01257039699703455,\n", " -0.009932918474078178,\n", " 0.04116906598210335,\n", " -0.008475187234580517,\n", " -0.007932774722576141,\n", " -0.016231678426265717,\n", " 0.012434793636202812,\n", " 0.024110211059451103,\n", " 0.03720945864915848,\n", " -0.006383510772138834,\n", " 0.01756058633327484,\n", " -0.0012060190783813596,\n", " 0.027147717773914337,\n", " 0.0016255407826974988,\n", " -0.018062317743897438,\n", " -0.02481534704566002,\n", " -0.01177711971104145,\n", " 0.008698931895196438,\n", " 0.0028052867855876684,\n", " 0.021330349147319794,\n", " -0.01547230128198862,\n", " 0.00404096907004714,\n", " 0.015187534503638744,\n", " 0.010502451099455357,\n", " -0.03102596290409565,\n", " 0.005342757795006037,\n", " 0.01156015507876873,\n", " 0.007471724878996611,\n", " -0.027147717773914337,\n", " -0.013119589537382126,\n", " -0.014943449757993221,\n", " 0.03693825379014015,\n", " 0.03061915561556816,\n", " 0.02716127783060074,\n", " 0.013858625665307045,\n", " -0.009919358417391777,\n", " -0.008380264975130558,\n", " -0.010814337991178036,\n", " -0.013770483434200287,\n", " -0.02021840587258339,\n", " 0.006678447127342224,\n", " 0.0002608238719403744,\n", " -0.00422403309494257,\n", " 0.03384650498628616,\n", " -0.011688977479934692,\n", " 0.014997690916061401,\n", " 0.017411423847079277,\n", " 0.01284160278737545,\n", " -0.01692325249314308,\n", " -0.0025781518779695034,\n", " 0.01593335159122944,\n", " 0.012333091348409653,\n", " 0.04537275806069374,\n", " -0.0018357255030423403,\n", " 0.02419157326221466,\n", " -0.0015789272729307413,\n", " 0.013817944563925266,\n", " -0.0016721542924642563,\n", " -0.005529211834073067,\n", " 0.013865405693650246,\n", " 0.02066589519381523,\n", " 0.0075869872234761715,\n", " 0.027459604665637016,\n", " 0.005525821819901466,\n", " -0.03447027876973152,\n", " -0.014767165295779705,\n", " 0.018008077517151833,\n", " 0.018455566838383675,\n", " 0.03029370680451393,\n", " 0.01181780081242323,\n", " -0.0005953818908892572,\n", " -0.012868723832070827,\n", " 0.023798324167728424,\n", " -0.009810876101255417,\n", " 0.00454948004335165,\n", " -0.03276168182492256,\n", " -0.011255048215389252,\n", " 0.005078332033008337,\n", " -0.0027985067572444677,\n", " 0.005902119912207127,\n", " -0.6274621486663818,\n", " -0.0018628460820764303,\n", " 0.03704673796892166,\n", " -0.03390074893832207,\n", " -0.0060411132872104645,\n", " -0.013431476429104805,\n", " -0.013180610723793507,\n", " 0.005593623500317335,\n", " -0.0088345343247056,\n", " 0.034741487354040146,\n", " -0.0027188400272279978,\n", " 0.012482254765927792,\n", " 0.0006004669703543186,\n", " -0.01692325249314308,\n", " 0.010000720620155334,\n", " -0.022442294284701347,\n", " 0.009180322289466858,\n", " -0.03395498916506767,\n", " -0.015553662553429604,\n", " 0.0008771818247623742,\n", " -0.008631129749119282,\n", " 0.0235542394220829,\n", " -0.021113384515047073,\n", " 0.020516732707619667,\n", " 0.0024442437570542097,\n", " 0.013390795327723026,\n", " -0.0029832657892256975,\n", " 0.003837564494460821,\n", " 0.023594919592142105,\n", " 0.029154643416404724,\n", " -0.014278994873166084,\n", " 0.005786857567727566,\n", " 0.014278994873166084,\n", " 8.54934478411451e-05,\n", " 0.040626656264066696,\n", " -0.008861655369400978,\n", " -0.019092900678515434,\n", " 0.03780611231923103,\n", " 0.009654932655394077,\n", " 0.03501269221305847,\n", " -0.030049622058868408,\n", " -0.013953547924757004,\n", " 0.02526283636689186,\n", " 0.011214367114007473,\n", " -0.009804096072912216,\n", " 0.02996825985610485,\n", " 0.011960183270275593,\n", " 0.00473254406824708,\n", " 0.004854586906731129,\n", " -0.013011106289923191,\n", " 0.009105741046369076,\n", " 0.00035447467234916985,\n", " 0.012536495923995972,\n", " 0.023377954959869385,\n", " 0.004366416018456221,\n", " -0.0022798252757638693,\n", " 0.006766588892787695,\n", " -0.02237449400126934,\n", " 0.006749638821929693,\n", " 0.005980091635137796,\n", " 0.010732976719737053,\n", " -0.008705711923539639,\n", " -0.04935948923230171,\n", " -0.013329774141311646,\n", " -0.018916616216301918,\n", " 0.007037795148789883,\n", " -0.021791400387883186,\n", " 0.029073281213641167,\n", " -0.02280842326581478,\n", " -0.010895700193941593,\n", " 0.019743794575333595,\n", " 0.02484246715903282,\n", " 0.019526829943060875,\n", " 0.006383510772138834,\n", " 0.0073564620688557625,\n", " 0.02203548513352871,\n", " 0.016340160742402077,\n", " -0.003174804849550128,\n", " 0.0022137188352644444,\n", " 0.011092324741184711,\n", " 0.002408648142591119,\n", " 0.003939266782253981,\n", " -0.018903056159615517,\n", " -0.014197632670402527,\n", " 0.005871609319001436,\n", " 0.00835314393043518,\n", " -0.016448643058538437,\n", " -0.007946334779262543,\n", " 0.007593767251819372,\n", " 4.547467324300669e-05,\n", " 0.007437823805958033,\n", " 0.02267281897366047,\n", " -0.01722157932817936,\n", " -0.06964569538831711,\n", " -0.00041549603338353336,\n", " 0.010760096833109856,\n", " 0.00765478890389204,\n", " 0.013302653096616268,\n", " 0.01776399090886116,\n", " -0.01295008510351181,\n", " -0.008861655369400978,\n", " -0.006037723273038864,\n", " 0.02130322903394699,\n", " -0.00401723850518465,\n", " -0.010461770929396152,\n", " 0.016855452209711075,\n", " -0.014414597302675247,\n", " 0.04699999466538429,\n", " 0.03219214826822281,\n", " -0.025615405291318893,\n", " -0.03145989403128624,\n", " -0.027676569297909737,\n", " -0.010651614516973495,\n", " 0.02246941439807415,\n", " 0.0001386752410326153,\n", " -0.030998842790722847,\n", " -0.008149739354848862,\n", " 0.00818364042788744,\n", " -0.008149739354848862,\n", " -0.019214943051338196,\n", " 0.00904471892863512,\n", " 0.013451816514134407,\n", " -0.006810660008341074,\n", " -0.0005280041368678212,\n", " 0.018048757687211037,\n", " 0.006268247961997986,\n", " -0.03102596290409565,\n", " -0.018225042149424553,\n", " -0.02687651291489601,\n", " -0.012265290133655071,\n", " -0.003701961599290371,\n", " -0.02280842326581478,\n", " 0.0007165770512074232,\n", " -0.018048757687211037,\n", " 0.02610357478260994,\n", " 0.010699075646698475,\n", " 0.01753346621990204,\n", " -0.021465953439474106,\n", " 0.0011017742799594998,\n", " -0.033656660467386246,\n", " -0.0016780869336798787,\n", " -0.02108626440167427,\n", " 0.024435658007860184,\n", " -0.02149307355284691,\n", " -0.015051932074129581,\n", " 0.005488530732691288,\n", " -0.018130119889974594,\n", " -0.0056444741785526276,\n", " 0.00861078966408968,\n", " 0.003969777375459671,\n", " 0.0012085615890100598,\n", " 0.010360068641602993,\n", " -4.751401502289809e-05,\n", " 0.0037867133505642414,\n", " 0.01636728085577488,\n", " -0.01992007903754711,\n", " -0.017709750682115555,\n", " -0.034280434250831604,\n", " -0.004712203983217478,\n", " -0.03346681594848633,\n", " -0.027459604665637016,\n", " 0.028612229973077774,\n", " -0.029019039124250412,\n", " 0.01798095554113388,\n", " -0.025656085461378098,\n", " -0.028313903138041496,\n", " -0.019621752202510834,\n", " 0.004630842246115208,\n", " 0.004786785691976547,\n", " -0.02428649552166462,\n", " -0.013085688464343548,\n", " -0.03908077999949455,\n", " -0.0011017742799594998,\n", " 0.014794286340475082,\n", " 0.010244805365800858,\n", " 0.006590305361896753,\n", " -0.011078763753175735,\n", " -0.01616387628018856,\n", " 0.016950374469161034,\n", " -0.018482686951756477,\n", " 0.007302220910787582,\n", " -0.0160553939640522,\n", " -0.0256967656314373,\n", " -0.010007500648498535,\n", " 0.03655856475234032,\n", " -0.006583524867892265,\n", " 0.009865117259323597,\n", " 0.0012543275952339172,\n", " -0.046376220881938934,\n", " -0.013451816514134407,\n", " -0.005413949489593506,\n", " 0.0246119424700737,\n", " -0.02858510985970497,\n", " 0.010102422907948494,\n", " -0.012116126716136932,\n", " 0.00023794086882844567,\n", " -0.0025815418921411037,\n", " 0.019540389999747276,\n", " -0.021872762590646744,\n", " 0.006773369386792183,\n", " 0.0028764784801751375,\n", " -0.015228215605020523,\n", " 0.01257717702537775,\n", " -0.019865836948156357,\n", " 0.012767021544277668,\n", " 0.002637478057295084,\n", " 0.019621752202510834,\n", " -0.026184936985373497,\n", " -0.016109634190797806,\n", " 0.002761215902864933,\n", " 0.006319099105894566,\n", " -0.016177436336874962,\n", " -0.009837997145950794,\n", " -0.013675561174750328,\n", " -0.017492786049842834,\n", " 0.03200230374932289,\n", " 0.02085573971271515,\n", " 0.0071259369142353535,\n", " -0.007844632491469383,\n", " -0.006786929443478584,\n", " -0.012556836940348148,\n", " -0.005000360310077667,\n", " 0.02484246715903282,\n", " -0.004864757414907217,\n", " -0.0030307266861200333,\n", " 0.021045584231615067,\n", " -0.015770627185702324,\n", " 0.009187102317810059,\n", " -0.011695757508277893,\n", " -0.04835602641105652,\n", " -0.0017933495109900832,\n", " 0.0181843601167202,\n", " -0.0021594776771962643,\n", " 0.014224753715097904,\n", " 0.028341025114059448,\n", " 0.0029120740946382284,\n", " 0.0034273655619472265,\n", " 0.001641643699258566,\n", " 0.06405885517597198,\n", " -0.0035866990219801664,\n", " -0.00039642685442231596,\n", " 0.009431187994778156,\n", " 0.010109202936291695,\n", " -0.01347893662750721,\n", " 0.01841488666832447,\n", " 0.01363488007336855,\n", " 0.04762376844882965,\n", " 0.014767165295779705,\n", " -0.0181843601167202,\n", " -0.012082226574420929,\n", " -0.009648152627050877,\n", " 0.002545946044847369,\n", " -0.00409182021394372,\n", " 0.013445036485791206,\n", " 0.007166618015617132,\n", " -0.015214655548334122,\n", " 0.009010818786919117,\n", " 0.0176419485360384,\n", " 0.015906229615211487,\n", " 0.025167914107441902,\n", " 0.017248699441552162,\n", " 0.006454702466726303,\n", " 0.0020103142596781254,\n", " -0.024462779983878136,\n", " -0.003459571162238717,\n", " -0.006383510772138834,\n", " 0.013804384507238865,\n", " -0.014306114986538887,\n", " -0.016014713793992996,\n", " -0.0036002593114972115,\n", " -0.013289093039929867,\n", " -0.020150603726506233,\n", " 0.012753461487591267,\n", " -0.018062317743897438,\n", " -0.00012998818419873714,\n", " 0.014170512557029724,\n", " -0.003539237892255187,\n", " 0.007695469539612532,\n", " -0.004685083404183388,\n", " 0.01594691164791584,\n", " -0.011214367114007473,\n", " -0.009492209181189537,\n", " 0.01316704973578453,\n", " 0.0181843601167202,\n", " 0.004952899180352688,\n", " -0.014170512557029724,\n", " -0.00808193814009428,\n", " 0.004498629365116358,\n", " 0.003993507940322161,\n", " -0.0069225323386490345,\n", " 0.006756418850272894,\n", " -0.005993652157485485,\n", " -0.011302509345114231,\n", " -0.008326023817062378,\n", " 0.0026866341941058636,\n", " -0.0004135891213081777,\n", " 0.029724175110459328,\n", " -0.009973599575459957,\n", " 0.008481967262923717,\n", " -0.027418924495577812,\n", " 0.03400922939181328,\n", " 0.010699075646698475,\n", " -0.011160125955939293,\n", " -0.015038371086120605,\n", " 0.036856893450021744,\n", " 0.011322849430143833,\n", " -0.023486437276005745,\n", " -0.009966819547116756,\n", " 0.0224829763174057,\n", " -0.026930753141641617,\n", " 0.0088277542963624,\n", " -0.00797345582395792,\n", " 0.012394113466143608,\n", " 0.005105452612042427,\n", " 0.018021637573838234,\n", " -0.011078763753175735,\n", " 0.009722733870148659,\n", " 0.005488530732691288,\n", " 0.008414165116846561,\n", " 0.014360356144607067,\n", " 0.01193306315690279,\n", " -0.013811164535582066,\n", " -0.016435083001852036,\n", " 0.019282745197415352,\n", " 0.03219214826822281,\n", " 0.02503231167793274,\n", " -0.020096363499760628,\n", " 0.016353720799088478,\n", " -0.02173715829849243,\n", " -0.01182458084076643,\n", " -0.017723310738801956,\n", " -0.025018751621246338,\n", " 0.01830640435218811,\n", " -0.009688833728432655,\n", " 0.009709173813462257,\n", " 0.009526110254228115,\n", " 0.015051932074129581,\n", " -0.0256967656314373,\n", " 0.017397863790392876,\n", " 0.029073281213641167,\n", " -0.007648008409887552,\n", " -0.0015696046175435185,\n", " 0.004454558249562979,\n", " -0.002530690748244524,\n", " 0.019187822937965393,\n", " 0.009546450339257717,\n", " 0.014251873828470707,\n", " -0.002032349817454815,\n", " 0.004807125777006149,\n", " 0.014468838460743427,\n", " 0.00032269273651763797,\n", " 0.025317078456282616,\n", " -0.00904471892863512,\n", " -0.023472877219319344,\n", " -0.02737824246287346,\n", " 0.01583842933177948,\n", " 0.010163444094359875,\n", " 0.018875936046242714,\n", " -0.029588572680950165,\n", " 0.04868147149682045,\n", " 0.0187132116407156,\n", " 0.036965373903512955,\n", " 0.006268247961997986,\n", " -0.007275100331753492,\n", " 0.014156951569020748,\n", " 0.00866503082215786,\n", " -0.0016840195748955011,\n", " -0.002395087853074074,\n", " -0.019757354632019997,\n", " 0.00812939926981926,\n", " 0.00839382503181696,\n", " 0.00892945658415556,\n", " -0.0028256273362785578,\n", " -0.0058377087116241455,\n", " 0.013519617728888988,\n", " -0.006390290800482035,\n", " -0.054431039839982986,\n", " -0.003501947270706296,\n", " 0.000861078966408968,\n", " 0.006871681660413742,\n", " 0.01776399090886116,\n", " -0.0035934792831540108,\n", " 0.015228215605020523,\n", " -0.04407097026705742,\n", " -0.019282745197415352,\n", " 0.0010585507843643427,\n", " 0.017682630568742752,\n", " 0.004969849716871977,\n", " 0.028530869632959366,\n", " 0.005529211834073067,\n", " -0.0023357614409178495,\n", " 0.012061885558068752,\n", " -0.01337045431137085,\n", " -0.003230741247534752,\n", " -0.029914019629359245,\n", " -0.017492786049842834,\n", " -0.037073858082294464,\n", " 0.008590449579060078,\n", " 0.032490476965904236,\n", " 0.02782573364675045,\n", " 0.011275388300418854,\n", " 0.0011890686582773924,\n", " -0.016652047634124756,\n", " 0.0071801780723035336,\n", " -0.0006750486209057271,\n", " -0.03167685866355896,\n", " -0.017099536955356598,\n", " -0.02899191901087761,\n", " 0.0090921800583601,\n", " -0.017804672941565514,\n", " 0.003298542695119977,\n", " -0.02397460862994194,\n", " 0.005308857187628746,\n", " 0.006675057113170624,\n", " 0.014699364081025124,\n", " -0.02610357478260994,\n", " -0.003820614190772176,\n", " 0.005081722047179937,\n", " 0.01616387628018856,\n", " 0.03978591784834862,\n", " 0.014902768656611443,\n", " -0.00016251170018222183,\n", " 0.020299768075346947,\n", " -0.015879109501838684,\n", " -0.00452235946431756,\n", " 0.0053936089389026165,\n", " 8.464592974632978e-05,\n", " -0.03403634950518608,\n", " 0.013845064677298069,\n", " -0.014590881764888763,\n", " 0.016407961025834084,\n", " 0.013695902191102505,\n", " 0.0025408610235899687,\n", " -0.016014713793992996,\n", " 0.03723657876253128,\n", " 0.014807846397161484,\n", " 0.029832657426595688,\n", " 0.0077836113050580025,\n", " 0.03767051175236702,\n", " 0.00430539483204484,\n", " 0.006295368541032076,\n", " 0.01722157932817936,\n", " -0.0007818359881639481,\n", " 0.007573426701128483,\n", " 0.010400748811662197,\n", " 0.0006148748216219246,\n", " 0.04271494224667549,\n", " 0.003220570972189307,\n", " 0.005502091255038977,\n", " -0.0003678231150843203,\n", " -0.0014984130393713713,\n", " -0.029832657426595688,\n", " -0.014455278404057026,\n", " 0.02001500129699707,\n", " 0.010319387540221214,\n", " 0.044667623937129974,\n", " -0.011343189515173435,\n", " -0.022103287279605865,\n", " -0.02759520895779133,\n", " -0.0012000864371657372,\n", " -0.03268032148480415,\n", " 0.013939986936748028,\n", " -0.0010424479842185974,\n", " -0.02227957174181938,\n", " 0.02344575710594654,\n", " 0.007275100331753492,\n", " 0.012129687704145908,\n", " -0.022754181176424026,\n", " 0.009498989209532738,\n", " -0.03720945864915848,\n", " -0.0021035412792116404,\n", " -0.0025103504303842783,\n", " 0.00785819347947836,\n", " 0.04184708371758461,\n", " -0.02610357478260994,\n", " 0.012767021544277668,\n", " -0.04304038733243942,\n", " -0.007051355205476284,\n", " -0.015540102496743202,\n", " -0.004112160764634609,\n", " 0.0008047189912758768,\n", " -0.02097778208553791,\n", " 0.026062894612550735,\n", " 0.05112232640385628,\n", " 0.03693825379014015,\n", " -0.002800201764330268,\n", " 0.003125648945569992,\n", " 0.02172359824180603,\n", " -0.0032765071373432875,\n", " 0.002986655803397298,\n", " 0.02000144124031067,\n", " -0.0015950301894918084,\n", " 0.008373484946787357,\n", " -0.0042579337023198605,\n", " 0.0010695685632526875,\n", " -0.01743854396045208,\n", " 0.016977494582533836,\n", " 0.0077836113050580025,\n", " 0.02653750404715538,\n", " -0.0007441213820129633,\n", " -0.0007928537088446319,\n", " -0.009180322289466858,\n", " -0.005434289574623108,\n", " -0.03384650498628616,\n", " -0.0024018678814172745,\n", " 0.039731673896312714,\n", " -0.005613963585346937,\n", " 0.03452451899647713,\n", " -0.022618578746914864,\n", " 0.004871537443250418,\n", " 0.010346507653594017,\n", " 0.021126946434378624,\n", " 0.009370166808366776,\n", " -0.002212023828178644,\n", " 0.02032688818871975,\n", " 0.011702537536621094,\n", " -0.007444603834301233,\n", " 0.027988456189632416,\n", " 0.00454948004335165,\n", " -0.02386612631380558,\n", " -0.028286783024668694,\n", " -0.034741487354040146,\n", " -0.026157816872000694,\n", " 0.01831996440887451,\n", " 0.0069225323386490345,\n", " 0.011119444854557514,\n", " 0.02331015281379223,\n", " 0.017208019271492958,\n", " -0.022727061063051224,\n", " -0.004590161144733429,\n", " -0.02363560162484646,\n", " -0.014889207668602467,\n", " -0.014414597302675247,\n", " -0.020313328132033348,\n", " -0.03048355132341385,\n", " -0.03102596290409565,\n", " -0.010326167568564415,\n", " 0.0018391155172139406,\n", " 0.004125720821321011,\n", " 0.00417318195104599,\n", " -0.02097778208553791,\n", " 0.005478360690176487,\n", " -0.029127521440386772,\n", " -0.009105741046369076,\n", " 0.01734362170100212,\n", " -0.014902768656611443,\n", " 0.03989439830183983,\n", " -0.019621752202510834,\n", " 0.023296592757105827,\n", " 0.014821406453847885,\n", " 0.0052003744058310986,\n", " -0.010916040278971195,\n", " -0.00861756969243288,\n", " 0.00866503082215786,\n", " -0.0016263882862403989,\n", " 0.029669933021068573,\n", " 0.00823788158595562,\n", " -0.023283032700419426,\n", " -0.016950374469161034,\n", " 0.0025137404445558786,\n", " -3.151710188831203e-05,\n", " 0.01257717702537775,\n", " -0.020408250391483307,\n", " 0.025967972353100777,\n", " 0.005217324942350388,\n", " 0.003166329814121127,\n", " 0.0023103358689695597,\n", " -0.010407529771327972,\n", " -0.02063877508044243,\n", " 0.03799595683813095,\n", " -0.02214396744966507,\n", " -0.007824292406439781,\n", " -0.02066589519381523,\n", " -0.034605883061885834,\n", " -0.018482686951756477,\n", " 0.011146565899252892,\n", " -0.013458596542477608,\n", " 0.031514134258031845,\n", " 0.014428158290684223,\n", " 0.003868075320497155,\n", " -0.0015721471281722188,\n", " -0.0024476340040564537,\n", " -0.0035222875885665417,\n", " 0.004373196512460709,\n", " 0.000404054531827569,\n", " 0.03273456171154976,\n", " -0.0020391298457980156,\n", " 0.02107270434498787,\n", " 0.02206260710954666,\n", " -0.009485429152846336,\n", " -0.013350114226341248,\n", " 0.0005991957150399685,\n", " 0.008163300342857838,\n", " 0.037182338535785675,\n", " -0.033656660467386246,\n", " -0.010746536776423454,\n", " 0.011160125955939293,\n", " -0.010448209941387177,\n", " 0.01283482275903225,\n", " -0.008326023817062378,\n", " -0.003052762243896723,\n", " -0.016231678426265717,\n", " -0.013031447306275368,\n", " -0.0080344770103693,\n", " 0.02439497783780098,\n", " 0.011811019852757454,\n", " -0.01648932322859764,\n", " 0.0065123336389660835,\n", " 0.008536208420991898,\n", " -0.009471869096159935,\n", " -0.013953547924757004,\n", " -0.01198052428662777,\n", " 0.005041040945798159,\n", " -0.023825444281101227,\n", " -0.030022501945495605,\n", " 0.018808133900165558,\n", " -0.005390218924731016,\n", " 0.0018086048075929284,\n", " -0.006983553990721703,\n", " -0.017357181757688522,\n", " 0.008109058253467083,\n", " 0.03468724340200424,\n", " -0.015187534503638744,\n", " 0.01777755096554756,\n", " 0.014658682979643345,\n", " 0.00888199545443058,\n", " -0.01667916774749756,\n", " 0.02162867598235607,\n", " -0.020923541858792305,\n", " -0.027486726641654968,\n", " 0.011282168328762054,\n", " -0.03710097819566727,\n", " -0.01198730431497097,\n", " -0.007146277464926243,\n", " 0.0024306834675371647,\n", " -0.0005890254979021847,\n", " 0.006305539049208164,\n", " -0.015282456763088703,\n", " -0.01272634044289589,\n", " 0.017913155257701874,\n", " 0.01916070282459259,\n", " -0.006664887070655823,\n", " 0.014794286340475082,\n", " 0.005559722427278757,\n", " 0.00840738508850336,\n", " -0.01187204197049141,\n", " 0.018686091527342796,\n", " -0.03517541661858559,\n", " -0.0004763054894283414,\n", " 0.0028764784801751375,\n", " -0.013885745778679848,\n", " 8.284494833787903e-05,\n", " -0.016841890290379524,\n", " 0.012109346687793732,\n", " -0.019987881183624268,\n", " 0.008142959326505661,\n", " -0.005247835535556078,\n", " 0.012339872308075428,\n", " 0.005532601848244667,\n", " 0.004237593617290258,\n", " -0.002756130648776889,\n", " 0.009756634943187237,\n", " 0.009119301103055477,\n", " -0.001601810334250331,\n", " -0.0064953831024467945,\n", " 0.001803519786335528,\n", " 0.014631562866270542,\n", " 0.005241055507212877,\n", " -0.030130984261631966,\n", " -0.028747834265232086,\n", " -0.005095282103866339,\n", " -0.015974031761288643,\n", " -0.03246335685253143,\n", " 0.003539237892255187,\n", " -0.013200950808823109,\n", " 0.048003457486629486,\n", " 0.03018522448837757,\n", " 0.000754715409129858,\n", " -0.004179961979389191,\n", " -0.011200807057321072,\n", " -0.030022501945495605,\n", " -0.00866503082215786,\n", " 0.0003540509205777198,\n", " 0.021221866831183434,\n", " -0.0010254975641146302,\n", " 0.03091748058795929,\n", " -0.00446811830624938,\n", " 0.01593335159122944,\n", " -0.008475187234580517,\n", " 0.017140217125415802,\n", " 0.009580351412296295,\n", " 0.006688617169857025,\n", " -0.017723310738801956,\n", " 0.0064580924808979034,\n", " -0.014889207668602467,\n", " -0.015011250972747803,\n", " -0.020177723839879036,\n", " -0.015079052187502384,\n", " 0.03368378058075905,\n", " -0.0240695308893919,\n", " -0.017302941530942917,\n", " 0.02288978360593319,\n", " 0.003015471389517188,\n", " -0.007241199724376202,\n", " -0.00395960733294487,\n", " 0.011200807057321072,\n", " 0.0027544356416910887,\n", " -0.004240983631461859,\n", " 0.019947199150919914,\n", " -0.01733006164431572,\n", " -0.015173974446952343,\n", " 0.0013984058750793338,\n", " -0.011329629458487034,\n", " -0.010109202936291695,\n", " -0.00023243199393618852,\n", " 0.002318811137229204,\n", " 0.0009899018332362175,\n", " 0.010217685252428055,\n", " -0.007064915727823973,\n", " 0.0009237953345291317,\n", " -0.003300237702205777,\n", " -0.015350257977843285,\n", " 0.021465953439474106,\n", " 0.011410991661250591,\n", " 0.01849624700844288,\n", " 0.02793421596288681,\n", " 0.01242123357951641,\n", " 0.015065492130815983,\n", " -0.016543565317988396,\n", " -0.021004902198910713,\n", " -0.0176690686494112,\n", " 0.018997978419065475,\n", " 0.026727348566055298,\n", " -0.026090014725923538,\n", " -0.023296592757105827,\n", " 0.002742570359259844,\n", " 0.022605018690228462,\n", " -0.03091748058795929,\n", " -0.03186670318245888,\n", " 0.006593695376068354,\n", " 0.009756634943187237,\n", " -0.0171266570687294,\n", " -0.007648008409887552,\n", " -0.008631129749119282,\n", " -0.022333811968564987,\n", " 0.011478792876005173,\n", " -0.011797459796071053,\n", " 0.013594199903309345,\n", " -0.015567222610116005,\n", " -0.02034044824540615,\n", " 0.02759520895779133,\n", " -0.0022357541602104902,\n", " -0.009892238304018974,\n", " 0.0012729730224236846,\n", " 0.0036951813381165266,\n", " -0.017682630568742752,\n", " -0.014753605239093304,\n", " 0.03159549459815025,\n", " -0.02291690558195114,\n", " -0.006356390193104744,\n", " 0.1991736739873886,\n", " -0.023377954959869385,\n", " 0.008922676555812359,\n", " -0.006756418850272894,\n", " 0.010271926410496235,\n", " -0.007193738594651222,\n", " 0.013289093039929867,\n", " -0.006769979372620583,\n", " -0.0004133772454224527,\n", " 0.005030870903283358,\n", " -0.005139353219419718,\n", " 0.010021060705184937,\n", " -0.035799190402030945,\n", " -0.0028493579011410475,\n", " 0.005705495830625296,\n", " -0.021547315642237663,\n", " -0.03669416904449463,\n", " -0.010380408726632595,\n", " 0.00034536386374384165,\n", " 0.011200807057321072,\n", " 0.027134157717227936,\n", " 0.010651614516973495,\n", " -0.004630842246115208,\n", " -0.03864685073494911,\n", " 0.029263125732541084,\n", " -0.0010678735561668873,\n", " -0.010800777934491634,\n", " 0.019350547343492508,\n", " 0.023676281794905663,\n", " 0.0035460181534290314,\n", " -0.019879398867487907,\n", " 0.011919503100216389,\n", " -0.007905654609203339,\n", " 0.009180322289466858,\n", " -0.03170397877693176,\n", " -0.004817296285182238,\n", " 0.04518291726708412,\n", " -0.0071327174082398415,\n", " -0.010793997906148434,\n", " -0.00925490353256464,\n", " 0.01209578663110733,\n", " 0.004190132487565279,\n", " -0.020286206156015396,\n", " 0.00026993470964953303,\n", " 0.0018560659373179078,\n", " 0.009492209181189537,\n", " ...]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["def create_embeddings(text, model=\"text-embedding-ada-002-2\"):\n", "    # Create embeddings for each document chunk\n", "    embeddings = openai.embeddings.create(input = text, model=model).data[0].embedding\n", "    return embeddings\n", "\n", "#embeddings for the first chunk\n", "create_embeddings(flattened_df['chunks'][0])"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["[-0.0070945825427770615,\n", " -0.017328109592199326,\n", " -0.009644086472690105,\n", " -0.03070768155157566,\n", " -0.012548675760626793,\n", " 0.003105211304500699,\n", " -0.005113212391734123,\n", " -0.04121817275881767,\n", " -0.014629469253122807,\n", " -0.021376069635152817,\n", " 0.019231360405683517,\n", " 0.05087646469473839,\n", " -0.0012907310156151652,\n", " 0.0024855893570929766,\n", " -0.03840590640902519,\n", " -0.006089693866670132,\n", " 0.0355084203183651,\n", " -0.004697763826698065,\n", " 0.0023630852811038494,\n", " -0.01342928409576416,\n", " -0.01891888678073883,\n", " 0.009019138291478157,\n", " 0.015893569216132164,\n", " -0.008713766001164913,\n", " -0.014672079123556614,\n", " 0.007233065087348223,\n", " 0.013031589798629284,\n", " -0.013365369290113449,\n", " 0.002858427818864584,\n", " 0.004861102905124426,\n", " 0.0040266546420753,\n", " -0.01677417755126953,\n", " -0.015850959345698357,\n", " -0.04306461289525032,\n", " -0.027242060750722885,\n", " -0.004278764594346285,\n", " 0.0080533092841506,\n", " -0.009984967298805714,\n", " 0.022015219554305077,\n", " -0.009040444158017635,\n", " 0.004900162108242512,\n", " 0.00031890999525785446,\n", " -0.012221998535096645,\n", " 0.013038692064583302,\n", " -0.0038775193970650434,\n", " 0.0070661758072674274,\n", " -0.022185660898685455,\n", " -0.004410145804286003,\n", " 0.0013573094038292766,\n", " 0.013912199065089226,\n", " 0.002606318099424243,\n", " 0.008266360498964787,\n", " -0.01138399913907051,\n", " 0.0103471539914608,\n", " -0.005084805656224489,\n", " 0.0029045888222754,\n", " 0.007960988208651543,\n", " -0.012697811238467693,\n", " 0.013265945948660374,\n", " 0.0023417803458869457,\n", " 0.015865162014961243,\n", " 0.004239705391228199,\n", " -0.0018144802888855338,\n", " 0.022753795608878136,\n", " 0.011163847520947456,\n", " -0.003371524391695857,\n", " -0.007265022955834866,\n", " 0.00042521333671174943,\n", " -0.004175790119916201,\n", " 0.005191330797970295,\n", " 0.03238368034362793,\n", " 0.028080059215426445,\n", " 0.023634403944015503,\n", " -0.005677796434611082,\n", " -0.00954466313123703,\n", " -0.0072082094848155975,\n", " -0.00857173278927803,\n", " 0.011312982998788357,\n", " -0.0005739048356190324,\n", " -0.005237491801381111,\n", " 0.03587770834565163,\n", " -0.03255411982536316,\n", " -0.016461703926324844,\n", " 0.0057701184414327145,\n", " 0.019742680713534355,\n", " 0.006501591764390469,\n", " -0.009374222718179226,\n", " 0.009580171667039394,\n", " -0.01024772971868515,\n", " 0.010574407875537872,\n", " 0.021049391478300095,\n", " 0.026375655084848404,\n", " -0.012399540282785892,\n", " 0.037553705275058746,\n", " -0.010375560261309147,\n", " 0.019572241231799126,\n", " -0.01681678742170334,\n", " 0.02505474165081978,\n", " -0.0068637775257229805,\n", " -0.059824585914611816,\n", " 0.005819830112159252,\n", " 0.005251695401966572,\n", " -0.04292257875204086,\n", " -0.007755038794130087,\n", " -0.0037248332519084215,\n", " -0.009097257629036903,\n", " 0.01852119155228138,\n", " 0.0009285451960749924,\n", " 0.017455939203500748,\n", " -0.0026471526362001896,\n", " -0.007506479974836111,\n", " 0.05746682733297348,\n", " 0.011909523978829384,\n", " -0.018208717927336693,\n", " 0.005986719857901335,\n", " -0.005255246069282293,\n", " -0.004868204239755869,\n", " -0.024372979998588562,\n", " -0.0060648382641375065,\n", " -0.015950381755828857,\n", " 0.010943694971501827,\n", " 0.00939552765339613,\n", " 0.039030853658914566,\n", " 0.007392853032797575,\n", " 0.018847869709134102,\n", " 0.019032513722777367,\n", " -0.00791837740689516,\n", " 0.013720453716814518,\n", " -0.013564216904342175,\n", " 0.017186075448989868,\n", " 0.02900327742099762,\n", " 0.0006746599683538079,\n", " 0.013912199065089226,\n", " 0.009878442622721195,\n", " 0.0008570844656787813,\n", " 0.0276255514472723,\n", " -0.04161586984992027,\n", " -0.0026933136396110058,\n", " -0.004722619894891977,\n", " -0.06277889013290405,\n", " -0.002331127878278494,\n", " 0.019330784678459167,\n", " -0.019600648432970047,\n", " 0.015652110800147057,\n", " -0.010759050957858562,\n", " 0.02367701381444931,\n", " 0.024799080565571785,\n", " 0.003817155258730054,\n", " 0.012101269327104092,\n", " 0.0021997466683387756,\n", " 0.005184229463338852,\n", " -0.009168273769319057,\n", " 0.02722785621881485,\n", " -0.017768412828445435,\n", " -0.007399954833090305,\n", " 0.030253173783421516,\n", " 0.003541964804753661,\n", " 0.014828315936028957,\n", " -0.0021198526956140995,\n", " -0.020182985812425613,\n", " -0.00025677026133053005,\n", " -0.004807840101420879,\n", " 0.016802584752440453,\n", " -0.014331198297441006,\n", " 0.005699101369827986,\n", " 0.027000602334737778,\n", " 0.020807934924960136,\n", " 0.004374637268483639,\n", " -0.013734657317399979,\n", " 0.004829145036637783,\n", " -0.019643258303403854,\n", " 0.02300945669412613,\n", " -0.00685667572543025,\n", " 0.0072366162203252316,\n", " -0.0037638924550265074,\n", " 0.00927479937672615,\n", " -0.0009099032613448799,\n", " -0.005194881930947304,\n", " -0.01339377649128437,\n", " -0.02705741673707962,\n", " -0.013642335310578346,\n", " 0.011064423248171806,\n", " 0.01562370453029871,\n", " 0.03269615396857262,\n", " 0.0009871340589597821,\n", " 0.004992483649402857,\n", " 0.02394687943160534,\n", " -0.007968089543282986,\n", " -0.009175376035273075,\n", " -0.004573484417051077,\n", " 0.005535762757062912,\n", " 0.02685856819152832,\n", " 0.020381832495331764,\n", " -0.023392947390675545,\n", " -0.6567637324333191,\n", " -0.024131521582603455,\n", " -0.009331612847745419,\n", " -0.012939268723130226,\n", " 0.012555777095258236,\n", " 0.024088911712169647,\n", " 0.0021163017954677343,\n", " 0.0026755593717098236,\n", " 0.006476735696196556,\n", " -0.014615265652537346,\n", " -0.016788380220532417,\n", " 0.009033341892063618,\n", " 0.003275651717558503,\n", " -0.010411068797111511,\n", " -0.01095079630613327,\n", " -0.0031939824111759663,\n", " -0.0044243489392101765,\n", " -0.01864902302622795,\n", " -0.01822292059659958,\n", " 0.023321930319070816,\n", " -0.0021961957681924105,\n", " 0.0052730003371834755,\n", " 0.0018890479113906622,\n", " -0.013003183528780937,\n", " 0.004651602823287249,\n", " -0.00982873048633337,\n", " 0.006870879326015711,\n", " -0.03295181319117546,\n", " 0.025097351521253586,\n", " 0.015311230905354023,\n", " -0.012236201204359531,\n", " 0.03053724206984043,\n", " -0.005425686482340097,\n", " -0.013571318238973618,\n", " 0.04672908037900925,\n", " 0.022824812680482864,\n", " -7.094748980307486e-06,\n", " 0.02715683914721012,\n", " 0.015112383291125298,\n", " 0.04062163084745407,\n", " -0.018265530467033386,\n", " -0.02005515620112419,\n", " 0.016759974882006645,\n", " 0.0025814620312303305,\n", " -0.02538141794502735,\n", " -0.012094167992472649,\n", " -0.0009125663782469928,\n", " -0.0005219737649895251,\n", " 0.010936593636870384,\n", " -0.005628084763884544,\n", " 0.0163054671138525,\n", " -0.0072934296913445,\n", " 0.0019476368324831128,\n", " -0.007364446297287941,\n", " 0.00833737663924694,\n", " 0.0045095691457390785,\n", " 0.024756470695137978,\n", " -0.02384745515882969,\n", " 0.003002236830070615,\n", " 0.012988979928195477,\n", " -0.0034531939309090376,\n", " 0.005280102137476206,\n", " -0.008188242092728615,\n", " -0.020268205553293228,\n", " -0.016234450042247772,\n", " 0.007204658351838589,\n", " -0.01533963717520237,\n", " -0.003845561994239688,\n", " 0.023762235417962074,\n", " 0.01095079630613327,\n", " 0.00572750810533762,\n", " 0.005745262373238802,\n", " -0.034741438925266266,\n", " 0.003944985568523407,\n", " 0.0037816467229276896,\n", " 0.022143051028251648,\n", " 0.008713766001164913,\n", " -0.012818539515137672,\n", " -0.0010750173823907971,\n", " -0.01190242264419794,\n", " -0.007648513652384281,\n", " 0.004133180249482393,\n", " -0.026205213740468025,\n", " -0.008110122755169868,\n", " 0.01573733240365982,\n", " -0.011731982231140137,\n", " -0.02123403549194336,\n", " -0.0013493199367076159,\n", " 0.01423177495598793,\n", " 0.011391101405024529,\n", " 0.007165599148720503,\n", " 0.024358775466680527,\n", " -0.017399126663804054,\n", " 0.014274384826421738,\n", " -0.009168273769319057,\n", " 0.009466544725000858,\n", " -0.006313397083431482,\n", " 0.0036538164131343365,\n", " 0.010588610544800758,\n", " -0.03812183812260628,\n", " -0.007960988208651543,\n", " -0.004964076913893223,\n", " 0.006522896699607372,\n", " -0.006746599916368723,\n", " 0.022214068099856377,\n", " 0.023819047957658768,\n", " -0.014757299795746803,\n", " 0.018663225695490837,\n", " 0.036587875336408615,\n", " -0.02153230644762516,\n", " 0.005365322344005108,\n", " -0.00474747596308589,\n", " -0.010027578100562096,\n", " 0.00015490548685193062,\n", " 0.013926402665674686,\n", " -0.03150307014584541,\n", " 0.012285913340747356,\n", " 0.004761679098010063,\n", " 0.018776852637529373,\n", " -0.022895829752087593,\n", " -0.010283238254487514,\n", " 0.02083634026348591,\n", " 0.013017387129366398,\n", " -0.008479410782456398,\n", " 0.003817155258730054,\n", " 0.0031318427063524723,\n", " -0.01674577035009861,\n", " 0.0011069750180467963,\n", " -0.005986719857901335,\n", " -0.0021997466683387756,\n", " 0.008401292376220226,\n", " -0.020353427156805992,\n", " 0.014558452181518078,\n", " -0.016191840171813965,\n", " 0.003360871924087405,\n", " 0.002217500936239958,\n", " 0.02505474165081978,\n", " 0.016064008697867393,\n", " 0.018407564610242844,\n", " -0.004495366010814905,\n", " -0.025466639548540115,\n", " -0.0004334246623329818,\n", " 0.010702237486839294,\n", " 0.006455430760979652,\n", " 0.018265530467033386,\n", " 0.002895711688324809,\n", " -0.006785659119486809,\n", " -0.024358775466680527,\n", " 0.0024127971846610308,\n", " 0.009573070332407951,\n", " -0.014885129407048225,\n", " -0.010695136152207851,\n", " 0.02759714424610138,\n", " 0.01644749939441681,\n", " 0.03465621918439865,\n", " -0.02239871211349964,\n", " -0.010091492906212807,\n", " -0.015424857847392559,\n", " -0.019245563074946404,\n", " -0.008181139826774597,\n", " 0.00573460990563035,\n", " 0.03420171141624451,\n", " -0.004296518862247467,\n", " -0.013692046515643597,\n", " -0.017100855708122253,\n", " -0.023250913247466087,\n", " 0.009480748325586319,\n", " 0.03309384733438492,\n", " -0.02535301260650158,\n", " -0.03329269587993622,\n", " -0.004612543620169163,\n", " -0.021546509116888046,\n", " -0.005713304970413446,\n", " 0.022029424086213112,\n", " 0.007932581007480621,\n", " 0.004385289736092091,\n", " 0.008685359731316566,\n", " -0.02782439813017845,\n", " 0.008216648362576962,\n", " 0.0034958040341734886,\n", " 0.02940097264945507,\n", " -0.0048540011048316956,\n", " -0.011660965159535408,\n", " -0.006508693564683199,\n", " 0.0010510492138564587,\n", " 0.016490111127495766,\n", " 0.03442896530032158,\n", " 0.01832234486937523,\n", " -0.018606411293148994,\n", " 0.01637648418545723,\n", " -0.017356514930725098,\n", " 0.0060932449996471405,\n", " -0.0009014700190164149,\n", " 0.011064423248171806,\n", " 0.0009853586088865995,\n", " -0.011014712043106556,\n", " 0.0055677201598882675,\n", " 0.022412914782762527,\n", " 0.017796820029616356,\n", " 0.021787965670228004,\n", " -0.0006249481812119484,\n", " 0.013166522607207298,\n", " 0.010503390803933144,\n", " -0.0024465301539748907,\n", " 0.019685868173837662,\n", " -0.036048147827386856,\n", " -0.013834080658853054,\n", " -0.00813852995634079,\n", " 0.03746848553419113,\n", " 0.025977959856390953,\n", " -0.00014170078793540597,\n", " -0.00833737663924694,\n", " -0.016234450042247772,\n", " -0.01257708203047514,\n", " 0.007336039561778307,\n", " 0.028491957113146782,\n", " -0.010453678667545319,\n", " -0.002373737981542945,\n", " -0.009225087240338326,\n", " 0.0002352432784391567,\n", " -0.007456768304109573,\n", " -0.003309384686872363,\n", " -0.005812728311866522,\n", " 0.01995573192834854,\n", " 0.006270787212997675,\n", " 0.011597050353884697,\n", " 0.020850544795393944,\n", " 0.0359061136841774,\n", " 0.004040858242660761,\n", " -0.026631314307451248,\n", " -0.02468545362353325,\n", " 0.0003808278124779463,\n", " 0.009622781537473202,\n", " 0.010943694971501827,\n", " 0.004513120278716087,\n", " -0.001350207719951868,\n", " 0.027668161317706108,\n", " -0.020268205553293228,\n", " 0.03363357484340668,\n", " 0.01309550553560257,\n", " 0.0001785407803254202,\n", " 0.025168368592858315,\n", " 0.014182062819600105,\n", " -0.024429792538285255,\n", " 0.011802999302744865,\n", " -0.003314710920676589,\n", " 0.023733828216791153,\n", " 0.015453264117240906,\n", " -0.0037958500906825066,\n", " 0.015183400362730026,\n", " -0.013372470624744892,\n", " 0.004861102905124426,\n", " -0.018237125128507614,\n", " 0.018734242767095566,\n", " 0.00446340860798955,\n", " -0.015921976417303085,\n", " 0.016830991953611374,\n", " 0.0065406509675085545,\n", " 0.027043212205171585,\n", " 0.03150307014584541,\n", " 0.02283901534974575,\n", " -0.0006870879442431033,\n", " 0.0042929681949317455,\n", " 0.0004001354973297566,\n", " 0.010474983602762222,\n", " 0.018364954739809036,\n", " 0.01975688524544239,\n", " -0.016319669783115387,\n", " 0.01563790813088417,\n", " 0.005848236847668886,\n", " -0.012712014839053154,\n", " -0.009416832588613033,\n", " 0.010780355893075466,\n", " -0.017668990418314934,\n", " 0.013152319006621838,\n", " 0.012207794934511185,\n", " -0.0007913939189165831,\n", " -0.0056600421667099,\n", " 0.0232367105782032,\n", " -0.0035526175051927567,\n", " -0.018066683784127235,\n", " -0.05124575272202492,\n", " 0.027682363986968994,\n", " -0.004662255756556988,\n", " -0.00870666466653347,\n", " -0.0017807473195716739,\n", " -0.04116136208176613,\n", " -0.017058245837688446,\n", " -0.0021145264618098736,\n", " 0.011724879965186119,\n", " 0.00024434231454506516,\n", " 0.01342928409576416,\n", " 0.022256677970290184,\n", " 0.01918875053524971,\n", " 0.013564216904342175,\n", " -0.0022565601393580437,\n", " 0.030224766582250595,\n", " -0.013131014071404934,\n", " 0.008948122151196003,\n", " 0.010972102172672749,\n", " 0.001673334278166294,\n", " -0.0270290095359087,\n", " -0.009459443390369415,\n", " -0.026517687365412712,\n", " 0.009722205810248852,\n", " -0.015992991626262665,\n", " -0.016064008697867393,\n", " -0.023861657828092575,\n", " -0.004545077681541443,\n", " 0.0031922070775181055,\n", " -0.019898919388651848,\n", " 0.017356514930725098,\n", " -0.023066269233822823,\n", " -0.010276136919856071,\n", " -0.014167859219014645,\n", " -0.008060411550104618,\n", " -0.03400286287069321,\n", " -0.0019582894165068865,\n", " 0.049143653362989426,\n", " -0.0012472332455217838,\n", " -0.011866914108395576,\n", " -0.012491862289607525,\n", " -0.03468462452292442,\n", " -0.01744173653423786,\n", " 0.07084640115499496,\n", " 0.008103021420538425,\n", " -0.009026240557432175,\n", " 0.01075194962322712,\n", " 0.002008001087233424,\n", " -0.019046716392040253,\n", " -0.029486192390322685,\n", " 0.0008411057060584426,\n", " 0.026872772723436356,\n", " 0.012548675760626793,\n", " -0.009715103544294834,\n", " 0.0060080247931182384,\n", " 0.009487849660217762,\n", " -0.0035100074019283056,\n", " -0.0020967721939086914,\n", " 0.017498549073934555,\n", " -0.019316580146551132,\n", " 0.0016094191232696176,\n", " 0.010460780933499336,\n", " -0.01357841957360506,\n", " 0.0022654372733086348,\n", " 0.0019068021792918444,\n", " 0.017129261046648026,\n", " 0.0030466224998235703,\n", " -0.004491815343499184,\n", " -0.005901499651372433,\n", " 0.005333364475518465,\n", " 0.03371879458427429,\n", " 0.018194515258073807,\n", " -0.02940097264945507,\n", " 0.005255246069282293,\n", " 0.018038276582956314,\n", " -0.015694722533226013,\n", " -0.0034034820273518562,\n", " 0.011788795702159405,\n", " 0.009068850427865982,\n", " -0.004729721695184708,\n", " 0.008614342659711838,\n", " 0.024600233882665634,\n", " -0.018194515258073807,\n", " 0.020949967205524445,\n", " 0.023293523117899895,\n", " -0.015921976417303085,\n", " -0.019316580146551132,\n", " 0.009942357428371906,\n", " -0.007091031409800053,\n", " 0.0012374684447422624,\n", " 0.021418679505586624,\n", " 0.011810100637376308,\n", " -0.004726170562207699,\n", " 0.03843431547284126,\n", " 0.02940097264945507,\n", " -0.007747937459498644,\n", " 0.005038644652813673,\n", " -0.016362279653549194,\n", " -0.01009859424084425,\n", " 0.014061334542930126,\n", " -0.00939552765339613,\n", " -0.002189094200730324,\n", " -0.01607821322977543,\n", " -0.008103021420538425,\n", " -0.012385336682200432,\n", " 0.012058659456670284,\n", " 0.0023488819133490324,\n", " -0.01366364024579525,\n", " -0.022895829752087593,\n", " 0.0013510953867807984,\n", " -0.022029424086213112,\n", " -0.031190596520900726,\n", " -0.013564216904342175,\n", " -0.013755962252616882,\n", " -0.016518516466021538,\n", " -0.0185495987534523,\n", " 0.007726632058620453,\n", " 0.006632972974330187,\n", " 0.023364540189504623,\n", " 0.021986814215779305,\n", " -0.023463964462280273,\n", " -0.0007123876712284982,\n", " 0.021546509116888046,\n", " -0.015396450646221638,\n", " -0.026247823610901833,\n", " 0.004612543620169163,\n", " -0.042212408035993576,\n", " -0.021844780072569847,\n", " 0.006590362638235092,\n", " -0.006867328658699989,\n", " -0.014459028840065002,\n", " -0.004935670178383589,\n", " -0.0002561044821050018,\n", " 0.019600648432970047,\n", " 0.0007816291181370616,\n", " 0.03465621918439865,\n", " -0.017597973346710205,\n", " -0.0038668669294565916,\n", " 0.013770164921879768,\n", " 0.014451926574110985,\n", " -0.009161172434687614,\n", " 0.0035827995743602514,\n", " -0.03369038924574852,\n", " 0.014118148013949394,\n", " -0.02424514852464199,\n", " -0.01323043741285801,\n", " 0.0020310815889388323,\n", " 0.01053179707378149,\n", " 0.000174435117514804,\n", " 0.019969934597611427,\n", " 0.03462781012058258,\n", " 0.002070140792056918,\n", " -0.01778261736035347,\n", " 0.009665392339229584,\n", " -0.0226401686668396,\n", " -0.009537561796605587,\n", " -0.010361356660723686,\n", " 0.01593617908656597,\n", " 0.01508397702127695,\n", " -0.022498134523630142,\n", " 0.024841690436005592,\n", " 0.024500809609889984,\n", " -0.007442564703524113,\n", " -0.02688697539269924,\n", " -0.01982790231704712,\n", " 0.01898990385234356,\n", " 0.030792901292443275,\n", " -0.011653863824903965,\n", " 0.002577911363914609,\n", " 0.0013235763181000948,\n", " 0.00800359807908535,\n", " -0.006299193948507309,\n", " 0.007541988510638475,\n", " 0.004289417061954737,\n", " 0.011980541050434113,\n", " -0.012143880128860474,\n", " -0.0460757240653038,\n", " -0.022782202810049057,\n", " -0.013258843682706356,\n", " -0.01315942034125328,\n", " 0.01024772971868515,\n", " -0.006739498116075993,\n", " -0.019785292446613312,\n", " -0.04510989785194397,\n", " 0.0060080247931182384,\n", " 0.0012880678987130523,\n", " -0.02042444236576557,\n", " -0.01859220862388611,\n", " -0.04232603684067726,\n", " -0.032838188111782074,\n", " -0.004562831949442625,\n", " 0.004640950355678797,\n", " 0.029315751045942307,\n", " -0.009622781537473202,\n", " 0.010695136152207851,\n", " -0.009175376035273075,\n", " -0.01684519462287426,\n", " -0.015751535072922707,\n", " -0.02320830337703228,\n", " -0.016632143408060074,\n", " -0.0017461265670135617,\n", " 0.03386082872748375,\n", " 0.020637493580579758,\n", " -0.0019156793132424355,\n", " 0.008884206414222717,\n", " 0.011312982998788357,\n", " -0.007478073239326477,\n", " -0.008500715717673302,\n", " 0.009182477369904518,\n", " 0.012207794934511185,\n", " 0.011270372197031975,\n", " -0.027270466089248657,\n", " 0.011526033282279968,\n", " 0.017285499721765518,\n", " -0.0038704178296029568,\n", " -0.0009977866429835558,\n", " -0.0016103069065138698,\n", " -0.022057831287384033,\n", " 0.020211393013596535,\n", " 0.0036644688807427883,\n", " -0.023080473765730858,\n", " 0.0030075632967054844,\n", " -0.007613005116581917,\n", " 0.014210470020771027,\n", " -0.0032206138130277395,\n", " 0.01583675481379032,\n", " 0.014913536608219147,\n", " 0.000739906681701541,\n", " -0.0022689879406243563,\n", " 0.01664634793996811,\n", " 0.002963177626952529,\n", " -0.008365783840417862,\n", " 0.004704865626990795,\n", " 0.014416418969631195,\n", " -0.01009859424084425,\n", " 0.010446577332913876,\n", " -0.02592114731669426,\n", " 0.014395113103091717,\n", " -0.030565647408366203,\n", " -0.016248652711510658,\n", " -0.0025086698587983847,\n", " 0.0015206481330096722,\n", " -0.013436386361718178,\n", " 0.011945032514631748,\n", " 0.03968420997262001,\n", " 0.0037283841520547867,\n", " 0.008884206414222717,\n", " 5.808955756947398e-05,\n", " -0.007218861952424049,\n", " -0.0002911690389737487,\n", " -0.01644749939441681,\n", " 0.0034176853951066732,\n", " -0.016234450042247772,\n", " -0.009381324984133244,\n", " -0.008394190110266209,\n", " -0.058347437530756,\n", " -0.026347247883677483,\n", " -0.006764354184269905,\n", " 0.0015446163015440106,\n", " -0.0016626818105578423,\n", " 0.0077124289236962795,\n", " -0.011078626848757267,\n", " -0.00813852995634079,\n", " -0.012079964391887188,\n", " 0.0025619324296712875,\n", " 0.026404060423374176,\n", " -0.026134196668863297,\n", " 0.029230531305074692,\n", " 0.02631884068250656,\n", " -0.018336547538638115,\n", " -0.00524104293435812,\n", " 0.007300531025975943,\n", " -0.015098180621862411,\n", " -0.04883117973804474,\n", " 0.008237953297793865,\n", " 0.0031797790434211493,\n", " -0.012910861521959305,\n", " 0.015566891059279442,\n", " -0.006735947448760271,\n", " 0.005116763524711132,\n", " -0.01487092673778534,\n", " -0.024600233882665634,\n", " 0.020779527723789215,\n", " 0.03215642645955086,\n", " -0.00953045953065157,\n", " -0.028989074751734734,\n", " 0.010595712810754776,\n", " -0.010865576565265656,\n", " 0.018024073913693428,\n", " -0.008500715717673302,\n", " -0.0021429331973195076,\n", " -0.0314178504049778,\n", " -0.006728845648467541,\n", " -0.018663225695490837,\n", " 0.016830991953611374,\n", " -0.018805259838700294,\n", " 0.01587936468422413,\n", " 0.007176251616328955,\n", " -0.0033679737243801355,\n", " 0.02217145822942257,\n", " 0.014025826007127762,\n", " -0.013841181993484497,\n", " -0.012562879361212254,\n", " -0.031219003722071648,\n", " 0.020111968740820885,\n", " -0.01938759721815586,\n", " 0.032298460602760315,\n", " 0.018734242767095566,\n", " 0.004211298655718565,\n", " -0.01965746097266674,\n", " 0.0011486973380669951,\n", " -0.017811022698879242,\n", " 0.03212801739573479,\n", " -0.0072934296913445,\n", " -0.010340051725506783,\n", " 0.012797234579920769,\n", " -0.022512339055538177,\n", " 0.015850959345698357,\n", " -0.01999834179878235,\n", " -0.020310815423727036,\n", " 0.002814042381942272,\n", " -0.01906091906130314,\n", " -0.04025234654545784,\n", " 0.016404889523983,\n", " 0.0075703952461481094,\n", " -0.012122574262320995,\n", " 0.0035348632372915745,\n", " -0.006590362638235092,\n", " -0.022143051028251648,\n", " -0.004047960042953491,\n", " -0.007478073239326477,\n", " -0.02173115313053131,\n", " -0.005013789050281048,\n", " -0.013173623941838741,\n", " -0.003916578833013773,\n", " 0.010013374499976635,\n", " 0.00166179402731359,\n", " 0.017470141872763634,\n", " -0.0005712417187169194,\n", " -0.0017771964194253087,\n", " 0.035451605916023254,\n", " -0.012364031746983528,\n", " 0.0245860293507576,\n", " -0.033065441995859146,\n", " 0.014572655782103539,\n", " -0.02106359414756298,\n", " -0.004736823029816151,\n", " -0.003902375465258956,\n", " 0.009672493673861027,\n", " 0.006640074774622917,\n", " -0.018677428364753723,\n", " -0.008351580239832401,\n", " -0.0009059085859917104,\n", " -0.006618769373744726,\n", " 0.0023843904491513968,\n", " -0.0020097766537219286,\n", " 0.00335732102394104,\n", " 0.0058624399825930595,\n", " 0.009736408479511738,\n", " 0.006284990347921848,\n", " -0.00897652842104435,\n", " -0.016064008697867393,\n", " 0.013826978392899036,\n", " -0.01026193331927061,\n", " 0.011021813377737999,\n", " 0.006192668341100216,\n", " -0.0017372494330629706,\n", " 0.008948122151196003,\n", " -0.01995573192834854,\n", " 0.026503484696149826,\n", " 0.012655201368033886,\n", " -0.01562370453029871,\n", " -0.013585521839559078,\n", " -0.01952963136136532,\n", " 0.00927479937672615,\n", " -0.003494028467684984,\n", " -0.035110726952552795,\n", " -0.014885129407048225,\n", " -0.03445737063884735,\n", " -0.021219830960035324,\n", " 0.02239871211349964,\n", " -0.01329435221850872,\n", " -0.010624119080603123,\n", " 0.01190242264419794,\n", " 0.005901499651372433,\n", " -0.0114692198112607,\n", " 0.003527761436998844,\n", " -0.022597558796405792,\n", " -0.024870097637176514,\n", " -0.010858475230634212,\n", " 0.008735070936381817,\n", " -0.025665486231446266,\n", " -0.006025779061019421,\n", " -0.021418679505586624,\n", " 0.0179530568420887,\n", " 0.009047545492649078,\n", " -0.040195532143116,\n", " -0.018762649968266487,\n", " 0.008905511349439621,\n", " -0.025566061958670616,\n", " -0.004484713543206453,\n", " -0.00136086018756032,\n", " 0.009423934854567051,\n", " 0.010560204274952412,\n", " -0.018393361940979958,\n", " 0.013656537979841232,\n", " 0.0003519772144500166,\n", " -0.0031069868709892035,\n", " 0.019898919388651848,\n", " -0.014842519536614418,\n", " -0.013599724508821964,\n", " 0.01898990385234356,\n", " 0.0038242568261921406,\n", " 0.024657046422362328,\n", " -0.016362279653549194,\n", " -0.01975688524544239,\n", " -0.024941114708781242,\n", " 0.01408263947814703,\n", " 0.01593617908656597,\n", " 0.0005401718663051724,\n", " 0.026262028142809868,\n", " 0.0007292541558854282,\n", " -0.014444825239479542,\n", " 0.0009214435121975839,\n", " 0.008514919318258762,\n", " 0.02401789464056492,\n", " -0.004907263442873955,\n", " 0.012122574262320995,\n", " 0.005649389699101448,\n", " -0.012917962856590748,\n", " 0.012598387897014618,\n", " -0.008067512884736061,\n", " 0.003375075291842222,\n", " -0.007826055400073528,\n", " -0.01109993178397417,\n", " -0.012761726044118404,\n", " -0.0023613099474459887,\n", " -0.012981878593564034,\n", " -0.0015952157555148005,\n", " -0.003110537538304925,\n", " -0.05516588315367699,\n", " -0.018166108056902885,\n", " 0.01985630765557289,\n", " 9.393086656928062e-05,\n", " 0.023265117779374123,\n", " -0.011085729114711285,\n", " -0.013684945181012154,\n", " -0.015779942274093628,\n", " 0.01925976760685444,\n", " -0.01647590659558773,\n", " -0.022725388407707214,\n", " 0.026162603870034218,\n", " 0.015581094659864902,\n", " 0.01958644390106201,\n", " -0.008628546260297298,\n", " -0.01239243894815445,\n", " -0.030082734301686287,\n", " -0.008948122151196003,\n", " 0.00030049000633880496,\n", " -0.006348905619233847,\n", " 0.00828766543418169,\n", " 0.0037638924550265074,\n", " 0.009161172434687614,\n", " -0.029258938506245613,\n", " 0.0019831452518701553,\n", " -0.0015490548685193062,\n", " -0.02498372457921505,\n", " 0.006853125058114529,\n", " -0.012648099102079868,\n", " 0.0017718701856210828,\n", " -0.0034017066936939955,\n", " -0.006238829344511032,\n", " 0.01952963136136532,\n", " 0.0028939361218363047,\n", " -0.0003040408482775092,\n", " -0.002572585130110383,\n", " -0.0006338253151625395,\n", " 0.01580834947526455,\n", " 0.004303620662540197,\n", " 0.18339388072490692,\n", " -0.00328985508531332,\n", " 0.002549504628404975,\n", " 0.032270051538944244,\n", " 0.007698225323110819,\n", " -0.023336132988333702,\n", " 0.03164510428905487,\n", " 0.019913122057914734,\n", " 0.00815273355692625,\n", " 0.0185495987534523,\n", " -0.005003136582672596,\n", " 0.0020967721939086914,\n", " -0.0026666822377592325,\n", " 0.008081716485321522,\n", " 0.0031975333113223314,\n", " 0.008465207181870937,\n", " -0.027909617871046066,\n", " -0.012094167992472649,\n", " -0.031900763511657715,\n", " -0.015779942274093628,\n", " 0.023151488974690437,\n", " 0.013202030211687088,\n", " -0.025665486231446266,\n", " -6.097461664467119e-05,\n", " 0.039428550750017166,\n", " 0.020268205553293228,\n", " -0.02555185928940773,\n", " -0.009551765397191048,\n", " 0.0124421501532197,\n", " 0.0035916767083108425,\n", " -0.019103530794382095,\n", " -0.009807425551116467,\n", " -0.008237953297793865,\n", " -0.009935256093740463,\n", " -0.004282315261662006,\n", " -0.011646761558949947,\n", " -0.015552688390016556,\n", " 0.022824812680482864,\n", " 0.0012667628470808268,\n", " 0.018194515258073807,\n", " -0.03556523472070694,\n", " 0.005738160572946072,\n", " -0.00924639217555523,\n", " -0.009835832752287388,\n", " -0.017228685319423676,\n", " 0.03363357484340668,\n", " ...]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["cat = create_embeddings(\"cat\")\n", "cat"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>path</th>\n", "      <th>text</th>\n", "      <th>chunks</th>\n", "      <th>embeddings</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td># Neural Network Frameworks As we have learned...</td>\n", "      <td>[-0.016977494582533836, 0.0028917337767779827,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td>descent optimization While the `numpy` library...</td>\n", "      <td>[-0.014787919819355011, 0.0016925617819651961,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td>should give us the opportunity to compute grad...</td>\n", "      <td>[-0.03673850744962692, -0.02062208764255047, 0...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td>those computations on GPUs is very important. ...</td>\n", "      <td>[-0.03166744112968445, -0.011117876507341862, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td>API, there is also higher-level API, called Ke...</td>\n", "      <td>[-0.007904806174337864, -0.03335562348365784, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 path                                               text  \\\n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "\n", "                                              chunks  \\\n", "0  # Neural Network Frameworks As we have learned...   \n", "0  descent optimization While the `numpy` library...   \n", "0  should give us the opportunity to compute grad...   \n", "0  those computations on GPUs is very important. ...   \n", "0  API, there is also higher-level API, called Ke...   \n", "\n", "                                          embeddings  \n", "0  [-0.016977494582533836, 0.0028917337767779827,...  \n", "0  [-0.014787919819355011, 0.0016925617819651961,...  \n", "0  [-0.03673850744962692, -0.02062208764255047, 0...  \n", "0  [-0.03166744112968445, -0.011117876507341862, ...  \n", "0  [-0.007904806174337864, -0.03335562348365784, ...  "]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["# create embeddings for the whole data chunks and store them in a list\n", "\n", "embeddings = []\n", "for chunk in flattened_df['chunks']:\n", "    embeddings.append(create_embeddings(chunk))\n", "\n", "# store the embeddings in the dataframe\n", "flattened_df['embeddings'] = embeddings\n", "\n", "flattened_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Retrieval\n", "\n", "Vector search and similiarity between our prompt and the database"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating an search index and reranking"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>path</th>\n", "      <th>text</th>\n", "      <th>chunks</th>\n", "      <th>embeddings</th>\n", "      <th>indices</th>\n", "      <th>distances</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td># Neural Network Frameworks As we have learned...</td>\n", "      <td>[-0.016977494582533836, 0.0028917337767779827,...</td>\n", "      <td>[0, 2, 11, 3, 1]</td>\n", "      <td>[0.0, 0.5220072028343841, 0.5281003720111753, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td>descent optimization While the `numpy` library...</td>\n", "      <td>[-0.014787919819355011, 0.0016925617819651961,...</td>\n", "      <td>[1, 0, 32, 2, 50]</td>\n", "      <td>[0.0, 0.5689486562368801, 0.5917805129945245, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td>should give us the opportunity to compute grad...</td>\n", "      <td>[-0.03673850744962692, -0.02062208764255047, 0...</td>\n", "      <td>[2, 3, 0, 5, 1]</td>\n", "      <td>[0.0, 0.5052294707599493, 0.5220072028343841, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td>those computations on GPUs is very important. ...</td>\n", "      <td>[-0.03166744112968445, -0.011117876507341862, ...</td>\n", "      <td>[3, 2, 0, 10, 11]</td>\n", "      <td>[0.0, 0.5052294707599493, 0.5456879720601056, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>data/frameworks.md</td>\n", "      <td># Neural Network Frameworks\\n\\nAs we have lear...</td>\n", "      <td>API, there is also higher-level API, called Ke...</td>\n", "      <td>[-0.007904806174337864, -0.03335562348365784, ...</td>\n", "      <td>[4, 12, 10, 9, 8]</td>\n", "      <td>[0.0, 0.5192304344185765, 0.5523440479637329, ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 path                                               text  \\\n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "0  data/frameworks.md  # Neural Network Frameworks\\n\\nAs we have lear...   \n", "\n", "                                              chunks  \\\n", "0  # Neural Network Frameworks As we have learned...   \n", "0  descent optimization While the `numpy` library...   \n", "0  should give us the opportunity to compute grad...   \n", "0  those computations on GPUs is very important. ...   \n", "0  API, there is also higher-level API, called Ke...   \n", "\n", "                                          embeddings            indices  \\\n", "0  [-0.016977494582533836, 0.0028917337767779827,...   [0, 2, 11, 3, 1]   \n", "0  [-0.014787919819355011, 0.0016925617819651961,...  [1, 0, 32, 2, 50]   \n", "0  [-0.03673850744962692, -0.02062208764255047, 0...    [2, 3, 0, 5, 1]   \n", "0  [-0.03166744112968445, -0.011117876507341862, ...  [3, 2, 0, 10, 11]   \n", "0  [-0.007904806174337864, -0.03335562348365784, ...  [4, 12, 10, 9, 8]   \n", "\n", "                                           distances  \n", "0  [0.0, 0.5220072028343841, 0.5281003720111753, ...  \n", "0  [0.0, 0.5689486562368801, 0.5917805129945245, ...  \n", "0  [0.0, 0.5052294707599493, 0.5220072028343841, ...  \n", "0  [0.0, 0.5052294707599493, 0.5456879720601056, ...  \n", "0  [0.0, 0.5192304344185765, 0.5523440479637329, ...  "]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["from sklearn.neighbors import NearestNeighbors\n", "\n", "embeddings = flattened_df['embeddings'].to_list()\n", "\n", "# Create the search index\n", "nbrs = NearestNeighbors(n_neighbors=5, algorithm='ball_tree').fit(embeddings)\n", "\n", "# To query the index, you can use the kneighbors method\n", "distances, indices = nbrs.kneighbors(embeddings)\n", "\n", "# Store the indices and distances in the DataFrame\n", "flattened_df['indices'] = indices.tolist()\n", "flattened_df['distances'] = distances.tolist()\n", "\n", "flattened_df.head()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["in our model, in which case the input vector would be a vector of size N. A perceptron is a **binary classification** model, i.e. it can distinguish between two classes of input data. We will assume that for each input vector x the output of our perceptron would be either +1 or -1, depending on the class.\n", "data/perceptron.md\n", "[0.0, 0.5349479188905069, 0.5355415711920977, 0.5439405604626569, 0.5535213920359319]\n", "# Introduction to Neural Networks: Perceptron One of the first attempts to implement something similar to a modern neural network was done by <PERSON> from Cornell Aeronautical Laboratory in 1957. It was a hardware implementation called \"Mark-1\", designed to recognize primitive geometric figures,\n", "data/perceptron.md\n", "[0.0, 0.4573465617700431, 0.5237117623258072, 0.5634745620918584, 0.5671484849463262]\n", "user to adjust the resistance of a circuit. > The New York Times wrote about perceptron at that time: *the embryo of an electronic computer that [the Navy] expects will be able to walk, talk, see, write, reproduce itself and be conscious of its existence.* ## Perceptron Model Suppose we have N features\n", "data/perceptron.md\n", "[0.0, 0.5237117623258072, 0.5439405604626569, 0.5640031504355143, 0.5743401185082532]\n", "and to continue learning - go to Perceptron notebook. Here's an interesting article about perceptrons as well. ## Assignment In this lesson, we have implemented a perceptron for binary classification task, and we have used it to classify between two handwritten digits. In this lab, you are asked to solve\n", "data/perceptron.md\n", "[0.0, 0.5106881050096326, 0.5142147678862024, 0.5291398797084144, 0.5355415711920977]\n", "# Introduction to Neural Networks. Multi-Layered Perceptron In the previous section, you learned about the simplest neural network model - one-layered perceptron, a linear two-class classification model. In this section we will extend this model into a more flexible framework, allowing us to: * perform\n", "data/own_framework.md\n", "[0.0, 0.4573465617700431, 0.5049903392874557, 0.5142147678862024, 0.5158709620578505]\n", "Index 25 not found in DataFrame\n", "in our model, in which case the input vector would be a vector of size N. A perceptron is a **binary classification** model, i.e. it can distinguish between two classes of input data. We will assume that for each input vector x the output of our perceptron would be either +1 or -1, depending on the class.\n", "data/perceptron.md\n", "[0.0, 0.5349479188905069, 0.5355415711920977, 0.5439405604626569, 0.5535213920359319]\n", "# Introduction to Neural Networks: Perceptron One of the first attempts to implement something similar to a modern neural network was done by <PERSON> from Cornell Aeronautical Laboratory in 1957. It was a hardware implementation called \"Mark-1\", designed to recognize primitive geometric figures,\n", "data/perceptron.md\n", "[0.0, 0.4573465617700431, 0.5237117623258072, 0.5634745620918584, 0.5671484849463262]\n", "user to adjust the resistance of a circuit. > The New York Times wrote about perceptron at that time: *the embryo of an electronic computer that [the Navy] expects will be able to walk, talk, see, write, reproduce itself and be conscious of its existence.* ## Perceptron Model Suppose we have N features\n", "data/perceptron.md\n", "[0.0, 0.5237117623258072, 0.5439405604626569, 0.5640031504355143, 0.5743401185082532]\n", "and to continue learning - go to Perceptron notebook. Here's an interesting article about perceptrons as well. ## Assignment In this lesson, we have implemented a perceptron for binary classification task, and we have used it to classify between two handwritten digits. In this lab, you are asked to solve\n", "data/perceptron.md\n", "[0.0, 0.5106881050096326, 0.5142147678862024, 0.5291398797084144, 0.5355415711920977]\n", "# Introduction to Neural Networks. Multi-Layered Perceptron In the previous section, you learned about the simplest neural network model - one-layered perceptron, a linear two-class classification model. In this section we will extend this model into a more flexible framework, allowing us to: * perform\n", "data/own_framework.md\n", "[0.0, 0.4573465617700431, 0.5049903392874557, 0.5142147678862024, 0.5158709620578505]\n", "Index 25 not found in DataFrame\n", "in our model, in which case the input vector would be a vector of size N. A perceptron is a **binary classification** model, i.e. it can distinguish between two classes of input data. We will assume that for each input vector x the output of our perceptron would be either +1 or -1, depending on the class.\n", "data/perceptron.md\n", "[0.0, 0.5349479188905069, 0.5355415711920977, 0.5439405604626569, 0.5535213920359319]\n", "# Introduction to Neural Networks: Perceptron One of the first attempts to implement something similar to a modern neural network was done by <PERSON> from Cornell Aeronautical Laboratory in 1957. It was a hardware implementation called \"Mark-1\", designed to recognize primitive geometric figures,\n", "data/perceptron.md\n", "[0.0, 0.4573465617700431, 0.5237117623258072, 0.5634745620918584, 0.5671484849463262]\n", "user to adjust the resistance of a circuit. > The New York Times wrote about perceptron at that time: *the embryo of an electronic computer that [the Navy] expects will be able to walk, talk, see, write, reproduce itself and be conscious of its existence.* ## Perceptron Model Suppose we have N features\n", "data/perceptron.md\n", "[0.0, 0.5237117623258072, 0.5439405604626569, 0.5640031504355143, 0.5743401185082532]\n", "and to continue learning - go to Perceptron notebook. Here's an interesting article about perceptrons as well. ## Assignment In this lesson, we have implemented a perceptron for binary classification task, and we have used it to classify between two handwritten digits. In this lab, you are asked to solve\n", "data/perceptron.md\n", "[0.0, 0.5106881050096326, 0.5142147678862024, 0.5291398797084144, 0.5355415711920977]\n", "# Introduction to Neural Networks. Multi-Layered Perceptron In the previous section, you learned about the simplest neural network model - one-layered perceptron, a linear two-class classification model. In this section we will extend this model into a more flexible framework, allowing us to: * perform\n", "data/own_framework.md\n", "[0.0, 0.4573465617700431, 0.5049903392874557, 0.5142147678862024, 0.5158709620578505]\n", "Index 25 not found in DataFrame\n"]}], "source": ["# Your text question\n", "question = \"what is a perceptron?\"\n", "\n", "# Convert the question to a query vector\n", "query_vector = create_embeddings(question)  # You need to define this function\n", "\n", "# Find the most similar documents\n", "distances, indices = nbrs.kneighbors([query_vector])\n", "\n", "index = []\n", "# Print the most similar documents\n", "for i in range(3):\n", "    index = indices[0][i]\n", "    for index in indices[0]:\n", "        print(flattened_df['chunks'].iloc[index])\n", "        print(flattened_df['path'].iloc[index])\n", "        print(flattened_df['distances'].iloc[index])\n", "    else:\n", "        print(f\"Index {index} not found in DataFrame\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Putting it all together to answer a question"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["import os\n", "import openai\n", "\n", "openai.api_type = \"azure\"\n", "openai.api_base = os.getenv(\"AZURE_OPENAI_ENDPOINT\")\n", "openai.api_version = \"2023-07-01-preview\"\n", "openai.api_key = os.getenv(\"AZURE_OPENAI_API_KEY\")"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatCompletionMessage(content='A perceptron is a type of artificial neural network model, which is a fundamental unit of a neural network. It is a simple algorithm used for binary classification tasks. The perceptron takes multiple input values, applies weights to these inputs, and produces a single output value. The output is determined by applying a step function to the weighted sum of the inputs. Perceptrons are often used as building blocks for more complex neural network architectures.', role='assistant', function_call=None, tool_calls=None)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["user_input = \"what is a perceptron?\"\n", "\n", "def chatbot(user_input):\n", "    # Convert the question to a query vector\n", "    query_vector = create_embeddings(user_input)\n", "\n", "    # Find the most similar documents\n", "    distances, indices = nbrs.kneighbors([query_vector])\n", "\n", "    # add documents to query  to provide context\n", "    history = []\n", "    for index in indices[0]:\n", "        history.append(flattened_df['chunks'].iloc[index])\n", "\n", "    # combine the history and the user input\n", "    history.append(user_input)\n", "\n", "    # create a message object\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"You are an AI assiatant that helps with AI questions.\"},\n", "        {\"role\": \"user\", \"content\": history[-1]}\n", "    ]\n", "\n", "    # use chat completion to generate a response\n", "    response = openai.chat.completions.create(\n", "        model=\"gpt-35-turbo-1106\",\n", "        temperature=0.7,\n", "        max_tokens=800,\n", "        messages=messages\n", "    )\n", "\n", "    return response.choices[0].message\n", "\n", "chatbot(user_input)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Testing and evaluation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A basic example of how you can use Mean Average Precision (MAP) to evaluate the responses of your model based on their relevance."]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["from sklearn.metrics import average_precision_score\n", "\n", "# Define your test cases\n", "test_cases = [\n", "    {\n", "        \"query\": \"What is a perceptron?\",\n", "        \"relevant_responses\": [\"A perceptron is a type of artificial neuron.\", \"It's a binary classifier used in machine learning.\"],\n", "        \"irrelevant_responses\": [\"A perceptron is a type of fruit.\", \"It's a type of car.\"]\n", "    },\n", "    {\n", "        \"query\": \"What is machine learning?\",\n", "        \"relevant_responses\": [\"Machine learning is a method of data analysis that automates analytical model building.\", \"It's a branch of artificial intelligence based on the idea that systems can learn from data, identify patterns and make decisions with minimal human intervention.\"],\n", "        \"irrelevant_responses\": [\"Machine learning is a type of fruit.\", \"It's a type of car.\"]\n", "    },\n", "    {\n", "        \"query\": \"What is deep learning?\",\n", "        \"relevant_responses\": [\"Deep learning is a subset of machine learning in artificial intelligence (AI) that has networks capable of learning unsupervised from data that is unstructured or unlabeled.\", \"It's a type of machine learning.\"],\n", "        \"irrelevant_responses\": [\"Deep learning is a type of fruit.\", \"It's a type of car.\"]\n", "    },\n", "    {\n", "        \"query\": \"What is a neural network?\",\n", "        \"relevant_responses\": [\"A neural network is a series of algorithms that endeavors to recognize underlying relationships in a set of data through a process that mimics the way the human brain operates.\", \"It's a type of machine learning.\"],\n", "        \"irrelevant_responses\": [\"A neural network is a type of fruit.\", \"It's a type of car.\"]\n", "    }\n", "]\n", "\n", "# Initialize the total average precision\n", "total_average_precision = 0\n", "\n", "# Test the RAG application\n", "for test_case in test_cases:\n", "    query = test_case[\"query\"]\n", "    relevant_responses = test_case[\"relevant_responses\"]\n", "    irrelevant_responses = test_case[\"irrelevant_responses\"]\n", "\n", "    # Generate a response using your RAG application\n", "    response = chatbot(query) \n", "\n", "    # Create a list of all responses and a list of true binary labels\n", "    all_responses = relevant_responses + irrelevant_responses\n", "    true_labels = [1] * len(relevant_responses) + [0] * len(irrelevant_responses)\n", "\n", "    # Create a list of predicted scores based on whether the response is the generated response\n", "    predicted_scores = [1 if resp == response else 0 for resp in all_responses]\n", "\n", "    # Calculate the average precision for this query\n", "    average_precision = average_precision_score(true_labels, predicted_scores)\n", "\n", "    # Add the average precision to the total average precision\n", "    total_average_precision += average_precision\n", "\n", "# Calculate the mean average precision\n", "mean_average_precision = total_average_precision / len(test_cases)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.5"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["mean_average_precision"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}