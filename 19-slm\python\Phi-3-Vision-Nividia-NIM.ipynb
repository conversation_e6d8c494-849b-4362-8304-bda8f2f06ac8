{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["import requests, base64"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["invoke_url = \"https://ai.api.nvidia.com/v1/vlm/microsoft/phi-3-vision-128k-instruct\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["with open(\"./img/demo.png\", \"rb\") as f:\n", "  image_b64 = base64.b64encode(f.read()).decode()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["len(image_b64)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["assert len(image_b64) < 180_000, \\\n", "  \"To upload larger images, use the assets API (see docs)\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["stream = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["headers = {\n", "  \"Authorization\": \"Bearer Your Nvidia NIM API Key\",\n", "  \"Accept\": \"text/event-stream\" if stream else \"application/json\"\n", "}\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["# payload = {\n", "#   \"messages\": [\n", "#     {\n", "#       \"role\": \"user\",\n", "#       \"content\": f'Tell me what is in the picture <img src=\"data:image/png;base64,{image_b64}\" />'\n", "#     }\n", "#   ],\n", "#   \"max_tokens\": 1024,\n", "#   \"temperature\": 0.6,\n", "#   \"top_p\": 1.0,\n", "#   \"stream\": stream\n", "# }\n", "\n", "payload = {\n", "  \"messages\": [\n", "    {\n", "      \"role\": \"user\",\n", "      \"content\": f'Please create Python code for image, and use plt to save the new picture under imgs/ and name it phi-3-vision.jpg. <img src=\"data:image/png;base64,{image_b64}\" />'\n", "    }\n", "  ],\n", "  \"max_tokens\": 1024,\n", "  \"temperature\": 0.6,\n", "  \"top_p\": 1.0,\n", "  \"stream\": stream\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["response = requests.post(invoke_url, headers=headers, json=payload)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["if stream:\n", "    for line in response.iter_lines():\n", "        if line:\n", "            print(line.decode(\"utf-8\"))\n", "else:\n", "    print(response.json())"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["code = response.json()[\"choices\"][0][\"message\"][\"content\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["code"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["# Find the starting index of the Python code block within the generated code\n", "# The index function locates the substring '```python' and adds 9 to skip past it\n", "begin = code.index('```python') + 9\n", "\n", "# Slice the code string from the calculated starting index to the end\n", "# This removes the initial part of the string up to and including '```python'\n", "code = code[begin:]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["end = code.index('```')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["code = code[:end]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": ["code"]}, {"cell_type": "code", "execution_count": null, "metadata": {"dotnet_interactive": {"language": "csharp"}, "polyglot_notebook": {"kernelName": "csharp"}, "vscode": {"languageId": "polyglot-notebook"}}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".NET (C#)", "language": "C#", "name": ".net-csharp"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}, "polyglot_notebook": {"kernelInfo": {"defaultKernelName": "csharp", "items": [{"aliases": [], "name": "csharp"}]}}}, "nbformat": 4, "nbformat_minor": 2}