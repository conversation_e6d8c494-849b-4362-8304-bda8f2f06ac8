{"name": "generative-ai-for-beginners", "version": "1.0.0", "description": "Generative AI for Beginners - A Curriculum", "main": "index.js", "scripts": {"convert": "node_modules/.bin/docsify-to-pdf"}, "repository": {"type": "git", "url": "git+https://github.com/microsoft/generative-ai-for-beginners.git"}, "keywords": ["generative", "ai", "curriculum"], "author": "<PERSON> and team", "license": "MIT", "bugs": {"url": "https://github.com/microsoft/generative-ai-for-beginners/issues"}, "homepage": "https://github.com/microsoft/generative-ai-for-beginners#readme", "devDependencies": {"@types/node": "^24.0.7", "docsify-to-pdf": "0.0.5"}, "dependencies": {"@azure-rest/ai-inference": "^1.0.0-beta.2", "@azure/core-auth": "^1.9.0", "openai": "^4.103.0"}}