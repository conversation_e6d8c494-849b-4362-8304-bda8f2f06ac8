#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提示工程实践练习
这个脚本不需要 API 密钥，可以立即开始学习提示工程的基本概念
"""

def tokenization_demo():
    """演示分词的概念"""
    print("=== 分词演示 ===")
    
    # 模拟简单的分词（实际的 AI 模型使用更复杂的分词器）
    text = "Hello, how are you today?"
    
    # 按空格分词（简化版本）
    simple_tokens = text.split()
    print(f"原文: {text}")
    print(f"简单分词: {simple_tokens}")
    print(f"Token 数量: {len(simple_tokens)}")
    
    # 演示标点符号的影响
    text_with_punctuation = "Hello, world! How are you?"
    tokens_with_punct = text_with_punctuation.replace(",", " ,").replace("!", " !").replace("?", " ?").split()
    print(f"\n带标点的文本: {text_with_punctuation}")
    print(f"处理标点后的分词: {tokens_with_punct}")
    print(f"Token 数量: {len(tokens_with_punct)}")

def prompt_structure_demo():
    """演示提示词结构"""
    print("\n=== 提示词结构演示 ===")
    
    # 基础提示
    basic_prompt = "写一篇关于人工智能的文章"
    print(f"基础提示: {basic_prompt}")
    
    # 结构化提示
    structured_prompt = """
系统角色: 你是一位专业的科技写作专家

任务: 写一篇关于人工智能的文章

要求:
- 长度: 500字左右
- 受众: 高中生
- 风格: 通俗易懂
- 结构: 包含引言、主体、结论

格式: 使用 Markdown 格式
"""
    print(f"\n结构化提示: {structured_prompt}")

def few_shot_demo():
    """演示少样本学习"""
    print("\n=== 少样本学习演示 ===")
    
    print("任务: 根据描述判断体育项目")
    
    examples = [
        ("球员跑垒", "棒球"),
        ("球员发出ace球", "网球"),
        ("球员击出六分", "板球"),
    ]
    
    print("\n示例:")
    for description, sport in examples:
        print(f'"{description}" => {sport}')
    
    test_case = "球员扣篮"
    print(f'\n测试: "{test_case}" => ?')
    print("预期答案: 篮球")

def temperature_simulation():
    """模拟温度参数的影响"""
    print("\n=== 温度参数模拟 ===")
    
    prompt = "生成一个 Python Web API 的代码"
    
    print(f"提示: {prompt}")
    
    # 模拟低温度 (0.1) - 更确定性的输出
    print("\n温度 = 0.1 (更确定性):")
    low_temp_responses = [
        "from flask import Flask\napp = Flask(__name__)",
        "from flask import Flask\napp = Flask(__name__)",
        "from flask import Flask\napp = Flask(__name__)"
    ]
    for i, response in enumerate(low_temp_responses, 1):
        print(f"运行 {i}: {response}")
    
    # 模拟高温度 (0.9) - 更多样性的输出
    print("\n温度 = 0.9 (更有创意):")
    high_temp_responses = [
        "import flask\nfrom flask import request, jsonify",
        "from flask import Flask, render_template",
        "import fastapi\nfrom fastapi import FastAPI"
    ]
    for i, response in enumerate(high_temp_responses, 1):
        print(f"运行 {i}: {response}")

def prompt_engineering_tips():
    """提示工程技巧总结"""
    print("\n=== 提示工程最佳实践 ===")
    
    tips = [
        "1. 具体明确: 避免模糊的指令",
        "2. 提供上下文: 说明任务的背景和目标",
        "3. 指定格式: 明确输出的结构和样式",
        "4. 使用示例: 通过例子展示期望的输出",
        "5. 设置约束: 限制长度、风格、内容范围",
        "6. 迭代优化: 根据结果不断改进提示词",
        "7. 测试变化: 尝试不同的措辞和结构"
    ]
    
    for tip in tips:
        print(tip)

def interactive_practice():
    """交互式练习"""
    print("\n=== 交互式练习 ===")
    print("让我们一起改进一个提示词!")
    
    # 原始提示
    original = "写代码"
    print(f"原始提示: '{original}'")
    print("这个提示太模糊了!")
    
    # 改进步骤
    improvements = [
        ("添加具体任务", "写一个 Python 函数"),
        ("指定功能", "写一个 Python 函数来计算两个数的和"),
        ("添加格式要求", "写一个 Python 函数来计算两个数的和，包含文档字符串和类型提示"),
        ("添加示例", "写一个 Python 函数来计算两个数的和，包含文档字符串和类型提示。\n示例调用: add(3, 5) 应该返回 8")
    ]
    
    current_prompt = original
    for step, improved_prompt in improvements:
        print(f"\n改进: {step}")
        print(f"新提示: '{improved_prompt}'")
        current_prompt = improved_prompt
    
    print(f"\n最终提示比原始提示好多了!")

def main():
    """主函数"""
    print("🤖 欢迎来到提示工程实践课堂!")
    print("这个练习将帮助您理解提示工程的核心概念")
    
    # 运行所有演示
    tokenization_demo()
    prompt_structure_demo()
    few_shot_demo()
    temperature_simulation()
    prompt_engineering_tips()
    interactive_practice()
    
    print("\n🎉 练习完成!")
    print("现在您可以尝试:")
    print("1. 修改这个脚本中的示例")
    print("2. 创建自己的提示词")
    print("3. 配置 API 密钥后尝试真实的 AI 调用")

if __name__ == "__main__":
    main()
