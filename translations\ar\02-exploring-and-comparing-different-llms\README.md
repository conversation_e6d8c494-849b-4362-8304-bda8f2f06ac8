<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "e2f686f2eb794941761252ac5e8e090b",
  "translation_date": "2025-07-09T08:10:18+00:00",
  "source_file": "02-exploring-and-comparing-different-llms/README.md",
  "language_code": "ar"
}
-->
# استكشاف ومقارنة نماذج اللغة الكبيرة المختلفة

[![استكشاف ومقارنة نماذج اللغة الكبيرة المختلفة](../../../translated_images/02-lesson-banner.ef94c84979f97f60f07e27d905e708cbcbdf78707120553ccab27d91c947805b.ar.png)](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)

> _انقر على الصورة أعلاه لمشاهدة فيديو هذا الدرس_

في الدرس السابق، رأينا كيف يغير الذكاء الاصطناعي التوليدي مشهد التكنولوجيا، وكيف تعمل نماذج اللغة الكبيرة (LLMs) وكيف يمكن للأعمال - مثل شركتنا الناشئة - تطبيقها على حالات الاستخدام الخاصة بها والنمو! في هذا الفصل، سنقوم بمقارنة وتباين أنواع مختلفة من نماذج اللغة الكبيرة لفهم مزاياها وعيوبها.

الخطوة التالية في رحلة شركتنا الناشئة هي استكشاف المشهد الحالي لنماذج اللغة الكبيرة وفهم أيها مناسب لحالة الاستخدام الخاصة بنا.

## المقدمة

سيغطي هذا الدرس:

- أنواع مختلفة من نماذج اللغة الكبيرة في المشهد الحالي.
- اختبار، تكرار، ومقارنة النماذج المختلفة لحالة الاستخدام الخاصة بك في Azure.
- كيفية نشر نموذج لغة كبير.

## أهداف التعلم

بعد إكمال هذا الدرس، ستكون قادرًا على:

- اختيار النموذج المناسب لحالة الاستخدام الخاصة بك.
- فهم كيفية اختبار، تكرار، وتحسين أداء النموذج الخاص بك.
- معرفة كيف تنشر الشركات النماذج.

## فهم أنواع مختلفة من نماذج اللغة الكبيرة

يمكن تصنيف نماذج اللغة الكبيرة بعدة طرق بناءً على هيكلها، وبيانات التدريب، وحالة الاستخدام. فهم هذه الاختلافات سيساعد شركتنا الناشئة على اختيار النموذج المناسب للسيناريو، وفهم كيفية اختبار، تكرار، وتحسين الأداء.

هناك العديد من أنواع نماذج اللغة الكبيرة، ويعتمد اختيارك للنموذج على ما تهدف لاستخدامه، وبياناتك، ومدى استعدادك للدفع، وأكثر.

اعتمادًا على ما إذا كنت تهدف لاستخدام النماذج للنص، أو الصوت، أو الفيديو، أو توليد الصور، وما إلى ذلك، قد تختار نوعًا مختلفًا من النماذج.

- **التعرف على الصوت والكلام**. لهذا الغرض، تعتبر نماذج نوع Whisper خيارًا ممتازًا لأنها متعددة الأغراض ومخصصة للتعرف على الكلام. تم تدريبها على مجموعة متنوعة من الأصوات ويمكنها أداء التعرف على الكلام بعدة لغات. تعرّف أكثر على [نماذج نوع Whisper هنا](https://platform.openai.com/docs/models/whisper?WT.mc_id=academic-105485-koreyst).

- **توليد الصور**. لتوليد الصور، يُعتبر DALL-E وMidjourney خيارين معروفين جدًا. DALL-E متاح عبر Azure OpenAI. [اقرأ المزيد عن DALL-E هنا](https://platform.openai.com/docs/models/dall-e?WT.mc_id=academic-105485-koreyst) وأيضًا في الفصل التاسع من هذا المنهج.

- **توليد النصوص**. معظم النماذج مدربة على توليد النصوص ولديك مجموعة واسعة من الخيارات من GPT-3.5 إلى GPT-4. تأتي بتكاليف مختلفة مع كون GPT-4 هو الأغلى. من المفيد تجربة [ملعب Azure OpenAI](https://oai.azure.com/portal/playground?WT.mc_id=academic-105485-koreyst) لتقييم أي النماذج تناسب احتياجاتك من حيث القدرات والتكلفة.

- **التعددية النمطية**. إذا كنت ترغب في التعامل مع أنواع متعددة من البيانات في الإدخال والإخراج، قد ترغب في الاطلاع على نماذج مثل [gpt-4 turbo مع الرؤية أو gpt-4o](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#gpt-4-and-gpt-4-turbo-models?WT.mc_id=academic-105485-koreyst) - أحدث إصدارات نماذج OpenAI - التي تجمع بين معالجة اللغة الطبيعية والفهم البصري، مما يتيح التفاعل عبر واجهات متعددة الأنماط.

اختيار نموذج يعني أنك تحصل على بعض القدرات الأساسية، والتي قد لا تكون كافية دائمًا. غالبًا ما يكون لديك بيانات خاصة بالشركة تحتاج بطريقة ما إلى إبلاغ نموذج اللغة الكبير بها. هناك عدة خيارات مختلفة لكيفية التعامل مع ذلك، سنتناولها في الأقسام القادمة.

### النماذج الأساسية مقابل نماذج اللغة الكبيرة

مصطلح النموذج الأساسي تم [صياغته من قبل باحثي ستانفورد](https://arxiv.org/abs/2108.07258?WT.mc_id=academic-105485-koreyst) ويُعرف كنموذج ذكاء اصطناعي يتبع بعض المعايير، مثل:

- **يتم تدريبه باستخدام التعلم غير المراقب أو التعلم الذاتي المراقب**، مما يعني أنه يتم تدريبه على بيانات متعددة الأنماط غير معنونة، ولا يتطلب تعليقًا بشريًا أو تصنيفًا للبيانات أثناء عملية التدريب.
- **هو نموذج ضخم جدًا**، يعتمد على شبكات عصبية عميقة جدًا مدربة على مليارات المعاملات.
- **عادة ما يُقصد به أن يكون "أساسًا" لنماذج أخرى**، بمعنى أنه يمكن استخدامه كنقطة انطلاق لبناء نماذج أخرى فوقه، وذلك من خلال التخصيص الدقيق.

![النماذج الأساسية مقابل نماذج اللغة الكبيرة](../../../translated_images/FoundationModel.e4859dbb7a825c94b284f17eae1c186aabc21d4d8644331f5b007d809cf8d0f2.ar.png)

مصدر الصورة: [الدليل الأساسي للنماذج الأساسية ونماذج اللغة الكبيرة | بقلم بابر م بهاتي | Medium](https://thebabar.medium.com/essential-guide-to-foundation-models-and-large-language-models-27dab58f7404)

لتوضيح هذا الفرق أكثر، لنأخذ ChatGPT كمثال. لبناء النسخة الأولى من ChatGPT، استخدم نموذج يسمى GPT-3.5 كنموذج أساسي. هذا يعني أن OpenAI استخدمت بعض بيانات المحادثة الخاصة لإنشاء نسخة مخصصة من GPT-3.5 متخصصة في الأداء الجيد في سيناريوهات المحادثة، مثل روبوتات الدردشة.

![النموذج الأساسي](../../../translated_images/Multimodal.2c389c6439e0fc51b0b7b226d95d7d900d372ae66902d71b8ce5ec4951b8efbe.ar.png)

مصدر الصورة: [2108.07258.pdf (arxiv.org)](https://arxiv.org/pdf/2108.07258.pdf?WT.mc_id=academic-105485-koreyst)

### النماذج مفتوحة المصدر مقابل النماذج المملوكة

طريقة أخرى لتصنيف نماذج اللغة الكبيرة هي ما إذا كانت مفتوحة المصدر أو مملوكة.

النماذج مفتوحة المصدر هي نماذج متاحة للجمهور ويمكن لأي شخص استخدامها. غالبًا ما توفرها الشركة التي أنشأتها أو المجتمع البحثي. يُسمح بفحص هذه النماذج وتعديلها وتخصيصها لمختلف حالات الاستخدام في نماذج اللغة الكبيرة. ومع ذلك، فهي ليست دائمًا محسنة للاستخدام الإنتاجي، وقد لا تكون بنفس كفاءة النماذج المملوكة. بالإضافة إلى ذلك، قد يكون التمويل للنماذج مفتوحة المصدر محدودًا، وقد لا يتم صيانتها على المدى الطويل أو تحديثها بأحدث الأبحاث. من أمثلة النماذج مفتوحة المصدر الشهيرة: [Alpaca](https://crfm.stanford.edu/2023/03/13/alpaca.html?WT.mc_id=academic-105485-koreyst)، [Bloom](https://huggingface.co/bigscience/bloom) و[LLaMA](https://llama.meta.com).

النماذج المملوكة هي نماذج تملكها شركة ولا تتاح للجمهور. غالبًا ما تكون هذه النماذج محسنة للاستخدام الإنتاجي. ومع ذلك، لا يُسمح بفحصها أو تعديلها أو تخصيصها لحالات استخدام مختلفة. كما أنها ليست متاحة دائمًا مجانًا، وقد تتطلب اشتراكًا أو دفعًا للاستخدام. أيضًا، لا يملك المستخدمون السيطرة على البيانات المستخدمة في تدريب النموذج، مما يعني أنه يجب عليهم الوثوق بمالك النموذج لضمان الالتزام بخصوصية البيانات والاستخدام المسؤول للذكاء الاصطناعي. من أمثلة النماذج المملوكة الشهيرة: [نماذج OpenAI](https://platform.openai.com/docs/models/overview?WT.mc_id=academic-105485-koreyst)، [Google Bard](https://sapling.ai/llm/bard?WT.mc_id=academic-105485-koreyst) أو [Claude 2](https://www.anthropic.com/index/claude-2?WT.mc_id=academic-105485-koreyst).

### التضمين مقابل توليد الصور مقابل توليد النصوص والرموز

يمكن أيضًا تصنيف نماذج اللغة الكبيرة حسب نوع المخرجات التي تولدها.

التضمينات هي مجموعة من النماذج التي يمكنها تحويل النص إلى شكل رقمي يُسمى التضمين، وهو تمثيل رقمي للنص المدخل. تجعل التضمينات من السهل على الآلات فهم العلاقات بين الكلمات أو الجمل، ويمكن استخدامها كمدخلات لنماذج أخرى، مثل نماذج التصنيف أو التجميع التي تحقق أداءً أفضل على البيانات الرقمية. غالبًا ما تُستخدم نماذج التضمين في التعلم الانتقالي، حيث يُبنى نموذج لمهمة بديلة تتوفر لها بيانات كثيرة، ثم تُعاد استخدام أوزان النموذج (التضمينات) لمهام أخرى لاحقة. مثال على هذه الفئة هو [تضمينات OpenAI](https://platform.openai.com/docs/models/embeddings?WT.mc_id=academic-105485-koreyst).

![التضمين](../../../translated_images/Embedding.c3708fe988ccf76073d348483dbb7569f622211104f073e22e43106075c04800.ar.png)

نماذج توليد الصور هي نماذج تولد صورًا. غالبًا ما تُستخدم هذه النماذج لتحرير الصور، تركيب الصور، وترجمة الصور. يتم تدريب نماذج توليد الصور على مجموعات بيانات ضخمة من الصور، مثل [LAION-5B](https://laion.ai/blog/laion-5b/?WT.mc_id=academic-105485-koreyst)، ويمكن استخدامها لتوليد صور جديدة أو لتحرير الصور الموجودة باستخدام تقنيات مثل التلوين، تحسين الدقة، والتلوين. من الأمثلة [DALL-E-3](https://openai.com/dall-e-3?WT.mc_id=academic-105485-koreyst) و[نماذج Stable Diffusion](https://github.com/Stability-AI/StableDiffusion?WT.mc_id=academic-105485-koreyst).

![توليد الصور](../../../translated_images/Image.349c080266a763fd255b840a921cd8fc526ed78dc58708fa569ff1873d302345.ar.png)

نماذج توليد النصوص والرموز هي نماذج تولد نصًا أو رمزًا برمجيًا. غالبًا ما تُستخدم هذه النماذج لتلخيص النصوص، الترجمة، والإجابة على الأسئلة. يتم تدريب نماذج توليد النصوص على مجموعات بيانات ضخمة من النصوص، مثل [BookCorpus](https://www.cv-foundation.org/openaccess/content_iccv_2015/html/Zhu_Aligning_Books_and_ICCV_2015_paper.html?WT.mc_id=academic-105485-koreyst)، ويمكن استخدامها لتوليد نص جديد أو للإجابة على الأسئلة. نماذج توليد الرموز، مثل [CodeParrot](https://huggingface.co/codeparrot?WT.mc_id=academic-105485-koreyst)، غالبًا ما تُدرّب على مجموعات بيانات ضخمة من الرموز البرمجية، مثل GitHub، ويمكن استخدامها لتوليد رمز جديد أو إصلاح الأخطاء في الرموز الموجودة.

![توليد النصوص والرموز](../../../translated_images/Text.a8c0cf139e5cc2a0cd3edaba8d675103774e6ddcb3c9fc5a98bb17c9a450e31d.ar.png)

### الترميز-فك الترميز مقابل فك الترميز فقط

لنتحدث عن أنواع الهياكل المختلفة لنماذج اللغة الكبيرة، دعونا نستخدم تشبيهًا.

تخيل أن مديرك كلفك بمهمة كتابة اختبار للطلاب. لديك زميلان؛ أحدهما مسؤول عن إنشاء المحتوى والآخر مسؤول عن مراجعته.

منشئ المحتوى يشبه نموذج فك الترميز فقط، يمكنه النظر إلى الموضوع ورؤية ما كتبته بالفعل ثم كتابة دورة بناءً على ذلك. هم جيدون جدًا في كتابة محتوى جذاب ومفيد، لكنهم ليسوا جيدين جدًا في فهم الموضوع وأهداف التعلم. بعض أمثلة نماذج فك الترميز هي عائلة GPT، مثل GPT-3.

المراجع يشبه نموذج الترميز فقط، ينظر إلى الدورة المكتوبة والإجابات، يلاحظ العلاقة بينهما ويفهم السياق، لكنه ليس جيدًا في توليد المحتوى. مثال على نموذج الترميز فقط هو BERT.

تخيل أنه يمكننا أن يكون لدينا شخص يمكنه إنشاء ومراجعة الاختبار، هذا هو نموذج الترميز-فك الترميز. بعض الأمثلة هي BART وT5.

### الخدمة مقابل النموذج

الآن، دعونا نتحدث عن الفرق بين الخدمة والنموذج. الخدمة هي منتج يقدمه مزود خدمة سحابية، وغالبًا ما تكون مزيجًا من النماذج، والبيانات، ومكونات أخرى. النموذج هو المكون الأساسي للخدمة، وغالبًا ما يكون نموذجًا أساسيًا، مثل نموذج لغة كبير.

غالبًا ما تكون الخدمات محسنة للاستخدام الإنتاجي وأسهل في الاستخدام من النماذج، عبر واجهة مستخدم رسومية. ومع ذلك، ليست الخدمات متاحة دائمًا مجانًا، وقد تتطلب اشتراكًا أو دفعًا للاستخدام، مقابل الاستفادة من معدات وموارد مالك الخدمة، مما يحسن التكاليف ويسهل التوسع. مثال على خدمة هو [خدمة Azure OpenAI](https://learn.microsoft.com/azure/ai-services/openai/overview?WT.mc_id=academic-105485-koreyst)، التي تقدم خطة دفع حسب الاستخدام، مما يعني أن المستخدمين يُحاسبون بنسبة استخدامهم للخدمة. كما تقدم خدمة Azure OpenAI أمانًا على مستوى المؤسسات وإطار عمل للذكاء الاصطناعي المسؤول فوق قدرات النماذج.

النماذج هي فقط الشبكة العصبية، مع المعاملات، والأوزان، وغيرها. تسمح للشركات بالتشغيل محليًا، ولكنها تحتاج إلى شراء معدات، وبناء بنية للتوسع، وشراء ترخيص أو استخدام نموذج مفتوح المصدر. نموذج مثل LLaMA متاح للاستخدام، ويتطلب قوة حوسبة لتشغيل النموذج.

## كيفية اختبار وتكرار النماذج المختلفة لفهم الأداء على Azure

بمجرد أن يستكشف فريقنا المشهد الحالي لنماذج اللغة الكبيرة ويحدد بعض المرشحين الجيدين لسيناريوهاتهم، تكون الخطوة التالية هي اختبارهم على بياناتهم وعلى عبء العمل الخاص بهم. هذه عملية تكرارية، تتم من خلال التجارب والقياسات.
معظم النماذج التي ذكرناها في الفقرات السابقة (نماذج OpenAI، النماذج مفتوحة المصدر مثل Llama2، ومحولات Hugging Face) متاحة في [كتالوج النماذج](https://learn.microsoft.com/azure/ai-studio/how-to/model-catalog-overview?WT.mc_id=academic-105485-koreyst) في [Azure AI Studio](https://ai.azure.com/?WT.mc_id=academic-105485-koreyst).

[Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/what-is-ai-studio?WT.mc_id=academic-105485-koreyst) هي منصة سحابية مصممة للمطورين لبناء تطبيقات الذكاء الاصطناعي التوليدي وإدارة دورة حياة التطوير كاملة - من التجريب إلى التقييم - من خلال دمج جميع خدمات Azure AI في مركز واحد مع واجهة مستخدم سهلة الاستخدام. يتيح كتالوج النماذج في Azure AI Studio للمستخدم:

- العثور على نموذج الأساس المطلوب في الكتالوج - سواء كان مملوكًا أو مفتوح المصدر، مع إمكانية التصفية حسب المهمة أو الترخيص أو الاسم. لتحسين إمكانية البحث، يتم تنظيم النماذج في مجموعات، مثل مجموعة Azure OpenAI، ومجموعة Hugging Face، والمزيد.

![كتالوج النماذج](../../../translated_images/AzureAIStudioModelCatalog.3cf8a499aa8ba0314f2c73d4048b3225d324165f547525f5b7cfa5f6c9c68941.ar.png)

- مراجعة بطاقة النموذج، التي تتضمن وصفًا مفصلًا للاستخدام المقصود وبيانات التدريب، وأمثلة على الشيفرة، ونتائج التقييم في مكتبة التقييمات الداخلية.

![بطاقة النموذج](../../../translated_images/ModelCard.598051692c6e400d681a713ba7717e8b6e5e65f08d12131556fcec0f1789459b.ar.png)

- مقارنة المعايير المرجعية بين النماذج ومجموعات البيانات المتاحة في الصناعة لتقييم أيها يلبي سيناريو العمل، من خلال لوحة [معايير النماذج](https://learn.microsoft.com/azure/ai-studio/how-to/model-benchmarks?WT.mc_id=academic-105485-koreyst).

![معايير النماذج](../../../translated_images/ModelBenchmarks.254cb20fbd06c03a4ca53994585c5ea4300a88bcec8eff0450f2866ee2ac5ff3.ar.png)

- تحسين النموذج باستخدام بيانات تدريب مخصصة لتحسين أداء النموذج في عبء عمل محدد، مستفيدًا من قدرات التجريب والتتبع في Azure AI Studio.

![تحسين النموذج](../../../translated_images/FineTuning.aac48f07142e36fddc6571b1f43ea2e003325c9c6d8e3fc9d8834b771e308dbf.ar.png)

- نشر النموذج الأصلي المدرب مسبقًا أو النسخة المحسنة إلى نقطة استدلال عن بُعد في الوقت الحقيقي - حوسبة مُدارة - أو نقطة نهاية API بدون خادم - [الدفع حسب الاستخدام](https://learn.microsoft.com/azure/ai-studio/how-to/model-catalog-overview#model-deployment-managed-compute-and-serverless-api-pay-as-you-go?WT.mc_id=academic-105485-koreyst) - لتمكين التطبيقات من استهلاكه.

![نشر النموذج](../../../translated_images/ModelDeploy.890da48cbd0bccdb4abfc9257f3d884831e5d41b723e7d1ceeac9d60c3c4f984.ar.png)


> [!NOTE]
> ليست كل النماذج في الكتالوج متاحة حاليًا للتحسين و/أو النشر بنظام الدفع حسب الاستخدام. تحقق من بطاقة النموذج لمعرفة تفاصيل قدرات النموذج وقيوده.

## تحسين نتائج LLM

قمنا مع فريق شركتنا الناشئة بتجربة أنواع مختلفة من LLM ومنصة سحابية (Azure Machine Learning) تتيح لنا مقارنة النماذج المختلفة، تقييمها على بيانات الاختبار، تحسين الأداء، ونشرها على نقاط الاستدلال.

لكن متى يجب التفكير في تحسين نموذج بدلاً من استخدام نموذج مدرب مسبقًا؟ هل هناك طرق أخرى لتحسين أداء النموذج على أعباء عمل محددة؟

هناك عدة طرق يمكن للأعمال استخدامها للحصول على النتائج التي تحتاجها من LLM. يمكنك اختيار أنواع مختلفة من النماذج بدرجات تدريب مختلفة عند نشر LLM في الإنتاج، مع مستويات مختلفة من التعقيد والتكلفة والجودة. إليك بعض الطرق المختلفة:

- **هندسة المطالبات مع السياق**. الفكرة هي توفير سياق كافٍ عند تقديم المطالبة لضمان الحصول على الردود التي تحتاجها.

- **التوليد المعزز بالاسترجاع، RAG**. قد تكون بياناتك موجودة في قاعدة بيانات أو نقطة نهاية ويب، على سبيل المثال، ولضمان تضمين هذه البيانات، أو جزء منها، في وقت المطالبة، يمكنك جلب البيانات ذات الصلة وجعلها جزءًا من مطالبة المستخدم.

- **النموذج المحسن**. هنا، قمت بتدريب النموذج أكثر على بياناتك الخاصة مما يجعل النموذج أكثر دقة واستجابة لاحتياجاتك، لكنه قد يكون مكلفًا.

![نشر LLMs](../../../translated_images/Deploy.18b2d27412ec8c02871386cbe91097c7f2190a8c6e2be88f66392b411609a48c.ar.png)

مصدر الصورة: [أربع طرق تنشر بها المؤسسات LLMs | مدونة Fiddler AI](https://www.fiddler.ai/blog/four-ways-that-enterprises-deploy-llms?WT.mc_id=academic-105485-koreyst)

### هندسة المطالبات مع السياق

تعمل نماذج LLM المدربة مسبقًا بشكل جيد جدًا في المهام العامة للغة الطبيعية، حتى عند استدعائها بمطالبة قصيرة، مثل جملة لإكمالها أو سؤال – ما يُعرف بـ "التعلم بدون عينة".

ومع ذلك، كلما استطاع المستخدم صياغة استفساره بشكل أفضل، مع طلب مفصل وأمثلة – أي السياق – كانت الإجابة أكثر دقة وأقرب لتوقعات المستخدم. في هذه الحالة، نتحدث عن "التعلم بعينة واحدة" إذا كانت المطالبة تحتوي على مثال واحد فقط، و"التعلم بعدة عينات" إذا احتوت على عدة أمثلة.
هندسة المطالبات مع السياق هي الطريقة الأكثر فعالية من حيث التكلفة للبدء.

### التوليد المعزز بالاسترجاع (RAG)

تواجه نماذج LLM محدودية في أنها تستخدم فقط البيانات التي تم تدريبها عليها لتوليد الإجابة. هذا يعني أنها لا تعرف شيئًا عن الأحداث التي وقعت بعد عملية التدريب، ولا يمكنها الوصول إلى المعلومات غير العامة (مثل بيانات الشركة).
يمكن تجاوز هذا من خلال تقنية RAG، التي تعزز المطالبة ببيانات خارجية على شكل أجزاء من الوثائق، مع مراعاة حدود طول المطالبة. تدعم هذه التقنية أدوات قواعد البيانات المتجهية (مثل [Azure Vector Search](https://learn.microsoft.com/azure/search/vector-search-overview?WT.mc_id=academic-105485-koreyst)) التي تسترجع الأجزاء المفيدة من مصادر بيانات محددة مسبقًا وتضيفها إلى سياق المطالبة.

هذه التقنية مفيدة جدًا عندما لا تمتلك الشركة بيانات كافية، أو وقتًا كافيًا، أو موارد لتحسين نموذج LLM، لكنها ترغب في تحسين الأداء على عبء عمل محدد وتقليل مخاطر التزييف، أي تحريف الواقع أو المحتوى الضار.

### النموذج المحسن

التحسين هو عملية تستخدم التعلم بالنقل لـ "تكييف" النموذج لمهمة محددة أو لحل مشكلة معينة. بخلاف التعلم بعدة عينات وRAG، ينتج عن ذلك نموذج جديد يتم إنشاؤه مع أوزان وانحيازات محدثة. يتطلب مجموعة من أمثلة التدريب التي تتكون من مدخل واحد (المطالبة) والمخرج المرتبط بها (الإكمال).
هذه هي الطريقة المفضلة إذا:

- **استخدام النماذج المحسنة**. ترغب الشركة في استخدام نماذج محسنة أقل قدرة (مثل نماذج التضمين) بدلاً من النماذج عالية الأداء، مما يؤدي إلى حل أكثر فعالية من حيث التكلفة وسرعة.

- **مراعاة زمن الاستجابة**. زمن الاستجابة مهم لحالة استخدام معينة، لذلك لا يمكن استخدام مطالبات طويلة جدًا أو عدد الأمثلة التي يجب أن يتعلمها النموذج لا يتناسب مع حد طول المطالبة.

- **البقاء محدثًا**. تمتلك الشركة الكثير من البيانات عالية الجودة والتسميات الحقيقية والموارد اللازمة للحفاظ على تحديث هذه البيانات بمرور الوقت.

### النموذج المدرب

تدريب نموذج LLM من الصفر هو بلا شك أصعب وأعقد نهج يمكن اتباعه، ويتطلب كميات هائلة من البيانات، وموارد ماهرة، وقوة حوسبة مناسبة. يجب النظر في هذا الخيار فقط في حالة وجود حالة استخدام متخصصة في مجال معين وكمية كبيرة من البيانات المرتكزة على هذا المجال.

## اختبار المعرفة

ما هي الطريقة الجيدة لتحسين نتائج إكمال LLM؟

1. هندسة المطالبات مع السياق  
2. RAG  
3. النموذج المحسن

الإجابة: 3، إذا كان لديك الوقت والموارد وبيانات عالية الجودة، فإن التحسين هو الخيار الأفضل للبقاء محدثًا. ومع ذلك، إذا كنت تبحث عن تحسين الأمور وتفتقر إلى الوقت، فمن المفيد التفكير في RAG أولاً.

## 🚀 التحدي

اقرأ المزيد عن كيفية [استخدام RAG](https://learn.microsoft.com/azure/search/retrieval-augmented-generation-overview?WT.mc_id=academic-105485-koreyst) لأعمالك.

## عمل رائع، استمر في التعلم

بعد إكمال هذا الدرس، اطلع على [مجموعة تعلم الذكاء الاصطناعي التوليدي](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) لمواصلة تطوير معرفتك في الذكاء الاصطناعي التوليدي!

توجه إلى الدرس 3 حيث سنستعرض كيفية [البناء باستخدام الذكاء الاصطناعي التوليدي بمسؤولية](../03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)!

**إخلاء المسؤولية**:  
تمت ترجمة هذا المستند باستخدام خدمة الترجمة الآلية [Co-op Translator](https://github.com/Azure/co-op-translator). بينما نسعى لتحقيق الدقة، يرجى العلم أن الترجمات الآلية قد تحتوي على أخطاء أو عدم دقة. يجب اعتبار المستند الأصلي بلغته الأصلية المصدر الموثوق به. للمعلومات الهامة، يُنصح بالاعتماد على الترجمة البشرية المهنية. نحن غير مسؤولين عن أي سوء فهم أو تفسير ناتج عن استخدام هذه الترجمة.