<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "ea4bbe640847aafbbba14dae4625e9af",
  "translation_date": "2025-07-09T12:16:09+00:00",
  "source_file": "07-building-chat-applications/README.md",
  "language_code": "ar"
}
-->
# بناء تطبيقات الدردشة المدعومة بالذكاء الاصطناعي التوليدي

[![بناء تطبيقات الدردشة المدعومة بالذكاء الاصطناعي التوليدي](../../../translated_images/07-lesson-banner.a279b937f2843833fe28b4597f51bdef92d0ad03efee7ba52d0f166dea7574e5.ar.png)](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst)

> _(انقر على الصورة أعلاه لمشاهدة فيديو هذا الدرس)_

بعد أن تعرفنا على كيفية بناء تطبيقات توليد النصوص، دعونا ننتقل إلى تطبيقات الدردشة.

أصبحت تطبيقات الدردشة جزءًا لا يتجزأ من حياتنا اليومية، حيث تقدم أكثر من مجرد وسيلة للمحادثات العادية. فهي تلعب دورًا أساسيًا في خدمة العملاء، والدعم الفني، وحتى في أنظمة الاستشارات المتقدمة. من المحتمل أنك قد حصلت على مساعدة من تطبيق دردشة في وقت قريب. ومع دمج تقنيات متقدمة مثل الذكاء الاصطناعي التوليدي في هذه المنصات، تزداد التعقيدات وكذلك التحديات.

بعض الأسئلة التي نحتاج إلى إجابات لها هي:

- **بناء التطبيق**. كيف نبني هذه التطبيقات المدعومة بالذكاء الاصطناعي بكفاءة وندمجها بسلاسة لحالات استخدام محددة؟
- **المراقبة**. بعد النشر، كيف يمكننا مراقبة وضمان عمل التطبيقات بأعلى مستوى من الجودة، سواء من حيث الوظائف أو الالتزام بـ [المبادئ الستة للذكاء الاصطناعي المسؤول](https://www.microsoft.com/ai/responsible-ai?WT.mc_id=academic-105485-koreyst)؟

مع تقدمنا في عصر يتميز بالأتمتة والتفاعل السلس بين الإنسان والآلة، يصبح فهم كيفية تحول الذكاء الاصطناعي التوليدي لنطاق وعمق وقابلية التكيف في تطبيقات الدردشة أمرًا ضروريًا. سيتناول هذا الدرس جوانب البنية التي تدعم هذه الأنظمة المعقدة، ويغوص في منهجيات ضبطها لمهام محددة، ويقيّم المقاييس والاعتبارات المتعلقة بضمان نشر الذكاء الاصطناعي بمسؤولية.

## المقدمة

يغطي هذا الدرس:

- تقنيات بناء ودمج تطبيقات الدردشة بكفاءة.
- كيفية تطبيق التخصيص وضبط الأداء على التطبيقات.
- استراتيجيات واعتبارات لمراقبة تطبيقات الدردشة بفعالية.

## أهداف التعلم

بنهاية هذا الدرس، ستكون قادرًا على:

- وصف الاعتبارات الخاصة ببناء ودمج تطبيقات الدردشة ضمن الأنظمة القائمة.
- تخصيص تطبيقات الدردشة لحالات استخدام محددة.
- تحديد المقاييس الرئيسية والاعتبارات لمراقبة وصيانة جودة تطبيقات الدردشة المدعومة بالذكاء الاصطناعي بفعالية.
- ضمان استخدام تطبيقات الدردشة للذكاء الاصطناعي بمسؤولية.

## دمج الذكاء الاصطناعي التوليدي في تطبيقات الدردشة

رفع مستوى تطبيقات الدردشة عبر الذكاء الاصطناعي التوليدي لا يقتصر فقط على جعلها أكثر ذكاءً؛ بل يتعلق بتحسين بنيتها وأدائها وواجهة المستخدم لتقديم تجربة مستخدم عالية الجودة. يشمل ذلك دراسة الأسس المعمارية، تكامل واجهات برمجة التطبيقات، واعتبارات واجهة المستخدم. تهدف هذه الفقرة إلى تقديم خارطة طريق شاملة تساعدك على التنقل في هذه المجالات المعقدة، سواء كنت تدمجها في أنظمة موجودة أو تبنيها كمنصات مستقلة.

بنهاية هذا القسم، ستكون مجهزًا بالخبرة اللازمة لبناء ودمج تطبيقات الدردشة بكفاءة.

### روبوت الدردشة أم تطبيق الدردشة؟

قبل أن نبدأ في بناء تطبيقات الدردشة، دعونا نقارن بين "روبوتات الدردشة" و"تطبيقات الدردشة المدعومة بالذكاء الاصطناعي"، حيث لكل منهما أدوار ووظائف مختلفة. الهدف الرئيسي من روبوت الدردشة هو أتمتة مهام محادثة محددة، مثل الإجابة على الأسئلة المتكررة أو تتبع الطرود. عادةً ما يتم التحكم فيه بواسطة منطق قائم على القواعد أو خوارزميات ذكاء اصطناعي معقدة. بالمقابل، تطبيق الدردشة المدعوم بالذكاء الاصطناعي هو بيئة أوسع بكثير تهدف إلى تسهيل أشكال مختلفة من التواصل الرقمي، مثل الدردشة النصية، الصوتية، والفيديو بين المستخدمين البشر. الميزة الأساسية هي دمج نموذج ذكاء اصطناعي توليدي يحاكي محادثات بشرية معقدة، ويولد ردودًا بناءً على مجموعة واسعة من المدخلات والسياقات. يمكن لتطبيق الدردشة المدعوم بالذكاء الاصطناعي التوليدي أن يشارك في مناقشات مفتوحة المجال، ويتكيف مع تطورات السياق المحادثي، بل وينتج حوارات إبداعية أو معقدة.

يوضح الجدول أدناه الفروقات والتشابهات الرئيسية لمساعدتنا على فهم أدوارهم الفريدة في التواصل الرقمي.

| روبوت الدردشة                         | تطبيق الدردشة المدعوم بالذكاء الاصطناعي التوليدي |
| ------------------------------------ | ----------------------------------------------- |
| موجه بالمهام وقائم على القواعد       | مدرك للسياق                                    |
| غالبًا ما يُدمج في أنظمة أكبر        | قد يستضيف روبوت دردشة واحد أو عدة روبوتات      |
| محدود بالوظائف المبرمجة              | يدمج نماذج الذكاء الاصطناعي التوليدي           |
| تفاعلات متخصصة ومنظمة                | قادر على مناقشات مفتوحة المجال                  |

### الاستفادة من الوظائف المدمجة مسبقًا عبر SDKs وAPIs

عند بناء تطبيق دردشة، من الجيد أولاً تقييم ما هو متاح بالفعل. استخدام SDKs وAPIs لبناء تطبيقات الدردشة هو استراتيجية مفيدة لأسباب متعددة. من خلال دمج SDKs وAPIs موثقة جيدًا، تضع تطبيقك استراتيجيًا للنجاح على المدى الطويل، مع معالجة قضايا التوسع والصيانة.

- **يسرع عملية التطوير ويقلل العبء**: الاعتماد على الوظائف المدمجة مسبقًا بدلاً من بناء كل شيء بنفسك يوفر الوقت والجهد، مما يتيح لك التركيز على جوانب أخرى من التطبيق مثل منطق الأعمال.
- **أداء أفضل**: عند بناء الوظائف من الصفر، ستسأل نفسك في النهاية "كيف يتوسع التطبيق؟ هل يمكنه التعامل مع تدفق مفاجئ للمستخدمين؟" غالبًا ما تحتوي SDKs وAPIs المدارة جيدًا على حلول مدمجة لهذه القضايا.
- **صيانة أسهل**: التحديثات والتحسينات أسهل في الإدارة، حيث يتطلب معظم APIs وSDKs فقط تحديث المكتبة عند إصدار نسخة جديدة.
- **الوصول إلى أحدث التقنيات**: الاستفادة من النماذج التي تم ضبطها وتدريبها على مجموعات بيانات واسعة يمنح تطبيقك قدرات متقدمة في معالجة اللغة الطبيعية.

عادةً ما يتطلب الوصول إلى وظائف SDK أو API الحصول على إذن لاستخدام الخدمات المقدمة، غالبًا من خلال مفتاح فريد أو رمز مصادقة. سنستخدم مكتبة OpenAI للغة بايثون لاستكشاف كيفية ذلك. يمكنك أيضًا تجربتها بنفسك في [دفتر ملاحظات OpenAI](../../../07-building-chat-applications/python/oai-assignment.ipynb) أو [دفتر ملاحظات Azure OpenAI Services](../../../07-building-chat-applications/python/aoai-assignment.ipynb) لهذا الدرس.

```python
import os
from openai import OpenAI

API_KEY = os.getenv("OPENAI_API_KEY","")

client = OpenAI(
    api_key=API_KEY
    )

chat_completion = client.chat.completions.create(model="gpt-3.5-turbo", messages=[{"role": "user", "content": "Suggest two titles for an instructional lesson on chat applications for generative AI."}])
```

المثال أعلاه يستخدم نموذج GPT-3.5 Turbo لإكمال الطلب، لكن لاحظ أنه تم تعيين مفتاح API قبل ذلك. ستتلقى خطأ إذا لم تقم بتعيين المفتاح.

## تجربة المستخدم (UX)

تنطبق مبادئ تجربة المستخدم العامة على تطبيقات الدردشة، لكن هناك بعض الاعتبارات الإضافية التي تصبح مهمة بشكل خاص بسبب مكونات التعلم الآلي المشاركة.

- **آلية لمعالجة الغموض**: قد تولد نماذج الذكاء الاصطناعي التوليدي أحيانًا إجابات غامضة. ميزة تسمح للمستخدمين بطلب توضيح يمكن أن تكون مفيدة عند مواجهة هذه المشكلة.
- **الاحتفاظ بالسياق**: لدى نماذج الذكاء الاصطناعي التوليدي المتقدمة القدرة على تذكر السياق داخل المحادثة، وهو أمر ضروري لتحسين تجربة المستخدم. تمكين المستخدمين من التحكم وإدارة السياق يحسن التجربة، لكنه يطرح مخاطر تتعلق بالاحتفاظ بمعلومات حساسة. يجب النظر في مدة تخزين هذه المعلومات، مثل تطبيق سياسة احتفاظ، لتحقيق توازن بين الحاجة للسياق والخصوصية.
- **التخصيص**: بفضل القدرة على التعلم والتكيف، تقدم النماذج تجربة مخصصة لكل مستخدم. تخصيص تجربة المستخدم من خلال ميزات مثل ملفات المستخدم لا يجعل المستخدم يشعر بأنه مفهوم فقط، بل يساعده أيضًا في العثور على إجابات محددة، مما يخلق تفاعلًا أكثر كفاءة ورضا.

مثال على التخصيص هو إعدادات "التعليمات المخصصة" في ChatGPT من OpenAI. تتيح لك تقديم معلومات عن نفسك قد تكون سياقًا مهمًا لطلباتك. إليك مثال على تعليمات مخصصة.

![إعدادات التعليمات المخصصة في ChatGPT](../../../translated_images/custom-instructions.b96f59aa69356fcfed456414221919e8996f93c90c20d0d58d1bc0221e3c909f.ar.png)

هذا "الملف الشخصي" يحفز ChatGPT على إنشاء خطة درس حول القوائم المرتبطة. لاحظ أن ChatGPT يأخذ في الاعتبار أن المستخدم قد يرغب في خطة درس أكثر تفصيلاً بناءً على خبرتها.

![طلب في ChatGPT لخطة درس عن القوائم المرتبطة](../../../translated_images/lesson-plan-prompt.cc47c488cf1343df5d67aa796a1acabca32c380e5b782971e289f6ab8b21cf5a.ar.png)

### إطار عمل رسالة النظام من مايكروسوفت لنماذج اللغة الكبيرة

[قدمت مايكروسوفت إرشادات](https://learn.microsoft.com/azure/ai-services/openai/concepts/system-message#define-the-models-output-format?WT.mc_id=academic-105485-koreyst) لكتابة رسائل نظام فعالة عند توليد الردود من نماذج اللغة الكبيرة، مقسمة إلى 4 مجالات:

1. تحديد من هو النموذج، بالإضافة إلى قدراته وقيوده.
2. تحديد تنسيق مخرجات النموذج.
3. تقديم أمثلة محددة توضح السلوك المقصود للنموذج.
4. توفير ضوابط سلوكية إضافية.

### إمكانية الوصول

سواء كان المستخدم يعاني من ضعف بصري، سمعي، حركي، أو إدراكي، يجب أن يكون تطبيق الدردشة مصممًا ليكون قابلًا للاستخدام من قبل الجميع. القائمة التالية توضح ميزات محددة تهدف إلى تعزيز إمكانية الوصول لمختلف الإعاقات.

- **ميزات لضعف البصر**: سمات عالية التباين ونص قابل لتغيير الحجم، توافق مع برامج قراءة الشاشة.
- **ميزات لضعف السمع**: وظائف تحويل النص إلى كلام والعكس، إشارات بصرية للإشعارات الصوتية.
- **ميزات لضعف الحركة**: دعم التنقل عبر لوحة المفاتيح، أوامر صوتية.
- **ميزات لضعف الإدراك**: خيارات لغة مبسطة.

## التخصيص وضبط الأداء لنماذج اللغة الخاصة بالمجال

تخيل تطبيق دردشة يفهم مصطلحات شركتك ويتوقع الاستفسارات الشائعة لمستخدميه. هناك بعض الطرق التي تستحق الذكر:

- **الاستفادة من نماذج اللغة الخاصة بالمجال (DSL)**. DSL تعني لغة خاصة بالمجال. يمكنك استخدام نموذج DSL مدرب على مجال معين لفهم مفاهيمه وسيناريوهاته.
- **تطبيق ضبط الأداء (Fine-tuning)**. ضبط الأداء هو عملية تدريب إضافي لنموذجك باستخدام بيانات محددة.

## التخصيص: استخدام نموذج DSL

يمكن أن يعزز استخدام نماذج اللغة الخاصة بالمجال (DSL Models) تفاعل المستخدم من خلال تقديم تفاعلات متخصصة وذات صلة بالسياق. هو نموذج يتم تدريبه أو ضبطه لفهم وتوليد نصوص متعلقة بمجال أو صناعة أو موضوع معين. خيارات استخدام نموذج DSL تتراوح بين تدريبه من الصفر، أو استخدام نماذج موجودة مسبقًا عبر SDKs وAPIs. خيار آخر هو ضبط الأداء، الذي يتضمن أخذ نموذج مدرب مسبقًا وتكييفه لمجال محدد.

## التخصيص: تطبيق ضبط الأداء

غالبًا ما يُنظر إلى ضبط الأداء عندما لا يلبي النموذج المدرب مسبقًا متطلبات مجال متخصص أو مهمة محددة.

على سبيل المثال، الاستفسارات الطبية معقدة وتتطلب الكثير من السياق. عندما يشخص طبيب مريضًا، يعتمد على عوامل متعددة مثل نمط الحياة أو الحالات الصحية السابقة، وقد يعتمد أيضًا على أحدث الأبحاث الطبية لتأكيد التشخيص. في مثل هذه السيناريوهات الدقيقة، لا يمكن لتطبيق دردشة عام أن يكون مصدرًا موثوقًا.

### سيناريو: تطبيق طبي

تخيل تطبيق دردشة مصمم لمساعدة الممارسين الطبيين من خلال توفير مراجع سريعة لإرشادات العلاج، تفاعلات الأدوية، أو أحدث نتائج الأبحاث.

قد يكون النموذج العام كافيًا للإجابة على الأسئلة الطبية الأساسية أو تقديم نصائح عامة، لكنه قد يواجه صعوبة في الحالات التالية:

- **حالات معقدة أو محددة للغاية**. على سبيل المثال، قد يسأل طبيب أعصاب التطبيق: "ما هي أفضل الممارسات الحالية لإدارة الصرع المقاوم للأدوية لدى الأطفال؟"
- **غياب التحديثات الحديثة**. قد يواجه النموذج العام صعوبة في تقديم إجابة حديثة تتضمن أحدث التطورات في علم الأعصاب والصيدلة.

في مثل هذه الحالات، يمكن أن يحسن ضبط النموذج باستخدام مجموعة بيانات طبية متخصصة قدرته على التعامل مع هذه الاستفسارات الطبية المعقدة بدقة وموثوقية أكبر. يتطلب ذلك الوصول إلى مجموعة بيانات كبيرة وذات صلة تمثل تحديات وأسئلة المجال المحدد.

## اعتبارات لتجربة دردشة مدفوعة بالذكاء الاصطناعي عالية الجودة

يحدد هذا القسم معايير تطبيقات الدردشة "عالية الجودة"، والتي تشمل جمع مقاييس قابلة للتنفيذ والالتزام بإطار عمل يستخدم الذكاء الاصطناعي بمسؤولية.

### المقاييس الرئيسية

للحفاظ على أداء عالي الجودة للتطبيق، من الضروري متابعة المقاييس والاعتبارات الرئيسية. هذه القياسات لا تضمن فقط وظيفة التطبيق، بل تقيم أيضًا جودة نموذج الذكاء الاصطناعي وتجربة المستخدم. فيما يلي قائمة تغطي المقاييس الأساسية، ومقاييس الذكاء الاصطناعي، وتجربة المستخدم التي يجب أخذها في الاعتبار.

| المقياس                      | التعريف                                                                                                               | اعتبارات لمطور الدردشة                                               |
| ---------------------------- | --------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------- |
| **مدة التشغيل (Uptime)**     | يقيس الوقت الذي يكون فيه التطبيق متاحًا وقيد التشغيل للمستخدمين.                                                    | كيف ستقلل من وقت التوقف؟                                            |
| **زمن الاستجابة**            | الوقت الذي يستغرقه التطبيق للرد على استفسار المستخدم.                                                                | كيف يمكنك تحسين معالجة الاستعلامات لتقليل زمن الاستجابة؟           |
| **الدقة (Precision)**         | نسبة التنبؤات الإيجابية الصحيحة إلى إجمالي التنبؤات الإيجابية.                                                       | كيف ستتحقق من دقة نموذجك؟                                           |
| **الاستدعاء (Recall أو الحساسية)** | نسبة التنبؤات الإيجابية الصحيحة إلى العدد الفعلي للإيجابيات.                                                        | كيف ستقيس وتحسن الاستدعاء؟                                         |
| **معدل F1**                  | المتوسط التوافقي بين الدقة والاستدعاء، يوازن بينهما.                                                                 | ما هو هدفك لمعدل F1؟ كيف ستوازن بين الدقة والاستدعاء؟              |
| **الارتباك (Perplexity)**    | يقيس مدى توافق توزيع الاحتمالات الذي يتنبأ به النموذج مع التوزيع الفعلي للبيانات.                                     | كيف ستقلل من الارتباك؟                                              |
| **مقاييس رضا المستخدم**      | تقيس تصور المستخدم للتطبيق، وغالبًا ما تُجمع عبر استبيانات.                                                           | كم مرة ستجمع ملاحظات المستخدم؟ كيف ستتكيف بناءً عليها؟             |
| **معدل الخطأ**               | معدل الأخطاء التي يرتكبها النموذج في الفهم أو الإخراج.                                                                 | ما هي الاستراتيجيات التي لديك لتقليل معدلات الخطأ؟                  |
| **دورات إعادة التدريب**      | تكرار تحديث النموذج لدمج بيانات ورؤى جديدة.                                                                           | كم مرة ستعيد تدريب النموذج؟ ما الذي يحفز دورة إعادة التدريب؟       |
| **كشف الشذوذ**           | أدوات وتقنيات لتحديد الأنماط غير المعتادة التي لا تتوافق مع السلوك المتوقع.                                      | كيف ستتعامل مع الشذوذات؟                                                    |

### تنفيذ ممارسات الذكاء الاصطناعي المسؤول في تطبيقات الدردشة

حددت مايكروسوفت ستة مبادئ توجه تطوير واستخدام الذكاء الاصطناعي المسؤول. فيما يلي هذه المبادئ، تعريفها، والأمور التي يجب على مطور الدردشة أخذها بعين الاعتبار ولماذا يجب أن يأخذها على محمل الجد.

| المبادئ               | تعريف مايكروسوفت                                      | اعتبارات لمطور الدردشة                                               | لماذا هو مهم                                                                     |
| --------------------- | ----------------------------------------------------- | ------------------------------------------------------------------- | -------------------------------------------------------------------------------- |
| العدالة               | يجب أن تعامل أنظمة الذكاء الاصطناعي الجميع بعدل.       | التأكد من أن تطبيق الدردشة لا يميز بناءً على بيانات المستخدم.       | لبناء الثقة والشمولية بين المستخدمين؛ وتجنب العواقب القانونية.                 |
| الموثوقية والسلامة   | يجب أن تعمل أنظمة الذكاء الاصطناعي بشكل موثوق وآمن.   | تنفيذ اختبارات وآليات أمان لتقليل الأخطاء والمخاطر.                | لضمان رضا المستخدم ومنع الأضرار المحتملة.                                      |
| الخصوصية والأمان      | يجب أن تكون أنظمة الذكاء الاصطناعي آمنة وتحترم الخصوصية.| تطبيق تشفير قوي وتدابير حماية البيانات.                             | لحماية بيانات المستخدم الحساسة والامتثال لقوانين الخصوصية.                     |
| الشمولية              | يجب أن تمكّن أنظمة الذكاء الاصطناعي الجميع وتشارك الناس.| تصميم واجهة مستخدم وتجربة استخدام سهلة الوصول ومتنوعة للجمهور.    | لضمان قدرة عدد أكبر من الناس على استخدام التطبيق بفعالية.                     |
| الشفافية              | يجب أن تكون أنظمة الذكاء الاصطناعي مفهومة.             | توفير توثيق واضح وتفسير لردود الذكاء الاصطناعي.                     | يميل المستخدمون إلى الثقة بالنظام إذا فهموا كيفية اتخاذ القرارات.              |
| المساءلة              | يجب أن يكون الأشخاص مسؤولين عن أنظمة الذكاء الاصطناعي. | وضع عملية واضحة لمراجعة وتحسين قرارات الذكاء الاصطناعي.            | يتيح التحسين المستمر واتخاذ إجراءات تصحيحية في حال حدوث أخطاء.                |

## المهمة

راجع [assignment](../../../07-building-chat-applications/python) حيث ستأخذك خلال سلسلة من التمارين بدءًا من تشغيل أول محادثاتك، إلى تصنيف وتلخيص النصوص والمزيد. لاحظ أن المهام متاحة بلغات برمجة مختلفة!

## عمل رائع! استمر في الرحلة

بعد إكمال هذا الدرس، اطلع على [مجموعة تعلم الذكاء الاصطناعي التوليدي](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) لمواصلة تطوير معرفتك في الذكاء الاصطناعي التوليدي!

توجه إلى الدرس 8 لترى كيف يمكنك البدء في [بناء تطبيقات البحث](../08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)!

**إخلاء المسؤولية**:  
تمت ترجمة هذا المستند باستخدام خدمة الترجمة الآلية [Co-op Translator](https://github.com/Azure/co-op-translator). بينما نسعى لتحقيق الدقة، يرجى العلم أن الترجمات الآلية قد تحتوي على أخطاء أو عدم دقة. يجب اعتبار المستند الأصلي بلغته الأصلية المصدر الموثوق به. للمعلومات الهامة، يُنصح بالاعتماد على الترجمة البشرية المهنية. نحن غير مسؤولين عن أي سوء فهم أو تفسير ناتج عن استخدام هذه الترجمة.