<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "f3cac698e9eea47dd563633bd82daf8c",
  "translation_date": "2025-07-09T15:11:41+00:00",
  "source_file": "13-securing-ai-applications/README.md",
  "language_code": "ar"
}
-->
# تأمين تطبيقات الذكاء الاصطناعي التوليدي الخاصة بك

[![تأمين تطبيقات الذكاء الاصطناعي التوليدي الخاصة بك](../../../translated_images/13-lesson-banner.14103e36b4bbf17398b64ed2b0531f6f2c6549e7f7342f797c40bcae5a11862e.ar.png)](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst)

## المقدمة

ستتناول هذه الدرس:

- الأمن في سياق أنظمة الذكاء الاصطناعي.
- المخاطر والتهديدات الشائعة لأنظمة الذكاء الاصطناعي.
- الطرق والاعتبارات لتأمين أنظمة الذكاء الاصطناعي.

## أهداف التعلم

بعد إتمام هذا الدرس، سيكون لديك فهم لـ:

- التهديدات والمخاطر التي تواجه أنظمة الذكاء الاصطناعي.
- الطرق والممارسات الشائعة لتأمين أنظمة الذكاء الاصطناعي.
- كيف يمكن لاختبار الأمان أن يمنع النتائج غير المتوقعة وتآكل ثقة المستخدمين.

## ماذا يعني الأمن في سياق الذكاء الاصطناعي التوليدي؟

مع تزايد تأثير تقنيات الذكاء الاصطناعي (AI) والتعلم الآلي (ML) في حياتنا، أصبح من الضروري حماية ليس فقط بيانات العملاء، بل وأنظمة الذكاء الاصطناعي نفسها. يُستخدم الذكاء الاصطناعي والتعلم الآلي بشكل متزايد لدعم عمليات اتخاذ القرار ذات القيمة العالية في صناعات قد تؤدي فيها القرارات الخاطئة إلى عواقب خطيرة.

إليك النقاط الرئيسية التي يجب مراعاتها:

- **تأثير الذكاء الاصطناعي والتعلم الآلي**: لهما تأثير كبير على الحياة اليومية، ولذلك أصبح من الضروري حمايتهما.
- **تحديات الأمان**: هذا التأثير يتطلب اهتمامًا خاصًا لحماية المنتجات المعتمدة على الذكاء الاصطناعي من الهجمات المتطورة، سواء من المتصيدين أو الجماعات المنظمة.
- **المشاكل الاستراتيجية**: يجب على صناعة التكنولوجيا التعامل بشكل استباقي مع التحديات الاستراتيجية لضمان سلامة العملاء على المدى الطويل وأمن البيانات.

بالإضافة إلى ذلك، غالبًا ما تعجز نماذج التعلم الآلي عن التمييز بين المدخلات الخبيثة والبيانات الشاذة غير الضارة. مصدر كبير من بيانات التدريب يأتي من مجموعات بيانات عامة غير منظمة وغير مراقبة، مفتوحة لمساهمات من أطراف ثالثة. لا يحتاج المهاجمون إلى اختراق هذه المجموعات عندما يمكنهم ببساطة المساهمة فيها. مع مرور الوقت، تتحول البيانات الخبيثة منخفضة الثقة إلى بيانات موثوقة عالية الثقة، طالما أن هيكل البيانات وصيغتها تظل صحيحة.

لهذا السبب من الضروري ضمان سلامة وحماية مخازن البيانات التي تستخدمها نماذجك لاتخاذ القرارات.

## فهم التهديدات والمخاطر المتعلقة بالذكاء الاصطناعي

فيما يتعلق بالذكاء الاصطناعي والأنظمة المرتبطة به، يُعتبر تسميم البيانات التهديد الأمني الأكبر حاليًا. تسميم البيانات هو عندما يقوم شخص ما بتغيير المعلومات المستخدمة لتدريب الذكاء الاصطناعي عمدًا، مما يؤدي إلى أخطاء في أدائه. ويرجع ذلك إلى غياب طرق موحدة للكشف والتخفيف، بالإضافة إلى اعتمادنا على مجموعات بيانات عامة غير موثوقة أو غير منظمة للتدريب. للحفاظ على سلامة البيانات ومنع عملية تدريب معيبة، من الضروري تتبع مصدر وسلسلة بياناتك. وإلا، فإن المثل القائل "المدخلات السيئة تؤدي إلى مخرجات سيئة" ينطبق، مما يؤدي إلى تدهور أداء النموذج.

فيما يلي أمثلة على كيفية تأثير تسميم البيانات على نماذجك:

1. **تبديل العلامات**: في مهمة تصنيف ثنائية، يقوم الخصم بتبديل علامات مجموعة صغيرة من بيانات التدريب عمدًا. على سبيل المثال، يتم تصنيف عينات غير ضارة على أنها ضارة، مما يجعل النموذج يتعلم ارتباطات خاطئة.\
   **مثال**: فلتر البريد المزعج يصنف رسائل شرعية كبريد مزعج بسبب علامات تم التلاعب بها.
2. **تسميم الميزات**: يقوم المهاجم بتعديل الميزات في بيانات التدريب بشكل طفيف لإدخال تحيز أو تضليل النموذج.\
   **مثال**: إضافة كلمات مفتاحية غير ذات صلة لوصف المنتجات للتلاعب بأنظمة التوصية.
3. **حقن البيانات**: إدخال بيانات خبيثة في مجموعة التدريب للتأثير على سلوك النموذج.\
   **مثال**: إدخال تقييمات مستخدمين مزيفة لتحريف نتائج تحليل المشاعر.
4. **هجمات الباب الخلفي**: يقوم الخصم بإدخال نمط مخفي (باب خلفي) في بيانات التدريب. يتعلم النموذج التعرف على هذا النمط ويتصرف بشكل خبيث عند تفعيله.\
   **مثال**: نظام التعرف على الوجه المدرب على صور تحتوي على باب خلفي يخطئ في التعرف على شخص معين.

أنشأت مؤسسة MITRE [ATLAS (مشهد التهديدات العدائية لأنظمة الذكاء الاصطناعي)](https://atlas.mitre.org/?WT.mc_id=academic-105485-koreyst)، وهي قاعدة معرفية للتكتيكات والتقنيات التي يستخدمها الخصوم في الهجمات الواقعية على أنظمة الذكاء الاصطناعي.

> هناك عدد متزايد من الثغرات في الأنظمة المدعومة بالذكاء الاصطناعي، حيث يزيد دمج الذكاء الاصطناعي من مساحة الهجوم للأنظمة القائمة بما يتجاوز الهجمات السيبرانية التقليدية. طورنا ATLAS لزيادة الوعي بهذه الثغرات الفريدة والمتطورة، مع تزايد دمج المجتمع العالمي للذكاء الاصطناعي في أنظمة مختلفة. تم تصميم ATLAS على غرار إطار عمل MITRE ATT&CK®، وتكتيكاته وتقنياته وإجراءاته (TTPs) تكمل تلك الموجودة في ATT&CK.

تمامًا مثل إطار عمل MITRE ATT&CK® المستخدم على نطاق واسع في الأمن السيبراني التقليدي لتخطيط سيناريوهات محاكاة التهديدات المتقدمة، يوفر ATLAS مجموعة من TTPs سهلة البحث تساعد على فهم أفضل والاستعداد للدفاع ضد الهجمات الناشئة.

بالإضافة إلى ذلك، أنشأ مشروع أمان تطبيقات الويب المفتوح (OWASP) "[قائمة العشرة الأوائل](https://llmtop10.com/?WT.mc_id=academic-105485-koreyst)" لأكثر الثغرات الحرجة في التطبيقات التي تستخدم نماذج اللغة الكبيرة (LLMs). تسلط القائمة الضوء على مخاطر تهديدات مثل تسميم البيانات المذكور أعلاه، بالإضافة إلى أخرى مثل:

- **حقن المطالبات**: تقنية يقوم فيها المهاجمون بالتلاعب بنموذج اللغة الكبير (LLM) من خلال مدخلات مصممة بعناية، مما يجعله يتصرف خارج سلوكه المقصود.
- **ثغرات سلسلة التوريد**: المكونات والبرمجيات التي تشكل التطبيقات المستخدمة بواسطة LLM، مثل وحدات Python أو مجموعات البيانات الخارجية، يمكن أن تتعرض للاختراق مما يؤدي إلى نتائج غير متوقعة، تحيزات مدخلة، وحتى ثغرات في البنية التحتية الأساسية.
- **الاعتماد المفرط**: نماذج اللغة الكبيرة عرضة للأخطاء وقد تم توثيق حالات لها تتخيل معلومات، مما يؤدي إلى نتائج غير دقيقة أو غير آمنة. في عدة حالات موثقة، أخذ الناس النتائج على محمل الجد مما أدى إلى عواقب سلبية غير مقصودة في العالم الحقيقي.

كتب مدافع سحابة مايكروسوفت رود ترينت كتابًا إلكترونيًا مجانيًا، [يجب تعلم أمان الذكاء الاصطناعي](https://github.com/rod-trent/OpenAISecurity/tree/main/Must_Learn/Book_Version?WT.mc_id=academic-105485-koreyst)، يغوص بعمق في هذه التهديدات الناشئة وغيرها، ويقدم إرشادات واسعة حول كيفية التعامل مع هذه السيناريوهات بأفضل شكل.

## اختبار الأمان لأنظمة الذكاء الاصطناعي ونماذج اللغة الكبيرة

يُحدث الذكاء الاصطناعي تحولًا في مجالات وصناعات متعددة، مقدمًا إمكانيات وفوائد جديدة للمجتمع. ومع ذلك، يطرح الذكاء الاصطناعي تحديات ومخاطر كبيرة، مثل خصوصية البيانات، التحيز، نقص الشرح، وسوء الاستخدام المحتمل. لذلك، من الضروري التأكد من أن أنظمة الذكاء الاصطناعي آمنة ومسؤولة، بمعنى أنها تلتزم بالمعايير الأخلاقية والقانونية ويمكن الوثوق بها من قبل المستخدمين وأصحاب المصلحة.

اختبار الأمان هو عملية تقييم أمان نظام الذكاء الاصطناعي أو نموذج اللغة الكبير، من خلال تحديد واستغلال نقاط الضعف فيه. يمكن أن يتم ذلك بواسطة المطورين، المستخدمين، أو مدققين خارجيين، حسب الغرض ونطاق الاختبار. بعض الطرق الشائعة لاختبار أمان أنظمة الذكاء الاصطناعي ونماذج اللغة الكبيرة هي:

- **تنقية البيانات**: هي عملية إزالة أو إخفاء المعلومات الحساسة أو الخاصة من بيانات التدريب أو مدخلات نظام الذكاء الاصطناعي أو نموذج اللغة الكبير. تساعد تنقية البيانات في منع تسرب البيانات والتلاعب الخبيث عن طريق تقليل تعرض البيانات السرية أو الشخصية.
- **الاختبار العدائي**: هو عملية توليد وتطبيق أمثلة عدائية على مدخلات أو مخرجات نظام الذكاء الاصطناعي أو نموذج اللغة الكبير لتقييم متانته وقدرته على الصمود أمام الهجمات العدائية. يساعد الاختبار العدائي في تحديد نقاط الضعف والثغرات التي قد يستغلها المهاجمون.
- **التحقق من النموذج**: هو عملية التحقق من صحة واكتمال معلمات النموذج أو هيكله في نظام الذكاء الاصطناعي أو نموذج اللغة الكبير. يساعد التحقق من النموذج في اكتشاف ومنع سرقة النموذج من خلال ضمان حمايته وتوثيقه.
- **التحقق من المخرجات**: هو عملية التحقق من جودة وموثوقية مخرجات نظام الذكاء الاصطناعي أو نموذج اللغة الكبير. يساعد التحقق من المخرجات في اكتشاف وتصحيح التلاعب الخبيث من خلال ضمان اتساق ودقة المخرجات.

أنشأت OpenAI، الرائدة في أنظمة الذكاء الاصطناعي، سلسلة من _تقييمات السلامة_ كجزء من مبادرة فريق الاختبار الأحمر الخاصة بها، تهدف إلى اختبار مخرجات أنظمة الذكاء الاصطناعي على أمل المساهمة في سلامة الذكاء الاصطناعي.

> يمكن أن تتراوح التقييمات من اختبارات أسئلة وأجوبة بسيطة إلى محاكاة أكثر تعقيدًا. كمثال ملموس، إليك بعض التقييمات التي طورتها OpenAI لتقييم سلوكيات الذكاء الاصطناعي من عدة زوايا:

#### الإقناع

- [MakeMeSay](https://github.com/openai/evals/tree/main/evals/elsuite/make_me_say/readme.md?WT.mc_id=academic-105485-koreyst): مدى قدرة نظام الذكاء الاصطناعي على خداع نظام آخر ليقول كلمة سرية؟
- [MakeMePay](https://github.com/openai/evals/tree/main/evals/elsuite/make_me_pay/readme.md?WT.mc_id=academic-105485-koreyst): مدى قدرة نظام الذكاء الاصطناعي على إقناع نظام آخر بالتبرع بالمال؟
- [Ballot Proposal](https://github.com/openai/evals/tree/main/evals/elsuite/ballots/readme.md?WT.mc_id=academic-105485-koreyst): مدى قدرة نظام الذكاء الاصطناعي على التأثير في دعم نظام آخر لمقترح سياسي؟

#### التشفير الخفي (الرسائل المخفية)

- [Steganography](https://github.com/openai/evals/tree/main/evals/elsuite/steganography/readme.md?WT.mc_id=academic-105485-koreyst): مدى قدرة نظام الذكاء الاصطناعي على تمرير رسائل سرية دون أن يكتشفها نظام آخر؟
- [Text Compression](https://github.com/openai/evals/tree/main/evals/elsuite/text_compression/readme.md?WT.mc_id=academic-105485-koreyst): مدى قدرة نظام الذكاء الاصطناعي على ضغط وفك ضغط الرسائل لتمكين إخفاء الرسائل السرية؟
- [Schelling Point](https://github.com/openai/evals/blob/main/evals/elsuite/schelling_point/README.md?WT.mc_id=academic-105485-koreyst): مدى قدرة نظام الذكاء الاصطناعي على التنسيق مع نظام آخر دون تواصل مباشر؟

### أمان الذكاء الاصطناعي

من الضروري أن نهدف إلى حماية أنظمة الذكاء الاصطناعي من الهجمات الخبيثة، سوء الاستخدام، أو العواقب غير المقصودة. يشمل ذلك اتخاذ خطوات لضمان سلامة وموثوقية وثقة أنظمة الذكاء الاصطناعي، مثل:

- تأمين البيانات والخوارزميات المستخدمة في تدريب وتشغيل نماذج الذكاء الاصطناعي
- منع الوصول غير المصرح به، التلاعب، أو التخريب لأنظمة الذكاء الاصطناعي
- الكشف عن التحيز والتمييز أو القضايا الأخلاقية في أنظمة الذكاء الاصطناعي والتخفيف منها
- ضمان المساءلة والشفافية وقابلية تفسير قرارات وأفعال الذكاء الاصطناعي
- مواءمة أهداف وقيم أنظمة الذكاء الاصطناعي مع تلك الخاصة بالبشر والمجتمع

أمان الذكاء الاصطناعي مهم لضمان سلامة وتوافر وسرية أنظمة وبيانات الذكاء الاصطناعي. بعض التحديات والفرص في أمان الذكاء الاصطناعي هي:

- فرصة: دمج الذكاء الاصطناعي في استراتيجيات الأمن السيبراني لأنه يمكن أن يلعب دورًا حاسمًا في تحديد التهديدات وتحسين أوقات الاستجابة. يمكن للذكاء الاصطناعي أن يساعد في أتمتة وتعزيز الكشف والتخفيف من الهجمات السيبرانية، مثل التصيد الاحتيالي، البرمجيات الخبيثة، أو برامج الفدية.
- تحدي: يمكن أيضًا للخصوم استخدام الذكاء الاصطناعي لشن هجمات متطورة، مثل توليد محتوى مزيف أو مضلل، انتحال هوية المستخدمين، أو استغلال الثغرات في أنظمة الذكاء الاصطناعي. لذلك، يتحمل مطورو الذكاء الاصطناعي مسؤولية فريدة في تصميم أنظمة قوية وقادرة على الصمود ضد سوء الاستخدام.

### حماية البيانات

يمكن لنماذج اللغة الكبيرة أن تشكل مخاطر على خصوصية وأمان البيانات التي تستخدمها. على سبيل المثال، قد تحفظ نماذج اللغة الكبيرة معلومات حساسة من بيانات التدريب مثل الأسماء الشخصية، العناوين، كلمات المرور، أو أرقام بطاقات الائتمان، وقد تقوم بتسريبها. كما يمكن التلاعب بها أو استهدافها من قبل جهات خبيثة ترغب في استغلال نقاط ضعفها أو تحيزاتها. لذلك، من المهم الوعي بهذه المخاطر واتخاذ التدابير المناسبة لحماية البيانات المستخدمة مع نماذج اللغة الكبيرة. هناك عدة خطوات يمكنك اتخاذها لحماية البيانات المستخدمة مع نماذج اللغة الكبيرة، منها:

- **تحديد كمية ونوع البيانات التي تشاركها مع نماذج اللغة الكبيرة**: شارك فقط البيانات الضرورية والمرتبطة بالأغراض المقصودة، وتجنب مشاركة أي بيانات حساسة أو سرية أو شخصية. يجب على المستخدمين أيضًا إخفاء الهوية أو تشفير البيانات التي يشاركونها، مثل إزالة أو إخفاء أي معلومات تعريفية، أو استخدام قنوات اتصال آمنة.
- **التحقق من البيانات التي تولدها نماذج اللغة الكبيرة**: تحقق دائمًا من دقة وجودة المخرجات التي تولدها نماذج اللغة الكبيرة لضمان عدم احتوائها على معلومات غير مرغوب فيها أو غير مناسبة.
- **الإبلاغ والتنبيه عن أي خروقات أو حوادث بيانات**: كن يقظًا لأي أنشطة أو سلوكيات مشبوهة أو غير طبيعية من نماذج اللغة الكبيرة، مثل توليد نصوص غير ذات صلة، غير دقيقة، مسيئة، أو ضارة. قد يكون هذا مؤشرًا على خرق بيانات أو حادث أمني.

أمان البيانات، الحوكمة، والامتثال أمر حاسم لأي منظمة ترغب في الاستفادة من قوة البيانات والذكاء الاصطناعي في بيئة متعددة السحب. تأمين وحوكمة جميع بياناتك مهمة معقدة ومتعددة الأوجه. تحتاج إلى تأمين وحوكمة أنواع مختلفة من البيانات (المنظمة، غير المنظمة، والبيانات التي يولدها الذكاء الاصطناعي) في مواقع مختلفة عبر سحب متعددة، ويجب أن تأخذ في الاعتبار متطلبات أمان البيانات والحوكمة والامتثال الحالية والمستقبلية. لحماية بياناتك، تحتاج إلى اعتماد بعض الممارسات الفضلى والاحتياطات، مثل:

- استخدام خدمات أو منصات سحابية توفر ميزات حماية البيانات والخصوصية.
- استخدام أدوات جودة البيانات والتحقق لفحص بياناتك بحثًا عن أخطاء أو تناقضات أو شذوذ.
- استخدام أطر حوكمة البيانات والأخلاقيات لضمان استخدام بياناتك بطريقة مسؤولة وشفافة.

### محاكاة التهديدات الواقعية - فرق الاختبار الأحمر للذكاء الاصطناعي

أصبحت محاكاة التهديدات الواقعية ممارسة معيارية في بناء أنظمة ذكاء اصطناعي مرنة من خلال استخدام أدوات وتكتيكات وإجراءات مماثلة لتحديد المخاطر على الأنظمة واختبار استجابة المدافعين.
> تطورت ممارسة فرق الاختراق في مجال الذكاء الاصطناعي لتشمل معنى أوسع: فهي لا تقتصر فقط على البحث عن ثغرات أمنية، بل تشمل أيضًا الكشف عن أعطال أخرى في النظام، مثل توليد محتوى قد يكون ضارًا. تأتي أنظمة الذكاء الاصطناعي مع مخاطر جديدة، ويُعد فريق الاختراق جزءًا أساسيًا لفهم هذه المخاطر الجديدة، مثل حقن الأوامر وإنتاج محتوى غير موثوق. - [Microsoft AI Red Team building future of safer AI](https://www.microsoft.com/security/blog/2023/08/07/microsoft-ai-red-team-building-future-of-safer-ai/?WT.mc_id=academic-105485-koreyst)
[![الإرشادات والموارد لفريق الاختراق الأحمر](../../../translated_images/13-AI-red-team.642ed54689d7e8a4d83bdf0635768c4fd8aa41ea539d8e3ffe17514aec4b4824.ar.png)]()

فيما يلي رؤى رئيسية شكلت برنامج فريق الاختراق الأحمر للذكاء الاصطناعي في مايكروسوفت.

1. **نطاق واسع لفريق الاختراق الأحمر في الذكاء الاصطناعي:**
   يشمل فريق الاختراق الأحمر في الذكاء الاصطناعي الآن نتائج الأمن والذكاء الاصطناعي المسؤول (RAI). تقليديًا، كان فريق الاختراق الأحمر يركز على الجوانب الأمنية، مع اعتبار النموذج كهدف (مثل سرقة النموذج الأساسي). ومع ذلك، تقدم أنظمة الذكاء الاصطناعي ثغرات أمنية جديدة (مثل حقن الأوامر، والتسميم)، مما يتطلب اهتمامًا خاصًا. بالإضافة إلى الأمن، يستكشف فريق الاختراق الأحمر قضايا العدالة (مثل التنميط) والمحتوى الضار (مثل تمجيد العنف). يتيح الكشف المبكر عن هذه القضايا تحديد أولويات استثمارات الدفاع.
2. **الإخفاقات الخبيثة والحميدة:**
   يأخذ فريق الاختراق الأحمر في الاعتبار الإخفاقات من منظورين: الخبيث والحميد. على سبيل المثال، عند اختبار فريق الاختراق الأحمر لـ Bing الجديد، نستكشف ليس فقط كيف يمكن للمهاجمين الخبيثين التلاعب بالنظام، بل أيضًا كيف قد يواجه المستخدمون العاديون محتوى إشكاليًا أو ضارًا. على عكس فريق الاختراق الأحمر الأمني التقليدي الذي يركز بشكل رئيسي على الجهات الخبيثة، يأخذ فريق الاختراق الأحمر في الذكاء الاصطناعي في الحسبان مجموعة أوسع من الشخصيات والإخفاقات المحتملة.
3. **الطبيعة الديناميكية لأنظمة الذكاء الاصطناعي:**
   تتطور تطبيقات الذكاء الاصطناعي باستمرار. في تطبيقات النماذج اللغوية الكبيرة، يتكيف المطورون مع المتطلبات المتغيرة. يضمن فريق الاختراق الأحمر المستمر اليقظة والتكيف مع المخاطر المتطورة.

فريق الاختراق الأحمر في الذكاء الاصطناعي ليس شاملاً لكل شيء ويجب اعتباره حركة تكميلية لضوابط إضافية مثل [التحكم في الوصول بناءً على الدور (RBAC)](https://learn.microsoft.com/azure/ai-services/openai/how-to/role-based-access-control?WT.mc_id=academic-105485-koreyst) وحلول إدارة البيانات الشاملة. الهدف منه هو دعم استراتيجية أمنية تركز على استخدام حلول ذكاء اصطناعي آمنة ومسؤولة تأخذ في الاعتبار الخصوصية والأمن مع السعي لتقليل التحيزات والمحتوى الضار والمعلومات المضللة التي قد تضعف ثقة المستخدم.

إليك قائمة بقراءات إضافية تساعدك على فهم كيف يمكن لفريق الاختراق الأحمر أن يساعد في تحديد المخاطر والتخفيف منها في أنظمة الذكاء الاصطناعي الخاصة بك:

- [تخطيط فريق الاختراق الأحمر للنماذج اللغوية الكبيرة (LLMs) وتطبيقاتها](https://learn.microsoft.com/azure/ai-services/openai/concepts/red-teaming?WT.mc_id=academic-105485-koreyst)
- [ما هو شبكة فريق الاختراق الأحمر في OpenAI؟](https://openai.com/blog/red-teaming-network?WT.mc_id=academic-105485-koreyst)
- [فريق الاختراق الأحمر في الذكاء الاصطناعي - ممارسة أساسية لبناء حلول ذكاء اصطناعي أكثر أمانًا ومسؤولية](https://rodtrent.substack.com/p/ai-red-teaming?WT.mc_id=academic-105485-koreyst)
- MITRE [ATLAS (مشهد التهديدات العدائية لأنظمة الذكاء الاصطناعي)](https://atlas.mitre.org/?WT.mc_id=academic-105485-koreyst)، قاعدة معرفية للتكتيكات والتقنيات التي يستخدمها الخصوم في الهجمات الواقعية على أنظمة الذكاء الاصطناعي.

## اختبار المعرفة

ما هي الطريقة المناسبة للحفاظ على سلامة البيانات ومنع سوء الاستخدام؟

1. وجود ضوابط قوية للوصول إلى البيانات وإدارتها بناءً على الدور
1. تنفيذ وتدقيق تصنيف البيانات لمنع التمثيل الخاطئ أو سوء الاستخدام
1. التأكد من أن بنية الذكاء الاصطناعي تدعم تصفية المحتوى

الإجابة: 1، رغم أن الثلاثة توصيات ممتازة، فإن التأكد من منح صلاحيات الوصول المناسبة للبيانات للمستخدمين يلعب دورًا كبيرًا في منع التلاعب والتمثيل الخاطئ للبيانات المستخدمة في النماذج اللغوية الكبيرة.

## 🚀 التحدي

اقرأ المزيد عن كيفية [حكم وحماية المعلومات الحساسة](https://learn.microsoft.com/training/paths/purview-protect-govern-ai/?WT.mc_id=academic-105485-koreyst) في عصر الذكاء الاصطناعي.

## عمل رائع، واصل التعلم

بعد إكمال هذا الدرس، اطلع على [مجموعة تعلم الذكاء الاصطناعي التوليدي](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) لمواصلة تطوير معرفتك في الذكاء الاصطناعي التوليدي!

توجه إلى الدرس 14 حيث سنستعرض [دورة حياة تطبيقات الذكاء الاصطناعي التوليدي](../14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)!

**إخلاء المسؤولية**:  
تمت ترجمة هذا المستند باستخدام خدمة الترجمة الآلية [Co-op Translator](https://github.com/Azure/co-op-translator). بينما نسعى لتحقيق الدقة، يرجى العلم أن الترجمات الآلية قد تحتوي على أخطاء أو عدم دقة. يجب اعتبار المستند الأصلي بلغته الأصلية المصدر الموثوق به. للمعلومات الهامة، يُنصح بالاعتماد على الترجمة البشرية المهنية. نحن غير مسؤولين عن أي سوء فهم أو تفسير ناتج عن استخدام هذه الترجمة.