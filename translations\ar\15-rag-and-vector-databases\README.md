<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "e2861bbca91c0567ef32bc77fe054f9e",
  "translation_date": "2025-07-09T16:03:05+00:00",
  "source_file": "15-rag-and-vector-databases/README.md",
  "language_code": "ar"
}
-->
# التوليد المعزز بالاسترجاع (RAG) وقواعد بيانات المتجهات

[![التوليد المعزز بالاسترجاع (RAG) وقواعد بيانات المتجهات](../../../translated_images/15-lesson-banner.ac49e59506175d4fc6ce521561dab2f9ccc6187410236376cfaed13cde371b90.ar.png)](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst)

في درس تطبيقات البحث، تعلّمنا باختصار كيفية دمج بياناتك الخاصة مع نماذج اللغة الكبيرة (LLMs). في هذا الدرس، سنتعمق أكثر في مفاهيم تأصيل بياناتك في تطبيق LLM الخاص بك، وآلية العملية، وطرق تخزين البيانات، بما في ذلك التضمينات والنصوص.

> **فيديو قادم قريبًا**

## المقدمة

في هذا الدرس سنغطي ما يلي:

- مقدمة عن RAG، ما هو ولماذا يُستخدم في الذكاء الاصطناعي.

- فهم ماهية قواعد بيانات المتجهات وكيفية إنشاء واحدة لتطبيقنا.

- مثال عملي حول كيفية دمج RAG في تطبيق.

## أهداف التعلم

بعد إكمال هذا الدرس، ستكون قادرًا على:

- شرح أهمية RAG في استرجاع البيانات ومعالجتها.

- إعداد تطبيق RAG وتأصيل بياناتك في LLM.

- دمج فعال لـ RAG وقواعد بيانات المتجهات في تطبيقات LLM.

## سيناريو التطبيق: تعزيز نماذج LLM الخاصة بنا ببياناتنا الخاصة

في هذا الدرس، نرغب في إضافة ملاحظاتنا الخاصة إلى مشروع تعليمي ناشئ، مما يسمح للدردشة الآلية بالحصول على مزيد من المعلومات حول المواضيع المختلفة. باستخدام الملاحظات التي لدينا، سيتمكن المتعلمون من الدراسة بشكل أفضل وفهم المواضيع المختلفة، مما يسهل عليهم المراجعة للامتحانات. لإنشاء سيناريو التطبيق، سنستخدم:

- `Azure OpenAI:` نموذج اللغة الكبير الذي سنستخدمه لإنشاء الدردشة الآلية

- `درس الذكاء الاصطناعي للمبتدئين حول الشبكات العصبية:` هذه هي البيانات التي سنؤصل عليها نموذج LLM الخاص بنا

- `Azure AI Search` و `Azure Cosmos DB:` قاعدة بيانات المتجهات لتخزين بياناتنا وإنشاء فهرس بحث

سيتمكن المستخدمون من إنشاء اختبارات تدريبية من ملاحظاتهم، وبطاقات مراجعة، وتلخيصها إلى نظرات عامة موجزة. للبدء، دعونا نلقي نظرة على ماهية RAG وكيف يعمل:

## التوليد المعزز بالاسترجاع (RAG)

الدردشة الآلية المدعومة بنموذج LLM تعالج مطالبات المستخدم لتوليد ردود. تم تصميمها لتكون تفاعلية وتتفاعل مع المستخدمين في مجموعة واسعة من المواضيع. ومع ذلك، تقتصر ردودها على السياق المقدم وبيانات التدريب الأساسية. على سبيل المثال، تاريخ معرفة GPT-4 هو سبتمبر 2021، مما يعني أنه يفتقر إلى معرفة الأحداث التي وقعت بعد هذه الفترة. بالإضافة إلى ذلك، البيانات المستخدمة لتدريب نماذج LLM تستثني المعلومات السرية مثل الملاحظات الشخصية أو دليل منتجات الشركة.

### كيف تعمل RAGs (التوليد المعزز بالاسترجاع)

![رسم يوضح كيفية عمل RAGs](../../../translated_images/how-rag-works.f5d0ff63942bd3a638e7efee7a6fce7f0787f6d7a1fca4e43f2a7a4d03cde3e0.ar.png)

افترض أنك تريد نشر دردشة آلية تنشئ اختبارات من ملاحظاتك، ستحتاج إلى اتصال بقاعدة المعرفة. هنا يأتي دور RAG. تعمل RAGs كما يلي:

- **قاعدة المعرفة:** قبل الاسترجاع، يجب استيعاب هذه الوثائق ومعالجتها مسبقًا، عادةً بتقسيم الوثائق الكبيرة إلى أجزاء أصغر، وتحويلها إلى تضمينات نصية وتخزينها في قاعدة بيانات.

- **استعلام المستخدم:** يطرح المستخدم سؤالًا

- **الاسترجاع:** عندما يطرح المستخدم سؤالًا، يقوم نموذج التضمين باسترجاع المعلومات ذات الصلة من قاعدة المعرفة لتوفير سياق إضافي يتم دمجه في المطالبة.

- **التوليد المعزز:** يعزز نموذج LLM رده بناءً على البيانات المسترجعة. يسمح هذا بأن يكون الرد المولد ليس فقط بناءً على البيانات المدربة مسبقًا، بل أيضًا على المعلومات ذات الصلة من السياق المضاف. تُستخدم البيانات المسترجعة لتعزيز ردود LLM. ثم يعيد LLM إجابة على سؤال المستخدم.

![رسم يوضح بنية RAGs](../../../translated_images/encoder-decode.f2658c25d0eadee2377bb28cf3aee8b67aa9249bf64d3d57bb9be077c4bc4e1a.ar.png)

يتم تنفيذ بنية RAGs باستخدام المحولات التي تتكون من جزأين: مشفر ومفكك ترميز. على سبيل المثال، عندما يطرح المستخدم سؤالًا، يتم "ترميز" النص المدخل إلى متجهات تلتقط معنى الكلمات، ثم يتم "فك ترميز" هذه المتجهات إلى فهرس الوثائق لدينا وينشئ نصًا جديدًا بناءً على استعلام المستخدم. يستخدم LLM نموذج مشفر-مفكك ترميز لتوليد المخرجات.

هناك طريقتان عند تنفيذ RAG وفقًا للورقة المقترحة: [التوليد المعزز بالاسترجاع لمهام معالجة اللغة الطبيعية المكثفة بالمعرفة](https://arxiv.org/pdf/2005.11401.pdf?WT.mc_id=academic-105485-koreyst):

- **_RAG-Sequence_** يستخدم الوثائق المسترجعة للتنبؤ بأفضل إجابة ممكنة لاستعلام المستخدم

- **RAG-Token** يستخدم الوثائق لتوليد الرمز التالي، ثم يسترجعها للإجابة على استعلام المستخدم

### لماذا تستخدم RAGs؟

- **غنى المعلومات:** يضمن أن تكون الردود النصية محدثة وحديثة. وبالتالي، يعزز الأداء في المهام الخاصة بالمجال من خلال الوصول إلى قاعدة المعرفة الداخلية.

- يقلل من التزييف باستخدام **بيانات يمكن التحقق منها** في قاعدة المعرفة لتوفير سياق لاستعلامات المستخدم.

- هو **موفر للتكاليف** لأنه أكثر اقتصادية مقارنة بضبط نموذج LLM.

## إنشاء قاعدة معرفة

تطبيقنا يعتمد على بياناتنا الشخصية، أي درس الشبكات العصبية في منهج الذكاء الاصطناعي للمبتدئين.

### قواعد بيانات المتجهات

قاعدة بيانات المتجهات، على عكس قواعد البيانات التقليدية، هي قاعدة بيانات متخصصة مصممة لتخزين وإدارة والبحث في المتجهات المضمنة. تخزن تمثيلات رقمية للوثائق. تقسيم البيانات إلى تضمينات رقمية يجعل من الأسهل على نظام الذكاء الاصطناعي فهم ومعالجة البيانات.

نخزن التضمينات في قواعد بيانات المتجهات لأن نماذج LLM لها حد لعدد الرموز التي تقبلها كمدخلات. بما أنه لا يمكن تمرير التضمينات كاملة إلى LLM، سنحتاج إلى تقسيمها إلى أجزاء وعندما يطرح المستخدم سؤالًا، يتم إرجاع التضمينات الأكثر تشابهًا مع السؤال مع المطالبة. التقسيم يقلل أيضًا من التكاليف المتعلقة بعدد الرموز الممررة عبر LLM.

بعض قواعد بيانات المتجهات الشهيرة تشمل Azure Cosmos DB، Clarifyai، Pinecone، Chromadb، ScaNN، Qdrant و DeepLake. يمكنك إنشاء نموذج Azure Cosmos DB باستخدام Azure CLI بالأمر التالي:

```bash
az login
az group create -n <resource-group-name> -l <location>
az cosmosdb create -n <cosmos-db-name> -r <resource-group-name>
az cosmosdb list-keys -n <cosmos-db-name> -g <resource-group-name>
```

### من النص إلى التضمينات

قبل تخزين بياناتنا، نحتاج إلى تحويلها إلى تضمينات متجهية قبل تخزينها في قاعدة البيانات. إذا كنت تعمل مع وثائق كبيرة أو نصوص طويلة، يمكنك تقسيمها بناءً على الاستعلامات التي تتوقعها. يمكن تقسيمها على مستوى الجملة أو الفقرة. بما أن التقسيم يستمد المعاني من الكلمات المحيطة، يمكنك إضافة بعض السياق الإضافي إلى الجزء، مثل إضافة عنوان الوثيقة أو تضمين بعض النص قبل أو بعد الجزء. يمكنك تقسيم البيانات كما يلي:

```python
def split_text(text, max_length, min_length):
    words = text.split()
    chunks = []
    current_chunk = []

    for word in words:
        current_chunk.append(word)
        if len(' '.join(current_chunk)) < max_length and len(' '.join(current_chunk)) > min_length:
            chunks.append(' '.join(current_chunk))
            current_chunk = []

    # If the last chunk didn't reach the minimum length, add it anyway
    if current_chunk:
        chunks.append(' '.join(current_chunk))

    return chunks
```

بمجرد تقسيمها، يمكننا تضمين نصنا باستخدام نماذج تضمين مختلفة. بعض النماذج التي يمكنك استخدامها تشمل: word2vec، ada-002 من OpenAI، Azure Computer Vision والعديد غيرها. اختيار النموذج يعتمد على اللغات التي تستخدمها، نوع المحتوى المشفر (نص/صور/صوت)، حجم المدخلات التي يمكنه ترميزها وطول مخرجات التضمين.

مثال على نص مضمّن باستخدام نموذج OpenAI `text-embedding-ada-002` هو:
![تضمين لكلمة cat](../../../translated_images/cat.74cbd7946bc9ca380a8894c4de0c706a4f85b16296ffabbf52d6175df6bf841e.ar.png)

## الاسترجاع والبحث في المتجهات

عندما يطرح المستخدم سؤالًا، يقوم المسترجع بتحويله إلى متجه باستخدام مشفر الاستعلام، ثم يبحث في فهرس البحث عن المتجهات ذات الصلة في الوثائق المرتبطة بالمدخل. بعد ذلك، يحول كل من متجه الإدخال ومتجهات الوثائق إلى نص ويمررها عبر LLM.

### الاسترجاع

يحدث الاسترجاع عندما يحاول النظام بسرعة العثور على الوثائق من الفهرس التي تلبي معايير البحث. هدف المسترجع هو الحصول على وثائق تُستخدم لتوفير السياق وتأصيل LLM على بياناتك.

هناك عدة طرق لأداء البحث داخل قاعدة بياناتنا مثل:

- **البحث بالكلمات المفتاحية** - يستخدم للبحث النصي

- **البحث الدلالي** - يستخدم المعنى الدلالي للكلمات

- **البحث بالمتجهات** - يحول الوثائق من نص إلى تمثيلات متجهية باستخدام نماذج التضمين. يتم الاسترجاع عن طريق استعلام الوثائق التي تمثيلاتها المتجهية الأقرب إلى سؤال المستخدم.

- **الهجين** - مزيج من البحث بالكلمات المفتاحية والبحث بالمتجهات.

تظهر تحديات الاسترجاع عندما لا يوجد رد مشابه للاستعلام في قاعدة البيانات، حينها يعيد النظام أفضل المعلومات المتاحة، ومع ذلك يمكنك استخدام استراتيجيات مثل تحديد الحد الأقصى للمسافة للملاءمة أو استخدام البحث الهجين الذي يجمع بين الكلمات المفتاحية والبحث بالمتجهات. في هذا الدرس سنستخدم البحث الهجين، وهو مزيج من البحث بالمتجهات والكلمات المفتاحية. سنخزن بياناتنا في إطار بيانات يحتوي على أعمدة للأجزاء وكذلك التضمينات.

### تشابه المتجهات

يبحث المسترجع في قاعدة المعرفة عن التضمينات المتقاربة، أي الجار الأقرب، لأنها نصوص متشابهة. في السيناريو، عندما يطرح المستخدم استعلامًا، يتم تضمينه أولاً ثم مطابقته مع التضمينات المشابهة. المقياس الشائع المستخدم لقياس مدى تشابه المتجهات المختلفة هو تشابه جيب التمام (cosine similarity) الذي يعتمد على الزاوية بين متجهين.

يمكننا قياس التشابه باستخدام بدائل أخرى مثل المسافة الإقليدية (Euclidean distance) وهي الخط المستقيم بين نقاط نهاية المتجهات، والضرب الداخلي (dot product) الذي يقيس مجموع حاصل ضرب العناصر المقابلة في متجهين.

### فهرس البحث

عند إجراء الاسترجاع، نحتاج إلى بناء فهرس بحث لقاعدة المعرفة قبل تنفيذ البحث. يخزن الفهرس التضمينات ويمكنه استرجاع الأجزاء الأكثر تشابهًا بسرعة حتى في قاعدة بيانات كبيرة. يمكننا إنشاء الفهرس محليًا باستخدام:

```python
from sklearn.neighbors import NearestNeighbors

embeddings = flattened_df['embeddings'].to_list()

# Create the search index
nbrs = NearestNeighbors(n_neighbors=5, algorithm='ball_tree').fit(embeddings)

# To query the index, you can use the kneighbors method
distances, indices = nbrs.kneighbors(embeddings)
```

### إعادة الترتيب

بعد استعلام قاعدة البيانات، قد تحتاج إلى فرز النتائج حسب الأكثر صلة. يستخدم LLM لإعادة الترتيب التعلم الآلي لتحسين ملاءمة نتائج البحث بترتيبها من الأكثر صلة. باستخدام Azure AI Search، تتم إعادة الترتيب تلقائيًا باستخدام معيد ترتيب دلالي. مثال على كيفية عمل إعادة الترتيب باستخدام الجيران الأقرب:

```python
# Find the most similar documents
distances, indices = nbrs.kneighbors([query_vector])

index = []
# Print the most similar documents
for i in range(3):
    index = indices[0][i]
    for index in indices[0]:
        print(flattened_df['chunks'].iloc[index])
        print(flattened_df['path'].iloc[index])
        print(flattened_df['distances'].iloc[index])
    else:
        print(f"Index {index} not found in DataFrame")
```

## جمع كل شيء معًا

الخطوة الأخيرة هي إضافة نموذج LLM إلى المعادلة للحصول على ردود مؤصلة على بياناتنا. يمكننا تنفيذ ذلك كما يلي:

```python
user_input = "what is a perceptron?"

def chatbot(user_input):
    # Convert the question to a query vector
    query_vector = create_embeddings(user_input)

    # Find the most similar documents
    distances, indices = nbrs.kneighbors([query_vector])

    # add documents to query  to provide context
    history = []
    for index in indices[0]:
        history.append(flattened_df['chunks'].iloc[index])

    # combine the history and the user input
    history.append(user_input)

    # create a message object
    messages=[
        {"role": "system", "content": "You are an AI assistant that helps with AI questions."},
        {"role": "user", "content": history[-1]}
    ]

    # use chat completion to generate a response
    response = openai.chat.completions.create(
        model="gpt-4",
        temperature=0.7,
        max_tokens=800,
        messages=messages
    )

    return response.choices[0].message

chatbot(user_input)
```

## تقييم تطبيقنا

### مقاييس التقييم

- جودة الردود المقدمة مع التأكد من أنها تبدو طبيعية، سلسة وبشبه الإنسان

- تأصيل البيانات: تقييم ما إذا كانت الردود مستندة إلى الوثائق المقدمة

- الصلة: تقييم مدى تطابق الرد مع السؤال المطروح وارتباطه به

- الطلاقة - ما إذا كان الرد منطقيًا نحويًا

## حالات استخدام RAG (التوليد المعزز بالاسترجاع) وقواعد بيانات المتجهات

هناك العديد من حالات الاستخدام المختلفة حيث يمكن لاستدعاءات الوظائف تحسين تطبيقك مثل:

- الأسئلة والأجوبة: تأصيل بيانات شركتك في دردشة يمكن للموظفين استخدامها لطرح الأسئلة.

- أنظمة التوصية: حيث يمكنك إنشاء نظام يطابق القيم الأكثر تشابهًا مثل الأفلام، المطاعم والعديد غيرها.

- خدمات الدردشة الآلية: يمكنك تخزين سجل المحادثات وتخصيص الحوار بناءً على بيانات المستخدم.

- البحث عن الصور بناءً على تضمينات المتجهات، مفيد عند إجراء التعرف على الصور واكتشاف الشذوذ.

## الملخص

لقد غطينا المجالات الأساسية لـ RAG من إضافة بياناتنا إلى التطبيق، استعلام المستخدم والمخرجات. لتبسيط إنشاء RAG، يمكنك استخدام أُطُر مثل Semanti Kernel، Langchain أو Autogen.

## الواجب

لمواصلة تعلمك حول التوليد المعزز بالاسترجاع (RAG) يمكنك بناء:

- واجهة أمامية للتطبيق باستخدام الإطار الذي تختاره

- استخدام إطار عمل، إما LangChain أو Semantic Kernel، وإعادة إنشاء تطبيقك.

تهانينا على إكمال الدرس 👏.

## التعلم لا يتوقف هنا، استمر في الرحلة

بعد إكمال هذا الدرس، اطلع على [مجموعة تعلم الذكاء الاصطناعي التوليدي](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) لمواصلة تطوير معرفتك في الذكاء الاصطناعي التوليدي!

**إخلاء المسؤولية**:  
تمت ترجمة هذا المستند باستخدام خدمة الترجمة الآلية [Co-op Translator](https://github.com/Azure/co-op-translator). بينما نسعى لتحقيق الدقة، يرجى العلم أن الترجمات الآلية قد تحتوي على أخطاء أو عدم دقة. يجب اعتبار المستند الأصلي بلغته الأصلية المصدر الموثوق به. للمعلومات الهامة، يُنصح بالاعتماد على الترجمة البشرية المهنية. نحن غير مسؤولين عن أي سوء فهم أو تفسير ناتج عن استخدام هذه الترجمة.