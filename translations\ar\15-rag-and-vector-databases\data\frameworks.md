<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:25:39+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "ar"
}
-->
# أُطُر الشبكات العصبية

كما تعلمنا سابقًا، لكي نتمكن من تدريب الشبكات العصبية بكفاءة نحتاج إلى القيام بشيئين:

* العمل على التنسورات، مثل الضرب، الجمع، وحساب بعض الدوال مثل sigmoid أو softmax  
* حساب التدرجات لجميع التعبيرات، من أجل تنفيذ تحسين الانحدار التدرجي

بينما يمكن لمكتبة `numpy` القيام بالجزء الأول، نحتاج إلى آلية لحساب التدرجات. في الإطار الذي طورناه في القسم السابق، كان علينا برمجة جميع دوال المشتقات يدويًا داخل دالة `backward` التي تقوم بالتراجع العكسي. من الناحية المثالية، يجب أن يتيح لنا الإطار فرصة حساب التدرجات لأي تعبير يمكننا تعريفه.

شيء مهم آخر هو القدرة على إجراء العمليات الحسابية على وحدة معالجة الرسومات (GPU)، أو أي وحدات حساب متخصصة أخرى مثل TPU. يتطلب تدريب الشبكات العصبية العميقة كمية كبيرة من العمليات الحسابية، ومن المهم جدًا أن نتمكن من توزيع هذه العمليات على وحدات معالجة الرسومات.

> ✅ مصطلح 'parallelize' يعني توزيع العمليات الحسابية على عدة أجهزة.

حاليًا، أكثر إطارين شيوعًا للشبكات العصبية هما: TensorFlow و PyTorch. كلاهما يوفر واجهة برمجة تطبيقات منخفضة المستوى للعمل مع التنسورات على كل من وحدة المعالجة المركزية (CPU) ووحدة معالجة الرسومات (GPU). بالإضافة إلى واجهة برمجة التطبيقات منخفضة المستوى، هناك أيضًا واجهة برمجة تطبيقات عالية المستوى، تسمى Keras و PyTorch Lightning على التوالي.

واجهة برمجة التطبيقات منخفضة المستوى | TensorFlow | PyTorch  
---------------------------------|-------------------------------|------------------------------  
واجهة برمجة التطبيقات عالية المستوى | Keras | PyTorch Lightning

**واجهات برمجة التطبيقات منخفضة المستوى** في كلا الإطارين تتيح لك بناء ما يسمى **الرسوم البيانية الحسابية**. هذا الرسم يحدد كيفية حساب المخرجات (عادة دالة الخسارة) باستخدام المعطيات المدخلة، ويمكن دفعه ليتم حسابه على وحدة معالجة الرسومات إذا كانت متاحة. هناك دوال لتفاضل هذا الرسم البياني الحسابي وحساب التدرجات، والتي يمكن استخدامها لاحقًا لتحسين معلمات النموذج.

**واجهات برمجة التطبيقات عالية المستوى** تعتبر الشبكات العصبية بشكل أساسي كـ **تسلسل من الطبقات**، وتجعل بناء معظم الشبكات العصبية أسهل بكثير. عادةً ما يتطلب تدريب النموذج تحضير البيانات ثم استدعاء دالة `fit` للقيام بالعملية.

واجهة برمجة التطبيقات عالية المستوى تتيح لك بناء الشبكات العصبية النموذجية بسرعة دون القلق بشأن الكثير من التفاصيل. في الوقت نفسه، توفر واجهة برمجة التطبيقات منخفضة المستوى تحكمًا أكبر في عملية التدريب، ولهذا تُستخدم كثيرًا في الأبحاث، خاصة عند التعامل مع معماريات شبكات عصبية جديدة.

من المهم أيضًا أن تفهم أنه يمكنك استخدام كلا الواجهتين معًا، مثلاً يمكنك تطوير بنية طبقة الشبكة الخاصة بك باستخدام واجهة برمجة التطبيقات منخفضة المستوى، ثم استخدامها داخل شبكة أكبر تم بناؤها وتدريبها باستخدام واجهة برمجة التطبيقات عالية المستوى. أو يمكنك تعريف شبكة باستخدام الواجهة عالية المستوى كتسلسل من الطبقات، ثم استخدام حلقة تدريب منخفضة المستوى خاصة بك لأداء التحسين. كلا الواجهتين تستخدمان نفس المفاهيم الأساسية، وهما مصممتان للعمل معًا بشكل جيد.

## التعلم

في هذه الدورة، نقدم معظم المحتوى لكل من PyTorch و TensorFlow. يمكنك اختيار الإطار المفضل لديك والاطلاع فقط على دفاتر الملاحظات الخاصة به. إذا لم تكن متأكدًا من أي إطار تختار، اقرأ بعض النقاشات على الإنترنت حول **PyTorch مقابل TensorFlow**. يمكنك أيضًا إلقاء نظرة على كلا الإطارين لفهم أفضل.

حيثما أمكن، سنستخدم واجهات برمجة التطبيقات عالية المستوى من أجل البساطة. ومع ذلك، نعتقد أنه من المهم فهم كيفية عمل الشبكات العصبية من الأساس، لذلك نبدأ في البداية بالعمل مع واجهة برمجة التطبيقات منخفضة المستوى والتنسورات. ولكن إذا كنت تريد البدء بسرعة ولا ترغب في قضاء وقت طويل في تعلم هذه التفاصيل، يمكنك تخطيها والانتقال مباشرة إلى دفاتر الملاحظات الخاصة بالواجهة عالية المستوى.

## ✍️ تمارين: الأُطُر

واصل تعلمك في دفاتر الملاحظات التالية:

واجهة برمجة التطبيقات منخفضة المستوى | دفتر ملاحظات TensorFlow+Keras | PyTorch  
---------------------------------|-------------------------------|------------------------------  
واجهة برمجة التطبيقات عالية المستوى | Keras | *PyTorch Lightning*

بعد إتقان الأُطُر، دعنا نراجع مفهوم الإفراط في التخصيص.

# الإفراط في التخصيص (Overfitting)

الإفراط في التخصيص هو مفهوم بالغ الأهمية في تعلم الآلة، ومن المهم جدًا فهمه بشكل صحيح!

فكر في المشكلة التالية لتقريب 5 نقاط (ممثلة بـ `x` على الرسوم البيانية أدناه):

!linear | overfit  
-------------------------|--------------------------  
**نموذج خطي، 2 معلمة** | **نموذج غير خطي، 7 معلمات**  
خطأ التدريب = 5.3 | خطأ التدريب = 0  
خطأ التحقق = 5.1 | خطأ التحقق = 20

* على اليسار، نرى تقريبًا جيدًا بخط مستقيم. لأن عدد المعلمات مناسب، يفهم النموذج التوزيع الصحيح للنقاط.  
* على اليمين، النموذج قوي جدًا. لأن لدينا 5 نقاط فقط والنموذج يحتوي على 7 معلمات، يمكنه التكيف بحيث يمر عبر جميع النقاط، مما يجعل خطأ التدريب صفرًا. ومع ذلك، هذا يمنع النموذج من فهم النمط الصحيح وراء البيانات، لذا فإن خطأ التحقق مرتفع جدًا.

من المهم جدًا تحقيق توازن صحيح بين غنى النموذج (عدد المعلمات) وعدد عينات التدريب.

## لماذا يحدث الإفراط في التخصيص

  * عدم وجود بيانات تدريب كافية  
  * نموذج قوي جدًا  
  * وجود ضوضاء كثيرة في بيانات الإدخال

## كيف نكتشف الإفراط في التخصيص

كما ترى من الرسم البياني أعلاه، يمكن اكتشاف الإفراط في التخصيص من خلال خطأ تدريب منخفض جدًا، وخطأ تحقق مرتفع. عادةً أثناء التدريب، سنرى كل من أخطاء التدريب والتحقق تبدأ في الانخفاض، ثم في نقطة ما قد يتوقف خطأ التحقق عن الانخفاض ويبدأ في الارتفاع. هذه ستكون علامة على الإفراط في التخصيص، ومؤشر على أنه من الأفضل التوقف عن التدريب في هذه النقطة (أو على الأقل أخذ لقطة من النموذج).

overfitting

## كيف نمنع الإفراط في التخصيص

إذا لاحظت حدوث الإفراط في التخصيص، يمكنك القيام بأحد الأمور التالية:

 * زيادة كمية بيانات التدريب  
 * تقليل تعقيد النموذج  
 * استخدام تقنية تنظيم مثل Dropout، والتي سنناقشها لاحقًا.

## الإفراط في التخصيص وتوازن الانحياز والتباين

الإفراط في التخصيص هو في الواقع حالة من مشكلة أعم في الإحصاء تُسمى توازن الانحياز والتباين. إذا نظرنا إلى مصادر الخطأ المحتملة في نموذجنا، يمكننا رؤية نوعين من الأخطاء:

* **أخطاء الانحياز** تحدث بسبب عدم قدرة خوارزميتنا على التقاط العلاقة بين بيانات التدريب بشكل صحيح. قد ينتج هذا عن أن نموذجنا ليس قويًا بما فيه الكفاية (**نقص التخصيص**).  
* **أخطاء التباين** تحدث بسبب أن النموذج يقرب الضوضاء في بيانات الإدخال بدلاً من العلاقة ذات المعنى (**الإفراط في التخصيص**).

أثناء التدريب، ينخفض خطأ الانحياز (مع تعلم النموذج تقريب البيانات)، ويزداد خطأ التباين. من المهم التوقف عن التدريب - إما يدويًا (عندما نكتشف الإفراط في التخصيص) أو تلقائيًا (عن طريق إدخال التنظيم) - لمنع الإفراط في التخصيص.

## الخلاصة

في هذا الدرس، تعلمت عن الفروقات بين واجهات برمجة التطبيقات المختلفة لأشهر إطارين في الذكاء الاصطناعي، TensorFlow و PyTorch. بالإضافة إلى ذلك، تعرفت على موضوع مهم جدًا وهو الإفراط في التخصيص.

## 🚀 التحدي

في دفاتر الملاحظات المرفقة، ستجد "مهام" في الأسفل؛ اعمل على دفاتر الملاحظات وأكمل المهام.

## المراجعة والدراسة الذاتية

قم ببعض البحث حول المواضيع التالية:

- TensorFlow  
- PyTorch  
- الإفراط في التخصيص

اسأل نفسك الأسئلة التالية:

- ما الفرق بين TensorFlow و PyTorch؟  
- ما الفرق بين الإفراط في التخصيص ونقص التخصيص؟

## الواجب

في هذا المختبر، يُطلب منك حل مشكلتين تصنيفيتين باستخدام شبكات متصلة بالكامل ذات طبقة واحدة ومتعددة الطبقات باستخدام PyTorch أو TensorFlow.

**إخلاء المسؤولية**:  
تمت ترجمة هذا المستند باستخدام خدمة الترجمة الآلية [Co-op Translator](https://github.com/Azure/co-op-translator). بينما نسعى لتحقيق الدقة، يرجى العلم أن الترجمات الآلية قد تحتوي على أخطاء أو عدم دقة. يجب اعتبار المستند الأصلي بلغته الأصلية المصدر الموثوق به. للمعلومات الهامة، يُنصح بالاعتماد على الترجمة البشرية المهنية. نحن غير مسؤولين عن أي سوء فهم أو تفسير ناتج عن استخدام هذه الترجمة.