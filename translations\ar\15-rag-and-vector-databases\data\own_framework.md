<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:40:56+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "ar"
}
-->
# مقدمة في الشبكات العصبية. الإدراك متعدد الطبقات

في القسم السابق، تعرّفت على أبسط نموذج للشبكة العصبية - الإدراك ذو الطبقة الواحدة، وهو نموذج تصنيف خطي لفئتين.

في هذا القسم سنقوم بتوسيع هذا النموذج إلى إطار عمل أكثر مرونة، يسمح لنا بـ:

* إجراء **تصنيف متعدد الفئات** بالإضافة إلى تصنيف فئتين
* حل **مشاكل الانحدار** بالإضافة إلى التصنيف
* فصل الفئات التي لا يمكن فصلها خطيًا

سنقوم أيضًا بتطوير إطار عمل معياري خاص بنا بلغة Python يسمح لنا ببناء هياكل شبكات عصبية مختلفة.

## صياغة مشكلة التعلم الآلي

لنبدأ بصياغة مشكلة التعلم الآلي بشكل رسمي. لنفترض أن لدينا مجموعة بيانات تدريبية **X** مع تسميات **Y**، ونحتاج إلى بناء نموذج *f* يقوم بالتنبؤ بأكبر دقة ممكنة. يتم قياس جودة التنبؤات بواسطة **دالة الخسارة** ℒ. غالبًا ما تُستخدم دوال خسارة مثل:

* لمشكلة الانحدار، عندما نحتاج إلى التنبؤ برقم، يمكننا استخدام **الخطأ المطلق** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>|، أو **الخطأ التربيعي** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup>
* للتصنيف، نستخدم **خسارة 0-1** (وهي في الأساس نفس دقة النموذج)، أو **خسارة لوجستية**.

بالنسبة للإدراك ذو الطبقة الواحدة، كانت الدالة *f* معرفة كدالة خطية *f(x)=wx+b* (حيث *w* هي مصفوفة الأوزان، و*x* هو متجه الميزات المدخلة، و*b* هو متجه الانحياز). بالنسبة لهياكل الشبكات العصبية المختلفة، يمكن أن تأخذ هذه الدالة شكلًا أكثر تعقيدًا.

> في حالة التصنيف، غالبًا ما يكون من المرغوب فيه الحصول على احتمالات الفئات المقابلة كمخرجات الشبكة. لتحويل أرقام عشوائية إلى احتمالات (مثلًا لتطبيع المخرجات)، نستخدم غالبًا دالة **softmax** σ، وتصبح الدالة *f* على الشكل *f(x)=σ(wx+b)*

في التعريف السابق لـ *f*، يُطلق على *w* و *b* اسم **المعلمات** θ=⟨*w,b*⟩. بالنظر إلى مجموعة البيانات ⟨**X**,**Y**⟩، يمكننا حساب الخطأ الكلي على كامل مجموعة البيانات كدالة للمعلمات θ.

> ✅ **هدف تدريب الشبكة العصبية هو تقليل الخطأ عن طريق تعديل المعلمات θ**

## تحسين الانحدار التدريجي

هناك طريقة معروفة لتحسين الدوال تسمى **الانحدار التدريجي**. الفكرة هي أننا نستطيع حساب مشتقة (في الحالة متعددة الأبعاد تسمى **التدرج**) لدالة الخسارة بالنسبة للمعلمات، وتعديل المعلمات بطريقة تقلل الخطأ. يمكن صياغة ذلك كما يلي:

* تهيئة المعلمات بقيم عشوائية w<sup>(0)</sup>، b<sup>(0)</sup>
* تكرار الخطوة التالية عدة مرات:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

أثناء التدريب، من المفترض أن تُحسب خطوات التحسين مع الأخذ في الاعتبار كامل مجموعة البيانات (تذكر أن الخسارة تُحسب كمجموع عبر جميع عينات التدريب). لكن في الواقع، نأخذ أجزاء صغيرة من مجموعة البيانات تسمى **minibatches**، ونحسب التدرجات بناءً على جزء من البيانات. وبما أن الجزء يُختار عشوائيًا في كل مرة، تُسمى هذه الطريقة **الانحدار التدريجي العشوائي** (SGD).

## الإدراكات متعددة الطبقات والانتشار العكسي

الشبكة ذات الطبقة الواحدة، كما رأينا أعلاه، قادرة على تصنيف الفئات القابلة للفصل خطيًا. لبناء نموذج أكثر تعقيدًا، يمكننا دمج عدة طبقات من الشبكة. رياضيًا، يعني ذلك أن الدالة *f* ستأخذ شكلًا أكثر تعقيدًا، وسيتم حسابها على عدة خطوات:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

هنا، α هي **دالة تفعيل غير خطية**، وσ هي دالة softmax، والمعلمات θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*>.

خوارزمية الانحدار التدريجي تبقى نفسها، لكن سيكون من الأصعب حساب التدرجات. باستخدام قاعدة اشتقاق السلسلة، يمكننا حساب المشتقات كما يلي:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ تُستخدم قاعدة اشتقاق السلسلة لحساب مشتقات دالة الخسارة بالنسبة للمعلمات.

لاحظ أن الجزء الأيسر من كل هذه التعبيرات هو نفسه، وبالتالي يمكننا حساب المشتقات بفعالية بدءًا من دالة الخسارة والرجوع "للخلف" عبر الرسم البياني الحسابي. لذلك تُسمى طريقة تدريب الإدراك متعدد الطبقات **الانتشار العكسي**، أو 'backprop'.

> TODO: image citation

> ✅ سنغطي الانتشار العكسي بتفصيل أكبر في مثال دفتر الملاحظات الخاص بنا.

## الخاتمة

في هذا الدرس، بنينا مكتبة الشبكة العصبية الخاصة بنا، واستخدمناها لمهمة تصنيف بسيطة ذات بعدين.

## 🚀 التحدي

في دفتر الملاحظات المرافق، ستقوم بتنفيذ إطار عمل خاص بك لبناء وتدريب الإدراكات متعددة الطبقات. ستتمكن من رؤية كيفية عمل الشبكات العصبية الحديثة بالتفصيل.

تابع إلى دفتر OwnFramework وابدأ العمل عليه.

## المراجعة والدراسة الذاتية

الانتشار العكسي هو خوارزمية شائعة تُستخدم في الذكاء الاصطناعي والتعلم الآلي، ويستحق دراسة أعمق.

## الواجب

في هذا المختبر، يُطلب منك استخدام الإطار الذي أنشأته في هذا الدرس لحل تصنيف أرقام MNIST المكتوبة يدويًا.

* التعليمات
* دفتر الملاحظات

**إخلاء المسؤولية**:  
تمت ترجمة هذا المستند باستخدام خدمة الترجمة الآلية [Co-op Translator](https://github.com/Azure/co-op-translator). بينما نسعى لتحقيق الدقة، يرجى العلم أن الترجمات الآلية قد تحتوي على أخطاء أو عدم دقة. يجب اعتبار المستند الأصلي بلغته الأصلية المصدر الموثوق به. للمعلومات الهامة، يُنصح بالترجمة البشرية المهنية. نحن غير مسؤولين عن أي سوء فهم أو تفسير ناتج عن استخدام هذه الترجمة.