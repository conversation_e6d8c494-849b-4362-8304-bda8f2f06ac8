<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T16:54:02+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "ar"
}
-->
# مقدمة في الشبكات العصبية: البرسيبترون

كانت إحدى المحاولات الأولى لتطبيق شيء مشابه للشبكة العصبية الحديثة من قبل فرانك روزنبلات من مختبر كورنيل للطيران في عام 1957. كان تنفيذًا ماديًا يُدعى "Mark-1"، صُمم للتعرف على أشكال هندسية بدائية مثل المثلثات والمربعات والدوائر.

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='<PERSON>'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> صور من ويكيبيديا

كانت صورة الإدخال ممثلة بمصفوفة من 20×20 خلية ضوئية، لذا كان لدى الشبكة العصبية 400 مدخل وخرج ثنائي واحد. احتوى النموذج البسيط على عصبون واحد، يُسمى أيضًا **وحدة المنطق العتبي**. كانت أوزان الشبكة العصبية تعمل مثل المقاومات المتغيرة التي تحتاج إلى تعديل يدوي خلال مرحلة التدريب.

> ✅ المقاوم المتغير هو جهاز يسمح للمستخدم بضبط مقاومة الدائرة.

> كتبت صحيفة نيويورك تايمز عن البرسيبترون في ذلك الوقت: *الجنين الأولي لحاسوب إلكتروني تتوقع البحرية أن يكون قادرًا على المشي، والتحدث، والرؤية، والكتابة، والتكاثر، وأن يكون واعيًا بوجوده.*

## نموذج البرسيبترون

لنفترض أن لدينا N من الميزات في نموذجنا، في هذه الحالة سيكون متجه الإدخال متجهًا بحجم N. البرسيبترون هو نموذج **تصنيف ثنائي**، أي يمكنه التمييز بين فئتين من بيانات الإدخال. سنفترض أنه لكل متجه إدخال x سيكون خرج البرسيبترون إما +1 أو -1، اعتمادًا على الفئة. يتم حساب الخرج باستخدام الصيغة:

y(x) = f(w<sup>T</sup>x)

حيث f هي دالة تفعيل عتبية

## تدريب البرسيبترون

لتدريب البرسيبترون نحتاج إلى إيجاد متجه الأوزان w الذي يصنف معظم القيم بشكل صحيح، أي ينتج أقل **خطأ** ممكن. يُعرف هذا الخطأ بواسطة **معيار البرسيبترون** على النحو التالي:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

حيث:

* المجموع يُؤخذ على نقاط بيانات التدريب i التي تؤدي إلى تصنيف خاطئ
* x<sub>i</sub> هي بيانات الإدخال، و t<sub>i</sub> إما -1 أو +1 للأمثلة السلبية والإيجابية على التوالي.

يُعتبر هذا المعيار دالة للأوزان w، ونحتاج إلى تقليله. غالبًا ما يُستخدم أسلوب يُسمى **انحدار التدرج**، حيث نبدأ ببعض الأوزان الأولية w<sup>(0)</sup>، ثم في كل خطوة نحدث الأوزان وفقًا للصيغة:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

هنا η هو ما يُسمى **معدل التعلم**، و∇E(w) تمثل **تدرج** E. بعد حساب التدرج، نحصل على

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

الخوارزمية في بايثون تبدو كالتالي:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## الخلاصة

في هذا الدرس، تعلّمت عن البرسيبترون، وهو نموذج تصنيف ثنائي، وكيفية تدريبه باستخدام متجه الأوزان.

## 🚀 التحدي

إذا أردت تجربة بناء البرسيبترون الخاص بك، جرب هذا المختبر على Microsoft Learn الذي يستخدم مصمم Azure ML


## المراجعة والدراسة الذاتية

لرؤية كيف يمكننا استخدام البرسيبترون لحل مشكلة بسيطة وكذلك مشاكل الحياة الواقعية، ولمواصلة التعلم - انتقل إلى دفتر ملاحظات Perceptron.

إليك أيضًا مقالًا مثيرًا للاهتمام عن البرسيبترونات.

## الواجب

في هذا الدرس، قمنا بتنفيذ برسيبترون لمهمة تصنيف ثنائي، واستخدمناه لتصنيف رقمين مكتوبين يدويًا. في هذا المختبر، يُطلب منك حل مشكلة تصنيف الأرقام بالكامل، أي تحديد الرقم الأكثر احتمالًا ليتطابق مع صورة معينة.

* التعليمات
* دفتر الملاحظات

**إخلاء المسؤولية**:  
تمت ترجمة هذا المستند باستخدام خدمة الترجمة الآلية [Co-op Translator](https://github.com/Azure/co-op-translator). بينما نسعى لتحقيق الدقة، يرجى العلم أن الترجمات الآلية قد تحتوي على أخطاء أو عدم دقة. يجب اعتبار المستند الأصلي بلغته الأصلية المصدر الموثوق به. للمعلومات الهامة، يُنصح بالاعتماد على الترجمة البشرية المهنية. نحن غير مسؤولين عن أي سوء فهم أو تفسير ناتج عن استخدام هذه الترجمة.