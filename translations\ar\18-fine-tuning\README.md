<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "68664f7e754a892ae1d8d5e2b7bd2081",
  "translation_date": "2025-07-09T17:35:37+00:00",
  "source_file": "18-fine-tuning/README.md",
  "language_code": "ar"
}
-->
[![نماذج مفتوحة المصدر](../../../translated_images/18-lesson-banner.f30176815b1a5074fce9cceba317720586caa99e24001231a92fd04eeb54a121.ar.png)](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst)

# تحسين نموذج اللغة الكبير الخاص بك

استخدام نماذج اللغة الكبيرة لبناء تطبيقات الذكاء الاصطناعي التوليدي يأتي مع تحديات جديدة. إحدى القضايا الرئيسية هي ضمان جودة الاستجابة (الدقة والملاءمة) في المحتوى الذي يولده النموذج لطلب المستخدم المعطى. في الدروس السابقة، ناقشنا تقنيات مثل هندسة المطالبات والتوليد المعزز بالاسترجاع التي تحاول حل المشكلة عن طريق _تعديل مدخلات المطالبة_ للنموذج الحالي.

في درس اليوم، نناقش التقنية الثالثة، **التحسين الدقيق (fine-tuning)**، التي تحاول معالجة التحدي عن طريق _إعادة تدريب النموذج نفسه_ باستخدام بيانات إضافية. هيا نغوص في التفاصيل.

## أهداف التعلم

يقدم هذا الدرس مفهوم التحسين الدقيق لنماذج اللغة المدربة مسبقًا، ويستعرض فوائد وتحديات هذا النهج، ويوفر إرشادات حول متى وكيفية استخدام التحسين الدقيق لتحسين أداء نماذج الذكاء الاصطناعي التوليدي الخاصة بك.

بنهاية هذا الدرس، يجب أن تكون قادرًا على الإجابة على الأسئلة التالية:

- ما هو التحسين الدقيق لنماذج اللغة؟
- متى ولماذا يكون التحسين الدقيق مفيدًا؟
- كيف يمكنني تحسين نموذج مدرب مسبقًا؟
- ما هي حدود التحسين الدقيق؟

هل أنت مستعد؟ لنبدأ.

## دليل مصور

هل تريد الحصول على صورة شاملة لما سنغطيه قبل أن نبدأ؟ اطلع على هذا الدليل المصور الذي يصف رحلة التعلم لهذا الدرس - من تعلم المفاهيم الأساسية والدوافع للتحسين الدقيق، إلى فهم العملية وأفضل الممارسات لتنفيذ مهمة التحسين الدقيق. هذا موضوع شيق للاستكشاف، فلا تنسَ زيارة صفحة [الموارد](./RESOURCES.md?WT.mc_id=academic-105485-koreyst) للحصول على روابط إضافية تدعم رحلتك التعليمية الذاتية!

![دليل مصور لتحسين نماذج اللغة](../../../translated_images/18-fine-tuning-sketchnote.11b21f9ec8a703467a120cb79a28b5ac1effc8d8d9d5b31bbbac6b8640432e14.ar.png)

## ما هو التحسين الدقيق لنماذج اللغة؟

بحسب التعريف، نماذج اللغة الكبيرة تكون _مدربة مسبقًا_ على كميات كبيرة من النصوص مأخوذة من مصادر متنوعة بما في ذلك الإنترنت. كما تعلمنا في الدروس السابقة، نحتاج إلى تقنيات مثل _هندسة المطالبات_ و _التوليد المعزز بالاسترجاع_ لتحسين جودة استجابات النموذج لأسئلة المستخدم ("المطالبات").

تقنية شائعة في هندسة المطالبات تتضمن إعطاء النموذج مزيدًا من التوجيه حول ما هو متوقع في الاستجابة إما عن طريق تقديم _تعليمات_ (توجيه صريح) أو _إعطائه بعض الأمثلة_ (توجيه ضمني). يُشار إلى هذا بالتعلم القليل الأمثلة (few-shot learning) لكنه له حدان:

- حدود عدد الرموز في النموذج قد تقيد عدد الأمثلة التي يمكنك تقديمها، وتحد من الفعالية.
- تكلفة الرموز في النموذج قد تجعل إضافة الأمثلة لكل مطالبة مكلفة، وتحد من المرونة.

التحسين الدقيق هو ممارسة شائعة في أنظمة التعلم الآلي حيث نأخذ نموذجًا مدربًا مسبقًا ونعيد تدريبه ببيانات جديدة لتحسين أدائه في مهمة محددة. في سياق نماذج اللغة، يمكننا تحسين النموذج المدرب مسبقًا _بمجموعة مختارة من الأمثلة لمهمة أو مجال تطبيقي معين_ لإنشاء **نموذج مخصص** قد يكون أكثر دقة وملاءمة لتلك المهمة أو المجال المحدد. فائدة جانبية للتحسين الدقيق هي أنه يمكن أن يقلل أيضًا من عدد الأمثلة المطلوبة للتعلم القليل الأمثلة - مما يقلل من استخدام الرموز والتكاليف المرتبطة بها.

## متى ولماذا يجب أن نحسن النماذج؟

في _هذا_ السياق، عندما نتحدث عن التحسين الدقيق، فإننا نشير إلى التحسين الدقيق **المشرف عليه** حيث يتم إعادة التدريب عن طريق **إضافة بيانات جديدة** لم تكن جزءًا من مجموعة البيانات الأصلية. هذا يختلف عن نهج التحسين الدقيق غير المشرف حيث يعاد تدريب النموذج على البيانات الأصلية، لكن مع معلمات فائقة مختلفة.

الأمر الأساسي الذي يجب تذكره هو أن التحسين الدقيق تقنية متقدمة تتطلب مستوى معين من الخبرة للحصول على النتائج المرجوة. إذا تم بشكل غير صحيح، قد لا يوفر التحسين التحسينات المتوقعة، وقد يؤدي حتى إلى تدهور أداء النموذج في المجال المستهدف.

لذا، قبل أن تتعلم "كيف" تحسن نماذج اللغة، تحتاج إلى معرفة "لماذا" يجب أن تسلك هذا الطريق، و"متى" تبدأ عملية التحسين الدقيق. ابدأ بطرح هذه الأسئلة على نفسك:

- **حالة الاستخدام**: ما هي _حالة الاستخدام_ الخاصة بك للتحسين الدقيق؟ ما الجانب الذي تريد تحسينه في النموذج المدرب مسبقًا الحالي؟
- **البدائل**: هل جربت _تقنيات أخرى_ لتحقيق النتائج المرجوة؟ استخدمها لإنشاء خط أساس للمقارنة.
  - هندسة المطالبات: جرب تقنيات مثل التوجيه القليل الأمثلة مع أمثلة على استجابات المطالبات ذات الصلة. قيّم جودة الاستجابات.
  - التوليد المعزز بالاسترجاع: جرب تعزيز المطالبات بنتائج استعلامات تم استرجاعها من بياناتك. قيّم جودة الاستجابات.
- **التكاليف**: هل حددت التكاليف المرتبطة بالتحسين الدقيق؟
  - قابلية التعديل - هل النموذج المدرب مسبقًا متاح للتحسين الدقيق؟
  - الجهد - لتحضير بيانات التدريب، وتقييم النموذج وتحسينه.
  - الحوسبة - لتشغيل مهام التحسين الدقيق، ونشر النموذج المحسن.
  - البيانات - الوصول إلى أمثلة ذات جودة كافية لتأثير التحسين الدقيق.
- **الفوائد**: هل أكدت الفوائد من التحسين الدقيق؟
  - الجودة - هل تفوق النموذج المحسن على الخط الأساسي؟
  - التكلفة - هل يقلل من استخدام الرموز عن طريق تبسيط المطالبات؟
  - القابلية للتوسع - هل يمكنك إعادة استخدام النموذج الأساسي لمجالات جديدة؟

بإجابتك على هذه الأسئلة، يجب أن تكون قادرًا على تحديد ما إذا كان التحسين الدقيق هو النهج المناسب لحالة استخدامك. من الناحية المثالية، يكون النهج صالحًا فقط إذا كانت الفوائد تفوق التكاليف. بمجرد أن تقرر المتابعة، حان الوقت للتفكير في _كيفية_ تحسين النموذج المدرب مسبقًا.

هل تريد المزيد من الرؤى حول عملية اتخاذ القرار؟ شاهد [To fine-tune or not to fine-tune](https://www.youtube.com/watch?v=0Jo-z-MFxJs)

## كيف يمكننا تحسين نموذج مدرب مسبقًا؟

لتحسين نموذج مدرب مسبقًا، تحتاج إلى:

- نموذج مدرب مسبقًا لتحسينه
- مجموعة بيانات لاستخدامها في التحسين الدقيق
- بيئة تدريب لتشغيل مهمة التحسين الدقيق
- بيئة استضافة لنشر النموذج المحسن

## التحسين الدقيق في التطبيق

توفر الموارد التالية دروسًا خطوة بخطوة لتوجيهك خلال مثال عملي باستخدام نموذج مختار مع مجموعة بيانات منظمة. للعمل من خلال هذه الدروس، تحتاج إلى حساب لدى المزود المحدد، بالإضافة إلى الوصول إلى النموذج ومجموعات البيانات ذات الصلة.

| المزود       | الدرس                                                                                                                                                                         | الوصف                                                                                                                                                                                                                                                                                                                                                                                                                            |
| ------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| OpenAI       | [كيفية تحسين نماذج الدردشة](https://github.com/openai/openai-cookbook/blob/main/examples/How_to_finetune_chat_models.ipynb?WT.mc_id=academic-105485-koreyst)                | تعلّم كيفية تحسين نموذج `gpt-35-turbo` لمجال محدد ("مساعد الوصفات") عن طريق تحضير بيانات التدريب، تشغيل مهمة التحسين الدقيق، واستخدام النموذج المحسن للاستدلال.                                                                                                                                                                                                                                                              |
| Azure OpenAI | [درس تحسين GPT 3.5 Turbo](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python-new%2Ccommand-line?WT.mc_id=academic-105485-koreyst)           | تعلّم كيفية تحسين نموذج `gpt-35-turbo-0613` **على Azure** من خلال خطوات إنشاء ورفع بيانات التدريب، تشغيل مهمة التحسين الدقيق، نشر واستخدام النموذج الجديد.                                                                                                                                                                                                                                                                  |
| Hugging Face | [تحسين نماذج اللغة الكبيرة مع Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                   | يشرح هذا المقال كيفية تحسين نموذج _مفتوح المصدر_ (مثال: `CodeLlama 7B`) باستخدام مكتبة [transformers](https://huggingface.co/docs/transformers/index?WT.mc_id=academic-105485-koreyst) و[تعلم التعزيز باستخدام المحولات (TRL)](https://huggingface.co/docs/trl/index?WT.mc_id=academic-105485-koreyst) مع مجموعات بيانات مفتوحة على Hugging Face.                                                                                   |
|              |                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| 🤗 AutoTrain | [تحسين نماذج اللغة الكبيرة مع AutoTrain](https://github.com/huggingface/autotrain-advanced/?WT.mc_id=academic-105485-koreyst)                                             | AutoTrain (أو AutoTrain Advanced) هي مكتبة بايثون طورتها Hugging Face تتيح التحسين الدقيق للعديد من المهام المختلفة بما في ذلك تحسين نماذج اللغة الكبيرة. AutoTrain هو حل بدون كود ويمكن إجراء التحسين الدقيق في سحابتك الخاصة، على Hugging Face Spaces أو محليًا. يدعم واجهة مستخدم ويب، واجهة سطر الأوامر، والتدريب عبر ملفات تكوين yaml.                                                                                   |
|              |                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                                                                  |

## الواجب

اختر أحد الدروس أعلاه واطّلع عليه خطوة بخطوة. _قد نقوم بتكرار نسخة من هذه الدروس في دفاتر Jupyter في هذا المستودع للرجوع إليها فقط. يرجى استخدام المصادر الأصلية مباشرة للحصول على أحدث الإصدارات_.

## عمل رائع! واصل تعلمك.

بعد إكمال هذا الدرس، اطلع على مجموعة [تعلم الذكاء الاصطناعي التوليدي](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) لمواصلة تطوير معرفتك في الذكاء الاصطناعي التوليدي!

تهانينا!! لقد أكملت الدرس النهائي من سلسلة الإصدار الثاني لهذا المقرر! لا تتوقف عن التعلم والبناء. \*\*اطلع على صفحة [الموارد](RESOURCES.md?WT.mc_id=academic-105485-koreyst) لقائمة اقتراحات إضافية حول هذا الموضوع فقط.

تم تحديث سلسلة دروس الإصدار الأول أيضًا مع المزيد من الواجبات والمفاهيم. لذا خذ دقيقة لتحديث معلوماتك - ويرجى [مشاركة أسئلتك وتعليقاتك](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) لمساعدتنا في تحسين هذه الدروس للمجتمع.

**إخلاء المسؤولية**:  
تمت ترجمة هذا المستند باستخدام خدمة الترجمة الآلية [Co-op Translator](https://github.com/Azure/co-op-translator). بينما نسعى لتحقيق الدقة، يرجى العلم أن الترجمات الآلية قد تحتوي على أخطاء أو عدم دقة. يجب اعتبار المستند الأصلي بلغته الأصلية المصدر الموثوق به. للمعلومات الهامة، يُنصح بالاعتماد على الترجمة البشرية المهنية. نحن غير مسؤولين عن أي سوء فهم أو تفسير ناتج عن استخدام هذه الترجمة.