<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:54:30+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "ar"
}
-->
# موارد للتعلم الذاتي

تم بناء الدرس باستخدام عدد من الموارد الأساسية من OpenAI و Azure OpenAI كمراجع للمصطلحات والدروس التعليمية. إليك قائمة غير شاملة، لتستخدمها في رحلات التعلم الذاتي الخاصة بك.

## 1. الموارد الأساسية

| العنوان/الرابط                                                                                                                                                                                                                   | الوصف                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | تحسين النماذج (Fine-tuning) يتفوق على التعلم بعدد قليل من الأمثلة من خلال التدريب على عدد أكبر بكثير من الأمثلة التي لا يمكن تضمينها في الموجه، مما يوفر التكاليف، ويحسن جودة الاستجابات، ويسمح بطلبات ذات زمن استجابة أقل. **اطلع على نظرة عامة حول تحسين النماذج من OpenAI.**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | فهم **ما هو تحسين النماذج (المفهوم)**، ولماذا يجب النظر فيه (المشكلة المحفزة)، وما هي البيانات التي يجب استخدامها (للتدريب) وكيفية قياس الجودة                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | خدمة Azure OpenAI تتيح لك تخصيص نماذجنا باستخدام مجموعات البيانات الخاصة بك عبر تحسين النماذج. تعلّم **كيفية تحسين النماذج (العملية)** واختيار النماذج باستخدام Azure AI Studio، Python SDK أو REST API.                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | قد لا تؤدي نماذج اللغة الكبيرة (LLMs) أداءً جيدًا في مجالات أو مهام أو مجموعات بيانات محددة، أو قد تنتج مخرجات غير دقيقة أو مضللة. **متى يجب أن تفكر في تحسين النماذج** كحل محتمل لهذه الحالات؟                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | التحسين المستمر هو عملية تكرارية لاختيار نموذج تم تحسينه مسبقًا كنموذج أساسي و**تحسينه أكثر** باستخدام مجموعات جديدة من أمثلة التدريب.                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | تحسين النموذج **مع أمثلة استدعاء الدوال** يمكن أن يحسن مخرجات النموذج من خلال الحصول على استجابات أكثر دقة واتساقًا - مع ردود متناسقة من حيث التنسيق وتوفير في التكاليف                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | اطلع على هذا الجدول لفهم **أي النماذج يمكن تحسينها** في Azure OpenAI، وفي أي المناطق متاحة. تحقق من حدود الرموز وتواريخ انتهاء صلاحية بيانات التدريب إذا لزم الأمر.                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | هذه الحلقة التي تستغرق 30 دقيقة من برنامج AI Show في **أكتوبر 2023** تناقش الفوائد والعيوب والرؤى العملية التي تساعدك في اتخاذ هذا القرار.                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | هذا المورد من **AI Playbook** يرشدك خلال متطلبات البيانات، التنسيق، ضبط المعاملات الفائقة، والتحديات/القيود التي يجب أن تعرفها.                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | تعلّم كيفية إنشاء مجموعة بيانات نموذجية للتحسين، التحضير للتحسين، إنشاء مهمة تحسين، ونشر النموذج المحسن على Azure.                                                                                                                                                                                    |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | يتيح لك Azure AI Studio تخصيص نماذج اللغة الكبيرة لمجموعات البيانات الخاصة بك _باستخدام واجهة مستخدم مناسبة للمطورين ذوي الخبرة القليلة في البرمجة_. اطلع على هذا المثال.                                                                                                                                                               |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | يصف هذا المقال كيفية تحسين نموذج Hugging Face باستخدام مكتبة Hugging Face transformers على وحدة معالجة رسومات واحدة مع Azure DataBricks ومكتبات Hugging Face Trainer                                                                                                                                                |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | يقدم كتالوج النماذج في Azure Machine Learning العديد من النماذج مفتوحة المصدر التي يمكنك تحسينها لمهمتك الخاصة. جرب هذا الوحدة من [مسار تعلم الذكاء الاصطناعي التوليدي في AzureML](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | يتيح تحسين نماذج GPT-3.5 أو GPT-4 على Microsoft Azure باستخدام W&B تتبعًا وتحليلًا مفصلًا لأداء النموذج. يوسع هذا الدليل المفاهيم من دليل تحسين OpenAI مع خطوات وميزات محددة لـ Azure OpenAI.                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. الموارد الثانوية

تحتوي هذه القسم على موارد إضافية تستحق الاستكشاف، لكن لم يكن لدينا وقت لتغطيتها في هذا الدرس. قد يتم تناولها في درس مستقبلي، أو كخيار واجب ثانوي في وقت لاحق. في الوقت الحالي، استخدمها لبناء خبرتك ومعرفتك حول هذا الموضوع.

| العنوان/الرابط                                                                                                                                                                                                            | الوصف                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [تحضير البيانات وتحليلها لتحسين نموذج الدردشة](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | هذا الدفتر يعمل كأداة لمعالجة وتحليل مجموعة بيانات الدردشة المستخدمة لتحسين نموذج الدردشة. يتحقق من أخطاء التنسيق، يوفر إحصائيات أساسية، ويقدر عدد الرموز لتكاليف التحسين. انظر: [طريقة تحسين gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                                                                   |
| **OpenAI Cookbook**: [تحسين نموذج لاسترجاع المعلومات المعزز (RAG) مع Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | يهدف هذا الدفتر إلى تقديم مثال شامل لكيفية تحسين نماذج OpenAI لتوليد المعلومات المعزز بالاسترجاع (RAG). سنقوم أيضًا بدمج Qdrant والتعلم بعدد قليل من الأمثلة لتعزيز أداء النموذج وتقليل الأخطاء.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [تحسين GPT باستخدام Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) هي منصة مطوري الذكاء الاصطناعي، توفر أدوات لتدريب النماذج، تحسين النماذج، والاستفادة من النماذج الأساسية. اقرأ أولاً دليلهم [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst)، ثم جرب تمرين Cookbook.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - تحسين نماذج اللغة الصغيرة                                                   | تعرّف على [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst)، نموذج مايكروسوفت الصغير الجديد، قوي بشكل مدهش ومضغوط. سيرشدك هذا الدرس خلال تحسين Phi-2، موضحًا كيفية بناء مجموعة بيانات فريدة وتحسين النموذج باستخدام QLoRA.                                                                                                                                                                       |
| **Hugging Face Tutorial** [كيفية تحسين نماذج اللغة الكبيرة في 2024 مع Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | يشرح هذا المقال كيفية تحسين نماذج اللغة الكبيرة المفتوحة باستخدام Hugging Face TRL، Transformers ومجموعات البيانات في 2024. تحدد حالة استخدام، تجهز بيئة التطوير، تحضر مجموعة البيانات، تحسن النموذج، تختبره وتقيمه، ثم تنشره للإنتاج.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | يوفر تدريبًا أسرع وأسهل ونشرًا لنماذج التعلم الآلي المتقدمة [state-of-the-art machine learning models](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). يحتوي المستودع على دروس صديقة لـ Colab مع إرشادات فيديو على YouTube، لتحسين النماذج. **يعكس التحديث الأخير [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)**. اقرأ [توثيق AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**إخلاء المسؤولية**:  
تمت ترجمة هذا المستند باستخدام خدمة الترجمة الآلية [Co-op Translator](https://github.com/Azure/co-op-translator). بينما نسعى لتحقيق الدقة، يرجى العلم أن الترجمات الآلية قد تحتوي على أخطاء أو عدم دقة. يجب اعتبار المستند الأصلي بلغته الأصلية المصدر الموثوق به. للمعلومات الهامة، يُنصح بالاعتماد على الترجمة البشرية المهنية. نحن غير مسؤولين عن أي سوء فهم أو تفسير ناتج عن استخدام هذه الترجمة.