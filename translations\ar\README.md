<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:18:52+00:00",
  "source_file": "README.md",
  "language_code": "ar"
}
-->
![الذكاء الاصطناعي التوليدي للمبتدئين](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.ar.png)

### 21 درسًا تعلمك كل ما تحتاجه لبدء بناء تطبيقات الذكاء الاصطناعي التوليدي

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 دعم متعدد اللغات

#### مدعوم عبر GitHub Action (آلي ومحدث دائمًا)

[الفرنسية](../fr/README.md) | [الإسبانية](../es/README.md) | [الألمانية](../de/README.md) | [الروسية](../ru/README.md) | [العربية](./README.md) | [الفارسية](../fa/README.md) | [الأردية](../ur/README.md) | [الصينية المبسطة](../zh/README.md) | [الصينية التقليدية، ماكاو](../mo/README.md) | [الصينية التقليدية، هونغ كونغ](../hk/README.md) | [الصينية التقليدية، تايوان](../tw/README.md) | [اليابانية](../ja/README.md) | [الكورية](../ko/README.md) | [الهندية](../hi/README.md) | [البنغالية](../bn/README.md) | [الماراثية](../mr/README.md) | [النيبالية](../ne/README.md) | [البنجابية (غورموخي)](../pa/README.md) | [البرتغالية (البرتغال)](../pt/README.md) | [البرتغالية (البرازيل)](../br/README.md) | [الإيطالية](../it/README.md) | [البولندية](../pl/README.md) | [التركية](../tr/README.md) | [اليونانية](../el/README.md) | [التايلاندية](../th/README.md) | [السويدية](../sv/README.md) | [الدنماركية](../da/README.md) | [النرويجية](../no/README.md) | [الفنلندية](../fi/README.md) | [الهولندية](../nl/README.md) | [العبرية](../he/README.md) | [الفيتنامية](../vi/README.md) | [الإندونيسية](../id/README.md) | [الماليزية](../ms/README.md) | [التاغالوغ (الفلبينية)](../tl/README.md) | [السواحلية](../sw/README.md) | [الهنغارية](../hu/README.md) | [التشيكية](../cs/README.md) | [السلوفاكية](../sk/README.md) | [الرومانية](../ro/README.md) | [البلغارية](../bg/README.md) | [الصربية (السيريلية)](../sr/README.md) | [الكرواتية](../hr/README.md) | [السلوفينية](../sl/README.md) | [الأوكرانية](../uk/README.md) | [البورمية (ميانمار)](../my/README.md)

# الذكاء الاصطناعي التوليدي للمبتدئين (الإصدار 3) - دورة تعليمية

تعلم أساسيات بناء تطبيقات الذكاء الاصطناعي التوليدي من خلال دورتنا الشاملة التي تتضمن 21 درسًا مقدمة من Microsoft Cloud Advocates.

## 🌱 البداية

تتضمن هذه الدورة 21 درسًا. كل درس يغطي موضوعًا خاصًا به، لذا يمكنك البدء من أي مكان تريده!

الدروس مصنفة إلى دروس "تعلم" تشرح مفهومًا في الذكاء الاصطناعي التوليدي، أو دروس "بناء" تشرح المفهوم مع أمثلة برمجية بلغة **Python** و **TypeScript** حيثما أمكن.

لمطوري .NET، اطلع على [الذكاء الاصطناعي التوليدي للمبتدئين (إصدار .NET)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

كل درس يتضمن أيضًا قسم "استمر في التعلم" مع أدوات تعلم إضافية.

## ما تحتاجه
### لتشغيل أكواد هذه الدورة، يمكنك استخدام أي من:
 - [خدمة Azure OpenAI](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **الدروس:** "aoai-assignment"
 - [كتالوج نماذج GitHub Marketplace](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **الدروس:** "githubmodels"
 - [واجهة برمجة تطبيقات OpenAI](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **الدروس:** "oai-assignment" 
   
- معرفة أساسية بـ Python أو TypeScript مفيدة - \*للمبتدئين تمامًا، اطلع على هذه الدورات [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) و [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst)
- حساب GitHub لـ [نسخ هذا المستودع بالكامل](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) إلى حسابك الخاص على GitHub

أنشأنا درس **[إعداد الدورة](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** لمساعدتك في تجهيز بيئة التطوير الخاصة بك.

لا تنسَ [وضع نجمة (🌟) على هذا المستودع](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst) لتسهيل العثور عليه لاحقًا.

## 🧠 هل أنت مستعد للنشر؟

إذا كنت تبحث عن أمثلة برمجية أكثر تقدمًا، اطلع على [مجموعتنا من أمثلة كود الذكاء الاصطناعي التوليدي](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) بكل من **Python** و **TypeScript**.

## 🗣️ تواصل مع متعلمين آخرين واحصل على الدعم

انضم إلى [خادم Discord الرسمي لـ Azure AI Foundry](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) لتلتقي وتتواصل مع متعلمين آخرين يأخذون هذه الدورة وللحصول على الدعم.

اطرح أسئلة أو شارك ملاحظاتك حول المنتج في [منتدى مطوري Azure AI Foundry](https://aka.ms/azureaifoundry/forum) على GitHub.

## 🚀 تبني شركة ناشئة؟

سجل في [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) لتحصل على **رصيد مجاني من OpenAI** وحتى **150 ألف دولار كرصيد على Azure للوصول إلى نماذج OpenAI عبر خدمات Azure OpenAI**.

## 🙏 هل تريد المساعدة؟

هل لديك اقتراحات أو وجدت أخطاء إملائية أو برمجية؟ [افتح مشكلة](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) أو [أنشئ طلب سحب](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 كل درس يتضمن:

- مقدمة فيديو قصيرة عن الموضوع
- درس مكتوب موجود في README
- أمثلة برمجية بـ Python و TypeScript تدعم Azure OpenAI و OpenAI API
- روابط لمصادر إضافية لمواصلة التعلم

## 🗃️ الدروس

| #   | **رابط الدرس**                                                                                                                              | **الوصف**                                                                                     | **الفيديو**                                                                 | **تعلم إضافي**                                                                 |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [إعداد الدورة](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **تعلم:** كيفية إعداد بيئة التطوير الخاصة بك                                                | الفيديو قادم قريبًا                                                           | [تعرف أكثر](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [مقدمة في الذكاء الاصطناعي التوليدي ونماذج اللغة الكبيرة](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)              | **تعلم:** فهم ماهية الذكاء الاصطناعي التوليدي وكيف تعمل نماذج اللغة الكبيرة (LLMs)           | [فيديو](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [تعرف أكثر](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [استكشاف ومقارنة نماذج اللغة الكبيرة المختلفة](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)         | **تعلم:** كيفية اختيار النموذج المناسب لحالة الاستخدام الخاصة بك                            | [فيديو](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [تعرف أكثر](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [استخدام الذكاء الاصطناعي التوليدي بمسؤولية](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                 | **تعلم:** كيفية بناء تطبيقات الذكاء الاصطناعي التوليدي بمسؤولية                              | [فيديو](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [تعرف أكثر](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [فهم أساسيات هندسة المطالبات](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)                                 | **تعلم:** أفضل الممارسات العملية لهندسة المطالبات                                          | [فيديو](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [تعرف أكثر](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [إنشاء مطالبات متقدمة](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                      | **تعلم:** كيفية تطبيق تقنيات هندسة المطالبات التي تحسن نتائج مطالباتك                        | [فيديو](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [تعرف أكثر](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [بناء تطبيقات توليد النصوص](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **بناء:** تطبيق لتوليد النصوص باستخدام Azure OpenAI / OpenAI API                                | [فيديو](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [بناء تطبيقات الدردشة](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **بناء:** تقنيات لبناء ودمج تطبيقات الدردشة بكفاءة.               | [فيديو](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [بناء تطبيقات البحث وقواعد بيانات المتجهات](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **بناء:** تطبيق بحث يستخدم Embeddings للبحث عن البيانات.                        | [فيديو](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [بناء تطبيقات توليد الصور](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **بناء:** تطبيق لتوليد الصور                                                       | [فيديو](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [بناء تطبيقات الذكاء الاصطناعي منخفضة الكود](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **بناء:** تطبيق ذكاء اصطناعي توليدي باستخدام أدوات منخفضة الكود                                     | [فيديو](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [دمج التطبيقات الخارجية مع Function Calling](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **بناء:** ما هو function calling وحالات استخدامه في التطبيقات                          | [فيديو](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [تصميم تجربة المستخدم لتطبيقات الذكاء الاصطناعي](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **تعلم:** كيفية تطبيق مبادئ تصميم تجربة المستخدم عند تطوير تطبيقات الذكاء الاصطناعي التوليدي         | [فيديو](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [تأمين تطبيقات الذكاء الاصطناعي التوليدي](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **تعلم:** التهديدات والمخاطر التي تواجه أنظمة الذكاء الاصطناعي وطرق تأمينها.             | [فيديو](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [دورة حياة تطبيقات الذكاء الاصطناعي التوليدي](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **تعلم:** الأدوات والمؤشرات لإدارة دورة حياة LLM وعمليات LLMOps                         | [فيديو](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [التوليد المعزز بالاسترجاع (RAG) وقواعد بيانات المتجهات](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **بناء:** تطبيق يستخدم إطار عمل RAG لاسترجاع embeddings من قواعد بيانات المتجهات  | [فيديو](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [النماذج مفتوحة المصدر وHugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **بناء:** تطبيق يستخدم نماذج مفتوحة المصدر المتوفرة على Hugging Face                    | [فيديو](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [وكلاء الذكاء الاصطناعي](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **بناء:** تطبيق يستخدم إطار عمل وكيل الذكاء الاصطناعي                                           | [فيديو](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [ضبط النماذج اللغوية الكبيرة (LLMs)](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **تعلم:** ما هو ضبط النماذج اللغوية الكبيرة، ولماذا وكيف يتم ذلك                                            | [فيديو](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [البناء باستخدام النماذج اللغوية الصغيرة (SLMs)](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **تعلم:** فوائد البناء باستخدام النماذج اللغوية الصغيرة                                            | سيتم إصدار الفيديو قريبًا | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [البناء باستخدام نماذج Mistral](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **تعلم:** ميزات واختلافات نماذج عائلة Mistral                                           | سيتم إصدار الفيديو قريبًا | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [البناء باستخدام نماذج Meta](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **تعلم:** ميزات واختلافات نماذج عائلة Meta                                           | سيتم إصدار الفيديو قريبًا | [تعرف على المزيد](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 شكر خاص

شكر خاص لـ [**John Aziz**](https://www.linkedin.com/in/john0isaac/) على إنشاء جميع GitHub Actions وسير العمل

[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) على مساهماته الرئيسية في كل درس لتحسين تجربة المتعلم والكود.

## 🎒 دورات أخرى

فريقنا يقدم دورات أخرى! اطلع على:

- [**جديد** بروتوكول سياق النموذج للمبتدئين](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [وكلاء الذكاء الاصطناعي للمبتدئين](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [الذكاء الاصطناعي التوليدي للمبتدئين باستخدام .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [الذكاء الاصطناعي التوليدي للمبتدئين باستخدام JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [تعلم الآلة للمبتدئين](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [علوم البيانات للمبتدئين](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [الذكاء الاصطناعي للمبتدئين](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [الأمن السيبراني للمبتدئين](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [تطوير الويب للمبتدئين](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [إنترنت الأشياء للمبتدئين](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [تطوير XR للمبتدئين](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [إتقان GitHub Copilot للبرمجة التشاركية بالذكاء الاصطناعي](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [إتقان GitHub Copilot لمطوري C#/.NET](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [اختر مغامرتك مع Copilot](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**إخلاء مسؤولية**:  
تمت ترجمة هذا المستند باستخدام خدمة الترجمة الآلية [Co-op Translator](https://github.com/Azure/co-op-translator). بينما نسعى لتحقيق الدقة، يرجى العلم أن الترجمات الآلية قد تحتوي على أخطاء أو عدم دقة. يجب اعتبار المستند الأصلي بلغته الأصلية المصدر الموثوق به. للمعلومات الهامة، يُنصح بالاعتماد على الترجمة البشرية المهنية. نحن غير مسؤولين عن أي سوء فهم أو تفسير ناتج عن استخدام هذه الترجمة.