<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:37:27+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "bg"
}
-->
# Neural Network Frameworks

Както вече научихме, за да можем ефективно да тренираме невронни мрежи, трябва да направим две неща:

* Да оперираме с тензори, например да ги умножаваме, събираме и да изчисляваме функции като sigmoid или softmax
* Да изчисляваме градиенти на всички изрази, за да можем да извършваме оптимизация чрез градиентен спуск



Докато библиотеката `numpy` може да се справи с първата част, ни е необходим механизъм за изчисляване на градиенти. В нашата рамка, която разработихме в предишния раздел, трябваше ръчно да програмираме всички производни във функцията `backward`, която извършва backpropagation. Идеално би било рамката да ни даде възможност да изчисляваме градиенти на *всякакъв израз*, който можем да дефинираме.

Друго важно нещо е да можем да извършваме изчисления на GPU или други специализирани изчислителни устройства, като TPU. Обучението на дълбоки невронни мрежи изисква *много* изчисления, и възможността за паралелизиране на тези изчисления на GPU е изключително важна.

> ✅ Терминът 'паралелизиране' означава разпределяне на изчисленията върху множество устройства.

В момента двата най-популярни невронни фреймуърка са: TensorFlow и PyTorch. И двата предоставят ниско ниво API за работа с тензори както на CPU, така и на GPU. Върху ниско ниво API има и по-високо ниво API, наречени съответно Keras и PyTorch Lightning.

Ниско ниво API | TensorFlow| PyTorch
--------------|-------------------------------------|--------------------------------
Високо ниво API| Keras| PyTorch

**Ниско ниво API** и в двата фреймуърка ви позволява да изграждате т.нар. **изчислителни графи**. Този граф дефинира как да се изчисли изходът (обикновено функцията на загуба) с дадени входни параметри и може да бъде изпратен за изчисление на GPU, ако е наличен. Съществуват функции за диференциране на този изчислителен граф и изчисляване на градиенти, които след това могат да се използват за оптимизиране на параметрите на модела.

**Високо ниво API** разглежда невронните мрежи като **последователност от слоеве** и улеснява значително изграждането на повечето невронни мрежи. Обучението на модела обикновено изисква подготовка на данните и след това извикване на функция `fit`, която върши работата.

Високо ниво API ви позволява бързо да конструирате типични невронни мрежи, без да се притеснявате за много детайли. В същото време ниско ниво API предлага много по-голям контрол върху процеса на обучение и затова се използва често в изследвания, когато работите с нови архитектури на невронни мрежи.

Също така е важно да разберете, че можете да използвате и двата API заедно, например да разработите собствена архитектура на слой с ниско ниво API и след това да я използвате в по-голяма мрежа, конструирана и обучавана с високо ниво API. Или можете да дефинирате мрежа с високо ниво API като последователност от слоеве и след това да използвате собствен цикъл за обучение с ниско ниво API за оптимизация. И двата API използват едни и същи основни концепции и са проектирани да работят добре заедно.

## Обучение

В този курс предлагаме повечето съдържание както за PyTorch, така и за TensorFlow. Можете да изберете предпочитания от вас фреймуърк и да преминете само през съответните тетрадки. Ако не сте сигурни кой фреймуърк да изберете, прочетете някои дискусии в интернет относно **PyTorch срещу TensorFlow**. Можете също така да разгледате и двата фреймуърка, за да добиете по-добро разбиране.

Където е възможно, ще използваме високо ниво API за по-голяма простота. Въпреки това, смятаме, че е важно да разберете как работят невронните мрежи от основата нагоре, затова в началото започваме с работа с ниско ниво API и тензори. Ако обаче искате да започнете бързо и не желаете да отделяте много време за изучаване на тези детайли, можете да ги пропуснете и да преминете директно към тетрадките с високо ниво API.

## ✍️ Упражнения: Фреймуъркове

Продължете обучението си в следните тетрадки:

Ниско ниво API | TensorFlow+Keras Notebook | PyTorch
--------------|-------------------------------------|--------------------------------
Високо ниво API| Keras | *PyTorch Lightning*

След като усвоите фреймуърковете, нека преговорим понятието overfitting.

# Overfitting

Overfitting е изключително важна концепция в машинното обучение и е много важно да я разберем правилно!

Разгледайте следния проблем с апроксимация на 5 точки (представени с `x` на графиките по-долу):

!linear | overfit
-------------------------|--------------------------
**Линеен модел, 2 параметъра** | **Нелинеен модел, 7 параметъра**
Грешка при обучение = 5.3 | Грешка при обучение = 0
Грешка при валидация = 5.1 | Грешка при валидация = 20

* Вляво виждаме добра линейна апроксимация. Тъй като броят на параметрите е адекватен, моделът улавя правилно разпределението на точките.
* Вдясно моделът е твърде мощен. Тъй като имаме само 5 точки, а моделът има 7 параметъра, той може да се нагоди така, че да премине през всички точки, правейки грешката при обучение равна на 0. Това обаче пречи на модела да разбере правилния модел зад данните, затова грешката при валидация е много висока.

Много е важно да се намери правилен баланс между сложността на модела (брой параметри) и броя на тренировъчните примери.

## Защо се получава overfitting

  * Недостатъчно тренировъчни данни
  * Твърде мощен модел
  * Твърде много шум в входните данни

## Как да разпознаем overfitting

Както се вижда от графиката по-горе, overfitting може да се разпознае по много ниска грешка при обучение и висока грешка при валидация. Обикновено по време на обучението и двете грешки започват да намаляват, но в някакъв момент грешката при валидация може да спре да намалява и да започне да се увеличава. Това е знак за overfitting и индикация, че вероятно трябва да спрем обучението в този момент (или поне да направим снимка на модела).

overfitting

## Как да предотвратим overfitting

Ако забележите, че се получава overfitting, можете да направите едно от следните неща:

 * Увеличете количеството тренировъчни данни
 * Намалете сложността на модела
 * Използвайте някаква техника за регуляризация, като Dropout, която ще разгледаме по-късно.

## Overfitting и Bias-Variance Tradeoff

Overfitting всъщност е частен случай на по-общ проблем в статистиката, наречен Bias-Variance Tradeoff. Ако разгледаме възможните източници на грешка в нашия модел, можем да видим два вида грешки:

* **Bias грешки** се причиняват от това, че алгоритъмът ни не може правилно да улови връзката между тренировъчните данни. Това може да се дължи на недостатъчна мощност на модела (**underfitting**).
* **Variance грешки**, които се дължат на това, че моделът апроксимира шума във входните данни, вместо смислената връзка (**overfitting**).

По време на обучението bias грешката намалява (тъй като моделът се учи да апроксимира данните), а variance грешката се увеличава. Важно е да спрем обучението – или ръчно (когато засечем overfitting), или автоматично (чрез въвеждане на регуляризация) – за да предотвратим overfitting.

## Заключение

В този урок научихте за разликите между различните API-та на двата най-популярни AI фреймуърка, TensorFlow и PyTorch. Освен това научихте и за много важната тема overfitting.

## 🚀 Предизвикателство

В придружаващите тетрадки ще намерите „задачи“ в края; преминете през тетрадките и изпълнете задачите.

## Преглед и самостоятелно обучение

Направете проучване по следните теми:

- TensorFlow
- PyTorch
- Overfitting

Задайте си следните въпроси:

- Каква е разликата между TensorFlow и PyTorch?
- Каква е разликата между overfitting и underfitting?

## Задача

В тази лаборатория трябва да решите два класификационни проблема, използвайки еднослойни и многостепенни напълно свързани мрежи с PyTorch или TensorFlow.

**Отказ от отговорност**:  
Този документ е преведен с помощта на AI преводаческа услуга [Co-op Translator](https://github.com/Azure/co-op-translator). Въпреки че се стремим към точност, моля, имайте предвид, че автоматизираните преводи могат да съдържат грешки или неточности. Оригиналният документ на неговия роден език трябва да се счита за авторитетен източник. За критична информация се препоръчва професионален човешки превод. Ние не носим отговорност за каквито и да е недоразумения или неправилни тълкувания, произтичащи от използването на този превод.