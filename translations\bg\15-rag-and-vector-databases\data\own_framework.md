<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:51:21+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "bg"
}
-->
# Въведение в невронните мрежи. Многослоен перцептрон

В предишния раздел научихте за най-простия модел на невронна мрежа - еднослоен перцептрон, линеен модел за двукласова класификация.

В този раздел ще разширим този модел до по-гъвкава рамка, която ни позволява да:

* извършваме **многокласова класификация** освен двукласова
* решаваме **регресионни задачи** освен класификация
* разделяме класове, които не са линейно разделими

Ще разработим и собствен модулен фреймуърк на Python, който ще ни позволи да изграждаме различни архитектури на невронни мрежи.

## Формализиране на машинното обучение

Нека започнем с формализиране на задачата машинно обучение. Да предположим, че имаме тренировъчен набор от данни **X** с етикети **Y**, и трябва да създадем модел *f*, който да прави възможно най-точни прогнози. Качеството на прогнозите се измерва чрез **функция на загубите** ℒ. Често използваните функции на загубите са:

* За регресионни задачи, когато трябва да предскажем число, можем да използваме **абсолютна грешка** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>| или **квадратична грешка** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup>
* За класификация използваме **0-1 загуба** (която всъщност е същата като **точността** на модела) или **логистична загуба**.

За еднослоен перцептрон функцията *f* беше дефинирана като линейна функция *f(x)=wx+b* (тук *w* е матрицата с тегла, *x* е векторът с входни характеристики, а *b* е векторът с изместване). За различни архитектури на невронни мрежи тази функция може да има по-сложна форма.

> В случай на класификация често е желателно да получим вероятности за съответните класове като изход на мрежата. За да преобразуваме произволни числа в вероятности (например за нормализиране на изхода), често използваме **softmax** функцията σ, и функцията *f* става *f(x)=σ(wx+b)*

В горната дефиниция на *f*, *w* и *b* се наричат **параметри** θ=⟨*w,b*⟩. Като имаме набора от данни ⟨**X**,**Y**⟩, можем да изчислим общата грешка върху целия набор като функция на параметрите θ.

> ✅ **Целта на обучението на невронната мрежа е да минимизира грешката чрез промяна на параметрите θ**

## Оптимизация чрез градиентен спуск

Съществува добре познат метод за оптимизация на функции, наречен **градиентен спуск**. Идеята е, че можем да изчислим производната (в многомерния случай наречена **градиент**) на функцията на загубите спрямо параметрите и да променяме параметрите така, че грешката да намалява. Това може да се формализира по следния начин:

* Инициализираме параметрите с някакви случайни стойности w<sup>(0)</sup>, b<sup>(0)</sup>
* Повтаряме следващата стъпка многократно:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

По време на обучението стъпките на оптимизация трябва да се изчисляват, като се вземе предвид целият набор от данни (помнете, че загубата се изчислява като сума през всички тренировъчни примери). В реалния живот обаче взимаме малки части от набора, наречени **минибатчове**, и изчисляваме градиентите на базата на подмножество от данни. Тъй като подмножеството се избира случайно всеки път, този метод се нарича **стохастичен градиентен спуск** (SGD).

## Многослойни перцептрони и обратно разпространение

Еднослойната мрежа, както видяхме по-горе, може да класифицира линейно разделими класове. За да изградим по-богат модел, можем да комбинираме няколко слоя на мрежата. Математически това означава, че функцията *f* ще има по-сложна форма и ще се изчислява на няколко стъпки:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

Тук α е **нелинейна активационна функция**, σ е softmax функция, а параметрите са θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*>.

Алгоритъмът на градиентния спуск остава същият, но изчисляването на градиентите става по-сложно. С помощта на правилото за диференциране на съставни функции можем да изчислим производните като:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ Правилото за диференциране на съставни функции се използва за изчисляване на производните на функцията на загубите спрямо параметрите.

Обърнете внимание, че най-лявата част на всички тези изрази е една и съща, затова можем ефективно да изчисляваме производните, започвайки от функцията на загубите и вървейки "назад" през изчислителния граф. Затова методът за обучение на многослоен перцептрон се нарича **обратно разпространение**, или 'backprop'.



> TODO: image citation

> ✅ Ще разгледаме обратно разпространение много по-подробно в примера в нашия ноутбук.

## Заключение

В този урок изградихме собствена библиотека за невронни мрежи и я използвахме за проста двуизмерна задача за класификация.

## 🚀 Предизвикателство

В придружаващия ноутбук ще реализирате собствен фреймуърк за изграждане и обучение на многослойни перцептрони. Ще можете да видите подробно как работят съвременните невронни мрежи.

Продължете към ноутбука OwnFramework и го преминете.

## Преговор и самостоятелно обучение

Обратно разпространение е често използван алгоритъм в AI и ML, който си заслужава да бъде изучен по-подробно.

## Задача

В тази лабораторна работа трябва да използвате фреймуърка, който сте изградили в този урок, за да решите задачата за класификация на ръкописни цифри MNIST.

* Инструкции
* Ноутбук

**Отказ от отговорност**:  
Този документ е преведен с помощта на AI преводаческа услуга [Co-op Translator](https://github.com/Azure/co-op-translator). Въпреки че се стремим към точност, моля, имайте предвид, че автоматизираните преводи могат да съдържат грешки или неточности. Оригиналният документ на неговия роден език трябва да се счита за авторитетен източник. За критична информация се препоръчва професионален човешки превод. Ние не носим отговорност за каквито и да е недоразумения или неправилни тълкувания, произтичащи от използването на този превод.