<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T17:01:14+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "bg"
}
-->
# Въведение в невронните мрежи: Перцептрон

Един от първите опити за реализиране на нещо подобно на съвременна невронна мрежа беше направен от Франк Розенблат от Cornell Aeronautical Laboratory през 1957 г. Това беше хардуерна реализация, наречена "Mark-1", предназначена да разпознава примитивни геометрични фигури като триъгълници, квадрати и кръгове.

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='Frank Rosenblatt'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> Изображения от Wikipedia

Входното изображение беше представено чрез масив от 20x20 фотоклетки, така че невронната мрежа имаше 400 входа и един двоичен изход. Простата мрежа съдържаше един неврон, наричан още **прагова логическа единица**. Теглата на невронната мрежа действаха като потенциометри, които изискваха ръчна настройка по време на фазата на обучение.

> ✅ Потенциометър е устройство, което позволява на потребителя да регулира съпротивлението в електрическа верига.

> The New York Times пише за перцептрона по това време: *зародиш на електронен компютър, който [Военноморският флот] очаква да може да ходи, говори, вижда, пише, възпроизвежда себе си и да е съзнателен за своето съществуване.*

## Модел на перцептрона

Нека имаме N характеристики в нашия модел, в този случай входният вектор ще бъде вектор с размер N. Перцептронът е модел за **двоична класификация**, т.е. може да различава между два класа входни данни. Ще приемем, че за всеки входен вектор x изходът на нашия перцептрон ще бъде или +1, или -1, в зависимост от класа. Изходът се изчислява по формулата:

y(x) = f(w<sup>T</sup>x)

където f е стъпкова активационна функция

## Обучение на перцептрона

За да обучим перцептрон, трябва да намерим вектор на теглата w, който класифицира повечето стойности правилно, т.е. води до най-малка **грешка**. Тази грешка се дефинира чрез **критерия на перцептрона** по следния начин:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

където:

* сумата се взема върху тези тренировъчни данни i, които водят до неправилна класификация
* x<sub>i</sub> е входният данни, а t<sub>i</sub> е или -1, или +1 за отрицателни и положителни примери съответно.

Този критерий се разглежда като функция на теглата w и трябва да го минимизираме. Често се използва метод, наречен **градиентен спуск**, при който започваме с някакви начални тегла w<sup>(0)</sup>, и след това на всяка стъпка актуализираме теглата според формулата:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

Тук η е т.нар. **скорост на обучение**, а ∇E(w) означава **градиент** на E. След като изчислим градиента, получаваме

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

Алгоритъмът на Python изглежда така:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## Заключение

В този урок научихте за перцептрона, който е модел за двоична класификация, и как да го обучите, използвайки вектор на теглата.

## 🚀 Предизвикателство

Ако искате да опитате да изградите собствен перцептрон, опитайте този лабораторен курс в Microsoft Learn, който използва Azure ML designer


## Преглед и самостоятелно обучение

За да видите как можем да използваме перцептрона за решаване на играчков проблем, както и реални задачи, и да продължите да учите - посетете Perceptron notebook.

Ето и интересна статия за перцептроните.

## Задача

В този урок реализирахме перцептрон за задача за двоична класификация и го използвахме за класифициране между две ръкописни цифри. В тази лабораторна работа трябва да решите задачата за класификация на цифри изцяло, т.е. да определите коя цифра най-вероятно съответства на дадено изображение.

* Инструкции
* Notebook

**Отказ от отговорност**:  
Този документ е преведен с помощта на AI преводаческа услуга [Co-op Translator](https://github.com/Azure/co-op-translator). Въпреки че се стремим към точност, моля, имайте предвид, че автоматизираните преводи могат да съдържат грешки или неточности. Оригиналният документ на неговия роден език трябва да се счита за авторитетен източник. За критична информация се препоръчва професионален човешки превод. Ние не носим отговорност за каквито и да е недоразумения или неправилни тълкувания, произтичащи от използването на този превод.