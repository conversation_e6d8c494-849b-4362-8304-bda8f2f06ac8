<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:09:00+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "bg"
}
-->
# Ресурси за самостоятелно обучение

Урокът е създаден с помощта на няколко основни ресурса от OpenAI и Azure OpenAI като референции за терминологията и уроците. Ето един непълен списък, който може да използвате за вашето самостоятелно обучение.

## 1. Основни ресурси

| Заглавие/Линк                                                                                                                                                                                                                 | Описание                                                                                                                                                                                                                                                                                                                                                                                     |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Фината настройка подобрява обучението с малко примери, като тренира върху много повече примери, отколкото могат да се поберат в подсказката, което ви спестява разходи, подобрява качеството на отговорите и позволява по-ниска латентност на заявките. **Вижте общ преглед на фината настройка от OpenAI.**                                                                                      |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Разберете **какво представлява фината настройка (концепция)**, защо да я разгледате (мотивация), какви данни да използвате (обучение) и как да измервате качеството.                                                                                                                                                               |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service ви позволява да персонализирате моделите според вашите лични набори от данни чрез фина настройка. Научете **как да извършите фината настройка (процес)**, като изберете модели чрез Azure AI Studio, Python SDK или REST API.                                                                                                                                            |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | Големите езикови модели (LLM) може да не се представят добре в специфични области, задачи или набори от данни, или да генерират неточни или подвеждащи резултати. **Кога трябва да обмислите фината настройка** като възможно решение?                                                                                                                                                        |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Непрекъснатата фина настройка е итеративен процес, при който вече финно настроен модел се използва като базов модел и **се настройва допълнително** върху нови набори от тренировъчни примери.                                                                                                                                       |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Фината настройка на вашия модел **с примери за извикване на функции** може да подобри изхода на модела, като осигури по-точни и последователни отговори - с подобен формат и спестяване на разходи.                                                                                                                                   |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Вижте тази таблица, за да разберете **кои модели могат да бъдат финно настроени** в Azure OpenAI и в кои региони са налични. Проверете техните лимити за токени и срокове на валидност на тренировъчните данни, ако е необходимо.                                                                                                      |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Този 30-минутен епизод от AI Show от октомври 2023 г. разглежда ползите, недостатъците и практическите прозрения, които ще ви помогнат да вземете това решение.                                                                                                                                                                   |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Този ресурс от AI Playbook ви превежда през изискванията за данни, форматиране, настройка на хиперпараметри и предизвикателствата/ограниченията, които трябва да знаете.                                                                                                                                                            |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Научете как да създадете примерен набор от данни за фина настройка, да се подготвите за финна настройка, да създадете задача за финна настройка и да разположите финно настроения модел в Azure.                                                                                                                                   |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio ви позволява да персонализирате големи езикови модели спрямо вашите лични набори от данни _чрез UI-базиран работен процес, подходящ за разработчици с малко код_. Вижте този пример.                                                                                                                                  |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Тази статия описва как да финно настроите модел на Hugging Face с библиотеката Hugging Face transformers на един GPU с помощта на Azure DataBricks и Hugging Face Trainer библиотеки.                                                                                                                                               |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Каталогът с модели в Azure Machine Learning предлага много отворени модели, които можете да финно настроите за вашата конкретна задача. Опитайте този модул от [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst).                                                        |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Финната настройка на GPT-3.5 или GPT-4 модели в Microsoft Azure с помощта на W&B позволява детайлно проследяване и анализ на представянето на модела. Това ръководство разширява концепциите от OpenAI Fine-Tuning с конкретни стъпки и функции за Azure OpenAI.                                                                                                                             |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                             |

## 2. Допълнителни ресурси

Този раздел съдържа допълнителни ресурси, които си заслужава да разгледате, но за които не ни остана време в този урок. Те може да бъдат включени в бъдещ урок или като опция за допълнително задание по-късно. За момента ги използвайте, за да развиете собствените си знания и умения по темата.

| Заглавие/Линк                                                                                                                                                                                                            | Описание                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Подготовка и анализ на данни за фина настройка на чат модел](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | Този ноутбук служи като инструмент за предварителна обработка и анализ на чат набора от данни, използван за фина настройка на чат модел. Проверява за грешки във формата, предоставя основна статистика и оценява броя на токените за разходите по финна настройка. Вижте: [Метод за фина настройка на gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [Фина настройка за Retrieval Augmented Generation (RAG) с Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | Целта на този ноутбук е да ви преведе през цялостен пример как да финно настроите OpenAI модели за Retrieval Augmented Generation (RAG). Ще интегрираме и Qdrant и Few-Shot Learning, за да подобрим представянето на модела и да намалим грешките.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Фина настройка на GPT с Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) е платформа за AI разработчици с инструменти за обучение на модели, фина настройка и използване на основни модели. Прочетете първо тяхното ръководство за [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst), след което опитайте упражнението от Cookbook.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - фина настройка за малки езикови модели                                                   | Запознайте се с [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), новия малък модел на Microsoft, изненадващо мощен и компактен. Този урок ще ви преведе през фината настройка на Phi-2, показвайки как да създадете уникален набор от данни и да настроите модела с помощта на QLoRA.                                                                                                                                                                       |
| **Hugging Face Tutorial** [Как да финно настроим LLM през 2024 с Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Този блог пост ви показва как да финно настроите отворени големи езикови модели с Hugging Face TRL, Transformers и datasets през 2024 г. Определяте случай на употреба, настройвате среда за разработка, подготвяте набор от данни, финно настройвате модела, тествате и оценявате, след което го внедрявате в продукция.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Предлага по-бързо и лесно обучение и внедряване на [най-съвременни модели за машинно обучение](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Репозиторият съдържа Colab-приятелски уроци с видео ръководства в YouTube за фина настройка. **Отразява последната [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) актуализация**. Прочетете [документацията на AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst). |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Отказ от отговорност**:  
Този документ е преведен с помощта на AI преводаческа услуга [Co-op Translator](https://github.com/Azure/co-op-translator). Въпреки че се стремим към точност, моля, имайте предвид, че автоматизираните преводи могат да съдържат грешки или неточности. Оригиналният документ на неговия роден език трябва да се счита за авторитетен източник. За критична информация се препоръчва професионален човешки превод. Ние не носим отговорност за каквито и да е недоразумения или неправилни тълкувания, произтичащи от използването на този превод.