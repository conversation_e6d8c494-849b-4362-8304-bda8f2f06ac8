<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:45:28+00:00",
  "source_file": "README.md",
  "language_code": "bg"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.bg.png)

### 21 урока, които ви учат на всичко необходимо, за да започнете да създавате приложения с Generative AI

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Поддръжка на множество езици

#### Поддържа се чрез GitHub Action (Автоматично и винаги актуално)

[French](../fr/README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](../ja/README.md) | [Korean](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](../el/README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](../he/README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](../sk/README.md) | [Romanian](../ro/README.md) | [Bulgarian](./README.md) | [Serbian (Cyrillic)](../sr/README.md) | [Croatian](../hr/README.md) | [Slovenian](../sl/README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Generative AI за начинаещи (Версия 3) - Курс

Научете основите на създаването на приложения с Generative AI с нашия изчерпателен курс от 21 урока, създаден от Microsoft Cloud Advocates.

## 🌱 Започване

Този курс съдържа 21 урока. Всеки урок разглежда отделна тема, така че започнете от където пожелаете!

Уроците са означени като "Learn" – обясняващи концепция от Generative AI, или "Build" – обясняващи концепция и предоставящи кодови примери както на **Python**, така и на **TypeScript**, когато е възможно.

За .NET разработчици вижте [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

Всеки урок включва и секция "Keep Learning" с допълнителни учебни материали.

## Какво ви е необходимо
### За да стартирате кода от този курс, можете да използвате:
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **Уроци:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **Уроци:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **Уроци:** "oai-assignment" 
   
- Основни познания по Python или TypeScript са полезни - \*За абсолютни начинаещи разгледайте тези [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) и [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) курсове
- GitHub акаунт, за да [форкнете цялото това хранилище](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) във вашия собствен акаунт

Създадохме урок **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)**, който ще ви помогне да настроите средата за разработка.

Не забравяйте да [отметнете с звезда (🌟) това хранилище](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst), за да го намерите по-лесно по-късно.

## 🧠 Готови ли сте за разгръщане?

Ако търсите по-напреднали кодови примери, разгледайте нашата [колекция от кодови примери за Generative AI](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) както на **Python**, така и на **TypeScript**.

## 🗣️ Срещнете други учащи, получете подкрепа

Присъединете се към нашия [официален Discord сървър Azure AI Foundry](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst), за да се запознаете и свържете с други учащи, които следват този курс, и да получите помощ.

Задавайте въпроси или споделяйте обратна връзка за продукта в нашия [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) в Github.

## 🚀 Създавате стартъп?

Регистрирайте се в [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst), за да получите **безплатни кредити за OpenAI** и до **150 000 долара в кредити за Azure за достъп до OpenAI модели чрез Azure OpenAI Services**.

## 🙏 Искате да помогнете?

Имате ли предложения или сте открили правописни или кодови грешки? [Отворете issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) или [създайте pull request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Всеки урок включва:

- Кратко видео въведение в темата
- Писмен урок, разположен в README
- Примери с код на Python и TypeScript, поддържащи Azure OpenAI и OpenAI API
- Връзки към допълнителни ресурси за продължаване на обучението

## 🗃️ Уроци

| #   | **Връзка към урока**                                                                                                                        | **Описание**                                                                                   | **Видео**                                                                   | **Допълнително обучение**                                                      |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Learn:** Как да настроите средата си за разработка                                         | Видео предстои                                                                 | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Въведение в Generative AI и LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Learn:** Разбиране какво е Generative AI и как работят големите езикови модели (LLMs)       | [Видео](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Изследване и сравнение на различни LLMs](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** Как да изберете правилния модел за вашия случай                                   | [Видео](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Отговорно използване на Generative AI](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                      | **Learn:** Как да създавате отговорни Generative AI приложения                               | [Видео](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Основи на Prompt Engineering](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)                               | **Learn:** Практически най-добри практики в Prompt Engineering                               | [Видео](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Създаване на усъвършенствани промптове](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                   | **Learn:** Как да прилагате техники за prompt engineering, които подобряват резултатите от вашите промптове | [Видео](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Създаване на приложения за генериране на текст](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Създаване:** Приложение за генериране на текст с помощта на Azure OpenAI / OpenAI API                                | [Видео](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Създаване на чат приложения](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Създаване:** Техники за ефективно изграждане и интегриране на чат приложения.               | [Видео](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Създаване на търсещи приложения с векторни бази данни](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Създаване:** Търсещо приложение, което използва Embeddings за търсене на данни.                        | [Видео](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Създаване на приложения за генериране на изображения](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Създаване:** Приложение за генериране на изображения                                                       | [Видео](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Създаване на AI приложения с нисък код](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Създаване:** Генеративно AI приложение с помощта на инструменти за нисък код                                     | [Видео](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Интегриране на външни приложения с Function Calling](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **Създаване:** Какво е function calling и как се използва в приложения                          | [Видео](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [Дизайн на UX за AI приложения](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Научете:** Как да приложите принципите на UX дизайна при разработката на генеративни AI приложения         | [Видео](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Защита на вашите генеративни AI приложения](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Научете:** Заплахите и рисковете за AI системите и методите за тяхната защита.             | [Видео](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Жизнен цикъл на генеративните AI приложения](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **Научете:** Инструментите и метриките за управление на жизнения цикъл на LLM и LLMOps                         | [Видео](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) и векторни бази данни](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **Създаване:** Приложение, използващо RAG Framework за извличане на embeddings от векторни бази данни  | [Видео](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Отворени модели и Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **Създаване:** Приложение, използващо отворени модели, налични в Hugging Face                    | [Видео](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI агенти](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **Създаване:** Приложение, използващо AI Agent Framework                                           | [Видео](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [Фина настройка на LLMs](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Научете:** Какво, защо и как се прави фина настройка на LLMs                                            | [Видео](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Създаване с SLMs](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Научете:** Ползите от създаването с малки езикови модели                                            | Видео предстои | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Създаване с Mistral модели](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Научете:** Характеристиките и разликите на моделите от фамилията Mistral                                           | Видео предстои | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Създаване с Meta модели](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Научете:** Характеристиките и разликите на моделите от фамилията Meta                                           | Видео предстои | [Научете повече](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Специални благодарности

Специални благодарности на [**John Aziz**](https://www.linkedin.com/in/john0isaac/) за създаването на всички GitHub Actions и работни потоци

[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) за ключовите приноси във всяко урок, които подобряват обучението и работата с кода.

## 🎒 Други курсове

Нашият екип създава и други курсове! Вижте:

- [**НОВ** Model Context Protocol за начинаещи](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI агенти за начинаещи](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Генеративен AI за начинаещи с .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Генеративен AI за начинаещи с JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML за начинаещи](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science за начинаещи](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI за начинаещи](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Киберсигурност за начинаещи](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Уеб разработка за начинаещи](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT за начинаещи](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR разработка за начинаещи](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Овладяване на GitHub Copilot за AI съвместно програмиране](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Овладяване на GitHub Copilot за C#/.NET разработчици](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Избери своето приключение с Copilot](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Отказ от отговорност**:  
Този документ е преведен с помощта на AI преводаческа услуга [Co-op Translator](https://github.com/Azure/co-op-translator). Въпреки че се стремим към точност, моля, имайте предвид, че автоматизираните преводи могат да съдържат грешки или неточности. Оригиналният документ на неговия роден език трябва да се счита за авторитетен източник. За критична информация се препоръчва професионален човешки превод. Ние не носим отговорност за каквито и да е недоразумения или неправилни тълкувания, произтичащи от използването на този превод.