<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:56:11+00:00",
  "source_file": "SECURITY.md",
  "language_code": "bg"
}
-->
## Сигурност

Microsoft приема сериозно сигурността на нашите софтуерни продукти и услуги, включително всички хранилища с изходен код, управлявани чрез нашите GitHub организации, които включват [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [X<PERSON>rin](https://github.com/xamarin) и [нашите GitHub организации](https://opensource.microsoft.com/).

Ако смятате, че сте открили уязвимост в сигурността в някое хранилище, собственост на Microsoft, което отговаря на [дефиницията на Microsoft за уязвимост в сигурността](https://aka.ms/opensource/security/definition), моля, докладвайте ни я по описания по-долу начин.

## Докладване на проблеми със сигурността

**Моля, не докладвайте уязвимости в сигурността чрез публични GitHub issues.**

Вместо това, моля, докладвайте ги на Microsoft Security Response Center (MSRC) на [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Ако предпочитате да изпратите доклада без да влизате в системата, изпратете имейл на [<EMAIL>](mailto:<EMAIL>). Ако е възможно, криптирайте съобщението си с нашия PGP ключ; можете да го изтеглите от страницата на [Microsoft Security Response Center PGP Key](https://aka.ms/opensource/security/pgpkey).

Трябва да получите отговор в рамките на 24 часа. Ако по някаква причина не получите, моля, последвайте с имейл, за да се уверите, че сме получили първоначалното ви съобщение. Допълнителна информация може да намерите на [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Моля, включете исканата по-долу информация (колкото можете да предоставите), за да ни помогнете по-добре да разберем естеството и обхвата на възможния проблем:

  * Тип на проблема (например buffer overflow, SQL injection, cross-site scripting и др.)
  * Пълни пътища до изходните файлове, свързани с проявлението на проблема
  * Местоположение на засегнатия изходен код (таг/клон/комит или директен URL)
  * Специална конфигурация, необходима за възпроизвеждане на проблема
  * Стъпка по стъпка инструкции за възпроизвеждане на проблема
  * Доказателство на концепцията или експлоатационен код (ако е възможно)
  * Въздействието на проблема, включително как нападател може да го използва

Тази информация ще ни помогне да обработим доклада ви по-бързо.

Ако докладвате за bug bounty, по-пълните доклади могат да допринесат за по-висока награда. Моля, посетете нашата страница [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) за повече подробности относно активните ни програми.

## Предпочитани езици

Предпочитаме всички комуникации да бъдат на английски език.

## Политика

Microsoft следва принципа на [Координирано разкриване на уязвимости](https://aka.ms/opensource/security/cvd).

**Отказ от отговорност**:  
Този документ е преведен с помощта на AI преводаческа услуга [Co-op Translator](https://github.com/Azure/co-op-translator). Въпреки че се стремим към точност, моля, имайте предвид, че автоматизираните преводи могат да съдържат грешки или неточности. Оригиналният документ на неговия език трябва да се счита за авторитетен източник. За критична информация се препоръчва професионален човешки превод. Ние не носим отговорност за каквито и да е недоразумения или неправилни тълкувания, произтичащи от използването на този превод.