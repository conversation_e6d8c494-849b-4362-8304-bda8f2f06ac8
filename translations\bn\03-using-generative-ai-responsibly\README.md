<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "7f8f4c11f8c1cb6e1794442dead414ea",
  "translation_date": "2025-07-09T08:52:04+00:00",
  "source_file": "03-using-generative-ai-responsibly/README.md",
  "language_code": "bn"
}
-->
# দায়িত্বশীলভাবে Generative AI ব্যবহার করা

[![Using Generative AI Responsibly](../../../translated_images/03-lesson-banner.1ed56067a452d97709d51f6cc8b6953918b2287132f4909ade2008c936cd4af9.bn.png)](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)

> _এই পাঠের ভিডিও দেখতে উপরের ছবিতে ক্লিক করুন_

AI এবং বিশেষ করে Generative AI নিয়ে মুগ্ধ হওয়া সহজ, কিন্তু আপনাকে ভাবতে হবে কীভাবে এটি দায়িত্বশীলভাবে ব্যবহার করবেন। আপনাকে বিবেচনা করতে হবে কিভাবে আউটপুটকে ন্যায়সঙ্গত, ক্ষতিকর নয় এবং আরও অনেক কিছু নিশ্চিত করবেন। এই অধ্যায়ে আমরা এই প্রসঙ্গ, বিবেচ্য বিষয় এবং AI ব্যবহারে উন্নতি করার জন্য সক্রিয় পদক্ষেপ নেওয়ার উপায় তুলে ধরব।

## পরিচিতি

এই পাঠে আলোচনা করা হবে:

- Generative AI অ্যাপ্লিকেশন তৈরি করার সময় কেন Responsible AI কে অগ্রাধিকার দেওয়া উচিত।
- Responsible AI এর মূল নীতিমালা এবং সেগুলো Generative AI এর সাথে কীভাবে সম্পর্কিত।
- কৌশল ও টুল ব্যবহার করে কীভাবে এই Responsible AI নীতিগুলো বাস্তবায়ন করা যায়।

## শেখার লক্ষ্য

এই পাঠ শেষ করার পর আপনি জানতে পারবেন:

- Generative AI অ্যাপ্লিকেশন তৈরি করার সময় Responsible AI এর গুরুত্ব।
- কখন Responsible AI এর মূল নীতিগুলো চিন্তা করে প্রয়োগ করতে হবে।
- Responsible AI ধারণা বাস্তবায়নের জন্য কী কী টুল ও কৌশল আপনার কাছে উপলব্ধ।

## Responsible AI নীতিমালা

Generative AI এর উত্তেজনা কখনো এত বেশি ছিল না। এই উত্তেজনা অনেক নতুন ডেভেলপার, মনোযোগ এবং তহবিল নিয়ে এসেছে। Generative AI ব্যবহার করে পণ্য ও কোম্পানি তৈরি করতে ইচ্ছুক যেকোনো ব্যক্তির জন্য এটি খুবই ইতিবাচক, তবে আমাদের দায়িত্বশীলভাবে এগিয়ে যাওয়াও জরুরি।

এই কোর্সে আমরা আমাদের স্টার্টআপ এবং AI শিক্ষা পণ্য তৈরি করার দিকে মনোযোগ দিচ্ছি। আমরা Responsible AI এর নীতিগুলো ব্যবহার করব: ন্যায়পরায়ণতা, অন্তর্ভুক্তিমূলকতা, নির্ভরযোগ্যতা/নিরাপত্তা, সুরক্ষা ও গোপনীয়তা, স্বচ্ছতা এবং জবাবদিহিতা। এই নীতিগুলো ব্যবহার করে আমরা দেখব Generative AI আমাদের পণ্যে কীভাবে প্রভাব ফেলে।

## কেন Responsible AI কে অগ্রাধিকার দেওয়া উচিত

কোনো পণ্য তৈরি করার সময় ব্যবহারকারীর সর্বোত্তম স্বার্থ মাথায় রেখে মানব-কেন্দ্রিক পদ্ধতি গ্রহণ করলে সেরা ফলাফল পাওয়া যায়।

Generative AI এর বিশেষত্ব হলো এটি ব্যবহারকারীদের জন্য সহায়ক উত্তর, তথ্য, নির্দেশনা এবং বিষয়বস্তু তৈরি করতে পারে। এটি অনেক ম্যানুয়াল ধাপ ছাড়াই করা যায়, যা চমৎকার ফলাফল দিতে পারে। তবে সঠিক পরিকল্পনা ও কৌশল ছাড়া এটি ব্যবহারকারীদের, আপনার পণ্য এবং সমাজের জন্য ক্ষতিকর ফলাফলও দিতে পারে।

চলুন কিছু (সব নয়) সম্ভাব্য ক্ষতিকর ফলাফল দেখি:

### হ্যালুসিনেশন

হ্যালুসিনেশন বলতে বোঝায় যখন কোনো LLM এমন বিষয়বস্তু তৈরি করে যা সম্পূর্ণ অর্থহীন বা অন্য তথ্যসূত্রের ভিত্তিতে ভুল।

উদাহরণস্বরূপ, আমরা আমাদের স্টার্টআপের জন্য এমন একটি ফিচার তৈরি করি যা শিক্ষার্থীদের ঐতিহাসিক প্রশ্ন জিজ্ঞাসা করার সুযোগ দেয়। একজন ছাত্র প্রশ্ন করে `Who was the sole survivor of Titanic?`

মডেল নিচের মতো উত্তর দেয়:

![Prompt saying "Who was the sole survivor of the Titanic"](../../../03-using-generative-ai-responsibly/images/ChatGPT-titanic-survivor-prompt.webp)

> _(উৎস: [Flying bisons](https://flyingbisons.com?WT.mc_id=academic-105485-koreyst))_

এটি একটি আত্মবিশ্বাসী এবং বিস্তারিত উত্তর। দুর্ভাগ্যবশত, এটি ভুল। সামান্য গবেষণায় জানা যায় টাইটানিক দুর্ঘটনার একাধিক বেঁচে থাকা ব্যক্তি ছিল। যারা এই বিষয় নিয়ে নতুন গবেষণা শুরু করছে, তাদের জন্য এই উত্তর যথেষ্ট বিশ্বাসযোগ্য হতে পারে এবং তারা এটিকে সত্য বলে ধরে নিতে পারে। এর ফলে AI সিস্টেম অবিশ্বাস্য হয়ে পড়তে পারে এবং আমাদের স্টার্টআপের সুনাম ক্ষতিগ্রস্ত হতে পারে।

প্রতিটি LLM সংস্করণে আমরা হ্যালুসিনেশন কমানোর ক্ষেত্রে উন্নতি দেখেছি। তবুও, অ্যাপ্লিকেশন নির্মাতা ও ব্যবহারকারী হিসেবে আমাদের এই সীমাবদ্ধতাগুলো সম্পর্কে সচেতন থাকা জরুরি।

### ক্ষতিকর বিষয়বস্তু

আগের অংশে আমরা দেখেছি কখন LLM ভুল বা অর্থহীন উত্তর দেয়। আরেকটি ঝুঁকি হলো মডেল যখন ক্ষতিকর বিষয়বস্তু তৈরি করে।

ক্ষতিকর বিষয়বস্তু বলতে বোঝায়:

- আত্মহত্যা বা নির্দিষ্ট গোষ্ঠীর প্রতি ক্ষতি করার নির্দেশনা বা উৎসাহ দেওয়া।
- ঘৃণাসূচক বা অবমাননাকর বিষয়বস্তু।
- কোনো ধরনের হামলা বা সহিংসতার পরিকল্পনা নির্দেশনা।
- অবৈধ বিষয়বস্তু খোঁজার বা অবৈধ কাজ করার নির্দেশনা।
- যৌনভাবে স্পষ্ট বিষয়বস্তু প্রদর্শন।

আমাদের স্টার্টআপে আমরা নিশ্চিত করতে চাই যে শিক্ষার্থীরা এই ধরনের বিষয়বস্তু দেখতে না পায়, এজন্য সঠিক টুল ও কৌশল প্রয়োগ করব।

### ন্যায়পরায়ণতার অভাব

ন্যায়পরায়ণতা মানে হলো “একটি AI সিস্টেম পক্ষপাত ও বৈষম্য থেকে মুক্ত এবং সবাইকে সমান ও ন্যায়সঙ্গতভাবে আচরণ করে।” Generative AI জগতে আমরা চাই মডেলের আউটপুটে সংখ্যালঘু গোষ্ঠীর বর্জিত দৃষ্টিভঙ্গি পুনরায় প্রতিষ্ঠিত না হয়।

এই ধরনের আউটপুট শুধুমাত্র ব্যবহারকারীদের জন্য ইতিবাচক পণ্য অভিজ্ঞতা তৈরি করতে বাধা দেয় না, বরং সমাজের জন্যও ক্ষতিকর। অ্যাপ্লিকেশন নির্মাতা হিসেবে আমাদের সবসময় বিস্তৃত ও বৈচিত্র্যময় ব্যবহারকারী ভিত্তি মাথায় রেখে Generative AI সমাধান তৈরি করা উচিত।

## কীভাবে Generative AI দায়িত্বশীলভাবে ব্যবহার করবেন

এখন যেহেতু আমরা Responsible Generative AI এর গুরুত্ব বুঝেছি, চলুন দেখি AI সমাধান দায়িত্বশীলভাবে তৈরি করার জন্য ৪টি ধাপ:

![Mitigate Cycle](../../../translated_images/mitigate-cycle.babcd5a5658e1775d5f2cb47f2ff305cca090400a72d98d0f9e57e9db5637c72.bn.png)

### সম্ভাব্য ক্ষতি পরিমাপ করা

সফটওয়্যার টেস্টিংয়ে আমরা ব্যবহারকারীর প্রত্যাশিত কাজ পরীক্ষা করি। একইভাবে, ব্যবহারকারীরা সবচেয়ে বেশি যেসব প্রম্পট ব্যবহার করতে পারে সেগুলো পরীক্ষা করা সম্ভাব্য ক্ষতি পরিমাপের ভালো উপায়।

আমাদের স্টার্টআপ একটি শিক্ষা পণ্য তৈরি করছে, তাই শিক্ষা সম্পর্কিত প্রম্পটের একটি তালিকা প্রস্তুত করা ভালো হবে। যেমন কোনো বিষয়, ঐতিহাসিক তথ্য, এবং ছাত্রজীবন সম্পর্কিত প্রম্পট।

### সম্ভাব্য ক্ষতি কমানো

এখন মডেল ও তার প্রতিক্রিয়ার কারণে সম্ভাব্য ক্ষতি প্রতিরোধ বা সীমিত করার উপায় খুঁজে বের করার সময়। আমরা এটি ৪টি স্তরে দেখতে পারি:

![Mitigation Layers](../../../translated_images/mitigation-layers.377215120b9a1159a8c3982c6bbcf41b6adf8c8fa04ce35cbaeeb13b4979cdfc.bn.png)

- **মডেল**। সঠিক ব্যবহারের জন্য সঠিক মডেল নির্বাচন। GPT-4 এর মতো বড় ও জটিল মডেল ছোট ও নির্দিষ্ট ব্যবহারে ক্ষতিকর বিষয়বস্তু তৈরি করার ঝুঁকি বাড়ায়। আপনার প্রশিক্ষণ ডেটা ব্যবহার করে ফাইন-টিউন করলে ক্ষতিকর বিষয়বস্তু কমে।

- **নিরাপত্তা ব্যবস্থা**। মডেল পরিবেশনকারী প্ল্যাটফর্মে টুল ও কনফিগারেশনের সেট যা ক্ষতি কমাতে সাহায্য করে। উদাহরণস্বরূপ Azure OpenAI সার্ভিসের কনটেন্ট ফিল্টারিং সিস্টেম। সিস্টেমগুলো জেলব্রেক আক্রমণ ও বট থেকে আসা অনাকাঙ্ক্ষিত অনুরোধ শনাক্ত করতে পারে।

- **মেটাপ্রম্পট**। মেটাপ্রম্পট ও গ্রাউন্ডিং হলো এমন পদ্ধতি যা মডেলকে নির্দিষ্ট আচরণ ও তথ্যের ভিত্তিতে নির্দেশনা দেয় বা সীমাবদ্ধ করে। যেমন সিস্টেম ইনপুট ব্যবহার করে মডেলের সীমা নির্ধারণ। এছাড়া আউটপুটকে সিস্টেমের ক্ষেত্র বা ডোমেইনের সাথে প্রাসঙ্গিক রাখা।

এছাড়া Retrieval Augmented Generation (RAG) এর মতো কৌশল ব্যবহার করে মডেলকে নির্ভরযোগ্য উৎস থেকে তথ্য সংগ্রহ করানো যায়। এই কোর্সে পরবর্তীতে [সার্চ অ্যাপ্লিকেশন তৈরি](../08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst) নিয়ে একটি পাঠ আছে।

- **ব্যবহারকারীর অভিজ্ঞতা**। চূড়ান্ত স্তর যেখানে ব্যবহারকারী আমাদের অ্যাপ্লিকেশনের মাধ্যমে সরাসরি মডেলের সাথে যোগাযোগ করে। এখানে UI/UX ডিজাইন করে ব্যবহারকারীর ইনপুট সীমাবদ্ধ করা যায় এবং প্রদর্শিত টেক্সট বা ছবি নিয়ন্ত্রণ করা যায়। AI অ্যাপ্লিকেশন ডিপ্লয় করার সময় আমাদের অবশ্যই স্বচ্ছ থাকতে হবে যে Generative AI অ্যাপ্লিকেশন কী করতে পারে এবং কী করতে পারে না।

আমাদের কাছে [AI অ্যাপ্লিকেশনের জন্য UX ডিজাইন](../12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst) নিয়ে একটি সম্পূর্ণ পাঠ রয়েছে।

- **মডেল মূল্যায়ন**। LLM নিয়ে কাজ করা চ্যালেঞ্জিং কারণ আমরা সবসময় মডেল প্রশিক্ষণের ডেটার নিয়ন্ত্রণে নেই। তবুও মডেলের পারফরম্যান্স ও আউটপুট মূল্যায়ন করা জরুরি। মডেলের সঠিকতা, সাদৃশ্য, ভিত্তিপ্রাপ্ততা এবং প্রাসঙ্গিকতা পরিমাপ করা গুরুত্বপূর্ণ। এটি স্টেকহোল্ডার ও ব্যবহারকারীদের কাছে স্বচ্ছতা ও বিশ্বাসযোগ্যতা দেয়।

### দায়িত্বশীল Generative AI সমাধান পরিচালনা করা

AI অ্যাপ্লিকেশনগুলোর চারপাশে একটি অপারেশনাল প্র্যাকটিস তৈরি করাই শেষ ধাপ। এর মধ্যে আমাদের স্টার্টআপের অন্যান্য অংশ যেমন লিগ্যাল ও সিকিউরিটির সাথে অংশীদারিত্ব করা যাতে সব নিয়ন্ত্রক নীতিমালা মেনে চলা হয়। লঞ্চের আগে ডেলিভারি, ঘটনা পরিচালনা এবং রোলব্যাকের পরিকল্পনা তৈরি করতে হবে যাতে ব্যবহারকারীদের ক্ষতি না হয়।

## টুলস

যদিও Responsible AI সমাধান তৈরি করা অনেক কাজ মনে হতে পারে, এটি প্রচেষ্টা করার মতোই মূল্যবান। Generative AI এর ক্ষেত্র বাড়ার সাথে সাথে ডেভেলপারদের জন্য দায়িত্বশীলতা কার্যকরভাবে workflow এ অন্তর্ভুক্ত করার জন্য আরও টুলস তৈরি হবে। উদাহরণস্বরূপ, [Azure AI Content Safety](https://learn.microsoft.com/azure/ai-services/content-safety/overview?WT.mc_id=academic-105485-koreyst) API অনুরোধের মাধ্যমে ক্ষতিকর বিষয়বস্তু ও ছবি শনাক্ত করতে সাহায্য করে।

## জ্ঞান যাচাই

দায়িত্বশীল AI ব্যবহারের জন্য আপনাকে কোন বিষয়গুলো খেয়াল রাখতে হবে?

1. উত্তর সঠিক হওয়া।
1. ক্ষতিকর ব্যবহার, AI অপরাধমূলক কাজে ব্যবহৃত না হওয়া।
1. AI পক্ষপাত ও বৈষম্য থেকে মুক্ত রাখা।

উত্তর: ২ এবং ৩ সঠিক। Responsible AI আপনাকে ক্ষতিকর প্রভাব ও পক্ষপাত কমানোর উপায় বিবেচনা করতে সাহায্য করে।

## 🚀 চ্যালেঞ্জ

[Azure AI Content Safety](https://learn.microsoft.com/azure/ai-services/content-safety/overview?WT.mc_id=academic-105485-koreyst) সম্পর্কে পড়ুন এবং দেখুন কীভাবে আপনি এটি আপনার ব্যবহারে অন্তর্ভুক্ত করতে পারেন।

## অসাধারণ কাজ, আপনার শেখা চালিয়ে যান

এই পাঠ শেষ করার পর আমাদের [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) দেখুন এবং আপনার Generative AI জ্ঞান আরও উন্নত করুন!

পরবর্তী পাঠে যান যেখানে আমরা [Prompt Engineering Fundamentals](../04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst) নিয়ে আলোচনা করব!

**অস্বীকৃতি**:  
এই নথিটি AI অনুবাদ সেবা [Co-op Translator](https://github.com/Azure/co-op-translator) ব্যবহার করে অনূদিত হয়েছে। আমরা যথাসাধ্য সঠিকতার চেষ্টা করি, তবে স্বয়ংক্রিয় অনুবাদে ত্রুটি বা অসঙ্গতি থাকতে পারে। মূল নথিটি তার নিজস্ব ভাষায়ই কর্তৃত্বপূর্ণ উৎস হিসেবে বিবেচিত হওয়া উচিত। গুরুত্বপূর্ণ তথ্যের জন্য পেশাদার মানব অনুবাদ গ্রহণ করার পরামর্শ দেওয়া হয়। এই অনুবাদের ব্যবহারে সৃষ্ট কোনো ভুল বোঝাবুঝি বা ভুল ব্যাখ্যার জন্য আমরা দায়ী নই।