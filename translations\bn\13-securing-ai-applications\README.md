<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "f3cac698e9eea47dd563633bd82daf8c",
  "translation_date": "2025-07-09T15:19:10+00:00",
  "source_file": "13-securing-ai-applications/README.md",
  "language_code": "bn"
}
-->
# আপনার জেনারেটিভ AI অ্যাপ্লিকেশন সুরক্ষা

[![আপনার জেনারেটিভ AI অ্যাপ্লিকেশন সুরক্ষা](../../../translated_images/13-lesson-banner.14103e36b4bbf17398b64ed2b0531f6f2c6549e7f7342f797c40bcae5a11862e.bn.png)](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst)

## পরিচিতি

এই পাঠে আলোচনা করা হবে:

- AI সিস্টেমের প্রেক্ষাপটে নিরাপত্তা।
- AI সিস্টেমের সাধারণ ঝুঁকি ও হুমকি।
- AI সিস্টেম সুরক্ষার পদ্ধতি ও বিবেচ্য বিষয়সমূহ।

## শেখার লক্ষ্য

এই পাঠ শেষ করার পর, আপনি বুঝতে পারবেন:

- AI সিস্টেমের বিরুদ্ধে হুমকি ও ঝুঁকি কী কী।
- AI সিস্টেম সুরক্ষার জন্য প্রচলিত পদ্ধতি ও অভ্যাস।
- কীভাবে নিরাপত্তা পরীক্ষা বাস্তবায়ন করে অপ্রত্যাশিত ফলাফল এবং ব্যবহারকারীর বিশ্বাসের ক্ষয় রোধ করা যায়।

## জেনারেটিভ AI প্রেক্ষাপটে নিরাপত্তার অর্থ কী?

যখন কৃত্রিম বুদ্ধিমত্তা (AI) এবং মেশিন লার্নিং (ML) প্রযুক্তি আমাদের জীবনকে ক্রমশ প্রভাবিত করছে, তখন শুধুমাত্র গ্রাহকের তথ্য নয়, AI সিস্টেমগুলোকেও সুরক্ষিত রাখা অত্যন্ত জরুরি। AI/ML উচ্চ-মূল্যের সিদ্ধান্ত গ্রহণ প্রক্রিয়ায় ব্যবহৃত হচ্ছে এমন শিল্পে, যেখানে ভুল সিদ্ধান্ত গুরুতর পরিণতি ডেকে আনতে পারে।

এখানে কিছু গুরুত্বপূর্ণ বিষয় বিবেচনা করার জন্য:

- **AI/ML এর প্রভাব**: AI/ML দৈনন্দিন জীবনে ব্যাপক প্রভাব ফেলে, তাই এগুলো সুরক্ষিত রাখা অপরিহার্য।
- **নিরাপত্তা চ্যালেঞ্জ**: AI/ML এর এই প্রভাবের কারণে উন্নত আক্রমণ থেকে AI-ভিত্তিক পণ্য রক্ষা করার প্রয়োজনীয়তা রয়েছে, যা হতে পারে ট্রোল বা সংগঠিত গোষ্ঠীর দ্বারা।
- **কৌশলগত সমস্যা**: প্রযুক্তি শিল্পকে দীর্ঘমেয়াদে গ্রাহকের নিরাপত্তা এবং তথ্য সুরক্ষা নিশ্চিত করতে কৌশলগত চ্যালেঞ্জ মোকাবেলা করতে হবে।

অতিরিক্তভাবে, মেশিন লার্নিং মডেলগুলি সাধারণত ক্ষতিকারক ইনপুট এবং সাধারণ অস্বাভাবিক ডেটার মধ্যে পার্থক্য করতে পারে না। প্রশিক্ষণের জন্য ব্যবহৃত ডেটার একটি বড় অংশ আসে অ-পরিচালিত, অ-পরিমার্জিত, পাবলিক ডেটাসেট থেকে, যা তৃতীয় পক্ষের অবদান গ্রহণ করে। আক্রমণকারীদের ডেটাসেট হ্যাক করতে হয় না, কারণ তারা সহজেই এতে অবদান রাখতে পারে। সময়ের সাথে সাথে, কম বিশ্বাসযোগ্য ক্ষতিকারক ডেটা উচ্চ বিশ্বাসযোগ্য বিশ্বস্ত ডেটায় পরিণত হয়, যদি ডেটার গঠন/ফরম্যাট ঠিক থাকে।

এই কারণেই আপনার মডেল যে ডেটা ব্যবহার করে সিদ্ধান্ত নেয়, সেই ডেটা স্টোরের অখণ্ডতা এবং সুরক্ষা নিশ্চিত করা অত্যন্ত গুরুত্বপূর্ণ।

## AI এর হুমকি ও ঝুঁকি বোঝা

AI এবং সংশ্লিষ্ট সিস্টেমের ক্ষেত্রে, ডেটা পয়জনিং আজকের সবচেয়ে বড় নিরাপত্তা হুমকি। ডেটা পয়জনিং হলো যখন কেউ ইচ্ছাকৃতভাবে AI প্রশিক্ষণের জন্য ব্যবহৃত তথ্য পরিবর্তন করে, যার ফলে AI ভুল সিদ্ধান্ত নেয়। এর কারণ হলো মানসম্মত সনাক্তকরণ ও প্রতিকার পদ্ধতির অভাব এবং আমরা অবিশ্বস্ত বা অ-পরিচালিত পাবলিক ডেটাসেটের উপর নির্ভর করি। ডেটার অখণ্ডতা বজায় রাখতে এবং ভুল প্রশিক্ষণ প্রক্রিয়া রোধ করতে, আপনার ডেটার উৎস এবং ইতিহাস ট্র্যাক করা জরুরি। না হলে, “গার্বেজ ইন, গার্বেজ আউট” কথাটি সত্য প্রমাণিত হবে, যা মডেলের কর্মক্ষমতা ক্ষতিগ্রস্ত করবে।

ডেটা পয়জনিং কীভাবে আপনার মডেলকে প্রভাবিত করতে পারে তার উদাহরণ:

1. **লেবেল ফ্লিপিং**: একটি বাইনারি শ্রেণীবিভাগ কাজের ক্ষেত্রে, প্রতিপক্ষ ইচ্ছাকৃতভাবে প্রশিক্ষণ ডেটার একটি ছোট অংশের লেবেল উল্টে দেয়। যেমন, সাধারণ নমুনাগুলোকে ক্ষতিকারক হিসেবে লেবেল করা হয়, যার ফলে মডেল ভুল সম্পর্ক শিখে।\
   **উদাহরণ**: একটি স্প্যাম ফিল্টার বৈধ ইমেইলকে স্প্যাম হিসেবে ভুল শনাক্ত করা।
2. **ফিচার পয়জনিং**: আক্রমণকারী প্রশিক্ষণ ডেটার বৈশিষ্ট্যগুলো সূক্ষ্মভাবে পরিবর্তন করে পক্ষপাত বা বিভ্রান্তি সৃষ্টি করে।\
   **উদাহরণ**: প্রোডাক্ট বর্ণনায় অপ্রাসঙ্গিক কীওয়ার্ড যোগ করে রিকমেন্ডেশন সিস্টেমকে প্রভাবিত করা।
3. **ডেটা ইনজেকশন**: মডেলের আচরণ প্রভাবিত করতে প্রশিক্ষণ সেটে ক্ষতিকারক ডেটা ঢোকানো।\
   **উদাহরণ**: মিথ্যা ব্যবহারকারী রিভিউ যোগ করে সেন্টিমেন্ট অ্যানালাইসিসকে বিকৃত করা।
4. **ব্যাকডোর আক্রমণ**: প্রতিপক্ষ প্রশিক্ষণ ডেটায় একটি গোপন প্যাটার্ন (ব্যাকডোর) ঢোকায়। মডেল এটি চিনে নেয় এবং ট্রিগার হলে ক্ষতিকারক আচরণ করে।\
   **উদাহরণ**: একটি ফেস রিকগনিশন সিস্টেম যা ব্যাকডোরযুক্ত ছবির মাধ্যমে নির্দিষ্ট ব্যক্তিকে ভুল শনাক্ত করে।

MITRE Corporation তৈরি করেছে [ATLAS (Adversarial Threat Landscape for Artificial-Intelligence Systems)](https://atlas.mitre.org/?WT.mc_id=academic-105485-koreyst), যা AI সিস্টেমে বাস্তব আক্রমণে প্রতিপক্ষের কৌশল ও প্রযুক্তির একটি জ্ঞানভাণ্ডার।

> AI-সক্ষম সিস্টেমে ক্রমবর্ধমান দুর্বলতা রয়েছে, কারণ AI সংযোজন ঐতিহ্যবাহী সাইবার-আক্রমণের চেয়ে বেশি আক্রমণ ক্ষেত্র তৈরি করে। আমরা ATLAS তৈরি করেছি এই অনন্য ও পরিবর্তনশীল দুর্বলতাগুলোর সচেতনতা বাড়াতে, কারণ বিশ্বব্যাপী সম্প্রদায় বিভিন্ন সিস্টেমে AI অন্তর্ভুক্ত করছে। ATLAS MITRE ATT&CK® ফ্রেমওয়ার্কের অনুকরণে তৈরি এবং এর কৌশল, প্রযুক্তি ও পদ্ধতিগুলো ATT&CK এর সাথে পরিপূরক।

MITRE ATT&CK® ফ্রেমওয়ার্কের মতো, যা প্রচলিত সাইবারসিকিউরিটিতে উন্নত হুমকি অনুকরণের জন্য ব্যবহৃত হয়, ATLAS একটি সহজে অনুসন্ধানযোগ্য TTPs সেট প্রদান করে যা উদীয়মান আক্রমণের বিরুদ্ধে প্রতিরক্ষা বুঝতে ও প্রস্তুত হতে সাহায্য করে।

অতিরিক্তভাবে, Open Web Application Security Project (OWASP) তৈরি করেছে LLM ব্যবহারকারী অ্যাপ্লিকেশনগুলোর সবচেয়ে গুরুত্বপূর্ণ দুর্বলতার "[Top 10 list](https://llmtop10.com/?WT.mc_id=academic-105485-koreyst)"। এই তালিকায় ডেটা পয়জনিংয়ের মতো হুমকির পাশাপাশি রয়েছে:

- **Prompt Injection**: একটি কৌশল যেখানে আক্রমণকারী সাবধানে তৈরি ইনপুটের মাধ্যমে একটি বড় ভাষা মডেল (LLM)কে তার উদ্দেশ্যবিরুদ্ধ আচরণ করতে প্ররোচিত করে।
- **Supply Chain Vulnerabilities**: LLM ব্যবহৃত অ্যাপ্লিকেশনের উপাদান ও সফটওয়্যার, যেমন পাইথন মডিউল বা বাহ্যিক ডেটাসেট, নিজেই দুর্বল হতে পারে, যা অপ্রত্যাশিত ফলাফল, পক্ষপাত এবং অবকাঠামোর দুর্বলতা সৃষ্টি করে।
- **Overreliance**: LLM ভুল করতে পারে এবং কখনও কখনও ভুল বা অনিরাপদ ফলাফল দেয়। অনেক documented ক্ষেত্রে মানুষ ফলাফলকে সরাসরি গ্রহণ করে যা বাস্তব জীবনে নেতিবাচক প্রভাব ফেলে।

Microsoft Cloud Advocate Rod Trent একটি বিনামূল্যের ইবুক লিখেছেন, [Must Learn AI Security](https://github.com/rod-trent/OpenAISecurity/tree/main/Must_Learn/Book_Version?WT.mc_id=academic-105485-koreyst), যা এই এবং অন্যান্য উদীয়মান AI হুমকির গভীর বিশ্লেষণ এবং সেগুলো মোকাবেলার ব্যাপক নির্দেশনা দেয়।

## AI সিস্টেম ও LLM এর জন্য নিরাপত্তা পরীক্ষা

কৃত্রিম বুদ্ধিমত্তা (AI) বিভিন্ন ক্ষেত্র ও শিল্পে বিপ্লব ঘটাচ্ছে, যা সমাজের জন্য নতুন সম্ভাবনা ও সুবিধা নিয়ে এসেছে। তবে AI এর সাথে রয়েছে বড় চ্যালেঞ্জ ও ঝুঁকি, যেমন ডেটা গোপনীয়তা, পক্ষপাত, ব্যাখ্যার অভাব এবং সম্ভাব্য অপব্যবহার। তাই AI সিস্টেমগুলোকে নিরাপদ ও দায়িত্বশীল রাখা জরুরি, অর্থাৎ তারা নৈতিক ও আইনগত মানদণ্ড মেনে চলে এবং ব্যবহারকারী ও অংশীদারদের বিশ্বাস অর্জন করে।

নিরাপত্তা পরীক্ষা হলো AI সিস্টেম বা LLM এর দুর্বলতা সনাক্ত ও ব্যবহার করে তাদের নিরাপত্তা মূল্যায়ন করার প্রক্রিয়া। এটি ডেভেলপার, ব্যবহারকারী বা তৃতীয় পক্ষের নিরীক্ষক দ্বারা করা যেতে পারে, পরীক্ষার উদ্দেশ্য ও পরিধি অনুযায়ী। AI সিস্টেম ও LLM এর জন্য সবচেয়ে প্রচলিত নিরাপত্তা পরীক্ষার পদ্ধতিগুলো হলো:

- **ডেটা স্যানিটাইজেশন**: প্রশিক্ষণ ডেটা বা AI সিস্টেম/LLM এর ইনপুট থেকে সংবেদনশীল বা ব্যক্তিগত তথ্য অপসারণ বা গোপনীয় করা। এটি ডেটা ফাঁস এবং ক্ষতিকারক পরিবর্তন রোধে সাহায্য করে।
- **বিরোধী পরীক্ষা (Adversarial testing)**: AI সিস্টেম বা LLM এর ইনপুট বা আউটপুটে বিরোধী উদাহরণ তৈরি ও প্রয়োগ করে তার স্থিতিশীলতা ও প্রতিরোধ ক্ষমতা যাচাই করা। এটি আক্রমণকারীদের দ্বারা শোষিত দুর্বলতা চিহ্নিত ও কমাতে সাহায্য করে।
- **মডেল যাচাই (Model verification)**: AI সিস্টেম বা LLM এর মডেল প্যারামিটার বা আর্কিটেকচারের সঠিকতা ও সম্পূর্ণতা যাচাই করা। এটি মডেল চুরি প্রতিরোধে সাহায্য করে।
- **আউটপুট যাচাই (Output validation)**: AI সিস্টেম বা LLM এর আউটপুটের গুণমান ও নির্ভরযোগ্যতা যাচাই করা। এটি ক্ষতিকারক পরিবর্তন সনাক্ত ও সংশোধনে সাহায্য করে।

OpenAI, AI সিস্টেমের অগ্রণী প্রতিষ্ঠান, তাদের রেড টিমিং নেটওয়ার্ক উদ্যোগের অংশ হিসেবে _সেফটি ইভ্যালুয়েশন_ সিরিজ তৈরি করেছে, যা AI সিস্টেমের আউটপুট পরীক্ষা করে AI নিরাপত্তায় অবদান রাখার লক্ষ্যে।

> মূল্যায়নগুলো সহজ প্রশ্নোত্তর থেকে শুরু করে জটিল সিমুলেশন পর্যন্ত হতে পারে। উদাহরণস্বরূপ, OpenAI তৈরি করেছে কিছু নমুনা মূল্যায়ন যা বিভিন্ন দৃষ্টিকোণ থেকে AI আচরণ মূল্যায়ন করে:

#### প্ররোচনা

- [MakeMeSay](https://github.com/openai/evals/tree/main/evals/elsuite/make_me_say/readme.md?WT.mc_id=academic-105485-koreyst): একটি AI সিস্টেম আরেকটি AI সিস্টেমকে গোপন শব্দ বলাতে কতটা সফল?
- [MakeMePay](https://github.com/openai/evals/tree/main/evals/elsuite/make_me_pay/readme.md?WT.mc_id=academic-105485-koreyst): একটি AI সিস্টেম আরেকটি AI সিস্টেমকে অর্থ দানে কতটা প্ররোচিত করতে পারে?
- [Ballot Proposal](https://github.com/openai/evals/tree/main/evals/elsuite/ballots/readme.md?WT.mc_id=academic-105485-koreyst): একটি AI সিস্টেম আরেকটি AI সিস্টেমের রাজনৈতিক প্রস্তাব সমর্থনে কতটা প্রভাব ফেলতে পারে?

#### স্টেগানোগ্রাফি (গোপন বার্তা)

- [Steganography](https://github.com/openai/evals/tree/main/evals/elsuite/steganography/readme.md?WT.mc_id=academic-105485-koreyst): একটি AI সিস্টেম আরেকটি AI সিস্টেমের নজর এড়িয়ে গোপন বার্তা কতটা সফলভাবে প্রেরণ করতে পারে?
- [Text Compression](https://github.com/openai/evals/tree/main/evals/elsuite/text_compression/readme.md?WT.mc_id=academic-105485-koreyst): একটি AI সিস্টেম বার্তা সংকুচিত ও পুনরায় প্রসারিত করে গোপন বার্তা লুকাতে কতটা সক্ষম?
- [Schelling Point](https://github.com/openai/evals/blob/main/evals/elsuite/schelling_point/README.md?WT.mc_id=academic-105485-koreyst): একটি AI সিস্টেম আরেকটি AI সিস্টেমের সাথে সরাসরি যোগাযোগ ছাড়াই কতটা সমন্বয় করতে পারে?

### AI নিরাপত্তা

আমাদের অবশ্যই AI সিস্টেমকে ক্ষতিকারক আক্রমণ, অপব্যবহার বা অনিচ্ছাকৃত পরিণতি থেকে রক্ষা করার লক্ষ্য রাখতে হবে। এর মধ্যে রয়েছে AI সিস্টেমের নিরাপত্তা, নির্ভরযোগ্যতা এবং বিশ্বাসযোগ্যতা নিশ্চিত করার পদক্ষেপ, যেমন:

- AI মডেল প্রশিক্ষণ ও চালনার জন্য ব্যবহৃত ডেটা ও অ্যালগরিদম সুরক্ষা করা
- AI সিস্টেমে অননুমোদিত প্রবেশ, পরিবর্তন বা ধ্বংসাত্মক কাজ প্রতিরোধ করা
- AI সিস্টেমে পক্ষপাত, বৈষম্য বা নৈতিক সমস্যা সনাক্ত ও কমানো
- AI সিদ্ধান্ত ও কর্মের জবাবদিহিতা, স্বচ্ছতা ও ব্যাখ্যাযোগ্যতা নিশ্চিত করা
- AI সিস্টেমের লক্ষ্য ও মূল্যবোধ মানুষের ও সমাজের সাথে সামঞ্জস্যপূর্ণ করা

AI নিরাপত্তা AI সিস্টেম ও ডেটার অখণ্ডতা, প্রাপ্যতা ও গোপনীয়তা নিশ্চিত করার জন্য গুরুত্বপূর্ণ। AI নিরাপত্তার কিছু চ্যালেঞ্জ ও সুযোগ হলো:

- সুযোগ: সাইবারসিকিউরিটি কৌশলে AI অন্তর্ভুক্ত করা, কারণ এটি হুমকি সনাক্তকরণ ও প্রতিক্রিয়া সময় উন্নত করতে সাহায্য করে। AI ফিশিং, ম্যালওয়্যার বা র‍্যানসমওয়্যার আক্রমণ সনাক্ত ও প্রতিরোধ স্বয়ংক্রিয় ও উন্নত করতে পারে।
- চ্যালেঞ্জ: প্রতিপক্ষ AI ব্যবহার করে জাল বা বিভ্রান্তিকর কন্টেন্ট তৈরি, ব্যবহারকারীর ছদ্মবেশ ধারণ বা AI সিস্টেমের দুর্বলতা শোষণ করে জটিল আক্রমণ চালাতে পারে। তাই AI ডেভেলপারদের দায়িত্ব হলো এমন সিস্টেম ডিজাইন করা যা অপব্যবহারের বিরুদ্ধে দৃঢ় ও স্থিতিশীল।

### ডেটা সুরক্ষা

LLM গুলো তাদের ব্যবহৃত ডেটার গোপনীয়তা ও নিরাপত্তার জন্য ঝুঁকি সৃষ্টি করতে পারে। উদাহরণস্বরূপ, LLM প্রশিক্ষণ ডেটা থেকে ব্যক্তিগত নাম, ঠিকানা, পাসওয়ার্ড বা ক্রেডিট কার্ড নম্বরের মতো সংবেদনশীল তথ্য মনে রাখার এবং ফাঁস করার সম্ভাবনা থাকে। এছাড়া, ক্ষতিকারক ব্যক্তি বা গোষ্ঠী তাদের দুর্বলতা বা পক্ষপাত শোষণ করতে LLM গুলোকে ম্যানিপুলেট বা আক্রমণ করতে পারে। তাই এই ঝুঁকিগুলো সম্পর্কে সচেতন থাকা এবং উপযুক্ত ব্যবস্থা নেওয়া জরুরি। LLM এর সাথে ব্যবহৃত ডেটা সুরক্ষার জন্য আপনি নিচের পদক্ষেপগুলো নিতে পারেন:

- **LLM এর সাথে শেয়ার করা ডেটার পরিমাণ ও ধরন সীমাবদ্ধ করা**: শুধুমাত্র প্রয়োজনীয় ও প্রাসঙ্গিক ডেটা শেয়ার করুন, এবং সংবেদনশীল, গোপনীয় বা ব্যক্তিগত ডেটা শেয়ার এড়িয়ে চলুন। ব্যবহারকারীরা ডেটা শেয়ার করার সময় পরিচয় সংক্রান্ত তথ্য মুছে ফেলা বা লুকানো, অথবা নিরাপদ যোগাযোগ চ্যানেল ব্যবহার করে ডেটা এনক্রিপ্ট করা উচিত।
- **LLM দ্বারা তৈরি ডেটা যাচাই করা**: LLM এর আউটপুটের সঠিকতা ও গুণমান নিয়মিত পরীক্ষা করুন যাতে অবাঞ্ছিত বা অনুপযুক্ত তথ্য না থাকে।
- **ডেটা লঙ্ঘন বা ঘটনার রিপোর্ট ও সতর্কতা প্রদান**: LLM থেকে সন্দেহজনক বা অস্বাভাবিক কার্যকলাপ যেমন অপ্রাসঙ্গিক, ভুল, আপত্তিকর বা ক্ষতিকারক টেক্সট তৈরি হলে সতর্ক থাকুন। এটি ডেটা লঙ্ঘন বা নিরাপত্তা ঘটনার ইঙ্গিত হতে পারে।

ডেটা নিরাপত্তা, শাসন ও সম্মতি যেকোনো প্রতিষ্ঠানের জন্য অত্যন্ত গুরুত্বপূর্ণ যারা মাল্টি-ক্লাউড পরিবেশে ডেটা ও AI এর শক্তি ব্যবহার করতে চায়। আপনার ডেটা সুরক্ষা ও শাসন একটি জটিল ও বহুমুখী কাজ। আপনাকে বিভিন্ন ধরনের ডেটা (গঠনযুক্ত, অগঠনযুক্ত, এবং AI দ্বারা তৈরি ডেটা) বিভিন্ন অবস্থানে বিভিন্ন ক্লাউডে সুরক্ষিত ও শাসিত করতে হবে, এবং বর্তমান ও ভবিষ্যতের ডেটা নিরাপত্তা, শাসন ও AI নিয়মকানুন বিবেচনা করতে হবে। আপনার ডেটা রক্ষা করতে কিছু সেরা অভ্যাস ও সতর্কতা গ্রহণ করা উচিত, যেমন:

- ডেটা সুরক্ষা ও গোপনীয়তা বৈশিষ্ট্যযুক্ত ক্লাউড সেবা বা প্ল্যাটফর্ম ব্যবহার করা।
- ডেটার ত্রুটি, অসঙ্গতি বা অস্বাভাবিকতা পরীক্ষা করার জন্য ডেটা গুণমান ও যাচাই সরঞ্জাম ব্যবহার করা।
- ডেটা দায়িত্বশীল ও স্বচ্ছ ব্যবহারের জন্য
> AI রেড টিমিংয়ের চর্চা এখন আরও বিস্তৃত অর্থ বহন করে: এটি কেবল নিরাপত্তা দুর্বলতা পরীক্ষা করাই নয়, বরং অন্যান্য সিস্টেম ত্রুটিও পরীক্ষা করে, যেমন সম্ভাব্য ক্ষতিকর বিষয়বস্তু তৈরি। AI সিস্টেমগুলোর সাথে নতুন ঝুঁকি আসে, এবং রেড টিমিং এই নতুন ঝুঁকিগুলো বোঝার মূল ভিত্তি, যেমন প্রম্পট ইনজেকশন এবং ভিত্তিহীন বিষয়বস্তু তৈরি। - [Microsoft AI Red Team building future of safer AI](https://www.microsoft.com/security/blog/2023/08/07/microsoft-ai-red-team-building-future-of-safer-ai/?WT.mc_id=academic-105485-koreyst)
[![Guidance and resources for red teaming](../../../translated_images/13-AI-red-team.642ed54689d7e8a4d83bdf0635768c4fd8aa41ea539d8e3ffe17514aec4b4824.bn.png)]()

নিচে Microsoft-এর AI Red Team প্রোগ্রামকে গঠন করা মূল অন্তর্দৃষ্টিগুলো দেওয়া হলো।

1. **AI Red Teaming-এর বিস্তৃত পরিধি:**
   AI red teaming এখন নিরাপত্তা এবং Responsible AI (RAI) উভয় দিকই অন্তর্ভুক্ত করে। ঐতিহ্যগতভাবে, red teaming মূলত নিরাপত্তার দিকেই মনোযোগ দিত, যেখানে মডেলকে একটি ভেক্টর হিসেবে দেখা হতো (যেমন, মডেল চুরি করা)। তবে, AI সিস্টেমগুলো নতুন ধরনের নিরাপত্তা দুর্বলতা নিয়ে আসে (যেমন, prompt injection, poisoning), যা বিশেষ মনোযোগ দাবি করে। নিরাপত্তার বাইরে, AI red teaming ন্যায্যতা বিষয়ক সমস্যা (যেমন, স্টেরিওটাইপিং) এবং ক্ষতিকর বিষয়বস্তু (যেমন, সহিংসতার প্রশংসা) সম্পর্কেও অনুসন্ধান করে। এই সমস্যাগুলো দ্রুত শনাক্ত করলে প্রতিরক্ষা বিনিয়োগের অগ্রাধিকার নির্ধারণ করা সহজ হয়।
2. **দুর্বৃত্ত এবং সাধারণ ব্যর্থতা:**
   AI red teaming ব্যর্থতাগুলোকে দু’দিক থেকে বিবেচনা করে—দুর্বৃত্ত এবং সাধারণ। উদাহরণস্বরূপ, নতুন Bing-এর red teaming করার সময় আমরা শুধু কিভাবে দুর্বৃত্ত প্রতিপক্ষ সিস্টেমকে ভঙ্গ করতে পারে তা দেখি না, বরং সাধারণ ব্যবহারকারীরাও কিভাবে সমস্যাযুক্ত বা ক্ষতিকর বিষয়বস্তুতে সম্মুখীন হতে পারে তা অনুসন্ধান করি। ঐতিহ্যগত নিরাপত্তা red teaming যেখানে মূলত দুর্বৃত্তদের দিকে মনোযোগ দেয়, AI red teaming এখানে আরও বিস্তৃত ব্যবহারকারী এবং সম্ভাব্য ব্যর্থতার দিক বিবেচনা করে।
3. **AI সিস্টেমের গতিশীল প্রকৃতি:**
   AI অ্যাপ্লিকেশনগুলো ক্রমাগত পরিবর্তিত হয়। বড় ভাষা মডেল অ্যাপ্লিকেশনগুলোর ক্ষেত্রে, ডেভেলপাররা পরিবর্তিত চাহিদার সাথে খাপ খাইয়ে নেন। ধারাবাহিক red teaming চলমান সতর্কতা এবং পরিবর্তিত ঝুঁকির সাথে খাপ খাওয়ানোর নিশ্চয়তা দেয়।

AI red teaming সবকিছুই নয় এবং এটি অতিরিক্ত নিয়ন্ত্রণ যেমন [role-based access control (RBAC)](https://learn.microsoft.com/azure/ai-services/openai/how-to/role-based-access-control?WT.mc_id=academic-105485-koreyst) এবং ব্যাপক ডেটা ব্যবস্থাপনা সমাধানের পরিপূরক হিসেবে বিবেচনা করা উচিত। এটি এমন একটি নিরাপত্তা কৌশলকে সহায়তা করে যা নিরাপদ এবং দায়িত্বশীল AI সমাধান প্রয়োগে মনোযোগ দেয়, যেখানে গোপনীয়তা এবং নিরাপত্তার পাশাপাশি পক্ষপাত, ক্ষতিকর বিষয়বস্তু এবং ভুল তথ্য কমানোর চেষ্টা করা হয় যা ব্যবহারকারীর বিশ্বাসকে ক্ষতিগ্রস্ত করতে পারে।

এখানে কিছু অতিরিক্ত পড়ার তালিকা দেওয়া হলো যা আপনাকে বুঝতে সাহায্য করবে কিভাবে red teaming আপনার AI সিস্টেমের ঝুঁকি সনাক্ত এবং হ্রাস করতে পারে:

- [Planning red teaming for large language models (LLMs) and their applications](https://learn.microsoft.com/azure/ai-services/openai/concepts/red-teaming?WT.mc_id=academic-105485-koreyst)
- [What is the OpenAI Red Teaming Network?](https://openai.com/blog/red-teaming-network?WT.mc_id=academic-105485-koreyst)
- [AI Red Teaming - A Key Practice for Building Safer and More Responsible AI Solutions](https://rodtrent.substack.com/p/ai-red-teaming?WT.mc_id=academic-105485-koreyst)
- MITRE [ATLAS (Adversarial Threat Landscape for Artificial-Intelligence Systems)](https://atlas.mitre.org/?WT.mc_id=academic-105485-koreyst), যা AI সিস্টেমে বাস্তব আক্রমণে প্রতিপক্ষদের ব্যবহৃত কৌশল ও পদ্ধতির একটি জ্ঞানভাণ্ডার।

## জ্ঞান যাচাই

ডেটার অখণ্ডতা বজায় রাখা এবং অপব্যবহার রোধ করার জন্য কোন পদ্ধতি ভালো হতে পারে?

1. ডেটা অ্যাক্সেস এবং ডেটা ব্যবস্থাপনার জন্য শক্তিশালী role-based নিয়ন্ত্রণ থাকা
1. ডেটা ভুল উপস্থাপনা বা অপব্যবহার রোধে ডেটা লেবেলিং প্রয়োগ এবং নিরীক্ষণ করা
1. নিশ্চিত করা যে আপনার AI অবকাঠামো কনটেন্ট ফিল্টারিং সমর্থন করে

উত্তর: 1, যদিও তিনটি সুপারিশই ভালো, ব্যবহারকারীদের সঠিক ডেটা অ্যাক্সেস অনুমতি প্রদান নিশ্চিত করা LLM-এ ব্যবহৃত ডেটার ম্যানিপুলেশন এবং ভুল উপস্থাপনা রোধে সবচেয়ে কার্যকর।

## 🚀 চ্যালেঞ্জ

AI যুগে [সংবেদনশীল তথ্যের শাসন এবং সুরক্ষা](https://learn.microsoft.com/training/paths/purview-protect-govern-ai/?WT.mc_id=academic-105485-koreyst) সম্পর্কে আরও পড়াশোনা করুন।

## চমৎকার কাজ, আপনার শেখা চালিয়ে যান

এই পাঠ শেষ করার পর, আমাদের [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) দেখুন এবং আপনার Generative AI জ্ঞান আরও উন্নত করুন!

পরবর্তী পাঠ ১৪-এ যান, যেখানে আমরা [Generative AI Application Lifecycle](../14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst) সম্পর্কে আলোচনা করব!

**অস্বীকৃতি**:  
এই নথিটি AI অনুবাদ সেবা [Co-op Translator](https://github.com/Azure/co-op-translator) ব্যবহার করে অনূদিত হয়েছে। আমরা যথাসাধ্য সঠিকতার চেষ্টা করি, তবে স্বয়ংক্রিয় অনুবাদে ত্রুটি বা অসঙ্গতি থাকতে পারে। মূল নথিটি তার নিজস্ব ভাষায়ই কর্তৃত্বপূর্ণ উৎস হিসেবে বিবেচিত হওয়া উচিত। গুরুত্বপূর্ণ তথ্যের জন্য পেশাদার মানব অনুবাদ গ্রহণ করার পরামর্শ দেওয়া হয়। এই অনুবাদের ব্যবহারে সৃষ্ট কোনো ভুল বোঝাবুঝি বা ভুল ব্যাখ্যার জন্য আমরা দায়ী নই।