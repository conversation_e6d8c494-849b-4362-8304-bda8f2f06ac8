<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "27a5347a5022d5ef0a72ab029b03526a",
  "translation_date": "2025-07-09T15:50:34+00:00",
  "source_file": "14-the-generative-ai-application-lifecycle/README.md",
  "language_code": "bn"
}
-->
[![Integrating with function calling](../../../translated_images/14-lesson-banner.066d74a31727ac121eeac06376a068a397d8e335281e63ce94130d11f516e46b.bn.png)](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst)

# জেনারেটিভ AI অ্যাপ্লিকেশন লাইফসাইকেল

সব AI অ্যাপ্লিকেশনের জন্য একটি গুরুত্বপূর্ণ প্রশ্ন হলো AI ফিচারগুলোর প্রাসঙ্গিকতা, কারণ AI একটি দ্রুত পরিবর্তনশীল ক্ষেত্র। আপনার অ্যাপ্লিকেশন যাতে প্রাসঙ্গিক, নির্ভরযোগ্য এবং শক্তিশালী থাকে, তা নিশ্চিত করতে আপনাকে এটি নিয়মিত পর্যবেক্ষণ, মূল্যায়ন এবং উন্নত করতে হবে। এখানেই জেনারেটিভ AI লাইফসাইকেল কাজ করে।

জেনারেটিভ AI লাইফসাইকেল একটি ফ্রেমওয়ার্ক যা আপনাকে জেনারেটিভ AI অ্যাপ্লিকেশন তৈরি, মোতায়েন এবং রক্ষণাবেক্ষণের ধাপগুলোতে গাইড করে। এটি আপনাকে আপনার লক্ষ্য নির্ধারণ করতে, পারফরম্যান্স পরিমাপ করতে, চ্যালেঞ্জগুলো চিহ্নিত করতে এবং সমাধান বাস্তবায়ন করতে সাহায্য করে। এছাড়াও এটি আপনার অ্যাপ্লিকেশনকে আপনার ডোমেইন এবং স্টেকহোল্ডারদের নৈতিক ও আইনি মানদণ্ডের সাথে সামঞ্জস্যপূর্ণ করতে সহায়তা করে। জেনারেটিভ AI লাইফসাইকেল অনুসরণ করে, আপনি নিশ্চিত করতে পারেন যে আপনার অ্যাপ্লিকেশন সর্বদা মূল্য প্রদান করছে এবং ব্যবহারকারীদের সন্তুষ্ট করছে।

## পরিচিতি

এই অধ্যায়ে আপনি:

- MLOps থেকে LLMOps-এ প্যারাডাইম শিফট বুঝবেন
- LLM লাইফসাইকেল
- লাইফসাইকেল টুলিং
- লাইফসাইকেল মেট্রিফিকেশন এবং মূল্যায়ন

## MLOps থেকে LLMOps-এ প্যারাডাইম শিফট বুঝুন

LLM হলো কৃত্রিম বুদ্ধিমত্তার নতুন একটি হাতিয়ার, যা বিশ্লেষণ এবং জেনারেশন টাস্কে অত্যন্ত শক্তিশালী। তবে এই শক্তি AI এবং ক্লাসিক মেশিন লার্নিং টাস্কগুলোকে কীভাবে স্ট্রিমলাইন করা হয় তার ওপর কিছু প্রভাব ফেলে।

এই কারণে, আমাদের একটি নতুন প্যারাডাইম দরকার যা এই টুলটিকে গতিশীলভাবে এবং সঠিক প্রণোদনার মাধ্যমে মানিয়ে নিতে পারে। আমরা পুরনো AI অ্যাপগুলোকে "ML Apps" এবং নতুন AI অ্যাপগুলোকে "GenAI Apps" বা শুধু "AI Apps" হিসেবে শ্রেণীবদ্ধ করতে পারি, যা তখনকার প্রধান প্রযুক্তি এবং কৌশলগুলোকে প্রতিফলিত করে। এটি আমাদের বর্ণনাকে একাধিক দিক থেকে পরিবর্তন করে, নিচের তুলনাটি দেখুন।

![LLMOps vs. MLOps comparison](../../../translated_images/01-llmops-shift.29bc933cb3bb0080a562e1655c0c719b71a72c3be6252d5c564b7f598987e602.bn.png)

দ্রষ্টব্য, LLMOps-এ আমরা অ্যাপ ডেভেলপারদের উপর বেশি ফোকাস করি, ইন্টিগ্রেশনকে মূল পয়েন্ট হিসেবে ব্যবহার করি, "Models-as-a-Service" ব্যবহার করি এবং মেট্রিক্সের জন্য নিম্নলিখিত বিষয়গুলো বিবেচনা করি।

- গুণগত মান: রেসপন্সের গুণমান
- ক্ষতি: দায়িত্বশীল AI
- সততা: রেসপন্সের ভিত্তিপ্রাপ্ততা (বুঝতে পারছেন? এটা সঠিক?)
- খরচ: সমাধানের বাজেট
- লেটেন্সি: টোকেন রেসপন্সের গড় সময়

## LLM লাইফসাইকেল

প্রথমে, লাইফসাইকেল এবং তার পরিবর্তনগুলো বুঝতে নিচের ইনফোগ্রাফিকটি লক্ষ্য করুন।

![LLMOps infographic](../../../translated_images/02-llmops.70a942ead05a7645db740f68727d90160cb438ab71f0fb20548bc7fe5cad83ff.bn.png)

আপনি লক্ষ্য করবেন, এটি MLOps-এর সাধারণ লাইফসাইকেল থেকে আলাদা। LLM-এর অনেক নতুন চাহিদা রয়েছে, যেমন প্রম্পটিং, গুণমান উন্নত করার বিভিন্ন কৌশল (ফাইন-টিউনিং, RAG, মেটা-প্রম্পট), দায়িত্বশীল AI-এর সাথে বিভিন্ন মূল্যায়ন এবং দায়িত্ব, এবং নতুন মূল্যায়ন মেট্রিক্স (গুণমান, ক্ষতি, সততা, খরচ এবং লেটেন্সি)।

উদাহরণস্বরূপ, আমরা কীভাবে আইডিয়া করি তা দেখুন। বিভিন্ন LLM নিয়ে পরীক্ষা-নিরীক্ষার জন্য প্রম্পট ইঞ্জিনিয়ারিং ব্যবহার করি, সম্ভাবনাগুলো অন্বেষণ করি এবং পরীক্ষা করি তাদের হাইপোথিসিস সঠিক হতে পারে কিনা।

মনে রাখবেন, এটি সরলরেখা নয়, বরং ইন্টিগ্রেটেড লুপ, পুনরাবৃত্তিমূলক এবং একটি সামগ্রিক চক্র।

আমরা কীভাবে এই ধাপগুলো অন্বেষণ করতে পারি? চলুন বিস্তারিত দেখি কীভাবে একটি লাইফসাইকেল তৈরি করা যায়।

![LLMOps Workflow](../../../translated_images/03-llm-stage-flows.3a1e1c401235a6cfa886ed6ba04aa52a096a545e1bc44fa54d7d5983a7201892.bn.png)

এটি কিছুটা জটিল মনে হতে পারে, প্রথমে তিনটি বড় ধাপে ফোকাস করি।

1. আইডিয়েটিং/এক্সপ্লোরিং: এক্সপ্লোরেশন, এখানে আমরা আমাদের ব্যবসায়িক চাহিদা অনুযায়ী অনুসন্ধান করতে পারি। প্রোটোটাইপ তৈরি, একটি [PromptFlow](https://microsoft.github.io/promptflow/index.html?WT.mc_id=academic-105485-koreyst) তৈরি করা এবং পরীক্ষা করা যে এটি আমাদের হাইপোথিসিসের জন্য যথেষ্ট কার্যকর কিনা।
2. বিল্ডিং/অগমেন্টিং: বাস্তবায়ন, এখন আমরা বড় ডেটাসেটের জন্য মূল্যায়ন শুরু করি, ফাইন-টিউনিং এবং RAG-এর মতো কৌশল প্রয়োগ করি, আমাদের সমাধানের দৃঢ়তা পরীক্ষা করি। যদি না হয়, পুনরায় বাস্তবায়ন, নতুন ধাপ যোগ করা বা ডেটা পুনর্গঠন সাহায্য করতে পারে। আমাদের ফ্লো এবং স্কেল পরীক্ষা করার পর, যদি এটি কাজ করে এবং মেট্রিক্স ভালো হয়, তাহলে এটি পরবর্তী ধাপের জন্য প্রস্তুত।
3. অপারেশনালাইজিং: ইন্টিগ্রেশন, এখন আমাদের সিস্টেমে মনিটরিং এবং অ্যালার্ট সিস্টেম যোগ করা, ডিপ্লয়মেন্ট এবং অ্যাপ্লিকেশন ইন্টিগ্রেশন করা।

এরপর, নিরাপত্তা, কমপ্লায়েন্স এবং গভর্নেন্সের ওপর ফোকাস করে একটি সামগ্রিক ম্যানেজমেন্ট চক্র রয়েছে।

অভিনন্দন, এখন আপনার AI অ্যাপ্লিকেশন প্রস্তুত এবং কার্যকর। হাতে কলমে অভিজ্ঞতার জন্য, [Contoso Chat Demo](https://nitya.github.io/contoso-chat/?WT.mc_id=academic-105485-koreys) দেখুন।

এখন, আমরা কী কী টুল ব্যবহার করতে পারি?

## লাইফসাইকেল টুলিং

টুলিং-এর জন্য, Microsoft প্রদান করে [Azure AI Platform](https://azure.microsoft.com/solutions/ai/?WT.mc_id=academic-105485-koreys) এবং [PromptFlow](https://microsoft.github.io/promptflow/index.html?WT.mc_id=academic-105485-koreyst) যা আপনার সাইকেল সহজে বাস্তবায়ন এবং প্রস্তুত করতে সাহায্য করে।

[Azure AI Platform](https://azure.microsoft.com/solutions/ai/?WT.mc_id=academic-105485-koreys) আপনাকে [AI Studio](https://ai.azure.com/?WT.mc_id=academic-105485-koreys) ব্যবহার করার সুযোগ দেয়। AI Studio একটি ওয়েব পোর্টাল যা আপনাকে মডেল, স্যাম্পল এবং টুলস এক্সপ্লোর করতে দেয়। আপনার রিসোর্স ম্যানেজ করা, UI ডেভেলপমেন্ট ফ্লো এবং SDK/CLI অপশন ব্যবহার করে কোড-ফার্স্ট ডেভেলপমেন্ট পরিচালনা করা যায়।

![Azure AI possibilities](../../../translated_images/04-azure-ai-platform.80203baf03a12fa8b166e194928f057074843d1955177baf0f5b53d50d7b6153.bn.png)

Azure AI আপনাকে বিভিন্ন রিসোর্স ব্যবহার করার সুযোগ দেয়, আপনার অপারেশন, সার্ভিস, প্রজেক্ট, ভেক্টর সার্চ এবং ডেটাবেসের চাহিদা ম্যানেজ করার জন্য।

![LLMOps with Azure AI](../../../translated_images/05-llm-azure-ai-prompt.a5ce85cdbb494bdf95420668e3464aae70d8b22275a744254e941dd5e73ae0d2.bn.png)

Proof-of-Concept (POC) থেকে বড় স্কেলের অ্যাপ্লিকেশন পর্যন্ত PromptFlow দিয়ে তৈরি করুন:

- VS Code থেকে ভিজ্যুয়াল এবং ফাংশনাল টুলস ব্যবহার করে অ্যাপ ডিজাইন এবং তৈরি করুন
- সহজে আপনার অ্যাপের গুণগত AI-এর জন্য পরীক্ষা এবং ফাইন-টিউন করুন
- Azure AI Studio ব্যবহার করে ক্লাউডের সাথে ইন্টিগ্রেট এবং পুনরাবৃত্তি করুন, দ্রুত ইন্টিগ্রেশনের জন্য পুশ এবং ডিপ্লয় করুন

![LLMOps with PromptFlow](../../../translated_images/06-llm-promptflow.a183eba07a3a7fdf4aa74db92a318b8cbbf4a608671f6b166216358d3203d8d4.bn.png)

## দারুণ! আপনার শেখা চালিয়ে যান!

অসাধারণ, এখন আরও শিখুন কীভাবে আমরা একটি অ্যাপ্লিকেশন গঠন করি এবং ধারণাগুলো ব্যবহার করি [Contoso Chat App](https://nitya.github.io/contoso-chat/?WT.mc_id=academic-105485-koreyst) দিয়ে, দেখুন কীভাবে ক্লাউড অ্যাডভোকেসি এই ধারণাগুলো ডেমোতে যোগ করে। আরও কন্টেন্টের জন্য আমাদের [Ignite ব্রেকআউট সেশন](https://www.youtube.com/watch?v=DdOylyrTOWg) দেখুন।

এখন, লেসন ১৫ দেখুন, বুঝতে কীভাবে [Retrieval Augmented Generation এবং Vector Databases](../15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst) জেনারেটিভ AI-কে প্রভাবিত করে এবং আরও আকর্ষণীয় অ্যাপ্লিকেশন তৈরি করে!

**অস্বীকৃতি**:  
এই নথিটি AI অনুবাদ সেবা [Co-op Translator](https://github.com/Azure/co-op-translator) ব্যবহার করে অনূদিত হয়েছে। আমরা যথাসাধ্য সঠিকতার চেষ্টা করি, তবে স্বয়ংক্রিয় অনুবাদে ত্রুটি বা অসঙ্গতি থাকতে পারে। মূল নথিটি তার নিজস্ব ভাষায়ই কর্তৃত্বপূর্ণ উৎস হিসেবে বিবেচিত হওয়া উচিত। গুরুত্বপূর্ণ তথ্যের জন্য পেশাদার মানব অনুবাদ গ্রহণ করার পরামর্শ দেওয়া হয়। এই অনুবাদের ব্যবহারে সৃষ্ট কোনো ভুল বোঝাবুঝি বা ভুল ব্যাখ্যার জন্য আমরা দায়ী নই।