<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "e2861bbca91c0567ef32bc77fe054f9e",
  "translation_date": "2025-07-09T16:07:37+00:00",
  "source_file": "15-rag-and-vector-databases/README.md",
  "language_code": "bn"
}
-->
# Retrieval Augmented Generation (RAG) এবং ভেক্টর ডাটাবেস

[![Retrieval Augmented Generation (RAG) এবং ভেক্টর ডাটাবেস](../../../translated_images/15-lesson-banner.ac49e59506175d4fc6ce521561dab2f9ccc6187410236376cfaed13cde371b90.bn.png)](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst)

সার্চ অ্যাপ্লিকেশন লেসনে আমরা সংক্ষেপে শিখেছি কিভাবে আপনার নিজস্ব ডেটা বড় ভাষার মডেলে (LLMs) ইন্টিগ্রেট করা যায়। এই লেসনে, আমরা আরও গভীরে যাব কিভাবে আপনার ডেটাকে আপনার LLM অ্যাপ্লিকেশনে ভিত্তি হিসেবে ব্যবহার করা যায়, প্রক্রিয়ার যান্ত্রিকতা এবং ডেটা সংরক্ষণের পদ্ধতি, যার মধ্যে রয়েছে এমবেডিংস এবং টেক্সট উভয়ই।

> **ভিডিও শীঘ্রই আসছে**

## পরিচিতি

এই লেসনে আমরা নিম্নলিখিত বিষয়গুলো আলোচনা করব:

- RAG কী এবং কেন এটি AI (কৃত্রিম বুদ্ধিমত্তা) তে ব্যবহৃত হয় তার একটি পরিচিতি।

- ভেক্টর ডাটাবেস কী এবং আমাদের অ্যাপ্লিকেশনের জন্য একটি তৈরি করা।

- RAG কিভাবে একটি অ্যাপ্লিকেশনে ইন্টিগ্রেট করা যায় তার একটি ব্যবহারিক উদাহরণ।

## শেখার লক্ষ্য

এই লেসন সম্পন্ন করার পর, আপনি সক্ষম হবেন:

- ডেটা রিট্রিভাল এবং প্রসেসিংয়ে RAG এর গুরুত্ব ব্যাখ্যা করতে।

- RAG অ্যাপ্লিকেশন সেটআপ এবং আপনার ডেটাকে একটি LLM এর সাথে যুক্ত করতে।

- LLM অ্যাপ্লিকেশনে RAG এবং ভেক্টর ডাটাবেসের কার্যকর ইন্টিগ্রেশন।

## আমাদের পরিস্থিতি: আমাদের নিজস্ব ডেটা দিয়ে আমাদের LLM উন্নত করা

এই লেসনের জন্য, আমরা আমাদের নিজস্ব নোটগুলো শিক্ষা স্টার্টআপে যোগ করতে চাই, যা চ্যাটবটকে বিভিন্ন বিষয় সম্পর্কে আরও তথ্য পেতে সাহায্য করবে। আমাদের নোট ব্যবহার করে, শিক্ষার্থীরা আরও ভালোভাবে পড়াশোনা করতে পারবে এবং বিভিন্ন বিষয় বুঝতে পারবে, যা তাদের পরীক্ষার জন্য রিভিশন সহজ করবে। আমাদের পরিস্থিতি তৈরি করতে আমরা ব্যবহার করব:

- `Azure OpenAI:` আমাদের চ্যাটবট তৈরির জন্য ব্যবহৃত LLM

- `AI for beginners' lesson on Neural Networks:` এই ডেটা আমাদের LLM এর ভিত্তি হবে

- `Azure AI Search` এবং `Azure Cosmos DB:` ভেক্টর ডাটাবেস যেখানে আমরা ডেটা সংরক্ষণ করব এবং একটি সার্চ ইনডেক্স তৈরি করব

ব্যবহারকারীরা তাদের নোট থেকে প্র্যাকটিস কুইজ তৈরি করতে পারবে, রিভিশন ফ্ল্যাশ কার্ড তৈরি করতে পারবে এবং সংক্ষিপ্ত সারাংশ পেতে পারবে। শুরু করার জন্য, চলুন দেখি RAG কী এবং এটি কিভাবে কাজ করে:

## Retrieval Augmented Generation (RAG)

একটি LLM চালিত চ্যাটবট ব্যবহারকারীর প্রম্পট প্রক্রিয়াকরণ করে উত্তর তৈরি করে। এটি ইন্টারেক্টিভ এবং ব্যবহারকারীদের সাথে বিভিন্ন বিষয়ে আলাপচারিতা করে। তবে, এর উত্তর সীমাবদ্ধ থাকে প্রদত্ত প্রসঙ্গ এবং এর প্রাথমিক প্রশিক্ষণ ডেটার উপর। উদাহরণস্বরূপ, GPT-4 এর জ্ঞান কাটঅফ সেপ্টেম্বর ২০২১, অর্থাৎ এর পরের ঘটনাগুলোর সম্পর্কে এটি জানে না। এছাড়াও, LLM প্রশিক্ষণের জন্য ব্যবহৃত ডেটায় ব্যক্তিগত নোট বা কোম্পানির পণ্য ম্যানুয়াল মত গোপনীয় তথ্য অন্তর্ভুক্ত থাকে না।

### RAG (Retrieval Augmented Generation) কিভাবে কাজ করে

![RAG কিভাবে কাজ করে তা দেখানো চিত্র](../../../translated_images/how-rag-works.f5d0ff63942bd3a638e7efee7a6fce7f0787f6d7a1fca4e43f2a7a4d03cde3e0.bn.png)

ধরা যাক আপনি এমন একটি চ্যাটবট তৈরি করতে চান যা আপনার নোট থেকে কুইজ তৈরি করে, তখন আপনার একটি জ্ঞানভিত্তিক ডাটাবেসের সাথে সংযোগ প্রয়োজন। এখানেই RAG সাহায্য করে। RAG এর কাজের ধাপগুলো হলো:

- **জ্ঞানভিত্তিক ডাটাবেস:** রিট্রিভালের আগে, এই ডকুমেন্টগুলোকে ইনজেস্ট এবং প্রিপ্রসেস করতে হয়, সাধারণত বড় ডকুমেন্টগুলোকে ছোট ছোট অংশে ভাগ করে, সেগুলোকে টেক্সট এমবেডিংসে রূপান্তর করে এবং ডাটাবেসে সংরক্ষণ করা হয়।

- **ব্যবহারকারীর প্রশ্ন:** ব্যবহারকারী একটি প্রশ্ন করে

- **রিট্রিভাল:** ব্যবহারকারী প্রশ্ন করলে এমবেডিং মডেল আমাদের জ্ঞানভিত্তিক ডাটাবেস থেকে প্রাসঙ্গিক তথ্য উদ্ধার করে, যা প্রম্পটে অন্তর্ভুক্ত করা হয়।

- **অগমেন্টেড জেনারেশন:** LLM তার উত্তর উন্নত করে উদ্ধারকৃত তথ্যের ভিত্তিতে। এটি শুধুমাত্র প্রি-ট্রেইনড ডেটার উপর নয়, বরং যুক্ত প্রসঙ্গ থেকে প্রাসঙ্গিক তথ্য ব্যবহার করে উত্তর তৈরি করে। LLM তারপর ব্যবহারকারীর প্রশ্নের উত্তর প্রদান করে।

![RAG এর আর্কিটেকচার দেখানো চিত্র](../../../translated_images/encoder-decode.f2658c25d0eadee2377bb28cf3aee8b67aa9249bf64d3d57bb9be077c4bc4e1a.bn.png)

RAG এর আর্কিটেকচার ট্রান্সফর্মার ব্যবহার করে তৈরি, যার দুটি অংশ থাকে: এনকোডার এবং ডিকোডার। উদাহরণস্বরূপ, যখন ব্যবহারকারী প্রশ্ন করে, ইনপুট টেক্সটকে শব্দের অর্থ ধারণকারী ভেক্টরে 'এনকোড' করা হয় এবং ভেক্টরগুলোকে আমাদের ডকুমেন্ট ইনডেক্সে 'ডিকোড' করে নতুন টেক্সট তৈরি করা হয় ব্যবহারকারীর প্রশ্নের ভিত্তিতে। LLM উভয় এনকোডার-ডিকোডার মডেল ব্যবহার করে আউটপুট তৈরি করে।

প্রস্তাবিত পেপার [Retrieval-Augmented Generation for Knowledge intensive NLP Tasks](https://arxiv.org/pdf/2005.11401.pdf?WT.mc_id=academic-105485-koreyst) অনুযায়ী RAG বাস্তবায়নের দুটি পদ্ধতি:

- **_RAG-Sequence_**: উদ্ধারকৃত ডকুমেন্ট ব্যবহার করে ব্যবহারকারীর প্রশ্নের সেরা সম্ভাব্য উত্তর অনুমান করা

- **RAG-Token**: ডকুমেন্ট ব্যবহার করে পরবর্তী টোকেন তৈরি করা, তারপর সেগুলো উদ্ধার করে ব্যবহারকারীর প্রশ্নের উত্তর দেওয়া

### কেন RAG ব্যবহার করবেন?

- **তথ্যের সম্পদশালীতা:** নিশ্চিত করে টেক্সট উত্তর আপডেট এবং বর্তমান থাকে। এটি ডোমেইন-নির্দিষ্ট কাজের পারফরম্যান্স উন্নত করে অভ্যন্তরীণ জ্ঞানভিত্তিক ডাটাবেস অ্যাক্সেস করে।

- **মিথ্যা তথ্য কমানো:** ব্যবহারকারীর প্রশ্নের প্রসঙ্গ দেওয়ার জন্য জ্ঞানভিত্তিক ডাটাবেসের **যাচাইযোগ্য তথ্য** ব্যবহার করে।

- এটি **খরচ সাশ্রয়ী**, কারণ এটি LLM ফাইন-টিউনিংয়ের তুলনায় বেশি অর্থনৈতিক।

## জ্ঞানভিত্তিক ডাটাবেস তৈরি করা

আমাদের অ্যাপ্লিকেশন আমাদের ব্যক্তিগত ডেটার উপর ভিত্তি করে, অর্থাৎ AI For Beginners কারিকুলামের Neural Network লেসন।

### ভেক্টর ডাটাবেস

একটি ভেক্টর ডাটাবেস, প্রচলিত ডাটাবেসের থেকে আলাদা, একটি বিশেষায়িত ডাটাবেস যা এমবেডেড ভেক্টরগুলো সংরক্ষণ, পরিচালনা এবং অনুসন্ধানের জন্য ডিজাইন করা হয়েছে। এটি ডকুমেন্টের সংখ্যাসূচক প্রতিনিধিত্ব সংরক্ষণ করে। ডেটাকে সংখ্যাসূচক এমবেডিংসে ভাঙ্গা আমাদের AI সিস্টেমের জন্য ডেটা বোঝা এবং প্রক্রিয়াকরণ সহজ করে।

আমরা আমাদের এমবেডিংস ভেক্টর ডাটাবেসে সংরক্ষণ করি কারণ LLM এর ইনপুট টোকেনের একটি সীমা থাকে। পুরো এমবেডিংস LLM এ পাঠানো সম্ভব নয়, তাই আমরা এগুলোকে ছোট ছোট অংশে ভাগ করব এবং যখন ব্যবহারকারী প্রশ্ন করবে, সবচেয়ে প্রাসঙ্গিক এমবেডিংস প্রম্পটের সাথে ফেরত দেওয়া হবে। অংশে ভাগ করা টোকেনের সংখ্যা কমিয়ে খরচও কমায়।

কিছু জনপ্রিয় ভেক্টর ডাটাবেস হলো Azure Cosmos DB, Clarifyai, Pinecone, Chromadb, ScaNN, Qdrant এবং DeepLake। আপনি Azure CLI ব্যবহার করে Azure Cosmos DB মডেল তৈরি করতে পারেন নিম্নলিখিত কমান্ড দিয়ে:

```bash
az login
az group create -n <resource-group-name> -l <location>
az cosmosdb create -n <cosmos-db-name> -r <resource-group-name>
az cosmosdb list-keys -n <cosmos-db-name> -g <resource-group-name>
```

### টেক্সট থেকে এমবেডিংস

আমাদের ডেটা সংরক্ষণ করার আগে, এটি ভেক্টর এমবেডিংসে রূপান্তর করতে হবে। যদি আপনি বড় ডকুমেন্ট বা দীর্ঘ টেক্সট নিয়ে কাজ করেন, তাহলে আপনি প্রত্যাশিত প্রশ্নের ভিত্তিতে এগুলোকে ভাগ করতে পারেন। ভাগ করা হতে পারে বাক্য স্তরে বা অনুচ্ছেদ স্তরে। কারণ ভাগ করার সময় শব্দের পারিপার্শ্বিক অর্থ বিবেচনা করা হয়, আপনি একটি অংশে অতিরিক্ত প্রসঙ্গ যোগ করতে পারেন, যেমন ডকুমেন্টের শিরোনাম বা অংশের আগে বা পরে কিছু টেক্সট। আপনি ডেটা ভাগ করতে পারেন নিম্নরূপ:

```python
def split_text(text, max_length, min_length):
    words = text.split()
    chunks = []
    current_chunk = []

    for word in words:
        current_chunk.append(word)
        if len(' '.join(current_chunk)) < max_length and len(' '.join(current_chunk)) > min_length:
            chunks.append(' '.join(current_chunk))
            current_chunk = []

    # If the last chunk didn't reach the minimum length, add it anyway
    if current_chunk:
        chunks.append(' '.join(current_chunk))

    return chunks
```

একবার ভাগ করার পর, আমরা বিভিন্ন এমবেডিং মডেল ব্যবহার করে টেক্সট এমবেড করতে পারি। কিছু মডেল হলো: word2vec, OpenAI এর ada-002, Azure Computer Vision এবং আরও অনেক। মডেল নির্বাচন নির্ভর করবে আপনি কোন ভাষা ব্যবহার করছেন, কোন ধরনের কন্টেন্ট এনকোড করছেন (টেক্সট/ছবি/অডিও), ইনপুটের আকার এবং এমবেডিং আউটপুটের দৈর্ঘ্যের উপর।

OpenAI এর `text-embedding-ada-002` মডেল ব্যবহার করে এমবেড করা টেক্সটের একটি উদাহরণ:
![cat শব্দের এমবেডিং](../../../translated_images/cat.74cbd7946bc9ca380a8894c4de0c706a4f85b16296ffabbf52d6175df6bf841e.bn.png)

## রিট্রিভাল এবং ভেক্টর সার্চ

যখন ব্যবহারকারী প্রশ্ন করে, রিট্রিভার এটি একটি ভেক্টরে রূপান্তর করে কুয়েরি এনকোডার ব্যবহার করে, তারপর আমাদের ডকুমেন্ট সার্চ ইনডেক্সে প্রাসঙ্গিক ভেক্টর খোঁজে যা ইনপুটের সাথে সম্পর্কিত। কাজ শেষ হলে, এটি ইনপুট ভেক্টর এবং ডকুমেন্ট ভেক্টর উভয়কে টেক্সটে রূপান্তর করে এবং LLM এর কাছে পাঠায়।

### রিট্রিভাল

রিট্রিভাল ঘটে যখন সিস্টেম দ্রুত সেই ডকুমেন্টগুলো খুঁজে বের করার চেষ্টা করে যা সার্চ ক্রাইটেরিয়া পূরণ করে। রিট্রিভারের লক্ষ্য হলো এমন ডকুমেন্ট পাওয়া যা প্রসঙ্গ সরবরাহ করবে এবং LLM কে আপনার ডেটার উপর ভিত্তি করতে সাহায্য করবে।

আমাদের ডাটাবেসে সার্চ করার বিভিন্ন পদ্ধতি রয়েছে যেমন:

- **কীওয়ার্ড সার্চ** - টেক্সট সার্চের জন্য ব্যবহৃত

- **সেমান্টিক সার্চ** - শব্দের অর্থ ব্যবহার করে

- **ভেক্টর সার্চ** - এমবেডিং মডেল ব্যবহার করে ডকুমেন্টকে টেক্সট থেকে ভেক্টর রূপে রূপান্তর করে। রিট্রিভাল হয় সেই ডকুমেন্টগুলোকে কুয়েরি করা যা ব্যবহারকারীর প্রশ্নের সবচেয়ে কাছাকাছি ভেক্টর প্রতিনিধিত্ব করে।

- **হাইব্রিড** - কীওয়ার্ড এবং ভেক্টর সার্চের সংমিশ্রণ।

রিট্রিভালে একটি চ্যালেঞ্জ হলো যখন ডাটাবেসে প্রশ্নের সাথে মিল থাকা কোনো উত্তর না থাকে, তখন সিস্টেম সর্বোত্তম তথ্য ফেরত দেয়। তবে আপনি প্রাসঙ্গিকতার জন্য সর্বোচ্চ দূরত্ব নির্ধারণ করতে পারেন বা কীওয়ার্ড ও ভেক্টর সার্চের সংমিশ্রণ হাইব্রিড সার্চ ব্যবহার করতে পারেন। এই লেসনে আমরা হাইব্রিড সার্চ ব্যবহার করব, অর্থাৎ ভেক্টর এবং কীওয়ার্ড সার্চের সংমিশ্রণ। আমরা আমাদের ডেটা একটি ডেটাফ্রেমে সংরক্ষণ করব যেখানে কলামগুলোতে থাকবে অংশগুলো এবং এমবেডিংস।

### ভেক্টর সাদৃশ্য

রিট্রিভার জ্ঞানভিত্তিক ডাটাবেসে এমবেডিংস খোঁজে যেগুলো একে অপরের কাছে থাকে, অর্থাৎ সবচেয়ে কাছের প্রতিবেশী, কারণ এগুলো টেক্সট হিসেবে মিল রয়েছে। যখন ব্যবহারকারী প্রশ্ন করে, প্রথমে এটি এমবেড করা হয় এবং তারপর মিল থাকা এমবেডিংসের সাথে মেলানো হয়। ভেক্টরগুলোর সাদৃশ্য পরিমাপের জন্য সাধারণত ব্যবহৃত হয় কোসাইন সাদৃশ্য, যা দুই ভেক্টরের মধ্যকার কোণ নির্ভর।

আমরা অন্যান্য বিকল্পও ব্যবহার করতে পারি যেমন ইউক্লিডিয়ান দূরত্ব, যা ভেক্টর শেষ বিন্দুগুলোর সরলরেখার দূরত্ব এবং ডট প্রোডাক্ট, যা দুই ভেক্টরের সংশ্লিষ্ট উপাদানের গুণফলের যোগফল পরিমাপ করে।

### সার্চ ইনডেক্স

রিট্রিভাল করার আগে আমাদের জ্ঞানভিত্তিক ডাটাবেসের জন্য একটি সার্চ ইনডেক্স তৈরি করতে হবে। ইনডেক্স আমাদের এমবেডিংস সংরক্ষণ করে এবং বড় ডাটাবেসেও দ্রুত সবচেয়ে মিল থাকা অংশগুলো উদ্ধার করতে পারে। আমরা স্থানীয়ভাবে আমাদের ইনডেক্স তৈরি করতে পারি:

```python
from sklearn.neighbors import NearestNeighbors

embeddings = flattened_df['embeddings'].to_list()

# Create the search index
nbrs = NearestNeighbors(n_neighbors=5, algorithm='ball_tree').fit(embeddings)

# To query the index, you can use the kneighbors method
distances, indices = nbrs.kneighbors(embeddings)
```

### রি-র‍্যাঙ্কিং

ডাটাবেস থেকে কুয়েরি করার পর, ফলাফলগুলো সবচেয়ে প্রাসঙ্গিক থেকে সাজাতে হতে পারে। একটি রি-র‍্যাঙ্কিং LLM মেশিন লার্নিং ব্যবহার করে সার্চ ফলাফলগুলোর প্রাসঙ্গিকতা উন্নত করে সবচেয়ে প্রাসঙ্গিক থেকে সাজায়। Azure AI Search ব্যবহার করলে, রি-র‍্যাঙ্কিং স্বয়ংক্রিয়ভাবে একটি সেমান্টিক রি-র‍্যাঙ্কার দ্বারা করা হয়। নিকটতম প্রতিবেশীদের ব্যবহার করে রি-র‍্যাঙ্কিং কিভাবে কাজ করে তার একটি উদাহরণ:

```python
# Find the most similar documents
distances, indices = nbrs.kneighbors([query_vector])

index = []
# Print the most similar documents
for i in range(3):
    index = indices[0][i]
    for index in indices[0]:
        print(flattened_df['chunks'].iloc[index])
        print(flattened_df['path'].iloc[index])
        print(flattened_df['distances'].iloc[index])
    else:
        print(f"Index {index} not found in DataFrame")
```

## সবকিছু একত্রিত করা

শেষ ধাপ হলো আমাদের LLM কে যুক্ত করা যাতে আমরা আমাদের ডেটার ভিত্তিতে উত্তর পেতে পারি। আমরা এটি নিম্নরূপ বাস্তবায়ন করতে পারি:

```python
user_input = "what is a perceptron?"

def chatbot(user_input):
    # Convert the question to a query vector
    query_vector = create_embeddings(user_input)

    # Find the most similar documents
    distances, indices = nbrs.kneighbors([query_vector])

    # add documents to query  to provide context
    history = []
    for index in indices[0]:
        history.append(flattened_df['chunks'].iloc[index])

    # combine the history and the user input
    history.append(user_input)

    # create a message object
    messages=[
        {"role": "system", "content": "You are an AI assistant that helps with AI questions."},
        {"role": "user", "content": history[-1]}
    ]

    # use chat completion to generate a response
    response = openai.chat.completions.create(
        model="gpt-4",
        temperature=0.7,
        max_tokens=800,
        messages=messages
    )

    return response.choices[0].message

chatbot(user_input)
```

## আমাদের অ্যাপ্লিকেশন মূল্যায়ন

### মূল্যায়ন মেট্রিক্স

- সরবরাহকৃত উত্তরের গুণগত মান, যা প্রাকৃতিক, সাবলীল এবং মানবসদৃশ শোনায়

- ডেটার ভিত্তিত্ব: মূল্যায়ন করা যে উত্তরটি সরবরাহকৃত ডকুমেন্ট থেকে এসেছে কিনা

- প্রাসঙ্গিকতা: মূল্যায়ন করা যে উত্তরটি প্রশ্নের সাথে মেলে এবং সম্পর্কিত

- সাবলীলতা - উত্তরটি ব্যাকরণগতভাবে অর্থবোধক কিনা

## RAG (Retrieval Augmented Generation) এবং ভেক্টর ডাটাবেস ব্যবহারের ব্যবহার ক্ষেত্র

অনেক ধরনের ব্যবহার ক্ষেত্র রয়েছে যেখানে ফাংশন কল আপনার অ্যাপ উন্নত করতে পারে, যেমন:

- প্রশ্নোত্তর: আপনার কোম্পানির ডেটাকে এমন একটি চ্যাটে ভিত্তি হিসেবে ব্যবহার করা যা কর্মচারীরা প্রশ্ন করার জন্য ব্যবহার করতে পারে।

- রিকমেন্ডেশন সিস্টেম: যেখানে আপনি এমন একটি সিস্টেম তৈরি করতে পারেন যা সবচেয়ে মিল থাকা মানগুলো যেমন সিনেমা, রেস্টুরেন্ট ইত্যাদি ম্যাচ করে।

- চ্যাটবট সার্ভিস: আপনি চ্যাট ইতিহাস সংরক্ষণ করতে পারেন এবং ব্যবহারকারীর ডেটার ভিত্তিতে আলাপচারিতা ব্যক্তিগতকরণ করতে পারেন।

- ভেক্টর এমবেডিংসের ভিত্তিতে ইমেজ সার্চ, যা ইমেজ রিকগনিশন এবং অ্যানোমালি ডিটেকশনে কাজে লাগে।

## সারাংশ

আমরা RAG এর মৌলিক বিষয়গুলো আলোচনা করেছি, যেমন আমাদের ডেটা অ্যাপ্লিকেশনে যোগ করা, ব্যবহারকারীর প্রশ্ন এবং আউটপুট। RAG তৈরি সহজ করতে আপনি Semanti Kernel, Langchain বা Autogen এর মতো ফ্রেমওয়ার্ক ব্যবহার করতে পারেন।

## অ্যাসাইনমেন্ট

Retrieval Augmented Generation (RAG) শেখা চালিয়ে যেতে আপনি করতে পারেন:

- আপনার পছন্দের ফ্রেমওয়ার্ক ব্যবহার করে অ্যাপ্লিকেশনের ফ্রন্ট-এন্ড তৈরি করা

- LangChain বা Semantic Kernel এর মতো একটি ফ্রেমওয়ার্ক ব্যবহার করে আপনার অ্যাপ্লিকেশন পুনরায় তৈরি করা

লেসন সম্পন্ন করার জন্য অভিনন্দন 👏।

## শেখা এখানেই শেষ নয়, যাত্রা চালিয়ে যান

এই লেসন শেষ করার পর, আমাদের [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) দেখুন এবং আপনার Generative AI জ্ঞান আরও উন্নত করুন!

**অস্বীকৃতি**:  
এই নথিটি AI অনুবাদ সেবা [Co-op Translator](https://github.com/Azure/co-op-translator) ব্যবহার করে অনূদিত হয়েছে। আমরা যথাসাধ্য সঠিকতার চেষ্টা করি, তবে স্বয়ংক্রিয় অনুবাদে ত্রুটি বা অসঙ্গতি থাকতে পারে। মূল নথিটি তার নিজস্ব ভাষায়ই কর্তৃত্বপূর্ণ উৎস হিসেবে বিবেচিত হওয়া উচিত। গুরুত্বপূর্ণ তথ্যের জন্য পেশাদার মানব অনুবাদ গ্রহণ করার পরামর্শ দেওয়া হয়। এই অনুবাদের ব্যবহারে সৃষ্ট কোনো ভুল বোঝাবুঝি বা ভুল ব্যাখ্যার জন্য আমরা দায়ী নই।