<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:28:46+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "bn"
}
-->
# নিউরাল নেটওয়ার্ক ফ্রেমওয়ার্কস

আমরা ইতিমধ্যে শিখেছি, নিউরাল নেটওয়ার্ক দক্ষতার সঙ্গে ট্রেন করার জন্য আমাদের দুইটি কাজ করতে হয়:

* টেনসর নিয়ে কাজ করা, যেমন গুণ, যোগ করা, এবং কিছু ফাংশন যেমন সিগময়েড বা সফটম্যাক্স হিসাব করা
* সব এক্সপ্রেশনের গ্রেডিয়েন্ট হিসাব করা, যাতে গ্রেডিয়েন্ট ডিসেন্ট অপটিমাইজেশন করা যায়

যখন `numpy` লাইব্রেরি প্রথম কাজটি করতে পারে, তখন গ্রেডিয়েন্ট হিসাব করার জন্য আমাদের একটি পদ্ধতি দরকার। আমাদের ফ্রেমওয়ার্কে, যা আমরা আগের অংশে তৈরি করেছি, আমাদের ম্যানুয়ালি সব ডেরিভেটিভ ফাংশন `backward` মেথডে প্রোগ্রাম করতে হয়েছিল, যা ব্যাকপ্রোপাগেশন করে। আদর্শভাবে, একটি ফ্রেমওয়ার্ক আমাদের এমন সুযোগ দেয় যে আমরা *যেকোনো এক্সপ্রেশনের* গ্রেডিয়েন্ট হিসাব করতে পারি যা আমরা সংজ্ঞায়িত করতে পারি।

আরেকটি গুরুত্বপূর্ণ বিষয় হলো GPU বা অন্য কোনো বিশেষায়িত কম্পিউটিং ইউনিট যেমন TPU তে কম্পিউটেশন চালানো। ডিপ নিউরাল নেটওয়ার্ক ট্রেনিং অনেক বেশি কম্পিউটেশন প্রয়োজন, এবং GPU তে সেই কম্পিউটেশনগুলো প্যারালালাইজ করা খুবই গুরুত্বপূর্ণ।

> ✅ 'প্যারালালাইজ' শব্দের অর্থ হলো কম্পিউটেশনগুলো একাধিক ডিভাইসে ভাগ করে দেওয়া।

বর্তমানে, সবচেয়ে জনপ্রিয় দুইটি নিউরাল ফ্রেমওয়ার্ক হলো: TensorFlow এবং PyTorch। উভয়ই CPU এবং GPU উভয় প্ল্যাটফর্মে টেনসর নিয়ে কাজ করার জন্য লো-লেভেল API দেয়। লো-লেভেল API এর উপরে, উচ্চ-লেভেল API রয়েছে, যথাক্রমে Keras এবং PyTorch Lightning নামে।

লো-লেভেল API | TensorFlow | PyTorch  
--------------|-------------|---------  
উচ্চ-লেভেল API | Keras | PyTorch Lightning

**লো-লেভেল API** উভয় ফ্রেমওয়ার্কে আপনাকে **কম্পিউটেশনাল গ্রাফ** তৈরি করার সুযোগ দেয়। এই গ্রাফটি নির্ধারণ করে কিভাবে আউটপুট (সাধারণত লস ফাংশন) ইনপুট প্যারামিটার দিয়ে হিসাব করা হবে, এবং GPU থাকলে সেখানে কম্পিউটেশন চালানো যায়। এই কম্পিউটেশনাল গ্রাফের ডিফারেনশিয়েশন এবং গ্রেডিয়েন্ট হিসাব করার ফাংশন রয়েছে, যা মডেল প্যারামিটার অপটিমাইজেশনে ব্যবহার করা হয়।

**উচ্চ-লেভেল API** নিউরাল নেটওয়ার্ককে একটি **লেয়ারের সিকোয়েন্স** হিসেবে বিবেচনা করে, এবং অধিকাংশ নিউরাল নেটওয়ার্ক তৈরি করা অনেক সহজ করে তোলে। মডেল ট্রেনিং সাধারণত ডেটা প্রস্তুত করে `fit` ফাংশন কল করার মাধ্যমে হয়।

উচ্চ-লেভেল API দিয়ে আপনি দ্রুত সাধারণ নিউরাল নেটওয়ার্ক তৈরি করতে পারেন, অনেক বিস্তারিত নিয়ে চিন্তা না করেই। একই সময়ে, লো-লেভেল API ট্রেনিং প্রক্রিয়ার উপর অনেক বেশি নিয়ন্ত্রণ দেয়, তাই গবেষণায় নতুন নিউরাল নেটওয়ার্ক আর্কিটেকচার নিয়ে কাজ করার সময় এটি বেশি ব্যবহৃত হয়।

এছাড়াও বুঝতে হবে যে, আপনি উভয় API একসাথে ব্যবহার করতে পারেন, যেমন লো-লেভেল API দিয়ে নিজের নেটওয়ার্ক লেয়ার আর্কিটেকচার তৈরি করে সেটি উচ্চ-লেভেল API দিয়ে তৈরি বড় নেটওয়ার্কে ব্যবহার করা যায়। অথবা উচ্চ-লেভেল API দিয়ে লেয়ারের সিকোয়েন্স হিসেবে নেটওয়ার্ক ডিফাইন করে, নিজের লো-লেভেল ট্রেনিং লুপ দিয়ে অপটিমাইজেশন করা যায়। উভয় API একই মৌলিক ধারণার উপর ভিত্তি করে তৈরি এবং একসাথে ভালো কাজ করার জন্য ডিজাইন করা হয়েছে।

## শেখা

এই কোর্সে আমরা PyTorch এবং TensorFlow উভয়ের জন্যই বেশিরভাগ বিষয়বস্তু প্রদান করছি। আপনি আপনার পছন্দের ফ্রেমওয়ার্ক বেছে নিয়ে সংশ্লিষ্ট নোটবুকগুলো দেখতে পারেন। যদি নিশ্চিত না হন কোন ফ্রেমওয়ার্ক বেছে নিবেন, ইন্টারনেটে **PyTorch বনাম TensorFlow** নিয়ে আলোচনা পড়তে পারেন। উভয় ফ্রেমওয়ার্ক দেখে ভালো ধারণা পাওয়া যাবে।

যেখানে সম্ভব, সরলতার জন্য আমরা উচ্চ-লেভেল API ব্যবহার করব। তবে, নিউরাল নেটওয়ার্কের কাজের মূল ধারণা বোঝার জন্য শুরুতে আমরা লো-লেভেল API এবং টেনসর নিয়ে কাজ শুরু করব। তবে আপনি দ্রুত শুরু করতে চাইলে এবং বিস্তারিত শেখার জন্য বেশি সময় দিতে না চাইলে, সেগুলো এড়িয়ে সরাসরি উচ্চ-লেভেল API নোটবুকগুলোতে যেতে পারেন।

## ✍️ অনুশীলন: ফ্রেমওয়ার্কস

নিম্নলিখিত নোটবুকগুলোতে আপনার শেখা চালিয়ে যান:

লো-লেভেল API | TensorFlow+Keras নোটবুক | PyTorch  
--------------|------------------------------|---------  
উচ্চ-লেভেল API | Keras | *PyTorch Lightning*

ফ্রেমওয়ার্কগুলো আয়ত্ত করার পর, আসুন ওভারফিটিং ধারণাটি পুনরায় দেখি।

# ওভারফিটিং

ওভারফিটিং মেশিন লার্নিংয়ের একটি অত্যন্ত গুরুত্বপূর্ণ ধারণা, এবং এটি সঠিকভাবে বোঝা খুব জরুরি!

নিচের সমস্যাটি বিবেচনা করুন যেখানে ৫টি ডট (গ্রাফে `x` দ্বারা চিহ্নিত) আনুমানিক করা হচ্ছে:

!linear | overfit  
-------------------------|--------------------------  
**লিনিয়ার মডেল, ২ প্যারামিটার** | **নন-লিনিয়ার মডেল, ৭ প্যারামিটার**  
ট্রেনিং এরর = ৫.৩ | ট্রেনিং এরর = ০  
ভ্যালিডেশন এরর = ৫.১ | ভ্যালিডেশন এরর = ২০

* বামে, আমরা একটি ভালো সরল রেখার আনুমানিক দেখতে পাচ্ছি। প্যারামিটার সংখ্যা যথেষ্ট হওয়ায়, মডেল পয়েন্টের বিন্যাসের ধারণা সঠিকভাবে বুঝতে পেরেছে।  
* ডানে, মডেলটি খুব শক্তিশালী। মাত্র ৫টি পয়েন্ট থাকলেও মডেলের ৭টি প্যারামিটার থাকার কারণে এটি সব পয়েন্টের মধ্য দিয়ে যাওয়ার মতো মানিয়ে নিতে পারে, ফলে ট্রেনিং এরর ০ হয়। তবে এতে মডেল ডেটার পেছনের সঠিক প্যাটার্ন বুঝতে পারে না, তাই ভ্যালিডেশন এরর অনেক বেশি।

মডেলের জটিলতা (প্যারামিটার সংখ্যা) এবং ট্রেনিং স্যাম্পলের সংখ্যা মধ্যে সঠিক ভারসাম্য রাখা খুবই গুরুত্বপূর্ণ।

## কেন ওভারফিটিং হয়

  * পর্যাপ্ত ট্রেনিং ডেটা না থাকা  
  * মডেল খুব শক্তিশালী হওয়া  
  * ইনপুট ডেটায় অনেক বেশি নয়েজ থাকা

## কিভাবে ওভারফিটিং শনাক্ত করবেন

উপরের গ্রাফ থেকে দেখা যায়, ওভারফিটিং শনাক্ত করা যায় খুব কম ট্রেনিং এরর এবং বেশি ভ্যালিডেশন এরর দেখে। সাধারণত ট্রেনিং চলাকালীন ট্রেনিং এবং ভ্যালিডেশন এরর দুটোই কমতে থাকে, কিন্তু এক পর্যায়ে ভ্যালিডেশন এরর কমা বন্ধ করে আবার বাড়তে শুরু করে। এটি ওভারফিটিংয়ের লক্ষণ, এবং এই সময়ে ট্রেনিং বন্ধ করা উচিত (অথবা মডেলের একটি স্ন্যাপশট নেওয়া উচিত)।

overfitting

## কিভাবে ওভারফিটিং প্রতিরোধ করবেন

যদি ওভারফিটিং হয়, আপনি নিম্নলিখিত কাজগুলো করতে পারেন:

 * ট্রেনিং ডেটার পরিমাণ বাড়ান  
 * মডেলের জটিলতা কমান  
 * কিছু নিয়মিতকরণ পদ্ধতি ব্যবহার করুন, যেমন Dropout, যা আমরা পরে আলোচনা করব।

## ওভারফিটিং এবং বায়াস-ভ্যারিয়েন্স ট্রেডঅফ

ওভারফিটিং আসলে পরিসংখ্যানের একটি সাধারণ সমস্যা বায়াস-ভ্যারিয়েন্স ট্রেডঅফের একটি উদাহরণ। মডেলের সম্ভাব্য ত্রুটির উৎস বিবেচনা করলে আমরা দুই ধরনের ত্রুটি দেখতে পাই:

* **বায়াস ত্রুটি** হয় যখন আমাদের অ্যালগরিদম ট্রেনিং ডেটার সম্পর্ক সঠিকভাবে ধরতে পারে না। এটি ঘটে যখন মডেল যথেষ্ট শক্তিশালী নয় (**আন্ডারফিটিং**)।  
* **ভ্যারিয়েন্স ত্রুটি** হয় যখন মডেল ইনপুট ডেটার নয়েজকে অর্থপূর্ণ সম্পর্কের পরিবর্তে আনুমানিক করে (**ওভারফিটিং**)।

ট্রেনিং চলাকালীন বায়াস ত্রুটি কমে (মডেল ডেটা আনুমানিক করতে শেখে), আর ভ্যারিয়েন্স ত্রুটি বাড়ে। ওভারফিটিং প্রতিরোধে ট্রেনিং বন্ধ করা জরুরি — ম্যানুয়ালি (ওভারফিটিং শনাক্ত করলে) অথবা স্বয়ংক্রিয়ভাবে (নিয়মিতকরণ প্রয়োগ করে)।

## উপসংহার

এই পাঠে, আপনি শিখেছেন দুইটি জনপ্রিয় AI ফ্রেমওয়ার্ক TensorFlow এবং PyTorch এর বিভিন্ন API এর পার্থক্য। এছাড়াও, একটি খুব গুরুত্বপূর্ণ বিষয় ওভারফিটিং সম্পর্কে জানলেন।

## 🚀 চ্যালেঞ্জ

সংযুক্ত নোটবুকগুলোতে নিচে 'tasks' পাবেন; নোটবুকগুলো অনুসরণ করে কাজগুলো সম্পন্ন করুন।

## পর্যালোচনা ও স্ব-অধ্যয়ন

নিম্নলিখিত বিষয়গুলো নিয়ে কিছু গবেষণা করুন:

- TensorFlow  
- PyTorch  
- ওভারফিটিং

নিজেকে প্রশ্ন করুন:

- TensorFlow এবং PyTorch এর মধ্যে পার্থক্য কী?  
- ওভারফিটিং এবং আন্ডারফিটিং এর মধ্যে পার্থক্য কী?

## অ্যাসাইনমেন্ট

এই ল্যাবে, আপনাকে PyTorch বা TensorFlow ব্যবহার করে সিঙ্গেল এবং মাল্টি-লেয়ার্ড ফুলি-কানেক্টেড নেটওয়ার্ক দিয়ে দুইটি ক্লাসিফিকেশন সমস্যা সমাধান করতে বলা হয়েছে।

**অস্বীকৃতি**:  
এই নথিটি AI অনুবাদ সেবা [Co-op Translator](https://github.com/Azure/co-op-translator) ব্যবহার করে অনূদিত হয়েছে। আমরা যথাসাধ্য সঠিকতার চেষ্টা করি, তবে স্বয়ংক্রিয় অনুবাদে ত্রুটি বা অসঙ্গতি থাকতে পারে। মূল নথিটি তার নিজস্ব ভাষায়ই কর্তৃত্বপূর্ণ উৎস হিসেবে বিবেচিত হওয়া উচিত। গুরুত্বপূর্ণ তথ্যের জন্য পেশাদার মানব অনুবাদ গ্রহণ করার পরামর্শ দেওয়া হয়। এই অনুবাদের ব্যবহারে সৃষ্ট কোনো ভুল বোঝাবুঝি বা ভুল ব্যাখ্যার জন্য আমরা দায়ী নই।