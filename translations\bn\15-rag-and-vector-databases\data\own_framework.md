<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:43:46+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "bn"
}
-->
# নিউরাল নেটওয়ার্কের পরিচিতি। মাল্টি-লেয়ারড পারসেপ্ট্রন

আগের অংশে, আপনি সবচেয়ে সহজ নিউরাল নেটওয়ার্ক মডেল সম্পর্কে শিখেছেন - একস্তরীয় পারসেপ্ট্রন, যা একটি লিনিয়ার দুই-ক্লাস শ্রেণীবিভাজন মডেল।

এই অংশে আমরা এই মডেলটিকে আরও নমনীয় ফ্রেমওয়ার্কে উন্নীত করব, যা আমাদের সক্ষম করবে:

* দুই-ক্লাসের পাশাপাশি **মাল্টি-ক্লাস শ্রেণীবিভাজন** সম্পাদন করতে
* শ্রেণীবিভাজনের পাশাপাশি **রিগ্রেশন সমস্যা** সমাধান করতে
* এমন ক্লাসগুলো আলাদা করতে যা লিনিয়ারভাবে পৃথক করা যায় না

আমরা পাইথনে আমাদের নিজস্ব মডুলার ফ্রেমওয়ার্কও তৈরি করব যা বিভিন্ন নিউরাল নেটওয়ার্ক আর্কিটেকচার গঠন করতে সাহায্য করবে।

## মেশিন লার্নিংয়ের ফরমালাইজেশন

চলুন মেশিন লার্নিং সমস্যাটিকে ফরমালাইজ করা যাক। ধরুন আমাদের কাছে একটি প্রশিক্ষণ ডেটাসেট **X** এবং লেবেলসমূহ **Y** আছে, এবং আমাদের এমন একটি মডেল *f* তৈরি করতে হবে যা সবচেয়ে সঠিক পূর্বাভাস দেবে। পূর্বাভাসের গুণগত মান পরিমাপ করা হয় **লস ফাংশন** ℒ দ্বারা। সাধারণত নিম্নলিখিত লস ফাংশনগুলো ব্যবহৃত হয়:

* রিগ্রেশন সমস্যার জন্য, যেখানে একটি সংখ্যা পূর্বাভাস করতে হয়, আমরা ব্যবহার করতে পারি **অ্যাবসোলিউট এরর** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>|, অথবা **স্কোয়ার্ড এরর** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup>
* শ্রেণীবিভাজনের জন্য, আমরা ব্যবহার করি **0-1 লস** (যা মূলত মডেলের **সঠিকতার** সমান), অথবা **লজিস্টিক লস**।

একস্তরীয় পারসেপ্ট্রনের জন্য, ফাংশন *f* একটি লিনিয়ার ফাংশন হিসেবে সংজ্ঞায়িত ছিল *f(x)=wx+b* (এখানে *w* ওজন ম্যাট্রিক্স, *x* ইনপুট ফিচারের ভেক্টর, এবং *b* বায়াস ভেক্টর)। বিভিন্ন নিউরাল নেটওয়ার্ক আর্কিটেকচারের জন্য, এই ফাংশন আরও জটিল রূপ নিতে পারে।

> শ্রেণীবিভাজনের ক্ষেত্রে, প্রায়ই নেটওয়ার্ক আউটপুট হিসেবে সংশ্লিষ্ট ক্লাসের সম্ভাবনা পাওয়া কাম্য হয়। যেকোনো সংখ্যাকে সম্ভাবনায় রূপান্তর করতে (যেমন আউটপুটকে নরমালাইজ করতে), আমরা প্রায়ই **softmax** ফাংশন σ ব্যবহার করি, এবং তখন ফাংশন *f* হয় *f(x)=σ(wx+b)*

উপরের *f* সংজ্ঞায়, *w* এবং *b* কে বলা হয় **প্যারামিটার** θ=⟨*w,b*⟩। ডেটাসেট ⟨**X**,**Y**⟩ দেওয়া থাকলে, আমরা প্যারামিটার θ এর ফাংশন হিসেবে পুরো ডেটাসেটের উপর মোট ত্রুটি হিসাব করতে পারি।

> ✅ **নিউরাল নেটওয়ার্ক প্রশিক্ষণের লক্ষ্য হলো প্যারামিটার θ পরিবর্তন করে ত্রুটি সর্বনিম্ন করা**

## গ্রেডিয়েন্ট ডিজেন্ট অপটিমাইজেশন

ফাংশন অপটিমাইজেশনের একটি সুপরিচিত পদ্ধতি হলো **গ্রেডিয়েন্ট ডিজেন্ট**। ধারণাটি হলো, আমরা লস ফাংশনের প্যারামিটারগুলোর প্রতি ডেরিভেটিভ (বহুমাত্রিক ক্ষেত্রে **গ্রেডিয়েন্ট**) হিসাব করতে পারি, এবং প্যারামিটারগুলো এমনভাবে পরিবর্তন করি যাতে ত্রুটি কমে। এটি ফরমালাইজ করা যায় এভাবে:

* প্যারামিটারগুলোকে কিছু র্যান্ডম মান দিয়ে আরম্ভ করি w<sup>(0)</sup>, b<sup>(0)</sup>
* নিম্নলিখিত ধাপটি অনেকবার পুনরাবৃত্তি করি:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

প্রশিক্ষণের সময়, অপটিমাইজেশন ধাপগুলো পুরো ডেটাসেট বিবেচনা করে হিসাব করা উচিত (মনে রাখবেন লস সব প্রশিক্ষণ নমুনার যোগফল হিসেবে হিসাব করা হয়)। তবে বাস্তবে আমরা ডেটাসেটের ছোট ছোট অংশ নিয়ে কাজ করি, যেগুলোকে **মিনিব্যাচ** বলা হয়, এবং ডেটার একটি উপসেটের উপর ভিত্তি করে গ্রেডিয়েন্ট হিসাব করি। যেহেতু প্রতিবার র্যান্ডমভাবে উপসেট নেওয়া হয়, এই পদ্ধতিকে বলা হয় **স্টোকাস্টিক গ্রেডিয়েন্ট ডিজেন্ট** (SGD)।

## মাল্টি-লেয়ারড পারসেপ্ট্রন এবং ব্যাকপ্রোপাগেশন

উপরের মতো একস্তরীয় নেটওয়ার্ক লিনিয়ারভাবে পৃথকযোগ্য ক্লাসগুলো শ্রেণীবিভাজন করতে সক্ষম। আরও সমৃদ্ধ মডেল গড়তে, আমরা নেটওয়ার্কের একাধিক স্তর একত্রিত করতে পারি। গাণিতিকভাবে এর মানে হলো ফাংশন *f* আরও জটিল রূপ নেবে এবং একাধিক ধাপে হিসাব করা হবে:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

এখানে, α একটি **নন-লিনিয়ার অ্যাক্টিভেশন ফাংশন**, σ হলো softmax ফাংশন, এবং প্যারামিটার θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*>।

গ্রেডিয়েন্ট ডিজেন্ট অ্যালগরিদম একই থাকবে, তবে গ্রেডিয়েন্ট হিসাব করা একটু কঠিন হবে। চেইন ডিফারেনশিয়েশন নিয়ম অনুযায়ী, ডেরিভেটিভগুলো হিসাব করা যায়:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ চেইন ডিফারেনশিয়েশন নিয়ম ব্যবহার করে প্যারামিটারগুলোর প্রতি লস ফাংশনের ডেরিভেটিভ হিসাব করা হয়।

মনে রাখবেন, এই সব এক্সপ্রেশনের বাম দিকের অংশ একই, তাই আমরা কার্যকরভাবে লস ফাংশন থেকে শুরু করে "পিছনে" গিয়ে ডেরিভেটিভগুলো হিসাব করতে পারি। তাই মাল্টি-লেয়ারড পারসেপ্ট্রন প্রশিক্ষণের পদ্ধতিকে বলা হয় **ব্যাকপ্রোপাগেশন**, বা সংক্ষেপে 'ব্যাকপ্রোপ'।

> TODO: ছবি উদ্ধৃতি

> ✅ আমরা আমাদের নোটবুক উদাহরণে ব্যাকপ্রোপ আরও বিস্তারিতভাবে আলোচনা করব।

## উপসংহার

এই পাঠে, আমরা আমাদের নিজস্ব নিউরাল নেটওয়ার্ক লাইব্রেরি তৈরি করেছি এবং এটি ব্যবহার করে একটি সহজ দুই-মাত্রিক শ্রেণীবিভাজন কাজ সম্পন্ন করেছি।

## 🚀 চ্যালেঞ্জ

সংযুক্ত নোটবুকে, আপনি মাল্টি-লেয়ারড পারসেপ্ট্রন তৈরি ও প্রশিক্ষণের জন্য আপনার নিজস্ব ফ্রেমওয়ার্ক বাস্তবায়ন করবেন। আপনি বিস্তারিতভাবে দেখতে পারবেন কিভাবে আধুনিক নিউরাল নেটওয়ার্ক কাজ করে।

OwnFramework নোটবুকে যান এবং কাজ শুরু করুন।

## পর্যালোচনা ও স্ব-অধ্যয়ন

ব্যাকপ্রোপাগেশন হলো AI এবং ML এ ব্যবহৃত একটি সাধারণ অ্যালগরিদম, যা আরও গভীরভাবে অধ্যয়ন করা উচিত।

## অ্যাসাইনমেন্ট

এই ল্যাবে, আপনাকে এই পাঠে তৈরি ফ্রেমওয়ার্ক ব্যবহার করে MNIST হ্যান্ডরিটেন ডিজিট শ্রেণীবিভাজন সমাধান করতে হবে।

* নির্দেশনা
* নোটবুক

**অস্বীকৃতি**:  
এই নথিটি AI অনুবাদ সেবা [Co-op Translator](https://github.com/Azure/co-op-translator) ব্যবহার করে অনূদিত হয়েছে। আমরা যথাসাধ্য সঠিকতার চেষ্টা করি, তবে স্বয়ংক্রিয় অনুবাদে ত্রুটি বা অসঙ্গতি থাকতে পারে। মূল নথিটি তার নিজস্ব ভাষায়ই কর্তৃত্বপূর্ণ উৎস হিসেবে বিবেচিত হওয়া উচিত। গুরুত্বপূর্ণ তথ্যের জন্য পেশাদার মানব অনুবাদ গ্রহণ করার পরামর্শ দেওয়া হয়। এই অনুবাদের ব্যবহারে সৃষ্ট কোনো ভুল বোঝাবুঝি বা ভুল ব্যাখ্যার জন্য আমরা দায়ী নই।