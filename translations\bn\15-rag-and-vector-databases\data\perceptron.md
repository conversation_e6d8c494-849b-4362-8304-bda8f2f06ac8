<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T16:55:54+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "bn"
}
-->
# নিউরাল নেটওয়ার্কের পরিচিতি: পারসেপ্ট্রন

আধুনিক নিউরাল নেটওয়ার্কের মতো কিছু তৈরি করার প্রথম প্রচেষ্টাগুলোর একটি ছিল ১৯৫৭ সালে কর্নেল এরোনটিক্যাল ল্যাবরেটরির ফ্রাঙ্ক রোজেনব্লাটের কাজ। এটি একটি হার্ডওয়্যার ইমপ্লিমেন্টেশন ছিল, যার নাম ছিল "Mark-1", যা প্রাথমিক জ্যামিতিক আকার যেমন ত্রিভুজ, বর্গ এবং বৃত্ত চিনতে সক্ষম ছিল।

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='Frank Rosenblatt'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> উইকিপিডিয়া থেকে ছবি

একটি ইনপুট ছবি ২০x২০ ফটোসেল অ্যারে দ্বারা উপস্থাপিত হত, তাই নিউরাল নেটওয়ার্কের ৪০০টি ইনপুট এবং একটি বাইনারি আউটপুট ছিল। একটি সাধারণ নেটওয়ার্কে একটি নিউরন ছিল, যাকে **threshold logic unit** বলা হত। নিউরাল নেটওয়ার্কের ওজনগুলি ছিল পটেনশিওমিটার-এর মতো, যা প্রশিক্ষণের সময় ম্যানুয়ালি সামঞ্জস্য করতে হত।

> ✅ পটেনশিওমিটার হল এমন একটি ডিভাইস যা ব্যবহারকারীকে সার্কিটের প্রতিরোধ ক্ষমতা সামঞ্জস্য করার সুযোগ দেয়।

> নিউ ইয়র্ক টাইমস তখন পারসেপ্ট্রন সম্পর্কে লিখেছিল: *একটি ইলেকট্রনিক কম্পিউটারের শৈশব, যা [নেভি] আশা করে যে এটি হাঁটতে, কথা বলতে, দেখতে, লিখতে, নিজেকে পুনরুত্পাদন করতে এবং নিজের অস্তিত্ব সম্পর্কে সচেতন হতে পারবে।*

## পারসেপ্ট্রন মডেল

ধরা যাক আমাদের মডেলে Nটি ফিচার আছে, তখন ইনপুট ভেক্টরটি হবে N আকারের একটি ভেক্টর। পারসেপ্ট্রন হল একটি **বাইনারি ক্লাসিফিকেশন** মডেল, অর্থাৎ এটি ইনপুট ডেটার দুইটি শ্রেণীর মধ্যে পার্থক্য করতে পারে। আমরা ধরে নেব যে প্রতিটি ইনপুট ভেক্টর x এর জন্য আমাদের পারসেপ্ট্রনের আউটপুট হবে +1 অথবা -1, শ্রেণীর উপর নির্ভর করে। আউটপুটটি নিম্নলিখিত সূত্র ব্যবহার করে গণনা করা হবে:

y(x) = f(w<sup>T</sup>x)

যেখানে f হল একটি স্টেপ অ্যাক্টিভেশন ফাংশন

## পারসেপ্ট্রন প্রশিক্ষণ

পারসেপ্ট্রন প্রশিক্ষণের জন্য আমাদের এমন একটি ওজন ভেক্টর w খুঁজে বের করতে হবে যা অধিকাংশ মান সঠিকভাবে শ্রেণীবদ্ধ করে, অর্থাৎ সর্বনিম্ন **ত্রুটি** তৈরি করে। এই ত্রুটিকে **perceptron criterion** দ্বারা নিম্নরূপ সংজ্ঞায়িত করা হয়:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

যেখানে:

* যোগফল নেওয়া হয় সেই প্রশিক্ষণ ডেটা পয়েন্ট i গুলোর উপর যেগুলো ভুল শ্রেণীবিভাজনের কারণ হয়
* x<sub>i</sub> হল ইনপুট ডেটা, এবং t<sub>i</sub> হল -1 অথবা +1, যথাক্রমে নেতিবাচক এবং ধনাত্মক উদাহরণের জন্য।

এই ক্রাইটেরিয়াকে ওজন w এর একটি ফাংশন হিসেবে বিবেচনা করা হয়, এবং আমাদের এটি সর্বনিম্ন করতে হবে। প্রায়ই, **gradient descent** নামক একটি পদ্ধতি ব্যবহার করা হয়, যেখানে আমরা কিছু প্রাথমিক ওজন w<sup>(0)</sup> দিয়ে শুরু করি, এবং প্রতিটি ধাপে ওজন আপডেট করি নিম্নলিখিত সূত্র অনুযায়ী:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

এখানে η হল **learning rate**, এবং ∇E(w) হল E এর **গ্রেডিয়েন্ট**। গ্রেডিয়েন্ট গণনার পর আমরা পাই:

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

পাইথনে অ্যালগরিদমটি এরকম দেখায়:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## উপসংহার

এই পাঠে, আপনি পারসেপ্ট্রন সম্পর্কে শিখেছেন, যা একটি বাইনারি ক্লাসিফিকেশন মডেল, এবং কীভাবে একটি ওজন ভেক্টর ব্যবহার করে এটি প্রশিক্ষণ দিতে হয়।

## 🚀 চ্যালেঞ্জ

আপনি যদি নিজে থেকে পারসেপ্ট্রন তৈরি করতে চান, তাহলে Microsoft Learn-এ এই ল্যাবটি চেষ্টা করুন, যা Azure ML ডিজাইনার ব্যবহার করে।

## পর্যালোচনা ও স্ব-অধ্যয়ন

পারসেপ্ট্রন ব্যবহার করে একটি খেলনা সমস্যা এবং বাস্তব জীবনের সমস্যাগুলো কীভাবে সমাধান করা যায় তা দেখতে এবং শেখা চালিয়ে যেতে - Perceptron নোটবুক দেখুন।

এছাড়াও পারসেপ্ট্রন সম্পর্কে একটি আকর্ষণীয় নিবন্ধ এখানে রয়েছে।

## অ্যাসাইনমেন্ট

এই পাঠে, আমরা বাইনারি ক্লাসিফিকেশন টাস্কের জন্য একটি পারসেপ্ট্রন ইমপ্লিমেন্ট করেছি, এবং এটি ব্যবহার করে দুইটি হাতের লেখা সংখ্যার মধ্যে শ্রেণীবিভাজন করেছি। এই ল্যাবে, আপনাকে সম্পূর্ণ সংখ্যার শ্রেণীবিভাজনের সমস্যা সমাধান করতে বলা হয়েছে, অর্থাৎ একটি নির্দিষ্ট ছবির সাথে কোন সংখ্যা সবচেয়ে বেশি সম্ভাব্য তা নির্ধারণ করতে হবে।

* নির্দেশনা
* নোটবুক

**অস্বীকৃতি**:  
এই নথিটি AI অনুবাদ সেবা [Co-op Translator](https://github.com/Azure/co-op-translator) ব্যবহার করে অনূদিত হয়েছে। আমরা যথাসাধ্য সঠিকতার চেষ্টা করি, তবে স্বয়ংক্রিয় অনুবাদে ত্রুটি বা অসঙ্গতি থাকতে পারে। মূল নথিটি তার নিজস্ব ভাষায়ই কর্তৃত্বপূর্ণ উৎস হিসেবে বিবেচিত হওয়া উচিত। গুরুত্বপূর্ণ তথ্যের জন্য পেশাদার মানব অনুবাদ গ্রহণ করার পরামর্শ দেওয়া হয়। এই অনুবাদের ব্যবহারে সৃষ্ট কোনো ভুল বোঝাবুঝি বা ভুল ব্যাখ্যার জন্য আমরা দায়ী নই।