<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "0bba96e53ab841d99db731892a51fab8",
  "translation_date": "2025-07-09T17:07:03+00:00",
  "source_file": "16-open-source-models/README.md",
  "language_code": "bn"
}
-->
[![Open Source Models](../../../translated_images/16-lesson-banner.6b56555e8404fda1716382db4832cecbe616ccd764de381f0af6cfd694d05f74.bn.png)](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst)

## পরিচিতি

ওপেন-সোর্স LLMs এর জগৎ উত্তেজনাপূর্ণ এবং ক্রমাগত পরিবর্তিত হচ্ছে। এই পাঠের উদ্দেশ্য হলো ওপেন সোর্স মডেলগুলোর একটি গভীর দৃষ্টিভঙ্গি প্রদান করা। যদি আপনি জানতে চান কিভাবে প্রোপাইটারি মডেলগুলি ওপেন সোর্স মডেলগুলোর সাথে তুলনা করে, তাহলে ["Exploring and Comparing Different LLMs" পাঠে](../02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst) যান। এই পাঠে ফাইন-টিউনিং সম্পর্কেও আলোচনা করা হবে, তবে এর বিস্তারিত ব্যাখ্যা ["Fine-Tuning LLMs" পাঠে](../18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst) পাওয়া যাবে।

## শেখার লক্ষ্যসমূহ

- ওপেন সোর্স মডেল সম্পর্কে ধারণা লাভ করা
- ওপেন সোর্স মডেল নিয়ে কাজ করার সুবিধাগুলো বোঝা
- Hugging Face এবং Azure AI Studio তে উপলব্ধ ওপেন মডেলগুলো অন্বেষণ করা

## ওপেন সোর্স মডেল কী?

ওপেন সোর্স সফটওয়্যার প্রযুক্তির বিভিন্ন ক্ষেত্রে বিকাশে গুরুত্বপূর্ণ ভূমিকা পালন করেছে। Open Source Initiative (OSI) [১০টি মানদণ্ড নির্ধারণ করেছে](https://web.archive.org/web/20241126001143/https://opensource.org/osd?WT.mc_id=academic-105485-koreyst) যা সফটওয়্যারকে ওপেন সোর্স হিসেবে শ্রেণীবদ্ধ করতে হয়। সোর্স কোড অবশ্যই OSI অনুমোদিত লাইসেন্সের অধীনে উন্মুক্তভাবে শেয়ার করতে হবে।

যদিও LLMs এর উন্নয়ন সফটওয়্যার উন্নয়নের সাথে কিছু মিল রয়েছে, পুরো প্রক্রিয়াটি একদম একই নয়। এই কারণে LLMs এর প্রেক্ষাপটে ওপেন সোর্সের সংজ্ঞা নিয়ে সম্প্রদায়ে অনেক আলোচনা হয়েছে। একটি মডেলকে ঐতিহ্যবাহী ওপেন সোর্স সংজ্ঞার সাথে সামঞ্জস্যপূর্ণ করতে নিম্নলিখিত তথ্যগুলো প্রকাশ্যে থাকা উচিত:

- মডেল ট্রেনিংয়ের জন্য ব্যবহৃত ডেটাসেট।
- ট্রেনিংয়ের অংশ হিসেবে সম্পূর্ণ মডেল ওজন।
- মূল্যায়ন কোড।
- ফাইন-টিউনিং কোড।
- সম্পূর্ণ মডেল ওজন এবং ট্রেনিং মেট্রিক্স।

বর্তমানে খুব কম মডেলই এই মানদণ্ড পূরণ করে। [Allen Institute for Artificial Intelligence (AllenAI) দ্বারা তৈরি OLMo মডেল](https://huggingface.co/allenai/OLMo-7B?WT.mc_id=academic-105485-koreyst) এই ক্যাটাগরিতে পড়ে।

এই পাঠে, আমরা এগুলোকে "ওপেন মডেল" হিসেবে উল্লেখ করব কারণ লেখার সময় এগুলো উপরের মানদণ্ডের সাথে পুরোপুরি মিলে নাও যেতে পারে।

## ওপেন মডেলের সুবিধাসমূহ

**অত্যন্ত কাস্টমাইজযোগ্য** - ওপেন মডেলগুলো বিস্তারিত ট্রেনিং তথ্যসহ প্রকাশিত হওয়ায় গবেষক এবং ডেভেলপাররা মডেলের অভ্যন্তরীণ অংশগুলো পরিবর্তন করতে পারেন। এর ফলে নির্দিষ্ট কাজ বা গবেষণার জন্য বিশেষায়িত মডেল তৈরি করা সম্ভব হয়। এর কিছু উদাহরণ হলো কোড জেনারেশন, গাণিতিক অপারেশন এবং জীববিজ্ঞান।

**খরচ** - এই মডেলগুলো ব্যবহার এবং ডিপ্লয়মেন্টের জন্য প্রতি টোকেন খরচ প্রোপাইটারি মডেলগুলোর তুলনায় কম। জেনারেটিভ AI অ্যাপ্লিকেশন তৈরি করার সময়, আপনার ব্যবহারের ক্ষেত্রে পারফরম্যান্স বনাম মূল্য বিবেচনা করা উচিত।

![Model Cost](../../../translated_images/model-price.3f5a3e4d32ae00b465325159e1f4ebe7b5861e95117518c6bfc37fe842950687.bn.png)  
উৎস: Artificial Analysis

**নমনীয়তা** - ওপেন মডেল নিয়ে কাজ করলে আপনি বিভিন্ন মডেল ব্যবহার বা একত্রিত করার ক্ষেত্রে নমনীয় হতে পারেন। এর একটি উদাহরণ হলো [HuggingChat Assistants](https://huggingface.co/chat?WT.mc_id=academic-105485-koreyst), যেখানে ব্যবহারকারী সরাসরি ইউজার ইন্টারফেস থেকে মডেল নির্বাচন করতে পারেন:

![Choose Model](../../../translated_images/choose-model.f095d15bbac922141591fd4fac586dc8d25e69b42abf305d441b84c238e293f2.bn.png)

## বিভিন্ন ওপেন মডেল অন্বেষণ

### Llama 2

[LLama2](https://huggingface.co/meta-llama?WT.mc_id=academic-105485-koreyst), মেটা দ্বারা উন্নত একটি ওপেন মডেল যা চ্যাট ভিত্তিক অ্যাপ্লিকেশনের জন্য অপ্টিমাইজ করা হয়েছে। এর ফাইন-টিউনিং পদ্ধতিতে প্রচুর ডায়ালগ এবং মানব প্রতিক্রিয়া অন্তর্ভুক্ত ছিল। এই পদ্ধতির ফলে মডেলটি মানুষের প্রত্যাশার সাথে আরও সামঞ্জস্যপূর্ণ ফলাফল দেয়, যা ব্যবহারকারীর অভিজ্ঞতাকে উন্নত করে।

Llama এর কিছু ফাইন-টিউন করা সংস্করণ হলো [Japanese Llama](https://huggingface.co/elyza/ELYZA-japanese-Llama-2-7b?WT.mc_id=academic-105485-koreyst), যা জাপানি ভাষায় বিশেষজ্ঞ এবং [Llama Pro](https://huggingface.co/TencentARC/LLaMA-Pro-8B?WT.mc_id=academic-105485-koreyst), যা বেস মডেলের উন্নত সংস্করণ।

### Mistral

[Mistral](https://huggingface.co/mistralai?WT.mc_id=academic-105485-koreyst) একটি ওপেন মডেল যা উচ্চ কর্মক্ষমতা এবং দক্ষতার উপর জোর দেয়। এটি Mixture-of-Experts পদ্ধতি ব্যবহার করে, যেখানে বিশেষায়িত একাধিক এক্সপার্ট মডেল একত্রিত হয়ে একটি সিস্টেম গঠন করে এবং ইনপুট অনুযায়ী নির্দিষ্ট মডেলগুলো নির্বাচন করা হয়। এর ফলে গণনা আরও কার্যকর হয় কারণ মডেলগুলো শুধুমাত্র তাদের বিশেষায়িত ইনপুটগুলো নিয়ে কাজ করে।

Mistral এর কিছু ফাইন-টিউন করা সংস্করণ হলো [BioMistral](https://huggingface.co/BioMistral/BioMistral-7B?text=Mon+nom+est+Thomas+et+mon+principal?WT.mc_id=academic-105485-koreyst), যা চিকিৎসা ক্ষেত্রে ফোকাস করে এবং [OpenMath Mistral](https://huggingface.co/nvidia/OpenMath-Mistral-7B-v0.1-hf?WT.mc_id=academic-105485-koreyst), যা গাণিতিক গণনা সম্পাদন করে।

### Falcon

[Falcon](https://huggingface.co/tiiuae?WT.mc_id=academic-105485-koreyst) হলো Technology Innovation Institute (**TII**) দ্বারা তৈরি একটি LLM। Falcon-40B ৪০ বিলিয়ন প্যারামিটারে প্রশিক্ষিত, যা কম কম্পিউটিং বাজেটে GPT-3 এর চেয়ে ভালো পারফরম্যান্স দেখিয়েছে। এর কারণ হলো FlashAttention অ্যালগরিদম এবং multiquery attention ব্যবহার, যা ইনফারেন্স সময় মেমোরি প্রয়োজনীয়তা কমায়। এই কম ইনফারেন্স সময়ের কারণে Falcon-40B চ্যাট অ্যাপ্লিকেশনের জন্য উপযুক্ত।

Falcon এর কিছু ফাইন-টিউন করা সংস্করণ হলো [OpenAssistant](https://huggingface.co/OpenAssistant/falcon-40b-sft-top1-560?WT.mc_id=academic-105485-koreyst), যা ওপেন মডেলের উপর ভিত্তি করে তৈরি একটি সহকারী এবং [GPT4ALL](https://huggingface.co/nomic-ai/gpt4all-falcon?WT.mc_id=academic-105485-koreyst), যা বেস মডেলের চেয়ে উন্নত পারফরম্যান্স প্রদান করে।

## কিভাবে নির্বাচন করবেন

একটি ওপেন মডেল নির্বাচন করার জন্য একক উত্তর নেই। শুরু করার জন্য Azure AI Studio এর টাস্ক ফিল্টার ব্যবহার করা ভালো। এটি আপনাকে বুঝতে সাহায্য করবে মডেলটি কোন ধরনের কাজের জন্য প্রশিক্ষিত। Hugging Face একটি LLM Leaderboard রক্ষণাবেক্ষণ করে, যা নির্দিষ্ট মেট্রিক্সের ভিত্তিতে সেরা পারফর্মিং মডেলগুলো দেখায়।

বিভিন্ন ধরনের LLM তুলনা করার জন্য [Artificial Analysis](https://artificialanalysis.ai/?WT.mc_id=academic-105485-koreyst) আরেকটি চমৎকার উৎস:

![Model Quality](../../../translated_images/model-quality.aaae1c22e00f7ee1cd9dc186c611ac6ca6627eabd19e5364dce9e216d25ae8a5.bn.png)  
উৎস: Artificial Analysis

নির্দিষ্ট কোনো ব্যবহারের ক্ষেত্রে, একই ক্ষেত্রে ফোকাস করা ফাইন-টিউন করা সংস্করণগুলো খোঁজা কার্যকর হতে পারে। একাধিক ওপেন মডেল নিয়ে পরীক্ষা-নিরীক্ষা করে দেখা যে সেগুলো আপনার এবং আপনার ব্যবহারকারীদের প্রত্যাশা অনুযায়ী কেমন কাজ করে, সেটাও একটি ভালো অভ্যাস।

## পরবর্তী ধাপ

ওপেন মডেলের সবচেয়ে ভালো দিক হলো এগুলো নিয়ে কাজ শুরু করা খুব দ্রুত সম্ভব। [Azure AI Studio Model Catalog](https://ai.azure.com?WT.mc_id=academic-105485-koreyst) দেখুন, যেখানে এই মডেলগুলোর জন্য একটি নির্দিষ্ট Hugging Face কালেকশন রয়েছে যা আমরা এখানে আলোচনা করেছি।

## শেখা এখানেই শেষ নয়, যাত্রা চালিয়ে যান

এই পাঠ শেষ করার পর, আমাদের [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) দেখুন এবং আপনার Generative AI জ্ঞানে আরও উন্নতি করুন!

**অস্বীকৃতি**:  
এই নথিটি AI অনুবাদ সেবা [Co-op Translator](https://github.com/Azure/co-op-translator) ব্যবহার করে অনূদিত হয়েছে। আমরা যথাসাধ্য সঠিকতার চেষ্টা করি, তবে স্বয়ংক্রিয় অনুবাদে ত্রুটি বা অসঙ্গতি থাকতে পারে। মূল নথিটি তার নিজস্ব ভাষায়ই কর্তৃত্বপূর্ণ উৎস হিসেবে বিবেচিত হওয়া উচিত। গুরুত্বপূর্ণ তথ্যের জন্য পেশাদার মানব অনুবাদ গ্রহণ করার পরামর্শ দেওয়া হয়। এই অনুবাদের ব্যবহারে সৃষ্ট কোনো ভুল বোঝাবুঝি বা ভুল ব্যাখ্যার জন্য আমরা দায়ী নই।