<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "68664f7e754a892ae1d8d5e2b7bd2081",
  "translation_date": "2025-07-09T17:39:23+00:00",
  "source_file": "18-fine-tuning/README.md",
  "language_code": "bn"
}
-->
[![Open Source Models](../../../translated_images/18-lesson-banner.f30176815b1a5074fce9cceba317720586caa99e24001231a92fd04eeb54a121.bn.png)](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst)

# আপনার LLM ফাইন-টিউনিং

বড় ভাষার মডেল ব্যবহার করে জেনারেটিভ AI অ্যাপ্লিকেশন তৈরি করার সময় নতুন চ্যালেঞ্জ আসে। একটি প্রধান সমস্যা হলো মডেল দ্বারা নির্দিষ্ট ব্যবহারকারীর অনুরোধের জন্য তৈরি কন্টেন্টের প্রতিক্রিয়ার গুণগত মান (সঠিকতা এবং প্রাসঙ্গিকতা) নিশ্চিত করা। পূর্ববর্তী পাঠে আমরা প্রম্পট ইঞ্জিনিয়ারিং এবং রিট্রিভাল-অগমেন্টেড জেনারেশন এর মতো কৌশল নিয়ে আলোচনা করেছি, যা বিদ্যমান মডেলের প্রম্পট ইনপুট পরিবর্তন করে সমস্যার সমাধান করার চেষ্টা করে।

আজকের পাঠে, আমরা তৃতীয় একটি কৌশল আলোচনা করব, **ফাইন-টিউনিং**, যা অতিরিক্ত ডেটা দিয়ে মডেলটিকে পুনরায় প্রশিক্ষণ করে এই চ্যালেঞ্জ মোকাবেলা করার চেষ্টা করে। চলুন বিস্তারিত জানি।

## শেখার উদ্দেশ্য

এই পাঠে প্রি-ট্রেইনড ভাষার মডেলের জন্য ফাইন-টিউনিং ধারণা পরিচয় করানো হবে, এই পদ্ধতির সুবিধা ও চ্যালেঞ্জগুলি অন্বেষণ করা হবে, এবং কবে ও কীভাবে ফাইন-টিউনিং ব্যবহার করে আপনার জেনারেটিভ AI মডেলের কর্মক্ষমতা উন্নত করা যায় সে সম্পর্কে নির্দেশনা দেওয়া হবে।

এই পাঠ শেষ করার পর, আপনি নিম্নলিখিত প্রশ্নগুলোর উত্তর দিতে সক্ষম হবেন:

- ভাষার মডেলের জন্য ফাইন-টিউনিং কী?
- কখন এবং কেন ফাইন-টিউনিং কার্যকর?
- আমি কীভাবে একটি প্রি-ট্রেইনড মডেল ফাইন-টিউন করতে পারি?
- ফাইন-টিউনিংয়ের সীমাবদ্ধতাগুলো কী কী?

প্রস্তুত? শুরু করা যাক।

## চিত্রসহ গাইড

আমরা যা আলোচনা করব তার একটি বড় চিত্র পেতে চান? এই চিত্রসহ গাইডটি দেখুন, যা ফাইন-টিউনিংয়ের মূল ধারণা ও প্রেরণা থেকে শুরু করে ফাইন-টিউনিং প্রক্রিয়া এবং সেরা অনুশীলনগুলো বোঝায়। এটি একটি আকর্ষণীয় বিষয়, তাই আপনার স্ব-অধ্যয়ন যাত্রাকে সহায়তা করার জন্য অতিরিক্ত লিঙ্কের জন্য [Resources](./RESOURCES.md?WT.mc_id=academic-105485-koreyst) পেজটি দেখতেও ভুলবেন না!

![Illustrated Guide to Fine Tuning Language Models](../../../translated_images/18-fine-tuning-sketchnote.11b21f9ec8a703467a120cb79a28b5ac1effc8d8d9d5b31bbbac6b8640432e14.bn.png)

## ভাষার মডেলের জন্য ফাইন-টিউনিং কী?

সংজ্ঞা অনুযায়ী, বড় ভাষার মডেলগুলো ইন্টারনেটসহ বিভিন্ন উৎস থেকে সংগৃহীত বিশাল পরিমাণ টেক্সটের উপর _পূর্বে প্রশিক্ষিত_। পূর্ববর্তী পাঠে আমরা শিখেছি, ব্যবহারকারীর প্রশ্ন ("প্রম্পট") এর উত্তরের গুণগত মান উন্নত করতে আমাদের _প্রম্পট ইঞ্জিনিয়ারিং_ এবং _রিট্রিভাল-অগমেন্টেড জেনারেশন_ এর মতো কৌশল প্রয়োজন।

একটি জনপ্রিয় প্রম্পট-ইঞ্জিনিয়ারিং কৌশল হলো মডেলকে প্রতিক্রিয়ায় কী আশা করা হচ্ছে সে সম্পর্কে আরও নির্দেশনা দেওয়া, হয় _নির্দেশনা_ (স্পষ্ট নির্দেশ) দিয়ে অথবা _কিছু উদাহরণ_ (অস্পষ্ট নির্দেশ) দিয়ে। এটিকে বলা হয় _ফিউ-শট লার্নিং_ কিন্তু এর দুটি সীমাবদ্ধতা আছে:

- মডেলের টোকেন সীমা আপনার দেওয়া উদাহরণের সংখ্যা সীমিত করতে পারে এবং কার্যকারিতা কমাতে পারে।
- মডেলের টোকেন খরচ প্রতিটি প্রম্পটে উদাহরণ যোগ করা ব্যয়বহুল করে তোলে এবং নমনীয়তা কমায়।

ফাইন-টিউনিং হলো একটি প্রচলিত পদ্ধতি যেখানে আমরা একটি প্রি-ট্রেইনড মডেল নিয়ে নতুন ডেটা দিয়ে পুনরায় প্রশিক্ষণ করি যাতে নির্দিষ্ট কাজের জন্য এর কর্মক্ষমতা উন্নত হয়। ভাষার মডেলের ক্ষেত্রে, আমরা নির্দিষ্ট কাজ বা অ্যাপ্লিকেশন ডোমেইনের জন্য একটি নির্বাচিত উদাহরণ সেট দিয়ে প্রি-ট্রেইনড মডেলটিকে ফাইন-টিউন করতে পারি, যার ফলে একটি **কাস্টম মডেল** তৈরি হয় যা ঐ নির্দিষ্ট কাজ বা ডোমেইনের জন্য আরও সঠিক এবং প্রাসঙ্গিক হতে পারে। ফাইন-টিউনিংয়ের একটি পার্শ্বপ্রতিক্রিয়া হলো এটি ফিউ-শট লার্নিংয়ের জন্য প্রয়োজনীয় উদাহরণের সংখ্যা কমিয়ে দিতে পারে — ফলে টোকেন ব্যবহার এবং সংশ্লিষ্ট খরচও কমে।

## কখন এবং কেন মডেল ফাইন-টিউন করা উচিত?

_এই_ প্রসঙ্গে, যখন আমরা ফাইন-টিউনিং বলি, তখন আমরা **সুপারভাইজড** ফাইন-টিউনিং বোঝাই, যেখানে পুনরায় প্রশিক্ষণ করা হয় **নতুন ডেটা যোগ করে** যা মূল প্রশিক্ষণ ডেটাসেটের অংশ ছিল না। এটি ভিন্ন একটি পদ্ধতি যা হলো আনসুপারভাইজড ফাইন-টিউনিং, যেখানে মডেলকে মূল ডেটার উপর পুনরায় প্রশিক্ষণ দেওয়া হয়, কিন্তু ভিন্ন হাইপারপ্যারামিটার ব্যবহার করে।

মুখ্য বিষয় হলো ফাইন-টিউনিং একটি উন্নত কৌশল যা কাঙ্ক্ষিত ফলাফল পেতে নির্দিষ্ট দক্ষতা প্রয়োজন। ভুলভাবে করলে এটি প্রত্যাশিত উন্নতি দিতে নাও পারে, বরং আপনার লক্ষ্য ডোমেইনের জন্য মডেলের কর্মক্ষমতা খারাপও করতে পারে।

সুতরাং, "কীভাবে" ফাইন-টিউন করতে হয় শেখার আগে, আপনাকে জানতে হবে "কেন" এই পথ নেওয়া উচিত এবং "কখন" ফাইন-টিউনিং শুরু করা উচিত। নিজেকে এই প্রশ্নগুলো করুন:

- **ব্যবহার ক্ষেত্র**: আপনার ফাইন-টিউনিংয়ের _ব্যবহার ক্ষেত্র_ কী? বর্তমান প্রি-ট্রেইনড মডেলের কোন দিক উন্নত করতে চান?
- **বিকল্প পদ্ধতি**: আপনি কি _অন্যান্য কৌশল_ চেষ্টা করেছেন কাঙ্ক্ষিত ফলাফল পেতে? সেগুলো ব্যবহার করে তুলনামূলক ভিত্তি তৈরি করুন।
  - প্রম্পট ইঞ্জিনিয়ারিং: প্রাসঙ্গিক প্রম্পট প্রতিক্রিয়ার উদাহরণ দিয়ে ফিউ-শট প্রম্পটিং চেষ্টা করুন। প্রতিক্রিয়ার গুণমান মূল্যায়ন করুন।
  - রিট্রিভাল অগমেন্টেড জেনারেশন: আপনার ডেটা অনুসন্ধান করে প্রাপ্ত ফলাফল দিয়ে প্রম্পট বাড়িয়ে দেখুন। প্রতিক্রিয়ার গুণমান মূল্যায়ন করুন।
- **খরচ**: ফাইন-টিউনিংয়ের খরচ নির্ধারণ করেছেন কি?
  - টিউনেবিলিটি - প্রি-ট্রেইনড মডেলটি ফাইন-টিউনিংয়ের জন্য উপলব্ধ কি?
  - প্রচেষ্টা - প্রশিক্ষণ ডেটা প্রস্তুতি, মডেল মূল্যায়ন ও পরিমার্জনের জন্য
  - কম্পিউট - ফাইন-টিউনিং কাজ চালানো এবং ফাইন-টিউনড মডেল ডিপ্লয় করার জন্য
  - ডেটা - ফাইন-টিউনিংয়ের প্রভাবের জন্য পর্যাপ্ত মানসম্পন্ন উদাহরণ পাওয়া
- **সুবিধা**: ফাইন-টিউনিংয়ের সুবিধা নিশ্চিত করেছেন কি?
  - গুণমান - ফাইন-টিউনড মডেল কি বেসলাইনকে ছাড়িয়ে গেছে?
  - খরচ - এটি কি প্রম্পট সহজ করে টোকেন ব্যবহার কমায়?
  - সম্প্রসারণযোগ্যতা - আপনি কি বেস মডেল নতুন ডোমেইনে পুনঃব্যবহার করতে পারবেন?

এই প্রশ্নগুলোর উত্তর দিয়ে আপনি সিদ্ধান্ত নিতে পারবেন ফাইন-টিউনিং আপনার ব্যবহার ক্ষেত্রের জন্য সঠিক পদ্ধতি কিনা। আদর্শভাবে, পদ্ধতিটি তখনই গ্রহণযোগ্য যখন সুবিধাগুলো খরচের চেয়ে বেশি হয়। সিদ্ধান্ত নিলে, এখন ভাবুন কীভাবে প্রি-ট্রেইনড মডেল ফাইন-টিউন করবেন।

সিদ্ধান্ত গ্রহণ প্রক্রিয়া সম্পর্কে আরও জানতে চান? দেখুন [To fine-tune or not to fine-tune](https://www.youtube.com/watch?v=0Jo-z-MFxJs)

## কীভাবে একটি প্রি-ট্রেইনড মডেল ফাইন-টিউন করা যায়?

একটি প্রি-ট্রেইনড মডেল ফাইন-টিউন করতে আপনার প্রয়োজন:

- ফাইন-টিউন করার জন্য একটি প্রি-ট্রেইনড মডেল
- ফাইন-টিউনিংয়ের জন্য একটি ডেটাসেট
- ফাইন-টিউনিং কাজ চালানোর জন্য একটি প্রশিক্ষণ পরিবেশ
- ফাইন-টিউনড মডেল ডিপ্লয় করার জন্য একটি হোস্টিং পরিবেশ

## ফাইন-টিউনিং কার্যক্রমে

নিম্নলিখিত রিসোর্সগুলো ধাপে ধাপে টিউটোরিয়াল প্রদান করে, যা আপনাকে নির্বাচিত মডেল ও নির্বাচিত ডেটাসেট ব্যবহার করে একটি বাস্তব উদাহরণের মাধ্যমে গাইড করবে। এই টিউটোরিয়ালগুলো অনুসরণ করতে, আপনাকে নির্দিষ্ট প্রদানকারীর কাছে একটি অ্যাকাউন্ট থাকতে হবে, এবং প্রাসঙ্গিক মডেল ও ডেটাসেটের অ্যাক্সেস থাকতে হবে।

| প্রদানকারী     | টিউটোরিয়াল                                                                                                                                                                       | বর্ণনা                                                                                                                                                                                                                                                                                                                                                                                                                        |
| ------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| OpenAI       | [How to fine-tune chat models](https://github.com/openai/openai-cookbook/blob/main/examples/How_to_finetune_chat_models.ipynb?WT.mc_id=academic-105485-koreyst)                | একটি নির্দিষ্ট ডোমেইনের ("রেসিপি সহকারী") জন্য `gpt-35-turbo` মডেল ফাইন-টিউন করা শিখুন, প্রশিক্ষণ ডেটা প্রস্তুত করা, ফাইন-টিউনিং কাজ চালানো, এবং ফাইন-টিউনড মডেল ব্যবহার করে ইনফারেন্স করা।                                                                                                                                                                                                                                              |
| Azure OpenAI | [GPT 3.5 Turbo fine-tuning tutorial](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python-new%2Ccommand-line?WT.mc_id=academic-105485-koreyst) | Azure-তে `gpt-35-turbo-0613` মডেল ফাইন-টিউন করা শিখুন, প্রশিক্ষণ ডেটা তৈরি ও আপলোড করা, ফাইন-টিউনিং কাজ চালানো, নতুন মডেল ডিপ্লয় ও ব্যবহার করার ধাপগুলো অনুসরণ করুন।                                                                                                                                                                                                                                                                 |
| Hugging Face | [Fine-tuning LLMs with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | এই ব্লগ পোস্টে একটি _ওপেন LLM_ (যেমন: `CodeLlama 7B`) ফাইন-টিউন করার পদ্ধতি দেখানো হয়েছে, যেখানে [transformers](https://huggingface.co/docs/transformers/index?WT.mc_id=academic-105485-koreyst) লাইব্রেরি এবং [Transformer Reinforcement Learning (TRL)](https://huggingface.co/docs/trl/index?WT.mc_id=academic-105485-koreyst) ব্যবহার করা হয়েছে, ওপেন [ডেটাসেট](https://huggingface.co/docs/datasets/index?WT.mc_id=academic-105485-koreyst) সহ। |
|              |                                                                                                                                                                                |                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| 🤗 AutoTrain | [Fine-tuning LLMs with AutoTrain](https://github.com/huggingface/autotrain-advanced/?WT.mc_id=academic-105485-koreyst)                                                         | AutoTrain (অথবা AutoTrain Advanced) হলো Hugging Face দ্বারা তৈরি একটি পাইথন লাইব্রেরি যা বিভিন্ন কাজের জন্য ফাইন-টিউনিং সহজ করে, যার মধ্যে LLM ফাইন-টিউনিংও রয়েছে। AutoTrain একটি নো-কোড সমাধান এবং ফাইন-টিউনিং আপনার নিজস্ব ক্লাউড, Hugging Face Spaces অথবা লোকাল পরিবেশে করা যায়। এটি ওয়েব-ভিত্তিক GUI, CLI এবং yaml কনফিগ ফাইলের মাধ্যমে প্রশিক্ষণ সমর্থন করে।                                                                               |
|              |                                                                                                                                                                                |                                                                                                                                                                                                                                                                                                                                                                                                                                    |

## অ্যাসাইনমেন্ট

উপরের টিউটোরিয়ালগুলোর মধ্যে একটি নির্বাচন করুন এবং সেটি অনুসরণ করুন। _আমরা এই রেপোতে Jupyter Notebooks এ এই টিউটোরিয়ালগুলোর একটি সংস্করণ রেফারেন্স হিসেবে তৈরি করতে পারি। সর্বশেষ সংস্করণ পেতে অনুগ্রহ করে সরাসরি মূল উৎস ব্যবহার করুন_।

## অসাধারণ কাজ! আপনার শেখা চালিয়ে যান।

এই পাঠ শেষ করার পর, আমাদের [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) দেখুন এবং আপনার জেনারেটিভ AI জ্ঞান আরও উন্নত করুন!

অভিনন্দন!! আপনি এই কোর্সের v2 সিরিজের শেষ পাঠ সম্পন্ন করেছেন! শেখা এবং তৈরি করা বন্ধ করবেন না। \*\*এই বিষয়ের জন্য অতিরিক্ত পরামর্শের তালিকার জন্য [RESOURCES](RESOURCES.md?WT.mc_id=academic-105485-koreyst) পেজটি দেখুন।

আমাদের v1 সিরিজের পাঠগুলোও নতুন অ্যাসাইনমেন্ট এবং ধারণা নিয়ে আপডেট হয়েছে। তাই একটু সময় নিয়ে আপনার জ্ঞান রিফ্রেশ করুন - এবং আমাদের এই পাঠগুলো সম্প্রদায়ের জন্য আরও উন্নত করতে [আপনার প্রশ্ন ও মতামত শেয়ার করুন](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst)।

**অস্বীকৃতি**:  
এই নথিটি AI অনুবাদ সেবা [Co-op Translator](https://github.com/Azure/co-op-translator) ব্যবহার করে অনূদিত হয়েছে। আমরা যথাসাধ্য সঠিকতার চেষ্টা করি, তবে স্বয়ংক্রিয় অনুবাদে ত্রুটি বা অসঙ্গতি থাকতে পারে। মূল নথিটি তার নিজস্ব ভাষায়ই কর্তৃত্বপূর্ণ উৎস হিসেবে বিবেচিত হওয়া উচিত। গুরুত্বপূর্ণ তথ্যের জন্য পেশাদার মানব অনুবাদ গ্রহণ করার পরামর্শ দেওয়া হয়। এই অনুবাদের ব্যবহারে সৃষ্ট কোনো ভুল বোঝাবুঝি বা ভুল ব্যাখ্যার জন্য আমরা দায়ী নই।