<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:58:29+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "bn"
}
-->
# স্ব-নির্দেশিত শিক্ষার জন্য সম্পদসমূহ

এই পাঠ OpenAI এবং Azure OpenAI থেকে নেওয়া বিভিন্ন মূল সম্পদ ব্যবহার করে তৈরি করা হয়েছে, যা টার্মিনোলজি এবং টিউটোরিয়ালের জন্য রেফারেন্স হিসেবে কাজ করেছে। এখানে একটি অসম্পূর্ণ তালিকা দেওয়া হলো, যা আপনার স্ব-নির্দেশিত শিক্ষার যাত্রার জন্য সহায়ক হবে।

## ১. প্রধান সম্পদসমূহ

| শিরোনাম/লিঙ্ক                                                                                                                                                                                                                   | বর্ণনা                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [OpenAI মডেল দিয়ে Fine-tuning](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning হলো একটি উন্নত পদ্ধতি যা few-shot learning এর থেকে অনেক বেশি উদাহরণ ব্যবহার করে প্রশিক্ষণ দেয়, ফলে খরচ কমে, প্রতিক্রিয়ার গুণগত মান উন্নত হয় এবং কম লেটেন্সি সহ অনুরোধ করা যায়। **OpenAI থেকে Fine-tuning এর একটি সারাংশ পান।**                                                                                    |
| [Azure OpenAI এর Fine-Tuning কী?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Fine-tuning কী (ধারণা), কেন এটি গুরুত্বপূর্ণ (প্রেরণাদায়ক সমস্যা), কোন ডেটা ব্যবহার করবেন (প্রশিক্ষণ) এবং গুণগত মান পরিমাপ করার পদ্ধতি সম্পর্কে জানুন।                                                                                                                                                                           |
| [Fine-tuning দিয়ে একটি মডেল কাস্টমাইজ করুন](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI সার্ভিস আপনাকে Fine-tuning ব্যবহার করে আপনার ব্যক্তিগত ডেটাসেট অনুযায়ী মডেল কাস্টমাইজ করার সুযোগ দেয়। Azure AI Studio, Python SDK বা REST API ব্যবহার করে **কিভাবে Fine-tuning করবেন (প্রক্রিয়া)** শিখুন।                                                                                                                                |
| [LLM Fine-tuning এর জন্য সুপারিশসমূহ](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM গুলো নির্দিষ্ট ডোমেইন, কাজ বা ডেটাসেটে ভালো পারফর্ম নাও করতে পারে, অথবা ভুল বা বিভ্রান্তিকর আউটপুট দিতে পারে। **কখন Fine-tuning বিবেচনা করবেন** এই সমস্যার সমাধান হিসেবে?                                                                                                                                  |
| [অবিচ্ছিন্ন Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | অবিচ্ছিন্ন Fine-tuning হলো একটি পুনরাবৃত্তিমূলক প্রক্রিয়া যেখানে ইতিমধ্যে Fine-tuned মডেলকে বেস মডেল হিসেবে নিয়ে নতুন প্রশিক্ষণ উদাহরণ দিয়ে **আরও Fine-tuning করা হয়**।                                                                                                                                                     |
| [Fine-tuning এবং ফাংশন কলিং](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | ফাংশন কলিং উদাহরণ সহ Fine-tuning করলে মডেলের আউটপুট আরও সঠিক এবং ধারাবাহিক হয় - একই ধরনের ফরম্যাটে প্রতিক্রিয়া পাওয়া যায় এবং খরচও কমে।                                                                                                                                        |
| [Fine-tuning মডেল: Azure OpenAI নির্দেশিকা](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Azure OpenAI তে কোন মডেলগুলো Fine-tuning করা যায় এবং কোন অঞ্চলে উপলব্ধ তা বোঝার জন্য এই টেবিলটি দেখুন। প্রয়োজনে তাদের টোকেন সীমা এবং প্রশিক্ষণ ডেটার মেয়াদ শেষ হওয়ার তারিখও দেখুন।                                                                                                                            |
| [Fine Tune করবেন নাকি করবেন না? এটাই প্রশ্ন](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | AI Show এর এই ৩০ মিনিটের **অক্টোবর ২০২৩** পর্বে Fine-tuning এর সুবিধা, অসুবিধা এবং ব্যবহারিক দিক নিয়ে আলোচনা করা হয়েছে যা আপনাকে সিদ্ধান্ত নিতে সাহায্য করবে।                                                                                                                                                                                        |
| [LLM Fine-Tuning শুরু করার জন্য](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | এই **AI Playbook** রিসোর্সটি ডেটা প্রয়োজনীয়তা, ফরম্যাটিং, হাইপারপ্যারামিটার Fine-tuning এবং চ্যালেঞ্জ/সীমাবদ্ধতা সম্পর্কে আপনাকে ধাপে ধাপে গাইড করবে।                                                                                                                                                                         |
| **টিউটোরিয়াল**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | একটি নমুনা Fine-tuning ডেটাসেট তৈরি করা, Fine-tuning এর জন্য প্রস্তুতি নেওয়া, Fine-tuning কাজ তৈরি করা এবং Azure এ Fine-tuned মডেল ডিপ্লয় করা শিখুন।                                                                                                                                                                                    |
| **টিউটোরিয়াল**: [Azure AI Studio তে Llama 2 মডেল Fine-tune করা](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio আপনাকে UI-ভিত্তিক ওয়ার্কফ্লো ব্যবহার করে বড় ভাষা মডেলগুলোকে আপনার ব্যক্তিগত ডেটাসেট অনুযায়ী কাস্টমাইজ করার সুযোগ দেয়, যা কম-কোড ডেভেলপারদের জন্য উপযোগী। এই উদাহরণটি দেখুন।                                                                                                                                                               |
| **টিউটোরিয়াল**: [Azure এ একক GPU তে Hugging Face মডেল Fine-tune করা](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | এই আর্টিকেলে বর্ণনা করা হয়েছে কিভাবে Azure DataBricks এবং Hugging Face Trainer লাইব্রেরি ব্যবহার করে একক GPU তে Hugging Face মডেল Fine-tune করবেন।                                                                                                                                                |
| **প্রশিক্ষণ:** [Azure Machine Learning দিয়ে একটি ফাউন্ডেশন মডেল Fine-tune করা](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Azure Machine Learning এর মডেল ক্যাটালগে অনেক ওপেন সোর্স মডেল রয়েছে যেগুলো আপনি আপনার নির্দিষ্ট কাজের জন্য Fine-tune করতে পারেন। এই মডিউলটি AzureML Generative AI Learning Path থেকে নেওয়া।                                                                                                                            |
| **টিউটোরিয়াল:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Microsoft Azure এ W&B ব্যবহার করে GPT-3.5 বা GPT-4 মডেল Fine-tuning করলে মডেল পারফরম্যান্সের বিস্তারিত ট্র্যাকিং এবং বিশ্লেষণ সম্ভব হয়। এই গাইডটি OpenAI Fine-Tuning গাইডের ধারণাগুলো Azure OpenAI এর জন্য বিশেষ ধাপ এবং ফিচার সহ সম্প্রসারিত করেছে।                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## ২. দ্বিতীয়ক সম্পদসমূহ

এই অংশে অতিরিক্ত কিছু সম্পদ রয়েছে যা অনুসন্ধান করার যোগ্য, কিন্তু আমরা এই পাঠে সময়ের অভাবে আলোচনা করতে পারিনি। ভবিষ্যতে এগুলোকে একটি পাঠে বা দ্বিতীয়ক অ্যাসাইনমেন্ট হিসেবে অন্তর্ভুক্ত করা হতে পারে। আপাতত, এইগুলো ব্যবহার করে এই বিষয়ে আপনার নিজস্ব দক্ষতা ও জ্ঞান গড়ে তুলুন।

| শিরোনাম/লিঙ্ক                                                                                                                                                                                                            | বর্ণনা                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [চ্যাট মডেল Fine-tuning এর জন্য ডেটা প্রস্তুতি ও বিশ্লেষণ](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | এই নোটবুকটি চ্যাট মডেল Fine-tuning এর জন্য ব্যবহৃত চ্যাট ডেটাসেট প্রিপ্রসেস এবং বিশ্লেষণের একটি টুল হিসেবে কাজ করে। এটি ফরম্যাট ত্রুটি পরীক্ষা করে, মৌলিক পরিসংখ্যান দেয় এবং Fine-tuning খরচের জন্য টোকেন গণনা অনুমান করে। দেখুন: [gpt-3.5-turbo এর Fine-tuning পদ্ধতি](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)।                                                                                                                                                                   |
| **OpenAI Cookbook**: [Qdrant সহ Retrieval Augmented Generation (RAG) এর জন্য Fine-Tuning](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | এই নোটবুকের লক্ষ্য হলো OpenAI মডেলগুলোকে Retrieval Augmented Generation (RAG) এর জন্য Fine-tune করার একটি বিস্তৃত উদাহরণ দেখানো। আমরা Qdrant এবং Few-Shot Learning একত্রিত করব যাতে মডেলের পারফরম্যান্স বাড়ে এবং ভুল তথ্য কমে।                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Weights & Biases দিয়ে GPT Fine-tuning](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) হলো AI ডেভেলপার প্ল্যাটফর্ম, যা মডেল প্রশিক্ষণ, Fine-tuning এবং ফাউন্ডেশন মডেল ব্যবহারের জন্য সরঞ্জাম সরবরাহ করে। প্রথমে তাদের [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) গাইড পড়ুন, তারপর Cookbook অনুশীলনটি চেষ্টা করুন।                                                                                                                                                                                                                  |
| **কমিউনিটি টিউটোরিয়াল** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - ছোট ভাষা মডেলের জন্য Fine-tuning                                                   | পরিচিত হন [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst) এর সাথে, মাইক্রোসফটের নতুন ছোট মডেল, যা চমকপ্রদভাবে শক্তিশালী এবং কমপ্যাক্ট। এই টিউটোরিয়াল আপনাকে Phi-2 Fine-tuning এর মাধ্যমে গাইড করবে, কিভাবে একটি অনন্য ডেটাসেট তৈরি করবেন এবং QLoRA ব্যবহার করে মডেল Fine-tune করবেন তা দেখাবে।                                                                                                                                                                       |
| **Hugging Face টিউটোরিয়াল** [২০২৪ সালে Hugging Face দিয়ে LLM Fine-Tuning কিভাবে করবেন](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | এই ব্লগ পোস্টটি আপনাকে দেখাবে কিভাবে ২০২৪ সালে Hugging Face TRL, Transformers এবং ডেটাসেট ব্যবহার করে ওপেন LLM গুলো Fine-tune করবেন। আপনি একটি ব্যবহার ক্ষেত্র নির্ধারণ করবেন, ডেভেলপমেন্ট পরিবেশ সেটআপ করবেন, ডেটাসেট প্রস্তুত করবেন, মডেল Fine-tune করবেন, পরীক্ষা-মূল্যায়ন করবেন এবং তারপর প্রোডাকশনে ডিপ্লয় করবেন।                                                                                                                                                                                                                                                                |
| **Hugging Face:** [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)                                                                                            | [অত্যাধুনিক মেশিন লার্নিং মডেলগুলোর দ্রুত এবং সহজ প্রশিক্ষণ ও ডিপ্লয়মেন্ট](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst) সরবরাহ করে। রিপোজিটরিতে Colab-সুবিধাজনক টিউটোরিয়াল এবং YouTube ভিডিও গাইডেন্স রয়েছে Fine-tuning এর জন্য। **সাম্প্রতিক [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) আপডেট প্রতিফলিত করে।** [AutoTrain ডকুমেন্টেশন](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) পড়ুন। |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**অস্বীকৃতি**:  
এই নথিটি AI অনুবাদ সেবা [Co-op Translator](https://github.com/Azure/co-op-translator) ব্যবহার করে অনূদিত হয়েছে। আমরা যথাসাধ্য সঠিকতার চেষ্টা করি, তবে স্বয়ংক্রিয় অনুবাদে ত্রুটি বা অসঙ্গতি থাকতে পারে। মূল নথিটি তার নিজস্ব ভাষায়ই কর্তৃত্বপূর্ণ উৎস হিসেবে বিবেচিত হওয়া উচিত। গুরুত্বপূর্ণ তথ্যের জন্য পেশাদার মানব অনুবাদ গ্রহণ করার পরামর্শ দেওয়া হয়। এই অনুবাদের ব্যবহারে সৃষ্ট কোনো ভুল বোঝাবুঝি বা ভুল ব্যাখ্যার জন্য আমরা দায়ী নই।