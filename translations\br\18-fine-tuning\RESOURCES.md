<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:00:39+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "br"
}
-->
# Recursos para Aprendizado Autodirigido

A lição foi construída usando diversos recursos principais da OpenAI e Azure OpenAI como referência para a terminologia e tutoriais. Aqui está uma lista não exaustiva, para suas jornadas de aprendizado autodirigido.

## 1. Recursos Primários

| Título/Link                                                                                                                                                                                                                 | Descrição                                                                                                                                                                                                                                                                                                                                                                                     |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | O fine-tuning melhora o few-shot learning ao treinar com muito mais exemplos do que cabem no prompt, economizando custos, melhorando a qualidade das respostas e permitindo requisições com menor latência. **Veja uma visão geral do fine-tuning pela OpenAI.**                                                                                                                                |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Entenda **o que é fine-tuning (conceito)**, por que você deve considerá-lo (problema motivador), quais dados usar (treinamento) e como medir a qualidade                                                                                                                                                                         |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | O Azure OpenAI Service permite que você personalize nossos modelos com seus próprios conjuntos de dados usando fine-tuning. Aprenda **como fazer fine-tuning (processo)** e selecionar modelos usando Azure AI Studio, Python SDK ou REST API.                                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLMs podem não ter bom desempenho em domínios, tarefas ou conjuntos de dados específicos, ou podem gerar respostas imprecisas ou enganosas. **Quando você deve considerar o fine-tuning** como solução possível para isso?                                                                                                                                                                    |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Fine-tuning contínuo é o processo iterativo de selecionar um modelo já fine-tuned como base e **refinar ainda mais** com novos conjuntos de exemplos de treinamento.                                                                                                                                                             |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fazer fine-tuning do seu modelo **com exemplos de chamadas de função** pode melhorar a saída do modelo, gerando respostas mais precisas e consistentes - com respostas formatadas de forma semelhante e economia de custos                                                                                                         |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Consulte esta tabela para entender **quais modelos podem ser fine-tuned** no Azure OpenAI, e em quais regiões estão disponíveis. Verifique seus limites de tokens e datas de expiração dos dados de treinamento, se necessário.                                                                                                     |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Este episódio de 30 minutos do AI Show, de **outubro de 2023**, discute benefícios, desvantagens e insights práticos para ajudar você a tomar essa decisão.                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Este recurso do **AI Playbook** guia você pelos requisitos de dados, formatação, ajuste de hiperparâmetros e desafios/limitações que você deve conhecer.                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Aprenda a criar um conjunto de dados de fine-tuning, preparar o fine-tuning, criar um job de fine-tuning e implantar o modelo fine-tuned no Azure.                                                                                                                                                                                |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | O Azure AI Studio permite personalizar grandes modelos de linguagem com seus próprios dados _usando um fluxo de trabalho baseado em UI, ideal para desenvolvedores low-code_. Veja este exemplo.                                                                                                                                   |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Este artigo descreve como fazer fine-tuning de um modelo Hugging Face com a biblioteca transformers em uma única GPU usando Azure DataBricks + bibliotecas Hugging Face Trainer                                                                                                                                                |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | O catálogo de modelos do Azure Machine Learning oferece muitos modelos open source que você pode fine-tunar para sua tarefa específica. Experimente este módulo que faz parte do [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fazer fine-tuning dos modelos GPT-3.5 ou GPT-4 no Microsoft Azure usando W&B permite um acompanhamento detalhado e análise do desempenho do modelo. Este guia amplia os conceitos do guia de Fine-Tuning da OpenAI com passos e recursos específicos para Azure OpenAI.                                                                                                                   |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                              |

## 2. Recursos Secundários

Esta seção reúne recursos adicionais que valem a pena explorar, mas que não tivemos tempo de cobrir nesta lição. Eles podem ser abordados em uma lição futura ou como opção de tarefa secundária, em outra ocasião. Por enquanto, use-os para construir seu próprio conhecimento e expertise sobre o tema.

| Título/Link                                                                                                                                                                                                            | Descrição                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Preparação e análise de dados para fine-tuning de modelos de chat](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                              | Este notebook serve como ferramenta para pré-processar e analisar o conjunto de dados de chat usado para fine-tuning de um modelo de chat. Ele verifica erros de formato, fornece estatísticas básicas e estima a contagem de tokens para custos de fine-tuning. Veja: [Método de fine-tuning para gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning para Retrieval Augmented Generation (RAG) com Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | O objetivo deste notebook é apresentar um exemplo completo de como fazer fine-tuning de modelos OpenAI para Retrieval Augmented Generation (RAG). Também integraremos Qdrant e Few-Shot Learning para melhorar o desempenho do modelo e reduzir informações falsas.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT com Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) é uma plataforma para desenvolvedores de IA, com ferramentas para treinar modelos, fazer fine-tuning e usar modelos base. Leia primeiro o guia [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) e depois experimente o exercício do Cookbook.                                                                                                                                                                                                                  |
| **Tutorial da Comunidade** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning para Pequenos Modelos de Linguagem                                         | Conheça o [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), o novo pequeno modelo da Microsoft, surpreendentemente poderoso e compacto. Este tutorial vai guiar você no fine-tuning do Phi-2, mostrando como construir um conjunto de dados único e ajustar o modelo usando QLoRA.                                                                                                                                                                       |
| **Tutorial Hugging Face** [Como fazer Fine-Tuning de LLMs em 2024 com Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                         | Este post no blog explica como fazer fine-tuning de LLMs abertos usando Hugging Face TRL, Transformers e datasets em 2024. Você define um caso de uso, configura o ambiente de desenvolvimento, prepara o conjunto de dados, faz o fine-tuning do modelo, testa e avalia, e depois implanta em produção.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Oferece treinamentos e implantações mais rápidos e fáceis de [modelos de machine learning de ponta](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). O repositório tem tutoriais compatíveis com Colab e vídeos no YouTube para guiar o fine-tuning. **Reflete a recente atualização [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)**. Leia a [documentação do AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Aviso Legal**:  
Este documento foi traduzido utilizando o serviço de tradução por IA [Co-op Translator](https://github.com/Azure/co-op-translator). Embora nos esforcemos para garantir a precisão, esteja ciente de que traduções automáticas podem conter erros ou imprecisões. O documento original em seu idioma nativo deve ser considerado a fonte autorizada. Para informações críticas, recomenda-se tradução profissional humana. Não nos responsabilizamos por quaisquer mal-entendidos ou interpretações incorretas decorrentes do uso desta tradução.