<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:53:05+00:00",
  "source_file": "SECURITY.md",
  "language_code": "br"
}
-->
## Segurança

A Microsoft leva a segurança de nossos produtos e serviços de software muito a sério, incluindo todos os repositórios de código-fonte gerenciados por meio de nossas organizações no GitHub, que incluem [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) e [nossas organizações no GitHub](https://opensource.microsoft.com/).

Se você acredita ter encontrado uma vulnerabilidade de segurança em algum repositório de propriedade da Microsoft que se enquadre na [definição de vulnerabilidade de segurança da Microsoft](https://aka.ms/opensource/security/definition), por favor, reporte-a conforme descrito abaixo.

## Reportando Problemas de Segurança

**Por favor, não reporte vulnerabilidades de segurança por meio de issues públicas no GitHub.**

Em vez disso, reporte-as ao Microsoft Security Response Center (MSRC) em [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Se preferir enviar sem fazer login, envie um e-mail para [<EMAIL>](mailto:<EMAIL>). Se possível, criptografe sua mensagem com nossa chave PGP; faça o download na [página da chave PGP do Microsoft Security Response Center](https://aka.ms/opensource/security/pgpkey).

Você deve receber uma resposta em até 24 horas. Se por algum motivo não receber, por favor, faça um acompanhamento por e-mail para garantir que recebemos sua mensagem original. Informações adicionais podem ser encontradas em [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Por favor, inclua as informações solicitadas abaixo (na medida do possível) para nos ajudar a entender melhor a natureza e o escopo do possível problema:

  * Tipo de problema (ex.: estouro de buffer, injeção SQL, cross-site scripting, etc.)
  * Caminhos completos dos arquivos-fonte relacionados à manifestação do problema
  * Localização do código-fonte afetado (tag/branch/commit ou URL direto)
  * Qualquer configuração especial necessária para reproduzir o problema
  * Instruções passo a passo para reproduzir o problema
  * Código de prova de conceito ou exploit (se possível)
  * Impacto do problema, incluindo como um atacante poderia explorá-lo

Essas informações nos ajudarão a analisar seu relatório com mais rapidez.

Se você estiver reportando para um programa de recompensa por bugs, relatórios mais completos podem contribuir para uma recompensa maior. Por favor, visite nossa página do [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) para mais detalhes sobre nossos programas ativos.

## Idiomas Preferenciais

Preferimos que todas as comunicações sejam em inglês.

## Política

A Microsoft segue o princípio de [Divulgação Coordenada de Vulnerabilidades](https://aka.ms/opensource/security/cvd).

**Aviso Legal**:  
Este documento foi traduzido utilizando o serviço de tradução por IA [Co-op Translator](https://github.com/Azure/co-op-translator). Embora nos esforcemos para garantir a precisão, esteja ciente de que traduções automáticas podem conter erros ou imprecisões. O documento original em seu idioma nativo deve ser considerado a fonte autorizada. Para informações críticas, recomenda-se tradução profissional humana. Não nos responsabilizamos por quaisquer mal-entendidos ou interpretações incorretas decorrentes do uso desta tradução.