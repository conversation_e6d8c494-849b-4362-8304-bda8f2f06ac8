<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:07:47+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "cs"
}
-->
# Zdroje pro samostatné učení

Lekce byla vytvořena s využitím <PERSON><PERSON> k<PERSON> zdrojů od OpenAI a Azure OpenAI jako referencí pro terminologii a návody. Zde je nevyčerpávající seznam pro vaše vlastní samostatné studium.

## 1. <PERSON><PERSON><PERSON><PERSON> zdro<PERSON>

| Název/Odkaz                                                                                                                                                                                                                 | Popis                                                                                                                                                                                                                                                                                                                                                                                        |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning zlepšuje few-shot learning tím, že trénuje na mnohem větším množství příkladů, než kolik se vejde do promptu, což šetří náklady, zvyšuje kvalitu odpovědí a umožňuje rychlejší odezvy. **Získejte přehled o fine-tuningu od OpenAI.**                                                                                                                                            |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Pochopte **co je fine-tuning (koncept)**, proč byste ho měli zvážit (motivující problém), jaká data použít (trénink) a jak měřit kvalitu.                                                                                                                                                                                                                                                  |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service vám umožňuje přizpůsobit modely vašim vlastním datovým sadám pomocí fine-tuningu. Naučte se **jak provést fine-tuning (proces)** vybraných modelů pomocí Azure AI Studia, Python SDK nebo REST API.                                                                                                                                                                      |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM nemusí dobře fungovat na specifických doménách, úlohách nebo datech, nebo mohou produkovat nepřesné či zavádějící výstupy. **Kdy byste měli zvážit fine-tuning** jako možné řešení?                                                                                                                                                                                                     |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Kontinuální fine-tuning je iterativní proces, kdy se již jednou doladěný model použije jako základ a **dále se doladí** na nových sadách tréninkových příkladů.                                                                                                                                                                                                                              |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fine-tuning modelu **s příklady volání funkcí** může zlepšit výstupy modelu tím, že zajistí přesnější a konzistentnější odpovědi – s podobně formátovanými reakcemi a úsporou nákladů.                                                                                                                                                                                                       |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Podívejte se na tuto tabulku, abyste pochopili, **které modely lze v Azure OpenAI doladit**, a ve kterých regionech jsou dostupné. Zjistěte jejich limity tokenů a data expirace tréninkových dat, pokud je to potřeba.                                                                                                                                                                      |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Tento 30minutový díl AI Show z října 2023 rozebírá výhody, nevýhody a praktické poznatky, které vám pomohou rozhodnout se.                                                                                                                                                                                                                                                                    |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Tento **AI Playbook** vás provede požadavky na data, formátováním, laděním hyperparametrů a výzvami/omezeními, která byste měli znát.                                                                                                                                                                                                                                                        |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Naučte se vytvořit ukázkovou datovou sadu pro fine-tuning, připravit se na fine-tuning, vytvořit úlohu fine-tuningu a nasadit doladěný model na Azure.                                                                                                                                                                                                                                         |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio vám umožňuje přizpůsobit velké jazykové modely vašim vlastním datům _pomocí uživatelského rozhraní vhodného pro low-code vývojáře_. Podívejte se na tento příklad.                                                                                                                                                                                                          |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Tento článek popisuje, jak doladit model Hugging Face pomocí knihovny Hugging Face transformers na jednom GPU s Azure DataBricks a knihovnami Hugging Face Trainer.                                                                                                                                                                                                                            |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Katalog modelů v Azure Machine Learning nabízí mnoho open source modelů, které můžete doladit pro svůj konkrétní úkol. Vyzkoušejte tento modul z [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst).                                                                       |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fine-tuning modelů GPT-3.5 nebo GPT-4 na Microsoft Azure pomocí W&B umožňuje podrobné sledování a analýzu výkonu modelu. Tento průvodce rozšiřuje koncepty z OpenAI Fine-Tuning průvodce o specifické kroky a funkce pro Azure OpenAI.                                                                                                                                                      |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                             |

## 2. Vedlejší zdroje

Tato sekce obsahuje další zdroje, které stojí za to prozkoumat, ale na které jsme v této lekci neměli čas. Mohou být zahrnuty v budoucí lekci nebo jako volitelný úkol později. Prozatím je využijte k rozšíření svých znalostí a dovedností v této oblasti.

| Název/Odkaz                                                                                                                                                                                                            | Popis                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Příprava a analýza dat pro fine-tuning chat modelu](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                              | Tento notebook slouží jako nástroj pro předzpracování a analýzu datové sady chatu používané pro fine-tuning chat modelu. Kontroluje formátové chyby, poskytuje základní statistiky a odhaduje počet tokenů pro náklady na fine-tuning. Viz: [Metoda fine-tuningu pro gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning pro Retrieval Augmented Generation (RAG) s Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)         | Cílem tohoto notebooku je provést komplexní příklad, jak doladit OpenAI modely pro Retrieval Augmented Generation (RAG). Také integrujeme Qdrant a Few-Shot Learning pro zvýšení výkonu modelu a snížení falešných informací.                                                                                                                                                                                                                                  |
| **OpenAI Cookbook**: [Fine-tuning GPT s Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                               | Weights & Biases (W&B) je platforma pro AI vývojáře s nástroji pro trénink modelů, fine-tuning a využití základních modelů. Nejprve si přečtěte jejich průvodce [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst), poté vyzkoušejte cvičení v Cookbooku.                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning pro malé jazykové modely                                                      | Seznamte se s [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), novým malým modelem od Microsoftu, který je překvapivě výkonný a zároveň kompaktní. Tento tutoriál vás provede fine-tuningem Phi-2, ukáže, jak vytvořit unikátní datovou sadu a doladit model pomocí QLoRA.                                                                                                                                               |
| **Hugging Face Tutorial** [Jak doladit LLM v roce 2024 s Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                                     | Tento blogový příspěvek vás provede, jak doladit otevřené LLM pomocí Hugging Face TRL, Transformers a datasetů v roce 2024. Definujete případ použití, nastavíte vývojové prostředí, připravíte datovou sadu, doladíte model, otestujete a vyhodnotíte ho a nakonec nasadíte do produkce.                                                                                                                                                                      |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Přináší rychlejší a jednodušší trénink a nasazení [nejmodernějších modelů strojového učení](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Repo obsahuje tutoriály přátelské k Colab s video návody na YouTube pro fine-tuning. **Zohledňuje nedávnou [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) aktualizaci**. Přečtěte si [dokumentaci AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst). |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |

**Prohlášení o vyloučení odpovědnosti**:  
Tento dokument byl přeložen pomocí AI překladatelské služby [Co-op Translator](https://github.com/Azure/co-op-translator). I když usilujeme o přesnost, mějte prosím na paměti, že automatické překlady mohou obsahovat chyby nebo nepřesnosti. Původní dokument v jeho mateřském jazyce by měl být považován za autoritativní zdroj. Pro důležité informace se doporučuje profesionální lidský překlad. Nejsme odpovědní za jakékoliv nedorozumění nebo nesprávné výklady vyplývající z použití tohoto překladu.