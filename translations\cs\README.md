<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:43:20+00:00",
  "source_file": "README.md",
  "language_code": "cs"
}
-->
![Generative AI pro začátečníky](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.cs.png)

### 21 lekc<PERSON>, které vás naučí vše, co potřebujete vědět pro začátek tvorby aplikací s generativní AI

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Podpora více jazyků

#### Podporováno přes GitHub Action (automatické a vždy aktuální)

[Francouzština](../fr/README.md) | [Španělština](../es/README.md) | [Němčina](../de/README.md) | [Ruština](../ru/README.md) | [Arabština](../ar/README.md) | [Perština (Fársí)](../fa/README.md) | [Urdu](../ur/README.md) | [Čínština (zjednodušená)](../zh/README.md) | [Čínština (tradiční, Macao)](../mo/README.md) | [Čínština (tradiční, Hongkong)](../hk/README.md) | [Čínština (tradiční, Tchaj-wan)](../tw/README.md) | [Japonština](../ja/README.md) | [Korejština](../ko/README.md) | [Hindština](../hi/README.md) | [Bengálština](../bn/README.md) | [Maráthština](../mr/README.md) | [Nepálština](../ne/README.md) | [Paňdžábština (Gurmukhí)](../pa/README.md) | [Portugalština (Portugalsko)](../pt/README.md) | [Portugalština (Brazílie)](../br/README.md) | [Italština](../it/README.md) | [Polština](../pl/README.md) | [Turečtina](../tr/README.md) | [Řečtina](../el/README.md) | [Thajština](../th/README.md) | [Švédština](../sv/README.md) | [Dánština](../da/README.md) | [Norština](../no/README.md) | [Finština](../fi/README.md) | [Nizozemština](../nl/README.md) | [Hebrejština](../he/README.md) | [Vietnamština](../vi/README.md) | [Indonéština](../id/README.md) | [Malajština](../ms/README.md) | [Tagalog (Filipínština)](../tl/README.md) | [Svahilština](../sw/README.md) | [Maďarština](../hu/README.md) | [Čeština](./README.md) | [Slovenština](../sk/README.md) | [Rumunština](../ro/README.md) | [Bulharština](../bg/README.md) | [Srbština (cyrilice)](../sr/README.md) | [Chorvatština](../hr/README.md) | [Slovinština](../sl/README.md) | [Ukrajinština](../uk/README.md) | [Barmština (Myanmar)](../my/README.md)

# Generativní AI pro začátečníky (verze 3) – kurz

Naučte se základy tvorby aplikací s generativní AI v našem komplexním kurzu o 21 lekcích od Microsoft Cloud Advocates.

## 🌱 Začínáme

Tento kurz obsahuje 21 lekcí. Každá lekce se věnuje samostatnému tématu, takže můžete začít kdekoliv chcete!

Lekce jsou označeny jako „Learn“ (Nauč se) – vysvětlují koncept generativní AI, nebo „Build“ (Postav) – vysvětlují koncept a ukazují příklady kódu v **Pythonu** a **TypeScriptu**, pokud je to možné.

Pro .NET vývojáře doporučujeme [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

Každá lekce obsahuje také sekci „Keep Learning“ (Pokračuj v učení) s dalšími vzdělávacími zdroji.

## Co potřebujete
### Pro spuštění kódu z tohoto kurzu můžete použít:
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) – **Lekce:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) – **Lekce:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) – **Lekce:** "oai-assignment"
   
- Základní znalost Pythonu nebo TypeScriptu je užitečná – \*Pro úplné začátečníky doporučujeme tyto [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) a [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) kurzy
- GitHub účet pro [forknutí celého tohoto repozitáře](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) do vašeho vlastního GitHub účtu

Vytvořili jsme lekci **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)**, která vám pomůže s nastavením vývojového prostředí.

Nezapomeňte [označit tento repozitář hvězdičkou (🌟)](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst), abyste ho později snadno našli.

## 🧠 Připraven na nasazení?

Pokud hledáte pokročilejší ukázky kódu, podívejte se na naši [sbírku ukázek kódu pro generativní AI](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) v **Pythonu** i **TypeScriptu**.

## 🗣️ Poznejte ostatní studenty, získejte podporu

Připojte se k našemu [oficiálnímu Discord serveru Azure AI Foundry](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst), kde můžete potkat a navázat kontakty s ostatními studenty tohoto kurzu a získat podporu.

Pokládejte otázky nebo sdílejte zpětnou vazbu v našem [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) na GitHubu.

## 🚀 Stavíte startup?

Zaregistrujte se do [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) a získejte **bezplatné kredity OpenAI** a až **150 000 $ na Azure kredity pro přístup k OpenAI modelům přes Azure OpenAI Services**.

## 🙏 Chcete pomoci?

Máte návrhy nebo jste našli chyby v pravopisu či kódu? [Vytvořte issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) nebo [pošlete pull request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Každá lekce obsahuje:

- Krátké video s úvodem k tématu
- Písemnou lekci v README
- Ukázky kódu v Pythonu a TypeScriptu podporující Azure OpenAI a OpenAI API
- Odkazy na další zdroje pro pokračování ve vzdělávání

## 🗃️ Lekce

| #   | **Odkaz na lekci**                                                                                                                          | **Popis**                                                                                      | **Video**                                                                   | **Další učení**                                                                |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Nastavení kurzu](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Learn:** Jak nastavit vývojové prostředí                                                    | Video brzy k dispozici                                                      | [Více informací](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Úvod do generativní AI a LLM](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                                         | **Learn:** Pochopení, co je generativní AI a jak fungují velké jazykové modely (LLM)          | [Video](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Více informací](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Prozkoumání a porovnání různých LLM](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)                 | **Learn:** Jak vybrat správný model pro váš případ použití                                    | [Video](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Více informací](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Používání generativní AI zodpovědně](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                        | **Learn:** Jak zodpovědně vytvářet aplikace s generativní AI                                  | [Video](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Více informací](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Základy prompt engineeringu](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)                                | **Learn:** Praktické osvědčené postupy v prompt engineeringu                                  | [Video](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Více informací](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Tvorba pokročilých promptů](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **Learn:** Jak aplikovat techniky prompt engineeringu, které zlepší výsledky vašich promptů   | [Video](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Více informací](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Vytváření aplikací pro generování textu](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Vytvořte:** Aplikaci pro generování textu pomocí Azure OpenAI / OpenAI API                      | [Video](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Vytváření chatovacích aplikací](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Vytvořte:** Techniky pro efektivní tvorbu a integraci chatovacích aplikací                      | [Video](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Vytváření vyhledávacích aplikací s vektorovými databázemi](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)         | **Vytvořte:** Vyhledávací aplikaci využívající Embeddings pro hledání dat                         | [Video](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Vytváření aplikací pro generování obrázků](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                          | **Vytvořte:** Aplikaci pro generování obrázků                                                    | [Video](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Vytváření AI aplikací s nízkým kódem](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Vytvořte:** Generativní AI aplikaci pomocí nástrojů Low Code                                   | [Video](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Integrace externích aplikací pomocí volání funkcí](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst)             | **Vytvořte:** Co je volání funkcí a jak se využívá v aplikacích                                 | [Video](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [Návrh UX pro AI aplikace](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                                       | **Naučte se:** Jak aplikovat principy UX designu při vývoji generativních AI aplikací            | [Video](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Zabezpečení vašich generativních AI aplikací](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Naučte se:** Hrozby a rizika pro AI systémy a způsoby, jak je zabezpečit                        | [Video](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Životní cyklus generativních AI aplikací](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)               | **Naučte se:** Nástroje a metriky pro správu životního cyklu LLM a LLMOps                        | [Video](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) a vektorové databáze](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)              | **Vytvořte:** Aplikaci využívající RAG Framework pro získávání embeddings z vektorových databází | [Video](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Open Source modely a Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                            | **Vytvořte:** Aplikaci využívající open source modely dostupné na Hugging Face                   | [Video](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI agenti](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                           | **Vytvořte:** Aplikaci využívající AI Agent Framework                                           | [Video](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [Ladění LLM](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                                        | **Naučte se:** Co, proč a jak ladit LLM                                                        | [Video](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Vytváření s SLM](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                                          | **Naučte se:** Výhody vytváření s Small Language Models                                        | Video brzy k dispozici | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Vytváření s Mistral modely](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                           | **Naučte se:** Vlastnosti a rozdíly modelů rodiny Mistral                                      | Video brzy k dispozici | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Vytváření s Meta modely](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Naučte se:** Vlastnosti a rozdíly modelů rodiny Meta                                         | Video brzy k dispozici | [Zjistit více](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Speciální poděkování

Speciální poděkování patří [**Johnu Azizovi**](https://www.linkedin.com/in/john0isaac/) za vytvoření všech GitHub Actions a workflow.

[**Bernhardu Merkleovi**](https://www.linkedin.com/in/bernhard-merkle-738b73/) za klíčové příspěvky ke každé lekci, které zlepšily zážitek z učení i práci s kódem.

## 🎒 Další kurzy

Náš tým připravuje i další kurzy! Podívejte se na:

- [**NOVÝ** Model Context Protocol pro začátečníky](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI agenti pro začátečníky](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Generativní AI pro začátečníky s .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Generativní AI pro začátečníky s JavaScriptem](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML pro začátečníky](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science pro začátečníky](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI pro začátečníky](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Kybernetická bezpečnost pro začátečníky](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Webový vývoj pro začátečníky](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT pro začátečníky](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [Vývoj XR pro začátečníky](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Ovládněte GitHub Copilot pro AI párové programování](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Ovládněte GitHub Copilot pro C#/.NET vývojáře](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Vyberte si vlastní Copilot dobrodružství](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Prohlášení o vyloučení odpovědnosti**:  
Tento dokument byl přeložen pomocí AI překladatelské služby [Co-op Translator](https://github.com/Azure/co-op-translator). I když usilujeme o přesnost, mějte prosím na paměti, že automatizované překlady mohou obsahovat chyby nebo nepřesnosti. Původní dokument v jeho mateřském jazyce by měl být považován za autoritativní zdroj. Pro důležité informace se doporučuje profesionální lidský překlad. Nejsme odpovědní za jakékoliv nedorozumění nebo nesprávné výklady vyplývající z použití tohoto překladu.