<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:55:45+00:00",
  "source_file": "SECURITY.md",
  "language_code": "cs"
}
-->
## Bezpečnost

Microsoft bere bezpečnost našich softwarových produktů a služeb vážně, což zahrnuje všechny repozitáře zdrojového kódu spravované prostřednictvím našich GitHub organizací, mezi kter<PERSON> pat<PERSON> [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) a [naše GitH<PERSON> organizace](https://opensource.microsoft.com/).

Pokud se domníváte, že jste našli bezpečnostní zranitelnost v jakémkoli repozitáři vlastněném Microsoftem, která splňuje [definici bezpečnostní zranitelnosti podle Microsoftu](https://aka.ms/opensource/security/definition), prosím nahlaste ji podle níže uvedených pokynů.

## Hlásení bezpečnostních problémů

**Prosím, nehlaste bezpečnostní zranitelnosti prostřednictvím veřejných GitHub issues.**

Místo toho je nahlaste Microsoft Security Response Center (MSRC) na [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Pokud dáváte přednost odeslání bez přihlášení, zašlete e-mail na [<EMAIL>](mailto:<EMAIL>). Pokud je to možné, zašifrujte svou zprávu pomocí našeho PGP klíče; stáhněte si jej prosím ze stránky [Microsoft Security Response Center PGP Key](https://aka.ms/opensource/security/pgpkey).

Odpověď byste měli obdržet do 24 hodin. Pokud z nějakého důvodu neobdržíte odpověď, prosím kontaktujte nás znovu e-mailem, abychom potvrdili přijetí vaší původní zprávy. Další informace najdete na [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Prosíme, zahrňte níže uvedené požadované informace (co nejvíce z nich můžete poskytnout), aby nám to pomohlo lépe pochopit povahu a rozsah možného problému:

  * Typ problému (např. přetečení bufferu, SQL injection, cross-site scripting atd.)
  * Kompletní cesty ke zdrojovým souborům souvisejícím s projevy problému
  * Umístění postiženého zdrojového kódu (tag/branch/commit nebo přímý URL)
  * Jakákoli speciální konfigurace potřebná k reprodukci problému
  * Krok za krokem instrukce k reprodukci problému
  * Proof-of-concept nebo exploit kód (pokud je to možné)
  * Dopad problému, včetně toho, jak by mohl útočník problém zneužít

Tyto informace nám pomohou rychleji zpracovat váš report.

Pokud hlásíte chybu v rámci bug bounty programu, podrobnější zprávy mohou přispět k vyšší odměně. Více informací o našich aktivních programech najdete na stránce [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty).

## Preferované jazyky

Preferujeme, aby veškerá komunikace probíhala v angličtině.

## Politika

Microsoft dodržuje princip [Koordinovaného zveřejňování zranitelností](https://aka.ms/opensource/security/cvd).

**Prohlášení o vyloučení odpovědnosti**:  
Tento dokument byl přeložen pomocí AI překladatelské služby [Co-op Translator](https://github.com/Azure/co-op-translator). I když usilujeme o přesnost, mějte prosím na paměti, že automatizované překlady mohou obsahovat chyby nebo nepřesnosti. Původní dokument v jeho mateřském jazyce by měl být považován za autoritativní zdroj. Pro důležité informace se doporučuje profesionální lidský překlad. Nejsme odpovědní za jakékoliv nedorozumění nebo nesprávné výklady vyplývající z použití tohoto překladu.