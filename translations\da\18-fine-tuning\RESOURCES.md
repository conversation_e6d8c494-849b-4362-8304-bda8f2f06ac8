<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:03:30+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "da"
}
-->
# Ressourcer til Selvstyret Læring

Lektionerne er bygget ved hjælp af en række centrale ressourcer fra OpenAI og Azure OpenAI som reference for terminologi og vejledninger. Her er en ikke-udtømmende liste til dine egne selvstyrede læringsrejser.

## 1. Primære Ressourcer

| Titel/Link                                                                                                                                                                                                                 | Beskrivelse                                                                                                                                                                                                                                                                                                                    |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning med OpenAI Modeller](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                   | Fine-tuning forbedrer few-shot læring ved at træne på mange flere eksempler, end der kan være i prompten, hvilket sparer omkostninger, forbedrer svarenes kvalitet og muliggør lavere latenstid på forespørgsler. **Få et overblik over fine-tuning fra OpenAI.**                                                                 |
| [Hvad er Fine-Tuning med Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                     | Forstå **hvad fine-tuning er (konceptet)**, hvorfor du bør overveje det (motiverende problem), hvilken data der skal bruges (træning) og hvordan man måler kvaliteten                                                                                                                                                            |
| [Tilpas en model med fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)   | Azure OpenAI Service giver dig mulighed for at tilpasse vores modeller til dine egne datasæt ved hjælp af fine-tuning. Lær **hvordan du finjusterer (processen)** og vælger modeller via Azure AI Studio, Python SDK eller REST API.                                                                                              |
| [Anbefalinger til LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                      | LLM’er kan have svært ved at præstere godt på specifikke domæner, opgaver eller datasæt, eller de kan producere unøjagtige eller misvisende resultater. **Hvornår bør du overveje fine-tuning** som en mulig løsning?                                                                                                            |
| [Kontinuerlig Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)           | Kontinuerlig fine-tuning er den iterative proces, hvor man vælger en allerede finjusteret model som basismodel og **finjusterer den yderligere** på nye sæt træningseksempler.                                                                                                                                                 |
| [Fine-tuning og funktionskald](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                         | Fine-tuning af din model **med eksempler på funktionskald** kan forbedre modellens output ved at opnå mere præcise og konsistente svar – med svar i lignende format og omkostningsbesparelser                                                                                                                                    |
| [Fine-tuning Modeller: Azure OpenAI Vejledning](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                  | Se denne tabel for at forstå **hvilke modeller der kan finjusteres** i Azure OpenAI, og hvilke regioner de er tilgængelige i. Tjek deres token-grænser og udløbsdatoer for træningsdata, hvis nødvendigt.                                                                                                                        |
| [At finjustere eller ikke finjustere? Det er spørgsmålet](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                    | Denne 30-minutters **oktober 2023** episode af AI Show diskuterer fordele, ulemper og praktiske indsigter, der hjælper dig med at træffe denne beslutning.                                                                                                                                                                      |
| [Kom godt i gang med LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                   | Denne **AI Playbook**-ressource guider dig gennem data krav, formatering, hyperparameter fine-tuning samt udfordringer og begrænsninger, du bør kende til.                                                                                                                                                                       |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Lær at oprette et eksempel på et fine-tuning datasæt, forberede til fine-tuning, oprette et fine-tuning job og implementere den finjusterede model på Azure.                                                                                                                                                                      |
| **Tutorial**: [Finjuster en Llama 2 model i Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                    | Azure AI Studio giver dig mulighed for at tilpasse store sprogmodeller til dine egne datasæt _ved hjælp af en UI-baseret arbejdsgang, der er velegnet til low-code udviklere_. Se dette eksempel.                                                                                                                               |
| **Tutorial**: [Finjuster Hugging Face modeller på en enkelt GPU i Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)             | Denne artikel beskriver, hvordan man finjusterer en Hugging Face model med Hugging Face transformers biblioteket på en enkelt GPU med Azure DataBricks + Hugging Face Trainer biblioteker.                                                                                                                                          |
| **Træning:** [Finjuster en foundation model med Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)             | Modelkataloget i Azure Machine Learning tilbyder mange open source modeller, som du kan finjustere til din specifikke opgave. Prøv denne modul fra [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)          |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                              | Fine-tuning af GPT-3.5 eller GPT-4 modeller på Microsoft Azure ved hjælp af W&B muliggør detaljeret sporing og analyse af modelpræstation. Denne guide udvider koncepterne fra OpenAI Fine-Tuning guiden med specifikke trin og funktioner for Azure OpenAI.                                                                     |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                               |

## 2. Sekundære Ressourcer

Denne sektion indeholder yderligere ressourcer, som er værd at udforske, men som vi ikke nåede at dække i denne lektion. De kan blive dækket i en fremtidig lektion eller som en sekundær opgave på et senere tidspunkt. Brug dem for nu til at opbygge din egen ekspertise og viden om emnet.

| Titel/Link                                                                                                                                                                                                                  | Beskrivelse                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Dataforberedelse og analyse til chatmodel fine-tuning](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                              | Denne notebook fungerer som et værktøj til at forbehandle og analysere chatdatasættet, der bruges til fine-tuning af en chatmodel. Den tjekker for formatfejl, giver grundlæggende statistik og estimerer tokenantal til beregning af fine-tuning omkostninger. Se: [Fine-tuning metode for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning til Retrieval Augmented Generation (RAG) med Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)         | Formålet med denne notebook er at gennemgå et omfattende eksempel på, hvordan man finjusterer OpenAI modeller til Retrieval Augmented Generation (RAG). Vi integrerer også Qdrant og Few-Shot Learning for at forbedre modelpræstation og reducere fejlinformationer.                                                                                                                                                                                                                                                  |
| **OpenAI Cookbook**: [Fine-tuning GPT med Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                                   | Weights & Biases (W&B) er en platform for AI-udviklere med værktøjer til træning, fine-tuning og udnyttelse af foundation modeller. Læs først deres [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) guide, og prøv derefter Cookbook øvelsen.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning for små sprogmodeller                                                           | Mød [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), Microsofts nye lille model, som er bemærkelsesværdigt kraftfuld og kompakt. Denne tutorial guider dig gennem finjustering af Phi-2, viser hvordan man bygger et unikt datasæt og finjusterer modellen ved hjælp af QLoRA.                                                                                                                                                             |
| **Hugging Face Tutorial** [Sådan finjusterer du LLM’er i 2024 med Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Dette blogindlæg gennemgår, hvordan man finjusterer åbne LLM’er ved hjælp af Hugging Face TRL, Transformers og datasæt i 2024. Du definerer en brugssag, opsætter et udviklingsmiljø, forbereder et datasæt, finjusterer modellen, tester og evaluerer den, og implementerer den derefter i produktion.                                                                                                                                                                                                                   |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                                | Gør træning og implementering af [state-of-the-art maskinlæringsmodeller](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst) hurtigere og nemmere. Repo’en indeholder Colab-venlige tutorials med YouTube-videoer til finjustering. **Afspejler den seneste [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) opdatering**. Læs [AutoTrain dokumentationen](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |

**Ansvarsfraskrivelse**:  
Dette dokument er blevet oversat ved hjælp af AI-oversættelsestjenesten [Co-op Translator](https://github.com/Azure/co-op-translator). Selvom vi bestræber os på nøjagtighed, bedes du være opmærksom på, at automatiserede oversættelser kan indeholde fejl eller unøjagtigheder. Det oprindelige dokument på dets oprindelige sprog bør betragtes som den autoritative kilde. For kritisk information anbefales professionel menneskelig oversættelse. Vi påtager os intet ansvar for misforståelser eller fejltolkninger, der opstår som følge af brugen af denne oversættelse.