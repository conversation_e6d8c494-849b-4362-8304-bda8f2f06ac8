<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:54:10+00:00",
  "source_file": "SECURITY.md",
  "language_code": "da"
}
-->
## Sikkerhed

Microsoft tager sikkerheden for vores softwareprodukter og -tjenester al<PERSON>, hvilket inkluderer alle kildekoderepositorier, der administreres gennem vores GitHub-organisationer, som omfatter [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) og [vores GitHub-organisationer](https://opensource.microsoft.com/).

<PERSON><PERSON> du mener, at du har fundet en sikkerhedssårbarhed i et Microsoft-ejet repository, som opfylder [Microsofts definition af en sikkerhedssårbarhed](https://aka.ms/opensource/security/definition), bedes du rapportere det til os som beskrevet nedenfor.

## Rapportering af sikkerhedsproblemer

**Rapportér venligst ikke sikkerhedssårbarheder via offentlige GitHub-issues.**

Rapportér dem i stedet til Microsoft Security Response Center (MSRC) på [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Hvis du foretrækker at indsende uden at logge ind, send en e-mail til [<EMAIL>](mailto:<EMAIL>). Hvis muligt, krypter din besked med vores PGP-nøgle; du kan hente den fra [Microsoft Security Response Center PGP Key-siden](https://aka.ms/opensource/security/pgpkey).

Du bør modtage et svar inden for 24 timer. Hvis du af en eller anden grund ikke gør, bedes du følge op via e-mail for at sikre, at vi har modtaget din oprindelige besked. Yderligere information kan findes på [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Inkludér venligst de ønskede oplysninger nedenfor (så mange som muligt), så vi bedre kan forstå arten og omfanget af det mulige problem:

  * Type af problem (f.eks. buffer overflow, SQL injection, cross-site scripting osv.)
  * Fuld sti til kildefil(er) relateret til problemets opståen
  * Placeringen af den berørte kildekode (tag/branch/commit eller direkte URL)
  * Eventuel særlig konfiguration, der kræves for at genskabe problemet
  * Trin-for-trin instruktioner til at genskabe problemet
  * Proof-of-concept eller exploit-kode (hvis muligt)
  * Problemets konsekvenser, herunder hvordan en angriber kunne udnytte det

Disse oplysninger hjælper os med at håndtere din rapport hurtigere.

Hvis du rapporterer for en bug bounty, kan mere komplette rapporter bidrage til en højere belønning. Besøg venligst vores [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) side for flere detaljer om vores aktive programmer.

## Foretrukne sprog

Vi foretrækker, at al kommunikation foregår på engelsk.

## Politik

Microsoft følger princippet om [Koordineret Sårbarhedsafsløring](https://aka.ms/opensource/security/cvd).

**Ansvarsfraskrivelse**:  
Dette dokument er blevet oversat ved hjælp af AI-oversættelsestjenesten [Co-op Translator](https://github.com/Azure/co-op-translator). Selvom vi bestræber os på nøjagtighed, bedes du være opmærksom på, at automatiserede oversættelser kan indeholde fejl eller unøjagtigheder. Det oprindelige dokument på dets oprindelige sprog bør betragtes som den autoritative kilde. For kritisk information anbefales professionel menneskelig oversættelse. Vi påtager os intet ansvar for misforståelser eller fejltolkninger, der opstår som følge af brugen af denne oversættelse.