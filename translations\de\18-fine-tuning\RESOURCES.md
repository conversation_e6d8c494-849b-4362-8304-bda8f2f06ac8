<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:53:44+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "de"
}
-->
# Ressourcen für selbstgesteuertes Lernen

Die Lektion wurde unter Verwendung einer Reihe von Kernressourcen von OpenAI und Azure OpenAI als Referenzen für Terminologie und Tutorials erstellt. Hier ist eine nicht vollständige Liste für deine eigenen selbstgesteuerten Lernwege.

## 1. Primäre Ressourcen

| Titel/Link                                                                                                                                                                                                                   | Beschreibung                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-Tuning verbessert das Few-Shot-Lernen, indem es auf viel mehr Beispielen trainiert als im Prompt Platz finden, was Kosten spart, die Antwortqualität verbessert und Anfragen mit geringerer Latenz ermöglicht. **Verschaffe dir einen Überblick über Fine-Tuning von OpenAI.**                                                                                     |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Verstehe **was Fine-Tuning ist (Konzept)**, warum du es in Betracht ziehen solltest (Motivation), welche Daten verwendet werden (Training) und wie die Qualität gemessen wird.                                                                                                                                               |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Der Azure OpenAI Service ermöglicht es dir, unsere Modelle mit Fine-Tuning an deine eigenen Datensätze anzupassen. Erfahre **wie man fine-tuned (Prozess)** und Modelle mit Azure AI Studio, Python SDK oder REST API auswählt.                                                                                                   |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLMs können in bestimmten Domänen, Aufgaben oder Datensätzen schlechter abschneiden oder ungenaue bzw. irreführende Ergebnisse liefern. **Wann solltest du Fine-Tuning als mögliche Lösung in Betracht ziehen?**                                                                                                               |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Kontinuierliches Fine-Tuning ist der iterative Prozess, bei dem ein bereits feinabgestimmtes Modell als Basismodell ausgewählt und **weiterhin mit neuen Trainingsbeispielen feinabgestimmt** wird.                                                                                                                           |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Das Fine-Tuning deines Modells **mit Beispielen für Funktionsaufrufe** kann die Ausgabe verbessern, indem genauere und konsistentere Antworten erzeugt werden – mit ähnlich formatierten Antworten und Kosteneinsparungen.                                                                                                      |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Sieh dir diese Tabelle an, um zu verstehen, **welche Modelle in Azure OpenAI feinabgestimmt werden können** und in welchen Regionen sie verfügbar sind. Prüfe bei Bedarf deren Token-Limits und Ablaufdaten der Trainingsdaten.                                                                                                |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Diese 30-minütige Folge der AI Show aus dem **Oktober 2023** behandelt Vorteile, Nachteile und praktische Einblicke, die dir bei dieser Entscheidung helfen.                                                                                                                                                                  |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Diese **AI Playbook**-Ressource führt dich durch Datenanforderungen, Formatierung, Hyperparameter-Fine-Tuning sowie Herausforderungen und Einschränkungen, die du kennen solltest.                                                                                                                                             |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Lerne, wie du einen Beispiel-Datensatz für Fine-Tuning erstellst, dich auf das Fine-Tuning vorbereitest, einen Fine-Tuning-Job anlegst und das feinabgestimmte Modell auf Azure bereitstellst.                                                                                                                                  |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio ermöglicht es dir, große Sprachmodelle an deine eigenen Datensätze anzupassen – _mit einem UI-basierten Workflow, der sich für Low-Code-Entwickler eignet_. Sieh dir dieses Beispiel an.                                                                                                                        |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Dieser Artikel beschreibt, wie man ein Hugging Face Modell mit der Hugging Face Transformers-Bibliothek auf einer einzelnen GPU mit Azure DataBricks und Hugging Face Trainer-Bibliotheken feinabstimmt.                                                                                                                        |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Der Modellkatalog in Azure Machine Learning bietet viele Open-Source-Modelle, die du für deine spezifische Aufgabe feinabstimmen kannst. Probiere dieses Modul aus dem [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst).                                               |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Das Fine-Tuning von GPT-3.5 oder GPT-4 Modellen auf Microsoft Azure mit W&B ermöglicht eine detaillierte Nachverfolgung und Analyse der Modellleistung. Diese Anleitung erweitert die Konzepte aus dem OpenAI Fine-Tuning Guide mit spezifischen Schritten und Funktionen für Azure OpenAI.                                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. Sekundäre Ressourcen

Dieser Abschnitt enthält zusätzliche Ressourcen, die es wert sind, erkundet zu werden, die wir aber in dieser Lektion nicht behandeln konnten. Sie könnten in einer zukünftigen Lektion oder als sekundäre Aufgabenoption zu einem späteren Zeitpunkt behandelt werden. Nutze sie vorerst, um dein eigenes Wissen und deine Expertise zu diesem Thema zu erweitern.

| Titel/Link                                                                                                                                                                                                            | Beschreibung                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Datenvorbereitung und Analyse für das Fine-Tuning von Chat-Modellen](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                              | Dieses Notebook dient als Werkzeug zur Vorverarbeitung und Analyse des Chat-Datensatzes, der für das Fine-Tuning eines Chat-Modells verwendet wird. Es prüft auf Formatfehler, liefert grundlegende Statistiken und schätzt die Token-Anzahl für Fine-Tuning-Kosten. Siehe: [Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning für Retrieval Augmented Generation (RAG) mit Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)     | Ziel dieses Notebooks ist es, ein umfassendes Beispiel zu zeigen, wie OpenAI-Modelle für Retrieval Augmented Generation (RAG) feinabgestimmt werden können. Dabei integrieren wir auch Qdrant und Few-Shot Learning, um die Modellleistung zu verbessern und Falschaussagen zu reduzieren.                                                                                                                                                                                                                              |
| **OpenAI Cookbook**: [Fine-Tuning von GPT mit Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                         | Weights & Biases (W&B) ist eine Plattform für KI-Entwickler mit Tools zum Trainieren, Fine-Tunen und Nutzen von Foundation-Modellen. Lies zuerst ihren [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) Guide und probiere dann die Cookbook-Übung aus.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) – Fine-Tuning für kleine Sprachmodelle                                                    | Lerne [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst) kennen, Microsofts neues kleines Modell, das überraschend leistungsstark und kompakt ist. Dieses Tutorial zeigt dir, wie du Phi-2 feinabstimmst, einen eigenen Datensatz erstellst und das Modell mit QLoRA trainierst.                                                                                                                                                       |
| **Hugging Face Tutorial** [Wie man LLMs 2024 mit Hugging Face feinabstimmt](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Dieser Blogbeitrag führt dich durch das Fine-Tuning offener LLMs mit Hugging Face TRL, Transformers und Datasets im Jahr 2024. Du definierst einen Anwendungsfall, richtest eine Entwicklungsumgebung ein, bereitest einen Datensatz vor, feinabstimmst das Modell, testest und bewertest es und stellst es dann produktiv bereit.                                                                                                                                                                                  |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Ermöglicht schnelleres und einfacheres Training und Deployment von [State-of-the-Art Machine Learning Modellen](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Das Repo enthält Colab-freundliche Tutorials mit YouTube-Videoanleitungen zum Fine-Tuning. **Spiegelt das aktuelle [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) Update wider**. Lies die [AutoTrain-Dokumentation](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst). |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Haftungsausschluss**:  
Dieses Dokument wurde mit dem KI-Übersetzungsdienst [Co-op Translator](https://github.com/Azure/co-op-translator) übersetzt. Obwohl wir uns um Genauigkeit bemühen, beachten Sie bitte, dass automatisierte Übersetzungen Fehler oder Ungenauigkeiten enthalten können. Das Originaldokument in seiner Ursprungssprache ist als maßgebliche Quelle zu betrachten. Für wichtige Informationen wird eine professionelle menschliche Übersetzung empfohlen. Wir übernehmen keine Haftung für Missverständnisse oder Fehlinterpretationen, die aus der Nutzung dieser Übersetzung entstehen.