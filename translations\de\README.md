<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:17:04+00:00",
  "source_file": "README.md",
  "language_code": "de"
}
-->
![Generative AI für Einsteiger](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.de.png)

### 21 Le<PERSON><PERSON><PERSON>, die alles vermitteln, was du wissen musst, um Generative AI-Anwendungen zu entwickeln

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Mehrsprachige Unterstützung

#### Unterstützt durch GitHub Action (Automatisiert & immer aktuell)

[Französisch](../fr/README.md) | [Spanisch](../es/README.md) | [Deutsch](./README.md) | [Russisch](../ru/README.md) | [Arabisch](../ar/README.md) | [Persisch (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinesisch (vereinfacht)](../zh/README.md) | [Chinesisch (traditionell, Macau)](../mo/README.md) | [Chinesisch (traditionell, Hongkong)](../hk/README.md) | [Chinesisch (traditionell, Taiwan)](../tw/README.md) | [Japanisch](../ja/README.md) | [Koreanisch](../ko/README.md) | [Hindi](../hi/README.md) | [Bengalisch](../bn/README.md) | [Marathi](../mr/README.md) | [Nepalesisch](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portugiesisch (Portugal)](../pt/README.md) | [Portugiesisch (Brasilien)](../br/README.md) | [Italienisch](../it/README.md) | [Polnisch](../pl/README.md) | [Türkisch](../tr/README.md) | [Griechisch](../el/README.md) | [Thailändisch](../th/README.md) | [Schwedisch](../sv/README.md) | [Dänisch](../da/README.md) | [Norwegisch](../no/README.md) | [Finnisch](../fi/README.md) | [Niederländisch](../nl/README.md) | [Hebräisch](../he/README.md) | [Vietnamesisch](../vi/README.md) | [Indonesisch](../id/README.md) | [Malaiisch](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Suaheli](../sw/README.md) | [Ungarisch](../hu/README.md) | [Tschechisch](../cs/README.md) | [Slowakisch](../sk/README.md) | [Rumänisch](../ro/README.md) | [Bulgarisch](../bg/README.md) | [Serbisch (Kyrillisch)](../sr/README.md) | [Kroatisch](../hr/README.md) | [Slowenisch](../sl/README.md) | [Ukrainisch](../uk/README.md) | [Birmanisch (Myanmar)](../my/README.md)

# Generative AI für Einsteiger (Version 3) – Ein Kurs

Lerne die Grundlagen zur Entwicklung von Generative AI-Anwendungen mit unserem umfassenden 21-Lektionen-Kurs von Microsoft Cloud Advocates.

## 🌱 Erste Schritte

Dieser Kurs besteht aus 21 Lektionen. Jede Lektion behandelt ein eigenes Thema, du kannst also dort starten, wo du möchtest!

Die Lektionen sind entweder als „Learn“-Lektionen gekennzeichnet, die ein Generative AI-Konzept erklären, oder als „Build“-Lektionen, die ein Konzept und Codebeispiele in **Python** und **TypeScript** zeigen, wenn möglich.

Für .NET-Entwickler gibt es [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

Jede Lektion enthält außerdem einen Abschnitt „Weiterlernen“ mit zusätzlichen Lernmaterialien.

## Was du brauchst
### Um den Code dieses Kurses auszuführen, kannst du eines der folgenden verwenden:
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) – **Lektionen:** „aoai-assignment“
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) – **Lektionen:** „githubmodels“
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) – **Lektionen:** „oai-assignment“
   
- Grundkenntnisse in Python oder TypeScript sind hilfreich – \*Für absolute Anfänger empfehlen wir diese [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) und [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) Kurse
- Ein GitHub-Konto, um [dieses gesamte Repository zu forken](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) und in deinem eigenen GitHub-Konto zu speichern

Wir haben eine **[Kurs-Einrichtung](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** Lektion erstellt, die dir beim Einrichten deiner Entwicklungsumgebung hilft.

Vergiss nicht, dieses Repo zu [starren (🌟)](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst), damit du es später leichter wiederfindest.

## 🧠 Bereit für die Bereitstellung?

Wenn du nach fortgeschritteneren Codebeispielen suchst, schau dir unsere [Sammlung von Generative AI Codebeispielen](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) in **Python** und **TypeScript** an.

## 🗣️ Triff andere Lernende, erhalte Unterstützung

Tritt unserem [offiziellen Azure AI Foundry Discord-Server](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) bei, um andere Lernende dieses Kurses zu treffen, dich zu vernetzen und Unterstützung zu bekommen.

Stelle Fragen oder teile Feedback im [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) auf GitHub.

## 🚀 Baust du ein Startup auf?

Melde dich beim [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) an, um **kostenlose OpenAI-Guthaben** und bis zu **150.000 $ Azure-Guthaben für den Zugriff auf OpenAI-Modelle über Azure OpenAI Services** zu erhalten.

## 🙏 Möchtest du helfen?

Hast du Vorschläge oder Fehler in Rechtschreibung oder Code gefunden? [Erstelle ein Issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) oder [sende einen Pull Request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Jede Lektion enthält:

- Eine kurze Videoeinführung in das Thema
- Eine schriftliche Lektion im README
- Python- und TypeScript-Codebeispiele, die Azure OpenAI und OpenAI API unterstützen
- Links zu zusätzlichen Ressourcen, um dein Lernen fortzusetzen

## 🗃️ Lektionen

| #   | **Lektionslink**                                                                                                                              | **Beschreibung**                                                                                 | **Video**                                                                   | **Zusätzliches Lernen**                                                        |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Kurs-Einrichtung](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Learn:** Wie du deine Entwicklungsumgebung einrichtest                                       | Video folgt bald                                                              | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Einführung in Generative AI und LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                                 | **Learn:** Verstehen, was Generative AI ist und wie Large Language Models (LLMs) funktionieren  | [Video](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Unterschiedliche LLMs erkunden und vergleichen](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)        | **Learn:** Wie du das richtige Modell für deinen Anwendungsfall auswählst                       | [Video](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Verantwortungsvoller Umgang mit Generative AI](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                | **Learn:** Wie man Generative AI-Anwendungen verantwortungsvoll entwickelt                      | [Video](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Grundlagen des Prompt Engineerings verstehen](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)                 | **Learn:** Praktische Best Practices im Prompt Engineering                                     | [Video](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Fortgeschrittene Prompts erstellen](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                         | **Learn:** Wie du Prompt Engineering-Techniken anwendest, die die Ergebnisse deiner Prompts verbessern | [Video](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Erstellung von Textgenerierungsanwendungen](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Erstellen:** Eine Textgenerierungs-App mit Azure OpenAI / OpenAI API                            | [Video](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Erstellung von Chat-Anwendungen](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Erstellen:** Techniken zum effizienten Aufbau und zur Integration von Chat-Anwendungen         | [Video](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Erstellung von Suchanwendungen mit Vektor-Datenbanken](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)              | **Erstellen:** Eine Suchanwendung, die Embeddings zur Datensuche verwendet                        | [Video](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Erstellung von Bildgenerierungsanwendungen](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                          | **Erstellen:** Eine Anwendung zur Bildgenerierung                                               | [Video](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Erstellung von Low-Code KI-Anwendungen](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Erstellen:** Eine Generative KI-Anwendung mit Low-Code-Tools                                  | [Video](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Integration externer Anwendungen mit Function Calling](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst)         | **Erstellen:** Was ist Function Calling und wie wird es in Anwendungen eingesetzt               | [Video](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [UX-Design für KI-Anwendungen](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                                   | **Lernen:** Wie man UX-Design-Prinzipien bei der Entwicklung von Generative KI-Anwendungen anwendet | [Video](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Absicherung Ihrer Generative KI-Anwendungen](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                           | **Lernen:** Bedrohungen und Risiken für KI-Systeme sowie Methoden zu deren Absicherung           | [Video](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Der Lebenszyklus von Generative KI-Anwendungen](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)         | **Lernen:** Werkzeuge und Kennzahlen zur Verwaltung des LLM-Lebenszyklus und LLMOps             | [Video](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) und Vektor-Datenbanken](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)            | **Erstellen:** Eine Anwendung mit einem RAG-Framework, das Embeddings aus Vektor-Datenbanken abruft | [Video](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Open Source Modelle und Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                        | **Erstellen:** Eine Anwendung mit Open Source Modellen von Hugging Face                         | [Video](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [KI-Agenten](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                           | **Erstellen:** Eine Anwendung mit einem KI-Agenten-Framework                                   | [Video](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [Feinabstimmung von LLMs](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                            | **Lernen:** Was, warum und wie man LLMs feinabstimmt                                           | [Video](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Erstellung mit SLMs](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                                        | **Lernen:** Die Vorteile der Arbeit mit Small Language Models                                  | Video folgt in Kürze | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Erstellung mit Mistral-Modellen](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                        | **Lernen:** Die Eigenschaften und Unterschiede der Mistral-Familie von Modellen               | Video folgt in Kürze | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Erstellung mit Meta-Modellen](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Lernen:** Die Eigenschaften und Unterschiede der Meta-Familie von Modellen                   | Video folgt in Kürze | [Mehr erfahren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Besonderer Dank

Besonderer Dank an [**John Aziz**](https://www.linkedin.com/in/john0isaac/) für die Erstellung aller GitHub Actions und Workflows

[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) für wichtige Beiträge zu jeder Lektion, die das Lernerlebnis und den Code verbessern.

## 🎒 Weitere Kurse

Unser Team bietet weitere Kurse an! Schau dir an:

- [**NEU** Model Context Protocol für Einsteiger](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [KI-Agenten für Einsteiger](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Generative KI für Einsteiger mit .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Generative KI für Einsteiger mit JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML für Einsteiger](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science für Einsteiger](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [KI für Einsteiger](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Cybersecurity für Einsteiger](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Webentwicklung für Einsteiger](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT für Einsteiger](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR-Entwicklung für Einsteiger](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [GitHub Copilot für AI-Paired Programming meistern](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [GitHub Copilot für C#/.NET-Entwickler meistern](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Wähle dein eigenes Copilot-Abenteuer](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Haftungsausschluss**:  
Dieses Dokument wurde mit dem KI-Übersetzungsdienst [Co-op Translator](https://github.com/Azure/co-op-translator) übersetzt. Obwohl wir uns um Genauigkeit bemühen, beachten Sie bitte, dass automatisierte Übersetzungen Fehler oder Ungenauigkeiten enthalten können. Das Originaldokument in seiner Ursprungssprache ist als maßgebliche Quelle zu betrachten. Für wichtige Informationen wird eine professionelle menschliche Übersetzung empfohlen. Wir übernehmen keine Haftung für Missverständnisse oder Fehlinterpretationen, die aus der Nutzung dieser Übersetzung entstehen.