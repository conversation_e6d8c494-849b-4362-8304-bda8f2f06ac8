<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:50:30+00:00",
  "source_file": "SECURITY.md",
  "language_code": "de"
}
-->
## Sicherheit

Microsoft nimmt die Sicherheit unserer Softwareprodukte und -dienste sehr ernst, dazu gehören alle Quellcode-Repositories, die über unsere GitHub-Organisationen verwaltet werden, darunter [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) und [unsere GitHub-Organisationen](https://opensource.microsoft.com/).

<PERSON><PERSON>lau<PERSON>, eine Sicherheitslücke in einem Microsoft-eigenen Repository gefunden zu haben, die der [Definition von Microsoft für eine Sicherheitslücke](https://aka.ms/opensource/security/definition) entspricht, melden Sie diese bitte wie unten beschrieben.

## Meldung von Sicherheitsproblemen

**Bitte melden Sie Sicherheitslücken nicht über öffentliche GitHub-Issues.**

Melden Sie diese stattdessen bitte an das Microsoft Security Response Center (MSRC) unter [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Wenn Sie lieber ohne Anmeldung melden möchten, senden Sie eine E-Mail an [<EMAIL>](mailto:<EMAIL>). Falls möglich, verschlüsseln Sie Ihre Nachricht mit unserem PGP-Schlüssel; laden Sie diesen bitte von der [Microsoft Security Response Center PGP Key Seite](https://aka.ms/opensource/security/pgpkey) herunter.

Sie sollten innerhalb von 24 Stunden eine Antwort erhalten. Falls dies aus irgendeinem Grund nicht der Fall ist, senden Sie bitte eine Folge-E-Mail, um sicherzustellen, dass wir Ihre ursprüngliche Nachricht erhalten haben. Weitere Informationen finden Sie unter [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Bitte fügen Sie die unten aufgeführten Informationen (so vollständig wie möglich) bei, damit wir die Art und den Umfang des möglichen Problems besser verstehen können:

  * Art des Problems (z. B. Pufferüberlauf, SQL-Injection, Cross-Site Scripting usw.)
  * Vollständige Pfade der Quellcodedatei(en), die mit dem Auftreten des Problems zusammenhängen
  * Der Ort des betroffenen Quellcodes (Tag/Branch/Commit oder direkte URL)
  * Besondere Konfigurationen, die zur Reproduktion des Problems erforderlich sind
  * Schritt-für-Schritt-Anleitung zur Reproduktion des Problems
  * Proof-of-Concept oder Exploit-Code (wenn möglich)
  * Auswirkungen des Problems, einschließlich wie ein Angreifer das Problem ausnutzen könnte

Diese Informationen helfen uns, Ihre Meldung schneller zu bearbeiten.

Wenn Sie eine Meldung im Rahmen eines Bug-Bounty-Programms einreichen, können vollständigere Berichte zu einer höheren Prämie führen. Weitere Details zu unseren aktiven Programmen finden Sie auf unserer [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty)-Seite.

## Bevorzugte Sprachen

Wir bevorzugen alle Kommunikationen auf Englisch.

## Richtlinie

Microsoft folgt dem Prinzip der [Koordinierten Sicherheitslückenoffenlegung](https://aka.ms/opensource/security/cvd).

**Haftungsausschluss**:  
Dieses Dokument wurde mit dem KI-Übersetzungsdienst [Co-op Translator](https://github.com/Azure/co-op-translator) übersetzt. Obwohl wir uns um Genauigkeit bemühen, beachten Sie bitte, dass automatisierte Übersetzungen Fehler oder Ungenauigkeiten enthalten können. Das Originaldokument in seiner Ursprungssprache ist als maßgebliche Quelle zu betrachten. Für wichtige Informationen wird eine professionelle menschliche Übersetzung empfohlen. Wir übernehmen keine Haftung für Missverständnisse oder Fehlinterpretationen, die aus der Nutzung dieser Übersetzung entstehen.