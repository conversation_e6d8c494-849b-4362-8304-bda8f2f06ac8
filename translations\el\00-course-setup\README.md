<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "00f2643fec1571acc5d38cc1a3b972d5",
  "translation_date": "2025-07-09T07:09:22+00:00",
  "source_file": "00-course-setup/README.md",
  "language_code": "el"
}
-->
# Ξεκινώντας με αυτό το μάθημα

Είμαστε πολύ ενθουσιασμένοι που ξεκινάτε αυτό το μάθημα και θα δείτε τι μπορείτε να εμπνευστείτε να δημιουργήσετε με τη Γενετική Τεχνητή Νοημοσύνη!

Για να διασφαλίσουμε την επιτυχία σας, αυτή η σελίδα περιγράφει τα βήματα εγκατάστασης, τις τεχνικές απαιτήσεις και πού να ζητήσετε βοήθεια αν χρειαστεί.

## Βήματα Εγκατάστασης

Για να ξεκινήσετε να παρακολουθείτε αυτό το μάθημα, θα χρειαστεί να ολοκληρώσετε τα παρακάτω βήματα.

### 1. Κλωνοποιήστε αυτό το αποθετήριο

[Κλωνοποιήστε ολόκληρο το αποθετήριο](https://github.com/microsoft/generative-ai-for-beginners/fork?WT.mc_id=academic-105485-koreyst) στον δικό σας λογαριασμό GitHub για να μπορείτε να τροποποιήσετε οποιονδήποτε κώδικα και να ολοκληρώσετε τις προκλήσεις. Μπορείτε επίσης να [σημειώσετε με αστέρι (🌟) αυτό το αποθετήριο](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst) για να το βρείτε πιο εύκολα μαζί με σχετικά αποθετήρια.

### 2. Δημιουργήστε ένα codespace

Για να αποφύγετε προβλήματα με εξαρτήσεις κατά την εκτέλεση του κώδικα, προτείνουμε να τρέξετε αυτό το μάθημα σε ένα [GitHub Codespaces](https://github.com/features/codespaces?WT.mc_id=academic-105485-koreyst).

Αυτό μπορεί να δημιουργηθεί επιλέγοντας την επιλογή `Code` στην κλωνοποιημένη έκδοση αυτού του αποθετηρίου και επιλέγοντας την επιλογή **Codespaces**.

![Παράθυρο διαλόγου που δείχνει κουμπιά για τη δημιουργία codespace](../../../00-course-setup/images/who-will-pay.webp)

### 3. Αποθήκευση των API Keys σας

Η ασφάλεια των API keys σας είναι σημαντική όταν δημιουργείτε οποιαδήποτε εφαρμογή. Συνιστούμε να μην αποθηκεύετε τα API keys απευθείας στον κώδικά σας. Η αποθήκευση αυτών των στοιχείων σε δημόσιο αποθετήριο μπορεί να προκαλέσει προβλήματα ασφαλείας και ακόμη και ανεπιθύμητα κόστη αν χρησιμοποιηθούν από κακόβουλους χρήστες.  
Ακολουθεί ένας οδηγός βήμα προς βήμα για το πώς να δημιουργήσετε ένα αρχείο `.env` για Python και να προσθέσετε το `GITHUB_TOKEN`:

1. **Πλοηγηθείτε στον φάκελο του έργου σας**: Ανοίξτε το τερματικό ή τη γραμμή εντολών και πλοηγηθείτε στον ριζικό φάκελο του έργου όπου θέλετε να δημιουργήσετε το αρχείο `.env`.

   ```bash
   cd path/to/your/project
   ```

2. **Δημιουργήστε το αρχείο `.env`**: Χρησιμοποιήστε τον αγαπημένο σας επεξεργαστή κειμένου για να δημιουργήσετε ένα νέο αρχείο με όνομα `.env`. Αν χρησιμοποιείτε τη γραμμή εντολών, μπορείτε να χρησιμοποιήσετε `touch` (σε συστήματα Unix) ή `echo` (σε Windows):

   Συστήματα Unix:

   ```bash
   touch .env
   ```

   Windows:

   ```cmd
   echo . > .env
   ```

3. **Επεξεργαστείτε το αρχείο `.env`**: Ανοίξτε το αρχείο `.env` σε έναν επεξεργαστή κειμένου (π.χ. VS Code, Notepad++ ή οποιονδήποτε άλλο). Προσθέστε την παρακάτω γραμμή, αντικαθιστώντας το `your_github_token_here` με το πραγματικό σας GitHub token:

   ```env
   GITHUB_TOKEN=your_github_token_here
   ```

4. **Αποθηκεύστε το αρχείο**: Αποθηκεύστε τις αλλαγές και κλείστε τον επεξεργαστή κειμένου.

5. **Εγκαταστήστε το `python-dotenv`**: Αν δεν το έχετε ήδη, θα χρειαστεί να εγκαταστήσετε το πακέτο `python-dotenv` για να φορτώνετε τις μεταβλητές περιβάλλοντος από το αρχείο `.env` στην Python εφαρμογή σας. Μπορείτε να το εγκαταστήσετε με `pip`:

   ```bash
   pip install python-dotenv
   ```

6. **Φορτώστε τις μεταβλητές περιβάλλοντος στο Python script σας**: Στο Python script σας, χρησιμοποιήστε το πακέτο `python-dotenv` για να φορτώσετε τις μεταβλητές περιβάλλοντος από το αρχείο `.env`:

   ```python
   from dotenv import load_dotenv
   import os

   # Load environment variables from .env file
   load_dotenv()

   # Access the GITHUB_TOKEN variable
   github_token = os.getenv("GITHUB_TOKEN")

   print(github_token)
   ```

Αυτό ήταν! Δημιουργήσατε με επιτυχία ένα αρχείο `.env`, προσθέσατε το GitHub token σας και το φορτώσατε στην Python εφαρμογή σας.

## Πώς να τρέξετε τοπικά στον υπολογιστή σας

Για να τρέξετε τον κώδικα τοπικά στον υπολογιστή σας, θα χρειαστεί να έχετε εγκαταστήσει κάποια έκδοση του [Python](https://www.python.org/downloads/?WT.mc_id=academic-105485-koreyst).

Για να χρησιμοποιήσετε το αποθετήριο, πρέπει να το κλωνοποιήσετε:

```shell
git clone https://github.com/microsoft/generative-ai-for-beginners
cd generative-ai-for-beginners
```

Μόλις έχετε όλα τα αρχεία, μπορείτε να ξεκινήσετε!

## Προαιρετικά Βήματα

### Εγκατάσταση Miniconda

Το [Miniconda](https://conda.io/en/latest/miniconda.html?WT.mc_id=academic-105485-koreyst) είναι ένας ελαφρύς εγκαταστάτης για την εγκατάσταση του [Conda](https://docs.conda.io/en/latest?WT.mc_id=academic-105485-koreyst), της Python, καθώς και μερικών πακέτων.  
Το Conda είναι ένας διαχειριστής πακέτων που διευκολύνει τη ρύθμιση και την εναλλαγή μεταξύ διαφορετικών [**εικονικών περιβαλλόντων**](https://docs.python.org/3/tutorial/venv.html?WT.mc_id=academic-105485-koreyst) Python και πακέτων. Είναι επίσης χρήσιμο για την εγκατάσταση πακέτων που δεν είναι διαθέσιμα μέσω του `pip`.

Μπορείτε να ακολουθήσετε τον [οδηγό εγκατάστασης MiniConda](https://docs.anaconda.com/free/miniconda/#quick-command-line-install?WT.mc_id=academic-105485-koreyst) για να το ρυθμίσετε.

Αφού εγκαταστήσετε το Miniconda, πρέπει να κλωνοποιήσετε το [αποθετήριο](https://github.com/microsoft/generative-ai-for-beginners/fork?WT.mc_id=academic-105485-koreyst) (αν δεν το έχετε ήδη κάνει).

Στη συνέχεια, πρέπει να δημιουργήσετε ένα εικονικό περιβάλλον. Για να το κάνετε αυτό με το Conda, δημιουργήστε ένα νέο αρχείο περιβάλλοντος (_environment.yml_). Αν ακολουθείτε το μάθημα μέσω Codespaces, δημιουργήστε το μέσα στον φάκελο `.devcontainer`, δηλαδή `.devcontainer/environment.yml`.

Συμπληρώστε το αρχείο περιβάλλοντος με το παρακάτω απόσπασμα:

```yml
name: <environment-name>
channels:
  - defaults
  - microsoft
dependencies:
  - python=<python-version>
  - openai
  - python-dotenv
  - pip
  - pip:
      - azure-ai-ml
```

Αν αντιμετωπίσετε σφάλματα με το conda, μπορείτε να εγκαταστήσετε χειροκίνητα τις Microsoft AI Libraries με την παρακάτω εντολή στο τερματικό.

```
conda install -c microsoft azure-ai-ml
```

Το αρχείο περιβάλλοντος καθορίζει τις εξαρτήσεις που χρειαζόμαστε. Το `<environment-name>` αναφέρεται στο όνομα που θέλετε να δώσετε στο Conda περιβάλλον σας, και το `<python-version>` είναι η έκδοση της Python που θέλετε να χρησιμοποιήσετε, για παράδειγμα, το `3` είναι η πιο πρόσφατη κύρια έκδοση της Python.

Αφού το κάνετε αυτό, μπορείτε να δημιουργήσετε το Conda περιβάλλον σας εκτελώντας τις παρακάτω εντολές στη γραμμή εντολών/τερματικό:

```bash
conda env create --name ai4beg --file .devcontainer/environment.yml # .devcontainer sub path applies to only Codespace setups
conda activate ai4beg
```

Ανατρέξτε στον [οδηγό περιβαλλόντων Conda](https://docs.conda.io/projects/conda/en/latest/user-guide/tasks/manage-environments.html?WT.mc_id=academic-105485-koreyst) αν αντιμετωπίσετε προβλήματα.

### Χρήση του Visual Studio Code με την επέκταση υποστήριξης Python

Συνιστούμε τη χρήση του επεξεργαστή [Visual Studio Code (VS Code)](https://code.visualstudio.com/?WT.mc_id=academic-105485-koreyst) με την εγκατεστημένη [επέκταση υποστήριξης Python](https://marketplace.visualstudio.com/items?itemName=ms-python.python&WT.mc_id=academic-105485-koreyst) για αυτό το μάθημα. Ωστόσο, πρόκειται περισσότερο για σύσταση παρά για απαραίτητη προϋπόθεση.

> **Note**: Ανοίγοντας το αποθετήριο του μαθήματος στο VS Code, έχετε τη δυνατότητα να ρυθμίσετε το έργο μέσα σε ένα container. Αυτό οφείλεται στον [ειδικό φάκελο `.devcontainer`](https://code.visualstudio.com/docs/devcontainers/containers?itemName=ms-python.python&WT.mc_id=academic-105485-koreyst) που υπάρχει στο αποθετήριο του μαθήματος. Θα αναφερθούμε σε αυτό αργότερα.

> **Note**: Μόλις κλωνοποιήσετε και ανοίξετε τον φάκελο στο VS Code, θα σας προταθεί αυτόματα να εγκαταστήσετε την επέκταση υποστήριξης Python.

> **Note**: Αν το VS Code σας προτείνει να ξανανοίξετε το αποθετήριο μέσα σε container, απορρίψτε αυτό το αίτημα για να χρησιμοποιήσετε την τοπικά εγκατεστημένη έκδοση της Python.

### Χρήση του Jupyter στον περιηγητή

Μπορείτε επίσης να δουλέψετε στο έργο χρησιμοποιώντας το περιβάλλον [Jupyter](https://jupyter.org?WT.mc_id=academic-105485-koreyst) απευθείας μέσα από τον περιηγητή σας. Τόσο το κλασικό Jupyter όσο και το [Jupyter Hub](https://jupyter.org/hub?WT.mc_id=academic-105485-koreyst) προσφέρουν ένα ευχάριστο περιβάλλον ανάπτυξης με λειτουργίες όπως αυτόματη συμπλήρωση, επισήμανση κώδικα κ.ά.

Για να ξεκινήσετε το Jupyter τοπικά, ανοίξτε το τερματικό/γραμμή εντολών, πλοηγηθείτε στον φάκελο του μαθήματος και εκτελέστε:

```bash
jupyter notebook
```

ή

```bash
jupyterhub
```

Αυτό θα ξεκινήσει μια παρουσία Jupyter και η διεύθυνση URL για πρόσβαση θα εμφανιστεί στο παράθυρο της γραμμής εντολών.

Μόλις αποκτήσετε πρόσβαση στη διεύθυνση URL, θα δείτε το περίγραμμα του μαθήματος και θα μπορείτε να πλοηγηθείτε σε οποιοδήποτε αρχείο `*.ipynb`. Για παράδειγμα, `08-building-search-applications/python/oai-solution.ipynb`.

### Εκτέλεση μέσα σε container

Μια εναλλακτική στο να ρυθμίσετε τα πάντα στον υπολογιστή σας ή στο Codespace είναι να χρησιμοποιήσετε ένα [container](../../../00-course-setup/<https:/en.wikipedia.org/wiki/Containerization_(computing)?WT.mc_id=academic-105485-koreyst>). Ο ειδικός φάκελος `.devcontainer` μέσα στο αποθετήριο του μαθήματος επιτρέπει στο VS Code να ρυθμίσει το έργο μέσα σε container. Εκτός των Codespaces, αυτό απαιτεί την εγκατάσταση του Docker και, ειλικρινά, περιλαμβάνει λίγη δουλειά, οπότε το προτείνουμε μόνο σε όσους έχουν εμπειρία με containers.

Ένας από τους καλύτερους τρόπους να κρατήσετε τα API keys σας ασφαλή όταν χρησιμοποιείτε GitHub Codespaces είναι μέσω των Codespace Secrets. Παρακαλούμε ακολουθήστε τον οδηγό [Codespaces secrets management](https://docs.github.com/en/codespaces/managing-your-codespaces/managing-secrets-for-your-codespaces?WT.mc_id=academic-105485-koreyst) για να μάθετε περισσότερα.

## Μαθήματα και Τεχνικές Απαιτήσεις

Το μάθημα περιλαμβάνει 6 μαθήματα θεωρίας και 6 μαθήματα κώδικα.

Για τα μαθήματα κώδικα, χρησιμοποιούμε την υπηρεσία Azure OpenAI. Θα χρειαστείτε πρόσβαση στην υπηρεσία Azure OpenAI και ένα API key για να τρέξετε αυτόν τον κώδικα. Μπορείτε να κάνετε αίτηση για πρόσβαση [συμπληρώνοντας αυτή την αίτηση](https://azure.microsoft.com/products/ai-services/openai-service?WT.mc_id=academic-105485-koreyst).

Ενώ περιμένετε να επεξεργαστεί η αίτησή σας, κάθε μάθημα κώδικα περιλαμβάνει επίσης ένα αρχείο `README.md` όπου μπορείτε να δείτε τον κώδικα και τα αποτελέσματα.

## Χρήση της υπηρεσίας Azure OpenAI για πρώτη φορά

Αν είναι η πρώτη φορά που δουλεύετε με την υπηρεσία Azure OpenAI, παρακαλούμε ακολουθήστε αυτόν τον οδηγό για το πώς να [δημιουργήσετε και να αναπτύξετε έναν πόρο Azure OpenAI Service.](https://learn.microsoft.com/azure/ai-services/openai/how-to/create-resource?pivots=web-portal&WT.mc_id=academic-105485-koreyst)

## Χρήση του OpenAI API για πρώτη φορά

Αν είναι η πρώτη φορά που δουλεύετε με το OpenAI API, παρακαλούμε ακολουθήστε τον οδηγό για το πώς να [δημιουργήσετε και να χρησιμοποιήσετε το Interface.](https://platform.openai.com/docs/quickstart?context=pythont&WT.mc_id=academic-105485-koreyst)

## Γνωρίστε άλλους μαθητές

Έχουμε δημιουργήσει κανάλια στον επίσημο [AI Community Discord server](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) για να γνωρίσετε άλλους μαθητές. Είναι ένας εξαιρετικός τρόπος να δικτυωθείτε με άλλους επιχειρηματίες, δημιουργούς, φοιτητές και όσους θέλουν να εξελιχθούν στη Γενετική Τεχνητή Νοημοσύνη.

[![Join discord channel](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

Η ομάδα του έργου θα είναι επίσης παρούσα σε αυτόν τον Discord server για να βοηθήσει τους μαθητές.

## Συνεισφορά

Αυτό το μάθημα είναι μια πρωτοβουλία ανοιχτού κώδικα. Αν δείτε σημεία βελτίωσης ή προβλήματα, παρακαλούμε δημιουργήστε ένα [Pull Request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst) ή καταχωρήστε ένα [GitHub issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst).

Η ομάδα του έργου παρακολουθεί όλες τις συνεισφορές. Η συνεισφορά σε ανοιχτό κώδικα είναι ένας υπέροχος τρόπος να χτίσετε την καριέρα σας στη Γενετική Τεχνητή Νοημοσύνη.

Οι περισσότερες συνεισφορές απαιτούν να συμφωνήσετε με μια Συμφωνία Άδειας Συνεισφοράς (CLA) που δηλώνει ότι έχετε το δικαίωμα και πράγματι παραχωρείτε τα δικαιώματα χρήσης της συνεισφοράς σας. Για λεπτομέρειες, επισκεφθείτε την [ιστοσελίδα CLA, Contributor License Agreement](https://cla.microsoft.com?WT.mc_id=academic-105485-koreyst).

Σημαντικό: όταν μεταφράζετε κείμενο σε αυτό το αποθετήριο, παρακαλούμε βεβαιωθείτε ότι δεν χρησιμοποιείτε μηχανική μετάφραση. Θα επαληθεύσουμε τις μεταφράσεις μέσω της κοινότητας, οπότε παρακαλούμε να αναλαμβάνετε μεταφράσεις μόνο σε γλώσσες που γνωρίζετε καλά.

Όταν υποβάλλετε ένα pull request, ένα CLA-bot θα καθορίσει αυτόματα αν χρειάζεται να παρέχετε CLA και θα διακοσμήσει το PR ανάλογα (π.χ. με ετικέτα, σχόλιο). Απλώς ακολουθήστε τις οδηγίες του bot. Θα χρειαστεί να το κάνετε μόνο μία φορά σε όλα τα αποθετήρια που χρησιμοποιούν το CLA μας.

Αυτό το έργο έχει υιοθετήσει τον [Κώδικα Δεοντολογίας Ανοιχτού Κώδικα της Microsoft](https://opensource.microsoft.com/codeofconduct/?WT.mc_id=academic-105485-koreyst). Για περισσότερες πληροφορίες διαβάστε τις Συχνές Ερωτήσεις για τον Κώδικα Δεοντολογίας ή επικοινωνήστε με το [Email opencode](<EMAIL>) για επιπλέον ερωτήσεις ή σχόλια.

## Ας Ξεκινήσουμε

Τώρα που ολο

**Αποποίηση ευθυνών**:  
Αυτό το έγγραφο έχει μεταφραστεί χρησιμοποιώντας την υπηρεσία αυτόματης μετάφρασης AI [Co-op Translator](https://github.com/Azure/co-op-translator). Παρόλο που επιδιώκουμε την ακρίβεια, παρακαλούμε να γνωρίζετε ότι οι αυτόματες μεταφράσεις ενδέχεται να περιέχουν λάθη ή ανακρίβειες. Το πρωτότυπο έγγραφο στη γλώσσα του θεωρείται η αυθεντική πηγή. Για κρίσιμες πληροφορίες, συνιστάται επαγγελματική ανθρώπινη μετάφραση. Δεν φέρουμε ευθύνη για τυχόν παρεξηγήσεις ή λανθασμένες ερμηνείες που προκύπτουν από τη χρήση αυτής της μετάφρασης.