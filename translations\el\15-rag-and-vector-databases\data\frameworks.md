<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:31:54+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "el"
}
-->
# Πλαίσια Νευρωνικών Δικτύων

Όπως έχουμε ήδη μάθει, για να μπορέσουμε να εκπαιδεύσουμε νευρωνικά δίκτυα αποδοτικά, πρέπει να κάνουμε δύο πράγματα:

* Να λειτουργούμε με τανυστές, π.χ. να πολλαπλασιάζουμε, να προσθέτουμε και να υπολογίζουμε κάποιες συναρτήσεις όπως sigmoid ή softmax
* Να υπολογίζουμε τις παραγώγους όλων των εκφράσεων, ώστε να εκτελούμε βελτιστοποίηση με κατιούσα κλίση (gradient descent)

Ενώ η βιβλιοθήκη `numpy` μπορεί να κάνει το πρώτο μέρος, χρειαζόμαστε κάποιο μηχανισμό για τον υπολογισμό των παραγώγων. Στο πλαίσιο που αναπτύξαμε στην προηγούμενη ενότητα, έπρεπε να προγραμματίσουμε χειροκίνητα όλες τις συναρτήσεις παραγώγων μέσα στη μέθοδο `backward`, που υλοποιεί την οπισθοδιάδοση (backpropagation). Ιδανικά, ένα πλαίσιο θα πρέπει να μας δίνει τη δυνατότητα να υπολογίζουμε τις παραγώγους *οποιασδήποτε έκφρασης* που μπορούμε να ορίσουμε.

Ένα ακόμα σημαντικό στοιχείο είναι να μπορούμε να εκτελούμε υπολογισμούς σε GPU ή σε άλλες εξειδικευμένες μονάδες υπολογισμού, όπως TPU. Η εκπαίδευση βαθιών νευρωνικών δικτύων απαιτεί *πολύ* υπολογιστική ισχύ, και η δυνατότητα παράλληλης εκτέλεσης αυτών των υπολογισμών σε GPUs είναι πολύ σημαντική.

> ✅ Ο όρος 'παράλληλη εκτέλεση' σημαίνει τη διανομή των υπολογισμών σε πολλαπλές συσκευές.

Προς το παρόν, τα δύο πιο δημοφιλή πλαίσια νευρωνικών δικτύων είναι: TensorFlow και PyTorch. Και τα δύο παρέχουν ένα χαμηλού επιπέδου API για λειτουργίες με τανυστές τόσο σε CPU όσο και σε GPU. Πάνω από το χαμηλού επιπέδου API, υπάρχει και ένα υψηλού επιπέδου API, που ονομάζεται αντίστοιχα Keras και PyTorch Lightning.

Χαμηλού Επιπέδου API | TensorFlow | PyTorch
---------------------|-------------------------------|---------------------------
Υψηλού Επιπέδου API  | Keras                         | PyTorch

**Τα χαμηλού επιπέδου APIs** και στα δύο πλαίσια επιτρέπουν τη δημιουργία των λεγόμενων **υπολογιστικών γραφημάτων**. Αυτό το γράφημα ορίζει πώς να υπολογιστεί το αποτέλεσμα (συνήθως η συνάρτηση κόστους) με δεδομένες παραμέτρους εισόδου, και μπορεί να εκτελεστεί σε GPU, αν είναι διαθέσιμη. Υπάρχουν συναρτήσεις για να διαφορίσουμε αυτό το υπολογιστικό γράφημα και να υπολογίσουμε τις παραγώγους, οι οποίες στη συνέχεια χρησιμοποιούνται για τη βελτιστοποίηση των παραμέτρων του μοντέλου.

**Τα υψηλού επιπέδου APIs** θεωρούν τα νευρωνικά δίκτυα ως **αλυσίδα από στρώματα** και κάνουν την κατασκευή των περισσότερων νευρωνικών δικτύων πολύ πιο εύκολη. Η εκπαίδευση του μοντέλου συνήθως απαιτεί την προετοιμασία των δεδομένων και στη συνέχεια την κλήση της συνάρτησης `fit` για να γίνει η δουλειά.

Το υψηλού επιπέδου API επιτρέπει την πολύ γρήγορη κατασκευή τυπικών νευρωνικών δικτύων χωρίς να ανησυχούμε για πολλές λεπτομέρειες. Ταυτόχρονα, το χαμηλού επιπέδου API προσφέρει πολύ μεγαλύτερο έλεγχο στη διαδικασία εκπαίδευσης, γι’ αυτό και χρησιμοποιείται πολύ στην έρευνα, όταν ασχολούμαστε με νέες αρχιτεκτονικές νευρωνικών δικτύων.

Είναι επίσης σημαντικό να κατανοήσουμε ότι μπορούμε να χρησιμοποιήσουμε και τα δύο APIs μαζί, π.χ. να αναπτύξουμε τη δική μας αρχιτεκτονική στρώματος δικτύου χρησιμοποιώντας το χαμηλού επιπέδου API και μετά να το ενσωματώσουμε σε ένα μεγαλύτερο δίκτυο που έχει κατασκευαστεί και εκπαιδευτεί με το υψηλού επιπέδου API. Ή να ορίσουμε ένα δίκτυο με το υψηλού επιπέδου API ως ακολουθία στρωμάτων και μετά να χρησιμοποιήσουμε τον δικό μας βρόχο εκπαίδευσης χαμηλού επιπέδου για τη βελτιστοποίηση. Και τα δύο APIs βασίζονται στις ίδιες βασικές έννοιες και έχουν σχεδιαστεί για να συνεργάζονται αρμονικά.

## Μάθηση

Σε αυτό το μάθημα, προσφέρουμε το μεγαλύτερο μέρος του περιεχομένου τόσο για PyTorch όσο και για TensorFlow. Μπορείτε να επιλέξετε το προτιμώμενο πλαίσιο και να ακολουθήσετε μόνο τα αντίστοιχα τετράδια. Αν δεν είστε σίγουροι ποιο πλαίσιο να επιλέξετε, διαβάστε κάποιες συζητήσεις στο διαδίκτυο σχετικά με το **PyTorch vs. TensorFlow**. Μπορείτε επίσης να δείτε και τα δύο πλαίσια για να κατανοήσετε καλύτερα.

Όπου είναι δυνατόν, θα χρησιμοποιήσουμε τα Υψηλού Επιπέδου APIs για απλότητα. Ωστόσο, πιστεύουμε ότι είναι σημαντικό να κατανοήσουμε πώς λειτουργούν τα νευρωνικά δίκτυα από τα βασικά, γι’ αυτό στην αρχή ξεκινάμε δουλεύοντας με το χαμηλού επιπέδου API και τανυστές. Αν όμως θέλετε να ξεκινήσετε γρήγορα και δεν θέλετε να αφιερώσετε πολύ χρόνο στη μάθηση αυτών των λεπτομερειών, μπορείτε να παραλείψετε αυτά και να προχωρήσετε απευθείας στα τετράδια του υψηλού επιπέδου API.

## ✍️ Ασκήσεις: Πλαίσια

Συνεχίστε τη μάθησή σας στα παρακάτω τετράδια:

Χαμηλού Επιπέδου API | TensorFlow+Keras Τετράδιο | PyTorch
---------------------|-----------------------------|-------------------
Υψηλού Επιπέδου API  | Keras                       | *PyTorch Lightning*

Αφού εξοικειωθείτε με τα πλαίσια, ας επαναλάβουμε την έννοια της υπερπροσαρμογής.

# Υπερπροσαρμογή (Overfitting)

Η υπερπροσαρμογή είναι μια εξαιρετικά σημαντική έννοια στη μηχανική μάθηση και είναι πολύ σημαντικό να την κατανοήσουμε σωστά!

Σκεφτείτε το παρακάτω πρόβλημα προσέγγισης 5 σημείων (που απεικονίζονται με `x` στα γραφήματα παρακάτω):

!linear | overfit
-------------------------|--------------------------
**Γραμμικό μοντέλο, 2 παράμετροι** | **Μη γραμμικό μοντέλο, 7 παράμετροι**
Σφάλμα εκπαίδευσης = 5.3 | Σφάλμα εκπαίδευσης = 0
Σφάλμα επικύρωσης = 5.1 | Σφάλμα επικύρωσης = 20

* Στα αριστερά, βλέπουμε μια καλή προσέγγιση με ευθεία γραμμή. Επειδή ο αριθμός των παραμέτρων είναι επαρκής, το μοντέλο κατανοεί σωστά τη διανομή των σημείων.
* Στα δεξιά, το μοντέλο είναι υπερβολικά ισχυρό. Επειδή έχουμε μόνο 5 σημεία και το μοντέλο έχει 7 παραμέτρους, μπορεί να προσαρμοστεί έτσι ώστε να περάσει από όλα τα σημεία, κάνοντας το σφάλμα εκπαίδευσης μηδέν. Ωστόσο, αυτό εμποδίζει το μοντέλο να κατανοήσει το σωστό μοτίβο πίσω από τα δεδομένα, με αποτέλεσμα το σφάλμα επικύρωσης να είναι πολύ υψηλό.

Είναι πολύ σημαντικό να βρούμε τη σωστή ισορροπία ανάμεσα στον πλούτο του μοντέλου (αριθμός παραμέτρων) και τον αριθμό των δειγμάτων εκπαίδευσης.

## Γιατί συμβαίνει η υπερπροσαρμογή

  * Δεν υπάρχουν αρκετά δεδομένα εκπαίδευσης
  * Το μοντέλο είναι υπερβολικά ισχυρό
  * Υπάρχει πολύς θόρυβος στα δεδομένα εισόδου

## Πώς να ανιχνεύσουμε την υπερπροσαρμογή

Όπως φαίνεται από το παραπάνω γράφημα, η υπερπροσαρμογή ανιχνεύεται από πολύ χαμηλό σφάλμα εκπαίδευσης και υψηλό σφάλμα επικύρωσης. Κατά τη διάρκεια της εκπαίδευσης, συνήθως βλέπουμε και τα δύο σφάλματα να μειώνονται, και μετά από κάποιο σημείο το σφάλμα επικύρωσης μπορεί να σταματήσει να μειώνεται και να αρχίσει να αυξάνεται. Αυτό είναι σημάδι υπερπροσαρμογής και ένδειξη ότι πιθανώς πρέπει να σταματήσουμε την εκπαίδευση εκεί (ή τουλάχιστον να κρατήσουμε ένα στιγμιότυπο του μοντέλου).

υπερπροσαρμογή

## Πώς να αποτρέψουμε την υπερπροσαρμογή

Αν διαπιστώσετε ότι συμβαίνει υπερπροσαρμογή, μπορείτε να κάνετε ένα από τα εξής:

 * Να αυξήσετε την ποσότητα των δεδομένων εκπαίδευσης
 * Να μειώσετε την πολυπλοκότητα του μοντέλου
 * Να χρησιμοποιήσετε κάποια τεχνική κανονικοποίησης (regularization), όπως το Dropout, που θα εξετάσουμε αργότερα.

## Υπερπροσαρμογή και το Δίλημμα Bias-Variance

Η υπερπροσαρμογή είναι στην πραγματικότητα μια περίπτωση ενός πιο γενικού προβλήματος στη στατιστική που ονομάζεται Δίλημμα Bias-Variance. Αν εξετάσουμε τις πιθανές πηγές σφάλματος στο μοντέλο μας, μπορούμε να διακρίνουμε δύο τύπους σφαλμάτων:

* **Σφάλματα bias** προκαλούνται από το γεγονός ότι ο αλγόριθμός μας δεν μπορεί να συλλάβει σωστά τη σχέση μεταξύ των δεδομένων εκπαίδευσης. Αυτό μπορεί να οφείλεται στο ότι το μοντέλο δεν είναι αρκετά ισχυρό (**υποπροσαρμογή**).
* **Σφάλματα variance**, που προκαλούνται από το ότι το μοντέλο προσεγγίζει τον θόρυβο στα δεδομένα εισόδου αντί για τη σημαντική σχέση (**υπερπροσαρμογή**).

Κατά την εκπαίδευση, το σφάλμα bias μειώνεται (καθώς το μοντέλο μαθαίνει να προσεγγίζει τα δεδομένα), ενώ το σφάλμα variance αυξάνεται. Είναι σημαντικό να σταματήσουμε την εκπαίδευση – είτε χειροκίνητα (όταν ανιχνεύσουμε υπερπροσαρμογή) είτε αυτόματα (με την εισαγωγή κανονικοποίησης) – για να αποτρέψουμε την υπερπροσαρμογή.

## Συμπέρασμα

Σε αυτό το μάθημα, μάθατε για τις διαφορές μεταξύ των διαφόρων APIs για τα δύο πιο δημοφιλή πλαίσια τεχνητής νοημοσύνης, TensorFlow και PyTorch. Επιπλέον, μάθατε για ένα πολύ σημαντικό θέμα, την υπερπροσαρμογή.

## 🚀 Πρόκληση

Στα συνοδευτικά τετράδια, θα βρείτε 'εργασίες' στο κάτω μέρος· δουλέψτε τα τετράδια και ολοκληρώστε τις εργασίες.

## Επανάληψη & Αυτοδιδασκαλία

Κάντε κάποια έρευνα στα παρακάτω θέματα:

- TensorFlow
- PyTorch
- Υπερπροσαρμογή

Ρωτήστε τον εαυτό σας τα εξής:

- Ποια είναι η διαφορά μεταξύ TensorFlow και PyTorch;
- Ποια είναι η διαφορά μεταξύ υπερπροσαρμογής και υποπροσαρμογής;

## Ανάθεση

Σε αυτό το εργαστήριο, σας ζητείται να λύσετε δύο προβλήματα ταξινόμησης χρησιμοποιώντας μονοστρωματικά και πολυστρωματικά πλήρως συνδεδεμένα δίκτυα, χρησιμοποιώντας PyTorch ή TensorFlow.

**Αποποίηση ευθυνών**:  
Αυτό το έγγραφο έχει μεταφραστεί χρησιμοποιώντας την υπηρεσία αυτόματης μετάφρασης AI [Co-op Translator](https://github.com/Azure/co-op-translator). Παρόλο που επιδιώκουμε την ακρίβεια, παρακαλούμε να γνωρίζετε ότι οι αυτόματες μεταφράσεις ενδέχεται να περιέχουν λάθη ή ανακρίβειες. Το πρωτότυπο έγγραφο στη γλώσσα του θεωρείται η αυθεντική πηγή. Για κρίσιμες πληροφορίες, συνιστάται επαγγελματική ανθρώπινη μετάφραση. Δεν φέρουμε ευθύνη για τυχόν παρεξηγήσεις ή λανθασμένες ερμηνείες που προκύπτουν από τη χρήση αυτής της μετάφρασης.