<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:46:32+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "el"
}
-->
# Εισαγωγή στα Νευρωνικά Δίκτυα. Πολυεπίπεδος Perceptron

Στην προηγούμενη ενότητα, μάθατε για το πιο απλό μοντέλο νευρωνικού δικτύου - τον μονοεπίπεδο perceptron, ένα γραμμικό μοντέλο ταξινόμησης δύο κλάσεων.

Σε αυτή την ενότητα θα επεκτείνουμε αυτό το μοντέλο σε ένα πιο ευέλικτο πλαίσιο, που μας επιτρέπει να:

* εκτελούμε **ταξινόμηση πολλαπλών κλάσεων** εκτός από δύο κλάσεων
* λύσουμε **προβλήματα παλινδρόμησης** εκτός από ταξινόμηση
* διαχωρίσουμε κλάσεις που δεν είναι γραμμικά διαχωρίσιμες

Επίσης, θα αναπτύξουμε το δικό μας αρθρωτό πλαίσιο σε Python που θα μας επιτρέπει να κατασκευάζουμε διαφορετικές αρχιτεκτονικές νευρωνικών δικτύων.

## Τυποποίηση της Μηχανικής Μάθησης

Ας ξεκινήσουμε με την τυποποίηση του προβλήματος της Μηχανικής Μάθησης. Υποθέτουμε ότι έχουμε ένα σύνολο εκπαίδευσης **X** με ετικέτες **Y**, και πρέπει να κατασκευάσουμε ένα μοντέλο *f* που θα κάνει τις πιο ακριβείς προβλέψεις. Η ποιότητα των προβλέψεων μετριέται με τη **συνάρτηση απώλειας** ℒ. Οι παρακάτω συναρτήσεις απώλειας χρησιμοποιούνται συχνά:

* Για πρόβλημα παλινδρόμησης, όταν πρέπει να προβλέψουμε έναν αριθμό, μπορούμε να χρησιμοποιήσουμε το **απόλυτο σφάλμα** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>|, ή το **τετραγωνικό σφάλμα** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup>
* Για ταξινόμηση, χρησιμοποιούμε το **0-1 loss** (που ουσιαστικά είναι το ίδιο με την **ακρίβεια** του μοντέλου), ή το **logistic loss**.

Για τον μονοεπίπεδο perceptron, η συνάρτηση *f* οριζόταν ως γραμμική συνάρτηση *f(x)=wx+b* (εδώ *w* είναι ο πίνακας βαρών, *x* είναι το διάνυσμα των εισόδων, και *b* είναι το διάνυσμα μετατόπισης). Για διαφορετικές αρχιτεκτονικές νευρωνικών δικτύων, αυτή η συνάρτηση μπορεί να πάρει πιο σύνθετη μορφή.

> Στην περίπτωση της ταξινόμησης, συχνά είναι επιθυμητό να λαμβάνουμε πιθανότητες των αντίστοιχων κλάσεων ως έξοδο του δικτύου. Για να μετατρέψουμε αυθαίρετους αριθμούς σε πιθανότητες (π.χ. για να κανονικοποιήσουμε την έξοδο), χρησιμοποιούμε συχνά τη συνάρτηση **softmax** σ, και η συνάρτηση *f* γίνεται *f(x)=σ(wx+b)*

Στον ορισμό της *f* παραπάνω, τα *w* και *b* ονομάζονται **παράμετροι** θ=⟨*w,b*⟩. Δεδομένου του συνόλου δεδομένων ⟨**X**,**Y**⟩, μπορούμε να υπολογίσουμε το συνολικό σφάλμα σε όλο το σύνολο ως συνάρτηση των παραμέτρων θ.

> ✅ **Ο στόχος της εκπαίδευσης του νευρωνικού δικτύου είναι η ελαχιστοποίηση του σφάλματος μεταβάλλοντας τις παραμέτρους θ**

## Βελτιστοποίηση με Κατιούσα Κλίση

Υπάρχει μια γνωστή μέθοδος βελτιστοποίησης συναρτήσεων που ονομάζεται **κατιούσα κλίση**. Η ιδέα είναι ότι μπορούμε να υπολογίσουμε την παράγωγο (σε πολυδιάστατη περίπτωση ονομάζεται **βαθμίδα**) της συνάρτησης απώλειας ως προς τις παραμέτρους, και να μεταβάλλουμε τις παραμέτρους έτσι ώστε το σφάλμα να μειώνεται. Αυτό μπορεί να τυποποιηθεί ως εξής:

* Αρχικοποιούμε τις παραμέτρους με κάποιες τυχαίες τιμές w<sup>(0)</sup>, b<sup>(0)</sup>
* Επαναλαμβάνουμε το παρακάτω βήμα πολλές φορές:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

Κατά την εκπαίδευση, τα βήματα βελτιστοποίησης υπολογίζονται λαμβάνοντας υπόψη ολόκληρο το σύνολο δεδομένων (θυμηθείτε ότι η απώλεια υπολογίζεται ως άθροισμα σε όλα τα δείγματα εκπαίδευσης). Ωστόσο, στην πράξη παίρνουμε μικρά τμήματα του συνόλου δεδομένων που ονομάζονται **minibatches**, και υπολογίζουμε τις βαθμίδες βάσει ενός υποσυνόλου δεδομένων. Επειδή το υποσύνολο επιλέγεται τυχαία κάθε φορά, αυτή η μέθοδος ονομάζεται **στοχαστική κατιούσα κλίση** (SGD).

## Πολυεπίπεδοι Perceptrons και Backpropagation

Το μονοεπίπεδο δίκτυο, όπως είδαμε παραπάνω, μπορεί να ταξινομήσει γραμμικά διαχωρίσιμες κλάσεις. Για να κατασκευάσουμε ένα πιο πλούσιο μοντέλο, μπορούμε να συνδυάσουμε πολλαπλά επίπεδα του δικτύου. Μαθηματικά, αυτό σημαίνει ότι η συνάρτηση *f* θα έχει πιο σύνθετη μορφή και θα υπολογίζεται σε πολλά βήματα:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

Εδώ, η α είναι μια **μη γραμμική συνάρτηση ενεργοποίησης**, η σ είναι η συνάρτηση softmax, και οι παράμετροι θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*>.

Ο αλγόριθμος κατιούσας κλίσης παραμένει ο ίδιος, αλλά ο υπολογισμός των βαθμίδων γίνεται πιο δύσκολος. Με τον κανόνα αλυσίδας διαφορισμού, μπορούμε να υπολογίσουμε τις παραγώγους ως:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ Ο κανόνας αλυσίδας διαφορισμού χρησιμοποιείται για τον υπολογισμό των παραγώγων της συνάρτησης απώλειας ως προς τις παραμέτρους.

Σημειώστε ότι το αριστερότερο μέρος όλων αυτών των εκφράσεων είναι το ίδιο, και έτσι μπορούμε να υπολογίσουμε αποτελεσματικά τις παραγώγους ξεκινώντας από τη συνάρτηση απώλειας και πηγαίνοντας "πίσω" μέσα από το υπολογιστικό γράφο. Έτσι, η μέθοδος εκπαίδευσης ενός πολυεπίπεδου perceptron ονομάζεται **backpropagation**, ή απλά 'backprop'.



> TODO: image citation

> ✅ Θα καλύψουμε το backprop με πολύ περισσότερες λεπτομέρειες στο παράδειγμα του notebook μας.  

## Συμπέρασμα

Σε αυτό το μάθημα, κατασκευάσαμε τη δική μας βιβλιοθήκη νευρωνικών δικτύων, και τη χρησιμοποιήσαμε για ένα απλό πρόβλημα ταξινόμησης δύο διαστάσεων.

## 🚀 Πρόκληση

Στο συνοδευτικό notebook, θα υλοποιήσετε το δικό σας πλαίσιο για την κατασκευή και εκπαίδευση πολυεπίπεδων perceptrons. Θα μπορέσετε να δείτε αναλυτικά πώς λειτουργούν τα σύγχρονα νευρωνικά δίκτυα.

Προχωρήστε στο OwnFramework notebook και δουλέψτε πάνω σε αυτό.



## Επανάληψη & Αυτοδιδασκαλία

Το backpropagation είναι ένας κοινός αλγόριθμος που χρησιμοποιείται στην Τεχνητή Νοημοσύνη και τη Μηχανική Μάθηση, αξίζει να μελετηθεί πιο αναλυτικά.

## Εργασία

Σε αυτό το εργαστήριο, σας ζητείται να χρησιμοποιήσετε το πλαίσιο που κατασκευάσατε σε αυτό το μάθημα για να λύσετε το πρόβλημα ταξινόμησης χειρόγραφων ψηφίων MNIST.

* Οδηγίες  
* Notebook

**Αποποίηση ευθυνών**:  
Αυτό το έγγραφο έχει μεταφραστεί χρησιμοποιώντας την υπηρεσία αυτόματης μετάφρασης AI [Co-op Translator](https://github.com/Azure/co-op-translator). Παρόλο που επιδιώκουμε την ακρίβεια, παρακαλούμε να έχετε υπόψη ότι οι αυτόματες μεταφράσεις ενδέχεται να περιέχουν λάθη ή ανακρίβειες. Το πρωτότυπο έγγραφο στη γλώσσα του θεωρείται η αυθεντική πηγή. Για κρίσιμες πληροφορίες, συνιστάται επαγγελματική ανθρώπινη μετάφραση. Δεν φέρουμε ευθύνη για τυχόν παρεξηγήσεις ή λανθασμένες ερμηνείες που προκύπτουν από τη χρήση αυτής της μετάφρασης.