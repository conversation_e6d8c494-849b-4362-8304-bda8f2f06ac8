<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T16:57:50+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "el"
}
-->
# Εισαγωγή στα Νευρωνικά Δίκτυα: Perceptron

Μία από τις πρώτες προσπάθειες υλοποίησης κάτι παρόμοιου με ένα σύγχρονο νευρωνικό δίκτυο έγινε από τον Frank <PERSON> από το Cornell Aeronautical Laboratory το 1957. Ήταν μια υλοποίηση υλικού που ονομάστηκε "Mark-1", σχεδιασμένη να αναγνωρίζει πρωτόγονες γεωμετρικές μορφές, όπως τρίγωνα, τετράγωνα και κύκλους.

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='Frank Rosenblatt'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> Εικόνες από τη Wikipedia

Μια εικόνα εισόδου αναπαριστάτο από έναν πίνακα 20x20 φωτοκυττάρων, έτσι το νευρωνικό δίκτυο είχε 400 εισόδους και μία δυαδική έξοδο. Ένα απλό δίκτυο περιείχε έναν νευρώνα, που ονομάζεται επίσης **threshold logic unit**. Τα βάρη του νευρωνικού δικτύου λειτουργούσαν σαν ποτενσιόμετρα που απαιτούσαν χειροκίνητη ρύθμιση κατά τη φάση εκπαίδευσης.

> ✅ Ένα ποτενσιόμετρο είναι μια συσκευή που επιτρέπει στον χρήστη να ρυθμίζει την αντίσταση ενός κυκλώματος.

> Οι New York Times έγραψαν για το perceptron εκείνη την εποχή: *το έμβρυο ενός ηλεκτρονικού υπολογιστή που [το Ναυτικό] αναμένει να μπορεί να περπατά, μιλά, βλέπει, γράφει, αναπαράγεται και να έχει επίγνωση της ύπαρξής του.*

## Μοντέλο Perceptron

Ας υποθέσουμε ότι έχουμε N χαρακτηριστικά στο μοντέλο μας, οπότε το διάνυσμα εισόδου θα είναι ένα διάνυσμα μεγέθους N. Το perceptron είναι ένα μοντέλο **δυαδικής ταξινόμησης**, δηλαδή μπορεί να διακρίνει μεταξύ δύο κατηγοριών δεδομένων εισόδου. Θα υποθέσουμε ότι για κάθε διάνυσμα εισόδου x, η έξοδος του perceptron μας θα είναι είτε +1 είτε -1, ανάλογα με την κατηγορία. Η έξοδος θα υπολογίζεται με τον τύπο:

y(x) = f(w<sup>T</sup>x)

όπου f είναι μια συνάρτηση ενεργοποίησης βήματος

## Εκπαίδευση του Perceptron

Για να εκπαιδεύσουμε ένα perceptron πρέπει να βρούμε ένα διάνυσμα βαρών w που ταξινομεί σωστά τις περισσότερες τιμές, δηλαδή να έχει το μικρότερο **σφάλμα**. Αυτό το σφάλμα ορίζεται από το **κριτήριο perceptron** ως εξής:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

όπου:

* το άθροισμα λαμβάνεται για τα σημεία εκπαίδευσης i που οδηγούν σε λανθασμένη ταξινόμηση
* x<sub>i</sub> είναι τα δεδομένα εισόδου, και t<sub>i</sub> είναι είτε -1 είτε +1 για αρνητικά και θετικά παραδείγματα αντίστοιχα.

Αυτό το κριτήριο θεωρείται ως συνάρτηση των βαρών w, και πρέπει να το ελαχιστοποιήσουμε. Συχνά χρησιμοποιείται μια μέθοδος που ονομάζεται **κατιούσα κλίση (gradient descent)**, όπου ξεκινάμε με κάποια αρχικά βάρη w<sup>(0)</sup>, και στη συνέχεια σε κάθε βήμα ενημερώνουμε τα βάρη σύμφωνα με τον τύπο:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

Εδώ η είναι ο λεγόμενος **ρυθμός μάθησης**, και ∇E(w) δηλώνει την **κλίση** της E. Αφού υπολογίσουμε την κλίση, καταλήγουμε σε

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

Ο αλγόριθμος σε Python μοιάζει ως εξής:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## Συμπέρασμα

Σε αυτό το μάθημα, μάθατε για το perceptron, που είναι ένα μοντέλο δυαδικής ταξινόμησης, και πώς να το εκπαιδεύσετε χρησιμοποιώντας ένα διάνυσμα βαρών.

## 🚀 Πρόκληση

Αν θέλετε να δοκιμάσετε να φτιάξετε το δικό σας perceptron, δοκιμάστε αυτό το εργαστήριο στο Microsoft Learn που χρησιμοποιεί το Azure ML designer


## Επισκόπηση & Αυτοδιδασκαλία

Για να δείτε πώς μπορούμε να χρησιμοποιήσουμε το perceptron για να λύσουμε ένα απλό πρόβλημα καθώς και προβλήματα πραγματικής ζωής, και να συνεχίσετε τη μάθηση - μεταβείτε στο Perceptron notebook.

Εδώ υπάρχει και ένα ενδιαφέρον άρθρο για τα perceptrons.

## Ανάθεση

Σε αυτό το μάθημα, υλοποιήσαμε ένα perceptron για εργασία δυαδικής ταξινόμησης, και το χρησιμοποιήσαμε για να ταξινομήσουμε ανάμεσα σε δύο χειρόγραφους αριθμούς. Σε αυτό το εργαστήριο, σας ζητείται να λύσετε πλήρως το πρόβλημα της ταξινόμησης ψηφίων, δηλαδή να προσδιορίσετε ποιο ψηφίο είναι πιο πιθανό να αντιστοιχεί σε μια δεδομένη εικόνα.

* Οδηγίες
* Notebook

**Αποποίηση ευθυνών**:  
Αυτό το έγγραφο έχει μεταφραστεί χρησιμοποιώντας την υπηρεσία αυτόματης μετάφρασης AI [Co-op Translator](https://github.com/Azure/co-op-translator). Παρόλο που προσπαθούμε για ακρίβεια, παρακαλούμε να έχετε υπόψη ότι οι αυτόματες μεταφράσεις ενδέχεται να περιέχουν λάθη ή ανακρίβειες. Το πρωτότυπο έγγραφο στη γλώσσα του θεωρείται η αυθεντική πηγή. Για κρίσιμες πληροφορίες, συνιστάται επαγγελματική ανθρώπινη μετάφραση. Δεν φέρουμε ευθύνη για τυχόν παρεξηγήσεις ή λανθασμένες ερμηνείες που προκύπτουν από τη χρήση αυτής της μετάφρασης.