<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:02:09+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "el"
}
-->
# Πόροι για Αυτοκαθοδηγούμενη Μάθηση

Το μάθημα δημιουργήθηκε χρησιμοποιώντας μια σειρά βασικών πόρων από το OpenAI και το Azure OpenAI ως αναφορές για την ορολογία και τα σεμινάρια. Εδ<PERSON> είναι μια μη εξαντλητική λίστα, για τα δικά σας ταξίδια αυτοκαθοδηγούμενης μάθησης.

## 1. <PERSON><PERSON><PERSON>ι<PERSON><PERSON> Πόροι

| Τίτλος/Σύνδεσμος                                                                                                                                                                                                             | Περιγραφή                                                                                                                                                                                                                                                                                                                                                                                     |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Η προσαρμογή (fine-tuning) βελτιώνει τη μάθηση με λίγα παραδείγματα (few-shot learning) εκπαιδεύοντας σε πολύ περισσότερα παραδείγματα από όσα χωρούν στο prompt, εξοικονομώντας κόστος, βελτιώνοντας την ποιότητα των απαντήσεων και επιτρέποντας αιτήματα με χαμηλότερη καθυστέρηση. **Αποκτήστε μια επισκόπηση της προσαρμογής από το OpenAI.**                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Κατανοήστε **τι είναι η προσαρμογή (fine-tuning) (έννοια)**, γιατί αξίζει να την εξετάσετε (το πρόβλημα που την κινητοποιεί), ποια δεδομένα να χρησιμοποιήσετε (εκπαίδευση) και πώς να μετρήσετε την ποιότητα.                                                                                                                                                                               |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Η υπηρεσία Azure OpenAI σας επιτρέπει να προσαρμόσετε τα μοντέλα μας στα προσωπικά σας σύνολα δεδομένων μέσω προσαρμογής. Μάθετε **πώς να κάνετε fine-tuning (διαδικασία)** επιλέγοντας μοντέλα με το Azure AI Studio, το Python SDK ή το REST API.                                                                                                                                            |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | Τα LLM μπορεί να μην αποδίδουν καλά σε συγκεκριμένους τομείς, εργασίες ή σύνολα δεδομένων, ή να παράγουν ανακριβείς ή παραπλανητικές απαντήσεις. **Πότε πρέπει να σκεφτείτε την προσαρμογή (fine-tuning)** ως πιθανή λύση σε αυτό;                                                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Η συνεχής προσαρμογή είναι η επαναληπτική διαδικασία επιλογής ενός ήδη προσαρμοσμένου μοντέλου ως βασικού και **η περαιτέρω προσαρμογή του** σε νέα σύνολα παραδειγμάτων εκπαίδευσης.                                                                                                                                                                                                             |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Η προσαρμογή του μοντέλου σας **με παραδείγματα κλήσης συναρτήσεων** μπορεί να βελτιώσει την απόδοση, παρέχοντας πιο ακριβείς και συνεπείς απαντήσεις - με ομοιόμορφα μορφοποιημένες αποκρίσεις και εξοικονόμηση κόστους.                                                                                                                                    |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Ανατρέξτε σε αυτόν τον πίνακα για να κατανοήσετε **ποια μοντέλα μπορούν να προσαρμοστούν** στο Azure OpenAI και σε ποιες περιοχές είναι διαθέσιμα. Ελέγξτε τα όρια tokens και τις ημερομηνίες λήξης των δεδομένων εκπαίδευσης αν χρειάζεται.                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Αυτό το 30λεπτο επεισόδιο του AI Show **Οκτώβριος 2023** συζητά τα οφέλη, τα μειονεκτήματα και πρακτικές γνώσεις που θα σας βοηθήσουν να πάρετε αυτή την απόφαση.                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Αυτός ο **AI Playbook** πόρος σας καθοδηγεί στα απαιτούμενα δεδομένα, τη μορφοποίηση, την προσαρμογή υπερπαραμέτρων και τις προκλήσεις/περιορισμούς που πρέπει να γνωρίζετε.                                                                                                                                                               |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Μάθετε πώς να δημιουργήσετε ένα δείγμα συνόλου δεδομένων για προσαρμογή, να προετοιμαστείτε για fine-tuning, να δημιουργήσετε μια εργασία προσαρμογής και να αναπτύξετε το προσαρμοσμένο μοντέλο στο Azure.                                                                                                                                |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Το Azure AI Studio σας επιτρέπει να προσαρμόσετε μεγάλα γλωσσικά μοντέλα στα προσωπικά σας δεδομένα _μέσω μιας διεπαφής χρήστη κατάλληλης για προγραμματιστές με χαμηλό κώδικα_. Δείτε αυτό το παράδειγμα.                                                                                                                               |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Αυτό το άρθρο περιγράφει πώς να προσαρμόσετε ένα μοντέλο Hugging Face με τη βιβλιοθήκη transformers σε μία GPU χρησιμοποιώντας Azure DataBricks και τις βιβλιοθήκες Hugging Face Trainer.                                                                                                                                               |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Ο κατάλογος μοντέλων στο Azure Machine Learning προσφέρει πολλά ανοιχτού κώδικα μοντέλα που μπορείτε να προσαρμόσετε για την ειδική σας εργασία. Δοκιμάστε αυτό το μάθημα από τη [Διαδρομή Μάθησης για Γενετικά Μοντέλα στο AzureML](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst).                                   |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Η προσαρμογή μοντέλων GPT-3.5 ή GPT-4 στο Microsoft Azure με χρήση W&B επιτρέπει λεπτομερή παρακολούθηση και ανάλυση της απόδοσης του μοντέλου. Αυτός ο οδηγός επεκτείνει τις έννοιες από τον οδηγό OpenAI Fine-Tuning με συγκεκριμένα βήματα και λειτουργίες για το Azure OpenAI.                                                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                              |

## 2. Δευτερεύοντες Πόροι

Αυτή η ενότητα περιλαμβάνει επιπλέον πόρους που αξίζει να εξερευνήσετε, αλλά δεν είχαμε χρόνο να καλύψουμε σε αυτό το μάθημα. Μπορεί να καλυφθούν σε μελλοντικό μάθημα ή ως δευτερεύουσα εργασία αργότερα. Προς το παρόν, χρησιμοποιήστε τους για να χτίσετε τη δική σας εμπειρογνωμοσύνη και γνώση γύρω από το θέμα.

| Τίτλος/Σύνδεσμος                                                                                                                                                                                                                  | Περιγραφή                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Προετοιμασία και ανάλυση δεδομένων για fine-tuning μοντέλου συνομιλίας](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                        | Αυτό το σημειωματάριο λειτουργεί ως εργαλείο για την προεπεξεργασία και ανάλυση του συνόλου δεδομένων συνομιλίας που χρησιμοποιείται για την προσαρμογή μοντέλου συνομιλίας. Ελέγχει για σφάλματα μορφοποίησης, παρέχει βασικά στατιστικά και εκτιμά τον αριθμό tokens για το κόστος της προσαρμογής. Δείτε: [Μέθοδος προσαρμογής για gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning για Retrieval Augmented Generation (RAG) με Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)                   | Σκοπός αυτού του σημειωματαρίου είναι να παρουσιάσει ένα ολοκληρωμένο παράδειγμα για το πώς να προσαρμόσετε μοντέλα OpenAI για Retrieval Augmented Generation (RAG). Θα ενσωματώσουμε επίσης Qdrant και Few-Shot Learning για να βελτιώσουμε την απόδοση του μοντέλου και να μειώσουμε τις ανακρίβειες.                                                                                                                                                                                                                   |
| **OpenAI Cookbook**: [Fine-tuning GPT με Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                                           | Το Weights & Biases (W&B) είναι η πλατφόρμα για προγραμματιστές AI, με εργαλεία για εκπαίδευση μοντέλων, προσαρμογή μοντέλων και αξιοποίηση θεμελιωδών μοντέλων. Διαβάστε πρώτα τον οδηγό τους [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) και μετά δοκιμάστε την άσκηση Cookbook.                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - προσαρμογή για Μικρά Γλωσσικά Μοντέλα                                                                 | Γνωρίστε το [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), το νέο μικρό μοντέλο της Microsoft, εξαιρετικά ισχυρό αλλά συμπαγές. Αυτό το σεμινάριο θα σας καθοδηγήσει στην προσαρμογή του Phi-2, δείχνοντας πώς να δημιουργήσετε ένα μοναδικό σύνολο δεδομένων και να προσαρμόσετε το μοντέλο χρησιμοποιώντας QLoRA.                                                                                                         |
| **Hugging Face Tutorial** [Πώς να προσαρμόσετε LLMs το 2024 με Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                                        | Αυτό το άρθρο σας καθοδηγεί πώς να προσαρμόσετε ανοιχτά LLMs χρησιμοποιώντας Hugging Face TRL, Transformers και datasets το 2024. Ορίζετε μια περίπτωση χρήσης, στήνετε περιβάλλον ανάπτυξης, προετοιμάζετε σύνολο δεδομένων, προσαρμόζετε το μοντέλο, το δοκιμάζετε και το αξιολογείτε, και μετά το αναπτύσσετε σε παραγωγή.                                                                                                                                    |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                                   | Προσφέρει ταχύτερη και ευκολότερη εκπαίδευση και ανάπτυξη [μοντέλων μηχανικής μάθησης αιχμής](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Το αποθετήριο περιλαμβάνει tutorials φιλικά προς το Colab με βίντεο στο YouTube για καθοδήγηση στην προσαρμογή. **Αντανακλά την πρόσφατη ενημέρωση [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst).** Διαβάστε την [τεκμηρίωση AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst). |
|                                                                                                                                                                                                                               |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Αποποίηση ευθυνών**:  
Αυτό το έγγραφο έχει μεταφραστεί χρησιμοποιώντας την υπηρεσία αυτόματης μετάφρασης AI [Co-op Translator](https://github.com/Azure/co-op-translator). Παρόλο που επιδιώκουμε την ακρίβεια, παρακαλούμε να έχετε υπόψη ότι οι αυτόματες μεταφράσεις ενδέχεται να περιέχουν λάθη ή ανακρίβειες. Το πρωτότυπο έγγραφο στη γλώσσα του θεωρείται η αυθεντική πηγή. Για κρίσιμες πληροφορίες, συνιστάται επαγγελματική ανθρώπινη μετάφραση. Δεν φέρουμε ευθύνη για τυχόν παρεξηγήσεις ή λανθασμένες ερμηνείες που προκύπτουν από τη χρήση αυτής της μετάφρασης.