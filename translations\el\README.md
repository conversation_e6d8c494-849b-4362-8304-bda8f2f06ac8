<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:33:08+00:00",
  "source_file": "README.md",
  "language_code": "el"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.el.png)

### 21 Μαθήματα που διδάσκουν όσα χρειάζεστε για να ξεκινήσετε να δημιουργείτε εφαρμογές Γεννητικής Τεχνητής Νοημοσύνης

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Υποστήριξη Πολλών Γλωσσών

#### Υποστηρίζεται μέσω GitHub Action (Αυτοματοποιημένο & Πάντα Ενημερωμένο)

[French](../fr/README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](../ja/README.md) | [Korean](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](./README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](../he/README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](../sk/README.md) | [Romanian](../ro/README.md) | [Bulgarian](../bg/README.md) | [Serbian (Cyrillic)](../sr/README.md) | [Croatian](../hr/README.md) | [Slovenian](../sl/README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Γεννητική Τεχνητή Νοημοσύνη για Αρχάριους (Έκδοση 3) - Ένα Μάθημα

Μάθετε τα βασικά για τη δημιουργία εφαρμογών Γεννητικής Τεχνητής Νοημοσύνης με το ολοκληρωμένο μάθημα 21 μαθημάτων από τους Microsoft Cloud Advocates.

## 🌱 Ξεκινώντας

Αυτό το μάθημα περιλαμβάνει 21 μαθήματα. Κάθε μάθημα καλύπτει ένα συγκεκριμένο θέμα, οπότε ξεκινήστε από όπου θέλετε!

Τα μαθήματα είναι είτε "Learn" που εξηγούν μια έννοια της Γεννητικής Τεχνητής Νοημοσύνης είτε "Build" που εξηγούν μια έννοια και παραδείγματα κώδικα σε **Python** και **TypeScript** όπου είναι δυνατόν.

Για προγραμματιστές .NET δείτε το [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

Κάθε μάθημα περιλαμβάνει επίσης μια ενότητα "Keep Learning" με επιπλέον εργαλεία μάθησης.

## Τι χρειάζεστε
### Για να τρέξετε τον κώδικα αυτού του μαθήματος, μπορείτε να χρησιμοποιήσετε είτε: 
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **Μαθήματα:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **Μαθήματα:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **Μαθήματα:** "oai-assignment" 
   
- Βασικές γνώσεις Python ή TypeScript είναι χρήσιμες - \*Για απόλυτους αρχάριους δείτε αυτά τα μαθήματα [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) και [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst)
- Λογαριασμό GitHub για να [κάνετε fork ολόκληρου αυτού του αποθετηρίου](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) στον δικό σας λογαριασμό GitHub

Έχουμε δημιουργήσει ένα μάθημα **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** για να σας βοηθήσουμε να ρυθμίσετε το περιβάλλον ανάπτυξής σας.

Μην ξεχάσετε να [κάνετε star (🌟) σε αυτό το αποθετήριο](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst) για να το βρίσκετε πιο εύκολα αργότερα.

## 🧠 Έτοιμοι για Ανάπτυξη;

Αν ψάχνετε για πιο προχωρημένα παραδείγματα κώδικα, δείτε τη [συλλογή μας με Παραδείγματα Κώδικα Γεννητικής Τεχνητής Νοημοσύνης](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) σε **Python** και **TypeScript**.

## 🗣️ Γνωρίστε Άλλους Μαθητές, Λάβετε Υποστήριξη

Εγγραφείτε στον [επίσημο Discord server του Azure AI Foundry](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) για να γνωρίσετε και να δικτυωθείτε με άλλους μαθητές που παρακολουθούν αυτό το μάθημα και να λάβετε υποστήριξη.

Κάντε ερωτήσεις ή μοιραστείτε σχόλια για το προϊόν στο [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) στο Github.

## 🚀 Δημιουργείτε Startup;

Εγγραφείτε στο [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) για να λάβετε **δωρεάν πιστώσεις OpenAI** και έως **$150k σε πιστώσεις Azure για πρόσβαση σε μοντέλα OpenAI μέσω Azure OpenAI Services**.

## 🙏 Θέλετε να βοηθήσετε;

Έχετε προτάσεις ή βρήκατε ορθογραφικά ή σφάλματα κώδικα; [Ανοίξτε ένα issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) ή [Δημιουργήστε ένα pull request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Κάθε μάθημα περιλαμβάνει:

- Μια σύντομη εισαγωγική παρουσίαση στο θέμα
- Ένα γραπτό μάθημα στο README
- Παραδείγματα κώδικα σε Python και TypeScript που υποστηρίζουν Azure OpenAI και OpenAI API
- Συνδέσμους με επιπλέον πόρους για να συνεχίσετε τη μάθησή σας

## 🗃️ Μαθήματα

| #   | **Σύνδεσμος Μαθήματος**                                                                                                                      | **Περιγραφή**                                                                                  | **Βίντεο**                                                                  | **Επιπλέον Μάθηση**                                                            |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                   | **Μάθετε:** Πώς να ρυθμίσετε το περιβάλλον ανάπτυξής σας                                      | Βίντεο Έρχεται Σύντομα                                                       | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Introduction to Generative AI and LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                                | **Μάθετε:** Κατανόηση τι είναι η Γεννητική Τεχνητή Νοημοσύνη και πώς λειτουργούν τα Μεγάλα Γλωσσικά Μοντέλα (LLMs). | [Βίντεο](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Exploring and comparing different LLMs](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)               | **Μάθετε:** Πώς να επιλέξετε το κατάλληλο μοντέλο για την περίπτωσή σας                        | [Βίντεο](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Using Generative AI Responsibly](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                             | **Μάθετε:** Πώς να δημιουργείτε εφαρμογές Γεννητικής Τεχνητής Νοημοσύνης με υπευθυνότητα       | [Βίντεο](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Understanding Prompt Engineering Fundamentals](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)               | **Μάθετε:** Πρακτικές βέλτιστες τεχνικές για το Prompt Engineering                             | [Βίντεο](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Creating Advanced Prompts](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                  | **Μάθετε:** Πώς να εφαρμόζετε τεχνικές prompt engineering που βελτιώνουν το αποτέλεσμα των prompts σας. | [Βίντεο](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Κατασκευή Εφαρμογών Δημιουργίας Κειμένου](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Κατασκευή:** Μια εφαρμογή δημιουργίας κειμένου χρησιμοποιώντας το Azure OpenAI / OpenAI API                                | [Βίντεο](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Κατασκευή Εφαρμογών Συνομιλίας](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Κατασκευή:** Τεχνικές για αποδοτική δημιουργία και ενσωμάτωση εφαρμογών συνομιλίας.               | [Βίντεο](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Κατασκευή Εφαρμογών Αναζήτησης με Βάσεις Δεδομένων Διανυσμάτων](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Κατασκευή:** Μια εφαρμογή αναζήτησης που χρησιμοποιεί Embeddings για την αναζήτηση δεδομένων.                        | [Βίντεο](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Κατασκευή Εφαρμογών Δημιουργίας Εικόνων](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Κατασκευή:** Μια εφαρμογή δημιουργίας εικόνων                                                       | [Βίντεο](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Κατασκευή Εφαρμογών Τεχνητής Νοημοσύνης με Χαμηλό Κώδικα](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Κατασκευή:** Μια εφαρμογή Γεννητικής Τεχνητής Νοημοσύνης χρησιμοποιώντας εργαλεία Χαμηλού Κώδικα                                     | [Βίντεο](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Ενσωμάτωση Εξωτερικών Εφαρμογών με Function Calling](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **Κατασκευή:** Τι είναι το function calling και οι περιπτώσεις χρήσης του σε εφαρμογές                          | [Βίντεο](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [Σχεδιασμός UX για Εφαρμογές Τεχνητής Νοημοσύνης](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Μάθετε:** Πώς να εφαρμόζετε αρχές σχεδιασμού UX κατά την ανάπτυξη εφαρμογών Γεννητικής Τεχνητής Νοημοσύνης         | [Βίντεο](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Ασφάλεια των Εφαρμογών Γεννητικής Τεχνητής Νοημοσύνης](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Μάθετε:** Οι απειλές και οι κίνδυνοι για τα συστήματα AI και οι μέθοδοι για την ασφάλειά τους.             | [Βίντεο](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Ο Κύκλος Ζωής Εφαρμογών Γεννητικής Τεχνητής Νοημοσύνης](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **Μάθετε:** Τα εργαλεία και οι μετρήσεις για τη διαχείριση του κύκλου ζωής LLM και του LLMOps                         | [Βίντεο](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) και Βάσεις Δεδομένων Διανυσμάτων](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **Κατασκευή:** Μια εφαρμογή που χρησιμοποιεί το πλαίσιο RAG για την ανάκτηση embeddings από Βάσεις Δεδομένων Διανυσμάτων  | [Βίντεο](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Ανοιχτά Μοντέλα και Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **Κατασκευή:** Μια εφαρμογή που χρησιμοποιεί ανοιχτά μοντέλα διαθέσιμα στο Hugging Face                    | [Βίντεο](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI Agents](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **Κατασκευή:** Μια εφαρμογή που χρησιμοποιεί το πλαίσιο AI Agent                                           | [Βίντεο](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [Fine-Tuning LLMs](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Μάθετε:** Τι είναι, γιατί και πώς γίνεται το fine-tuning των LLMs                                            | [Βίντεο](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Κατασκευή με SLMs](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Μάθετε:** Τα οφέλη της κατασκευής με Small Language Models                                            | Βίντεο Έρχεται Σύντομα | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Κατασκευή με Mistral Models](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Μάθετε:** Τα χαρακτηριστικά και οι διαφορές των μοντέλων της οικογένειας Mistral                                           | Βίντεο Έρχεται Σύντομα | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Κατασκευή με Meta Models](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Μάθετε:** Τα χαρακτηριστικά και οι διαφορές των μοντέλων της οικογένειας Meta                                           | Βίντεο Έρχεται Σύντομα | [Μάθετε Περισσότερα](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Ειδικές ευχαριστίες

Ειδικές ευχαριστίες στον [**John Aziz**](https://www.linkedin.com/in/john0isaac/) για τη δημιουργία όλων των GitHub Actions και workflows

τον [**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) για τις σημαντικές συνεισφορές σε κάθε μάθημα που βελτιώνουν την εμπειρία του μαθητή και του κώδικα.

## 🎒 Άλλα Μαθήματα

Η ομάδα μας παράγει και άλλα μαθήματα! Ρίξτε μια ματιά:

- [**ΝΕΟ** Πρωτόκολλο Πλαισίου Μοντέλου για Αρχάριους](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI Agents για Αρχάριους](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Γεννητική Τεχνητή Νοημοσύνη για Αρχάριους με .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Γεννητική Τεχνητή Νοημοσύνη για Αρχάριους με JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [Μηχανική Μάθηση για Αρχάριους](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Επιστήμη Δεδομένων για Αρχάριους](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [Τεχνητή Νοημοσύνη για Αρχάριους](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Κυβερνοασφάλεια για Αρχάριους](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Ανάπτυξη Ιστοσελίδων για Αρχάριους](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT για Αρχάριους](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [Ανάπτυξη XR για Αρχάριους](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Εξοικείωση με το GitHub Copilot για Προγραμματισμό με Τεχνητή Νοημοσύνη](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Εξοικείωση με το GitHub Copilot για Προγραμματιστές C#/.NET](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Διάλεξε τη Δική σου Περιπέτεια με το Copilot](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Αποποίηση ευθυνών**:  
Αυτό το έγγραφο έχει μεταφραστεί χρησιμοποιώντας την υπηρεσία αυτόματης μετάφρασης AI [Co-op Translator](https://github.com/Azure/co-op-translator). Παρόλο που επιδιώκουμε την ακρίβεια, παρακαλούμε να έχετε υπόψη ότι οι αυτόματες μεταφράσεις ενδέχεται να περιέχουν λάθη ή ανακρίβειες. Το πρωτότυπο έγγραφο στη γλώσσα του θεωρείται η επίσημη πηγή. Για κρίσιμες πληροφορίες, συνιστάται επαγγελματική ανθρώπινη μετάφραση. Δεν φέρουμε ευθύνη για τυχόν παρεξηγήσεις ή λανθασμένες ερμηνείες που προκύπτουν από τη χρήση αυτής της μετάφρασης.