<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:52:42+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "en"
}
-->
# Resources For Self-Guided Learning

The lesson was created using several key resources from OpenAI and Azure OpenAI as references for terminology and tutorials. Here is a non-exhaustive list for your own self-guided learning journey.

## 1. Primary Resources

| Title/Link                                                                                                                                                                                                                   | Description                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning enhances few-shot learning by training on many more examples than can fit in the prompt, saving costs, improving response quality, and enabling lower-latency requests. **Get an overview of fine-tuning from OpenAI.**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Learn **what fine-tuning is (concept)**, why it’s worth considering (motivating problem), what data to use (training), and how to measure quality.                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service lets you adapt models to your own datasets using fine-tuning. Learn **how to fine-tune (process)** selected models using Azure AI Studio, Python SDK, or REST API.                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLMs may struggle with specific domains, tasks, or datasets, or produce inaccurate or misleading outputs. **When should you consider fine-tuning** as a solution?                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Continuous fine-tuning is the iterative process of taking an already fine-tuned model as a base and **fine-tuning it further** on new training examples.                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fine-tuning your model **with function calling examples** can improve output by producing more accurate and consistent responses with similar formatting, while also saving costs.                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Check this table to see **which models can be fine-tuned** in Azure OpenAI, their available regions, token limits, and training data expiration dates if needed.                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | This 30-minute **Oct 2023** episode of the AI Show covers benefits, drawbacks, and practical insights to help you decide whether to fine-tune.                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | This **AI Playbook** resource guides you through data requirements, formatting, hyperparameter tuning, and challenges/limitations to be aware of.                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Learn how to create a sample fine-tuning dataset, prepare for fine-tuning, create a fine-tuning job, and deploy the fine-tuned model on Azure.                                                                                                                                                                                    |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio lets you customize large language models to your own datasets _using a UI-based workflow suitable for low-code developers_. See this example.                                                                                                                                                               |
| **Tutorial**: [Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | This article explains how to fine-tune a Hugging Face model with the transformers library on a single GPU using Azure Databricks and Hugging Face Trainer libraries.                                                                                                                                                |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Azure Machine Learning’s model catalog offers many open-source models you can fine-tune for your specific task. Try this module from the [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst). |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fine-tuning GPT-3.5 or GPT-4 models on Microsoft Azure using W&B enables detailed tracking and analysis of model performance. This guide builds on the OpenAI Fine-Tuning guide with specific steps and features for Azure OpenAI.                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. Secondary Resources

This section lists additional resources worth exploring, though we didn’t have time to cover them in this lesson. They may be included in future lessons or offered as optional assignments later. For now, use them to deepen your expertise and knowledge on this topic.

| Title/Link                                                                                                                                                                                                            | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Data preparation and analysis for chat model fine-tuning](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | This notebook helps preprocess and analyze chat datasets used for fine-tuning chat models. It checks for formatting errors, provides basic statistics, and estimates token counts to help calculate fine-tuning costs. See: [Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning for Retrieval Augmented Generation (RAG) with Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | This notebook walks through a detailed example of fine-tuning OpenAI models for Retrieval Augmented Generation (RAG). It also integrates Qdrant and Few-Shot Learning to boost model performance and reduce hallucinations.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT with Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) is an AI developer platform with tools for training, fine-tuning, and leveraging foundation models. Read their [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) guide first, then try the Cookbook exercise.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning for Small Language Models                                                   | Meet [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), Microsoft’s new small but powerful model. This tutorial guides you through fine-tuning Phi-2, showing how to build a custom dataset and fine-tune the model using QLoRA.                                                                                                                                                                       |
| **Hugging Face Tutorial** [How to Fine-Tune LLMs in 2024 with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | This blog post walks you through fine-tuning open LLMs using Hugging Face TRL, Transformers, and datasets in 2024. You define a use case, set up a dev environment, prepare a dataset, fine-tune the model, evaluate it, and deploy it to production.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Offers faster and easier training and deployment of [state-of-the-art machine learning models](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). The repo includes Colab-friendly tutorials with YouTube video guides for fine-tuning. **Reflects recent [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) update**. See the [AutoTrain documentation](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst). |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Disclaimer**:  
This document has been translated using the AI translation service [Co-op Translator](https://github.com/Azure/co-op-translator). While we strive for accuracy, please be aware that automated translations may contain errors or inaccuracies. The original document in its native language should be considered the authoritative source. For critical information, professional human translation is recommended. We are not liable for any misunderstandings or misinterpretations arising from the use of this translation.