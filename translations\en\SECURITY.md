<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:50:04+00:00",
  "source_file": "SECURITY.md",
  "language_code": "en"
}
-->
## Security

Microsoft takes the security of our software products and services seriously, including all source code repositories managed through our GitHub organizations, such as [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin), and [our GitHub organizations](https://opensource.microsoft.com/).

If you believe you have discovered a security vulnerability in any Microsoft-owned repository that fits [Microsoft's definition of a security vulnerability](https://aka.ms/opensource/security/definition), please report it to us as described below.

## Reporting Security Issues

**Please do not report security vulnerabilities through public GitHub issues.**

Instead, report them to the Microsoft Security Response Center (MSRC) at [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

If you prefer to submit without logging in, send an email to [<EMAIL>](mailto:<EMAIL>). If possible, encrypt your message using our PGP key; you can download it from the [Microsoft Security Response Center PGP Key page](https://aka.ms/opensource/security/pgpkey).

You should receive a response within 24 hours. If you don’t, please follow up by email to confirm we received your original message. Additional information is available at [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Please include the following information (as much as you can provide) to help us better understand the nature and scope of the potential issue:

  * Type of issue (e.g., buffer overflow, SQL injection, cross-site scripting, etc.)
  * Full paths of source file(s) related to the issue
  * Location of the affected source code (tag/branch/commit or direct URL)
  * Any special configuration needed to reproduce the issue
  * Step-by-step instructions to reproduce the issue
  * Proof-of-concept or exploit code (if available)
  * Impact of the issue, including how an attacker might exploit it

This information will help us prioritize your report more efficiently.

If you are submitting a bug bounty report, more detailed reports can lead to higher rewards. Please visit our [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) page for more information about our active programs.

## Preferred Languages

We prefer all communications to be in English.

## Policy

Microsoft follows the principle of [Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd).

**Disclaimer**:  
This document has been translated using the AI translation service [Co-op Translator](https://github.com/Azure/co-op-translator). While we strive for accuracy, please be aware that automated translations may contain errors or inaccuracies. The original document in its native language should be considered the authoritative source. For critical information, professional human translation is recommended. We are not liable for any misunderstandings or misinterpretations arising from the use of this translation.