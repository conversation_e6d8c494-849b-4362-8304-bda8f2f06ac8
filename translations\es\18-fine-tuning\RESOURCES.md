<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:53:21+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "es"
}
-->
# Recursos para el Aprendizaje Autónomo

La lección se construyó utilizando varios recursos clave de OpenAI y Azure OpenAI como referencia para la terminología y los tutoriales. Aquí tienes una lista no exhaustiva para tus propios recorridos de aprendizaje autónomo.

## 1. Recursos Principales

| Título/Enlace                                                                                                                                                                                                                   | Descripción                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | El fine-tuning mejora el aprendizaje con pocos ejemplos entrenando con muchos más ejemplos de los que caben en el prompt, ahorrando costos, mejorando la calidad de las respuestas y permitiendo solicitudes con menor latencia. **Obtén una visión general del fine-tuning de OpenAI.**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Entiende **qué es el fine-tuning (concepto)**, por qué deberías considerarlo (problema motivador), qué datos usar (entrenamiento) y cómo medir la calidad.                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service te permite adaptar nuestros modelos a tus propios conjuntos de datos mediante fine-tuning. Aprende **cómo hacer fine-tuning (proceso)** y seleccionar modelos usando Azure AI Studio, Python SDK o REST API.                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | Los LLMs pueden no funcionar bien en dominios, tareas o conjuntos de datos específicos, o pueden generar resultados inexactos o engañosos. **¿Cuándo deberías considerar el fine-tuning** como posible solución?                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | El fine-tuning continuo es un proceso iterativo que consiste en seleccionar un modelo ya fine-tuneado como modelo base y **ajustarlo aún más** con nuevos conjuntos de ejemplos de entrenamiento.                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Hacer fine-tuning de tu modelo **con ejemplos de llamadas a funciones** puede mejorar la salida del modelo al obtener respuestas más precisas y consistentes, con respuestas formateadas de manera similar y ahorro en costos.                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Consulta esta tabla para entender **qué modelos se pueden fine-tunear** en Azure OpenAI y en qué regiones están disponibles. Consulta sus límites de tokens y fechas de expiración de datos de entrenamiento si es necesario.                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Este episodio de 30 minutos de octubre de 2023 del AI Show discute beneficios, desventajas e ideas prácticas que te ayudarán a tomar esta decisión.                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Este recurso del **AI Playbook** te guía a través de los requisitos de datos, formato, ajuste de hiperparámetros y desafíos/limitaciones que debes conocer.                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Aprende a crear un conjunto de datos de ejemplo para fine-tuning, prepararte para el fine-tuning, crear un trabajo de fine-tuning y desplegar el modelo fine-tuneado en Azure.                                                                                                                                                                                    |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio te permite adaptar grandes modelos de lenguaje a tus propios conjuntos de datos _usando un flujo de trabajo basado en UI adecuado para desarrolladores low-code_. Mira este ejemplo.                                                                                                                                                               |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Este artículo describe cómo hacer fine-tuning de un modelo Hugging Face con la biblioteca transformers de Hugging Face en una sola GPU usando Azure DataBricks + las librerías Hugging Face Trainer.                                                                                                                                                |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | El catálogo de modelos en Azure Machine Learning ofrece muchos modelos open source que puedes fine-tunear para tu tarea específica. Prueba este módulo que forma parte de la [ruta de aprendizaje de AzureML Generative AI](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst). |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Hacer fine-tuning de modelos GPT-3.5 o GPT-4 en Microsoft Azure usando W&B permite un seguimiento y análisis detallado del rendimiento del modelo. Esta guía amplía los conceptos de la guía de Fine-Tuning de OpenAI con pasos y características específicas para Azure OpenAI.                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. Recursos Secundarios

Esta sección recoge recursos adicionales que vale la pena explorar, pero que no tuvimos tiempo de cubrir en esta lección. Pueden ser tratados en una lección futura o como opción de tarea secundaria en una fecha posterior. Por ahora, úsalos para construir tu propia experiencia y conocimiento sobre este tema.

| Título/Enlace                                                                                                                                                                                                            | Descripción                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Preparación y análisis de datos para fine-tuning de modelos de chat](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | Este cuaderno sirve como herramienta para preprocesar y analizar el conjunto de datos de chat usado para el fine-tuning de un modelo de chat. Revisa errores de formato, proporciona estadísticas básicas y estima el conteo de tokens para calcular costos de fine-tuning. Consulta: [Método de fine-tuning para gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning para Retrieval Augmented Generation (RAG) con Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | El objetivo de este cuaderno es mostrar un ejemplo completo de cómo hacer fine-tuning de modelos OpenAI para Retrieval Augmented Generation (RAG). También integraremos Qdrant y Few-Shot Learning para mejorar el rendimiento del modelo y reducir las invenciones.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT con Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) es una plataforma para desarrolladores de IA, con herramientas para entrenar modelos, hacer fine-tuning y aprovechar modelos base. Lee primero su guía de [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) y luego prueba el ejercicio del Cookbook.                                                                                                                                                                                                                  |
| **Tutorial Comunitario** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning para Modelos de Lenguaje Pequeños                                                   | Conoce [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), el nuevo modelo pequeño de Microsoft, sorprendentemente potente y compacto. Este tutorial te guía para hacer fine-tuning de Phi-2, mostrando cómo construir un conjunto de datos único y ajustar el modelo usando QLoRA.                                                                                                                                                                       |
| **Tutorial Hugging Face** [Cómo hacer Fine-Tuning de LLMs en 2024 con Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Esta publicación explica cómo hacer fine-tuning de LLMs abiertos usando Hugging Face TRL, Transformers y datasets en 2024. Defines un caso de uso, configuras un entorno de desarrollo, preparas un dataset, haces fine-tuning del modelo, lo pruebas y evalúas, y luego lo despliegas en producción.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Facilita entrenamientos y despliegues más rápidos y sencillos de [modelos de aprendizaje automático de última generación](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). El repositorio incluye tutoriales compatibles con Colab y videos en YouTube para guiar el fine-tuning. **Refleja la reciente actualización [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)**. Lee la [documentación de AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst). |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Aviso legal**:  
Este documento ha sido traducido utilizando el servicio de traducción automática [Co-op Translator](https://github.com/Azure/co-op-translator). Aunque nos esforzamos por la precisión, tenga en cuenta que las traducciones automáticas pueden contener errores o inexactitudes. El documento original en su idioma nativo debe considerarse la fuente autorizada. Para información crítica, se recomienda la traducción profesional realizada por humanos. No nos hacemos responsables de malentendidos o interpretaciones erróneas derivadas del uso de esta traducción.