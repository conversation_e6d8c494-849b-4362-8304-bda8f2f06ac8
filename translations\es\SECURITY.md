<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:50:21+00:00",
  "source_file": "SECURITY.md",
  "language_code": "es"
}
-->
## Seguridad

Microsoft se toma en serio la seguridad de nuestros productos y servicios de software, lo que incluye todos los repositorios de código fuente gestionados a través de nuestras organizaciones en GitHub, que incluyen [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) y [nuestras organizaciones en GitHub](https://opensource.microsoft.com/).

Si crees haber encontrado una vulnerabilidad de seguridad en algún repositorio propiedad de Microsoft que cumpla con la [definición de vulnerabilidad de seguridad de Microsoft](https://aka.ms/opensource/security/definition), por favor repórtala como se indica a continuación.

## Reporte de problemas de seguridad

**Por favor, no reportes vulnerabilidades de seguridad a través de issues públicos en GitHub.**

En su lugar, repórtalas al Microsoft Security Response Center (MSRC) en [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Si prefieres enviar el reporte sin iniciar sesión, envía un correo electrónico a [<EMAIL>](mailto:<EMAIL>). Si es posible, cifra tu mensaje con nuestra clave PGP; descárgala desde la [página de la clave PGP del Microsoft Security Response Center](https://aka.ms/opensource/security/pgpkey).

Deberías recibir una respuesta en un plazo de 24 horas. Si por alguna razón no la recibes, por favor haz un seguimiento por correo electrónico para asegurarte de que recibimos tu mensaje original. Más información está disponible en [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Por favor, incluye la información solicitada que se detalla a continuación (tanta como puedas proporcionar) para ayudarnos a entender mejor la naturaleza y el alcance del posible problema:

  * Tipo de problema (por ejemplo, desbordamiento de búfer, inyección SQL, cross-site scripting, etc.)
  * Rutas completas de los archivos fuente relacionados con la manifestación del problema
  * La ubicación del código fuente afectado (tag/branch/commit o URL directa)
  * Cualquier configuración especial necesaria para reproducir el problema
  * Instrucciones paso a paso para reproducir el problema
  * Código de prueba de concepto o exploit (si es posible)
  * Impacto del problema, incluyendo cómo un atacante podría explotarlo

Esta información nos ayudará a clasificar tu reporte más rápidamente.

Si estás reportando para un programa de recompensas por bugs, reportes más completos pueden contribuir a una mayor recompensa. Por favor visita nuestra página del [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) para más detalles sobre nuestros programas activos.

## Idiomas preferidos

Preferimos que todas las comunicaciones sean en inglés.

## Política

Microsoft sigue el principio de [Divulgación Coordinada de Vulnerabilidades](https://aka.ms/opensource/security/cvd).

**Aviso legal**:  
Este documento ha sido traducido utilizando el servicio de traducción automática [Co-op Translator](https://github.com/Azure/co-op-translator). Aunque nos esforzamos por la precisión, tenga en cuenta que las traducciones automáticas pueden contener errores o inexactitudes. El documento original en su idioma nativo debe considerarse la fuente autorizada. Para información crítica, se recomienda la traducción profesional realizada por humanos. No nos hacemos responsables de malentendidos o interpretaciones erróneas derivadas del uso de esta traducción.