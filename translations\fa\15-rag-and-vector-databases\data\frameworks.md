<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:25:56+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "fa"
}
-->
# چارچوب‌های شبکه عصبی

همان‌طور که قبلاً یاد گرفتیم، برای آموزش مؤثر شبکه‌های عصبی باید دو کار انجام دهیم:

* انجام عملیات روی تنسورها، مثلاً ضرب، جمع و محاسبه توابعی مانند سیگموید یا سافت‌مکس
* محاسبه گرادیان تمام عبارات برای انجام بهینه‌سازی با روش نزول گرادیان

در حالی که کتابخانه `numpy` می‌تواند بخش اول را انجام دهد، ما به مکانیزمی برای محاسبه گرادیان‌ها نیاز داریم. در چارچوبی که در بخش قبلی توسعه دادیم، مجبور بودیم تمام توابع مشتق را به صورت دستی داخل متد `backward` که عملیات پس‌انتشار را انجام می‌دهد، برنامه‌نویسی کنیم. ایده‌آل این است که چارچوب به ما امکان دهد گرادیان *هر عبارتی* که تعریف می‌کنیم را محاسبه کنیم.

نکته مهم دیگر این است که بتوانیم محاسبات را روی GPU یا هر واحد محاسباتی تخصصی دیگر مانند TPU انجام دهیم. آموزش عمیق شبکه‌های عصبی نیازمند *حجم زیادی* محاسبات است و توانایی موازی‌سازی این محاسبات روی GPU بسیار اهمیت دارد.

> ✅ اصطلاح «موازی‌سازی» به معنای توزیع محاسبات روی چند دستگاه است.

در حال حاضر، دو چارچوب محبوب شبکه عصبی عبارتند از: TensorFlow و PyTorch. هر دو یک API سطح پایین برای کار با تنسورها روی CPU و GPU ارائه می‌دهند. در بالای این API سطح پایین، API سطح بالاتری نیز وجود دارد که به ترتیب Keras و PyTorch Lightning نامیده می‌شوند.

API سطح پایین | TensorFlow | PyTorch  
--------------|-------------------------------|------------------------------  
API سطح بالا  | Keras                         | PyTorch

**APIهای سطح پایین** در هر دو چارچوب به شما اجازه می‌دهند تا گراف‌های محاسباتی بسازید. این گراف مشخص می‌کند که چگونه خروجی (معمولاً تابع خطا) با پارامترهای ورودی محاسبه شود و می‌تواند برای محاسبه روی GPU ارسال شود، اگر در دسترس باشد. توابعی برای مشتق‌گیری از این گراف محاسباتی و محاسبه گرادیان‌ها وجود دارد که سپس برای بهینه‌سازی پارامترهای مدل استفاده می‌شوند.

**APIهای سطح بالا** شبکه‌های عصبی را به عنوان **دنباله‌ای از لایه‌ها** در نظر می‌گیرند و ساخت اکثر شبکه‌های عصبی را بسیار ساده‌تر می‌کنند. آموزش مدل معمولاً شامل آماده‌سازی داده‌ها و سپس فراخوانی تابع `fit` برای انجام آموزش است.

API سطح بالا به شما اجازه می‌دهد شبکه‌های عصبی معمولی را خیلی سریع بسازید بدون اینکه نگران جزئیات زیاد باشید. در عین حال، API سطح پایین کنترل بیشتری روی فرایند آموزش ارائه می‌دهد و به همین دلیل در تحقیقات که با معماری‌های جدید شبکه عصبی سر و کار دارید، بسیار استفاده می‌شود.

همچنین مهم است که بدانید می‌توانید هر دو API را با هم استفاده کنید، مثلاً می‌توانید معماری لایه شبکه خود را با API سطح پایین توسعه دهید و سپس آن را در شبکه بزرگتری که با API سطح بالا ساخته و آموزش داده شده، به کار ببرید. یا می‌توانید شبکه‌ای را با API سطح بالا به صورت دنباله‌ای از لایه‌ها تعریف کنید و سپس حلقه آموزش خود را با API سطح پایین برای بهینه‌سازی اجرا کنید. هر دو API از مفاهیم پایه یکسانی استفاده می‌کنند و طوری طراحی شده‌اند که به خوبی با هم کار کنند.

## یادگیری

در این دوره، بیشتر محتوا را برای هر دو چارچوب PyTorch و TensorFlow ارائه می‌دهیم. می‌توانید چارچوب مورد علاقه خود را انتخاب کرده و فقط دفترچه‌های مربوط به آن را دنبال کنید. اگر مطمئن نیستید کدام چارچوب را انتخاب کنید، بحث‌هایی درباره **PyTorch در مقابل TensorFlow** در اینترنت بخوانید. همچنین می‌توانید هر دو چارچوب را بررسی کنید تا درک بهتری پیدا کنید.

هر جا ممکن باشد، برای سادگی از APIهای سطح بالا استفاده خواهیم کرد. با این حال، معتقدیم مهم است که نحوه کار شبکه‌های عصبی را از پایه بفهمید، بنابراین در ابتدا با API سطح پایین و تنسورها کار می‌کنیم. اما اگر می‌خواهید سریع شروع کنید و وقت زیادی برای یادگیری این جزئیات صرف نکنید، می‌توانید این بخش‌ها را رد کنید و مستقیماً به دفترچه‌های API سطح بالا بروید.

## ✍️ تمرین‌ها: چارچوب‌ها

یادگیری خود را در دفترچه‌های زیر ادامه دهید:

API سطح پایین | دفترچه TensorFlow+Keras | PyTorch  
--------------|--------------------------|-------------------------  
API سطح بالا  | Keras                   | *PyTorch Lightning*

پس از تسلط بر چارچوب‌ها، بیایید مفهوم overfitting را مرور کنیم.

# بیش‌برازش (Overfitting)

بیش‌برازش یکی از مفاهیم بسیار مهم در یادگیری ماشین است و درک درست آن حیاتی است!

مسئله تقریب ۵ نقطه (که با `x` در نمودارهای زیر نشان داده شده‌اند) را در نظر بگیرید:

!linear | overfit  
-------------------------|--------------------------  
**مدل خطی، ۲ پارامتر** | **مدل غیرخطی، ۷ پارامتر**  
خطای آموزش = ۵.۳ | خطای آموزش = ۰  
خطای اعتبارسنجی = ۵.۱ | خطای اعتبارسنجی = ۲۰

* در سمت چپ، تقریب خطی خوبی می‌بینیم. چون تعداد پارامترها مناسب است، مدل الگوی توزیع نقاط را به درستی یاد می‌گیرد.
* در سمت راست، مدل بیش از حد قدرتمند است. چون فقط ۵ نقطه داریم و مدل ۷ پارامتر دارد، می‌تواند طوری تنظیم شود که از تمام نقاط عبور کند و خطای آموزش صفر شود. اما این باعث می‌شود مدل الگوی درست داده‌ها را نفهمد و خطای اعتبارسنجی بسیار بالا باشد.

مهم است که تعادل مناسبی بین پیچیدگی مدل (تعداد پارامترها) و تعداد نمونه‌های آموزشی برقرار کنیم.

## چرا بیش‌برازش رخ می‌دهد

  * داده آموزشی کافی نیست
  * مدل بیش از حد قدرتمند است
  * نویز زیاد در داده‌های ورودی وجود دارد

## چگونه بیش‌برازش را تشخیص دهیم

همان‌طور که در نمودار بالا می‌بینید، بیش‌برازش با خطای آموزش بسیار پایین و خطای اعتبارسنجی بالا قابل تشخیص است. معمولاً در طول آموزش، خطاهای آموزش و اعتبارسنجی هر دو کاهش می‌یابند، اما در نقطه‌ای خطای اعتبارسنجی ممکن است کاهش را متوقف کرده و شروع به افزایش کند. این نشانه بیش‌برازش است و علامتی است که باید احتمالاً آموزش را در این نقطه متوقف کنیم (یا حداقل یک نسخه از مدل ذخیره کنیم).

بیش‌برازش

## چگونه از بیش‌برازش جلوگیری کنیم

اگر ببینید بیش‌برازش رخ داده است، می‌توانید یکی از کارهای زیر را انجام دهید:

 * افزایش حجم داده‌های آموزشی
 * کاهش پیچیدگی مدل
 * استفاده از تکنیک‌های منظم‌سازی مانند Dropout که بعداً بررسی خواهیم کرد.

## بیش‌برازش و تعادل بایاس-واریانس

بیش‌برازش در واقع نمونه‌ای از یک مشکل کلی‌تر در آمار به نام تعادل بایاس-واریانس است. اگر منابع خطا در مدل خود را بررسی کنیم، دو نوع خطا داریم:

* **خطاهای بایاس** ناشی از این است که الگوریتم ما نمی‌تواند رابطه بین داده‌های آموزشی را به درستی مدل کند. این معمولاً به دلیل قدرت ناکافی مدل است (**کم‌برازش**).
* **خطاهای واریانس** ناشی از این است که مدل به جای یادگیری رابطه معنادار، نویز موجود در داده‌های ورودی را تقریب می‌زند (**بیش‌برازش**).

در طول آموزش، خطای بایاس کاهش می‌یابد (چون مدل بهتر داده‌ها را تقریب می‌زند) و خطای واریانس افزایش می‌یابد. مهم است که آموزش را متوقف کنیم - یا به صورت دستی (وقتی بیش‌برازش را تشخیص می‌دهیم) یا به صورت خودکار (با استفاده از منظم‌سازی) - تا از بیش‌برازش جلوگیری شود.

## نتیجه‌گیری

در این درس، با تفاوت‌های APIهای مختلف دو چارچوب محبوب هوش مصنوعی، TensorFlow و PyTorch آشنا شدید. همچنین با موضوع بسیار مهم بیش‌برازش آشنا شدید.

## 🚀 چالش

در دفترچه‌های همراه، در پایین صفحه «وظایف» وجود دارد؛ دفترچه‌ها را دنبال کنید و وظایف را کامل کنید.

## مرور و مطالعه خودآموز

در مورد موضوعات زیر تحقیق کنید:

- TensorFlow  
- PyTorch  
- بیش‌برازش

از خودتان بپرسید:

- تفاوت TensorFlow و PyTorch چیست؟  
- تفاوت بیش‌برازش و کم‌برازش چیست؟

## تمرین

در این آزمایشگاه، از شما خواسته شده است دو مسئله طبقه‌بندی را با استفاده از شبکه‌های کاملاً متصل تک‌لایه و چندلایه با PyTorch یا TensorFlow حل کنید.

**سلب مسئولیت**:  
این سند با استفاده از سرویس ترجمه هوش مصنوعی [Co-op Translator](https://github.com/Azure/co-op-translator) ترجمه شده است. در حالی که ما در تلاش برای دقت هستیم، لطفاً توجه داشته باشید که ترجمه‌های خودکار ممکن است حاوی خطاها یا نواقصی باشند. سند اصلی به زبان بومی خود باید به عنوان منبع معتبر در نظر گرفته شود. برای اطلاعات حیاتی، ترجمه حرفه‌ای انسانی توصیه می‌شود. ما مسئول هیچ گونه سوءتفاهم یا تفسیر نادرستی که از استفاده این ترجمه ناشی شود، نیستیم.