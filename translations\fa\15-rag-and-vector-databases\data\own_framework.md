<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:41:12+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "fa"
}
-->
# مقدمه‌ای بر شبکه‌های عصبی. پرسپترون چندلایه

در بخش قبلی، با ساده‌ترین مدل شبکه عصبی یعنی پرسپترون تک‌لایه آشنا شدید، مدلی خطی برای دسته‌بندی دوکلاسه.

در این بخش، این مدل را به چارچوبی انعطاف‌پذیرتر گسترش می‌دهیم که به ما امکان می‌دهد:

* علاوه بر دسته‌بندی دوکلاسه، **دسته‌بندی چندکلاسه** انجام دهیم
* علاوه بر دسته‌بندی، مسائل **رگرسیون** را حل کنیم
* کلاس‌هایی که به صورت خطی قابل تفکیک نیستند را جدا کنیم

همچنین چارچوب مدولار خودمان را در پایتون توسعه می‌دهیم که به ما اجازه می‌دهد معماری‌های مختلف شبکه عصبی را بسازیم.

## فرموله‌سازی یادگیری ماشین

بیایید با فرموله کردن مسئله یادگیری ماشین شروع کنیم. فرض کنید یک مجموعه داده آموزشی **X** با برچسب‌های **Y** داریم و باید مدلی *f* بسازیم که دقیق‌ترین پیش‌بینی‌ها را انجام دهد. کیفیت پیش‌بینی‌ها با **تابع خطا** ℒ اندازه‌گیری می‌شود. توابع خطای زیر معمولاً استفاده می‌شوند:

* برای مسئله رگرسیون، وقتی باید یک عدد پیش‌بینی کنیم، می‌توانیم از **خطای مطلق** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>| یا **خطای مربعی** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup> استفاده کنیم
* برای دسته‌بندی، از **خطای ۰-۱** (که اساساً همان **دقت** مدل است) یا **خطای لجستیک** استفاده می‌کنیم.

برای پرسپترون تک‌لایه، تابع *f* به صورت خطی تعریف شده بود: *f(x)=wx+b* (اینجا *w* ماتریس وزن، *x* بردار ویژگی‌های ورودی و *b* بردار بایاس است). برای معماری‌های مختلف شبکه عصبی، این تابع می‌تواند شکل پیچیده‌تری داشته باشد.

> در حالت دسته‌بندی، معمولاً تمایل داریم احتمال کلاس‌های مربوطه را به عنوان خروجی شبکه داشته باشیم. برای تبدیل اعداد دلخواه به احتمال (مثلاً نرمال‌سازی خروجی)، معمولاً از تابع **softmax** σ استفاده می‌کنیم و تابع *f* به صورت *f(x)=σ(wx+b)* درمی‌آید.

در تعریف تابع *f* بالا، *w* و *b* را **پارامترها** θ=⟨*w,b*⟩ می‌نامیم. با داشتن مجموعه داده ⟨**X**,**Y**⟩، می‌توانیم خطای کلی روی کل داده‌ها را به عنوان تابعی از پارامترها θ محاسبه کنیم.

> ✅ **هدف آموزش شبکه عصبی کمینه کردن خطا با تغییر پارامترهای θ است**

## بهینه‌سازی با نزول گرادیان

روش شناخته‌شده‌ای برای بهینه‌سازی تابع وجود دارد به نام **نزول گرادیان**. ایده این است که می‌توانیم مشتق (در حالت چندبعدی به آن **گرادیان** می‌گوییم) تابع خطا را نسبت به پارامترها محاسبه کنیم و پارامترها را به گونه‌ای تغییر دهیم که خطا کاهش یابد. این را می‌توان به صورت زیر فرموله کرد:

* پارامترها را با مقادیر تصادفی اولیه مقداردهی می‌کنیم w<sup>(0)</sup>, b<sup>(0)</sup>
* مرحله زیر را چندین بار تکرار می‌کنیم:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

در طول آموزش، مراحل بهینه‌سازی باید با در نظر گرفتن کل مجموعه داده محاسبه شوند (به یاد داشته باشید که خطا به صورت مجموع روی تمام نمونه‌های آموزشی محاسبه می‌شود). اما در عمل، داده‌ها را به بخش‌های کوچکی به نام **مینیبچ‌ها** تقسیم می‌کنیم و گرادیان‌ها را بر اساس زیرمجموعه‌ای از داده‌ها محاسبه می‌کنیم. چون هر بار زیرمجموعه به صورت تصادفی انتخاب می‌شود، این روش را **نزول گرادیان تصادفی** (SGD) می‌نامند.

## پرسپترون‌های چندلایه و پس‌انتشار خطا

شبکه تک‌لایه، همانطور که دیدیم، قادر به دسته‌بندی کلاس‌های خطی قابل تفکیک است. برای ساخت مدل غنی‌تر، می‌توانیم چندین لایه شبکه را با هم ترکیب کنیم. از نظر ریاضی، این یعنی تابع *f* شکل پیچیده‌تری خواهد داشت و در چند مرحله محاسبه می‌شود:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

در اینجا، α یک **تابع فعال‌سازی غیرخطی** است، σ تابع softmax است و پارامترها θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*>.

الگوریتم نزول گرادیان همان باقی می‌ماند، اما محاسبه گرادیان‌ها دشوارتر می‌شود. با استفاده از قاعده زنجیره‌ای مشتق‌گیری، می‌توان مشتق‌ها را به صورت زیر محاسبه کرد:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂ز<sub>1</sub>/∂w<sub>1</sub>)

> ✅ قاعده زنجیره‌ای مشتق‌گیری برای محاسبه مشتق‌های تابع خطا نسبت به پارامترها استفاده می‌شود.

توجه کنید که بخش سمت چپ همه این عبارات یکسان است، بنابراین می‌توانیم مشتق‌ها را به طور مؤثر از تابع خطا شروع کرده و به صورت «عقب‌گرد» در گراف محاسباتی حرکت کنیم. به همین دلیل روش آموزش پرسپترون چندلایه را **پس‌انتشار خطا** یا «backprop» می‌نامند.

> TODO: image citation

> ✅ در مثال دفترچه یادداشت خود، پس‌انتشار را با جزئیات بیشتری بررسی خواهیم کرد.

## نتیجه‌گیری

در این درس، کتابخانه شبکه عصبی خودمان را ساختیم و از آن برای یک مسئله ساده دسته‌بندی دوبعدی استفاده کردیم.

## 🚀 چالش

در دفترچه یادداشت همراه، چارچوب خودتان را برای ساخت و آموزش پرسپترون‌های چندلایه پیاده‌سازی خواهید کرد. می‌توانید به طور دقیق ببینید که شبکه‌های عصبی مدرن چگونه کار می‌کنند.

به دفترچه OwnFramework بروید و آن را دنبال کنید.

## مرور و مطالعه خودآموز

پس‌انتشار خطا الگوریتم رایجی در هوش مصنوعی و یادگیری ماشین است که ارزش مطالعه دقیق‌تر را دارد.

## تمرین

در این آزمایشگاه، از شما خواسته شده است که از چارچوبی که در این درس ساختید برای حل مسئله دسته‌بندی ارقام دست‌نویس MNIST استفاده کنید.

* دستورالعمل‌ها
* دفترچه یادداشت

**سلب مسئولیت**:  
این سند با استفاده از سرویس ترجمه هوش مصنوعی [Co-op Translator](https://github.com/Azure/co-op-translator) ترجمه شده است. در حالی که ما در تلاش برای دقت هستیم، لطفاً توجه داشته باشید که ترجمه‌های خودکار ممکن است حاوی خطاها یا نواقصی باشند. سند اصلی به زبان بومی خود باید به عنوان منبع معتبر در نظر گرفته شود. برای اطلاعات حیاتی، ترجمه حرفه‌ای انسانی توصیه می‌شود. ما مسئول هیچ گونه سوءتفاهم یا تفسیر نادرستی که از استفاده این ترجمه ناشی شود، نیستیم.