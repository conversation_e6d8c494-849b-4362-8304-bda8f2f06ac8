<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T16:54:13+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "fa"
}
-->
# مقدمه‌ای بر شبکه‌های عصبی: پرسپترون

یکی از اولین تلاش‌ها برای پیاده‌سازی چیزی شبیه به شبکه عصبی مدرن توسط فرانک روزنبلات از آزمایشگاه هوافضای کرنل در سال ۱۹۵۷ انجام شد. این پیاده‌سازی سخت‌افزاری به نام «Mark-1» بود که برای شناسایی اشکال هندسی ابتدایی مانند مثلث، مربع و دایره طراحی شده بود.

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='Frank Rosenblatt'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> تصاویر از ویکی‌پدیا

یک تصویر ورودی توسط آرایه‌ای از ۲۰ در ۲۰ فوتوسل نمایش داده می‌شد، بنابراین شبکه عصبی ۴۰۰ ورودی و یک خروجی دودویی داشت. یک شبکه ساده شامل یک نورون بود که به آن **واحد منطق آستانه‌ای** نیز گفته می‌شد. وزن‌های شبکه عصبی مانند پتانسیومترهایی عمل می‌کردند که در مرحله آموزش نیاز به تنظیم دستی داشتند.

> ✅ پتانسیومتر دستگاهی است که به کاربر اجازه می‌دهد مقاومت یک مدار را تنظیم کند.

> روزنامه نیویورک تایمز در آن زمان درباره پرسپترون نوشت: *جنین یک کامپیوتر الکترونیکی که [نیروی دریایی] انتظار دارد بتواند راه برود، صحبت کند، ببیند، بنویسد، خود را تکثیر کند و از وجود خود آگاه باشد.*

## مدل پرسپترون

فرض کنید در مدل ما N ویژگی وجود دارد، در این صورت بردار ورودی برداری با اندازه N خواهد بود. پرسپترون یک مدل **دسته‌بندی دودویی** است، یعنی می‌تواند بین دو کلاس داده ورودی تمایز قائل شود. فرض می‌کنیم برای هر بردار ورودی x خروجی پرسپترون ما یا +1 یا -1 باشد، بسته به کلاس. خروجی با استفاده از فرمول زیر محاسبه می‌شود:

y(x) = f(w<sup>T</sup>x)

که در آن f تابع فعال‌سازی پله‌ای است.

## آموزش پرسپترون

برای آموزش پرسپترون باید بردار وزن w را پیدا کنیم که بیشترین مقدار داده‌ها را به درستی دسته‌بندی کند، یعنی کمترین **خطا** را داشته باشد. این خطا توسط **معیار پرسپترون** به صورت زیر تعریف می‌شود:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

که در آن:

* جمع بر روی داده‌های آموزشی i انجام می‌شود که منجر به دسته‌بندی نادرست شده‌اند
* x<sub>i</sub> داده ورودی است و t<sub>i</sub> برای نمونه‌های منفی و مثبت به ترتیب -1 یا +1 است.

این معیار به عنوان تابعی از وزن‌ها w در نظر گرفته می‌شود و باید آن را کمینه کنیم. اغلب از روشی به نام **نزول گرادیان** استفاده می‌شود که در آن با وزن‌های اولیه w<sup>(0)</sup> شروع می‌کنیم و سپس در هر گام وزن‌ها را طبق فرمول زیر به‌روزرسانی می‌کنیم:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

اینجا η نرخ یادگیری نامیده می‌شود و ∇E(w) گرادیان تابع E است. پس از محاسبه گرادیان، به رابطه زیر می‌رسیم:

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

الگوریتم در پایتون به این شکل است:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## نتیجه‌گیری

در این درس با پرسپترون که یک مدل دسته‌بندی دودویی است آشنا شدید و یاد گرفتید چگونه با استفاده از بردار وزن آن را آموزش دهید.

## 🚀 چالش

اگر می‌خواهید پرسپترون خودتان را بسازید، این آزمایشگاه در Microsoft Learn را امتحان کنید که از Azure ML designer استفاده می‌کند.

## مرور و خودآموزی

برای دیدن اینکه چگونه می‌توانیم از پرسپترون برای حل یک مسئله ساده و همچنین مسائل واقعی استفاده کنیم و به یادگیری ادامه دهیم، به دفترچه Perceptron مراجعه کنید.

همچنین این مقاله جالب درباره پرسپترون‌ها را ببینید.

## تمرین

در این درس، پرسپترونی برای وظیفه دسته‌بندی دودویی پیاده‌سازی کردیم و از آن برای دسته‌بندی بین دو رقم دست‌نویس استفاده کردیم. در این آزمایشگاه، از شما خواسته شده است که مسئله دسته‌بندی ارقام را به طور کامل حل کنید، یعنی تعیین کنید کدام رقم بیشترین احتمال تطابق با تصویر داده شده را دارد.

* دستورالعمل‌ها
* دفترچه یادداشت

**سلب مسئولیت**:  
این سند با استفاده از سرویس ترجمه هوش مصنوعی [Co-op Translator](https://github.com/Azure/co-op-translator) ترجمه شده است. در حالی که ما در تلاش برای دقت هستیم، لطفاً توجه داشته باشید که ترجمه‌های خودکار ممکن است حاوی خطاها یا نادرستی‌هایی باشند. سند اصلی به زبان بومی خود باید به عنوان منبع معتبر در نظر گرفته شود. برای اطلاعات حیاتی، ترجمه حرفه‌ای انسانی توصیه می‌شود. ما مسئول هیچ گونه سوءتفاهم یا تفسیر نادرستی که از استفاده این ترجمه ناشی شود، نیستیم.