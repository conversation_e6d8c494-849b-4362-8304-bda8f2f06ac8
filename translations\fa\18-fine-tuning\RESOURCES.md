<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:54:51+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "fa"
}
-->
# منابع برای یادگیری خودآموز

این درس با استفاده از منابع اصلی متعددی از OpenAI و Azure OpenAI ساخته شده است که به عنوان مرجع برای اصطلاحات و آموزش‌ها استفاده شده‌اند. در اینجا فهرستی غیرجامع برای سفرهای یادگیری خودآموز شما آورده شده است.

## ۱. منابع اصلی

| عنوان/لینک                                                                                                                                                                                                                   | توضیحات                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | فاین‌تیونینگ نسبت به یادگیری چندنمونه‌ای بهبود می‌بخشد با آموزش روی نمونه‌های بسیار بیشتری نسبت به آنچه در پرامپت جا می‌شود، که باعث صرفه‌جویی در هزینه‌ها، بهبود کیفیت پاسخ‌ها و کاهش تأخیر درخواست‌ها می‌شود. **مروری بر فاین‌تیونینگ از OpenAI دریافت کنید.**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | درک کنید **فاین‌تیونینگ چیست (مفهوم)**، چرا باید به آن توجه کنید (مسئله انگیزشی)، چه داده‌هایی برای آموزش استفاده شود و چگونه کیفیت را اندازه‌گیری کنید.                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | سرویس Azure OpenAI به شما امکان می‌دهد مدل‌ها را با استفاده از فاین‌تیونینگ بر اساس داده‌های شخصی خود سفارشی کنید. یاد بگیرید **چگونه فاین‌تیونینگ کنید (فرآیند)** و مدل‌ها را با استفاده از Azure AI Studio، Python SDK یا REST API انتخاب کنید.                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | مدل‌های زبان بزرگ (LLM) ممکن است در حوزه‌ها، وظایف یا مجموعه داده‌های خاص عملکرد خوبی نداشته باشند یا خروجی‌های نادرست یا گمراه‌کننده تولید کنند. **چه زمانی باید فاین‌تیونینگ را به عنوان راه‌حلی ممکن در نظر بگیرید؟**                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | فاین‌تیونینگ مداوم فرآیند تکراری انتخاب یک مدل قبلاً فاین‌تیون‌شده به عنوان مدل پایه و **ادامه فاین‌تیونینگ آن** روی مجموعه‌های جدیدی از نمونه‌های آموزشی است.                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | فاین‌تیونینگ مدل شما **با نمونه‌های فراخوانی تابع** می‌تواند خروجی مدل را با دریافت پاسخ‌های دقیق‌تر و سازگارتر بهبود دهد - با پاسخ‌هایی با قالب مشابه و صرفه‌جویی در هزینه‌ها.                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | این جدول را ببینید تا بفهمید **کدام مدل‌ها در Azure OpenAI قابل فاین‌تیونینگ هستند** و در کدام مناطق در دسترس‌اند. در صورت نیاز محدودیت توکن‌ها و تاریخ انقضای داده‌های آموزشی آن‌ها را بررسی کنید.                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | این قسمت ۳۰ دقیقه‌ای **اکتبر ۲۰۲۳** از AI Show مزایا، معایب و نکات عملی را بررسی می‌کند که به شما در تصمیم‌گیری کمک می‌کند.                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | این منبع **AI Playbook** شما را با نیازهای داده، قالب‌بندی، تنظیم ابرپارامترها و چالش‌ها/محدودیت‌هایی که باید بدانید، راهنمایی می‌کند.                                                                                                                                                                         |
| **آموزش**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | یاد بگیرید چگونه یک مجموعه داده نمونه برای فاین‌تیونینگ بسازید، برای فاین‌تیونینگ آماده شوید، یک کار فاین‌تیونینگ ایجاد کنید و مدل فاین‌تیون‌شده را در Azure مستقر کنید.                                                                                                                                                                                    |
| **آموزش**: [فاین‌تیونینگ مدل Llama 2 در Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio به شما امکان می‌دهد مدل‌های زبان بزرگ را با استفاده از یک روند مبتنی بر رابط کاربری مناسب برای توسعه‌دهندگان کم‌کد، بر اساس داده‌های شخصی خود سفارشی کنید. این مثال را ببینید.                                                                                                                                                               |
| **آموزش**: [فاین‌تیونینگ مدل‌های Hugging Face روی یک GPU در Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | این مقاله نحوه فاین‌تیونینگ یک مدل Hugging Face با کتابخانه transformers روی یک GPU با استفاده از Azure DataBricks و کتابخانه‌های Hugging Face Trainer را توضیح می‌دهد.                                                                                                                                                |
| **آموزش:** [فاین‌تیونینگ مدل پایه با Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | کاتالوگ مدل‌ها در Azure Machine Learning مدل‌های متن‌باز متعددی را ارائه می‌دهد که می‌توانید برای وظیفه خاص خود فاین‌تیون کنید. این ماژول بخشی از مسیر یادگیری AzureML Generative AI است.                                                                                                                            |
| **آموزش:** [فاین‌تیونینگ Azure OpenAI](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | فاین‌تیونینگ مدل‌های GPT-3.5 یا GPT-4 در Microsoft Azure با استفاده از W&B امکان ردیابی و تحلیل دقیق عملکرد مدل را فراهم می‌کند. این راهنما مفاهیم فاین‌تیونینگ OpenAI را با مراحل و ویژگی‌های خاص Azure OpenAI گسترش می‌دهد.                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## ۲. منابع ثانویه

این بخش منابع اضافی را شامل می‌شود که ارزش بررسی دارند اما فرصت پوشش آن‌ها در این درس نبوده است. ممکن است در درسی آینده یا به عنوان گزینه‌ای برای تکلیف ثانویه پوشش داده شوند. فعلاً از آن‌ها برای افزایش دانش و تخصص خود در این موضوع استفاده کنید.

| عنوان/لینک                                                                                                                                                                                                            | توضیحات                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [آماده‌سازی و تحلیل داده‌ها برای فاین‌تیونینگ مدل چت](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | این دفترچه به عنوان ابزاری برای پیش‌پردازش و تحلیل مجموعه داده چت مورد استفاده برای فاین‌تیونینگ مدل چت عمل می‌کند. خطاهای قالب را بررسی می‌کند، آمار پایه ارائه می‌دهد و تعداد توکن‌ها را برای برآورد هزینه‌های فاین‌تیونینگ تخمین می‌زند. ببینید: [روش فاین‌تیونینگ برای gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                                                                   |
| **OpenAI Cookbook**: [فاین‌تیونینگ برای تولید افزوده بازیابی‌شده (RAG) با Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | هدف این دفترچه راهنمایی گام‌به‌گام یک مثال جامع از نحوه فاین‌تیونینگ مدل‌های OpenAI برای تولید افزوده بازیابی‌شده (RAG) است. همچنین Qdrant و یادگیری چندنمونه‌ای را برای افزایش عملکرد مدل و کاهش خطاها ادغام خواهیم کرد.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [فاین‌تیونینگ GPT با Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) پلتفرم توسعه‌دهندگان هوش مصنوعی است که ابزارهایی برای آموزش مدل‌ها، فاین‌تیونینگ و استفاده از مدل‌های پایه ارائه می‌دهد. ابتدا راهنمای [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) آن‌ها را بخوانید، سپس تمرین Cookbook را انجام دهید.                                                                                                                                                                                                                  |
| **آموزش جامعه** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - فاین‌تیونینگ برای مدل‌های زبان کوچک                                                   | با [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst) آشنا شوید، مدل کوچک جدید مایکروسافت که قدرتمند و در عین حال جمع‌وجور است. این آموزش شما را در فاین‌تیونینگ Phi-2 راهنمایی می‌کند و نشان می‌دهد چگونه یک مجموعه داده منحصر به فرد بسازید و مدل را با استفاده از QLoRA فاین‌تیون کنید.                                                                                                                                                                       |
| **آموزش Hugging Face** [چگونه در ۲۰۲۴ مدل‌های زبان بزرگ را با Hugging Face فاین‌تیون کنیم](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | این پست وبلاگی شما را گام‌به‌گام در فاین‌تیونینگ مدل‌های زبان بزرگ متن‌باز با استفاده از Hugging Face TRL، Transformers و مجموعه داده‌ها در سال ۲۰۲۴ راهنمایی می‌کند. شما یک مورد استفاده تعریف می‌کنید، محیط توسعه را راه‌اندازی می‌کنید، مجموعه داده آماده می‌کنید، مدل را فاین‌تیون می‌کنید، آن را آزمایش و ارزیابی می‌کنید و سپس به تولید می‌رسانید.                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | آموزش و استقرار سریع‌تر و آسان‌تر [مدل‌های پیشرفته یادگیری ماشین](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst) را فراهم می‌کند. این مخزن آموزش‌های سازگار با Colab به همراه راهنمای ویدیویی یوتیوب برای فاین‌تیونینگ دارد. **بازتاب به‌روزرسانی اخیر [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)** . مستندات [AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) را بخوانید. |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**سلب مسئولیت**:  
این سند با استفاده از سرویس ترجمه هوش مصنوعی [Co-op Translator](https://github.com/Azure/co-op-translator) ترجمه شده است. در حالی که ما در تلاش برای دقت هستیم، لطفاً توجه داشته باشید که ترجمه‌های خودکار ممکن است حاوی خطاها یا نواقصی باشند. سند اصلی به زبان بومی خود باید به عنوان منبع معتبر در نظر گرفته شود. برای اطلاعات حیاتی، ترجمه حرفه‌ای انسانی توصیه می‌شود. ما مسئول هیچ گونه سوءتفاهم یا تفسیر نادرستی که از استفاده این ترجمه ناشی شود، نیستیم.