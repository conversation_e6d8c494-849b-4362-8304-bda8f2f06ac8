<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:04:18+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "fi"
}
-->
# Itseopiskelun resurssit

Tämä oppitunti on rakennettu käyttäen useita OpenAI:n ja Azure OpenAI:n ydintason resursseja terminologian ja opetusmateriaalien pohjana. Tässä on ei-kattava lista, jota voit hyödyntää omissa itseopiskeluprojekteissasi.

## 1. Ensisijaiset resurssit

| Otsikko/Linkki                                                                                                                                                                                                              | Kuvaus                                                                                                                                                                                                                                                                                                                                                                                       |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning parantaa few-shot-oppimista kouluttamalla mallia paljon useammilla esimerkeillä kuin mitä promptiin mahtuu, mikä säästää kustannuksia, parantaa vastausten laatua ja mahdollistaa nopeammat vastaukset. **Tutustu OpenAI:n fine-tuningin yleiskatsaukseen.**                                                                                                                     |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                     | Ymmärrä **mikä fine-tuning on (käsite)**, miksi sitä kannattaa harkita (ongelman motivaatio), mitä dataa käyttää (koulutus) ja miten laatua mitataan                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service antaa mahdollisuuden räätälöidä malleja omien datasetiesi pohjalta fine-tuningin avulla. Opi **miten fine-tuning tehdään (prosessi)** valitsemalla malleja Azure AI Studion, Python SDK:n tai REST API:n kautta.                                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM-mallit eivät välttämättä toimi hyvin tietyillä aloilla, tehtävissä tai dataseteissä, tai ne voivat tuottaa epätarkkoja tai harhaanjohtavia vastauksia. **Milloin kannattaa harkita fine-tuningia** ratkaisuna tähän?                                                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Jatkuva fine-tuning on iteratiivinen prosessi, jossa jo fine-tunattu malli valitaan pohjamalliksi ja sitä **fine-tunataan edelleen** uusilla koulutusesimerkeillä.                                                                                                                                                             |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Mallin fine-tuning **funktiokutsuesimerkkien avulla** voi parantaa mallin tuottamia vastauksia tekemällä niistä tarkempia ja johdonmukaisempia – samalla saadaan samankaltaisesti muotoiltuja vastauksia ja kustannussäästöjä                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Tarkista tämä taulukko ymmärtääksesi **mitä malleja voi fine-tunata** Azure OpenAI:ssa ja missä alueilla ne ovat saatavilla. Tarkista myös niiden token-rajoitukset ja koulutusdatan vanhenemispäivät tarvittaessa.                                                                                                                  |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Tämä 30 minuutin **lokakuun 2023** AI Show -jakso käsittelee fine-tuningin hyötyjä, haittoja ja käytännön näkemyksiä, jotka auttavat sinua tekemään päätöksen.                                                                                                                                                                    |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Tämä **AI Playbook** -resurssi opastaa sinua datavaatimuksissa, muotoilussa, hyperparametrien hienosäädössä sekä haasteissa ja rajoituksissa, jotka on hyvä tietää.                                                                                                                                                               |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Opi luomaan esimerkkidatasetti fine-tuningia varten, valmistautumaan fine-tuningiin, luomaan fine-tuning-tehtävä ja ottamaan käyttöön fine-tunattu malli Azure-ympäristössä.                                                                                                                                                      |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio mahdollistaa suurten kielimallien räätälöinnin omiin datasettiisi _käyttöliittymäpohjaisen työnkulun avulla, joka sopii vähäkoodisille kehittäjille_. Katso tämä esimerkki.                                                                                                                                         |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Tässä artikkelissa kuvataan, miten Hugging Face -mallia voi fine-tunata Hugging Face transformers -kirjastolla yhdellä GPU:lla Azure DataBricksin ja Hugging Face Trainer -kirjastojen avulla.                                                                                                                                     |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Azure Machine Learningin mallikatalogista löytyy monia avoimen lähdekoodin malleja, joita voit fine-tunata omaan tehtävääsi sopiviksi. Kokeile tätä moduulia osana [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) -opintopolkua. |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | GPT-3.5- tai GPT-4-mallien fine-tuning Microsoft Azuren päällä W&B:n avulla mahdollistaa mallin suorituskyvyn yksityiskohtaisen seurannan ja analyysin. Tämä opas laajentaa OpenAI:n fine-tuning -oppaan käsitteitä Azure OpenAI:n erityisillä vaiheilla ja ominaisuuksilla.                                                                                                                  |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                                                                                              |

## 2. Toissijaiset resurssit

Tässä osiossa on lisäresursseja, joita kannattaa tutkia, vaikka emme ehtineet käsitellä niitä tässä oppitunnissa. Ne voivat tulla esille tulevissa oppitunneissa tai vaihtoehtoisina lisätehtävinä myöhemmin. Käytä niitä nyt oman asiantuntemuksesi ja tietämyksesi rakentamiseen aiheesta.

| Otsikko/Linkki                                                                                                                                                                                                                 | Kuvaus                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Data preparation and analysis for chat model fine-tuning](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                               | Tämä muistikirja toimii työkaluna chat-mallin fine-tuningia varten käytetyn chat-datan esikäsittelyyn ja analysointiin. Se tarkistaa muotoiluvirheitä, tarjoaa perusstatistiikkaa ja arvioi token-määriä fine-tuning-kustannuksia varten. Katso: [Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                         |
| **OpenAI Cookbook**: [Fine-Tuning for Retrieval Augmented Generation (RAG) with Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)            | Tämän muistikirjan tavoitteena on käydä läpi kattava esimerkki siitä, miten OpenAI-malleja voi fine-tunata Retrieval Augmented Generation (RAG) -menetelmällä. Integroimme myös Qdrantin ja Few-Shot Learningin parantaaksemme mallin suorituskykyä ja vähentääksemme virheellisiä vastauksia.                                                                                                                                                                         |
| **OpenAI Cookbook**: [Fine-tuning GPT with Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                                    | Weights & Biases (W&B) on tekoälykehittäjien alusta, joka tarjoaa työkaluja mallien koulutukseen, fine-tuningiin ja perustamallien hyödyntämiseen. Lue ensin heidän [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) -oppaansa ja kokeile sitten Cookbookin harjoitusta.                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning for Small Language Models                                                        | Tutustu [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), Microsoftin uuteen pieneen malliin, joka on yllättävän tehokas ja kompakti. Tämä opas ohjaa sinut Phi-2:n fine-tuning-prosessin läpi, näyttää miten rakennetaan ainutlaatuinen datasetti ja miten mallia hienosäädetään QLoRA:n avulla.                                                                                                                               |
| **Hugging Face Tutorial** [How to Fine-Tune LLMs in 2024 with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                                    | Tämä blogikirjoitus opastaa sinua, miten fine-tunata avoimia LLM-malleja Hugging Face TRL:n, Transformers-kirjaston ja datasetien avulla vuonna 2024. Määrittelet käyttötapauksen, luot kehitysympäristön, valmistelet datasetin, hienosäädät mallin, testaat ja arvioit sen, ja lopuksi otat sen käyttöön tuotannossa.                                                                                                                                            |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                                 | Tarjoaa nopeamman ja helpomman tavan kouluttaa ja ottaa käyttöön [huippuluokan koneoppimismalleja](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Repo sisältää Colab-ystävällisiä tutoriaaleja YouTube-video-opastuksilla fine-tuningia varten. **Heijastaa viimeisintä [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) -päivitystä**. Lue [AutoTrain-dokumentaatio](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst). |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Vastuuvapauslauseke**:  
Tämä asiakirja on käännetty käyttämällä tekoälypohjaista käännöspalvelua [Co-op Translator](https://github.com/Azure/co-op-translator). Vaikka pyrimme tarkkuuteen, huomioithan, että automaattikäännöksissä saattaa esiintyä virheitä tai epätarkkuuksia. Alkuperäistä asiakirjaa sen alkuperäiskielellä tulee pitää virallisena lähteenä. Tärkeissä tiedoissa suositellaan ammattimaista ihmiskäännöstä. Emme ole vastuussa tämän käännöksen käytöstä aiheutuvista väärinymmärryksistä tai tulkinnoista.