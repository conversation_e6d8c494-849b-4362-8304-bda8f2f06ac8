<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:36:40+00:00",
  "source_file": "README.md",
  "language_code": "fi"
}
-->
![Generative AI Aloittelijoille](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.fi.png)

### 21 oppituntia, jotka opettavat kaiken tarvittavan Generatiivisten AI-sovellusten rakentamisen aloittamiseen

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Monikielinen tuki

#### Tuettu GitHub Actionin kautta (automaattinen ja aina ajan tasalla)

[Ranska](../fr/README.md) | [Espanja](../es/README.md) | [Saksa](../de/README.md) | [Venäjä](../ru/README.md) | [Arabia](../ar/README.md) | [Persia (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Kiina (yksinkertaistettu)](../zh/README.md) | [Kiina (perinteinen, Macao)](../mo/README.md) | [Kiina (perinteinen, Hongkong)](../hk/README.md) | [Kiina (perinteinen, Taiwan)](../tw/README.md) | [Japani](../ja/README.md) | [Korea](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portugali (Portugali)](../pt/README.md) | [Portugali (Brasilia)](../br/README.md) | [Italia](../it/README.md) | [Puola](../pl/README.md) | [Turkki](../tr/README.md) | [Kreikka](../el/README.md) | [Thai](../th/README.md) | [Ruotsi](../sv/README.md) | [Tanska](../da/README.md) | [Norja](../no/README.md) | [Suomi](./README.md) | [Hollanti](../nl/README.md) | [Heprea](../he/README.md) | [Vietnam](../vi/README.md) | [Indonesia](../id/README.md) | [Malaiji](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Unkari](../hu/README.md) | [Tšekki](../cs/README.md) | [Slovakki](../sk/README.md) | [Romania](../ro/README.md) | [Bulgaria](../bg/README.md) | [Serbia (kyrillinen)](../sr/README.md) | [Kroatia](../hr/README.md) | [Sloveeni](../sl/README.md) | [Ukraina](../uk/README.md) | [Burma (Myanmar)](../my/README.md)

# Generatiivinen AI aloittelijoille (Versio 3) – Kurssi

Opi Generatiivisten AI-sovellusten rakentamisen perusteet Microsoft Cloud Advocatesin kattavalla 21 oppitunnin kurssilla.

## 🌱 Aloittaminen

Tässä kurssissa on 21 oppituntia. Jokainen oppitunti käsittelee omaa aihettaan, joten voit aloittaa mistä tahansa!

Oppitunnit on merkitty joko "Learn" -tunneiksi, jotka selittävät Generatiivisen AI:n käsitteen, tai "Build" -tunneiksi, joissa käydään läpi käsite ja koodiesimerkkejä sekä **Pythonilla** että **TypeScriptillä** mahdollisuuksien mukaan.

.NET-kehittäjille suosittelemme [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

Jokaisessa oppitunnissa on myös "Keep Learning" -osio, jossa on lisäoppimateriaaleja.

## Mitä tarvitset
### Voit käyttää tämän kurssin koodin suorittamiseen joko: 
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **Oppitunnit:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **Oppitunnit:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **Oppitunnit:** "oai-assignment" 
   
- Perustiedot Pythonista tai TypeScriptistä ovat hyödyksi – \*Täydellisille aloittelijoille suosittelemme näitä [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) ja [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) -kursseja
- GitHub-tili, jotta voit [forkata tämän koko repositorion](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) omaan GitHub-tiliisi

Olemme luoneet **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** -oppitunnin, joka auttaa sinua kehitysympäristön asennuksessa.

Älä unohda [tähtä (🌟) tätä repositoriota](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst), jotta löydät sen helpommin myöhemmin.

## 🧠 Valmis käyttöönottoon?

Jos etsit edistyneempiä koodiesimerkkejä, tutustu [Generatiivisen AI:n koodiesimerkkikokoelmaamme](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) sekä **Pythonilla** että **TypeScriptillä**.

## 🗣️ Tapaa muita oppijoita, saa tukea

Liity [viralliselle Azure AI Foundry Discord -palvelimellemme](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) tavata ja verkostoitua muiden kurssin osallistujien kanssa sekä saadaksesi tukea.

Kysy kysymyksiä tai jaa palautetta tuotteesta [Azure AI Foundry Developer Forumissa](https://aka.ms/azureaifoundry/forum) GitHubissa.

## 🚀 Rakennatko startupia?

Rekisteröidy [Microsoft for Startups Founders Hubiin](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) saadaksesi **ilmaisia OpenAI-krediittejä** ja jopa **150 000 dollaria Azure-krediittejä OpenAI-mallien käyttöön Azure OpenAI Servicesin kautta**.

## 🙏 Haluatko auttaa?

Onko sinulla ehdotuksia tai oletko löytänyt kirjoitus- tai koodivirheitä? [Luo issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) tai [tee pull request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Jokainen oppitunti sisältää:

- Lyhyen videoesittelyn aiheesta
- Kirjallisen oppitunnin README-tiedostossa
- Python- ja TypeScript-koodiesimerkit, jotka tukevat Azure OpenAI:ta ja OpenAI API:a
- Linkkejä lisäresursseihin oppimisen jatkamiseksi

## 🗃️ Oppitunnit

| #   | **Oppitunnin linkki**                                                                                                                        | **Kuvaus**                                                                                     | **Video**                                                                   | **Lisäoppiminen**                                                              |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Learn:** Kuinka asettaa kehitysympäristö                                                    | Video tulossa pian                                                           | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Johdatus Generatiiviseen AI:hin ja LLM:iin](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                          | **Learn:** Mitä Generatiivinen AI on ja miten Suuret Kielenmallit (LLM) toimivat               | [Video](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Eri LLM:ien tutkiminen ja vertailu](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)                 | **Learn:** Kuinka valita oikea malli omaan käyttötarkoitukseen                                | [Video](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Generatiivisen AI:n vastuullinen käyttö](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                     | **Learn:** Kuinka rakentaa Generatiivisia AI-sovelluksia vastuullisesti                        | [Video](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Prompt Engineeringin perusteet](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)                             | **Learn:** Käytännön parhaat käytännöt promptien suunnittelussa                              | [Video](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Edistyneet promptit](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                      | **Learn:** Kuinka soveltaa prompt engineering -tekniikoita, jotka parantavat promptien tulosta | [Video](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Tekstintuotantosovellusten rakentaminen](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Rakenna:** Tekstintuotantosovellus käyttäen Azure OpenAI / OpenAI APIa                                | [Video](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Chat-sovellusten rakentaminen](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Rakenna:** Tekniikoita chat-sovellusten tehokkaaseen rakentamiseen ja integrointiin.               | [Video](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Hakusovellusten rakentaminen vektoritietokantojen avulla](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Rakenna:** Hakusovellus, joka käyttää Embeddings-menetelmää datan hakemiseen.                        | [Video](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Kuvantuotantosovellusten rakentaminen](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Rakenna:** Kuvantuotantosovellus                                                       | [Video](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Low Code -tekoälysovellusten rakentaminen](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Rakenna:** Generatiivinen tekoälysovellus käyttäen Low Code -työkaluja                                     | [Video](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Ulkoisten sovellusten integrointi Function Calling -toiminnolla](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **Rakenna:** Mikä on function calling ja sen käyttötarkoitukset sovelluksissa                          | [Video](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [UX:n suunnittelu tekoälysovelluksille](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Opiskele:** Miten soveltaa UX-suunnittelun periaatteita generatiivisten tekoälysovellusten kehityksessä         | [Video](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Generatiivisten tekoälysovellusten suojaaminen](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Opiskele:** Tekoälyjärjestelmien uhkat ja riskit sekä keinot näiden suojaamiseksi.             | [Video](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Generatiivisen tekoälysovelluksen elinkaari](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **Opiskele:** Työkalut ja mittarit LLM-elinkaaren ja LLMOpsin hallintaan                         | [Video](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) ja vektoritietokannat](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **Rakenna:** Sovellus, joka käyttää RAG-kehystä hakemaan embeddings-tietoa vektoritietokannoista  | [Video](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Avoimen lähdekoodin mallit ja Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **Rakenna:** Sovellus, joka käyttää Hugging Facen avoimen lähdekoodin malleja                    | [Video](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [Tekoälyagentit](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **Rakenna:** Sovellus, joka käyttää AI Agent Frameworkia                                           | [Video](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [LLM-mallien hienosäätö](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Opiskele:** Mitä, miksi ja miten LLM-malleja hienosäädetään                                            | [Video](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Rakentaminen SLM-malleilla](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Opiskele:** Pienten kielimallien rakentamisen hyödyt                                            | Video tulossa pian | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Rakentaminen Mistral-malleilla](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Opiskele:** Mistral-perheen mallien ominaisuudet ja erot                                           | Video tulossa pian | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Rakentaminen Meta-malleilla](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Opiskele:** Meta-perheen mallien ominaisuudet ja erot                                           | Video tulossa pian | [Lisätietoja](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Erityiskiitokset

Erityiskiitos [**John Azizille**](https://www.linkedin.com/in/john0isaac/) kaikista GitHub Actions -toiminnoista ja työnkuluista

[**Bernhard Merklelle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) avainpanoksista jokaisen oppitunnin parantamiseksi oppimiskokemuksen ja koodin osalta.

## 🎒 Muut kurssit

Tiimimme tuottaa myös muita kursseja! Tutustu:

- [**UUSI** Model Context Protocol aloittelijoille](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI Agents aloittelijoille](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Generatiivinen tekoäly aloittelijoille .NETillä](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Generatiivinen tekoäly aloittelijoille JavaScriptillä](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML aloittelijoille](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science aloittelijoille](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI aloittelijoille](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Kyberturvallisuus aloittelijoille](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Web-kehitys aloittelijoille](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT aloittelijoille](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR-kehitys aloittelijoille](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [GitHub Copilotin hallinta tekoälypariohjelmointiin](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [GitHub Copilotin hallinta C#/.NET-kehittäjille](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Valitse oma Copilot-seikkailusi](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Vastuuvapauslauseke**:  
Tämä asiakirja on käännetty käyttämällä tekoälypohjaista käännöspalvelua [Co-op Translator](https://github.com/Azure/co-op-translator). Vaikka pyrimme tarkkuuteen, huomioithan, että automaattikäännöksissä saattaa esiintyä virheitä tai epätarkkuuksia. Alkuperäistä asiakirjaa sen alkuperäiskielellä tulee pitää virallisena lähteenä. Tärkeissä tiedoissa suositellaan ammattimaista ihmiskäännöstä. Emme ole vastuussa tämän käännöksen käytöstä aiheutuvista väärinymmärryksistä tai tulkinnoista.