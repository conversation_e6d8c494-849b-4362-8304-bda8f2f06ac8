<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:53:01+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "fr"
}
-->
# Ressources pour l’auto-apprentissage

Cette leçon a été construite en s’appuyant sur plusieurs ressources principales d’OpenAI et d’Azure OpenAI comme références pour la terminologie et les tutoriels. Voici une liste non exhaustive, pour vos propres parcours d’apprentissage autonome.

## 1. Ressources principales

| Titre/Lien                                                                                                                                                                                                                 | Description                                                                                                                                                                                                                                                                                                                                                                                   |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                     | Le fine-tuning améliore l’apprentissage par few-shot en s’entraînant sur beaucoup plus d’exemples que ce qui peut tenir dans le prompt, ce qui vous fait économiser des coûts, améliore la qualité des réponses et permet des requêtes à plus faible latence. **Découvrez une vue d’ensemble du fine-tuning par OpenAI.**                                                                                 |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                     | Comprenez **ce qu’est le fine-tuning (concept)**, pourquoi vous devriez vous y intéresser (problématique motivante), quelles données utiliser (entraînement) et comment mesurer la qualité                                                                                                                                                                               |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)   | Le service Azure OpenAI vous permet d’adapter nos modèles à vos propres jeux de données grâce au fine-tuning. Apprenez **comment faire du fine-tuning (processus)** et sélectionner des modèles via Azure AI Studio, le SDK Python ou l’API REST.                                                                                                                                    |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | Les LLM peuvent ne pas bien fonctionner sur certains domaines, tâches ou jeux de données spécifiques, ou produire des résultats inexacts ou trompeurs. **Quand devriez-vous envisager le fine-tuning** comme solution possible ?                                                                                                                                                        |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)               | Le fine-tuning continu est un processus itératif qui consiste à prendre un modèle déjà fine-tuné comme modèle de base et à **le fine-tuner davantage** sur de nouveaux ensembles d’exemples d’entraînement.                                                                                                                                                                               |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                         | Le fine-tuning de votre modèle **avec des exemples d’appels de fonctions** peut améliorer la sortie du modèle en obtenant des réponses plus précises et cohérentes – avec des réponses formatées de manière similaire et des économies de coûts.                                                                                                                                          |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                          | Consultez ce tableau pour comprendre **quels modèles peuvent être fine-tunés** dans Azure OpenAI, et dans quelles régions ils sont disponibles. Vérifiez leurs limites de tokens et les dates d’expiration des données d’entraînement si nécessaire.                                                                                                                                       |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                        | Cet épisode de 30 minutes de l’AI Show, diffusé en **octobre 2023**, aborde les avantages, inconvénients et retours d’expérience pratiques pour vous aider à prendre cette décision.                                                                                                                                                                                                       |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                       | Cette ressource du **AI Playbook** vous guide à travers les exigences en données, le formatage, le réglage des hyperparamètres et les défis/limitations à connaître.                                                                                                                                                                                                                         |
| **Tutoriel** : [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                    | Apprenez à créer un jeu de données d’exemple pour le fine-tuning, préparer le fine-tuning, lancer un travail de fine-tuning et déployer le modèle fine-tuné sur Azure.                                                                                                                                                                                                                      |
| **Tutoriel** : [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                        | Azure AI Studio vous permet d’adapter les grands modèles de langage à vos propres jeux de données _via un workflow basé sur une interface utilisateur adapté aux développeurs low-code_. Découvrez cet exemple.                                                                                                                                                                            |
| **Tutoriel** : [Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)                 | Cet article explique comment fine-tuner un modèle Hugging Face avec la bibliothèque transformers de Hugging Face sur un GPU unique avec Azure DataBricks et les bibliothèques Hugging Face Trainer.                                                                                                                                                                                          |
| **Formation :** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)             | Le catalogue de modèles dans Azure Machine Learning propose de nombreux modèles open source que vous pouvez fine-tuner pour votre tâche spécifique. Essayez ce module issu du [parcours d’apprentissage AzureML Generative AI](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst).                                         |
| **Tutoriel :** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Le fine-tuning des modèles GPT-3.5 ou GPT-4 sur Microsoft Azure avec W&B permet un suivi et une analyse détaillés des performances du modèle. Ce guide étend les concepts du guide OpenAI Fine-Tuning avec des étapes et fonctionnalités spécifiques à Azure OpenAI.                                                                                                                     |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                                                                                             |

## 2. Ressources secondaires

Cette section regroupe des ressources supplémentaires qui valent la peine d’être explorées, mais que nous n’avons pas eu le temps d’aborder dans cette leçon. Elles pourront être traitées dans une leçon future, ou comme option de devoir secondaire, à une date ultérieure. Pour l’instant, utilisez-les pour approfondir vos connaissances et votre expertise sur ce sujet.

| Titre/Lien                                                                                                                                                                                                                  | Description                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook** : [Préparation et analyse des données pour le fine-tuning de modèles de chat](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                            | Ce notebook sert d’outil pour prétraiter et analyser le jeu de données de chat utilisé pour le fine-tuning d’un modèle de chat. Il vérifie les erreurs de format, fournit des statistiques de base et estime le nombre de tokens pour le calcul des coûts de fine-tuning. Voir : [Méthode de fine-tuning pour gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook** : [Fine-tuning pour Retrieval Augmented Generation (RAG) avec Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)       | Ce notebook a pour but de présenter un exemple complet de fine-tuning des modèles OpenAI pour la génération augmentée par récupération (RAG). Nous intégrerons également Qdrant et le Few-Shot Learning pour améliorer les performances du modèle et réduire les erreurs factuelles.                                                                                                                                                                                                                                  |
| **OpenAI Cookbook** : [Fine-tuning GPT avec Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                               | Weights & Biases (W&B) est une plateforme pour développeurs IA, avec des outils pour entraîner des modèles, faire du fine-tuning et exploiter des modèles de base. Lisez d’abord leur guide [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst), puis essayez l’exercice du Cookbook.                                                                                                                                                                      |
| **Tutoriel communautaire** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning pour petits modèles de langage                                                    | Découvrez [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), le nouveau petit modèle de Microsoft, étonnamment puissant et compact. Ce tutoriel vous guide pour fine-tuner Phi-2, en montrant comment construire un jeu de données unique et fine-tuner le modèle avec QLoRA.                                                                                                                                                              |
| **Tutoriel Hugging Face** [Comment fine-tuner des LLM en 2024 avec Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Ce billet de blog vous explique comment fine-tuner des LLM open source avec Hugging Face TRL, Transformers et datasets en 2024. Vous définissez un cas d’usage, configurez un environnement de dev, préparez un jeu de données, fine-tunez le modèle, le testez et l’évaluez, puis le déployez en production.                                                                                                                                                                                               |
| **Hugging Face : [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                                | Facilite l’entraînement et le déploiement rapides de [modèles d’apprentissage automatique de pointe](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Le dépôt propose des tutoriels compatibles Colab avec des vidéos YouTube, pour le fine-tuning. **Reflète la récente mise à jour [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)**. Consultez la [documentation AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Avertissement** :  
Ce document a été traduit à l’aide du service de traduction automatique [Co-op Translator](https://github.com/Azure/co-op-translator). Bien que nous nous efforcions d’assurer l’exactitude, veuillez noter que les traductions automatiques peuvent contenir des erreurs ou des inexactitudes. Le document original dans sa langue d’origine doit être considéré comme la source faisant foi. Pour les informations critiques, une traduction professionnelle réalisée par un humain est recommandée. Nous déclinons toute responsabilité en cas de malentendus ou de mauvaises interprétations résultant de l’utilisation de cette traduction.