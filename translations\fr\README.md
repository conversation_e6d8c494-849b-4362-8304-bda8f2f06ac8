<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:15:40+00:00",
  "source_file": "README.md",
  "language_code": "fr"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.fr.png)

### 21 leçons qui vous enseignent tout ce qu’il faut savoir pour commencer à créer des applications d’IA générative

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Support multilingue

#### Pris en charge via GitHub Action (Automatisé & Toujours à jour)

[French](./README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](../ja/README.md) | [Korean](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](../el/README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](../he/README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](../sk/README.md) | [Romanian](../ro/README.md) | [Bulgarian](../bg/README.md) | [Serbian (Cyrillic)](../sr/README.md) | [Croatian](../hr/README.md) | [Slovenian](../sl/README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Generative AI for Beginners (Version 3) - Un cours

Apprenez les bases pour créer des applications d’IA générative grâce à notre cours complet de 21 leçons, proposé par les Microsoft Cloud Advocates.

## 🌱 Pour bien démarrer

Ce cours comprend 21 leçons. Chaque leçon aborde un sujet spécifique, vous pouvez donc commencer où vous voulez !

Les leçons sont classées en deux catégories : les leçons "Learn" qui expliquent un concept d’IA générative, et les leçons "Build" qui présentent un concept ainsi que des exemples de code en **Python** et **TypeScript** lorsque c’est possible.

Pour les développeurs .NET, consultez [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst) !

Chaque leçon comprend également une section "Keep Learning" avec des ressources complémentaires.

## Ce dont vous avez besoin
### Pour exécuter le code de ce cours, vous pouvez utiliser : 
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **Leçons :** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **Leçons :** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **Leçons :** "oai-assignment" 
   
- Des connaissances de base en Python ou TypeScript sont utiles - \*Pour les débutants complets, consultez ces cours [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) et [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst)
- Un compte GitHub pour [forker ce dépôt complet](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) sur votre propre compte GitHub

Nous avons créé une leçon **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** pour vous aider à configurer votre environnement de développement.

N’oubliez pas de [mettre une étoile (🌟) sur ce dépôt](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst) pour le retrouver plus facilement plus tard.

## 🧠 Prêt à déployer ?

Si vous cherchez des exemples de code plus avancés, consultez notre [collection d’exemples de code pour l’IA générative](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) en **Python** et **TypeScript**.

## 🗣️ Rencontrez d’autres apprenants, obtenez du support

Rejoignez notre [serveur Discord officiel Azure AI Foundry](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) pour rencontrer et échanger avec d’autres apprenants suivant ce cours et obtenir de l’aide.

Posez vos questions ou partagez vos retours sur le produit dans notre [forum développeurs Azure AI Foundry](https://aka.ms/azureaifoundry/forum) sur Github.

## 🚀 Vous lancez une startup ?

Inscrivez-vous au [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) pour recevoir des **crédits OpenAI gratuits** et jusqu’à **150 000 $ en crédits Azure pour accéder aux modèles OpenAI via Azure OpenAI Services**.

## 🙏 Vous souhaitez aider ?

Vous avez des suggestions ou avez trouvé des fautes d’orthographe ou des erreurs dans le code ? [Ouvrez une issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) ou [créez une pull request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Chaque leçon comprend :

- Une courte vidéo d’introduction au sujet
- Une leçon écrite dans le README
- Des exemples de code en Python et TypeScript compatibles avec Azure OpenAI et OpenAI API
- Des liens vers des ressources supplémentaires pour approfondir vos connaissances

## 🗃️ Leçons

| #   | **Lien de la leçon**                                                                                                                          | **Description**                                                                                 | **Vidéo**                                                                   | **Ressources complémentaires**                                                  |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Configuration du cours](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                        | **Learn :** Comment configurer votre environnement de développement                             | Vidéo à venir                                                               | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Introduction à l’IA générative et aux LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                          | **Learn :** Comprendre ce qu’est l’IA générative et comment fonctionnent les grands modèles de langage (LLMs). | [Vidéo](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Explorer et comparer différents LLMs](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)               | **Learn :** Comment choisir le modèle adapté à votre cas d’usage                               | [Vidéo](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Utiliser l’IA générative de manière responsable](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn :** Comment créer des applications d’IA générative de façon responsable                | [Vidéo](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Comprendre les bases du Prompt Engineering](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)                 | **Learn :** Pratiques recommandées du Prompt Engineering en mode pratique                      | [Vidéo](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Créer des prompts avancés](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                  | **Learn :** Comment appliquer des techniques de prompt engineering pour améliorer les résultats de vos prompts. | [Vidéo](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Créer des applications de génération de texte](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Construire :** Une application de génération de texte utilisant Azure OpenAI / OpenAI API                                | [Vidéo](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Créer des applications de chat](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Construire :** Techniques pour créer et intégrer efficacement des applications de chat.               | [Vidéo](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Créer des applications de recherche avec bases de données vectorielles](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Construire :** Une application de recherche utilisant des embeddings pour interroger les données.                        | [Vidéo](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Créer des applications de génération d’images](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Construire :** Une application de génération d’images                                                       | [Vidéo](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Créer des applications IA Low Code](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Construire :** Une application d’IA générative utilisant des outils Low Code                                     | [Vidéo](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Intégrer des applications externes avec Function Calling](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **Construire :** Qu’est-ce que le function calling et ses cas d’usage pour les applications                          | [Vidéo](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [Concevoir l’UX pour les applications IA](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Apprendre :** Comment appliquer les principes de design UX lors du développement d’applications d’IA générative         | [Vidéo](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Sécuriser vos applications d’IA générative](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Apprendre :** Les menaces et risques pour les systèmes d’IA ainsi que les méthodes pour les sécuriser.             | [Vidéo](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Le cycle de vie des applications d’IA générative](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **Apprendre :** Les outils et métriques pour gérer le cycle de vie des LLM et le LLMOps                         | [Vidéo](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) et bases de données vectorielles](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **Construire :** Une application utilisant un framework RAG pour récupérer des embeddings depuis une base de données vectorielle  | [Vidéo](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Modèles open source et Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **Construire :** Une application utilisant des modèles open source disponibles sur Hugging Face                    | [Vidéo](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [Agents IA](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **Construire :** Une application utilisant un framework d’agent IA                                           | [Vidéo](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [Fine-tuning des LLMs](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Apprendre :** Quoi, pourquoi et comment faire le fine-tuning des LLMs                                            | [Vidéo](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Construire avec des SLMs](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Apprendre :** Les avantages de construire avec des Small Language Models                                            | Vidéo à venir | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Construire avec les modèles Mistral](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Apprendre :** Les caractéristiques et différences des modèles de la famille Mistral                                           | Vidéo à venir | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Construire avec les modèles Meta](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Apprendre :** Les caractéristiques et différences des modèles de la famille Meta                                           | Vidéo à venir | [En savoir plus](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Remerciements spéciaux

Un grand merci à [**John Aziz**](https://www.linkedin.com/in/john0isaac/) pour avoir créé toutes les GitHub Actions et workflows

[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) pour ses contributions clés à chaque leçon afin d’améliorer l’expérience des apprenants et du code.

## 🎒 Autres cours

Notre équipe propose d’autres cours ! Découvrez :

- [**NOUVEAU** Model Context Protocol pour débutants](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Agents IA pour débutants](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [IA générative pour débutants avec .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [IA générative pour débutants avec JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML pour débutants](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science pour débutants](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [IA pour débutants](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Cybersécurité pour débutants](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Développement web pour débutants](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT pour débutants](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [Développement XR pour débutants](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Maîtriser GitHub Copilot pour la programmation assistée par IA](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Maîtriser GitHub Copilot pour les développeurs C#/.NET](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Choisissez votre propre aventure Copilot](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Avertissement** :  
Ce document a été traduit à l’aide du service de traduction automatique [Co-op Translator](https://github.com/Azure/co-op-translator). Bien que nous nous efforcions d’assurer l’exactitude, veuillez noter que les traductions automatiques peuvent contenir des erreurs ou des inexactitudes. Le document original dans sa langue d’origine doit être considéré comme la source faisant foi. Pour les informations critiques, une traduction professionnelle réalisée par un humain est recommandée. Nous déclinons toute responsabilité en cas de malentendus ou de mauvaises interprétations résultant de l’utilisation de cette traduction.