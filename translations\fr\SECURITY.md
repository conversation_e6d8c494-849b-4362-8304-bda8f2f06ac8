<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:50:11+00:00",
  "source_file": "SECURITY.md",
  "language_code": "fr"
}
-->
## Sécurité

Microsoft prend très au sérieux la sécurité de ses produits logiciels et services, ce qui inclut tous les dépôts de code source gérés via nos organisations GitHub, notamment [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin), ainsi que [nos organisations GitHub](https://opensource.microsoft.com/).

Si vous pensez avoir découvert une vulnérabilité de sécurité dans un dépôt appartenant à Microsoft qui correspond à la [définition de vulnérabilité de sécurité de Microsoft](https://aka.ms/opensource/security/definition), veuillez nous la signaler comme indiqué ci-dessous.

## Signalement des problèmes de sécurité

**Merci de ne pas signaler les vulnérabilités de sécurité via les issues publiques sur GitHub.**

À la place, veuillez les signaler au Microsoft Security Response Center (MSRC) à l’adresse [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Si vous préférez soumettre votre rapport sans vous connecter, envoyez un email à [<EMAIL>](mailto:<EMAIL>). Si possible, chiffrez votre message avec notre clé PGP ; vous pouvez la télécharger depuis la [page de la clé PGP du Microsoft Security Response Center](https://aka.ms/opensource/security/pgpkey).

Vous devriez recevoir une réponse sous 24 heures. Si, pour une raison quelconque, ce n’est pas le cas, veuillez relancer par email afin de vous assurer que nous avons bien reçu votre message initial. Des informations supplémentaires sont disponibles sur [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Merci d’inclure les informations demandées ci-dessous (autant que possible) pour nous aider à mieux comprendre la nature et l’étendue du problème potentiel :

  * Type de problème (par exemple débordement de tampon, injection SQL, cross-site scripting, etc.)
  * Chemins complets des fichiers source liés à la manifestation du problème
  * Emplacement du code source affecté (tag/branche/commit ou URL directe)
  * Toute configuration spéciale nécessaire pour reproduire le problème
  * Instructions détaillées pour reproduire le problème
  * Preuve de concept ou code d’exploitation (si possible)
  * Impact du problème, y compris la manière dont un attaquant pourrait l’exploiter

Ces informations nous aideront à traiter votre signalement plus rapidement.

Si vous signalez dans le cadre d’un programme de bug bounty, des rapports plus complets peuvent contribuer à une récompense plus élevée. Veuillez consulter notre page [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) pour plus de détails sur nos programmes en cours.

## Langues préférées

Nous préférons que toutes les communications soient en anglais.

## Politique

Microsoft suit le principe de la [Divulgation Coordonnée des Vulnérabilités](https://aka.ms/opensource/security/cvd).

**Avertissement** :  
Ce document a été traduit à l’aide du service de traduction automatique [Co-op Translator](https://github.com/Azure/co-op-translator). Bien que nous nous efforcions d’assurer l’exactitude, veuillez noter que les traductions automatiques peuvent contenir des erreurs ou des inexactitudes. Le document original dans sa langue d’origine doit être considéré comme la source faisant foi. Pour les informations critiques, une traduction professionnelle réalisée par un humain est recommandée. Nous déclinons toute responsabilité en cas de malentendus ou de mauvaises interprétations résultant de l’utilisation de cette traduction.