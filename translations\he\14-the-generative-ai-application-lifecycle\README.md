<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "27a5347a5022d5ef0a72ab029b03526a",
  "translation_date": "2025-07-09T15:55:54+00:00",
  "source_file": "14-the-generative-ai-application-lifecycle/README.md",
  "language_code": "he"
}
-->
[![שילוב עם קריאת פונקציות](../../../translated_images/14-lesson-banner.066d74a31727ac121eeac06376a068a397d8e335281e63ce94130d11f516e46b.he.png)](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst)

# מחזור החיים של יישומי AI גנרטיביים

שאלה חשובה לכל יישומי ה-AI היא הרלוונטיות של תכונות ה-AI, מכיוון שתחום ה-AI מתפתח במהירות. כדי להבטיח שהיישום שלך יישאר רלוונטי, אמין וחזק, יש צורך במעקב, הערכה ושיפור מתמידים. כאן נכנס לתמונה מחזור החיים של ה-AI הגנרטיבי.

מחזור החיים של ה-AI הגנרטיבי הוא מסגרת שמנחה אותך בשלבי הפיתוח, הפריסה והתחזוקה של יישום AI גנרטיבי. הוא עוזר לך להגדיר את המטרות שלך, למדוד את הביצועים, לזהות את האתגרים וליישם פתרונות. בנוסף, הוא מסייע לך ליישר את היישום עם הסטנדרטים האתיים והחוקיים של התחום והגורמים המעורבים. על ידי מעקב אחר מחזור החיים של ה-AI הגנרטיבי, תוכל להבטיח שהיישום שלך תמיד יספק ערך ויספק את צורכי המשתמשים.

## מבוא

בפרק זה תלמד:

- להבין את המעבר מה-MLOps ל-LLMOps  
- מחזור החיים של LLM  
- כלים למחזור החיים  
- מדידה והערכה במחזור החיים  

## להבין את המעבר מ-MLOps ל-LLMOps

LLMs הם כלי חדש בארסנל של הבינה המלאכותית, הם חזקים מאוד במשימות ניתוח ויצירה עבור יישומים, אך הכוח הזה מביא עמו השלכות על האופן שבו אנו מייעלים משימות AI ולמידת מכונה קלאסית.

לכן, אנו זקוקים לפרדיגמה חדשה שתתאים את הכלי הזה בצורה דינמית, עם התמריצים הנכונים. ניתן לסווג יישומי AI ישנים כ"ML Apps" ויישומי AI חדשים כ"GenAI Apps" או פשוט "AI Apps", המשקפים את הטכנולוגיות והטכניקות המרכזיות באותה תקופה. זה משנה את הסיפור שלנו בכמה מובנים, ראו את ההשוואה הבאה.

![השוואה בין LLMOps ל-MLOps](../../../translated_images/01-llmops-shift.29bc933cb3bb0080a562e1655c0c719b71a72c3be6252d5c564b7f598987e602.he.png)

שימו לב שב-LLMOps, אנו מתמקדים יותר במפתחי היישומים, משתמשים באינטגרציות כנקודת מפתח, משתמשים ב"Models-as-a-Service" וחושבים על המדדים הבאים:

- איכות: איכות התגובה  
- נזק: AI אחראי  
- יושרה: עמידות התגובה (האם זה הגיוני? האם זה נכון?)  
- עלות: תקציב הפתרון  
- השהייה: זמן ממוצע לתגובה לטוקן  

## מחזור החיים של LLM

ראשית, כדי להבין את מחזור החיים והשינויים, שימו לב לאינפוגרפיקה הבאה.

![אינפוגרפיקה של LLMOps](../../../translated_images/02-llmops.70a942ead05a7645db740f68727d90160cb438ab71f0fb20548bc7fe5cad83ff.he.png)

כפי שניתן לראות, זה שונה ממחזורי החיים הרגילים של MLOps. ל-LLMs יש דרישות חדשות רבות, כמו פרומפטינג, טכניקות שונות לשיפור האיכות (Fine-Tuning, RAG, Meta-Prompts), הערכה ואחריות עם AI אחראי, ולבסוף מדדי הערכה חדשים (איכות, נזק, יושרה, עלות והשהייה).

לדוגמה, שימו לב לאופן שבו אנו מפתחים רעיונות. שימוש בהנדסת פרומפטים כדי להתנסות עם LLMs שונים ולחקור אפשרויות כדי לבדוק אם ההשערות שלנו יכולות להיות נכונות.

שימו לב שזה לא תהליך ליניארי, אלא לולאות משולבות, איטרטיביות, עם מחזור כולל.

איך נוכל לחקור את השלבים האלה? בואו נצלול לפרטים כיצד לבנות מחזור חיים.

![זרימת עבודה של LLMOps](../../../translated_images/03-llm-stage-flows.3a1e1c401235a6cfa886ed6ba04aa52a096a545e1bc44fa54d7d5983a7201892.he.png)

זה עשוי להיראות קצת מסובך, בואו נתמקד בשלושת השלבים הגדולים תחילה.

1. יצירת רעיונות/חקירה: חקירה, כאן אנו יכולים לחקור בהתאם לצרכי העסק שלנו. יצירת אב-טיפוס, יצירת [PromptFlow](https://microsoft.github.io/promptflow/index.html?WT.mc_id=academic-105485-koreyst) ובדיקת יעילותו להשערות שלנו.  
1. בנייה/הרחבה: יישום, כעת מתחילים להעריך על מערכי נתונים גדולים יותר, מיישמים טכניקות כמו Fine-tuning ו-RAG, כדי לבדוק את החוסן של הפתרון שלנו. אם לא, ייתכן שנצטרך ליישם מחדש, להוסיף שלבים חדשים בזרימה או לארגן מחדש את הנתונים. לאחר בדיקת הזרימה והקנה מידה, אם זה עובד ועומד במדדים, הוא מוכן לשלב הבא.  
1. הפעלה: אינטגרציה, כעת מוסיפים מערכות ניטור והתראות למערכת, פריסה ואינטגרציה עם היישום.  

לאחר מכן, יש את מחזור הניהול הכולל, המתמקד באבטחה, תאימות וממשל.

כל הכבוד, עכשיו יש לך יישום AI מוכן לפעולה. לחוויה מעשית, עיין ב-[Contoso Chat Demo.](https://nitya.github.io/contoso-chat/?WT.mc_id=academic-105485-koreys)

עכשיו, אילו כלים נוכל להשתמש?

## כלים למחזור החיים

לגבי כלים, מיקרוסופט מספקת את [Azure AI Platform](https://azure.microsoft.com/solutions/ai/?WT.mc_id=academic-105485-koreys) ו-[PromptFlow](https://microsoft.github.io/promptflow/index.html?WT.mc_id=academic-105485-koreyst) שמקלים על יישום המחזור וגורמים לו להיות פשוט ונגיש.

[Azure AI Platform](https://azure.microsoft.com/solutions/ai/?WT.mc_id=academic-105485-koreys) מאפשרת לך להשתמש ב-[AI Studio](https://ai.azure.com/?WT.mc_id=academic-105485-koreys). AI Studio הוא פורטל אינטרנטי שמאפשר לך לחקור מודלים, דוגמאות וכלים, לנהל משאבים, לפתח זרימות UI ואפשרויות SDK/CLI לפיתוח קוד-ראשון.

![אפשרויות Azure AI](../../../translated_images/04-azure-ai-platform.80203baf03a12fa8b166e194928f057074843d1955177baf0f5b53d50d7b6153.he.png)

Azure AI מאפשרת לך להשתמש במשאבים רבים לניהול פעולות, שירותים, פרויקטים, חיפוש וקטורי וצרכי מסדי נתונים.

![LLMOps עם Azure AI](../../../translated_images/05-llm-azure-ai-prompt.a5ce85cdbb494bdf95420668e3464aae70d8b22275a744254e941dd5e73ae0d2.he.png)

בנה, מהוכחת היתכנות (POC) ועד יישומים בקנה מידה גדול עם PromptFlow:

- עיצוב ובניית יישומים מ-VS Code, עם כלים ויזואליים ופונקציונליים  
- בדיקה וכיול מדויק של היישומים לאיכות AI, בקלות  
- שימוש ב-Azure AI Studio לאינטגרציה ואיטרציה עם הענן, דחיפה ופריסה לאינטגרציה מהירה  

![LLMOps עם PromptFlow](../../../translated_images/06-llm-promptflow.a183eba07a3a7fdf4aa74db92a318b8cbbf4a608671f6b166216358d3203d8d4.he.png)

## מצוין! המשך ללמוד!

מדהים, עכשיו למד עוד על איך אנו מבנים יישום כדי להשתמש במושגים עם [Contoso Chat App](https://nitya.github.io/contoso-chat/?WT.mc_id=academic-105485-koreyst), כדי לראות איך Cloud Advocacy מוסיף את המושגים האלה בהדגמות. לתוכן נוסף, עיין במפגש הפיצול שלנו ב-[Ignite!](https://www.youtube.com/watch?v=DdOylyrTOWg)

כעת, בדוק את שיעור 15, כדי להבין כיצד [Retrieval Augmented Generation ו-Vector Databases](../15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst) משפיעים על AI גנרטיבי ומאפשרים יצירת יישומים מעניינים יותר!

**כתב ויתור**:  
מסמך זה תורגם באמצעות שירות תרגום מבוסס בינה מלאכותית [Co-op Translator](https://github.com/Azure/co-op-translator). למרות שאנו שואפים לדיוק, יש לקחת בחשבון כי תרגומים אוטומטיים עלולים להכיל שגיאות או אי-דיוקים. המסמך המקורי בשפת המקור שלו נחשב למקור הסמכותי. למידע קריטי מומלץ להשתמש בתרגום מקצועי על ידי מתרגם אנושי. אנו לא נושאים באחריות לכל אי-הבנה או פרשנות שגויה הנובעת משימוש בתרגום זה.