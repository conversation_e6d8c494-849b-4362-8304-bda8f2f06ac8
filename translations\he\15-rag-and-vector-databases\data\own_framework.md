<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:48:40+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "he"
}
-->
# מבוא לרשתות עצביות. פרספטרון רב-שכבתי

בפרק הקודם למדתם על המודל הפשוט ביותר של רשת עצבית - פרס<PERSON><PERSON>רון חד-שכבתי, מודל ליניארי לסיווג בינארי.

בפרק זה נרחיב את המודל הזה למסגרת גמישה יותר, שתאפשר לנו:

* לבצע **סיווג רב-מעמדי** בנוסף לסיווג בינארי
* לפתור **בעיות רגרסיה** בנוסף לסיווג
* להפריד בין מחלקות שאינן ניתנות להפרדה ליניארית

כמו כן, נפתח מסגרת מודולרית משלו בפייתון שתאפשר לנו לבנות ארכיטקטורות שונות של רשתות עצביות.

## פורמליזציה של למידת מכונה

נתחיל בפורמליזציה של בעיית למידת המכונה. נניח שיש לנו מערך אימון **X** עם תוויות **Y**, ואנחנו צריכים לבנות מודל *f* שיבצע את התחזיות המדויקות ביותר. איכות התחזיות נמדדת על ידי **פונקציית הפסד** ℒ. פונקציות הפסד הבאות משמשות לעיתים קרובות:

* לבעיית רגרסיה, כאשר יש לנבא מספר, ניתן להשתמש ב**שגיאה מוחלטת** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>|, או ב**שגיאה ריבועית** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup>
* לסיווג, משתמשים ב**אובדן 0-1** (שזה בעצם אותו דבר כמו **דיוק** המודל), או ב**אובדן לוגיסטי**.

לפרספטרון חד-שכבתי, הפונקציה *f* הוגדרה כפונקציה ליניארית *f(x)=wx+b* (כאן *w* היא מטריצת המשקלים, *x* הוא וקטור התכונות הקלט, ו-*b* הוא וקטור ההטיה). בארכיטקטורות שונות של רשתות עצביות, פונקציה זו יכולה לקבל צורה מורכבת יותר.

> במקרה של סיווג, לעיתים רצוי לקבל הסתברויות של המחלקות המתאימות כפלט מהרשת. כדי להמיר מספרים אקראיים להסתברויות (למשל לנרמל את הפלט), משתמשים לעיתים קרובות בפונקציית **softmax** σ, והפונקציה *f* הופכת ל-*f(x)=σ(wx+b)*

בהגדרת *f* שלמעלה, *w* ו-*b* נקראים **פרמטרים** θ=⟨*w,b*⟩. בהתחשב במערך הנתונים ⟨**X**,**Y**⟩, ניתן לחשב את השגיאה הכוללת על כל מערך הנתונים כפונקציה של הפרמטרים θ.

> ✅ **מטרת האימון של הרשת העצבית היא למזער את השגיאה על ידי שינוי הפרמטרים θ**

## אופטימיזציית ירידת גרדיאנט

ישנה שיטה מוכרת לאופטימיזציה של פונקציות הנקראת **ירידת גרדיאנט**. הרעיון הוא שניתן לחשב נגזרת (במקרה רב-ממדי נקראת **גרדיאנט**) של פונקציית ההפסד ביחס לפרמטרים, ולשנות את הפרמטרים כך שהשגיאה תקטן. ניתן לתאר זאת פורמלית כך:

* לאתחל את הפרמטרים בערכים אקראיים w<sup>(0)</sup>, b<sup>(0)</sup>
* לחזור על הצעד הבא מספר פעמים:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

במהלך האימון, שלבי האופטימיזציה אמורים להיחשב על כל מערך הנתונים (זכרו שההפסד מחושב כסכום על כל דגימות האימון). עם זאת, במציאות לוקחים חלקים קטנים ממערך הנתונים הנקראים **minibatches**, ומחשבים את הגרדיאנטים על סמך תת-קבוצה של הנתונים. מכיוון שהתת-קבוצה נבחרת באקראי בכל פעם, שיטה זו נקראת **ירידת גרדיאנט סטוכסטית** (SGD).

## פרספטרונים רב-שכבתיים ו-backpropagation

רשת חד-שכבתית, כפי שראינו למעלה, מסוגלת לסווג מחלקות הניתנות להפרדה ליניארית. כדי לבנות מודל עשיר יותר, ניתן לשלב מספר שכבות ברשת. מתמטית, זה אומר שהפונקציה *f* תקבל צורה מורכבת יותר, ותחושב בכמה שלבים:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

כאן, α היא **פונקציית הפעלה לא ליניארית**, σ היא פונקציית softmax, והפרמטרים θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*>.

אלגוריתם ירידת הגרדיאנט נשאר זהה, אך חישוב הגרדיאנטים יהיה מורכב יותר. בהתחשב בכלל השרשרת של הנגזרות, ניתן לחשב נגזרות כך:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ כלל השרשרת משמש לחישוב נגזרות של פונקציית ההפסד ביחס לפרמטרים.

שימו לב שהחלק השמאלי ביותר של כל הביטויים האלה זהה, ולכן ניתן לחשב את הנגזרות בצורה יעילה החל מפונקציית ההפסד והולכים "לאחור" דרך גרף החישוב. לכן שיטת האימון של פרספטרון רב-שכבתי נקראת **backpropagation**, או בקיצור 'backprop'.

> TODO: image citation

> ✅ נפרט על backprop בהרחבה בדוגמת המחברת שלנו.

## סיכום

בשיעור זה בנינו ספריית רשת עצבית משלנו, והשתמשנו בה למשימת סיווג פשוטה דו-ממדית.

## 🚀 אתגר

במחברת המצורפת, תיישמו מסגרת משלכם לבניית ואימון פרספטרונים רב-שכבתיים. תוכלו לראות בפירוט כיצד רשתות עצביות מודרניות פועלות.

המשיכו למחברת OwnFramework ועבדו דרכה.

## סקירה ולמידה עצמית

Backpropagation הוא אלגוריתם נפוץ ב-AI ו-ML, שראוי ללמוד אותו לעומק.

## מטלה

במעבדה זו, עליכם להשתמש במסגרת שבניתם בשיעור זה כדי לפתור את בעיית סיווג ספרות MNIST בכתב יד.

* הוראות
* מחברת

**כתב ויתור**:  
מסמך זה תורגם באמצעות שירות תרגום מבוסס בינה מלאכותית [Co-op Translator](https://github.com/Azure/co-op-translator). למרות שאנו שואפים לדיוק, יש לקחת בחשבון כי תרגומים אוטומטיים עלולים להכיל שגיאות או אי-דיוקים. המסמך המקורי בשפת המקור שלו נחשב למקור הסמכותי. למידע קריטי מומלץ להשתמש בתרגום מקצועי על ידי מתרגם אנושי. אנו לא נושאים באחריות לכל אי-הבנה או פרשנות שגויה הנובעת משימוש בתרגום זה.