<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T16:59:17+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "he"
}
-->
# מבוא לרשתות עצביות: פרספטרון

אחת הניסיונות הראשונות ליישם משהו דומה לרשת עצבית מודרנית נעשתה על ידי פרנק רוזנבלט ממעבדת קורנל לאווירונאוטיקה בשנת 1957. זו הייתה יישום חומרה שנקרא "Mark-1", שנועד לזהות צורות גאומטריות פשוטות, כמו משולשים, ריבועים ומעגלים.

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='Frank Rosenblatt'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> תמונות מוויקיפדיה

תמונה קלט הוצגה על ידי מערך של 20x20 תאי צילום, כך שלרשת העצבית היו 400 קלטים ופלט בינארי אחד. רשת פשוטה כללה נוירון אחד, שנקרא גם **יחידת לוגיקה סף**. המשקלים ברשת העצבית פעלו כמו פוטנציומטרים שדרשו כוונון ידני במהלך שלב האימון.

> ✅ פוטנציומטר הוא מכשיר שמאפשר למשתמש לכוון את ההתנגדות במעגל.

> הניו יורק טיימס כתב על הפרספטרון באותה תקופה: *העובר של מחשב אלקטרוני ש[הצי] מצפה שיוכל ללכת, לדבר, לראות, לכתוב, לשכפל את עצמו ולהיות מודע לקיומו.*

## מודל הפרספטרון

נניח שיש לנו N תכונות במודל שלנו, במקרה כזה וקטור הקלט יהיה וקטור בגודל N. פרספטרון הוא מודל **סיווג בינארי**, כלומר הוא יכול להבחין בין שתי קטגוריות של נתוני קלט. נניח שלכל וקטור קלט x הפלט של הפרספטרון שלנו יהיה או +1 או -1, בהתאם לקטגוריה. הפלט יחושב באמצעות הנוסחה:

y(x) = f(w<sup>T</sup>x)

כאשר f היא פונקציית הפעלה מסוג שלב

## אימון הפרספטרון

כדי לאמן פרספטרון עלינו למצוא וקטור משקלים w שמסווג נכון את רוב הערכים, כלומר מביא ל**שגיאה** הקטנה ביותר. שגיאה זו מוגדרת על ידי **קריטריון הפרספטרון** באופן הבא:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

כאשר:

* הסכום נלקח על נקודות הנתונים i באימון שמובילות לסיווג שגוי
* x<sub>i</sub> הוא נתון הקלט, ו-t<sub>i</sub> הוא או -1 או +1 עבור דוגמאות שליליות וחיוביות בהתאמה.

קריטריון זה נחשב כפונקציה של המשקלים w, ועלינו למזער אותו. לעיתים קרובות משתמשים בשיטה שנקראת **ירידת מדרון** (gradient descent), שבה מתחילים ממשקלים התחלתיים w<sup>(0)</sup>, ואז בכל שלב מעדכנים את המשקלים לפי הנוסחה:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

כאן η הוא מה שנקרא **קצב הלמידה**, ו-∇E(w) מייצג את **גרדיאנט** של E. לאחר חישוב הגרדיאנט, נקבל

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

האלגוריתם בפייתון נראה כך:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## סיכום

בשיעור זה למדתם על פרספטרון, שהוא מודל סיווג בינארי, ואיך לאמן אותו באמצעות וקטור משקלים.

## 🚀 אתגר

אם תרצו לנסות לבנות פרספטרון משלכם, נסו את המעבדה הזו ב-Microsoft Learn שמשתמשת ב-Azure ML designer


## סקירה ולמידה עצמית

כדי לראות איך אפשר להשתמש בפרספטרון כדי לפתור בעיה פשוטה וגם בעיות מהחיים האמיתיים, ולהמשיך ללמוד - עברו למחברת Perceptron.

הנה גם מאמר מעניין על פרספטרונים.

## משימה

בשיעור זה יישמנו פרספטרון למשימת סיווג בינארית, והשתמשנו בו כדי לסווג בין שתי ספרות כתובות ביד. במעבדה זו, עליכם לפתור את בעיית סיווג הספרות במלואה, כלומר לקבוע איזו ספרה היא הסבירה ביותר שתתאים לתמונה נתונה.

* הוראות
* מחברת

**כתב ויתור**:  
מסמך זה תורגם באמצעות שירות תרגום מבוסס בינה מלאכותית [Co-op Translator](https://github.com/Azure/co-op-translator). למרות שאנו שואפים לדיוק, יש לקחת בחשבון כי תרגומים אוטומטיים עלולים להכיל שגיאות או אי-דיוקים. המסמך המקורי בשפת המקור שלו נחשב למקור הסמכותי. למידע קריטי מומלץ להשתמש בתרגום מקצועי על ידי מתרגם אנושי. אנו לא נושאים באחריות לכל אי-הבנה או פרשנות שגויה הנובעת משימוש בתרגום זה.