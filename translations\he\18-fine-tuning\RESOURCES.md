<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:05:05+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "he"
}
-->
# משאבים ללמידה עצמית

השיעור נבנה תוך שימוש במגוון משאבים מרכזיים מ-OpenAI ו-Azure OpenAI כהפניות למונחים והדרכות. להלן רשימה לא מקיפה, לטובת מסעות הלמידה העצמית שלכם.

## 1. משאבים ראשיים

| כותרת/קישור                                                                                                                                                                                                                 | תיאור                                                                                                                                                                                                                                                                                                                                                                                        |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | כוונון עדין משפר את הלמידה עם דוגמאות מועטות על ידי אימון על הרבה יותר דוגמאות ממה שניתן להכניס לפרומפט, חוסך עלויות, משפר את איכות התגובה ומאפשר בקשות עם זמן תגובה נמוך יותר. **קבלו סקירה כללית על כוונון עדין מ-OpenAI.**                                                                                                         |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | הבן **מהו כוונון עדין (מושג)**, מדוע כדאי לשקול אותו (הבעיה המניעה), אילו נתונים להשתמש (אימון) וכיצד למדוד את האיכות                                                                                                                                                                                                                 |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | שירות Azure OpenAI מאפשר לך להתאים את המודלים שלנו לנתונים האישיים שלך באמצעות כוונון עדין. למד **איך לבצע כוונון עדין (תהליך)**, לבחור מודלים באמצעות Azure AI Studio, Python SDK או REST API.                                                                                                                               |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | מודלים גדולים של שפה (LLMs) עשויים לא להצליח היטב בתחומים, משימות או מערכי נתונים ספציפיים, או לייצר פלטים לא מדויקים או מטעות. **מתי כדאי לשקול כוונון עדין** כפתרון אפשרי לכך?                                                                                                                                               |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | כוונון עדין רציף הוא תהליך איטרטיבי של בחירת מודל שכבר כוונן כבסיס ו**כוונונו עוד יותר** על מערכי דוגמאות אימון חדשים.                                                                                                                                                                                                             |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | כוונון עדין של המודל שלך **עם דוגמאות של קריאות לפונקציות** יכול לשפר את הפלט על ידי קבלת תגובות מדויקות ועקביות יותר - עם תגובות בפורמט דומה וחיסכון בעלויות                                                                                                                                                                   |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | עיין בטבלה זו כדי להבין **אילו מודלים ניתן לכוונן עדין** ב-Azure OpenAI, ובאילו אזורים הם זמינים. בדוק את מגבלות הטוקנים ותאריכי תפוגת נתוני האימון במידת הצורך.                                                                                                                                                                  |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | פרק של 30 דקות מ-**אוקטובר 2023** בתוכנית AI Show, שבו נדונים היתרונות, החסרונות ותובנות מעשיות שיעזרו לך לקבל החלטה זו.                                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | משאב זה ב-**AI Playbook** מלווה אותך בדרישות הנתונים, עיצוב הפורמט, כוונון היפרפרמטרים ואתגרים/מגבלות שכדאי להכיר.                                                                                                                                                                                                                   |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | למד כיצד ליצור מערך נתונים לדוגמה לכוונון עדין, להתכונן לכוונון, ליצור משימת כוונון עדין ולפרוס את המודל המכוונן ב-Azure.                                                                                                                                                                                                          |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio מאפשר לך להתאים מודלים גדולים של שפה לנתונים האישיים שלך _באמצעות ממשק משתמש מתאים למפתחים עם מעט קוד_. ראה דוגמה זו.                                                                                                                                                                                             |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | מאמר זה מתאר כיצד לכוונן עדין מודל Hugging Face עם ספריית Hugging Face transformers על GPU יחיד באמצעות Azure DataBricks וספריות Hugging Face Trainer.                                                                                                                                                                            |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | קטלוג המודלים ב-Azure Machine Learning מציע מודלים רבים בקוד פתוח שניתן לכוונן למשימה הספציפית שלך. נסה את המודול הזה מתוך [מסלול הלמידה של AzureML Generative AI](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | כוונון עדין של מודלים GPT-3.5 או GPT-4 ב-Microsoft Azure באמצעות W&B מאפשר מעקב וניתוח מפורטים של ביצועי המודל. מדריך זה מרחיב את המושגים מדריך הכוונון העדין של OpenAI עם שלבים ותכונות ספציפיות ל-Azure OpenAI.                                                                                                                  |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                               |

## 2. משאבים משניים

חלק זה כולל משאבים נוספים שכדאי לחקור, אך לא הספקנו לכסות בשיעור זה. ייתכן שיכוסו בשיעור עתידי, או כאפשרות משימה משנית במועד מאוחר יותר. לעת עתה, השתמשו בהם כדי לבנות את המומחיות והידע שלכם בנושא.

| כותרת/קישור                                                                                                                                                                                                            | תיאור                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [הכנת נתונים וניתוח לכוונון עדין של מודל שיחה](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | מחברת זו משמשת ככלי לעיבוד מקדים וניתוח מערך הנתונים של השיחה המשמש לכוונון עדין של מודל שיחה. היא בודקת שגיאות פורמט, מספקת סטטיסטיקות בסיסיות ומעריכה את ספירת הטוקנים לצורך חישוב עלויות הכוונון. ראה: [שיטת כוונון עדין ל-gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [כוונון עדין ל-Retrieval Augmented Generation (RAG) עם Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | מטרת מחברת זו היא להדריך דוגמה מקיפה כיצד לכוונן עדין מודלים של OpenAI ל-Retrieval Augmented Generation (RAG). נשלב גם את Qdrant ולמידה עם דוגמאות מועטות לשיפור ביצועי המודל ולהפחתת המצאות שגויות.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [כוונון GPT עם Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) היא פלטפורמת מפתחים ל-AI עם כלים לאימון מודלים, כוונון עדין וניצול מודלים בסיסיים. קרא תחילה את מדריך [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst), ואז נסה את התרגיל במחברת.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - כוונון עדין למודלים קטנים של שפה                                                   | הכירו את [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), המודל הקטן החדש של מיקרוסופט, חזק ומרשים למרות גודלו הקומפקטי. מדריך זה ילווה אותך בתהליך כוונון Phi-2, כולל בניית מערך נתונים ייחודי וכוונון המודל באמצעות QLoRA.                                                                                                                                                                       |
| **Hugging Face Tutorial** [כיצד לכוונן LLMs ב-2024 עם Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | פוסט בלוג זה מדריך כיצד לכוונן מודלים גדולים של שפה פתוחים באמצעות Hugging Face TRL, Transformers ומערכי נתונים בשנת 2024. אתה מגדיר מקרה שימוש, מכין סביבת פיתוח, מכין מערך נתונים, מבצע כוונון, בודק ומעריך, ואז מפעיל לפרודקשן.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | מביא אימון ופריסה מהירים וקלים יותר של [מודלים מתקדמים ללמידת מכונה](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). הריפו כולל מדריכים ידידותיים ל-Colab עם הדרכה בווידאו ביוטיוב, לכוונון עדין. **משקף עדכון [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) עדכני**. קרא את [תיעוד AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**כתב ויתור**:  
מסמך זה תורגם באמצעות שירות תרגום מבוסס בינה מלאכותית [Co-op Translator](https://github.com/Azure/co-op-translator). למרות שאנו שואפים לדיוק, יש לקחת בחשבון כי תרגומים אוטומטיים עלולים להכיל שגיאות או אי-דיוקים. המסמך המקורי בשפת המקור שלו נחשב למקור הסמכותי. למידע קריטי מומלץ להשתמש בתרגום מקצועי על ידי מתרגם אנושי. אנו לא נושאים באחריות לכל אי-הבנה או פרשנות שגויה הנובעת משימוש בתרגום זה.