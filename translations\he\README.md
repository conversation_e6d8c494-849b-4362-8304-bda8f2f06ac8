<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:38:08+00:00",
  "source_file": "README.md",
  "language_code": "he"
}
-->
![Generative AI למתחילים](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.he.png)

### 21 שיעורים המלמדים את כל מה שצריך לדעת כדי להתחיל לבנות יישומי Generative AI

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 תמיכה בריבוי שפות

#### נתמך באמצעות GitHub Action (אוטומטי ותמיד מעודכן)

[French](../fr/README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](../ja/README.md) | [Korean](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](../el/README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](./README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](../sk/README.md) | [Romanian](../ro/README.md) | [Bulgarian](../bg/README.md) | [Serbian (Cyrillic)](../sr/README.md) | [Croatian](../hr/README.md) | [Slovenian](../sl/README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Generative AI למתחילים (גרסה 3) - קורס

למדו את היסודות של בניית יישומי Generative AI עם הקורס המקיף שלנו הכולל 21 שיעורים, בהנחיית Microsoft Cloud Advocates.

## 🌱 להתחלה

הקורס כולל 21 שיעורים. כל שיעור עוסק בנושא שונה, אז התחילו מאיפה שנוח לכם!

השיעורים מסומנים כ"שיעורי Learn" שמסבירים מושג ב-Generative AI או "שיעורי Build" שמסבירים מושג ומציגים דוגמאות קוד ב-**Python** ו-**TypeScript** כשאפשר.

למפתחי .NET מומלץ לבדוק את [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

כל שיעור כולל גם חלק "Keep Learning" עם כלים נוספים ללמידה.

## מה צריך
### כדי להריץ את הקוד של הקורס, אפשר להשתמש באחד מהבאים:
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **שיעורים:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **שיעורים:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **שיעורים:** "oai-assignment" 
   
- ידע בסיסי ב-Python או TypeScript יעזור - \*למתחילים מוחלטים מומלץ לעבור את הקורסים האלה ב-[Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) ו-[TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst)
- חשבון GitHub כדי [לעשות fork לכל המאגר הזה](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) לחשבון ה-GitHub האישי שלכם

יצרנו שיעור **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** שיעזור לכם בהגדרת סביבת הפיתוח.

אל תשכחו [לסמן בכוכב (🌟) את המאגר הזה](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst) כדי למצוא אותו בקלות בהמשך.

## 🧠 מוכנים לפרוס?

אם אתם מחפשים דוגמאות קוד מתקדמות יותר, בדקו את [אוסף דוגמאות הקוד ל-Generative AI](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) שלנו ב-**Python** ו-**TypeScript**.

## 🗣️ פגשו לומדים אחרים, קבלו תמיכה

הצטרפו לשרת ה-Discord הרשמי של Azure AI Foundry ב-[קישור הזה](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) כדי להכיר וליצור קשר עם לומדים אחרים שלוקחים את הקורס ולקבל תמיכה.

שאלו שאלות או שתפו משוב על המוצר בפורום המפתחים של [Azure AI Foundry](https://aka.ms/azureaifoundry/forum) ב-GitHub.

## 🚀 בונים סטארטאפ?

הירשמו ל-[Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) כדי לקבל **קרדיטים חינמיים ל-OpenAI** ועד **150,000$ קרדיטים ל-Azure** לגישה למודלים של OpenAI דרך Azure OpenAI Services.

## 🙏 רוצים לעזור?

יש לכם הצעות או מצאתם שגיאות כתיב או בקוד? [פתחו Issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) או [צרו Pull Request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 כל שיעור כולל:

- הקדמה קצרה בווידאו לנושא
- שיעור כתוב ב-README
- דוגמאות קוד ב-Python ו-TypeScript התומכות ב-Azure OpenAI ו-OpenAI API
- קישורים למשאבים נוספים להמשך הלמידה

## 🗃️ שיעורים

| #   | **קישור לשיעור**                                                                                                                              | **תיאור**                                                                                 | **וידאו**                                                                   | **למידה נוספת**                                                             |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | --------------------------------------------------------------------------- |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                   | **Learn:** איך להגדיר את סביבת הפיתוח שלך                                               | וידאו בקרוב                                                                 | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Introduction to Generative AI and LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                                | **Learn:** הבנת מה זה Generative AI ואיך מודלים שפתיים גדולים (LLMs) עובדים               | [וידאו](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Exploring and comparing different LLMs](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)               | **Learn:** איך לבחור את המודל המתאים למקרה השימוש שלך                                   | [וידאו](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Using Generative AI Responsibly](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                             | **Learn:** איך לבנות יישומי Generative AI באחריות                                      | [וידאו](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Understanding Prompt Engineering Fundamentals](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)               | **Learn:** תרגול מעשי של שיטות עבודה מומלצות בהנדסת פרומפטים                           | [וידאו](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Creating Advanced Prompts](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                  | **Learn:** איך ליישם טכניקות הנדסת פרומפטים שמשפרות את התוצאה של הפרומפטים שלך          | [וידאו](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [בניית אפליקציות ליצירת טקסט](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **בניית:** אפליקציית יצירת טקסט באמצעות Azure OpenAI / OpenAI API                                | [וידאו](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [בניית אפליקציות צ'אט](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **בניית:** טכניקות לבניית אינטגרציה יעילה של אפליקציות צ'אט.               | [וידאו](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [בניית אפליקציות חיפוש עם מסדי נתונים וקטוריים](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **בניית:** אפליקציית חיפוש המשתמשת ב-Embeddings לחיפוש מידע.                        | [וידאו](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [בניית אפליקציות ליצירת תמונות](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **בניית:** אפליקציה ליצירת תמונות                                                       | [וידאו](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [בניית אפליקציות AI בקוד נמוך](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **בניית:** אפליקציית Generative AI באמצעות כלים של Low Code                                     | [וידאו](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [אינטגרציה של אפליקציות חיצוניות עם Function Calling](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **בניית:** מה זה function calling ומתי משתמשים בו באפליקציות                          | [וידאו](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [עיצוב UX לאפליקציות AI](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **למידה:** איך ליישם עקרונות עיצוב UX בפיתוח אפליקציות Generative AI         | [וידאו](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [אבטחת אפליקציות Generative AI שלך](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **למידה:** האיומים והסיכונים למערכות AI ודרכים לאבטחתן.             | [וידאו](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [מחזור החיים של אפליקציות Generative AI](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **למידה:** הכלים והמדדים לניהול מחזור החיים של LLM ו-LLMOps                         | [וידאו](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) ומסדי נתונים וקטוריים](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **בניית:** אפליקציה המשתמשת במסגרת RAG לשליפת embeddings ממסדי נתונים וקטוריים  | [וידאו](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [מודלים בקוד פתוח ו-Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **בניית:** אפליקציה המשתמשת במודלים בקוד פתוח הזמינים ב-Hugging Face                    | [וידאו](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [סוכני AI](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **בניית:** אפליקציה המשתמשת במסגרת סוכן AI                                           | [וידאו](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [כיוונון עדין של LLMs](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **למידה:** מהו, למה ואיך לבצע כיוונון עדין ל-LLMs                                            | [וידאו](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [בניית אפליקציות עם SLMs](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **למידה:** היתרונות של בנייה עם Small Language Models                                            | וידאו בקרוב | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [בניית אפליקציות עם מודלי Mistral](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **למידה:** התכונות וההבדלים של משפחת מודלי Mistral                                           | וידאו בקרוב | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [בניית אפליקציות עם מודלי Meta](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **למידה:** התכונות וההבדלים של משפחת מודלי Meta                                           | וידאו בקרוב | [למידע נוסף](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 תודה מיוחדת

תודה מיוחדת ל-[**John Aziz**](https://www.linkedin.com/in/john0isaac/) על יצירת כל ה-GitHub Actions וה-Workflows

[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) על תרומות מפתח בכל שיעור לשיפור חוויית הלמידה והקוד.

## 🎒 קורסים נוספים

הצוות שלנו מפתח קורסים נוספים! בדקו:

- [**חדש** Model Context Protocol למתחילים](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI Agents למתחילים](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Generative AI למתחילים עם .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Generative AI למתחילים עם JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML למתחילים](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science למתחילים](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI למתחילים](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Cybersecurity למתחילים](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Web Dev למתחילים](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT למתחילים](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR Development למתחילים](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Mastering GitHub Copilot לתכנות משותף עם AI](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Mastering GitHub Copilot למפתחי C#/.NET](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [בחר את ההרפתקה שלך עם Copilot](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**כתב ויתור**:  
מסמך זה תורגם באמצעות שירות תרגום מבוסס בינה מלאכותית [Co-op Translator](https://github.com/Azure/co-op-translator). למרות שאנו שואפים לדיוק, יש לקחת בחשבון כי תרגומים אוטומטיים עלולים להכיל שגיאות או אי-דיוקים. המסמך המקורי בשפת המקור שלו נחשב למקור הסמכותי. למידע קריטי מומלץ להשתמש בתרגום מקצועי על ידי מתרגם אנושי. אנו לא נושאים באחריות לכל אי-הבנה או פרשנות שגויה הנובעת משימוש בתרגום זה.