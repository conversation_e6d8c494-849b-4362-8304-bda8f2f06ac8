<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:28:27+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "hi"
}
-->
# Neural Network Frameworks

जैसा कि हमने पहले ही सीखा है, न्यूरल नेटवर्क को प्रभावी ढंग से ट्रेन करने के लिए हमें दो काम करने होते हैं:

* टेन्सर्स पर ऑपरेशन करना, जैसे गुणा करना, जोड़ना, और कुछ फंक्शन्स जैसे sigmoid या softmax की गणना करना
* सभी एक्सप्रेशन्स के ग्रेडिएंट्स की गणना करना, ताकि ग्रेडिएंट डिसेंट ऑप्टिमाइजेशन किया जा सके

जहां `numpy` लाइब्रेरी पहला काम कर सकती है, वहीं ग्रेडिएंट्स की गणना के लिए हमें कोई अन्य तरीका चाहिए। हमारे फ्रेमवर्क में, जिसे हमने पिछले सेक्शन में विकसित किया था, हमें `backward` मेथड के अंदर सभी डेरिवेटिव फंक्शन्स मैन्युअली प्रोग्राम करने पड़ते थे, जो बैकप्रोपेगेशन करता है। आदर्श रूप से, एक फ्रेमवर्क हमें *किसी भी एक्सप्रेशन* के ग्रेडिएंट्स की गणना करने का मौका देना चाहिए जिसे हम परिभाषित कर सकें।

एक और महत्वपूर्ण बात यह है कि GPU या किसी अन्य विशेष कंप्यूट यूनिट जैसे TPU पर कंप्यूटेशन कर सकें। डीप न्यूरल नेटवर्क ट्रेनिंग में *बहुत* सारी गणनाएं होती हैं, और उन गणनाओं को GPUs पर समानांतर रूप से चलाना बहुत जरूरी है।

> ✅ 'parallelize' का मतलब है गणनाओं को कई डिवाइसेस में बांटना।

वर्तमान में, दो सबसे लोकप्रिय न्यूरल फ्रेमवर्क हैं: TensorFlow और PyTorch। दोनों CPU और GPU दोनों पर टेन्सर्स के साथ काम करने के लिए लो-लेवल API प्रदान करते हैं। लो-लेवल API के ऊपर, उच्च-स्तरीय API भी है, जिन्हें क्रमशः Keras और PyTorch Lightning कहा जाता है।

Low-Level API | TensorFlow| PyTorch
--------------|-------------------------------------|--------------------------------
High-level API| Keras| Pytorch

**लो-लेवल APIs** दोनों फ्रेमवर्क में आपको **computational graphs** बनाने की अनुमति देते हैं। यह ग्राफ यह परिभाषित करता है कि आउटपुट (आमतौर पर लॉस फंक्शन) को इनपुट पैरामीटर्स के साथ कैसे गणना किया जाए, और यदि GPU उपलब्ध हो तो इसे GPU पर भेजा जा सकता है। इस computational graph को डिफरेंशिएट करने और ग्रेडिएंट्स की गणना करने के लिए फंक्शन्स होते हैं, जिन्हें बाद में मॉडल पैरामीटर्स को ऑप्टिमाइज़ करने के लिए इस्तेमाल किया जा सकता है।

**हाई-लेवल APIs** न्यूरल नेटवर्क्स को एक **लेयर्स की श्रृंखला** के रूप में देखते हैं, और अधिकांश न्यूरल नेटवर्क्स को बनाना बहुत आसान बना देते हैं। मॉडल ट्रेनिंग के लिए आमतौर पर डेटा तैयार करना होता है और फिर `fit` फंक्शन कॉल करना होता है।

हाई-लेवल API आपको बिना ज्यादा डिटेल्स की चिंता किए सामान्य न्यूरल नेटवर्क्स जल्दी से बनाने की सुविधा देता है। वहीं, लो-लेवल API ट्रेनिंग प्रक्रिया पर अधिक नियंत्रण देता है, इसलिए रिसर्च में जब आप नए न्यूरल नेटवर्क आर्किटेक्चर के साथ काम कर रहे होते हैं, तब इसका ज्यादा उपयोग होता है।

यह समझना भी जरूरी है कि आप दोनों APIs को साथ में भी इस्तेमाल कर सकते हैं, जैसे कि आप लो-लेवल API का उपयोग करके अपनी खुद की नेटवर्क लेयर आर्किटेक्चर विकसित कर सकते हैं, और फिर उसे हाई-लेवल API के साथ बने बड़े नेटवर्क में इस्तेमाल कर सकते हैं। या आप हाई-लेवल API का उपयोग करके लेयर्स की श्रृंखला के रूप में नेटवर्क परिभाषित कर सकते हैं, और फिर अपनी खुद की लो-लेवल ट्रेनिंग लूप का उपयोग करके ऑप्टिमाइजेशन कर सकते हैं। दोनों APIs एक ही मूलभूत अवधारणाओं पर आधारित हैं, और इन्हें साथ में काम करने के लिए डिज़ाइन किया गया है।

## Learning

इस कोर्स में, हम अधिकांश कंटेंट PyTorch और TensorFlow दोनों के लिए प्रदान करते हैं। आप अपनी पसंद के फ्रेमवर्क को चुन सकते हैं और केवल संबंधित नोटबुक्स को देख सकते हैं। यदि आप तय नहीं कर पा रहे हैं कि कौन सा फ्रेमवर्क चुनना है, तो इंटरनेट पर **PyTorch vs. TensorFlow** पर चर्चा पढ़ें। आप दोनों फ्रेमवर्क्स को देखकर भी बेहतर समझ प्राप्त कर सकते हैं।

जहां संभव हो, हम सरलता के लिए हाई-लेवल APIs का उपयोग करेंगे। हालांकि, हमें यह समझना जरूरी है कि न्यूरल नेटवर्क्स कैसे काम करते हैं, इसलिए शुरुआत में हम लो-लेवल API और टेन्सर्स के साथ काम करना शुरू करते हैं। लेकिन यदि आप जल्दी शुरू करना चाहते हैं और इन डिटेल्स में ज्यादा समय नहीं लगाना चाहते, तो आप इन्हें छोड़कर सीधे हाई-लेवल API नोटबुक्स पर जा सकते हैं।

## ✍️ Exercises: Frameworks

अपने सीखने को निम्नलिखित नोटबुक्स में जारी रखें:

Low-Level API | TensorFlow+Keras Notebook | PyTorch
--------------|-------------------------------------|--------------------------------
High-level API| Keras | *PyTorch Lightning*

फ्रेमवर्क्स में महारत हासिल करने के बाद, आइए ओवरफिटिंग की अवधारणा को दोबारा समझें।

# Overfitting

ओवरफिटिंग मशीन लर्निंग में एक बेहद महत्वपूर्ण अवधारणा है, और इसे सही समझना बहुत जरूरी है!

निम्नलिखित समस्या पर विचार करें जिसमें 5 डॉट्स (ग्राफ में `x` द्वारा दर्शाए गए) को अप्रोक्सिमेट करना है:

!linear | overfit
-------------------------|--------------------------
**Linear model, 2 parameters** | **Non-linear model, 7 parameters**
Training error = 5.3 | Training error = 0
Validation error = 5.1 | Validation error = 20

* बाईं ओर, हम एक अच्छी सीधी रेखा का अप्रोक्सिमेशन देखते हैं। क्योंकि पैरामीटर्स की संख्या उपयुक्त है, मॉडल पॉइंट वितरण के पीछे की सोच को सही समझ पाता है।
* दाईं ओर, मॉडल बहुत शक्तिशाली है। चूंकि हमारे पास केवल 5 पॉइंट्स हैं और मॉडल में 7 पैरामीटर्स हैं, यह सभी पॉइंट्स से होकर गुजर सकता है, जिससे ट्रेनिंग एरर 0 हो जाता है। हालांकि, इससे मॉडल डेटा के पीछे सही पैटर्न को समझने में असमर्थ रहता है, इसलिए वैलिडेशन एरर बहुत अधिक होता है।

मॉडल की जटिलता (पैरामीटर्स की संख्या) और ट्रेनिंग सैंपल्स की संख्या के बीच सही संतुलन बनाना बहुत जरूरी है।

## Why overfitting occurs

  * पर्याप्त ट्रेनिंग डेटा न होना
  * बहुत शक्तिशाली मॉडल
  * इनपुट डेटा में बहुत अधिक शोर (noise)

## How to detect overfitting

जैसा कि ऊपर के ग्राफ से देखा जा सकता है, ओवरफिटिंग का पता बहुत कम ट्रेनिंग एरर और उच्च वैलिडेशन एरर से चलता है। आमतौर पर ट्रेनिंग के दौरान हम दोनों ट्रेनिंग और वैलिडेशन एरर को घटते हुए देखेंगे, और फिर किसी बिंदु पर वैलिडेशन एरर घटना बंद कर सकता है और बढ़ने लग सकता है। यह ओवरफिटिंग का संकेत होगा, और यह बताता है कि हमें शायद इस बिंदु पर ट्रेनिंग रोक देनी चाहिए (या कम से कम मॉडल का स्नैपशॉट लेना चाहिए)।

overfitting

## How to prevent overfitting

यदि आप देख रहे हैं कि ओवरफिटिंग हो रही है, तो आप निम्नलिखित में से कोई एक कर सकते हैं:

 * ट्रेनिंग डेटा की मात्रा बढ़ाएं
 * मॉडल की जटिलता कम करें
 * कुछ रेगुलराइजेशन तकनीक का उपयोग करें, जैसे Dropout, जिसे हम बाद में देखेंगे।

## Overfitting and Bias-Variance Tradeoff

ओवरफिटिंग वास्तव में सांख्यिकी में एक अधिक सामान्य समस्या का हिस्सा है, जिसे Bias-Variance Tradeoff कहा जाता है। यदि हम अपने मॉडल में संभावित त्रुटियों के स्रोतों पर विचार करें, तो हम दो प्रकार की त्रुटियां देख सकते हैं:

* **Bias errors** तब होती हैं जब हमारा एल्गोरिदम ट्रेनिंग डेटा के बीच संबंध को सही ढंग से पकड़ नहीं पाता। यह इसलिए हो सकता है क्योंकि हमारा मॉडल पर्याप्त शक्तिशाली नहीं है (**underfitting**).
* **Variance errors** तब होती हैं जब मॉडल इनपुट डेटा के शोर को सार्थक संबंध के बजाय अप्रोक्सिमेट करता है (**overfitting**).

ट्रेनिंग के दौरान, bias error घटती है (जैसे-जैसे मॉडल डेटा को बेहतर समझता है), और variance error बढ़ती है। ओवरफिटिंग को रोकने के लिए ट्रेनिंग को रोकना जरूरी है - या तो मैन्युअली (जब हम ओवरफिटिंग का पता लगाते हैं) या ऑटोमेटिकली (रेगुलराइजेशन लागू करके)।

## Conclusion

इस पाठ में, आपने दो सबसे लोकप्रिय AI फ्रेमवर्क्स, TensorFlow और PyTorch के विभिन्न APIs के बीच के अंतर के बारे में जाना। साथ ही, आपने एक बहुत महत्वपूर्ण विषय, ओवरफिटिंग के बारे में भी सीखा।

## 🚀 Challenge

साथ में दिए गए नोटबुक्स में, आपको नीचे 'tasks' मिलेंगे; नोटबुक्स को पूरा करें और टास्क्स को पूरा करें।

## Review & Self Study

निम्नलिखित विषयों पर कुछ रिसर्च करें:

- TensorFlow
- PyTorch
- Overfitting

अपने आप से ये सवाल पूछें:

- TensorFlow और PyTorch में क्या अंतर है?
- ओवरफिटिंग और अंडरफिटिंग में क्या फर्क है?

## Assignment

इस लैब में, आपसे कहा गया है कि आप PyTorch या TensorFlow का उपयोग करके सिंगल- और मल्टी-लेयर्ड फुली-कनेक्टेड नेटवर्क्स के साथ दो क्लासिफिकेशन समस्याओं को हल करें।

**अस्वीकरण**:  
यह दस्तावेज़ AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) का उपयोग करके अनुवादित किया गया है। जबकि हम सटीकता के लिए प्रयासरत हैं, कृपया ध्यान दें कि स्वचालित अनुवादों में त्रुटियाँ या अशुद्धियाँ हो सकती हैं। मूल दस्तावेज़ अपनी मूल भाषा में ही अधिकारिक स्रोत माना जाना चाहिए। महत्वपूर्ण जानकारी के लिए, पेशेवर मानव अनुवाद की सलाह दी जाती है। इस अनुवाद के उपयोग से उत्पन्न किसी भी गलतफहमी या गलत व्याख्या के लिए हम जिम्मेदार नहीं हैं।