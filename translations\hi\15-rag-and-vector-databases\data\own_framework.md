<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:43:23+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "hi"
}
-->
# न्यूरल नेटवर्क्स का परिचय। मल्टी-लेयर्ड पर्सेप्ट्रॉन

पिछले भाग में, आपने सबसे सरल न्यूरल नेटवर्क मॉडल - एक-परत वाले पर्सेप्ट्रॉन के बारे में जाना, जो एक रैखिक दो-क्लास वर्गीकरण मॉडल है।

इस भाग में हम इस मॉडल को एक अधिक लचीले फ्रेमवर्क में विस्तारित करेंगे, जिससे हम:

* दो-क्लास के अलावा **मल्टी-क्लास वर्गीकरण** कर सकेंगे
* वर्गीकरण के अलावा **रिग्रेशन समस्याओं** को हल कर सकेंगे
* उन वर्गों को अलग कर सकेंगे जो रैखिक रूप से अलग नहीं किए जा सकते

हम Python में अपना खुद का मॉड्यूलर फ्रेमवर्क भी विकसित करेंगे, जो हमें विभिन्न न्यूरल नेटवर्क आर्किटेक्चर बनाने की अनुमति देगा।

## मशीन लर्निंग का औपचारिक स्वरूप

आइए मशीन लर्निंग समस्या को औपचारिक रूप से परिभाषित करें। मान लीजिए हमारे पास एक प्रशिक्षण डेटासेट **X** है जिसमें लेबल्स **Y** हैं, और हमें एक मॉडल *f* बनाना है जो सबसे सटीक भविष्यवाणियाँ करे। भविष्यवाणियों की गुणवत्ता को **लॉस फंक्शन** ℒ से मापा जाता है। निम्नलिखित लॉस फंक्शन अक्सर उपयोग किए जाते हैं:

* रिग्रेशन समस्या के लिए, जब हमें कोई संख्या भविष्यवाणी करनी होती है, तो हम **absolute error** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>| या **squared error** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup> का उपयोग कर सकते हैं
* वर्गीकरण के लिए, हम **0-1 loss** (जो मॉडल की **सटीकता** के समान है), या **logistic loss** का उपयोग करते हैं।

एक-स्तरीय पर्सेप्ट्रॉन के लिए, फ़ंक्शन *f* को एक रैखिक फ़ंक्शन *f(x)=wx+b* के रूप में परिभाषित किया गया था (यहाँ *w* वेट मैट्रिक्स है, *x* इनपुट फीचर्स का वेक्टर है, और *b* बायस वेक्टर है)। विभिन्न न्यूरल नेटवर्क आर्किटेक्चर के लिए, यह फ़ंक्शन अधिक जटिल रूप ले सकता है।

> वर्गीकरण के मामले में, अक्सर नेटवर्क आउटपुट के रूप में संबंधित वर्गों की संभावनाएँ प्राप्त करना वांछनीय होता है। किसी भी संख्या को संभावनाओं में बदलने के लिए (जैसे आउटपुट को सामान्यीकृत करने के लिए), हम अक्सर **softmax** फ़ंक्शन σ का उपयोग करते हैं, और फ़ंक्शन *f* बन जाता है *f(x)=σ(wx+b)*

ऊपर *f* की परिभाषा में, *w* और *b* को **पैरामीटर** θ=⟨*w,b*⟩ कहा जाता है। दिए गए डेटासेट ⟨**X**,**Y**⟩ के आधार पर, हम पूरे डेटासेट पर कुल त्रुटि को पैरामीटर θ के फ़ंक्शन के रूप में गणना कर सकते हैं।

> ✅ **न्यूरल नेटवर्क प्रशिक्षण का उद्देश्य पैरामीटर θ को बदलकर त्रुटि को न्यूनतम करना है**

## ग्रेडिएंट डिसेंट ऑप्टिमाइज़ेशन

फ़ंक्शन ऑप्टिमाइज़ेशन की एक प्रसिद्ध विधि है जिसे **gradient descent** कहा जाता है। विचार यह है कि हम लॉस फ़ंक्शन का पैरामीटर के सापेक्ष अवकलज (मल्टी-डायमेंशनल मामले में इसे **ग्रेडिएंट** कहते हैं) निकाल सकते हैं, और पैरामीटर को इस तरह बदल सकते हैं कि त्रुटि कम हो। इसे निम्नलिखित रूप में औपचारिक किया जा सकता है:

* पैरामीटर को कुछ यादृच्छिक मानों से आरंभ करें w<sup>(0)</sup>, b<sup>(0)</sup>
* निम्नलिखित चरण को कई बार दोहराएं:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

प्रशिक्षण के दौरान, ऑप्टिमाइज़ेशन चरण पूरे डेटासेट को ध्यान में रखकर किए जाते हैं (याद रखें कि लॉस सभी प्रशिक्षण नमूनों के योग के रूप में गणना किया जाता है)। हालांकि, वास्तविक जीवन में हम डेटासेट के छोटे हिस्से लेते हैं जिन्हें **minibatches** कहा जाता है, और डेटा के एक उपसमूह के आधार पर ग्रेडिएंट्स की गणना करते हैं। चूंकि हर बार उपसमूह यादृच्छिक रूप से लिया जाता है, इस विधि को **stochastic gradient descent** (SGD) कहा जाता है।

## मल्टी-लेयर्ड पर्सेप्ट्रॉन और बैकप्रोपेगेशन

जैसा कि ऊपर देखा गया, एक-परत वाला नेटवर्क रैखिक रूप से अलग किए जा सकने वाले वर्गों को वर्गीकृत करने में सक्षम है। एक समृद्ध मॉडल बनाने के लिए, हम नेटवर्क की कई परतों को जोड़ सकते हैं। गणितीय रूप से इसका मतलब होगा कि फ़ंक्शन *f* अधिक जटिल रूप लेगा, और इसे कई चरणों में गणना किया जाएगा:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

यहाँ, α एक **गैर-रैखिक सक्रियण फ़ंक्शन** है, σ एक softmax फ़ंक्शन है, और पैरामीटर θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*> हैं।

ग्रेडिएंट डिसेंट एल्गोरिदम समान रहेगा, लेकिन ग्रेडिएंट्स की गणना अधिक जटिल होगी। चेन डिफरेंशिएशन नियम के अनुसार, हम अवकलज इस प्रकार निकाल सकते हैं:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ चेन डिफरेंशिएशन नियम का उपयोग पैरामीटर के सापेक्ष लॉस फ़ंक्शन के अवकलज निकालने के लिए किया जाता है।

ध्यान दें कि इन सभी अभिव्यक्तियों का सबसे बायाँ भाग समान होता है, इसलिए हम लॉस फ़ंक्शन से शुरू करके और कम्प्यूटेशनल ग्राफ के "पीछे" की ओर जाकर प्रभावी ढंग से अवकलज निकाल सकते हैं। इसलिए मल्टी-लेयर्ड पर्सेप्ट्रॉन के प्रशिक्षण की विधि को **backpropagation** या 'backprop' कहा जाता है।

> TODO: image citation

> ✅ हम अपने नोटबुक उदाहरण में backprop को और अधिक विस्तार से कवर करेंगे।

## निष्कर्ष

इस पाठ में, हमने अपनी खुद की न्यूरल नेटवर्क लाइब्रेरी बनाई, और इसका उपयोग एक सरल दो-आयामी वर्गीकरण कार्य के लिए किया।

## 🚀 चुनौती

साथ में दिए गए नोटबुक में, आप मल्टी-लेयर्ड पर्सेप्ट्रॉन बनाने और प्रशिक्षित करने के लिए अपना खुद का फ्रेमवर्क लागू करेंगे। आप विस्तार से देख पाएंगे कि आधुनिक न्यूरल नेटवर्क कैसे काम करते हैं।

OwnFramework नोटबुक पर जाएं और इसे पूरा करें।

## समीक्षा और स्व-अध्ययन

Backpropagation AI और ML में एक सामान्य एल्गोरिदम है, जिसे और अधिक विस्तार से अध्ययन करना लाभकारी है।

## असाइनमेंट

इस लैब में, आप इस पाठ में बनाए गए फ्रेमवर्क का उपयोग करके MNIST हस्तलिखित अंक वर्गीकरण समस्या को हल करेंगे।

* निर्देश
* नोटबुक

**अस्वीकरण**:  
यह दस्तावेज़ AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) का उपयोग करके अनुवादित किया गया है। जबकि हम सटीकता के लिए प्रयासरत हैं, कृपया ध्यान दें कि स्वचालित अनुवादों में त्रुटियाँ या अशुद्धियाँ हो सकती हैं। मूल दस्तावेज़ अपनी मूल भाषा में ही अधिकारिक स्रोत माना जाना चाहिए। महत्वपूर्ण जानकारी के लिए, पेशेवर मानव अनुवाद की सलाह दी जाती है। इस अनुवाद के उपयोग से उत्पन्न किसी भी गलतफहमी या गलत व्याख्या के लिए हम जिम्मेदार नहीं हैं।