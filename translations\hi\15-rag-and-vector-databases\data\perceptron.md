<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T16:55:42+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "hi"
}
-->
# न्यूरल नेटवर्क्स का परिचय: पर्सेप्ट्रॉन

आधुनिक न्यूरल नेटवर्क के समान कुछ बनाने का पहला प्रयास 1957 में कॉर्नेल एरोनॉटिकल लैबोरेटरी के फ्रैंक रोसेनब्लैट ने किया था। इसे "Mark-1" नामक हार्डवेयर इम्प्लीमेंटेशन कहा जाता था, जिसे त्रिभुज, वर्ग और वृत्त जैसे प्राथमिक ज्यामितीय आकृतियों को पहचानने के लिए डिजाइन किया गया था।

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='<PERSON>'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> चित्र विकिपीडिया से

एक इनपुट इमेज को 20x20 फोटोसेल एरे द्वारा दर्शाया गया था, इसलिए न्यूरल नेटवर्क में 400 इनपुट और एक बाइनरी आउटपुट था। एक सरल नेटवर्क में एक न्यूरॉन होता था, जिसे **threshold logic unit** भी कहा जाता है। न्यूरल नेटवर्क के वेट्स पोटेंशियोमीटर की तरह काम करते थे, जिन्हें प्रशिक्षण चरण के दौरान मैन्युअल रूप से समायोजित करना पड़ता था।

> ✅ पोटेंशियोमीटर एक ऐसा उपकरण है जो उपयोगकर्ता को सर्किट के प्रतिरोध को समायोजित करने की अनुमति देता है।

> उस समय न्यूयॉर्क टाइम्स ने पर्सेप्ट्रॉन के बारे में लिखा था: *एक इलेक्ट्रॉनिक कंप्यूटर का भ्रूण जिसे [नेवी] उम्मीद करती है कि वह चल सकेगा, बात कर सकेगा, देख सकेगा, लिख सकेगा, खुद को पुनरुत्पादित कर सकेगा और अपनी मौजूदगी का एहसास कर सकेगा।*

## पर्सेप्ट्रॉन मॉडल

मान लीजिए हमारे मॉडल में N फीचर्स हैं, तब इनपुट वेक्टर का आकार N होगा। पर्सेप्ट्रॉन एक **बाइनरी क्लासिफिकेशन** मॉडल है, यानी यह इनपुट डेटा के दो वर्गों के बीच अंतर कर सकता है। हम मानेंगे कि प्रत्येक इनपुट वेक्टर x के लिए हमारे पर्सेप्ट्रॉन का आउटपुट या तो +1 होगा या -1, जो वर्ग पर निर्भर करता है। आउटपुट निम्नलिखित सूत्र से निकाला जाएगा:

y(x) = f(w<sup>T</sup>x)

जहाँ f एक स्टेप एक्टिवेशन फंक्शन है।

## पर्सेप्ट्रॉन का प्रशिक्षण

पर्सेप्ट्रॉन को प्रशिक्षित करने के लिए हमें ऐसा वेट्स वेक्टर w खोजना होता है जो अधिकांश मानों को सही वर्गीकृत करे, यानी सबसे कम **त्रुटि** (error) हो। यह त्रुटि **perceptron criterion** द्वारा इस प्रकार परिभाषित की जाती है:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

जहाँ:

* योग उन प्रशिक्षण डेटा पॉइंट्स i पर लिया जाता है जिनका वर्गीकरण गलत हुआ है
* x<sub>i</sub> इनपुट डेटा है, और t<sub>i</sub> नकारात्मक और सकारात्मक उदाहरणों के लिए क्रमशः -1 या +1 होता है।

यह मानदंड वेट्स w के एक फ़ंक्शन के रूप में माना जाता है, और हमें इसे न्यूनतम करना होता है। अक्सर, **gradient descent** नामक विधि का उपयोग किया जाता है, जिसमें हम कुछ प्रारंभिक वेट्स w<sup>(0)</sup> से शुरू करते हैं, और फिर प्रत्येक चरण में वेट्स को निम्नलिखित सूत्र के अनुसार अपडेट करते हैं:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

यहाँ η को **learning rate** कहा जाता है, और ∇E(w) E का **ग्रेडिएंट** दर्शाता है। ग्रेडिएंट की गणना के बाद, हमें मिलता है:

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

पायथन में यह एल्गोरिदम इस प्रकार दिखता है:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## निष्कर्ष

इस पाठ में, आपने पर्सेप्ट्रॉन के बारे में जाना, जो एक बाइनरी क्लासिफिकेशन मॉडल है, और इसे वेट्स वेक्टर का उपयोग करके कैसे प्रशिक्षित किया जाता है।

## 🚀 चुनौती

यदि आप अपना खुद का पर्सेप्ट्रॉन बनाना चाहते हैं, तो Microsoft Learn पर यह लैब आज़माएं जो Azure ML designer का उपयोग करती है।

## समीक्षा और स्व-अध्ययन

देखने के लिए कि हम पर्सेप्ट्रॉन का उपयोग खिलौना समस्या और वास्तविक जीवन की समस्याओं को कैसे हल करने में कर सकते हैं, और सीखना जारी रखने के लिए - Perceptron नोटबुक पर जाएं।

यहाँ पर्सेप्ट्रॉन के बारे में एक दिलचस्प लेख भी है।

## असाइनमेंट

इस पाठ में, हमने बाइनरी क्लासिफिकेशन कार्य के लिए पर्सेप्ट्रॉन को लागू किया है, और इसका उपयोग दो हस्तलिखित अंकों के बीच वर्गीकरण के लिए किया है। इस लैब में, आप पूरी तरह से अंक वर्गीकरण की समस्या को हल करने के लिए कहे गए हैं, यानी यह निर्धारित करना है कि दी गई छवि के लिए कौन सा अंक सबसे अधिक संभावित है।

* निर्देश
* नोटबुक

**अस्वीकरण**:  
यह दस्तावेज़ AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) का उपयोग करके अनुवादित किया गया है। जबकि हम सटीकता के लिए प्रयासरत हैं, कृपया ध्यान दें कि स्वचालित अनुवादों में त्रुटियाँ या अशुद्धियाँ हो सकती हैं। मूल दस्तावेज़ अपनी मूल भाषा में ही अधिकारिक स्रोत माना जाना चाहिए। महत्वपूर्ण जानकारी के लिए, पेशेवर मानव अनुवाद की सलाह दी जाती है। इस अनुवाद के उपयोग से उत्पन्न किसी भी गलतफहमी या गलत व्याख्या के लिए हम जिम्मेदार नहीं हैं।