<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:58:04+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "hi"
}
-->
# स्व-निर्देशित सीखने के लिए संसाधन

यह पाठ OpenAI और Azure OpenAI के कई मुख्य संसाधनों का उपयोग करके बनाया गया था, जो शब्दावली और ट्यूटोरियल के संदर्भ के रूप में काम करते हैं। यहाँ आपकी स्व-निर्देशित सीखने की यात्रा के लिए एक संक्षिप्त सूची दी गई है।

## 1. प्राथमिक संसाधन

| शीर्षक/लिंक                                                                                                                                                                                                                   | विवरण                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [OpenAI मॉडल के साथ फाइन-ट्यूनिंग](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | फाइन-ट्यूनिंग फ्यू-शॉट लर्निंग से बेहतर होती है क्योंकि यह प्रॉम्प्ट में फिट होने वाले उदाहरणों से कहीं अधिक उदाहरणों पर प्रशिक्षण देती है, जिससे लागत बचती है, प्रतिक्रिया की गुणवत्ता सुधरती है, और कम विलंबता वाले अनुरोध संभव होते हैं। **OpenAI से फाइन-ट्यूनिंग का अवलोकन प्राप्त करें।**                                                                                    |
| [Azure OpenAI के साथ फाइन-ट्यूनिंग क्या है?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | समझें कि **फाइन-ट्यूनिंग क्या है (संकल्पना)**, इसे क्यों देखना चाहिए (प्रेरक समस्या), किस डेटा का उपयोग करना है (प्रशिक्षण) और गुणवत्ता को कैसे मापा जाता है।                                                                                                                                                                           |
| [फाइन-ट्यूनिंग के साथ मॉडल को अनुकूलित करें](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI सेवा आपको फाइन-ट्यूनिंग के माध्यम से अपने व्यक्तिगत डेटासेट के अनुसार मॉडल को अनुकूलित करने देती है। जानें कि **फाइन-ट्यूनिंग कैसे करें (प्रक्रिया)**, Azure AI Studio, Python SDK या REST API का उपयोग करके मॉडल चुनना।                                                                                                                                |
| [LLM फाइन-ट्यूनिंग के लिए सिफारिशें](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLMs विशिष्ट डोमेन, कार्य या डेटासेट पर अच्छा प्रदर्शन नहीं कर सकते, या गलत या भ्रामक आउटपुट दे सकते हैं। **कब आपको फाइन-ट्यूनिंग पर विचार करना चाहिए** यह एक संभावित समाधान के रूप में?                                                                                                                                  |
| [सतत फाइन-ट्यूनिंग](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | सतत फाइन-ट्यूनिंग एक पुनरावृत्त प्रक्रिया है जिसमें पहले से फाइन-ट्यून किए गए मॉडल को आधार मॉडल के रूप में चुनकर नए प्रशिक्षण उदाहरणों के सेट पर **और अधिक फाइन-ट्यूनिंग** की जाती है।                                                                                                                                                     |
| [फाइन-ट्यूनिंग और फंक्शन कॉलिंग](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | फंक्शन कॉलिंग उदाहरणों के साथ अपने मॉडल को फाइन-ट्यून करना मॉडल आउटपुट को बेहतर बना सकता है, जिससे अधिक सटीक और सुसंगत प्रतिक्रियाएं मिलती हैं - समान प्रारूपित प्रतिक्रियाओं और लागत बचत के साथ।                                                                                                                                        |
| [फाइन-ट्यूनिंग मॉडल: Azure OpenAI मार्गदर्शन](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | इस तालिका को देखें ताकि आप समझ सकें कि Azure OpenAI में **कौन से मॉडल फाइन-ट्यून किए जा सकते हैं**, और ये किन क्षेत्रों में उपलब्ध हैं। यदि आवश्यक हो तो उनके टोकन सीमाएं और प्रशिक्षण डेटा की समाप्ति तिथियां भी देखें।                                                                                                                            |
| [फाइन-ट्यून करें या न करें? यही सवाल है](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | AI शो के इस 30 मिनट के **अक्टूबर 2023** एपिसोड में फाइन-ट्यूनिंग के लाभ, नुकसान और व्यावहारिक अंतर्दृष्टि पर चर्चा की गई है जो इस निर्णय में आपकी मदद करती है।                                                                                                                                                                                        |
| [LLM फाइन-ट्यूनिंग के साथ शुरुआत](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | यह **AI प्लेबुक** संसाधन आपको डेटा आवश्यकताओं, फॉर्मेटिंग, हाइपरपैरामीटर फाइन-ट्यूनिंग और चुनौतियों/सीमाओं के बारे में मार्गदर्शन करता है जिन्हें आपको जानना चाहिए।                                                                                                                                                                         |
| **ट्यूटोरियल**: [Azure OpenAI GPT3.5 Turbo फाइन-ट्यूनिंग](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | एक नमूना फाइन-ट्यूनिंग डेटासेट बनाना, फाइन-ट्यूनिंग के लिए तैयारी करना, फाइन-ट्यूनिंग जॉब बनाना, और Azure पर फाइन-ट्यून किए गए मॉडल को तैनात करना सीखें।                                                                                                                                                                                    |
| **ट्यूटोरियल**: [Azure AI Studio में Llama 2 मॉडल को फाइन-ट्यून करें](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio आपको बड़े भाषा मॉडल को आपके व्यक्तिगत डेटासेट के अनुसार अनुकूलित करने देता है _एक UI-आधारित वर्कफ़्लो के माध्यम से जो कम-कोड डेवलपर्स के लिए उपयुक्त है_। इस उदाहरण को देखें।                                                                                                                                                               |
| **ट्यूटोरियल**: [Azure पर एकल GPU के लिए Hugging Face मॉडल को फाइन-ट्यून करें](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | यह लेख बताता है कि Azure DataBricks + Hugging Face Trainer लाइब्रेरीज़ के साथ Hugging Face ट्रांसफॉर्मर्स लाइब्रेरी का उपयोग करके एकल GPU पर Hugging Face मॉडल को कैसे फाइन-ट्यून किया जाए।                                                                                                                                                |
| **प्रशिक्षण:** [Azure Machine Learning के साथ फाउंडेशन मॉडल को फाइन-ट्यून करें](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Azure Machine Learning में मॉडल कैटलॉग में कई ओपन सोर्स मॉडल उपलब्ध हैं जिन्हें आप अपने विशिष्ट कार्य के लिए फाइन-ट्यून कर सकते हैं। यह मॉड्यूल AzureML जनरेटिव AI लर्निंग पाथ का हिस्सा है।                                                                                                                                 |
| **ट्यूटोरियल:** [Azure OpenAI फाइन-ट्यूनिंग](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Microsoft Azure पर W&B का उपयोग करके GPT-3.5 या GPT-4 मॉडल की फाइन-ट्यूनिंग से मॉडल प्रदर्शन का विस्तृत ट्रैकिंग और विश्लेषण संभव होता है। यह गाइड OpenAI फाइन-ट्यूनिंग गाइड की अवधारणाओं को Azure OpenAI के लिए विशिष्ट चरणों और फीचर्स के साथ बढ़ाता है।                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. द्वितीयक संसाधन

यह अनुभाग अतिरिक्त संसाधनों को समेटता है जिन्हें एक्सप्लोर करना उपयोगी है, लेकिन जिन पर इस पाठ में समय नहीं दिया गया। इन्हें भविष्य के पाठ में या द्वितीयक असाइनमेंट विकल्प के रूप में शामिल किया जा सकता है। फिलहाल, इनका उपयोग इस विषय पर अपनी विशेषज्ञता और ज्ञान बढ़ाने के लिए करें।

| शीर्षक/लिंक                                                                                                                                                                                                            | विवरण                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI कुकबुक**: [चैट मॉडल फाइन-ट्यूनिंग के लिए डेटा तैयारी और विश्लेषण](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | यह नोटबुक चैट मॉडल की फाइन-ट्यूनिंग के लिए उपयोग किए जाने वाले चैट डेटासेट को पूर्व-प्रसंस्कृत और विश्लेषित करने का उपकरण है। यह फॉर्मेट त्रुटियों की जांच करता है, बुनियादी सांख्यिकी प्रदान करता है, और फाइन-ट्यूनिंग लागत के लिए टोकन गणना का अनुमान लगाता है। देखें: [gpt-3.5-turbo के लिए फाइन-ट्यूनिंग विधि](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)।                                                                                                                                                                   |
| **OpenAI कुकबुक**: [Qdrant के साथ Retrieval Augmented Generation (RAG) के लिए फाइन-ट्यूनिंग](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | इस नोटबुक का उद्देश्य OpenAI मॉडल को Retrieval Augmented Generation (RAG) के लिए फाइन-ट्यून करने का एक व्यापक उदाहरण प्रस्तुत करना है। हम Qdrant और Few-Shot Learning को भी एकीकृत करेंगे ताकि मॉडल प्रदर्शन बढ़े और गलत जानकारी कम हो।                                                                                                                                                                                                                                                                |
| **OpenAI कुकबुक**: [Weights & Biases के साथ GPT फाइन-ट्यूनिंग](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) AI डेवलपर प्लेटफ़ॉर्म है, जो मॉडल प्रशिक्षण, फाइन-ट्यूनिंग और फाउंडेशन मॉडल के उपयोग के लिए उपकरण प्रदान करता है। पहले उनका [OpenAI फाइन-ट्यूनिंग](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) गाइड पढ़ें, फिर कुकबुक अभ्यास करें।                                                                                                                                                                                                                  |
| **कम्युनिटी ट्यूटोरियल** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - छोटे भाषा मॉडलों के लिए फाइन-ट्यूनिंग                                                   | मिलिए [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst) से, Microsoft का नया छोटा मॉडल, जो आश्चर्यजनक रूप से शक्तिशाली और कॉम्पैक्ट है। यह ट्यूटोरियल आपको Phi-2 को फाइन-ट्यून करने के लिए मार्गदर्शन करेगा, जिसमें एक अनूठा डेटासेट बनाने और QLoRA का उपयोग करके मॉडल को फाइन-ट्यून करने का तरीका दिखाया गया है।                                                                                                                                                                       |
| **Hugging Face ट्यूटोरियल** [2024 में Hugging Face के साथ LLMs को कैसे फाइन-ट्यून करें](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | यह ब्लॉग पोस्ट आपको 2024 में Hugging Face TRL, Transformers और datasets का उपयोग करके खुले LLMs को फाइन-ट्यून करने का तरीका बताता है। आप एक उपयोग मामला परिभाषित करते हैं, विकास पर्यावरण सेटअप करते हैं, डेटासेट तैयार करते हैं, मॉडल को फाइन-ट्यून करते हैं, परीक्षण और मूल्यांकन करते हैं, फिर इसे प्रोडक्शन में तैनात करते हैं।                                                                                                                                                                                                                                                                |
| **Hugging Face**: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)                                                                                            | यह [अत्याधुनिक मशीन लर्निंग मॉडल](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst) के तेज और आसान प्रशिक्षण और तैनाती लाता है। रिपॉजिटरी में Colab-अनुकूल ट्यूटोरियल और YouTube वीडियो मार्गदर्शन हैं, फाइन-ट्यूनिंग के लिए। **हालिया [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) अपडेट को दर्शाता है**। [AutoTrain दस्तावेज़](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) पढ़ें। |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**अस्वीकरण**:  
यह दस्तावेज़ AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) का उपयोग करके अनुवादित किया गया है। जबकि हम सटीकता के लिए प्रयासरत हैं, कृपया ध्यान दें कि स्वचालित अनुवादों में त्रुटियाँ या अशुद्धियाँ हो सकती हैं। मूल दस्तावेज़ अपनी मूल भाषा में ही अधिकारिक स्रोत माना जाना चाहिए। महत्वपूर्ण जानकारी के लिए, पेशेवर मानव अनुवाद की सलाह दी जाती है। इस अनुवाद के उपयोग से उत्पन्न किसी भी गलतफहमी या गलत व्याख्या के लिए हम जिम्मेदार नहीं हैं।