<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:25:13+00:00",
  "source_file": "README.md",
  "language_code": "hi"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.hi.png)

### 21 पाठ जो आपको Generative AI एप्लिकेशन बनाने के लिए आवश्यक सब कुछ सिखाते हैं

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 बहुभाषी समर्थन

#### GitHub Action के माध्यम से समर्थित (स्वचालित और हमेशा अपडेटेड)

[French](../fr/README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](../ja/README.md) | [Korean](../ko/README.md) | [Hindi](./README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](../el/README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](../he/README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](../sk/README.md) | [Romanian](../ro/README.md) | [Bulgarian](../bg/README.md) | [Serbian (Cyrillic)](../sr/README.md) | [Croatian](../hr/README.md) | [Slovenian](../sl/README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Generative AI for Beginners (संस्करण 3) - एक कोर्स

Microsoft Cloud Advocates द्वारा तैयार 21-पाठों वाले इस व्यापक कोर्स के साथ Generative AI एप्लिकेशन बनाने की बुनियादी बातें सीखें।

## 🌱 शुरुआत कैसे करें

इस कोर्स में 21 पाठ हैं। हर पाठ अपने विषय को कवर करता है, इसलिए आप कहीं से भी शुरू कर सकते हैं!

पाठों को "Learn" (सीखें) और "Build" (बनाएं) के रूप में लेबल किया गया है। "Learn" पाठ Generative AI की अवधारणा समझाते हैं, जबकि "Build" पाठ में अवधारणा के साथ-साथ **Python** और **TypeScript** में कोड उदाहरण भी होते हैं, जब संभव हो।

.NET डेवलपर्स के लिए देखें [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

हर पाठ में "Keep Learning" सेक्शन भी होता है जिसमें अतिरिक्त सीखने के संसाधन होते हैं।

## आपको क्या चाहिए
### इस कोर्स का कोड चलाने के लिए, आप निम्न में से कोई भी उपयोग कर सकते हैं: 
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **पाठ:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **पाठ:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **पाठ:** "oai-assignment" 
   
- Python या TypeScript का बुनियादी ज्ञान मददगार होगा - \*पूर्ण शुरुआत करने वालों के लिए ये [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) और [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) कोर्स देखें
- एक GitHub अकाउंट ताकि आप [इस पूरे रिपॉजिटरी को फोर्क कर सकें](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) अपने GitHub अकाउंट में

हमने आपके विकास पर्यावरण को सेटअप करने में मदद के लिए एक **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** पाठ बनाया है।

बाद में इसे आसानी से खोजने के लिए [इस रिपॉजिटरी को स्टार (🌟) करना न भूलें](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst)।

## 🧠 तैनाती के लिए तैयार?

अगर आप और अधिक उन्नत कोड उदाहरण ढूंढ रहे हैं, तो हमारे [Generative AI कोड नमूनों का संग्रह](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) देखें, जो दोनों **Python** और **TypeScript** में उपलब्ध हैं।

## 🗣️ अन्य शिक्षार्थियों से मिलें, सहायता पाएं

इस कोर्स को कर रहे अन्य शिक्षार्थियों से मिलने और नेटवर्क बनाने के लिए हमारे [आधिकारिक Azure AI Foundry Discord सर्वर](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) में शामिल हों और सहायता प्राप्त करें।

प्रश्न पूछें या उत्पाद प्रतिक्रिया साझा करें हमारे [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) पर GitHub में।

## 🚀 स्टार्टअप बना रहे हैं?

[Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) के लिए साइन अप करें और **मुफ्त OpenAI क्रेडिट्स** और Azure OpenAI Services के माध्यम से OpenAI मॉडल तक पहुँचने के लिए **Azure क्रेडिट्स में $150k तक** प्राप्त करें।

## 🙏 मदद करना चाहते हैं?

क्या आपके पास सुझाव हैं या आपने कोई वर्तनी या कोड त्रुटि पाई है? [इश्यू उठाएं](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) या [पुल रिक्वेस्ट बनाएं](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 हर पाठ में शामिल है:

- विषय का एक संक्षिप्त वीडियो परिचय
- README में लिखित पाठ
- Azure OpenAI और OpenAI API का समर्थन करने वाले Python और TypeScript कोड उदाहरण
- सीखने को जारी रखने के लिए अतिरिक्त संसाधनों के लिंक

## 🗃️ पाठ

| #   | **पाठ लिंक**                                                                                                                              | **विवरण**                                                                                 | **वीडियो**                                                                   | **अतिरिक्त सीखने के संसाधन**                                                     |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **सीखें:** अपने विकास पर्यावरण को कैसे सेटअप करें                                        | वीडियो जल्द आ रहा है                                                        | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)   |
| 01  | [Introduction to Generative AI and LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                              | **सीखें:** Generative AI क्या है और Large Language Models (LLMs) कैसे काम करते हैं       | [वीडियो](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)   |
| 02  | [Exploring and comparing different LLMs](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)             | **सीखें:** अपने उपयोग के लिए सही मॉडल कैसे चुनें                                        | [वीडियो](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)   |
| 03  | [Using Generative AI Responsibly](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                           | **सीखें:** Generative AI एप्लिकेशन जिम्मेदारी से कैसे बनाएं                              | [वीडियो](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)   |
| 04  | [Understanding Prompt Engineering Fundamentals](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)             | **सीखें:** प्रॉम्प्ट इंजीनियरिंग के बेसिक्स पर व्यावहारिक अभ्यास                         | [वीडियो](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)   |
| 05  | [Creating Advanced Prompts](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **सीखें:** प्रॉम्प्ट इंजीनियरिंग तकनीकों को कैसे लागू करें जो आपके प्रॉम्प्ट के परिणाम बेहतर बनाएं | [वीडियो](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)   |
| 06  | [टेक्स्ट जनरेशन एप्लिकेशन बनाना](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **बनाएं:** Azure OpenAI / OpenAI API का उपयोग करके एक टेक्स्ट जनरेशन ऐप                                | [वीडियो](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [चैट एप्लिकेशन बनाना](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **बनाएं:** चैट एप्लिकेशन को कुशलतापूर्वक बनाने और एकीकृत करने की तकनीकें               | [वीडियो](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [सर्च ऐप्स और वेक्टर डेटाबेस बनाना](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **बनाएं:** एक सर्च एप्लिकेशन जो डेटा खोजने के लिए Embeddings का उपयोग करता है                        | [वीडियो](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [इमेज जनरेशन एप्लिकेशन बनाना](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **बनाएं:** एक इमेज जनरेशन एप्लिकेशन                                                       | [वीडियो](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [लो कोड AI एप्लिकेशन बनाना](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **बनाएं:** Low Code टूल्स का उपयोग करके एक Generative AI एप्लिकेशन                                     | [वीडियो](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [फंक्शन कॉलिंग के साथ बाहरी एप्लिकेशन इंटीग्रेट करना](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **बनाएं:** फंक्शन कॉलिंग क्या है और एप्लिकेशन में इसके उपयोग के मामले                          | [वीडियो](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [AI एप्लिकेशन के लिए UX डिजाइन करना](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **सीखें:** Generative AI एप्लिकेशन विकसित करते समय UX डिजाइन सिद्धांतों को कैसे लागू करें         | [वीडियो](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [अपने Generative AI एप्लिकेशन को सुरक्षित बनाना](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **सीखें:** AI सिस्टम्स के खतरे और जोखिम, और इन्हें सुरक्षित करने के तरीके             | [वीडियो](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Generative AI एप्लिकेशन का जीवनचक्र](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **सीखें:** LLM जीवनचक्र और LLMOps को प्रबंधित करने के लिए टूल्स और मेट्रिक्स                         | [वीडियो](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) और वेक्टर डेटाबेस](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **बनाएं:** RAG फ्रेमवर्क का उपयोग करके वेक्टर डेटाबेस से embeddings पुनः प्राप्त करने वाला एप्लिकेशन  | [वीडियो](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [ओपन सोर्स मॉडल और Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **बनाएं:** Hugging Face पर उपलब्ध ओपन सोर्स मॉडल का उपयोग करने वाला एप्लिकेशन                    | [वीडियो](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI एजेंट्स](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **बनाएं:** AI एजेंट फ्रेमवर्क का उपयोग करने वाला एप्लिकेशन                                           | [वीडियो](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [LLMs का फाइन-ट्यूनिंग](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **सीखें:** LLMs के फाइन-ट्यूनिंग क्या है, क्यों और कैसे किया जाता है                                            | [वीडियो](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [SLMs के साथ बनाना](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **सीखें:** Small Language Models के साथ बनाने के फायदे                                            | वीडियो जल्द आ रहा है | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Mistral मॉडल्स के साथ बनाना](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **सीखें:** Mistral फैमिली मॉडल्स की विशेषताएं और अंतर                                           | वीडियो जल्द आ रहा है | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Meta मॉडल्स के साथ बनाना](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **सीखें:** Meta फैमिली मॉडल्स की विशेषताएं और अंतर                                           | वीडियो जल्द आ रहा है | [और जानें](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 विशेष धन्यवाद

[**John Aziz**](https://www.linkedin.com/in/john0isaac/) को सभी GitHub Actions और वर्कफ़्लोज़ बनाने के लिए विशेष धन्यवाद

[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) को प्रत्येक पाठ में महत्वपूर्ण योगदान देने के लिए, जिससे सीखने और कोड अनुभव में सुधार हुआ।

## 🎒 अन्य कोर्स

हमारी टीम अन्य कोर्स भी बनाती है! देखें:

- [**नया** Model Context Protocol for Beginners](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI Agents for Beginners](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Generative AI for Beginners using .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Generative AI for Beginners using JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML for Beginners](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science for Beginners](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI for Beginners](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Cybersecurity for Beginners](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Web Dev for Beginners](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT for Beginners](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR Development for Beginners](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Mastering GitHub Copilot for AI Paired Programming](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Mastering GitHub Copilot for C#/.NET Developers](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Choose Your Own Copilot Adventure](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**अस्वीकरण**:  
यह दस्तावेज़ AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) का उपयोग करके अनुवादित किया गया है। जबकि हम सटीकता के लिए प्रयासरत हैं, कृपया ध्यान दें कि स्वचालित अनुवादों में त्रुटियाँ या अशुद्धियाँ हो सकती हैं। मूल दस्तावेज़ अपनी मूल भाषा में ही अधिकारिक स्रोत माना जाना चाहिए। महत्वपूर्ण जानकारी के लिए, पेशेवर मानव अनुवाद की सलाह दी जाती है। इस अनुवाद के उपयोग से उत्पन्न किसी भी गलतफहमी या गलत व्याख्या के लिए हम जिम्मेदार नहीं हैं।