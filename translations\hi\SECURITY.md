<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:52:09+00:00",
  "source_file": "SECURITY.md",
  "language_code": "hi"
}
-->
## Security

Microsoft हमारे सॉफ़्टवेयर उत्पादों और सेवाओं की सुरक्षा को गंभीरता से लेता है, जिसमें हमारे GitHub संगठनों के माध्यम से प्रबंधित सभी स्रोत कोड रिपॉजिटरी शामिल हैं, जिनमें [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin), और [हमारे GitHub संगठन](https://opensource.microsoft.com/) शामिल हैं।

यदि आपको किसी Microsoft-स्वामित्व वाले रिपॉजिटरी में कोई सुरक्षा कमजोरि मिली है जो [Microsoft की सुरक्षा कमजोरि की परिभाषा](https://aka.ms/opensource/security/definition) के अनुरूप है, तो कृपया नीचे दिए अनुसार हमें इसकी रिपोर्ट करें।

## Reporting Security Issues

**कृपया सुरक्षा कमजोरियों की रिपोर्ट सार्वजनिक GitHub इश्यू के माध्यम से न करें।**

इसके बजाय, कृपया Microsoft Security Response Center (MSRC) को [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report) पर रिपोर्ट करें।

यदि आप लॉगिन किए बिना सबमिट करना चाहते हैं, तो ईमेल भेजें [<EMAIL>](mailto:<EMAIL>)। यदि संभव हो, तो हमारे PGP कुंजी के साथ अपना संदेश एन्क्रिप्ट करें; कृपया इसे [Microsoft Security Response Center PGP Key पेज](https://aka.ms/opensource/security/pgpkey) से डाउनलोड करें।

आपको 24 घंटे के भीतर प्रतिक्रिया प्राप्त होनी चाहिए। यदि किसी कारणवश आपको प्रतिक्रिया नहीं मिलती है, तो कृपया ईमेल के माध्यम से फॉलो-अप करें ताकि हम आपकी मूल संदेश प्राप्त कर सकें। अतिरिक्त जानकारी [microsoft.com/msrc](https://aka.ms/opensource/security/msrc) पर उपलब्ध है।

कृपया नीचे दी गई आवश्यक जानकारी (जितना संभव हो) शामिल करें ताकि हम संभावित समस्या की प्रकृति और दायरे को बेहतर समझ सकें:

  * समस्या का प्रकार (जैसे बफर ओवरफ्लो, SQL इंजेक्शन, क्रॉस-साइट स्क्रिप्टिंग, आदि)
  * समस्या के प्रकट होने से संबंधित स्रोत फ़ाइल(ओं) के पूर्ण पथ
  * प्रभावित स्रोत कोड का स्थान (टैग/ब्रांच/कमिट या सीधे URL)
  * समस्या को पुनः उत्पन्न करने के लिए आवश्यक कोई विशेष कॉन्फ़िगरेशन
  * समस्या को पुनः उत्पन्न करने के लिए चरण-दर-चरण निर्देश
  * प्रूफ-ऑफ-कॉन्सेप्ट या एक्सप्लॉइट कोड (यदि संभव हो)
  * समस्या का प्रभाव, जिसमें यह भी शामिल है कि कोई हमलावर इसे कैसे भुनाकर नुकसान पहुंचा सकता है

यह जानकारी हमें आपकी रिपोर्ट को तेजी से प्राथमिकता देने में मदद करेगी।

यदि आप बग बाउंटी के लिए रिपोर्ट कर रहे हैं, तो अधिक पूर्ण रिपोर्ट उच्च बाउंटी पुरस्कार में योगदान कर सकती हैं। कृपया हमारे [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) पेज पर हमारे सक्रिय कार्यक्रमों के बारे में अधिक जानकारी प्राप्त करें।

## Preferred Languages

हम सभी संचार अंग्रेज़ी में होने को प्राथमिकता देते हैं।

## Policy

Microsoft [Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd) के सिद्धांत का पालन करता है।

**अस्वीकरण**:  
यह दस्तावेज़ AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) का उपयोग करके अनुवादित किया गया है। जबकि हम सटीकता के लिए प्रयासरत हैं, कृपया ध्यान दें कि स्वचालित अनुवादों में त्रुटियाँ या अशुद्धियाँ हो सकती हैं। मूल दस्तावेज़ अपनी मूल भाषा में ही अधिकारिक स्रोत माना जाना चाहिए। महत्वपूर्ण जानकारी के लिए, पेशेवर मानव अनुवाद की सलाह दी जाती है। इस अनुवाद के उपयोग से उत्पन्न किसी भी गलतफहमी या गलत व्याख्या के लिए हम जिम्मेदार नहीं हैं।