<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "ec385b41ee50579025d50cc03bfb3a25",
  "translation_date": "2025-07-09T14:51:25+00:00",
  "source_file": "12-designing-ux-for-ai-applications/README.md",
  "language_code": "hk"
}
-->
# 為 AI 應用程式設計用戶體驗

[![為 AI 應用程式設計用戶體驗](../../../translated_images/12-lesson-banner.c53c3c7c802e8f563953ce388f6a987ca493472c724d924b060be470951c53c8.hk.png)](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst)

> _(點擊上方圖片觀看本課程影片)_

用戶體驗是開發應用程式中非常重要的一環。用戶需要能夠有效率地使用你的應用程式來完成任務。效率固然重要，但你同時也需要設計出人人都能使用的應用程式，讓它們具備「無障礙」特性。本章將聚焦於這方面，希望你最終能設計出既實用又受歡迎的應用程式。

## 介紹

用戶體驗是指用戶如何與特定產品或服務互動和使用，無論是系統、工具或設計。在開發 AI 應用程式時，開發者不僅要確保用戶體驗有效，還要兼顧倫理。本課程將介紹如何打造符合用戶需求的人工智能（AI）應用程式。

本課程將涵蓋以下主題：

- 用戶體驗介紹與理解用戶需求
- 為信任與透明度設計 AI 應用程式
- 為協作與回饋設計 AI 應用程式

## 學習目標

完成本課程後，你將能夠：

- 了解如何打造符合用戶需求的 AI 應用程式。
- 設計促進信任與協作的 AI 應用程式。

### 先備知識

花點時間閱讀更多關於[用戶體驗與設計思維](https://learn.microsoft.com/training/modules/ux-design?WT.mc_id=academic-105485-koreyst)的內容。

## 用戶體驗介紹與理解用戶需求

在我們虛構的教育新創公司中，有兩類主要用戶：教師和學生。這兩類用戶各有獨特需求。以用戶為中心的設計會優先考量用戶，確保產品對目標用戶有意義且有幫助。

應用程式應該是**有用、可靠、無障礙且令人愉悅**，才能提供良好的用戶體驗。

### 易用性

有用代表應用程式的功能符合其預期目的，例如自動化評分流程或生成複習用的抽認卡。自動化評分的應用程式應能根據預設標準準確且有效率地為學生作業打分。類似地，生成複習抽認卡的應用程式應能根據資料創造相關且多樣的問題。

### 可靠性

可靠代表應用程式能持續且無誤地執行任務。然而，AI 和人類一樣並非完美，可能會出錯。應用程式可能會遇到錯誤或意外狀況，需要人工介入或修正。你會如何處理錯誤？本課程最後一節將介紹如何設計 AI 系統與應用程式以促進協作與回饋。

### 無障礙

無障礙代表將用戶體驗延伸至各種能力的用戶，包括身心障礙者，確保沒有人被排除在外。遵循無障礙指引與原則，AI 解決方案能變得更具包容性、易用且對所有用戶有益。

### 愉悅感

愉悅感代表應用程式使用起來令人愉快。吸引人的用戶體驗能正面影響用戶，鼓勵他們持續使用應用程式，並提升業務收益。

![說明 AI 中用戶體驗考量的圖片](../../../translated_images/uxinai.d5b4ed690f5cefff0c53ffcc01b480cdc1828402e1fdbc980490013a3c50935a.hk.png)

並非所有挑戰都能靠 AI 解決。AI 是用來強化你的用戶體驗，無論是自動化手動任務，還是個人化用戶體驗。

## 為信任與透明度設計 AI 應用程式

建立信任在設計 AI 應用程式時至關重要。信任讓用戶有信心應用程式能完成工作、持續交付結果，且結果符合用戶需求。這方面的風險包括不信任與過度信任。不信任是指用戶對 AI 系統幾乎沒有信心，導致拒絕使用你的應用程式。過度信任則是用戶高估 AI 系統能力，過度依賴 AI。例如，過度信任自動評分系統可能導致教師不再仔細審核部分試卷，結果可能造成學生評分不公或錯失回饋與改進的機會。

確保信任成為設計核心的兩個方法是可解釋性與控制權。

### 可解釋性

當 AI 幫助做出決策，例如傳授知識給未來世代時，教師和家長理解 AI 如何做決策非常重要。這就是可解釋性——了解 AI 應用程式如何做出決策。設計可解釋性包括加入 AI 應用程式能做什麼的範例細節。例如，系統可以用「使用 AI 幫你整理筆記，方便複習」來取代「開始使用 AI 教師」。

![展示 AI 應用程式中可解釋性的應用程式首頁](../../../translated_images/explanability-in-ai.134426a96b498fbfdc80c75ae0090aedc0fc97424ae0734fccf7fb00a59a20d9.hk.png)

另一個例子是 AI 如何使用用戶和個人資料。例如，擁有學生角色的用戶可能會有角色限制。AI 可能無法直接揭示答案，但能引導用戶思考如何解決問題。

![根據角色回答問題的 AI](../../../translated_images/solving-questions.b7dea1604de0cbd2e9c5fa00b1a68a0ed77178a035b94b9213196b9d125d0be8.hk.png)

可解釋性的另一個關鍵是簡化說明。學生和教師可能不是 AI 專家，因此應用程式能做什麼或不能做什麼的說明應該簡單易懂。

![簡化 AI 功能說明](../../../translated_images/simplified-explanations.4679508a406c3621fa22bad4673e717fbff02f8b8d58afcab8cb6f1aa893a82f.hk.png)

### 控制權

生成式 AI 促成 AI 與用戶的協作，例如用戶可以修改提示詞以獲得不同結果。此外，產生結果後，用戶應能修改結果，讓他們感受到掌控感。例如，使用 Bing 時，你可以根據格式、語氣和長度調整提示詞，還能對輸出結果進行修改，如下圖所示：

![Bing 搜尋結果，提供修改提示詞和輸出結果的選項](../../../translated_images/bing1.293ae8527dbe2789b675c8591c9fb3cb1aa2ada75c2877f9aa9edc059f7a8b1c.hk.png)

Bing 另一項讓用戶掌控應用程式的功能是可選擇是否讓 AI 使用其資料。對於學校應用程式，學生可能想使用自己的筆記以及教師的資源作為複習材料。

![Bing 搜尋結果，提供修改提示詞和輸出結果的選項](../../../translated_images/bing2.309f4845528a88c28c1c9739fb61d91fd993dc35ebe6fc92c66791fb04fceb4d.hk.png)

> 設計 AI 應用程式時，刻意性是關鍵，確保用戶不會過度信任並對 AI 能力抱持不切實際的期望。一種做法是在提示詞與結果之間製造摩擦，提醒用戶這是 AI，而非真人。

## 為協作與回饋設計 AI 應用程式

如前所述，生成式 AI 促成用戶與 AI 的協作。大多數互動是用戶輸入提示詞，AI 產生結果。如果結果錯誤，應用程式如何處理？AI 會怪罪用戶，還是花時間解釋錯誤？

AI 應用程式應設計成能接收並給予回饋。這不僅有助於 AI 系統改進，也能建立用戶信任。設計中應包含回饋迴路，例如對結果給予簡單的讚或踩。

另一種做法是清楚溝通系統的能力與限制。當用戶提出超出 AI 能力範圍的請求時，應有相應的處理方式，如下圖所示。

![提供回饋與錯誤處理](../../../translated_images/feedback-loops.7955c134429a94663443ad74d59044f8dc4ce354577f5b79b4bd2533f2cafc6f.hk.png)

系統錯誤在應用程式中很常見，例如用戶可能需要 AI 範圍外的資訊，或應用程式限制用戶能生成的問題/科目數量。例如，一個只訓練於歷史和數學資料的 AI 應用程式，可能無法處理地理相關問題。為了緩解這種情況，AI 系統可以回應：「抱歉，我們的產品只訓練於以下科目……，無法回答您提出的問題。」

AI 應用程式並非完美，必然會犯錯。設計應用程式時，應確保有空間讓用戶提供回饋，並以簡單且易於理解的方式處理錯誤。

## 作業

挑選你目前開發的任何 AI 應用程式，考慮在你的應用中實施以下步驟：

- **愉悅感：** 思考如何讓你的應用程式更令人愉快。你是否在各處加入說明？是否鼓勵用戶探索？你的錯誤訊息用詞如何？

- **易用性：** 建立網頁應用程式。確保你的應用程式能用滑鼠和鍵盤操作。

- **信任與透明度：** 不要完全信任 AI 及其輸出，思考如何加入人工審核流程來驗證結果。同時考慮並實施其他促進信任與透明度的方法。

- **控制權：** 讓用戶掌控他們提供給應用程式的資料。實作用戶可選擇加入或退出資料收集的機制。

## 繼續學習！

完成本課程後，請參考我們的[生成式 AI 學習合集](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)，持續提升你的生成式 AI 知識！

接著前往第 13 課，我們將探討如何[保障 AI 應用程式安全](../13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)！

**免責聲明**：  
本文件由 AI 翻譯服務 [Co-op Translator](https://github.com/Azure/co-op-translator) 進行翻譯。雖然我們致力於確保準確性，但請注意自動翻譯可能包含錯誤或不準確之處。原始文件的母語版本應被視為權威來源。對於重要資訊，建議採用專業人工翻譯。我們不對因使用本翻譯而引起的任何誤解或誤釋承擔責任。