<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:27:10+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "hk"
}
-->
# Neural Network Frameworks

正如我們已經學過的，要有效地訓練神經網絡，我們需要做兩件事：

* 操作張量，例如乘法、加法，以及計算一些函數如 sigmoid 或 softmax
* 計算所有表達式的梯度，以便進行梯度下降優化

雖然 `numpy` 庫可以完成第一部分，但我們需要某種機制來計算梯度。在我們之前開發的框架中，我們必須手動在 `backward` 方法中編寫所有導數函數，該方法負責反向傳播。理想情況下，框架應該能讓我們計算*任何定義的表達式*的梯度。

另一個重要的事情是能夠在 GPU 或其他專用計算單元（如 TPU）上執行計算。深度神經網絡訓練需要*大量*計算，能夠在 GPU 上並行化這些計算非常重要。

> ✅ 「並行化」一詞指的是將計算分配到多個設備上。

目前，最流行的兩個神經網絡框架是 TensorFlow 和 PyTorch。兩者都提供了在 CPU 和 GPU 上操作張量的低階 API。在低階 API 之上，還有更高階的 API，分別是 Keras 和 PyTorch Lightning。

低階 API | TensorFlow | PyTorch
--------------|-------------------------------------|--------------------------------
高階 API | Keras | PyTorch Lightning

兩個框架的**低階 API**允許你構建所謂的**計算圖**。這個圖定義了如何用給定的輸入參數計算輸出（通常是損失函數），並且如果有 GPU 可用，可以推送到 GPU 上計算。框架中有函數可以對計算圖求導並計算梯度，這些梯度可以用來優化模型參數。

**高階 API**則將神經網絡視為**一系列層**，使構建大多數神經網絡變得更簡單。訓練模型通常只需準備數據，然後調用 `fit` 函數完成訓練。

高階 API 讓你能快速構建典型的神經網絡，而不必擔心太多細節。與此同時，低階 API 提供了對訓練過程更多的控制，因此在研究新神經網絡架構時被廣泛使用。

同時也要明白，你可以同時使用兩種 API，例如，你可以用低階 API 開發自己的網絡層架構，然後在用高階 API 構建和訓練的更大網絡中使用它。或者你可以用高階 API 定義一個層序列的網絡，然後用自己的低階訓練循環來進行優化。兩種 API 使用相同的基本概念，並且設計上能很好地協同工作。

## Learning

在本課程中，我們提供了 PyTorch 和 TensorFlow 兩個框架的大部分內容。你可以選擇自己喜歡的框架，只學習相應的筆記本。如果不確定選哪個框架，可以在網上查閱關於 **PyTorch vs. TensorFlow** 的討論，也可以兩個都試試以加深理解。

在可能的情況下，我們會使用高階 API 以簡化學習過程。但我們認為從基礎理解神經網絡的運作很重要，因此一開始會從低階 API 和張量開始學習。不過，如果你想快速上手，不想花太多時間在細節上，也可以跳過這部分，直接學高階 API 的筆記本。

## ✍️ Exercises: Frameworks

繼續學習以下筆記本：

低階 API | TensorFlow+Keras 筆記本 | PyTorch
--------------|-------------------------------------|--------------------------------
高階 API | Keras | *PyTorch Lightning*

掌握框架後，我們來回顧一下過擬合的概念。

# Overfitting

過擬合是機器學習中非常重要的概念，理解它至關重要！

考慮以下用 5 個點（圖中以 `x` 表示）進行擬合的問題：

!linear | overfit
-------------------------|--------------------------
**線性模型，2 個參數** | **非線性模型，7 個參數**
訓練誤差 = 5.3 | 訓練誤差 = 0
驗證誤差 = 5.1 | 驗證誤差 = 20

* 左邊，我們看到一條不錯的直線擬合。由於參數數量合適，模型能正確捕捉點的分佈趨勢。
* 右邊，模型過於強大。因為只有 5 個點，但模型有 7 個參數，它可以調整到通過所有點，使訓練誤差為 0。但這阻止了模型理解數據背後的正確模式，因此驗證誤差非常高。

在模型的複雜度（參數數量）和訓練樣本數量之間取得正確平衡非常重要。

## Why overfitting occurs

  * 訓練數據不足
  * 模型過於強大
  * 輸入數據中噪聲過多

## How to detect overfitting

如上圖所示，過擬合可以通過非常低的訓練誤差和很高的驗證誤差來檢測。通常在訓練過程中，訓練和驗證誤差都會下降，但在某個時候，驗證誤差可能停止下降並開始上升。這是過擬合的信號，也表明我們應該停止訓練（或者至少保存當前模型快照）。

overfitting

## How to prevent overfitting

如果發現過擬合，可以採取以下措施：

 * 增加訓練數據量
 * 降低模型複雜度
 * 使用正則化技術，例如 Dropout，我們稍後會介紹。

## Overfitting and Bias-Variance Tradeoff

過擬合其實是統計學中一個更通用問題——偏差-方差權衡（Bias-Variance Tradeoff）的具體表現。如果考慮模型中可能的誤差來源，可以分為兩類：

* **偏差誤差** 是由於算法無法正確捕捉訓練數據間的關係造成的，這可能是因為模型能力不足（**欠擬合**）。
* **方差誤差** 是模型擬合了輸入數據中的噪聲而非有意義的關係（**過擬合**）。

在訓練過程中，偏差誤差會下降（模型學會擬合數據），而方差誤差會上升。重要的是要在適當時候停止訓練——無論是手動（檢測到過擬合時）還是自動（通過引入正則化）——以防止過擬合。

## Conclusion

本課中，你了解了兩個最流行 AI 框架 TensorFlow 和 PyTorch 的不同 API，還學到了非常重要的主題——過擬合。

## 🚀 Challenge

在配套的筆記本中，你會在底部看到「任務」；請完成這些任務以鞏固學習。

## Review & Self Study

請自行研究以下主題：

- TensorFlow
- PyTorch
- 過擬合

思考以下問題：

- TensorFlow 和 PyTorch 有什麼區別？
- 過擬合和欠擬合有什麼不同？

## Assignment

本實驗要求你使用 PyTorch 或 TensorFlow，利用單層和多層全連接網絡解決兩個分類問題。

**免責聲明**：  
本文件由 AI 翻譯服務 [Co-op Translator](https://github.com/Azure/co-op-translator) 進行翻譯。雖然我們致力於確保準確性，但請注意自動翻譯可能包含錯誤或不準確之處。原始文件的母語版本應被視為權威來源。對於重要資訊，建議採用專業人工翻譯。我們不對因使用本翻譯而引起的任何誤解或誤釋承擔責任。