<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:56:23+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "hk"
}
-->
# 自學資源

本課程參考了 OpenAI 及 Azure OpenAI 的多個核心資源，作為術語和教學的依據。以下是非完整列表，供你自學之用。

## 1. 主要資源

| 標題/連結                                                                                                                                                                                                                   | 說明                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [使用 OpenAI 模型進行微調](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | 微調透過訓練更多範例，超越少量示例學習的限制，幫助你節省成本、提升回應品質，並降低延遲。**了解 OpenAI 的微調概覽。**                                                                                    |
| [什麼是 Azure OpenAI 的微調？](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | 了解**微調的概念**、為何要使用微調（動機問題）、應該使用哪些資料（訓練）以及如何評估品質。                                                                                                                                                                           |
| [使用微調自訂模型](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI 服務讓你能用微調方式，根據個人資料集調整模型。學習**如何微調（流程）**，並使用 Azure AI Studio、Python SDK 或 REST API 選擇模型。                                                                                                                                |
| [LLM 微調建議](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | 大型語言模型（LLM）在特定領域、任務或資料集上可能表現不佳，或產生不準確或誤導的結果。**何時應考慮微調**作為解決方案？                                                                                                                                  |
| [持續微調](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | 持續微調是指以已微調的模型作為基礎，**在新的訓練範例上進一步微調**的迭代過程。                                                                                                                                                     |
| [微調與函數呼叫](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | 透過**帶有函數呼叫範例的微調**，可提升模型輸出準確度與一致性，產生格式相似的回應，並節省成本。                                                                                                                                        |
| [微調模型：Azure OpenAI 指南](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | 查閱此表格了解**哪些模型可在 Azure OpenAI 進行微調**，以及它們在哪些區域可用。必要時查看其 token 限制和訓練資料過期日期。                                                                                                                            |
| [要不要微調？這是個問題](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | 這集 2023 年 10 月的 AI Show（30 分鐘）討論微調的優缺點及實務見解，幫助你做出決定。                                                                                                                                                                                        |
| [LLM 微調入門](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | 這份**AI 操作手冊**帶你了解資料需求、格式化、超參數微調，以及你應該知道的挑戰與限制。                                                                                                                                                                         |
| **教學**：[Azure OpenAI GPT3.5 Turbo 微調](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | 學習如何建立範例微調資料集、準備微調、建立微調工作，並在 Azure 上部署微調後的模型。                                                                                                                                                                                    |
| **教學**：[在 Azure AI Studio 微調 Llama 2 模型](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio 讓你用 UI 流程（適合低代碼開發者）根據個人資料集調整大型語言模型。參考此範例。                                                                                                                                                               |
| **教學**：[在 Azure 上用單 GPU 微調 Hugging Face 模型](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | 本文說明如何使用 Hugging Face transformers 庫，在 Azure DataBricks 和 Hugging Face Trainer 庫上，利用單 GPU 微調 Hugging Face 模型。                                                                                                                                                |
| **訓練課程**：[用 Azure Machine Learning 微調基礎模型](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Azure Machine Learning 的模型目錄提供多款開源模型，供你針對特定任務微調。此課程來自 [AzureML 生成式 AI 學習路徑](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)。 |
| **教學**：[Azure OpenAI 微調](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | 使用 W&B 在 Microsoft Azure 上微調 GPT-3.5 或 GPT-4 模型，可詳細追蹤和分析模型表現。本指南擴展了 OpenAI 微調指南的概念，並提供 Azure OpenAI 的具體步驟和功能。                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. 次要資源

本節收錄了值得探索的額外資源，但本課程未能涵蓋。未來課程或次要作業可能會涉及。現階段可用來增進你對此主題的專業知識。

| 標題/連結                                                                                                                                                                                                            | 說明                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**：[聊天模型微調的資料準備與分析](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | 此筆記本用於預處理和分析用於聊天模型微調的資料集。它會檢查格式錯誤、提供基本統計數據，並估算微調所需的 token 數量及成本。參考：[gpt-3.5-turbo 微調方法](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)。                                                                                                                                                                   |
| **OpenAI Cookbook**：[用 Qdrant 進行檢索增強生成（RAG）微調](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | 本筆記本示範如何微調 OpenAI 模型以實現檢索增強生成（RAG）。同時整合 Qdrant 和少量示例學習，提升模型效能並減少虛構內容。                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**：[使用 Weights & Biases 微調 GPT](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) 是 AI 開發平台，提供訓練、微調模型及利用基礎模型的工具。先閱讀他們的 [OpenAI 微調指南](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst)，再嘗試 Cookbook 練習。                                                                                                                                                                                                                  |
| **社群教學** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - 小型語言模型微調                                                   | 認識 [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst)，微軟新推出的小型模型，體積小但效能強大。此教學帶你了解如何建立獨特資料集並用 QLoRA 微調 Phi-2。                                                                                                                                                                       |
| **Hugging Face 教學** [2024 年如何用 Hugging Face 微調 LLM](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | 本文介紹如何用 Hugging Face TRL、Transformers 和 datasets 微調開源大型語言模型。涵蓋用例定義、開發環境設置、資料集準備、模型微調、測試評估及部署。                                                                                                                                                                                                                                                                |
| **Hugging Face**：[AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)                                                                                            | 提供更快速且簡易的[尖端機器學習模型](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst)訓練與部署。此倉庫包含適合 Colab 的教學和 YouTube 影片指導，方便微調。**反映近期的[local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)更新**。詳見 [AutoTrain 文件](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst)。 |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**免責聲明**：  
本文件乃使用 AI 翻譯服務 [Co-op Translator](https://github.com/Azure/co-op-translator) 進行翻譯。雖然我們致力於確保準確性，但請注意，自動翻譯可能包含錯誤或不準確之處。原始文件的母語版本應被視為權威來源。對於重要資訊，建議採用專業人工翻譯。我們不對因使用本翻譯而引起的任何誤解或誤釋承擔責任。