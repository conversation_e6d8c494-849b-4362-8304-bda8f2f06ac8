<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:09:48+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "hr"
}
-->
# Resursi za samostalno učenje

Lekcija je izrađena koristeći niz ključnih resursa iz OpenAI i Azure OpenAI kao referenci za terminologiju i tutorijale. Evo nepotpune liste za vaše samostalno učenje.

## 1. Primarni resursi

| Naslov/Link                                                                                                                                                                                                                 | Opis                                                                                                                                                                                                                                                                                                                                                                                        |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning poboljšava few-shot učenje treniranjem na mnogo većem broju primjera nego što stane u prompt, štedi troškove, poboljšava kvalitetu odgovora i omogućuje brže zahtjeve. **Pregledajte fine-tuning iz OpenAI izvora.**                                                                                                                                                              |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                     | Razumite **što je fine-tuning (pojam)**, zašto ga trebate razmotriti (motivacijski problem), koje podatke koristiti (trening) i kako mjeriti kvalitetu                                                                                                                                                                                                                                      |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)   | Azure OpenAI Service omogućuje prilagodbu modela vašim osobnim skupovima podataka pomoću fine-tuninga. Naučite **kako provesti fine-tuning (proces)** odabirom modela koristeći Azure AI Studio, Python SDK ili REST API.                                                                                                                                                                      |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM modeli možda neće dobro raditi na specifičnim domenama, zadacima ili skupovima podataka, ili mogu davati netočne ili zavaravajuće rezultate. **Kada biste trebali razmotriti fine-tuning** kao moguću soluciju?                                                                                                                                                                         |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)               | Kontinuirani fine-tuning je iterativni proces odabira već fino podešenog modela kao osnovnog i **daljnjeg podešavanja** na novim skupovima primjera za trening.                                                                                                                                                                                                                             |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fine-tuning vašeg modela **s primjerima poziva funkcija** može poboljšati izlaz modela dajući točnije i konzistentnije rezultate - s odgovorima sličnog formata i uštedom troškova                                                                                                                                                                                                          |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Pogledajte ovu tablicu da biste razumjeli **koji se modeli mogu fino podešavati** u Azure OpenAI i u kojim su regijama dostupni. Provjerite njihove limite tokena i datume isteka podataka za trening ako je potrebno.                                                                                                                                                                      |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Ova 30-minutna epizoda AI Show iz **listopada 2023.** raspravlja o prednostima, nedostacima i praktičnim uvidima koji vam pomažu donijeti odluku.                                                                                                                                                                                                                                         |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                   | Ovaj **AI Playbook** vodi vas kroz zahtjeve za podatke, formatiranje, podešavanje hiperparametara i izazove/ograničenja koje trebate znati.                                                                                                                                                                                                                                               |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Naučite kako kreirati uzorak skupa podataka za fine-tuning, pripremiti se za fine-tuning, pokrenuti posao fine-tuninga i implementirati fino podešeni model na Azure.                                                                                                                                                                                                                       |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio omogućuje prilagodbu velikih jezičnih modela vašim osobnim skupovima podataka _korištenjem UI-based workflowa pogodnog za low-code developere_. Pogledajte ovaj primjer.                                                                                                                                                                                                  |
| **Tutorial**: [Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Ovaj članak opisuje kako fino podesiti Hugging Face model koristeći Hugging Face transformers biblioteku na jednoj GPU jedinici s Azure DataBricks i Hugging Face Trainer bibliotekama.                                                                                                                                                                                                       |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)             | Katalog modela u Azure Machine Learning nudi mnoge open source modele koje možete fino podesiti za svoj specifični zadatak. Isprobajte ovaj modul iz [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst).                                                                 |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fine-tuning GPT-3.5 ili GPT-4 modela na Microsoft Azure koristeći W&B omogućuje detaljno praćenje i analizu performansi modela. Ovaj vodič proširuje koncepte iz OpenAI Fine-Tuning vodiča s posebnim koracima i značajkama za Azure OpenAI.                                                                                                                                               |
|                                                                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                                                                                            |

## 2. Sekundarni resursi

Ovaj odjeljak sadrži dodatne resurse koje vrijedi istražiti, ali za koje nismo imali vremena u ovoj lekciji. Mogu biti obrađeni u budućim lekcijama ili kao sekundarni zadatak. Za sada ih koristite za izgradnju vlastitog znanja i stručnosti o ovoj temi.

| Naslov/Link                                                                                                                                                                                                                 | Opis                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Priprema i analiza podataka za fine-tuning chat modela](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                              | Ovaj notebook služi kao alat za predobradu i analizu chat skupa podataka korištenog za fine-tuning chat modela. Provjerava greške u formatu, daje osnovne statistike i procjenjuje broj tokena za troškove fine-tuninga. Pogledajte: [Fine-tuning metoda za gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                                            |
| **OpenAI Cookbook**: [Fine-Tuning za Retrieval Augmented Generation (RAG) s Qdrantom](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)           | Cilj ovog notebooka je prikazati detaljan primjer kako fino podesiti OpenAI modele za Retrieval Augmented Generation (RAG). Također ćemo integrirati Qdrant i Few-Shot Learning za poboljšanje performansi modela i smanjenje izmišljotina.                                                                                                                                                                                                                                                                        |
| **OpenAI Cookbook**: [Fine-tuning GPT s Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                                     | Weights & Biases (W&B) je platforma za AI developere s alatima za treniranje modela, fine-tuning i korištenje osnovnih modela. Prvo pročitajte njihov vodič za [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst), a zatim isprobajte vježbu iz Cookbooka.                                                                                                                                                                                        |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning za male jezične modele                                                               | Upoznajte [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), Microsoftov novi mali model, iznimno moćan, a kompaktan. Ovaj tutorial vodi vas kroz fine-tuning Phi-2, pokazujući kako izgraditi jedinstveni skup podataka i fino podesiti model koristeći QLoRA.                                                                                                                                                                  |
| **Hugging Face Tutorial** [Kako fino podesiti LLM modele u 2024. s Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                                   | Ovaj blog vodi vas kroz proces fine-tuninga otvorenih LLM modela koristeći Hugging Face TRL, Transformers i datasets u 2024. Definirate slučaj upotrebe, postavite razvojno okruženje, pripremite skup podataka, fino podesite model, testirate i evaluirate ga, a zatim implementirate u produkciju.                                                                                                                                                                                               |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                                  | Omogućuje brže i lakše treniranje i implementaciju [najmodernijih modela strojnog učenja](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Repo sadrži tutorijale prilagođene Colabu s YouTube video uputama za fine-tuning. **Odražava nedavnu [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) nadogradnju**. Pročitajte [AutoTrain dokumentaciju](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst). |
|                                                                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |

**Odricanje od odgovornosti**:  
Ovaj dokument je preveden korištenjem AI usluge za prevođenje [Co-op Translator](https://github.com/Azure/co-op-translator). Iako težimo točnosti, imajte na umu da automatski prijevodi mogu sadržavati pogreške ili netočnosti. Izvorni dokument na izvornom jeziku treba smatrati autoritativnim izvorom. Za kritične informacije preporučuje se profesionalni ljudski prijevod. Ne snosimo odgovornost za bilo kakva nesporazuma ili pogrešna tumačenja koja proizlaze iz korištenja ovog prijevoda.