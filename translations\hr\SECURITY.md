<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:56:32+00:00",
  "source_file": "SECURITY.md",
  "language_code": "hr"
}
-->
## Sigurnost

Microsoft ozbiljno shvaća sigurnost naših softverskih proizvoda i usluga, što uključuje sve repozitorije izvornog koda kojima upravljamo putem naših GitHub organizacija, uključujući [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) i [naše GitHub organizacije](https://opensource.microsoft.com/).

Ako smatrate da ste pronašli sigurnosnu ranjivost u bilo kojem repozitoriju u vlasništvu Microsofta koja zadovoljava [Microsoftovu definiciju sigurnosne ranjivosti](https://aka.ms/opensource/security/definition), molimo da nam to prijavite na način opisan u nastavku.

## Prijava sigurnosnih problema

**Molimo vas da ne prijavljujete sigurnosne ranjivosti putem javnih GitHub issues.**

Umjesto toga, prijavite ih Microsoft Security Response Centeru (MSRC) na [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Ako želite poslati prijavu bez prijave na sustav, pošaljite e-mail na [<EMAIL>](mailto:<EMAIL>). Ako je moguće, šifrirajte poruku našim PGP ključem; preuzmite ga s [Microsoft Security Response Center PGP Key stranice](https://aka.ms/opensource/security/pgpkey).

Trebali biste dobiti odgovor unutar 24 sata. Ako iz nekog razloga ne dobijete odgovor, molimo da nas putem e-maila podsjetite kako bismo bili sigurni da smo zaprimili vašu izvornu poruku. Dodatne informacije dostupne su na [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Molimo uključite tražene informacije navedene u nastavku (što više možete pružiti) kako bismo bolje razumjeli prirodu i opseg mogućeg problema:

  * Vrsta problema (npr. buffer overflow, SQL injection, cross-site scripting i slično)
  * Potpune putanje izvornog koda povezane s pojavom problema
  * Lokacija pogođenog izvornog koda (tag/grana/commit ili izravni URL)
  * Bilo kakve posebne konfiguracije potrebne za reproduciranje problema
  * Korak-po-korak upute za reproduciranje problema
  * Proof-of-concept ili exploit kod (ako je moguće)
  * Utjecaj problema, uključujući kako bi napadač mogao iskoristiti ranjivost

Ove informacije pomoći će nam da brže obradimo vašu prijavu.

Ako prijavljujete u sklopu bug bounty programa, detaljnije prijave mogu doprinijeti većoj nagradi. Posjetite našu [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) stranicu za više detalja o aktivnim programima.

## Preferirani jezici

Preferiramo da sva komunikacija bude na engleskom jeziku.

## Politika

Microsoft se pridržava načela [Koordinirane objave ranjivosti](https://aka.ms/opensource/security/cvd).

**Odricanje od odgovornosti**:  
Ovaj dokument je preveden korištenjem AI usluge za prevođenje [Co-op Translator](https://github.com/Azure/co-op-translator). Iako težimo točnosti, imajte na umu da automatski prijevodi mogu sadržavati pogreške ili netočnosti. Izvorni dokument na izvornom jeziku treba smatrati autoritativnim izvorom. Za kritične informacije preporučuje se profesionalni ljudski prijevod. Ne snosimo odgovornost za bilo kakva nesporazuma ili pogrešna tumačenja koja proizlaze iz korištenja ovog prijevoda.