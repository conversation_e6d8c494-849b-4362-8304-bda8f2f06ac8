<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:07:22+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "hu"
}
-->
# <PERSON><PERSON><PERSON><PERSON>ul<PERSON>hoz Hasznos Források

A leckét több alapvető forrás felhasz<PERSON><PERSON> össze, amelyek az OpenAI és az Azure OpenAI terminológiáját és oktatóanyagait tartalmazzák. Íme egy nem teljes lista, amely segíthet az önálló tanulási utazásod során.

## 1. Elsődleges Források

| Cím/Link                                                                                                                                                                                                                   | Leírás                                                                                                                                                                                                                                                                                                                       |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | A fine-tuning javítja a few-shot tanulást azáltal, hogy sokkal több példán tanít, mint ami a promptba beleférne, így költséget takarít meg, javítja a válaszok minőségét, és alacsonyabb késleltetésű kéréseket tesz lehetővé. **Ismerd meg az OpenAI fine-tuning áttekintését.**                                            |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Értsd meg, **mi a fine-tuning (fogalom)**, miért érdemes foglalkozni vele (motiváló probléma), milyen adatokat használj (tréning), és hogyan mérd a minőséget.                                                                                                                                                               |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Az Azure OpenAI Service lehetővé teszi, hogy személyre szabott adatbázisod alapján finomhangold a modelleket. Tanuld meg, **hogyan végezd a fine-tuningot (folyamat)**, és válassz modelleket az Azure AI Studio, Python SDK vagy REST API segítségével.                                                                             |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | A nagy nyelvi modellek (LLM-ek) nem mindig teljesítenek jól bizonyos területeken, feladatokban vagy adatkészleteken, illetve pontatlan vagy félrevezető válaszokat adhatnak. **Mikor érdemes megfontolni a fine-tuningot** megoldásként?                                                                                         |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | A folyamatos fine-tuning egy iteratív folyamat, amely során egy már finomhangolt modellt választunk ki alapmodellként, és **további finomhangolást végzünk** újabb tréning példákon.                                                                                                                                           |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | A modell finomhangolása **függvényhívásos példákkal** javíthatja a kimenetek pontosságát és következetességét – hasonló formátumú válaszokat és költségmegtakarítást eredményezve.                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Nézd meg ezt a táblázatot, hogy megtudd, **mely modellek finomhangolhatók** az Azure OpenAI-ban, és mely régiókban érhetők el. Ha szükséges, ellenőrizd a tokenkorlátokat és a tréningadatok lejárati idejét.                                                                                                                    |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Ez a 30 perces, **2023 októberi** AI Show epizód a fine-tuning előnyeit, hátrányait és gyakorlati tapasztalatait tárgyalja, amelyek segítenek a döntés meghozatalában.                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Ez az **AI Playbook** forrás végigvezet az adatigényeken, formázáson, hiperparaméterek finomhangolásán, valamint a tudnivaló kihívásokon és korlátokon.                                                                                                                                                                       |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Tanuld meg, hogyan készíts mintafinomhangolási adatbázist, készülj fel a fine-tuningra, indíts fine-tuning feladatot, és telepítsd az így kapott modellt az Azure-on.                                                                                                                                                          |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Az Azure AI Studio lehetővé teszi, hogy nagy nyelvi modelleket személyre szabj saját adataid alapján _egy UI-alapú munkafolyamat segítségével, amely alacsony kódolási igényű fejlesztőknek is megfelelő_. Nézd meg ezt a példát.                                                                                                   |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Ez a cikk bemutatja, hogyan finomhangolj egy Hugging Face modellt a Hugging Face transformers könyvtárral egyetlen GPU-n Azure DataBricks és Hugging Face Trainer könyvtárak segítségével.                                                                                                                                       |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Az Azure Machine Learning modellkatalógusa számos nyílt forráskódú modellt kínál, amelyeket finomhangolhatsz a saját feladatodra. Próbáld ki ezt a modult az [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) részeként. |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | A GPT-3.5 vagy GPT-4 modellek finomhangolása Microsoft Azure-on W&B segítségével részletes nyomon követést és elemzést tesz lehetővé a modell teljesítményéről. Ez az útmutató kiterjeszti az OpenAI Fine-Tuning útmutató fogalmait az Azure OpenAI specifikus lépéseivel és funkcióival.                                               |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. Másodlagos Források

Ez a szakasz további forrásokat tartalmaz, amelyek érdemesek a felfedezésre, de a leckében nem volt időnk részletesen foglalkozni velük. Ezeket később egy másik lecke vagy másodlagos feladat részeként is érinthetjük. Addig is használd őket saját szakértelmed és tudásod bővítésére a témában.

| Cím/Link                                                                                                                                                                                                            | Leírás                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Adatelőkészítés és elemzés chat modell finomhangoláshoz](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | Ez a jegyzetfüzet eszközként szolgál a chat modell finomhangolásához használt adatbázis előfeldolgozásához és elemzéséhez. Ellenőrzi a formátumhibákat, alapstatisztikákat ad, és becslést készít a tokenek számára a finomhangolási költségekhez. Lásd: [Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [Retrieval Augmented Generation (RAG) finomhangolása Qdrant-tal](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | Ennek a jegyzetfüzetnek a célja, hogy egy átfogó példán keresztül bemutassa, hogyan lehet OpenAI modelleket finomhangolni a Retrieval Augmented Generation (RAG) feladathoz. Integráljuk a Qdrant-ot és a Few-Shot Learninget a modell teljesítményének növelése és a téves válaszok csökkentése érdekében.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [GPT finomhangolása Weights & Biases segítségével](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | A Weights & Biases (W&B) egy AI fejlesztői platform, amely eszközöket kínál modellek tanításához, finomhangolásához és alapmodellek kihasználásához. Először olvasd el az ő [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) útmutatójukat, majd próbáld ki a Cookbook gyakorlatot.                                                                                                                                                                                                                  |
| **Közösségi oktatóanyag** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - finomhangolás kis nyelvi modellekhez                                                   | Ismerd meg a [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst) modellt, a Microsoft új, meglepően erős, mégis kompakt kis modelljét. Ez az oktatóanyag végigvezet a Phi-2 finomhangolásán, bemutatva, hogyan építs egyedi adatbázist és hogyan finomhangold a modellt QLoRA segítségével.                                                                                                                                                                       |
| **Hugging Face oktatóanyag** [Hogyan finomhangoljunk LLM-eket 2024-ben Hugging Face segítségével](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Ez a blogbejegyzés bemutatja, hogyan finomhangolj nyílt LLM-eket Hugging Face TRL, Transformers és datasets könyvtárakkal 2024-ben. Meghatározod az alkalmazási esetet, beállítod a fejlesztői környezetet, előkészíted az adatbázist, finomhangolod a modellt, teszteled és értékeled, majd élesíted.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Gyorsabb és egyszerűbb tanítást és telepítést kínál a [legmodernebb gépi tanulási modellekhez](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). A repó Colab-barát oktatóanyagokat és YouTube videós útmutatót tartalmaz finomhangoláshoz. **Tükrözi a legutóbbi [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) frissítést**. Olvasd el az [AutoTrain dokumentációt](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst). |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Jogi nyilatkozat**:  
Ez a dokumentum az AI fordító szolgáltatás, a [Co-op Translator](https://github.com/Azure/co-op-translator) segítségével készült. Bár a pontosságra törekszünk, kérjük, vegye figyelembe, hogy az automatikus fordítások hibákat vagy pontatlanságokat tartalmazhatnak. Az eredeti dokumentum az anyanyelvén tekintendő hiteles forrásnak. Kritikus információk esetén professzionális emberi fordítást javaslunk. Nem vállalunk felelősséget az ebből eredő félreértésekért vagy téves értelmezésekért.