<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:42:30+00:00",
  "source_file": "README.md",
  "language_code": "hu"
}
-->
![Generatív MI kezd<PERSON>knek](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.hu.png)

### 21 lecke, amely mindent megtanít, amit a generatív MI alkalmazások fejlesztésének megkezdéséhez tudni kell

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Többnyelvű támogatás

#### GitHub Action segítségével (Automatikus és mindig naprakész)

[Francia](../fr/README.md) | [Spanyol](../es/README.md) | [Német](../de/README.md) | [Orosz](../ru/README.md) | [Arab](../ar/README.md) | [Perzsa (Fárszi)](../fa/README.md) | [Urdu](../ur/README.md) | [Kínai (egyszerűsített)](../zh/README.md) | [Kínai (hagyományos, Makaó)](../mo/README.md) | [Kínai (hagyományos, Hongkong)](../hk/README.md) | [Kínai (hagyományos, Tajvan)](../tw/README.md) | [Japán](../ja/README.md) | [Koreai](../ko/README.md) | [Hindi](../hi/README.md) | [Bengáli](../bn/README.md) | [Maráthi](../mr/README.md) | [Nepáli](../ne/README.md) | [Pandzsábi (Gurmukhi)](../pa/README.md) | [Portugál (Portugália)](../pt/README.md) | [Portugál (Brazília)](../br/README.md) | [Olasz](../it/README.md) | [Lengyel](../pl/README.md) | [Török](../tr/README.md) | [Görög](../el/README.md) | [Thai](../th/README.md) | [Svéd](../sv/README.md) | [Dán](../da/README.md) | [Norvég](../no/README.md) | [Finn](../fi/README.md) | [Holland](../nl/README.md) | [Héber](../he/README.md) | [Vietnami](../vi/README.md) | [Indonéz](../id/README.md) | [Maláj](../ms/README.md) | [Tagalog (Filippínó)](../tl/README.md) | [Szuahéli](../sw/README.md) | [Magyar](./README.md) | [Cseh](../cs/README.md) | [Szlovák](../sk/README.md) | [Román](../ro/README.md) | [Bolgár](../bg/README.md) | [Szerb (cirill)](../sr/README.md) | [Horvát](../hr/README.md) | [Szlovén](../sl/README.md) | [Ukrán](../uk/README.md) | [Burmai (Myanmar)](../my/README.md)

# Generatív MI kezdőknek (3. verzió) – Egy tanfolyam

Ismerd meg a generatív MI alkalmazások fejlesztésének alapjait a Microsoft Cloud Advocates 21 leckéből álló átfogó tanfolyamával.

## 🌱 Kezdés

Ez a tanfolyam 21 leckéből áll. Minden lecke egy-egy témát fed le, szóval kezdj ott, ahol szeretnél!

A leckék vagy "Learn" típusúak, amelyek egy generatív MI koncepciót magyaráznak, vagy "Build" típusúak, amelyek egy koncepciót és kódpéldákat mutatnak be mind **Python**, mind **TypeScript** nyelven, ha lehetséges.

.NET fejlesztőknek ajánljuk a [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst) projektet!

Minden lecke tartalmaz egy "Keep Learning" részt is, további tanulási eszközökkel.

## Amire szükséged van
### A tanfolyam kódjának futtatásához használhatod az alábbiakat:
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) – **Leckék:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) – **Leckék:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) – **Leckék:** "oai-assignment"
   
- Alapvető Python vagy TypeScript ismeretek hasznosak – \*Teljesen kezdőknek ajánljuk ezeket a [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) és [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) tanfolyamokat
- Egy GitHub fiók, hogy [forkold ezt a teljes repót](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) a saját GitHub fiókodba

Készítettünk egy **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** leckét, amely segít a fejlesztői környezet beállításában.

Ne felejtsd el [megcsillagozni (🌟) ezt a repót](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst), hogy később könnyebben megtaláld.

## 🧠 Készen állsz a telepítésre?

Ha fejlettebb kódpéldákat keresel, nézd meg a [Generatív MI kódminták gyűjteményét](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) mind **Python**, mind **TypeScript** nyelven.

## 🗣️ Találkozz más tanulókkal, kapj támogatást

Csatlakozz az [Azure AI Foundry hivatalos Discord szerveréhez](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst), hogy találkozz és kapcsolatot építs más tanulókkal, akik ezt a tanfolyamot végzik, és kapj segítséget.

Tedd fel kérdéseidet vagy oszd meg visszajelzésedet a termékről az [Azure AI Foundry fejlesztői fórumán](https://aka.ms/azureaifoundry/forum) GitHubon.

## 🚀 Startupot építesz?

Regisztrálj a [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) szolgáltatásra, hogy **ingyenes OpenAI krediteket** és akár **150 000 dollár értékű Azure kreditet kapj az OpenAI modellek Azure OpenAI Services-en keresztüli eléréséhez**.

## 🙏 Szeretnél segíteni?

Van javaslatod, vagy találtál helyesírási vagy kódhibákat? [Nyiss egy issue-t](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) vagy [készíts egy pull requestet](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Minden lecke tartalmaz:

- Egy rövid videós bevezetőt a témához
- Egy írott leckét a README-ben
- Python és TypeScript kódpéldákat, amelyek támogatják az Azure OpenAI-t és az OpenAI API-t
- Linkeket további forrásokhoz a tanulás folytatásához

## 🗃️ Leckék

| #   | **Lecke linkje**                                                                                                                              | **Leírás**                                                                                     | **Videó**                                                                   | **További tanulás**                                                            |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Learn:** Fejlesztői környezet beállítása                                                  | Videó hamarosan                                                                | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Bevezetés a generatív MI-be és a LLM-ekbe](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                            | **Learn:** Mi a generatív MI és hogyan működnek a nagy nyelvi modellek (LLM-ek)               | [Videó](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Különböző LLM-ek felfedezése és összehasonlítása](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)     | **Learn:** Hogyan válasszuk ki a megfelelő modellt az adott feladathoz                        | [Videó](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [A generatív MI felelősségteljes használata](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                   | **Learn:** Hogyan építsünk felelősségteljes generatív MI alkalmazásokat                       | [Videó](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Prompt mérnökség alapjai](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)                                   | **Learn:** Gyakorlati prompt mérnökségi legjobb gyakorlatok                                  | [Videó](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Fejlett promptok létrehozása](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                              | **Learn:** Hogyan alkalmazzunk prompt mérnökségi technikákat, amelyek javítják a promptok eredményét | [Videó](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Szövegalkotó alkalmazások fejlesztése](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Fejlesztés:** Szövegalkotó alkalmazás készítése Azure OpenAI / OpenAI API használatával           | [Videó](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Csevegőalkalmazások fejlesztése](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Fejlesztés:** Hatékony technikák csevegőalkalmazások építéséhez és integrálásához               | [Videó](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Keresőalkalmazások fejlesztése vektoralapú adatbázisokkal](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Fejlesztés:** Keresőalkalmazás, amely Embeddingeket használ az adatok kereséséhez               | [Videó](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Képalkotó alkalmazások fejlesztése](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Fejlesztés:** Képalkotó alkalmazás                                                             | [Videó](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Alacsony kódú AI alkalmazások fejlesztése](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Fejlesztés:** Generatív AI alkalmazás alacsony kódú eszközökkel                                | [Videó](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Külső alkalmazások integrálása function calling segítségével](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **Fejlesztés:** Mi az a function calling és hogyan használható alkalmazásokban                   | [Videó](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [UX tervezése AI alkalmazásokhoz](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Tanulás:** Hogyan alkalmazzuk az UX tervezési elveket generatív AI alkalmazások fejlesztésekor | [Videó](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Generatív AI alkalmazások biztonságossá tétele](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Tanulás:** Az AI rendszerek fenyegetettségei és kockázatai, valamint ezek biztonságossá tételének módjai | [Videó](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [A generatív AI alkalmazások életciklusa](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **Tanulás:** Eszközök és mérőszámok az LLM életciklus és LLMOps kezeléséhez                      | [Videó](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) és vektoralapú adatbázisok](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **Fejlesztés:** Alkalmazás RAG keretrendszerrel, amely vektoralapú adatbázisból kérdez Embeddingeket | [Videó](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Nyílt forráskódú modellek és Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **Fejlesztés:** Alkalmazás nyílt forráskódú modellek használatával a Hugging Face-en             | [Videó](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI ügynökök](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **Fejlesztés:** Alkalmazás AI Agent keretrendszerrel                                            | [Videó](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [LLM-ek finomhangolása](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Tanulás:** Mi, miért és hogyan történik az LLM-ek finomhangolása                              | [Videó](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Fejlesztés SLM-ekkel](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Tanulás:** A kis nyelvi modellekkel való fejlesztés előnyei                                   | Videó hamarosan | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Fejlesztés Mistral modellekkel](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Tanulás:** A Mistral család modelleinek jellemzői és különbségei                             | Videó hamarosan | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Fejlesztés Meta modellekkel](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Tanulás:** A Meta család modelleinek jellemzői és különbségei                               | Videó hamarosan | [Tudj meg többet](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Külön köszönet

Külön köszönet [**John Aziz**](https://www.linkedin.com/in/john0isaac/)-nak az összes GitHub Actions és munkafolyamat létrehozásáért

[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/)-nek, aki kulcsfontosságú hozzájárulásokat tett minden leckéhez a tanulói és kódélmény javítása érdekében.

## 🎒 Egyéb tanfolyamok

Csapatunk más tanfolyamokat is készít! Nézd meg:

- [**ÚJ** Model Context Protocol kezdőknek](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI ügynökök kezdőknek](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Generatív AI kezdőknek .NET használatával](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Generatív AI kezdőknek JavaScript használatával](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML kezdőknek](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Adattudomány kezdőknek](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI kezdőknek](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Kiberbiztonság kezdőknek](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Webfejlesztés kezdőknek](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT kezdőknek](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR fejlesztés kezdőknek](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [GitHub Copilot mesterfokon AI páros programozáshoz](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [GitHub Copilot mesterfokon C#/.NET fejlesztőknek](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Válaszd a saját Copilot kalandodat](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Jogi nyilatkozat**:  
Ez a dokumentum az AI fordító szolgáltatás, a [Co-op Translator](https://github.com/Azure/co-op-translator) segítségével készült. Bár a pontosságra törekszünk, kérjük, vegye figyelembe, hogy az automatikus fordítások hibákat vagy pontatlanságokat tartalmazhatnak. Az eredeti dokumentum az anyanyelvén tekintendő hiteles forrásnak. Kritikus információk esetén professzionális emberi fordítást javaslunk. Nem vállalunk felelősséget a fordítás használatából eredő félreértésekért vagy téves értelmezésekért.