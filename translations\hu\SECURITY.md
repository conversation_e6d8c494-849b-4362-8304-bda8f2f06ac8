<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:55:35+00:00",
  "source_file": "SECURITY.md",
  "language_code": "hu"
}
-->
## Biztonság

A Microsoft komolyan veszi szoftvertermékeink és szolgáltatásaink biztonságát, ideértve az ö<PERSON>, GitHub szervezeteinken keresztül kezelt forráskód-tárat is, amelyek közé tartozik a [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) és [a GitHub szervezeteink](https://opensource.microsoft.com/).

Ha úgy gondolja, hogy biztonsági sebezhetőséget talált bármely Microsoft tulajdonában lévő tárban, amely megfelel a [Microsoft biztonsági sebezhetőség definíciójának](https://aka.ms/opensource/security/definition), kérjük, jelezze nekünk az alábbiak szerint.

## Biztonsági problémák bejelentése

**Kérjük, ne jelentsen biztonsági sebezhetőségeket nyilvános GitHub issue-kon keresztül.**

Ehelyett kérjük, jelentse a Microsoft Security Response Centernek (MSRC) a [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report) címen.

Ha inkább bejelentést szeretne tenni bejelentkezés nélkül, küldjön e-mailt a [<EMAIL>](mailto:<EMAIL>) címre. Ha lehetséges, titkosítsa üzenetét a PGP kulcsunkkal; kérjük, töltse le a [Microsoft Security Response Center PGP Key oldaláról](https://aka.ms/opensource/security/pgpkey).

Várhatóan 24 órán belül választ kap. Ha valamilyen okból nem kap választ, kérjük, kövesse nyomon e-mailben, hogy megbizonyosodjunk arról, hogy megkaptuk az eredeti üzenetét. További információk találhatók a [microsoft.com/msrc](https://aka.ms/opensource/security/msrc) oldalon.

Kérjük, hogy a lent felsorolt információkat (amennyit csak tud) mellékelje, hogy jobban megérthessük a lehetséges probléma jellegét és kiterjedését:

  * A probléma típusa (pl. puffer túlcsordulás, SQL injekció, cross-site scripting stb.)
  * A probléma megjelenéséhez kapcsolódó forrásfájl(ok) teljes elérési útja
  * Az érintett forráskód helye (tag/branch/commit vagy közvetlen URL)
  * Bármilyen speciális konfiguráció, amely szükséges a probléma reprodukálásához
  * Lépésről lépésre útmutató a probléma reprodukálásához
  * Bizonyító koncepció vagy exploit kód (ha lehetséges)
  * A probléma hatása, beleértve, hogy egy támadó hogyan használhatná ki azt

Ezek az információk segítenek minket abban, hogy gyorsabban feldolgozzuk a bejelentését.

Ha hibavadászatra jelentkezik, a részletesebb jelentések magasabb jutalmat eredményezhetnek. Kérjük, látogasson el a [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) oldalra, ahol további részleteket talál aktív programjainkról.

## Előnyben részesített nyelvek

Kommunikációink során az angol nyelvet részesítjük előnyben.

## Irányelv

A Microsoft követi a [Koordinált Sebezhetőség Közlés](https://aka.ms/opensource/security/cvd) elvét.

**Jogi nyilatkozat**:  
Ez a dokumentum az AI fordító szolgáltatás, a [Co-op Translator](https://github.com/Azure/co-op-translator) segítségével készült. Bár a pontosságra törekszünk, kérjük, vegye figyelembe, hogy az automatikus fordítások hibákat vagy pontatlanságokat tartalmazhatnak. Az eredeti dokumentum az anyanyelvén tekintendő hiteles forrásnak. Fontos információk esetén szakmai, emberi fordítást javaslunk. Nem vállalunk felelősséget a fordítás használatából eredő félreértésekért vagy téves értelmezésekért.