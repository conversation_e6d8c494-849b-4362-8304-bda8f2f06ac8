<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:05:50+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "id"
}
-->
# Sumber Daya untuk Pembelajaran Mandiri

Pelajaran ini dibuat dengan menggunakan sejumlah sumber utama dari OpenAI dan Azure OpenAI sebagai referensi untuk terminologi dan tutorial. Berikut adalah daftar yang tidak lengkap, untuk perjalanan pembelajaran mandiri Anda sendiri.

## 1. Sumber Utama

| Judul/Tautan                                                                                                                                                                                                                   | Deskripsi                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning dengan Model OpenAI](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning meningkatkan pembelajaran few-shot dengan melatih pada lebih banyak contoh daripada yang bisa dimasukkan ke dalam prompt, menghemat biaya, meningkatkan kualitas respons, dan memungkinkan permintaan dengan latensi lebih rendah. **Dapatkan gambaran umum tentang fine-tuning dari OpenAI.**                                                                                    |
| [Apa itu Fine-Tuning dengan Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Pahami **apa itu fine-tuning (konsep)**, mengapa Anda harus mempertimbangkannya (masalah yang memotivasi), data apa yang digunakan (pelatihan), dan cara mengukur kualitasnya                                                                                                                                                                           |
| [Sesuaikan model dengan fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service memungkinkan Anda menyesuaikan model kami dengan dataset pribadi menggunakan fine-tuning. Pelajari **cara melakukan fine-tuning (proses)** memilih model menggunakan Azure AI Studio, Python SDK, atau REST API.                                                                                                                                |
| [Rekomendasi untuk fine-tuning LLM](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM mungkin tidak bekerja dengan baik pada domain, tugas, atau dataset tertentu, atau bisa menghasilkan output yang tidak akurat atau menyesatkan. **Kapan Anda harus mempertimbangkan fine-tuning** sebagai solusi yang mungkin?                                                                                                                                  |
| [Fine-Tuning Berkelanjutan](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Fine-tuning berkelanjutan adalah proses iteratif memilih model yang sudah di-fine-tune sebagai model dasar dan **melakukan fine-tuning lebih lanjut** pada set contoh pelatihan baru.                                                                                                                                                     |
| [Fine-tuning dan pemanggilan fungsi](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Melakukan fine-tuning model Anda **dengan contoh pemanggilan fungsi** dapat meningkatkan keluaran model dengan menghasilkan respons yang lebih akurat dan konsisten - dengan format respons yang serupa & penghematan biaya                                                                                                                                        |
| [Fine-tuning Models: Panduan Azure OpenAI](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Lihat tabel ini untuk memahami **model apa saja yang bisa di-fine-tune** di Azure OpenAI, dan di wilayah mana saja tersedia. Periksa batas token dan tanggal kedaluwarsa data pelatihan jika diperlukan.                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Episode AI Show berdurasi 30 menit **Oktober 2023** ini membahas manfaat, kekurangan, dan wawasan praktis yang membantu Anda membuat keputusan ini.                                                                                                                                                                                        |
| [Memulai Fine-Tuning LLM](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Sumber daya **AI Playbook** ini membimbing Anda melalui kebutuhan data, format, fine-tuning hyperparameter, serta tantangan dan keterbatasan yang perlu Anda ketahui.                                                                                                                                                                         |
| **Tutorial**: [Fine-Tuning Azure OpenAI GPT3.5 Turbo](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Pelajari cara membuat dataset contoh untuk fine-tuning, mempersiapkan fine-tuning, membuat pekerjaan fine-tuning, dan menerapkan model yang sudah di-fine-tune di Azure.                                                                                                                                                                                    |
| **Tutorial**: [Fine-tune model Llama 2 di Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio memungkinkan Anda menyesuaikan model bahasa besar dengan dataset pribadi _menggunakan alur kerja berbasis UI yang cocok untuk pengembang low-code_. Lihat contoh ini.                                                                                                                                                               |
| **Tutorial**: [Fine-tune model Hugging Face untuk satu GPU di Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Artikel ini menjelaskan cara melakukan fine-tuning model Hugging Face dengan library transformers Hugging Face pada satu GPU menggunakan Azure DataBricks + library Hugging Face Trainer                                                                                                                                                |
| **Pelatihan:** [Fine-tune foundation model dengan Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Katalog model di Azure Machine Learning menawarkan banyak model open source yang bisa Anda fine-tune untuk tugas spesifik Anda. Coba modul ini yang merupakan bagian dari [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Fine-Tuning Azure OpenAI](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fine-tuning model GPT-3.5 atau GPT-4 di Microsoft Azure menggunakan W&B memungkinkan pelacakan dan analisis performa model secara mendetail. Panduan ini memperluas konsep dari panduan Fine-Tuning OpenAI dengan langkah dan fitur khusus untuk Azure OpenAI.                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. Sumber Sekunder

Bagian ini berisi sumber tambahan yang layak untuk dijelajahi, namun belum sempat kami bahas dalam pelajaran ini. Mungkin akan dibahas di pelajaran mendatang, atau sebagai opsi tugas tambahan di waktu berikutnya. Untuk saat ini, gunakan sumber ini untuk membangun keahlian dan pengetahuan Anda sendiri tentang topik ini.

| Judul/Tautan                                                                                                                                                                                                            | Deskripsi                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Persiapan dan analisis data untuk fine-tuning model chat](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | Notebook ini berfungsi sebagai alat untuk memproses dan menganalisis dataset chat yang digunakan untuk fine-tuning model chat. Memeriksa kesalahan format, memberikan statistik dasar, dan memperkirakan jumlah token untuk biaya fine-tuning. Lihat: [Metode fine-tuning untuk gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning untuk Retrieval Augmented Generation (RAG) dengan Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | Notebook ini bertujuan memberikan contoh lengkap cara melakukan fine-tuning model OpenAI untuk Retrieval Augmented Generation (RAG). Kami juga akan mengintegrasikan Qdrant dan Few-Shot Learning untuk meningkatkan performa model dan mengurangi kesalahan informasi.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT dengan Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) adalah platform pengembang AI dengan alat untuk melatih model, fine-tuning model, dan memanfaatkan foundation model. Baca panduan mereka tentang [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) terlebih dahulu, lalu coba latihan Cookbook ini.                                                                                                                                                                                                                  |
| **Tutorial Komunitas** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning untuk Small Language Models                                                   | Kenali [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), model kecil baru dari Microsoft yang sangat kuat namun ringkas. Tutorial ini akan memandu Anda melakukan fine-tuning Phi-2, menunjukkan cara membangun dataset unik dan melakukan fine-tuning model menggunakan QLoRA.                                                                                                                                                                       |
| **Tutorial Hugging Face** [Cara Fine-Tune LLM di 2024 dengan Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Posting blog ini membimbing Anda cara melakukan fine-tuning LLM terbuka menggunakan Hugging Face TRL, Transformers & datasets di tahun 2024. Anda mendefinisikan kasus penggunaan, menyiapkan lingkungan pengembangan, mempersiapkan dataset, melakukan fine-tuning model, menguji dan mengevaluasi, lalu menerapkannya ke produksi.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Mempercepat dan mempermudah pelatihan serta penerapan [model machine learning terkini](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Repo ini memiliki tutorial ramah Colab dengan panduan video YouTube untuk fine-tuning. **Mencerminkan pembaruan terbaru [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)**. Baca dokumentasi [AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Penafian**:  
Dokumen ini telah diterjemahkan menggunakan layanan terjemahan AI [Co-op Translator](https://github.com/Azure/co-op-translator). Meskipun kami berupaya untuk mencapai akurasi, harap diperhatikan bahwa terjemahan otomatis mungkin mengandung kesalahan atau ketidakakuratan. Dokumen asli dalam bahasa aslinya harus dianggap sebagai sumber yang sahih. Untuk informasi penting, disarankan menggunakan terjemahan profesional oleh manusia. Kami tidak bertanggung jawab atas kesalahpahaman atau penafsiran yang keliru yang timbul dari penggunaan terjemahan ini.