<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:55:01+00:00",
  "source_file": "SECURITY.md",
  "language_code": "id"
}
-->
## Keamanan

Microsoft sangat memperhatikan keamanan produk dan layanan perangkat lunak kami, termasuk semua repositori kode sumber yang dikelola melalui organisasi GitHub kami, seperti [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin), dan [organisasi GitHub kami](https://opensource.microsoft.com/).

<PERSON><PERSON> Anda yakin telah menemukan kerentanan keamanan di repositori milik Microsoft yang memenuhi [definisi kerentanan keamanan Microsoft](https://aka.ms/opensource/security/definition), harap laporkan kepada kami seperti yang dijelaskan di bawah ini.

## Melaporkan Masalah Keamanan

**Harap jangan melaporkan kerentanan keamanan melalui isu publik di GitHub.**

Sebagai gantinya, laporkan ke Microsoft Security Response Center (MSRC) di [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Jika Anda lebih memilih untuk mengirim tanpa login, kirim email ke [<EMAIL>](mailto:<EMAIL>). Jika memungkinkan, enkripsi pesan Anda dengan kunci PGP kami; silakan unduh dari halaman [Microsoft Security Response Center PGP Key](https://aka.ms/opensource/security/pgpkey).

Anda seharusnya menerima balasan dalam waktu 24 jam. Jika karena suatu alasan tidak menerima, harap tindak lanjuti melalui email untuk memastikan kami telah menerima pesan asli Anda. Informasi tambahan dapat ditemukan di [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Harap sertakan informasi yang diminta di bawah ini (sebanyak mungkin yang dapat Anda berikan) untuk membantu kami memahami sifat dan cakupan masalah yang mungkin terjadi:

  * Jenis masalah (misalnya buffer overflow, SQL injection, cross-site scripting, dll.)
  * Jalur lengkap file sumber yang terkait dengan munculnya masalah
  * Lokasi kode sumber yang terdampak (tag/branch/commit atau URL langsung)
  * Konfigurasi khusus yang diperlukan untuk mereproduksi masalah
  * Instruksi langkah demi langkah untuk mereproduksi masalah
  * Bukti konsep atau kode eksploitasi (jika memungkinkan)
  * Dampak masalah, termasuk bagaimana penyerang dapat memanfaatkan masalah tersebut

Informasi ini akan membantu kami memproses laporan Anda lebih cepat.

Jika Anda melaporkan untuk program bug bounty, laporan yang lebih lengkap dapat berkontribusi pada hadiah yang lebih besar. Silakan kunjungi halaman [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) untuk detail lebih lanjut tentang program aktif kami.

## Bahasa yang Disukai

Kami lebih memilih semua komunikasi menggunakan bahasa Inggris.

## Kebijakan

Microsoft mengikuti prinsip [Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd).

**Penafian**:  
Dokumen ini telah diterjemahkan menggunakan layanan terjemahan AI [Co-op Translator](https://github.com/Azure/co-op-translator). Meskipun kami berusaha untuk akurasi, harap diketahui bahwa terjemahan otomatis mungkin mengandung kesalahan atau ketidakakuratan. Dokumen asli dalam bahasa aslinya harus dianggap sebagai sumber yang sahih. Untuk informasi penting, disarankan menggunakan terjemahan profesional oleh manusia. Kami tidak bertanggung jawab atas kesalahpahaman atau penafsiran yang keliru yang timbul dari penggunaan terjemahan ini.