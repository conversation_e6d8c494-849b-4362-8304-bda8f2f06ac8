<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:01:00+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "it"
}
-->
# Risorse per l'Apprendimento Autonomo

La lezione è stata costruita utilizzando diverse risorse fondamentali di OpenAI e Azure OpenAI come riferimento per la terminologia e i tutorial. Ecco una lista non esaustiva, per i tuoi percorsi di apprendimento autonomo.

## 1. Risorse Principali

| Titolo/Link                                                                                                                                                                                                                   | Descrizione                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Il fine-tuning migliora l’apprendimento few-shot addestrando su molti più esempi di quanti ne possano stare nel prompt, riducendo i costi, migliorando la qualità delle risposte e permettendo richieste a latenza più bassa. **Ottieni una panoramica del fine-tuning da OpenAI.**                                                                                     |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Comprendi **cos’è il fine-tuning (concetto)**, perché dovresti considerarlo (problema motivante), quali dati usare (addestramento) e come misurare la qualità                                                                                                                                                                   |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service ti permette di personalizzare i modelli con i tuoi dataset usando il fine-tuning. Scopri **come fare il fine-tuning (processo)** selezionando modelli tramite Azure AI Studio, Python SDK o REST API.                                                                                                         |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | I LLM potrebbero non performare bene su domini, compiti o dataset specifici, o produrre output inaccurati o fuorvianti. **Quando dovresti considerare il fine-tuning** come possibile soluzione?                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Il fine-tuning continuo è un processo iterativo che prende un modello già fine-tuned come base e lo **affina ulteriormente** su nuovi set di esempi di addestramento.                                                                                                                                                             |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Il fine-tuning del modello **con esempi di function calling** può migliorare l’output rendendolo più accurato e coerente - con risposte formattate in modo simile e risparmi sui costi                                                                                                                                            |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Consulta questa tabella per capire **quali modelli possono essere fine-tuned** in Azure OpenAI e in quali regioni sono disponibili. Controlla i limiti di token e le date di scadenza dei dati di addestramento se necessario.                                                                                                    |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Questo episodio di 30 minuti dell’**ottobre 2023** di AI Show discute vantaggi, svantaggi e approfondimenti pratici per aiutarti a prendere questa decisione.                                                                                                                                                                    |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Questa risorsa dell’**AI Playbook** ti guida attraverso requisiti di dati, formattazione, iperparametri per il fine-tuning e sfide/limiti da conoscere.                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Impara a creare un dataset di esempio per il fine-tuning, prepararti al fine-tuning, creare un job di fine-tuning e distribuire il modello fine-tuned su Azure.                                                                                                                                                                  |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio ti permette di personalizzare grandi modelli linguistici con i tuoi dataset _usando un flusso di lavoro basato su UI adatto a sviluppatori low-code_. Guarda questo esempio.                                                                                                                                     |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Questo articolo descrive come fare il fine-tuning di un modello Hugging Face con la libreria transformers su una singola GPU usando Azure DataBricks + librerie Hugging Face Trainer                                                                                                                                            |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Il catalogo modelli di Azure Machine Learning offre molti modelli open source che puoi fine-tunare per il tuo compito specifico. Prova questo modulo che fa parte del [Percorso di Apprendimento AzureML Generative AI](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Il fine-tuning di modelli GPT-3.5 o GPT-4 su Microsoft Azure usando W&B permette un tracciamento dettagliato e un’analisi delle prestazioni del modello. Questa guida estende i concetti della guida OpenAI Fine-Tuning con passaggi e funzionalità specifiche per Azure OpenAI.                                                  |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. Risorse Secondarie

Questa sezione raccoglie risorse aggiuntive che vale la pena esplorare, ma che non abbiamo avuto tempo di trattare in questa lezione. Potrebbero essere incluse in una lezione futura o come opzione di compito secondario in un secondo momento. Per ora, usale per costruire la tua competenza e conoscenza su questo argomento.

| Titolo/Link                                                                                                                                                                                                            | Descrizione                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Preparazione e analisi dei dati per il fine-tuning di modelli chat](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                              | Questo notebook serve come strumento per preprocessare e analizzare il dataset chat usato per il fine-tuning di un modello chat. Controlla errori di formato, fornisce statistiche di base e stima il numero di token per i costi di fine-tuning. Vedi: [Metodo di fine-tuning per gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning per Retrieval Augmented Generation (RAG) con Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | Lo scopo di questo notebook è guidarti attraverso un esempio completo di come fare il fine-tuning di modelli OpenAI per Retrieval Augmented Generation (RAG). Integreremo anche Qdrant e Few-Shot Learning per migliorare le prestazioni del modello e ridurre le invenzioni.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT con Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) è la piattaforma per sviluppatori AI, con strumenti per addestrare modelli, fare fine-tuning e sfruttare modelli foundation. Leggi prima la loro guida [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst), poi prova l’esercizio Cookbook.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning per Small Language Models                                                   | Scopri [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), il nuovo piccolo modello di Microsoft, sorprendentemente potente e compatto. Questo tutorial ti guida nel fine-tuning di Phi-2, mostrando come costruire un dataset unico e fare il fine-tuning usando QLoRA.                                                                                                                                                                       |
| **Hugging Face Tutorial** [Come fare il Fine-Tuning di LLM nel 2024 con Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                     | Questo post sul blog ti guida su come fare il fine-tuning di LLM open usando Hugging Face TRL, Transformers e datasets nel 2024. Definisci un caso d’uso, configura l’ambiente di sviluppo, prepara un dataset, fai il fine-tuning del modello, testalo e valutalo, poi distribuiscilo in produzione.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Offre un addestramento e distribuzione più rapidi e semplici di [modelli di machine learning all’avanguardia](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Il repo contiene tutorial compatibili con Colab e video guida su YouTube per il fine-tuning. **Riflette l’aggiornamento recente [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)**. Leggi la [documentazione AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Disclaimer**:  
Questo documento è stato tradotto utilizzando il servizio di traduzione automatica [Co-op Translator](https://github.com/Azure/co-op-translator). Pur impegnandoci per garantire l’accuratezza, si prega di notare che le traduzioni automatiche possono contenere errori o imprecisioni. Il documento originale nella sua lingua nativa deve essere considerato la fonte autorevole. Per informazioni critiche, si raccomanda una traduzione professionale effettuata da un umano. Non ci assumiamo alcuna responsabilità per eventuali malintesi o interpretazioni errate derivanti dall’uso di questa traduzione.