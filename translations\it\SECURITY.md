<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:53:14+00:00",
  "source_file": "SECURITY.md",
  "language_code": "it"
}
-->
## Sicurezza

Microsoft prende seriamente la sicurezza dei nostri prodotti software e servizi, inclusi tutti i repository di codice sorgente gestiti attraverso le nostre organizzazioni GitHub, che comprendono [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) e [le nostre organizzazioni GitHub](https://opensource.microsoft.com/).

Se ritieni di aver trovato una vulnerabilità di sicurezza in un repository di proprietà Microsoft che soddisfa la [definizione di vulnerabilità di sicurezza di Microsoft](https://aka.ms/opensource/security/definition), ti preghiamo di segnalarla come descritto di seguito.

## Segnalazione di problemi di sicurezza

**Ti preghiamo di non segnalare vulnerabilità di sicurezza tramite issue pubbliche su GitHub.**

Invece, ti invitiamo a segnalarle al Microsoft Security Response Center (MSRC) all’indirizzo [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Se preferisci inviare la segnalazione senza effettuare il login, manda una email a [<EMAIL>](mailto:<EMAIL>). Se possibile, cripta il messaggio con la nostra chiave PGP; puoi scaricarla dalla [pagina della chiave PGP del Microsoft Security Response Center](https://aka.ms/opensource/security/pgpkey).

Dovresti ricevere una risposta entro 24 ore. Se per qualche motivo non dovessi riceverla, ti preghiamo di inviare un sollecito via email per assicurarci di aver ricevuto il tuo messaggio originale. Ulteriori informazioni sono disponibili su [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Ti preghiamo di includere le informazioni richieste elencate di seguito (per quanto possibile) per aiutarci a comprendere meglio la natura e l’entità del possibile problema:

  * Tipo di problema (es. buffer overflow, SQL injection, cross-site scripting, ecc.)
  * Percorsi completi dei file sorgente correlati alla manifestazione del problema
  * La posizione del codice sorgente interessato (tag/branch/commit o URL diretto)
  * Qualsiasi configurazione speciale necessaria per riprodurre il problema
  * Istruzioni passo-passo per riprodurre il problema
  * Codice proof-of-concept o exploit (se possibile)
  * Impatto del problema, incluso come un attaccante potrebbe sfruttarlo

Queste informazioni ci aiuteranno a gestire la tua segnalazione più rapidamente.

Se stai segnalando nell’ambito di un bug bounty, report più completi possono contribuire a un premio più alto. Visita la nostra pagina del [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) per maggiori dettagli sui programmi attivi.

## Lingue preferite

Preferiamo che tutte le comunicazioni siano in inglese.

## Politica

Microsoft segue il principio di [Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd).

**Disclaimer**:  
Questo documento è stato tradotto utilizzando il servizio di traduzione automatica [Co-op Translator](https://github.com/Azure/co-op-translator). Pur impegnandoci per garantire accuratezza, si prega di notare che le traduzioni automatiche possono contenere errori o imprecisioni. Il documento originale nella sua lingua nativa deve essere considerato la fonte autorevole. Per informazioni critiche, si raccomanda una traduzione professionale effettuata da un umano. Non ci assumiamo alcuna responsabilità per eventuali malintesi o interpretazioni errate derivanti dall’uso di questa traduzione.