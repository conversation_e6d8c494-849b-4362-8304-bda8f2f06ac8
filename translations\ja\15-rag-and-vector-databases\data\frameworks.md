<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:27:44+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "ja"
}
-->
# ニューラルネットワークフレームワーク

すでに学んだように、ニューラルネットワークを効率的に訓練するためには、次の2つが必要です。

* テンソル上での演算（例えば、掛け算、足し算、シグモイドやソフトマックスなどの関数の計算）
* 勾配をすべての式について計算し、勾配降下法による最適化を行うこと

`numpy`ライブラリは前者は対応できますが、勾配を計算する仕組みが必要です。前のセクションで開発したフレームワークでは、`backward`メソッド内にすべての微分関数を手動でプログラムし、逆伝播を行っていました。理想的には、フレームワークは私たちが定義できる*任意の式*の勾配を計算できる機能を提供すべきです。

もう一つ重要なのは、GPUやTPUなどの特殊な計算ユニット上で計算を行えることです。深層ニューラルネットワークの訓練は*非常に多くの*計算を必要とし、GPU上でこれらの計算を並列化できることが非常に重要です。

> ✅ 「並列化(parallelize)」とは、計算を複数のデバイスに分散させることを意味します。

現在、最も人気のあるニューラルネットワークフレームワークはTensorFlowとPyTorchの2つです。どちらもCPUとGPUの両方でテンソルを操作するための低レベルAPIを提供しています。低レベルAPIの上には、それぞれKerasとPyTorch Lightningという高レベルAPIも存在します。

Low-Level API | TensorFlow| PyTorch
--------------|-------------------------------------|--------------------------------
High-level API| Keras| PyTorch

両フレームワークの**低レベルAPI**は、いわゆる**計算グラフ**を構築できます。このグラフは、与えられた入力パラメータから出力（通常は損失関数）をどのように計算するかを定義し、GPUが利用可能ならGPU上で計算を実行できます。この計算グラフを微分して勾配を計算する関数もあり、それを使ってモデルパラメータの最適化が可能です。

**高レベルAPI**はニューラルネットワークを**層の連なり**として捉え、ほとんどのニューラルネットワークの構築を非常に簡単にします。モデルの訓練は通常、データの準備を行い、`fit`関数を呼び出すだけで完了します。

高レベルAPIは多くの詳細を気にせずに典型的なニューラルネットワークを素早く構築できます。一方で、低レベルAPIは訓練プロセスをより細かく制御できるため、新しいニューラルネットワークアーキテクチャを扱う研究分野でよく使われます。

また、両方のAPIを組み合わせて使うことも可能です。例えば、低レベルAPIで独自のネットワーク層アーキテクチャを開発し、それを高レベルAPIで構築・訓練する大きなネットワーク内で使うことができます。あるいは、高レベルAPIで層の連なりとしてネットワークを定義し、独自の低レベルの訓練ループで最適化を行うこともできます。両APIは同じ基本的な概念を共有しており、連携して使えるよう設計されています。

## 学習について

このコースでは、PyTorchとTensorFlowの両方の内容を提供しています。お好みのフレームワークを選び、対応するノートブックだけを進めてください。どちらを選ぶか迷った場合は、インターネット上の**PyTorch vs. TensorFlow**に関する議論を参照すると良いでしょう。両方のフレームワークを触ってみるのも理解を深める助けになります。

可能な限り、簡単さのために高レベルAPIを使いますが、ニューラルネットワークの仕組みを根本から理解することも重要だと考えています。そのため、最初は低レベルAPIとテンソルを使って学習を始めます。ただし、詳細を学ぶ時間をかけたくない場合は、低レベルAPIの部分を飛ばして高レベルAPIのノートブックに直接進んでも構いません。

## ✍️ 演習：フレームワーク

以下のノートブックで学習を続けましょう。

Low-Level API | TensorFlow+Keras ノートブック | PyTorch
--------------|-------------------------------------|--------------------------------
High-level API| Keras | *PyTorch Lightning*

フレームワークを習得したら、過学習の概念を復習しましょう。

# 過学習（Overfitting）

過学習は機械学習において非常に重要な概念で、正しく理解することが大切です！

以下の5つの点（グラフ上では`x`で示されています）を近似する問題を考えます。

!linear | overfit
-------------------------|--------------------------
**線形モデル、パラメータ数2** | **非線形モデル、パラメータ数7**
訓練誤差 = 5.3 | 訓練誤差 = 0
検証誤差 = 5.1 | 検証誤差 = 20

* 左側は良い直線近似です。パラメータ数が適切なため、点の分布の傾向を正しく捉えています。
* 右側はモデルが強力すぎます。点は5つしかないのにパラメータが7つあるため、すべての点を通るように調整でき、訓練誤差は0になります。しかし、データの背後にある正しいパターンを理解できず、検証誤差が非常に大きくなっています。

モデルの複雑さ（パラメータ数）と訓練サンプル数のバランスを適切に取ることが非常に重要です。

## なぜ過学習が起こるのか

  * 訓練データが不足している
  * モデルが強力すぎる
  * 入力データにノイズが多すぎる

## 過学習の検出方法

上のグラフからわかるように、過学習は訓練誤差が非常に低く、検証誤差が高いことで検出できます。通常、訓練中は訓練誤差と検証誤差の両方が減少しますが、ある時点で検証誤差の減少が止まり、上昇し始めることがあります。これは過学習の兆候であり、その時点で訓練を止める（あるいはモデルのスナップショットを保存する）べきサインです。

overfitting

## 過学習の防止方法

過学習が起きているとわかったら、次のいずれかを試しましょう。

 * 訓練データの量を増やす
 * モデルの複雑さを減らす
 * Dropoutなどの正則化手法を使う（後で扱います）

## 過学習とバイアス・バリアンストレードオフ

過学習は統計学でいうバイアス・バリアンスのトレードオフ問題の一例です。モデルの誤差の原因を考えると、2種類の誤差があります。

* **バイアス誤差**は、アルゴリズムが訓練データの関係性を正しく捉えられないことによる誤差です。モデルが十分に強力でない場合（**アンダーフィッティング**）に起こります。
* **バリアンス誤差**は、モデルが入力データのノイズを意味のある関係として近似してしまうことによる誤差です（**過学習**）。

訓練が進むとバイアス誤差は減少しますが、バリアンス誤差は増加します。過学習を防ぐためには、訓練を手動で（過学習を検出した時点で）または正則化を導入して自動的に止めることが重要です。

## まとめ

このレッスンでは、TensorFlowとPyTorchという2つの人気AIフレームワークのAPIの違いについて学びました。また、非常に重要なトピックである過学習についても理解しました。

## 🚀 チャレンジ

付属のノートブックの最後に「課題」があります。ノートブックを進めながら課題をこなしてください。

## 復習と自主学習

以下のトピックについて調べてみましょう。

- TensorFlow
- PyTorch
- 過学習

次の質問を自問してみてください。

- TensorFlowとPyTorchの違いは何か？
- 過学習とアンダーフィッティングの違いは何か？

## 課題

このラボでは、PyTorchまたはTensorFlowを使って、単層および多層の全結合ネットワークによる2つの分類問題を解くことが求められます。

**免責事項**：  
本書類はAI翻訳サービス「[Co-op Translator](https://github.com/Azure/co-op-translator)」を使用して翻訳されました。正確性には努めておりますが、自動翻訳には誤りや不正確な部分が含まれる可能性があります。原文の言語によるオリジナル文書が正式な情報源とみなされるべきです。重要な情報については、専門の人間による翻訳を推奨します。本翻訳の利用により生じたいかなる誤解や誤訳についても、当方は一切の責任を負いかねます。