<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:42:48+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "ja"
}
-->
# ニューラルネットワーク入門：多層パーセプトロン

前のセクションでは、最も単純なニューラルネットワークモデルである一層パーセプトロン、すなわち線形の二クラス分類モデルについて学びました。

このセクションでは、このモデルをより柔軟なフレームワークに拡張し、以下を可能にします：

* 二クラス分類に加えて**多クラス分類**を行う
* 分類に加えて**回帰問題**を解く
* 線形分離不可能なクラスを分ける

また、Pythonで独自のモジュール式フレームワークを開発し、さまざまなニューラルネットワークの構造を構築できるようにします。

## 機械学習の形式化

まずは機械学習問題の形式化から始めましょう。訓練データセット**X**とラベル**Y**が与えられたとき、最も正確な予測を行うモデル*f*を構築する必要があります。予測の質は**損失関数**ℒで測定されます。よく使われる損失関数は以下の通りです：

* 回帰問題では、数値を予測するために、**絶対誤差** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>| や **二乗誤差** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup> を使います
* 分類問題では、**0-1損失**（モデルの**正解率**とほぼ同じ）や**ロジスティック損失**を使います

一層パーセプトロンでは、関数*f*は線形関数*f(x)=wx+b*（ここで*w*は重み行列、*x*は入力特徴ベクトル、*b*はバイアスベクトル）として定義されていました。異なるニューラルネットワークの構造では、この関数はより複雑な形をとることがあります。

> 分類の場合、ネットワークの出力として対応するクラスの確率を得ることが望ましいことが多いです。任意の数値を確率に変換（例えば出力の正規化）するために、よく**softmax**関数σを使い、関数*f*は*f(x)=σ(wx+b)*となります。

上記の*f*の定義において、*w*と*b*は**パラメータ**θ=⟨*w,b*⟩と呼ばれます。データセット⟨**X**,**Y**⟩が与えられたとき、パラメータθの関数として全体の誤差を計算できます。

> ✅ **ニューラルネットワークの学習の目的は、パラメータθを変化させて誤差を最小化することです**

## 勾配降下法による最適化

関数最適化のよく知られた手法に**勾配降下法**があります。これは損失関数のパラメータに関する微分（多次元の場合は**勾配**）を計算し、誤差が減少する方向にパラメータを更新する方法です。具体的には以下のように表せます：

* パラメータをランダムな初期値 w<sup>(0)</sup>, b<sup>(0)</sup> で初期化する
* 次のステップを何度も繰り返す：
    - w<sup>(i+1)</sup> = w<sup>(i)</sup> - η ∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup> - η ∂ℒ/∂b

学習中は、最適化のステップは全データセットを考慮して計算されるべきです（損失は全訓練サンプルの和として計算されることを思い出してください）。しかし実際には、**ミニバッチ**と呼ばれるデータの小さな部分集合を使い、その部分集合に基づいて勾配を計算します。毎回ランダムに部分集合を取るため、この方法は**確率的勾配降下法**（SGD）と呼ばれます。

## 多層パーセプトロンと逆伝播法

上で見たように、一層ネットワークは線形分離可能なクラスを分類できます。より豊かなモデルを作るために、複数の層を組み合わせることができます。数学的には、関数*f*はより複雑な形をとり、複数のステップで計算されます：

* z<sub>1</sub> = w<sub>1</sub>x + b<sub>1</sub>
* z<sub>2</sub> = w<sub>2</sub>α(z<sub>1</sub>) + b<sub>2</sub>
* f = σ(z<sub>2</sub>)

ここで、αは**非線形活性化関数**、σはsoftmax関数、パラメータはθ=⟨*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*⟩です。

勾配降下法のアルゴリズムは同じですが、勾配の計算はより複雑になります。連鎖律を用いて、微分は次のように計算できます：

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ 連鎖律は損失関数のパラメータに関する微分を計算するために使われます。

これらの式の左端はすべて同じなので、損失関数から計算グラフを「逆方向」にたどって効率的に微分を計算できます。これが多層パーセプトロンの学習方法である**逆伝播法**（backpropagation、略してbackprop）と呼ばれる理由です。

> TODO: 画像引用

> ✅ 逆伝播法については、ノートブックの例でより詳しく扱います。

## まとめ

このレッスンでは、独自のニューラルネットワークライブラリを構築し、簡単な2次元分類タスクに利用しました。

## 🚀 チャレンジ

付属のノートブックでは、多層パーセプトロンの構築と学習のための独自フレームワークを実装します。現代のニューラルネットワークがどのように動作するかを詳しく理解できるでしょう。

OwnFrameworkノートブックに進み、取り組んでみてください。

## 復習と自主学習

逆伝播法はAIや機械学習でよく使われるアルゴリズムで、より詳しく学ぶ価値があります。

## 課題

このラボでは、本レッスンで構築したフレームワークを使ってMNIST手書き数字分類を解いてください。

* 説明書
* ノートブック

**免責事項**：  
本書類はAI翻訳サービス「[Co-op Translator](https://github.com/Azure/co-op-translator)」を使用して翻訳されました。正確性の向上に努めておりますが、自動翻訳には誤りや不正確な部分が含まれる可能性があります。原文の言語によるオリジナル文書が正式な情報源とみなされるべきです。重要な情報については、専門の人間による翻訳を推奨します。本翻訳の利用により生じたいかなる誤解や誤訳についても、当方は一切の責任を負いかねます。