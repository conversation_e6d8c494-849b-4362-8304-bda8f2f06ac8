<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "68664f7e754a892ae1d8d5e2b7bd2081",
  "translation_date": "2025-07-09T17:38:06+00:00",
  "source_file": "18-fine-tuning/README.md",
  "language_code": "ja"
}
-->
[![Open Source Models](../../../translated_images/18-lesson-banner.f30176815b1a5074fce9cceba317720586caa99e24001231a92fd04eeb54a121.ja.png)](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst)

# LLMのファインチューニング

大規模言語モデルを使って生成AIアプリケーションを構築する際には、新たな課題が生じます。重要な問題の一つは、ユーザーのリクエストに対してモデルが生成するコンテンツの応答品質（正確性と関連性）を確保することです。これまでのレッスンでは、既存のモデルへの入力であるプロンプトを修正することで問題を解決しようとする、プロンプトエンジニアリングや検索強化生成といった手法について学びました。

本日のレッスンでは、3つ目の手法である**ファインチューニング**について説明します。これは、追加のデータを使ってモデル自体を再学習させることで課題に取り組む方法です。詳しく見ていきましょう。

## 学習目標

このレッスンでは、事前学習済み言語モデルのファインチューニングの概念を紹介し、このアプローチの利点と課題を探り、生成AIモデルの性能を向上させるためにファインチューニングをいつ、どのように使うべきかの指針を提供します。

レッスン終了時には、以下の質問に答えられるようになることを目指します。

- 言語モデルのファインチューニングとは何か？
- いつ、なぜファインチューニングが有効なのか？
- 事前学習済みモデルをどのようにファインチューニングできるか？
- ファインチューニングの限界は何か？

準備はいいですか？始めましょう。

## イラスト付きガイド

本格的に学習を始める前に、今回のレッスンで扱う内容の全体像を掴みたいですか？このイラスト付きガイドでは、ファインチューニングの基本概念や動機から、実際のファインチューニング作業のプロセスやベストプラクティスまでの学習の流れを説明しています。非常に興味深いテーマなので、ぜひ[Resources](./RESOURCES.md?WT.mc_id=academic-105485-koreyst)ページもチェックして、自己学習をサポートする追加リンクも活用してください！

![Illustrated Guide to Fine Tuning Language Models](../../../translated_images/18-fine-tuning-sketchnote.11b21f9ec8a703467a120cb79a28b5ac1effc8d8d9d5b31bbbac6b8640432e14.ja.png)

## 言語モデルのファインチューニングとは？

大規模言語モデルは、インターネットを含む多様なソースから収集した大量のテキストで事前学習されています。これまでのレッスンで学んだように、ユーザーの質問（プロンプト）に対するモデルの応答品質を向上させるためには、プロンプトエンジニアリングや検索強化生成のような技術が必要です。

よく使われるプロンプトエンジニアリングの手法の一つに、モデルに対して応答に期待する内容をより明確に示す方法があります。これは、_指示_（明示的なガイダンス）を与えたり、_いくつかの例_（暗黙的なガイダンス）を示したりすることです。これを少数ショット学習（few-shot learning）と呼びますが、以下の2つの制約があります。

- モデルのトークン制限により、与えられる例の数が制限され、効果が限定される。
- トークンコストが高くなり、毎回のプロンプトに例を追加するのが高価で柔軟性が低くなる。

ファインチューニングは、機械学習システムでよく使われる手法で、事前学習済みモデルを新しいデータで再学習させ、特定のタスクでの性能を向上させます。言語モデルの場合、特定のタスクや応用領域に合わせて厳選した例を使って事前学習済みモデルをファインチューニングし、そのタスクや領域により適した**カスタムモデル**を作成できます。ファインチューニングの副次的な利点として、少数ショット学習に必要な例の数を減らせるため、トークン使用量や関連コストの削減にもつながります。

## いつ、なぜモデルをファインチューニングすべきか？

ここでいうファインチューニングとは、**監督あり**のファインチューニングを指し、元の学習データセットに含まれていなかった**新しいデータを追加して再学習**することを意味します。これは、元のデータで異なるハイパーパラメータを使って再学習する無監督ファインチューニングとは異なります。

重要なのは、ファインチューニングは高度な技術であり、望む結果を得るには一定の専門知識が必要だということです。誤った方法で行うと、期待した改善が得られないだけでなく、対象ドメインでのモデル性能が低下する可能性もあります。

したがって、言語モデルのファインチューニング方法を学ぶ前に、「なぜ」この方法を選ぶべきか、「いつ」ファインチューニングを始めるべきかを理解する必要があります。まずは以下の質問を自問してください。

- **ユースケース**: ファインチューニングの目的は何か？現在の事前学習済みモデルのどの部分を改善したいのか？
- **代替手段**: 望む結果を得るために他の手法を試したか？それらを基準として比較できるか？
  - プロンプトエンジニアリング：関連するプロンプト応答の例を使った少数ショットプロンプトなどを試し、応答の質を評価する。
  - 検索強化生成：データ検索で得たクエリ結果をプロンプトに追加し、応答の質を評価する。
- **コスト**: ファインチューニングにかかるコストを把握しているか？
  - チューニング可能性：事前学習済みモデルはファインチューニング可能か？
  - 労力：トレーニングデータの準備、モデルの評価・改良にかかる労力
  - 計算資源：ファインチューニングジョブの実行やファインチューニング済みモデルのデプロイに必要な計算資源
  - データ：ファインチューニングに効果的な十分な質の例を入手できるか
- **利点**: ファインチューニングの利点を確認したか？
  - 品質：ファインチューニング済みモデルはベースラインを上回ったか？
  - コスト：プロンプトを簡素化してトークン使用量を削減できるか？
  - 拡張性：ベースモデルを新しいドメインに再利用できるか？

これらの質問に答えることで、ファインチューニングが自分のユースケースに適しているか判断できます。理想的には、利点がコストを上回る場合にのみこのアプローチを採用すべきです。進めると決めたら、次は事前学習済みモデルをどのようにファインチューニングするかを考えましょう。

意思決定プロセスについてさらに詳しく知りたい場合は、[To fine-tune or not to fine-tune](https://www.youtube.com/watch?v=0Jo-z-MFxJs)をご覧ください。

## 事前学習済みモデルをどのようにファインチューニングするか？

事前学習済みモデルをファインチューニングするには、以下が必要です。

- ファインチューニング対象の事前学習済みモデル
- ファインチューニングに使うデータセット
- ファインチューニングジョブを実行するトレーニング環境
- ファインチューニング済みモデルをデプロイするホスティング環境

## ファインチューニングの実例

以下のリソースは、選択したモデルと厳選されたデータセットを使った実例をステップバイステップで解説するチュートリアルです。これらのチュートリアルを進めるには、各プロバイダーのアカウントと該当モデルおよびデータセットへのアクセスが必要です。

| プロバイダー | チュートリアル                                                                                                                                                                | 説明                                                                                                                                                                                                                                                                                                                                                                                                                             |
| ------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| OpenAI       | [How to fine-tune chat models](https://github.com/openai/openai-cookbook/blob/main/examples/How_to_finetune_chat_models.ipynb?WT.mc_id=academic-105485-koreyst)               | `gpt-35-turbo`を特定のドメイン（「レシピアシスタント」）向けにファインチューニングする方法を学びます。トレーニングデータの準備、ファインチューニングジョブの実行、ファインチューニング済みモデルの推論利用を含みます。                                                                                                                                                                                                 |
| Azure OpenAI | [GPT 3.5 Turbo fine-tuning tutorial](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python-new%2Ccommand-line?WT.mc_id=academic-105485-koreyst) | Azure上で`gpt-35-turbo-0613`モデルをファインチューニングする方法を学びます。トレーニングデータの作成・アップロード、ファインチューニングジョブの実行、新モデルのデプロイと利用までの手順を解説しています。                                                                                                                                                                                                                   |
| Hugging Face | [Fine-tuning LLMs with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                            | [transformers](https://huggingface.co/docs/transformers/index?WT.mc_id=academic-105485-koreyst)ライブラリと[Transformer Reinforcement Learning (TRL)](https://huggingface.co/docs/trl/index?WT.mc_id=academic-105485-koreyst)を使い、Hugging Face上のオープンLLM（例：`CodeLlama 7B`）をファインチューニングする方法を紹介しています。オープンな[データセット](https://huggingface.co/docs/datasets/index?WT.mc_id=academic-105485-koreyst)も活用します。 |
|              |                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| 🤗 AutoTrain | [Fine-tuning LLMs with AutoTrain](https://github.com/huggingface/autotrain-advanced/?WT.mc_id=academic-105485-koreyst)                                                      | AutoTrain（またはAutoTrain Advanced）はHugging Faceが開発したPythonライブラリで、多様なタスクのファインチューニングをサポートします。LLMのファインチューニングも可能で、ノーコードで利用でき、自分のクラウド、Hugging Face Spaces、ローカル環境で実行できます。ウェブGUI、CLI、yaml設定ファイルによるトレーニングもサポートしています。                                                                                                   |
|              |                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                                                                                                                                 |

## 課題

上記のチュートリアルのいずれかを選んで実際に試してみてください。_これらのチュートリアルの最新版を直接利用することを推奨しますが、このリポジトリ内のJupyter Notebookで参考用にバージョンを複製する場合もあります。_

## よくできました！学習を続けましょう。

このレッスンを終えたら、[Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)をチェックして、生成AIの知識をさらに深めてください！

おめでとうございます！このコースのv2シリーズの最終レッスンを修了しました！学びと開発を止めずに続けてください。**このトピックに関する追加の提案リストは[RESOURCES](RESOURCES.md?WT.mc_id=academic-105485-koreyst)ページでご覧いただけます。**

また、v1シリーズのレッスンも課題や概念が追加されて更新されています。ぜひ知識をリフレッシュし、[質問やフィードバック](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst)を共有して、コミュニティのためにこれらのレッスンを改善する手助けをお願いします。

**免責事項**：  
本書類はAI翻訳サービス「[Co-op Translator](https://github.com/Azure/co-op-translator)」を使用して翻訳されました。正確性の向上に努めておりますが、自動翻訳には誤りや不正確な部分が含まれる可能性があります。原文の言語によるオリジナル文書が正式な情報源とみなされます。重要な情報については、専門の人間による翻訳を推奨します。本翻訳の利用により生じたいかなる誤解や誤訳についても、当方は責任を負いかねます。