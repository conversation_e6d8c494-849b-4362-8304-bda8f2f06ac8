<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:57:12+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "ja"
}
-->
# 自主学習のためのリソース

このレッスンは、用語やチュートリアルの参考としてOpenAIおよびAzure OpenAIの主要なリソースをいくつか使用して作成されました。以下は、あなた自身の自主学習の旅に役立つ非網羅的なリストです。

## 1. 主要リソース

| タイトル/リンク                                                                                                                                                                                                                   | 説明                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | ファインチューニングは、プロンプトに収まる数よりも多くの例で学習することで、少数ショット学習を改善し、コスト削減、応答品質の向上、低遅延リクエストを可能にします。**OpenAIによるファインチューニングの概要を確認しましょう。**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | **ファインチューニングとは何か（概念）**、なぜ検討すべきか（動機となる問題）、どのデータを使うか（トレーニング）、そして品質の測定方法を理解しましょう。                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Serviceでは、ファインチューニングを使ってモデルをあなたのデータセットに合わせてカスタマイズできます。Azure AI Studio、Python SDK、REST APIを使った**ファインチューニングの方法（プロセス）**を学びましょう。                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLMは特定のドメインやタスク、データセットでうまく機能しないことや、不正確または誤解を招く出力を生成することがあります。**ファインチューニングを検討すべきタイミング**について解説しています。                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | 継続的ファインチューニングは、すでにファインチューニングされたモデルをベースモデルとして選び、新しいトレーニング例で**さらにファインチューニングを行う反復的なプロセス**です。                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | 関数呼び出しの例を使ったファインチューニングにより、より正確で一貫した出力が得られ、同様のフォーマットの応答やコスト削減が可能になります。                                                                                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Azure OpenAIで**ファインチューニング可能なモデル**や利用可能なリージョン、トークン制限、トレーニングデータの有効期限を確認できる表です。                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | 2023年10月の30分間のAI Showエピソードで、ファインチューニングの利点や欠点、実践的な洞察を紹介し、この判断を助けます。                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | この**AIプレイブック**では、データ要件、フォーマット、ハイパーパラメータのファインチューニング、知っておくべき課題や制限について解説しています。                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | サンプルのファインチューニングデータセットの作成、ファインチューニングの準備、ジョブの作成、Azure上でのファインチューニングモデルのデプロイ方法を学べます。                                                                                                                                                                                    |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studioでは、ローコード開発者向けのUIベースのワークフローを使って、大規模言語モデルをあなたのデータセットに合わせてカスタマイズできます。この例を参照してください。                                                                                                                                                               |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Azure DataBricksとHugging Face Trainerライブラリを使い、単一GPUでHugging Faceモデルをファインチューニングする方法を説明しています。                                                                                                                                                |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Azure Machine Learningのモデルカタログには、特定のタスク向けにファインチューニング可能な多くのオープンソースモデルがあります。このモジュールは[AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)の一部です。 |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Microsoft Azure上でW&Bを使ってGPT-3.5やGPT-4モデルをファインチューニングし、モデル性能の詳細な追跡と分析が可能です。このガイドはOpenAIのファインチューニングガイドの概念を拡張し、Azure OpenAI向けの具体的な手順と機能を紹介しています。                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. 二次リソース

このセクションでは、今回のレッスンで時間の都合上カバーできなかったが、探求する価値のある追加リソースを紹介します。将来的なレッスンや副課題として取り上げられる可能性があります。今のところは、これらを使ってこのトピックに関する知識と専門性を深めてください。

| タイトル/リンク                                                                                                                                                                                                            | 説明                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Data preparation and analysis for chat model fine-tuning](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | このノートブックは、チャットモデルのファインチューニングに使うチャットデータセットの前処理と分析ツールです。フォーマットエラーのチェック、基本統計の提供、ファインチューニングコストのためのトークン数推定を行います。詳細は[Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)を参照してください。                                                                                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning for Retrieval Augmented Generation (RAG) with Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | このノートブックは、Retrieval Augmented Generation (RAG)のためにOpenAIモデルをファインチューニングする包括的な例を紹介します。QdrantとFew-Shot Learningを統合して、モデル性能の向上と誤情報の削減を目指します。                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT with Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B)は、モデルのトレーニングやファインチューニング、基盤モデルの活用に役立つAI開発プラットフォームです。まず[OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst)ガイドを読み、その後Cookbookの演習に挑戦してください。                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - Small Language Models向けファインチューニング                                                   | Microsoftの新しい小型モデル[Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst)は、驚くほど強力でコンパクトです。このチュートリアルでは、独自のデータセット作成とQLoRAを使ったPhi-2のファインチューニング方法を案内します。                                                                                                                                                                       |
| **Hugging Face Tutorial** [2024年のHugging FaceによるLLMファインチューニング方法](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | このブログ記事では、Hugging Face TRL、Transformers、datasetsを使ったオープンLLMのファインチューニング方法を2024年版として解説します。ユースケースの定義、開発環境のセットアップ、データセット準備、モデルのファインチューニング、テスト評価、そして本番環境へのデプロイまでをカバーします。                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | 最新の[最先端機械学習モデル](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst)の高速かつ簡単なトレーニングとデプロイを実現します。Colab対応のチュートリアルとYouTube動画ガイドがあり、ファインチューニングに最適です。**最近の[local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)アップデートを反映しています。** [AutoTrainドキュメント](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst)もご覧ください。 |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**免責事項**：  
本書類はAI翻訳サービス「[Co-op Translator](https://github.com/Azure/co-op-translator)」を使用して翻訳されました。正確性の向上に努めておりますが、自動翻訳には誤りや不正確な部分が含まれる可能性があります。原文の言語によるオリジナル文書が正式な情報源とみなされるべきです。重要な情報については、専門の人間による翻訳を推奨します。本翻訳の利用により生じたいかなる誤解や誤訳についても、当方は一切の責任を負いかねます。