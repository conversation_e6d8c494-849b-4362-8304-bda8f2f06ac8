<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:23:49+00:00",
  "source_file": "README.md",
  "language_code": "ja"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.ja.png)

### ジェネレーティブAIアプリケーション構築に必要なすべてを学べる21のレッスン

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 多言語対応

#### GitHub Actionによるサポート（自動化＆常に最新）

[French](../fr/README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](./README.md) | [Korean](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](../el/README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](../he/README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](../sk/README.md) | [Romanian](../ro/README.md) | [Bulgarian](../bg/README.md) | [Serbian (Cyrillic)](../sr/README.md) | [Croatian](../hr/README.md) | [Slovenian](../sl/README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Generative AI for Beginners (Version 3) - コース

Microsoft Cloud Advocatesによる21レッスンの包括的なコースで、ジェネレーティブAIアプリケーション構築の基礎を学びましょう。

## 🌱 はじめに

このコースは21のレッスンで構成されています。各レッスンは独立したトピックを扱っているので、好きなところから始めてください！

レッスンは「Learn」レッスン（ジェネレーティブAIの概念を解説）と、「Build」レッスン（概念の説明と可能な限り**Python**と**TypeScript**のコード例を含む）に分かれています。

.NET開発者の方は[Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)もご覧ください！

各レッスンには追加の学習ツールを紹介する「Keep Learning」セクションもあります。

## 必要なもの
### このコースのコードを実行するには、以下のいずれかを使用できます：
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **対象レッスン:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **対象レッスン:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **対象レッスン:** "oai-assignment" 
   
- PythonまたはTypeScriptの基本知識があると便利です - \*完全な初心者の方はこれらの[Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst)と[TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst)コースをチェックしてください
- このリポジトリを自分のGitHubアカウントに[フォーク](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst)するためのGitHubアカウント

開発環境のセットアップをサポートする**[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)**レッスンも用意しています。

後で見つけやすくするために、このリポジトリに[スター(🌟)を付ける](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst)のを忘れないでください。

## 🧠 デプロイの準備はできましたか？

より高度なコードサンプルをお探しなら、**Python**と**TypeScript**両方の[ジェネレーティブAIコードサンプル集](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst)をご覧ください。

## 🗣️ 他の学習者と交流、サポートを受ける

このコースを受講している他の学習者と交流し、サポートを受けるには[公式Azure AI Foundry Discordサーバー](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)に参加しましょう。

質問や製品フィードバックは[Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum)（GitHub上）でどうぞ。

## 🚀 スタートアップを立ち上げる？

[Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst)に登録すると、**無料のOpenAIクレジット**や、Azure OpenAIサービスを通じてOpenAIモデルにアクセスするための最大15万ドル分のAzureクレジットが受け取れます。

## 🙏 協力したいですか？

提案やスペルミス、コードの誤りを見つけたら、[Issueを投稿](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst)するか、[プルリクエストを作成](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)してください。

## 📂 各レッスンには以下が含まれます：

- トピックの短いビデオ紹介
- READMEに記載されたテキストレッスン
- Azure OpenAIおよびOpenAI APIをサポートするPythonとTypeScriptのコードサンプル
- 学習を続けるための追加リソースへのリンク

## 🗃️ レッスン一覧

| #   | **レッスンリンク**                                                                                                                              | **説明**                                                                                 | **ビデオ**                                                                   | **追加学習**                                                             |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Learn:** 開発環境のセットアップ方法                                                  | ビデオは近日公開予定                                                        | [詳しく学ぶ](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Introduction to Generative AI and LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                              | **Learn:** ジェネレーティブAIとは何か、大規模言語モデル（LLM）の仕組みを理解する         | [ビデオ](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [詳しく学ぶ](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Exploring and comparing different LLMs](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** 用途に合ったモデルの選び方                                                  | [ビデオ](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [詳しく学ぶ](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Using Generative AI Responsibly](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                           | **Learn:** ジェネレーティブAIアプリケーションを責任を持って構築する方法                 | [ビデオ](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [詳しく学ぶ](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Understanding Prompt Engineering Fundamentals](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** 実践的なプロンプトエンジニアリングの基本                                    | [ビデオ](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [詳しく学ぶ](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Creating Advanced Prompts](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **Learn:** プロンプトの結果を向上させるための高度なテクニックの応用                     | [ビデオ](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [詳しく学ぶ](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [テキスト生成アプリケーションの構築](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **構築:** Azure OpenAI / OpenAI API を使ったテキスト生成アプリ                                | [ビデオ](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [チャットアプリケーションの構築](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **構築:** 効率的なチャットアプリの構築と統合の手法               | [ビデオ](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [検索アプリとベクトルデータベースの構築](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **構築:** Embeddings を使ったデータ検索アプリ                        | [ビデオ](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [画像生成アプリケーションの構築](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **構築:** 画像生成アプリケーション                                                       | [ビデオ](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [ローコードAIアプリケーションの構築](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **構築:** ローコードツールを使った生成AIアプリ                                     | [ビデオ](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Function Calling を使った外部アプリケーションの統合](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **構築:** function calling とは何か、アプリケーションでの活用例                          | [ビデオ](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [AIアプリケーションのUX設計](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **学習:** 生成AIアプリ開発におけるUXデザインの原則の適用方法         | [ビデオ](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [生成AIアプリケーションのセキュリティ](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **学習:** AIシステムに対する脅威とリスク、そしてそれらを守る方法             | [ビデオ](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [生成AIアプリケーションのライフサイクル](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **学習:** LLMのライフサイクル管理とLLMOpsのためのツールと指標                         | [ビデオ](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) とベクトルデータベース](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **構築:** RAGフレームワークを使い、ベクトルデータベースからembeddingを取得するアプリ  | [ビデオ](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [オープンソースモデルとHugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **構築:** Hugging Faceで利用可能なオープンソースモデルを使ったアプリ                    | [ビデオ](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AIエージェント](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **構築:** AIエージェントフレームワークを使ったアプリ                                           | [ビデオ](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [LLMのファインチューニング](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **学習:** LLMのファインチューニングとは何か、なぜ必要か、どう行うか                                            | [ビデオ](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [SLMを使った構築](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **学習:** Small Language Modelsを使うメリット                                            | ビデオは近日公開予定 | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Mistralモデルを使った構築](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **学習:** Mistralファミリーモデルの特徴と違い                                           | ビデオは近日公開予定 | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Metaモデルを使った構築](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **学習:** Metaファミリーモデルの特徴と違い                                           | ビデオは近日公開予定 | [詳細はこちら](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 特別な感謝

すべてのGitHub Actionsとワークフローを作成してくださった[**John Aziz**](https://www.linkedin.com/in/john0isaac/)に特別な感謝を。

また、各レッスンの学習者体験とコード体験を向上させるために重要な貢献をしてくださった[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/)にも感謝します。

## 🎒 その他のコース

私たちのチームは他にもコースを提供しています！ぜひご覧ください：

- [**NEW** Model Context Protocol for Beginners](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI Agents for Beginners](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Generative AI for Beginners using .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Generative AI for Beginners using JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML for Beginners](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science for Beginners](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI for Beginners](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Cybersecurity for Beginners](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Web Dev for Beginners](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT for Beginners](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR Development for Beginners](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Mastering GitHub Copilot for AI Paired Programming](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Mastering GitHub Copilot for C#/.NET Developers](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Choose Your Own Copilot Adventure](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**免責事項**：  
本書類はAI翻訳サービス「[Co-op Translator](https://github.com/Azure/co-op-translator)」を使用して翻訳されました。正確性を期しておりますが、自動翻訳には誤りや不正確な部分が含まれる可能性があります。原文の言語によるオリジナル文書が正式な情報源とみなされます。重要な情報については、専門の人間による翻訳を推奨します。本翻訳の利用により生じた誤解や誤訳について、当方は一切の責任を負いかねます。