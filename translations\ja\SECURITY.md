<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:51:51+00:00",
  "source_file": "SECURITY.md",
  "language_code": "ja"
}
-->
## セキュリティ

Microsoftは、当社のソフトウェア製品およびサービスのセキュリティを非常に重視しており、これには[Microsoft](https://github.com/microsoft)、[Azure](https://github.com/Azure)、[DotNet](https://github.com/dotnet)、[AspNet](https://github.com/aspnet)、[Xamarin](https://github.com/xamarin)、および[当社のGitHub組織](https://opensource.microsoft.com/)を通じて管理されているすべてのソースコードリポジトリが含まれます。

もし、[Microsoftのセキュリティ脆弱性の定義](https://aka.ms/opensource/security/definition)に該当するMicrosoft所有のリポジトリでセキュリティ脆弱性を発見したと思われる場合は、以下の方法でご報告ください。

## セキュリティ問題の報告

**セキュリティ脆弱性を公開のGitHub Issuesで報告しないでください。**

代わりに、Microsoft Security Response Center (MSRC) のウェブサイト [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report) からご報告ください。

ログインせずに報告したい場合は、[<EMAIL>](mailto:<EMAIL>) へメールをお送りください。可能であれば、当社のPGPキーでメッセージを暗号化してください。PGPキーは[Microsoft Security Response Center PGP Keyページ](https://aka.ms/opensource/security/pgpkey)からダウンロードできます。

通常、24時間以内に返信を受け取るはずです。もし何らかの理由で返信がない場合は、メールで再度ご連絡いただき、当社が元のメッセージを受け取っているかご確認ください。詳細は[microsoft.com/msrc](https://aka.ms/opensource/security/msrc)にてご覧いただけます。

以下の情報（可能な限り）を含めていただくと、問題の性質や範囲をより正確に把握するのに役立ちます：

  * 問題の種類（例：バッファオーバーフロー、SQLインジェクション、クロスサイトスクリプティングなど）
  * 問題が発生しているソースファイルのフルパス
  * 影響を受けるソースコードの場所（タグ／ブランチ／コミット、または直接のURL）
  * 問題を再現するために必要な特別な設定
  * 問題を再現するための手順
  * 証明概念やエクスプロイトコード（可能な場合）
  * 問題の影響範囲、攻撃者がどのように悪用できるか

これらの情報は、報告内容の迅速な評価に役立ちます。

バグバウンティの報告の場合、より詳細な報告はより高い報酬につながる可能性があります。詳細は[Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty)のページをご覧ください。

## 推奨言語

すべてのコミュニケーションは英語で行うことを推奨します。

## ポリシー

Microsoftは[Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd)の原則に従っています。

**免責事項**：  
本書類はAI翻訳サービス「[Co-op Translator](https://github.com/Azure/co-op-translator)」を使用して翻訳されました。正確性を期しておりますが、自動翻訳には誤りや不正確な部分が含まれる可能性があります。原文の言語による文書が正式な情報源とみなされるべきです。重要な情報については、専門の人間による翻訳を推奨します。本翻訳の利用により生じた誤解や誤訳について、当方は一切の責任を負いかねます。