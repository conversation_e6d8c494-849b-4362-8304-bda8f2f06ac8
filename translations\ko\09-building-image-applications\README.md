<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "1a7fd0f95f9eb673b79da47c0814f4d4",
  "translation_date": "2025-07-09T13:19:40+00:00",
  "source_file": "09-building-image-applications/README.md",
  "language_code": "ko"
}
-->
# 이미지 생성 애플리케이션 만들기

[![Building Image Generation Applications](../../../translated_images/09-lesson-banner.906e408c741f44112ff5da17492a30d3872abb52b8530d6506c2631e86e704d0.ko.png)](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)

LLM은 단순한 텍스트 생성 그 이상입니다. 텍스트 설명으로부터 이미지를 생성하는 것도 가능합니다. 이미지라는 모달리티는 MedTech, 건축, 관광, 게임 개발 등 다양한 분야에서 매우 유용하게 활용될 수 있습니다. 이번 장에서는 가장 인기 있는 두 가지 이미지 생성 모델인 DALL-E와 Midjourney에 대해 살펴보겠습니다.

## 소개

이번 수업에서는 다음 내용을 다룹니다:

- 이미지 생성과 그 유용성
- DALL-E와 Midjourney가 무엇이며, 어떻게 작동하는지
- 이미지 생성 애플리케이션을 만드는 방법

## 학습 목표

이 수업을 마치면 다음을 할 수 있습니다:

- 이미지 생성 애플리케이션을 구축할 수 있습니다.
- 메타 프롬프트로 애플리케이션의 경계를 정의할 수 있습니다.
- DALL-E와 Midjourney를 활용할 수 있습니다.

## 왜 이미지 생성 애플리케이션을 만들까요?

이미지 생성 애플리케이션은 생성형 AI의 가능성을 탐구하는 훌륭한 방법입니다. 예를 들어 다음과 같은 용도로 활용할 수 있습니다:

- **이미지 편집 및 합성**: 이미지 편집이나 합성 등 다양한 용도로 이미지를 생성할 수 있습니다.

- **다양한 산업에 적용 가능**: MedTech, 관광, 게임 개발 등 여러 산업 분야에서 이미지를 생성하는 데 활용할 수 있습니다.

## 시나리오: Edu4All

이번 수업에서는 스타트업 Edu4All과 함께 작업을 이어갑니다. 학생들은 평가 과제를 위해 이미지를 생성할 것입니다. 어떤 이미지를 만들지는 학생들의 자유지만, 자신만의 동화를 위한 삽화, 이야기 속 새로운 캐릭터, 아이디어와 개념을 시각화하는 데 활용할 수 있습니다.

예를 들어, 학생들이 수업에서 기념물을 주제로 작업한다면 다음과 같은 이미지를 생성할 수 있습니다:

![Edu4All startup, class on monuments, Eiffel Tower](../../../translated_images/startup.94d6b79cc4bb3f5afbf6e2ddfcf309aa5d1e256b5f30cc41d252024eaa9cc5dc.ko.png)

다음과 같은 프롬프트를 사용하여

> "Dog next to Eiffel Tower in early morning sunlight"

## DALL-E와 Midjourney란 무엇인가요?

[DALL-E](https://openai.com/dall-e-2?WT.mc_id=academic-105485-koreyst)와 [Midjourney](https://www.midjourney.com/?WT.mc_id=academic-105485-koreyst)는 가장 인기 있는 이미지 생성 모델 두 가지로, 프롬프트를 사용해 이미지를 생성할 수 있습니다.

### DALL-E

먼저 DALL-E부터 살펴보겠습니다. DALL-E는 텍스트 설명으로부터 이미지를 생성하는 생성형 AI 모델입니다.

> [DALL-E는 CLIP과 diffused attention 두 모델의 결합입니다](https://towardsdatascience.com/openais-dall-e-and-clip-101-a-brief-introduction-3a4367280d4e?WT.mc_id=academic-105485-koreyst).

- **CLIP**은 이미지와 텍스트로부터 임베딩(데이터의 수치적 표현)을 생성하는 모델입니다.

- **Diffused attention**은 임베딩으로부터 이미지를 생성하는 모델입니다. DALL-E는 이미지와 텍스트 데이터셋으로 학습되어 텍스트 설명으로부터 이미지를 생성할 수 있습니다. 예를 들어, 모자를 쓴 고양이나 모호크 헤어스타일을 한 개의 이미지를 생성할 수 있습니다.

### Midjourney

Midjourney도 DALL-E와 비슷한 방식으로 작동하며, 텍스트 프롬프트로부터 이미지를 생성합니다. “모자를 쓴 고양이”나 “모호크 헤어스타일을 한 개” 같은 프롬프트로 이미지를 만들 수 있습니다.

![Image generated by Midjourney, mechanical pigeon](https://upload.wikimedia.org/wikipedia/commons/thumb/8/8c/Rupert_Breheny_mechanical_dove_eca144e7-476d-4976-821d-a49c408e4f36.png/440px-Rupert_Breheny_mechanical_dove_eca144e7-476d-4976-821d-a49c408e4f36.png?WT.mc_id=academic-105485-koreyst)  
_이미지 출처: Wikipedia, Midjourney가 생성한 이미지_

## DALL-E와 Midjourney는 어떻게 작동하나요?

먼저 [DALL-E](https://arxiv.org/pdf/2102.12092.pdf?WT.mc_id=academic-105485-koreyst)를 살펴보겠습니다. DALL-E는 트랜스포머 아키텍처 기반의 생성형 AI 모델로, _자기회귀 트랜스포머(autoregressive transformer)_를 사용합니다.

_자기회귀 트랜스포머_는 텍스트 설명으로부터 이미지를 생성하는 방식을 정의하며, 한 번에 한 픽셀씩 생성하고, 생성된 픽셀을 바탕으로 다음 픽셀을 생성합니다. 신경망의 여러 층을 거치면서 이미지가 완성됩니다.

이 과정을 통해 DALL-E는 이미지 내 속성, 객체, 특징 등을 제어할 수 있습니다. 다만 DALL-E 2와 3은 생성 이미지에 대해 더 세밀한 제어가 가능합니다.

## 첫 번째 이미지 생성 애플리케이션 만들기

이미지 생성 애플리케이션을 만들려면 어떤 것이 필요할까요? 다음 라이브러리를 준비해야 합니다:

- **python-dotenv**: 비밀 정보를 코드와 분리해 _.env_ 파일에 보관할 때 권장되는 라이브러리입니다.
- **openai**: OpenAI API와 상호작용할 때 사용하는 라이브러리입니다.
- **pillow**: 파이썬에서 이미지를 다룰 때 사용합니다.
- **requests**: HTTP 요청을 보낼 때 도움을 줍니다.

1. _.env_ 파일을 만들고 다음 내용을 입력하세요:

   ```text
   AZURE_OPENAI_ENDPOINT=<your endpoint>
   AZURE_OPENAI_API_KEY=<your key>
   ```

   이 정보는 Azure Portal의 "Keys and Endpoint" 섹션에서 확인할 수 있습니다.

1. 위 라이브러리들을 _requirements.txt_ 파일에 다음과 같이 적습니다:

   ```text
   python-dotenv
   openai
   pillow
   requests
   ```

1. 가상 환경을 만들고 라이브러리를 설치합니다:

   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

   Windows에서는 다음 명령어로 가상 환경을 만들고 활성화하세요:

   ```bash
   python3 -m venv venv
   venv\Scripts\activate.bat
   ```

1. _app.py_ 파일에 다음 코드를 추가합니다:

   ```python
   import openai
   import os
   import requests
   from PIL import Image
   import dotenv

   # import dotenv
   dotenv.load_dotenv()

   # Get endpoint and key from environment variables
   openai.api_base = os.environ['AZURE_OPENAI_ENDPOINT']
   openai.api_key = os.environ['AZURE_OPENAI_API_KEY']

   # Assign the API version (DALL-E is currently supported for the 2023-06-01-preview API version only)
   openai.api_version = '2023-06-01-preview'
   openai.api_type = 'azure'


   try:
       # Create an image by using the image generation API
       generation_response = openai.Image.create(
           prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
           size='1024x1024',
           n=2,
           temperature=0,
       )
       # Set the directory for the stored image
       image_dir = os.path.join(os.curdir, 'images')

       # If the directory doesn't exist, create it
       if not os.path.isdir(image_dir):
           os.mkdir(image_dir)

       # Initialize the image path (note the filetype should be png)
       image_path = os.path.join(image_dir, 'generated-image.png')

       # Retrieve the generated image
       image_url = generation_response["data"][0]["url"]  # extract image URL from response
       generated_image = requests.get(image_url).content  # download the image
       with open(image_path, "wb") as image_file:
           image_file.write(generated_image)

       # Display the image in the default image viewer
       image = Image.open(image_path)
       image.show()

   # catch exceptions
   except openai.InvalidRequestError as err:
       print(err)

   ```

코드를 설명해 보겠습니다:

- 먼저 OpenAI, dotenv, requests, Pillow 라이브러리를 임포트합니다.

  ```python
  import openai
  import os
  import requests
  from PIL import Image
  import dotenv
  ```

- 다음으로 _.env_ 파일에서 환경 변수를 불러옵니다.

  ```python
  # import dotenv
  dotenv.load_dotenv()
  ```

- 그 후 OpenAI API의 엔드포인트, 키, 버전, 타입을 설정합니다.

  ```python
  # Get endpoint and key from environment variables
  openai.api_base = os.environ['AZURE_OPENAI_ENDPOINT']
  openai.api_key = os.environ['AZURE_OPENAI_API_KEY']

  # add version and type, Azure specific
  openai.api_version = '2023-06-01-preview'
  openai.api_type = 'azure'
  ```

- 이제 이미지를 생성합니다:

  ```python
  # Create an image by using the image generation API
  generation_response = openai.Image.create(
      prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
      size='1024x1024',
      n=2,
      temperature=0,
  )
  ```

  위 코드는 생성된 이미지의 URL을 포함한 JSON 객체를 반환합니다. 이 URL을 사용해 이미지를 다운로드하고 파일로 저장할 수 있습니다.

- 마지막으로 이미지를 열어 기본 이미지 뷰어로 표시합니다:

  ```python
  image = Image.open(image_path)
  image.show()
  ```

### 이미지 생성 코드 자세히 보기

이미지를 생성하는 코드를 좀 더 자세히 살펴보겠습니다:

```python
generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2,
        temperature=0,
    )
```

- **prompt**: 이미지를 생성하는 데 사용되는 텍스트 프롬프트입니다. 여기서는 "Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils"를 사용했습니다.
- **size**: 생성할 이미지 크기입니다. 여기서는 1024x1024 픽셀 크기의 이미지를 생성합니다.
- **n**: 생성할 이미지 수입니다. 여기서는 두 장의 이미지를 생성합니다.
- **temperature**: 생성형 AI 모델 출력의 무작위성을 조절하는 매개변수입니다. 0에서 1 사이의 값이며, 0은 결정론적(항상 같은 결과), 1은 무작위적 출력을 의미합니다. 기본값은 0.7입니다.

다음 섹션에서 이미지와 관련해 더 다양한 기능을 다룰 예정입니다.

## 이미지 생성의 추가 기능

지금까지 파이썬 몇 줄로 이미지를 생성하는 방법을 보았습니다. 하지만 이미지로 할 수 있는 일은 더 많습니다.

다음과 같은 작업도 가능합니다:

- **편집 수행**: 기존 이미지에 마스크와 프롬프트를 제공해 이미지를 수정할 수 있습니다. 예를 들어, 토끼 이미지에 모자를 씌우는 작업이 가능합니다. 이미지, 변경할 영역을 지정하는 마스크, 그리고 수행할 작업을 설명하는 텍스트 프롬프트를 함께 제공합니다.

  ```python
  response = openai.Image.create_edit(
    image=open("base_image.png", "rb"),
    mask=open("mask.png", "rb"),
    prompt="An image of a rabbit with a hat on its head.",
    n=1,
    size="1024x1024"
  )
  image_url = response['data'][0]['url']
  ```

  기본 이미지는 토끼만 포함하지만, 최종 이미지는 토끼가 모자를 쓴 모습이 됩니다.

- **변형 생성**: 기존 이미지를 바탕으로 변형 이미지를 만들 수 있습니다. 변형을 만들려면 이미지와 텍스트 프롬프트를 제공하고 다음과 같이 코드를 작성합니다:

  ```python
  response = openai.Image.create_variation(
    image=open("bunny-lollipop.png", "rb"),
    n=1,
    size="1024x1024"
  )
  image_url = response['data'][0]['url']
  ```

  > 참고: 이 기능은 OpenAI에서만 지원됩니다.

## 온도(Temperature)

온도는 생성형 AI 모델 출력의 무작위성을 조절하는 매개변수입니다. 0에서 1 사이의 값이며, 0은 결정론적(항상 같은 결과), 1은 무작위적 출력을 의미합니다. 기본값은 0.7입니다.

온도가 어떻게 작동하는지 예를 들어 보겠습니다. 다음 프롬프트를 두 번 실행해 봅니다:

> 프롬프트: "Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils"

![Bunny on a horse holding a lollipop, version 1](../../../translated_images/v1-generated-image.a295cfcffa3c13c2432eb1e41de7e49a78c814000fb1b462234be24b6e0db7ea.ko.png)

이제 같은 프롬프트를 다시 실행해 보겠습니다. 같은 이미지가 나오지 않는 것을 확인할 수 있습니다:

![Generated image of bunny on horse](../../../translated_images/v2-generated-image.33f55a3714efe61dc19622c869ba6cd7d6e6de562e26e95b5810486187aace39.ko.png)

보시다시피 두 이미지는 비슷하지만 동일하지 않습니다. 이번에는 온도 값을 0.1로 바꿔서 어떤 변화가 있는지 살펴봅시다:

```python
 generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2
    )
```

### 온도 값 변경하기

출력을 좀 더 결정론적으로 만들기 위해 온도를 낮춰보겠습니다. 앞서 생성한 두 이미지를 보면 첫 번째는 토끼가 있고 두 번째는 말이 있어 이미지가 크게 다릅니다.

따라서 코드를 수정해 온도를 0으로 설정해 보겠습니다:

```python
generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2,
        temperature=0
    )
```

이 코드를 실행하면 다음 두 이미지를 얻을 수 있습니다:

- ![Temperature 0, v1](../../../translated_images/v1-temp-generated-image.a4346e1d2360a056d855ee3dfcedcce91211747967cb882e7d2eff2076f90e4a.ko.png)
- ![Temperature 0 , v2](../../../translated_images/v2-temp-generated-image.871d0c920dbfb0f1cb5d9d80bffd52da9b41f83b386320d9a9998635630ec83d.ko.png)

여기서는 이미지들이 서로 훨씬 더 닮았다는 것을 명확히 알 수 있습니다.

## 메타프롬프트로 애플리케이션 경계 정의하기

우리 데모에서는 이미 고객을 위한 이미지를 생성할 수 있습니다. 하지만 애플리케이션에 일정한 경계를 설정할 필요가 있습니다.

예를 들어, 업무에 부적합하거나 어린이에게 적절하지 않은 이미지는 생성하지 않도록 해야 합니다.

이를 위해 _메타프롬프트(metaprompts)_를 사용할 수 있습니다. 메타프롬프트는 생성형 AI 모델의 출력을 제어하는 텍스트 프롬프트입니다. 예를 들어, 메타프롬프트를 사용해 생성된 이미지가 업무에 적합하고 어린이에게도 적절하도록 제어할 수 있습니다.

### 어떻게 작동하나요?

메타프롬프트는 생성형 AI 모델의 출력을 제어하는 텍스트 프롬프트로, 일반 프롬프트 앞에 위치합니다. 모델 출력을 제어하기 위해 애플리케이션에 내장되어, 프롬프트 입력과 메타프롬프트 입력을 하나의 텍스트 프롬프트로 감쌉니다.

메타프롬프트의 예시는 다음과 같습니다:

```text
You are an assistant designer that creates images for children.

The image needs to be safe for work and appropriate for children.

The image needs to be in color.

The image needs to be in landscape orientation.

The image needs to be in a 16:9 aspect ratio.

Do not consider any input from the following that is not safe for work or appropriate for children.

(Input)

```

이제 데모에서 메타프롬프트를 어떻게 사용하는지 살펴보겠습니다.

```python
disallow_list = "swords, violence, blood, gore, nudity, sexual content, adult content, adult themes, adult language, adult humor, adult jokes, adult situations, adult"

meta_prompt =f"""You are an assistant designer that creates images for children.

The image needs to be safe for work and appropriate for children.

The image needs to be in color.

The image needs to be in landscape orientation.

The image needs to be in a 16:9 aspect ratio.

Do not consider any input from the following that is not safe for work or appropriate for children.
{disallow_list}
"""

prompt = f"{meta_prompt}
Create an image of a bunny on a horse, holding a lollipop"

# TODO add request to generate image
```

위 프롬프트를 보면 생성되는 모든 이미지가 메타프롬프트를 고려하는 것을 알 수 있습니다.

## 과제 - 학생들이 이미지 생성할 수 있도록 하기

수업 초반에 소개한 Edu4All을 기억하시나요? 이제 학생들이 평가 과제를 위해 이미지를 생성할 수 있도록 해봅시다.

학생들은 평가 과제에 사용할 기념물 이미지를 생성할 것입니다. 어떤 기념물을 선택할지는 학생들의 자유이며, 다양한 맥락에 기념물을 배치하는 창의력을 발휘하도록 요청받습니다.

## 해결책

가능한 해결책 중 하나는 다음과 같습니다:

```python
import openai
import os
import requests
from PIL import Image
import dotenv

# import dotenv
dotenv.load_dotenv()

# Get endpoint and key from environment variables
openai.api_base = "<replace with endpoint>"
openai.api_key = "<replace with api key>"

# Assign the API version (DALL-E is currently supported for the 2023-06-01-preview API version only)
openai.api_version = '2023-06-01-preview'
openai.api_type = 'azure'

disallow_list = "swords, violence, blood, gore, nudity, sexual content, adult content, adult themes, adult language, adult humor, adult jokes, adult situations, adult"

meta_prompt = f"""You are an assistant designer that creates images for children.

The image needs to be safe for work and appropriate for children.

The image needs to be in color.

The image needs to be in landscape orientation.

The image needs to be in a 16:9 aspect ratio.

Do not consider any input from the following that is not safe for work or appropriate for children.
{disallow_list}"""

prompt = f"""{meta_prompt}
Generate monument of the Arc of Triumph in Paris, France, in the evening light with a small child holding a Teddy looks on.
""""

try:
    # Create an image by using the image generation API
    generation_response = openai.Image.create(
        prompt=prompt,    # Enter your prompt text here
        size='1024x1024',
        n=2,
        temperature=0,
    )
    # Set the directory for the stored image
    image_dir = os.path.join(os.curdir, 'images')

    # If the directory doesn't exist, create it
    if not os.path.isdir(image_dir):
        os.mkdir(image_dir)

    # Initialize the image path (note the filetype should be png)
    image_path = os.path.join(image_dir, 'generated-image.png')

    # Retrieve the generated image
    image_url = generation_response["data"][0]["url"]  # extract image URL from response
    generated_image = requests.get(image_url).content  # download the image
    with open(image_path, "wb") as image_file:
        image_file.write(generated_image)

    # Display the image in the default image viewer
    image = Image.open(image_path)
    image.show()

# catch exceptions
except openai.InvalidRequestError as err:
    print(err)
```

## 훌륭합니다! 학습을 계속하세요

이번 수업을 마친 후에는 [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)을 확인해 생성형 AI 지식을 더욱 향상시키세요!

다음 10강에서는 [로우코드로 AI 애플리케이션을 만드는 방법](../10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)을 다룹니다.

**면책 조항**:  
이 문서는 AI 번역 서비스 [Co-op Translator](https://github.com/Azure/co-op-translator)를 사용하여 번역되었습니다. 정확성을 위해 최선을 다하고 있으나, 자동 번역에는 오류나 부정확한 부분이 있을 수 있음을 유의하시기 바랍니다. 원문은 해당 언어의 원본 문서가 권위 있는 자료로 간주되어야 합니다. 중요한 정보의 경우 전문적인 인간 번역을 권장합니다. 본 번역 사용으로 인해 발생하는 오해나 잘못된 해석에 대해 당사는 책임을 지지 않습니다.