<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:43:07+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "ko"
}
-->
# 신경망 소개. 다층 퍼셉트론

이전 섹션에서는 가장 간단한 신경망 모델인 단일층 퍼셉트론, 즉 선형 이진 분류 모델에 대해 배웠습니다.

이번 섹션에서는 이 모델을 좀 더 유연한 프레임워크로 확장하여 다음을 가능하게 할 것입니다:

* 이진 분류뿐만 아니라 **다중 클래스 분류** 수행
* 분류 문제뿐만 아니라 **회귀 문제** 해결
* 선형적으로 분리되지 않는 클래스 구분

또한, 다양한 신경망 아키텍처를 구성할 수 있는 자체 모듈형 프레임워크를 Python으로 개발할 것입니다.

## 머신러닝의 형식화

머신러닝 문제를 형식화하는 것부터 시작해 봅시다. 학습 데이터셋 **X**와 레이블 **Y**가 주어졌다고 가정하고, 가장 정확한 예측을 할 수 있는 모델 *f*를 만들어야 합니다. 예측의 품질은 **손실 함수** ℒ로 측정합니다. 다음과 같은 손실 함수들이 자주 사용됩니다:

* 회귀 문제에서는 숫자를 예측해야 하므로, **절대 오차** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>| 또는 **제곱 오차** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup>를 사용할 수 있습니다.
* 분류 문제에서는 **0-1 손실** (사실상 모델의 **정확도**와 동일) 또는 **로지스틱 손실**을 사용합니다.

단일층 퍼셉트론에서 함수 *f*는 선형 함수 *f(x)=wx+b*로 정의되었습니다 (*w*는 가중치 행렬, *x*는 입력 특징 벡터, *b*는 편향 벡터). 다양한 신경망 아키텍처에서는 이 함수가 더 복잡한 형태를 가질 수 있습니다.

> 분류 문제의 경우, 네트워크 출력으로 각 클래스의 확률을 얻는 것이 종종 바람직합니다. 임의의 숫자를 확률로 변환하기 위해 (예: 출력을 정규화하기 위해) **softmax** 함수 σ를 자주 사용하며, 함수 *f*는 *f(x)=σ(wx+b)*가 됩니다.

위에서 정의한 *f*에서 *w*와 *b*는 **파라미터** θ=⟨*w,b*⟩라고 부릅니다. 데이터셋 ⟨**X**,**Y**⟩가 주어지면, 파라미터 θ에 대한 전체 데이터셋의 오차를 계산할 수 있습니다.

> ✅ **신경망 학습의 목표는 파라미터 θ를 조정하여 오차를 최소화하는 것입니다.**

## 경사 하강법 최적화

함수 최적화에 널리 알려진 방법 중 하나가 **경사 하강법**입니다. 아이디어는 손실 함수에 대한 파라미터의 미분(다차원에서는 **기울기**)을 계산하고, 오차가 줄어들도록 파라미터를 조정하는 것입니다. 이를 수식으로 표현하면 다음과 같습니다:

* 파라미터를 임의의 값 w<sup>(0)</sup>, b<sup>(0)</sup>로 초기화
* 다음 단계를 여러 번 반복:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup> - η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup> - η∂ℒ/∂b

학습 중에는 전체 데이터셋을 고려하여 최적화 단계를 계산하는 것이 이상적입니다 (손실이 모든 학습 샘플에 대해 합산되기 때문). 하지만 실제로는 **미니배치**라고 불리는 데이터의 작은 부분집합을 무작위로 선택하여 그 부분집합에 대해 기울기를 계산합니다. 이 방법을 **확률적 경사 하강법**(SGD)이라고 합니다.

## 다층 퍼셉트론과 역전파

위에서 본 단일층 네트워크는 선형적으로 분리 가능한 클래스를 분류할 수 있습니다. 더 풍부한 모델을 만들기 위해 여러 층을 결합할 수 있습니다. 수학적으로는 함수 *f*가 더 복잡한 형태를 가지며 여러 단계로 계산됩니다:
* z<sub>1</sub> = w<sub>1</sub>x + b<sub>1</sub>
* z<sub>2</sub> = w<sub>2</sub>α(z<sub>1</sub>) + b<sub>2</sub>
* f = σ(z<sub>2</sub>)

여기서 α는 **비선형 활성화 함수**, σ는 softmax 함수이며, 파라미터는 θ = ⟨*w<sub>1</sub>, b<sub>1</sub>, w<sub>2</sub>, b<sub>2</sub>*⟩입니다.

경사 하강법 알고리즘은 동일하지만, 기울기를 계산하는 것이 더 복잡해집니다. 연쇄 미분 법칙(chain rule)을 이용해 다음과 같이 미분을 계산할 수 있습니다:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ 연쇄 미분 법칙은 손실 함수의 파라미터에 대한 미분을 계산하는 데 사용됩니다.

위 식들에서 가장 왼쪽 부분은 모두 같으므로, 손실 함수에서 시작해 계산 그래프를 "역방향"으로 따라가며 미분을 효율적으로 계산할 수 있습니다. 따라서 다층 퍼셉트론 학습 방법을 **역전파(backpropagation)** 또는 줄여서 '백프로프'라고 부릅니다.

> TODO: 이미지 출처

> ✅ 역전파에 대해서는 노트북 예제에서 훨씬 더 자세히 다룰 예정입니다.

## 결론

이번 강의에서는 자체 신경망 라이브러리를 만들고, 이를 사용해 간단한 2차원 분류 문제를 해결해 보았습니다.

## 🚀 도전 과제

동봉된 노트북에서 다층 퍼셉트론을 구축하고 학습시키는 자체 프레임워크를 구현해 보세요. 현대 신경망이 어떻게 작동하는지 자세히 확인할 수 있을 것입니다.

OwnFramework 노트북으로 이동하여 실습을 진행하세요.

## 복습 및 자기주도 학습

역전파는 AI와 머신러닝에서 널리 사용되는 알고리즘으로, 더 깊이 공부할 가치가 있습니다.

## 과제

이번 실습에서는 이번 강의에서 만든 프레임워크를 사용해 MNIST 손글씨 숫자 분류 문제를 해결해 보세요.

* 지침
* 노트북

**면책 조항**:  
이 문서는 AI 번역 서비스 [Co-op Translator](https://github.com/Azure/co-op-translator)를 사용하여 번역되었습니다. 정확성을 위해 최선을 다하고 있으나, 자동 번역에는 오류나 부정확한 부분이 있을 수 있음을 유의하시기 바랍니다. 원문은 해당 언어의 원본 문서가 권위 있는 출처로 간주되어야 합니다. 중요한 정보의 경우 전문적인 인간 번역을 권장합니다. 본 번역 사용으로 인해 발생하는 오해나 잘못된 해석에 대해 당사는 책임을 지지 않습니다.