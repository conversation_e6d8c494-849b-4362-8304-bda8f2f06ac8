<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:57:42+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "ko"
}
-->
# 자가 학습을 위한 자료

이 수업은 용어와 튜토리얼 참고를 위해 OpenAI와 Azure OpenAI의 핵심 자료들을 기반으로 제작되었습니다. 아래는 여러분의 자가 학습 여정을 위한 비포괄적인 자료 목록입니다.

## 1. 주요 자료

| 제목/링크                                                                                                                                                                                                                  | 설명                                                                                                                                                                                                                                                                                                                                                                                        |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [OpenAI 모델로 파인튜닝하기](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                          | 파인튜닝은 프롬프트에 들어갈 수 있는 예시보다 훨씬 많은 예시로 학습하여 few-shot 학습을 개선합니다. 비용 절감, 응답 품질 향상, 낮은 지연 시간 요청이 가능해집니다. **OpenAI에서 제공하는 파인튜닝 개요를 확인하세요.**                                                                                                                                                                  |
| [Azure OpenAI에서 파인튜닝이란?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                            | **파인튜닝의 개념**, 왜 파인튜닝을 고려해야 하는지(동기 부여 문제), 어떤 데이터를 사용해야 하는지(학습 데이터), 그리고 품질 측정 방법을 이해할 수 있습니다.                                                                                                                                                                                                                             |
| [파인튜닝으로 모델 맞춤화하기](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)        | Azure OpenAI 서비스는 파인튜닝을 통해 개인 데이터셋에 맞게 모델을 맞춤화할 수 있습니다. Azure AI Studio, Python SDK 또는 REST API를 사용하여 **파인튜닝 과정과 모델 선택 방법**을 배워보세요.                                                                                                                                                                                        |
| [LLM 파인튜닝 권장사항](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                               | LLM은 특정 도메인, 작업, 데이터셋에서 성능이 좋지 않거나 부정확하거나 오해를 불러일으키는 결과를 낼 수 있습니다. 이런 경우 **파인튜닝을 고려해야 할 때**를 알려줍니다.                                                                                                                                                                                                                   |
| [지속적 파인튜닝](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)                    | 지속적 파인튜닝은 이미 파인튜닝된 모델을 기반 모델로 선택하여 새로운 학습 예시로 **추가 파인튜닝을 반복하는 과정**입니다.                                                                                                                                                                                                                                                               |
| [파인튜닝과 함수 호출](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                                  | 함수 호출 예시를 포함한 파인튜닝은 모델 출력의 정확성과 일관성을 높이고, 유사한 형식의 응답과 비용 절감 효과를 가져올 수 있습니다.                                                                                                                                                                                                                                                     |
| [파인튜닝 가능한 모델: Azure OpenAI 가이드](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                     | Azure OpenAI에서 **파인튜닝이 가능한 모델과 해당 모델이 제공되는 지역**, 토큰 제한 및 학습 데이터 만료일을 확인할 수 있는 표입니다.                                                                                                                                                                                                                                                     |
| [파인튜닝을 할까 말까? 그것이 문제로다](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                                   | 2023년 10월 AI Show 30분 에피소드로, 파인튜닝의 장단점과 실용적인 인사이트를 다루어 결정에 도움을 줍니다.                                                                                                                                                                                                                                                                                   |
| [LLM 파인튜닝 시작하기](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                              | 이 **AI 플레이북** 자료는 데이터 요구사항, 포맷팅, 하이퍼파라미터 조정, 그리고 알아야 할 도전과제 및 한계점을 안내합니다.                                                                                                                                                                                                                                                                     |
| **튜토리얼**: [Azure OpenAI GPT3.5 Turbo 파인튜닝](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                   | 샘플 파인튜닝 데이터셋 생성, 파인튜닝 준비, 파인튜닝 작업 생성, 그리고 Azure에서 파인튜닝된 모델 배포 방법을 배울 수 있습니다.                                                                                                                                                                                                                                                             |
| **튜토리얼**: [Azure AI Studio에서 Llama 2 모델 파인튜닝](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                     | Azure AI Studio는 저코드 개발자에게 적합한 UI 기반 워크플로우를 통해 대형 언어 모델을 개인 데이터셋에 맞게 맞춤화할 수 있습니다. 예제를 참고하세요.                                                                                                                                                                                                                                       |
| **튜토리얼**: [Azure에서 단일 GPU로 Hugging Face 모델 파인튜닝](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)                   | 이 글은 Azure DataBricks와 Hugging Face Trainer 라이브러리를 사용해 단일 GPU에서 Hugging Face 트랜스포머 모델을 파인튜닝하는 방법을 설명합니다.                                                                                                                                                                                                                                           |
| **교육 과정:** [Azure Machine Learning으로 파운데이션 모델 파인튜닝](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)              | Azure Machine Learning 모델 카탈로그에는 특정 작업에 맞게 파인튜닝할 수 있는 다양한 오픈 소스 모델이 있습니다. 이 모듈은 [AzureML 생성 AI 학습 경로](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)의 일부입니다.                                                                                              |
| **튜토리얼:** [Azure OpenAI 파인튜닝](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                               | Microsoft Azure에서 W&B를 사용해 GPT-3.5 또는 GPT-4 모델을 파인튜닝하면 모델 성능을 상세히 추적하고 분석할 수 있습니다. 이 가이드는 OpenAI 파인튜닝 가이드의 개념을 확장하여 Azure OpenAI에 맞는 구체적인 단계와 기능을 제공합니다.                                                                                                                                                     |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                                                                                             |

## 2. 부가 자료

이 섹션은 이번 수업에서 다루지 못했지만 탐색해볼 가치가 있는 추가 자료들을 모았습니다. 추후 수업이나 부가 과제로 다뤄질 수 있으며, 지금은 이 주제에 대한 여러분의 전문성과 지식을 쌓는 데 활용하세요.

| 제목/링크                                                                                                                                                                                                                 | 설명                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [챗 모델 파인튜닝을 위한 데이터 준비 및 분석](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                                        | 이 노트북은 챗 모델 파인튜닝에 사용되는 챗 데이터셋을 전처리하고 분석하는 도구입니다. 포맷 오류를 검사하고 기본 통계와 파인튜닝 비용 산정을 위한 토큰 수를 추정합니다. 참고: [gpt-3.5-turbo 파인튜닝 방법](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                                                                         |
| **OpenAI Cookbook**: [Qdrant와 함께하는 검색 증강 생성(RAG) 파인튜닝](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)                        | 이 노트북은 OpenAI 모델을 검색 증강 생성(RAG)용으로 파인튜닝하는 포괄적인 예제를 안내합니다. Qdrant와 Few-Shot Learning을 통합하여 모델 성능을 높이고 허위 정보를 줄이는 방법도 다룹니다.                                                                                                                                                                                                                                                                       |
| **OpenAI Cookbook**: [Weights & Biases를 이용한 GPT 파인튜닝](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                              | Weights & Biases(W&B)는 모델 학습, 파인튜닝, 파운데이션 모델 활용을 위한 AI 개발 플랫폼입니다. 먼저 [OpenAI 파인튜닝 가이드](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst)를 읽고, Cookbook 연습을 시도해보세요.                                                                                                                                                                                                                   |
| **커뮤니티 튜토리얼** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - 소형 언어 모델 파인튜닝                                                                                 | Microsoft의 새 소형 모델 [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst)를 만나보세요. 작지만 강력한 이 모델을 QLoRA를 사용해 독특한 데이터셋을 만들고 파인튜닝하는 방법을 안내합니다.                                                                                                                                                                                                                  |
| **Hugging Face 튜토리얼** [2024년 Hugging Face로 LLM 파인튜닝하는 방법](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                                        | 이 블로그 글은 2024년 Hugging Face TRL, Transformers, datasets를 사용해 오픈 LLM을 파인튜닝하는 과정을 안내합니다. 사용 사례 정의, 개발 환경 설정, 데이터셋 준비, 모델 파인튜닝, 테스트 및 평가, 그리고 프로덕션 배포까지 다룹니다.                                                                                                                                                                                                                                   |
| **Hugging Face**: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)                                                                                                | 최신 [최첨단 머신러닝 모델](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst)의 빠르고 쉬운 학습 및 배포를 지원합니다. Colab 친화적 튜토리얼과 YouTube 영상 가이드가 포함되어 있으며, 최근 [로컬 퍼스트](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) 업데이트를 반영했습니다. [AutoTrain 문서](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst)를 참고하세요. |
|                                                                                                                                                                                                                           |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**면책 조항**:  
이 문서는 AI 번역 서비스 [Co-op Translator](https://github.com/Azure/co-op-translator)를 사용하여 번역되었습니다. 정확성을 위해 최선을 다하고 있으나, 자동 번역에는 오류나 부정확한 부분이 있을 수 있음을 유의해 주시기 바랍니다. 원문은 해당 언어의 원본 문서가 권위 있는 출처로 간주되어야 합니다. 중요한 정보의 경우 전문적인 인간 번역을 권장합니다. 본 번역 사용으로 인해 발생하는 오해나 잘못된 해석에 대해 당사는 책임을 지지 않습니다.