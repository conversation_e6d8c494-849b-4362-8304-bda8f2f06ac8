<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:24:31+00:00",
  "source_file": "README.md",
  "language_code": "ko"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.ko.png)

### 생성형 AI 애플리케이션 개발을 시작하는 데 필요한 모든 것을 가르치는 21개의 강의

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 다국어 지원

#### GitHub Action을 통해 지원 (자동화 및 항상 최신 상태 유지)

[French](../fr/README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](../ja/README.md) | [Korean](./README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](../el/README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](../he/README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](../sk/README.md) | [Romanian](../ro/README.md) | [Bulgarian](../bg/README.md) | [Serbian (Cyrillic)](../sr/README.md) | [Croatian](../hr/README.md) | [Slovenian](../sl/README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Generative AI for Beginners (버전 3) - 강의 과정

Microsoft Cloud Advocates가 제공하는 21개의 강의를 통해 생성형 AI 애플리케이션 개발의 기본을 배워보세요.

## 🌱 시작하기

이 과정은 총 21개의 강의로 구성되어 있습니다. 각 강의는 독립적인 주제를 다루므로 원하는 곳부터 시작하세요!

강의는 "Learn" 강의와 "Build" 강의로 구분됩니다. "Learn" 강의는 생성형 AI 개념을 설명하고, "Build" 강의는 개념과 함께 가능할 경우 **Python**과 **TypeScript** 코드 예제를 제공합니다.

.NET 개발자라면 [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)를 확인해 보세요!

각 강의에는 추가 학습 도구가 포함된 "Keep Learning" 섹션도 포함되어 있습니다.

## 필요한 것
### 이 과정의 코드를 실행하려면 다음 중 하나를 사용할 수 있습니다:
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **강의:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **강의:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **강의:** "oai-assignment"
   
- Python 또는 TypeScript의 기본 지식이 있으면 도움이 됩니다 - \*완전 초보자는 이 [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) 및 [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) 강의를 참고하세요
- 이 전체 저장소를 자신의 GitHub 계정으로 [포크](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst)할 GitHub 계정

개발 환경 설정을 돕기 위해 **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** 강의를 준비했습니다.

나중에 쉽게 찾을 수 있도록 이 저장소에 [별(🌟)을 눌러주세요](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst).

## 🧠 배포할 준비가 되셨나요?

더 고급 코드 샘플을 원하시면, **Python**과 **TypeScript**로 된 [생성형 AI 코드 샘플 모음](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst)을 확인해 보세요.

## 🗣️ 다른 학습자들과 만나고 지원 받기

이 과정을 수강하는 다른 학습자들과 교류하고 지원을 받으려면 [공식 Azure AI Foundry Discord 서버](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)에 참여하세요.

질문을 하거나 제품 피드백을 공유하려면 GitHub의 [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum)을 이용하세요.

## 🚀 스타트업을 시작하시나요?

[Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst)에 가입하면 **무료 OpenAI 크레딧**과 Azure OpenAI 서비스를 통해 OpenAI 모델에 접근할 수 있는 **최대 15만 달러 상당의 Azure 크레딧**을 받을 수 있습니다.

## 🙏 도움을 주시겠어요?

제안 사항이 있거나 오타나 코드 오류를 발견하셨다면 [이슈를 등록](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst)하거나 [풀 리퀘스트를 만들어 주세요](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst).

## 📂 각 강의에는 다음이 포함됩니다:

- 주제에 대한 짧은 영상 소개
- README에 작성된 강의 내용
- Azure OpenAI 및 OpenAI API를 지원하는 Python과 TypeScript 코드 샘플
- 학습을 이어갈 수 있는 추가 자료 링크

## 🗃️ 강의 목록

| #   | **강의 링크**                                                                                                                              | **설명**                                                                                      | **영상**                                                                    | **추가 학습**                                                                 |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Learn:** 개발 환경 설정 방법                                                                | 곧 영상 제공 예정                                                             | [더 알아보기](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Introduction to Generative AI and LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                              | **Learn:** 생성형 AI와 대형 언어 모델(LLM)이 무엇인지 이해하기                                 | [영상](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst)   | [더 알아보기](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Exploring and comparing different LLMs](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** 사용 사례에 맞는 적합한 모델 선택 방법                                            | [영상](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)    | [더 알아보기](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Using Generative AI Responsibly](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                           | **Learn:** 생성형 AI 애플리케이션을 책임감 있게 개발하는 방법                                 | [영상](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)    | [더 알아보기](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Understanding Prompt Engineering Fundamentals](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** 실습을 통한 프롬프트 엔지니어링 기본기                                            | [영상](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)    | [더 알아보기](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Creating Advanced Prompts](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **Learn:** 프롬프트 결과를 향상시키는 고급 프롬프트 기법 적용법                              | [영상](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)    | [더 알아보기](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [텍스트 생성 애플리케이션 구축](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **구축:** Azure OpenAI / OpenAI API를 사용한 텍스트 생성 앱                                | [Video](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [채팅 애플리케이션 구축](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **구축:** 채팅 애플리케이션을 효율적으로 개발하고 통합하는 기법               | [Video](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [검색 앱 및 벡터 데이터베이스 구축](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **구축:** 임베딩을 활용한 데이터 검색 애플리케이션                        | [Video](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [이미지 생성 애플리케이션 구축](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **구축:** 이미지 생성 애플리케이션                                                       | [Video](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [로우코드 AI 애플리케이션 구축](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **구축:** 로우코드 도구를 활용한 생성 AI 애플리케이션                                     | [Video](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Function Calling을 활용한 외부 애플리케이션 통합](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **구축:** function calling이란 무엇이며 애플리케이션에서의 활용 사례                          | [Video](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [AI 애플리케이션 UX 디자인](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **학습:** 생성 AI 애플리케이션 개발 시 UX 디자인 원칙 적용 방법         | [Video](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [생성 AI 애플리케이션 보안](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **학습:** AI 시스템에 대한 위협과 위험, 그리고 이를 보호하는 방법             | [Video](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [생성 AI 애플리케이션 라이프사이클](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **학습:** LLM 라이프사이클과 LLMOps 관리를 위한 도구와 지표                         | [Video](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) 및 벡터 데이터베이스](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **구축:** RAG 프레임워크를 사용해 벡터 데이터베이스에서 임베딩을 검색하는 애플리케이션  | [Video](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [오픈 소스 모델과 Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **구축:** Hugging Face에서 제공하는 오픈 소스 모델을 활용한 애플리케이션                    | [Video](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI 에이전트](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **구축:** AI 에이전트 프레임워크를 활용한 애플리케이션                                           | [Video](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [LLM 미세 조정](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **학습:** LLM 미세 조정의 개념, 필요성, 그리고 방법                                            | [Video](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [SLM을 활용한 구축](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **학습:** Small Language Models를 활용한 구축의 장점                                            | 곧 영상 제공 예정 | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Mistral 모델을 활용한 구축](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **학습:** Mistral 패밀리 모델의 특징과 차이점                                           | 곧 영상 제공 예정 | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Meta 모델을 활용한 구축](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **학습:** Meta 패밀리 모델의 특징과 차이점                                           | 곧 영상 제공 예정 | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 특별 감사의 말씀

모든 GitHub Actions와 워크플로우를 만들어 주신 [**John Aziz**](https://www.linkedin.com/in/john0isaac/)께 특별히 감사드립니다.

각 강의에서 학습자와 코드 경험을 향상시키기 위해 핵심 기여를 해주신 [**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/)께도 감사드립니다.

## 🎒 기타 강의

저희 팀은 다양한 강의를 제작하고 있습니다! 아래를 확인해 보세요:

- [**NEW** 초보자를 위한 Model Context Protocol](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 AI 에이전트](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [.NET을 활용한 초보자용 생성 AI](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [JavaScript를 활용한 초보자용 생성 AI](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 머신러닝](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 데이터 과학](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 AI](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 사이버보안](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [초보자를 위한 웹 개발](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 IoT](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [초보자를 위한 XR 개발](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI 페어 프로그래밍을 위한 GitHub Copilot 마스터하기](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [C#/.NET 개발자를 위한 GitHub Copilot 마스터하기](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [나만의 Copilot 모험 선택하기](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**면책 조항**:  
이 문서는 AI 번역 서비스 [Co-op Translator](https://github.com/Azure/co-op-translator)를 사용하여 번역되었습니다. 정확성을 위해 노력하고 있으나, 자동 번역에는 오류나 부정확한 부분이 있을 수 있음을 유의하시기 바랍니다. 원문은 해당 언어의 원본 문서가 권위 있는 출처로 간주되어야 합니다. 중요한 정보의 경우 전문적인 인간 번역을 권장합니다. 본 번역 사용으로 인해 발생하는 오해나 잘못된 해석에 대해 당사는 책임을 지지 않습니다.