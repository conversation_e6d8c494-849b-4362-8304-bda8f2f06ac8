<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:52:00+00:00",
  "source_file": "SECURITY.md",
  "language_code": "ko"
}
-->
## 보안

Microsoft는 소프트웨어 제품과 서비스의 보안을 매우 중요하게 생각하며, 여기에는 [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin), 그리고 [우리의 GitHub 조직들](https://opensource.microsoft.com/)을 포함한 모든 GitHub 조직을 통해 관리되는 소스 코드 저장소가 포함됩니다.

Microsoft 소유의 저장소에서 [Microsoft의 보안 취약점 정의](https://aka.ms/opensource/security/definition)에 부합하는 보안 취약점을 발견했다고 생각되면, 아래에 설명된 대로 신고해 주시기 바랍니다.

## 보안 문제 신고

**보안 취약점은 공개 GitHub 이슈를 통해 신고하지 말아 주세요.**

대신 Microsoft 보안 대응 센터(MSRC)에서 [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report)로 신고해 주시기 바랍니다.

로그인 없이 제출하고 싶으시면 [<EMAIL>](mailto:<EMAIL>)으로 이메일을 보내 주세요. 가능하다면, 메시지를 당사 PGP 키로 암호화해 주시기 바랍니다. PGP 키는 [Microsoft 보안 대응 센터 PGP 키 페이지](https://aka.ms/opensource/security/pgpkey)에서 다운로드할 수 있습니다.

24시간 이내에 답변을 받으실 수 있습니다. 만약 답변을 받지 못하셨다면, 이메일로 다시 연락하여 원래 메시지를 받았는지 확인해 주세요. 추가 정보는 [microsoft.com/msrc](https://aka.ms/opensource/security/msrc)에서 확인할 수 있습니다.

가능한 한 아래 요청된 정보를 포함해 주시면 문제의 성격과 범위를 더 잘 이해하는 데 도움이 됩니다:

  * 문제 유형 (예: 버퍼 오버플로우, SQL 인젝션, 크로스 사이트 스크립팅 등)
  * 문제 발생과 관련된 소스 파일의 전체 경로
  * 영향을 받는 소스 코드 위치 (태그/브랜치/커밋 또는 직접 URL)
  * 문제 재현에 필요한 특별한 설정
  * 문제 재현을 위한 단계별 지침
  * 개념 증명 또는 익스플로잇 코드 (가능한 경우)
  * 문제의 영향 및 공격자가 이를 어떻게 악용할 수 있는지

이 정보는 신고 내용을 신속하게 분류하는 데 도움이 됩니다.

버그 바운티 신고의 경우, 더 완전한 보고서는 더 높은 보상으로 이어질 수 있습니다. 활성 프로그램에 대한 자세한 내용은 [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) 페이지를 방문해 주세요.

## 선호 언어

모든 소통은 영어로 진행하는 것을 선호합니다.

## 정책

Microsoft는 [Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd) 원칙을 따릅니다.

**면책 조항**:  
이 문서는 AI 번역 서비스 [Co-op Translator](https://github.com/Azure/co-op-translator)를 사용하여 번역되었습니다. 정확성을 위해 최선을 다하고 있으나, 자동 번역에는 오류나 부정확한 부분이 있을 수 있음을 유의하시기 바랍니다. 원문은 해당 언어의 원본 문서가 권위 있는 출처로 간주되어야 합니다. 중요한 정보의 경우 전문적인 인간 번역을 권장합니다. 본 번역 사용으로 인해 발생하는 오해나 잘못된 해석에 대해 당사는 책임을 지지 않습니다.