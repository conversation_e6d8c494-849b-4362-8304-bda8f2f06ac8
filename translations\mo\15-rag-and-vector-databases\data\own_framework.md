<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:41:59+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "mo"
}
-->
# 神經網路入門。多層感知器

在上一節中，你學習了最簡單的神經網路模型——單層感知器，一個線性二分類模型。

本節我們將把這個模型擴展成更靈活的架構，使我們能夠：

* 除了二分類外，進行**多分類**
* 除了分類外，解決**迴歸問題**
* 區分非線性可分的類別

我們也會用 Python 開發自己的模組化框架，讓我們能夠構建不同的神經網路架構。

## 機器學習的形式化

讓我們先形式化機器學習問題。假設我們有一組帶標籤的訓練資料集 **X** 和 **Y**，我們需要建立一個模型 *f*，使其能做出最準確的預測。預測的好壞由**損失函數** ℒ 來衡量。常用的損失函數有：

* 對於迴歸問題，當我們需要預測一個數值時，可以使用**絕對誤差** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>|，或**平方誤差** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup>
* 對於分類問題，我們使用**0-1 損失**（本質上等同於模型的**準確率**），或**邏輯損失**。

對於單層感知器，函數 *f* 定義為線性函數 *f(x)=wx+b*（其中 *w* 是權重矩陣，*x* 是輸入特徵向量，*b* 是偏差向量）。對於不同的神經網路架構，這個函數可以更複雜。

> 在分類問題中，通常希望網路輸出對應類別的機率。為了將任意數值轉換成機率（例如正規化輸出），我們常用 **softmax** 函數 σ，函數 *f* 變成 *f(x)=σ(wx+b)*

在上述 *f* 的定義中，*w* 和 *b* 稱為**參數** θ=⟨*w,b*⟩。給定資料集 ⟨**X**,**Y**⟩，我們可以將整個資料集上的總誤差視為參數 θ 的函數。

> ✅ **神經網路訓練的目標是透過調整參數 θ 來最小化誤差**

## 梯度下降優化

有一種著名的函數優化方法叫做**梯度下降**。其想法是，我們可以計算損失函數對參數的導數（多維情況下稱為**梯度**），並以使誤差下降的方向調整參數。形式化如下：

* 用隨機值初始化參數 w<sup>(0)</sup>, b<sup>(0)</sup>
* 重複執行以下步驟多次：
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

訓練過程中，優化步驟理應基於整個資料集計算（記得損失是所有訓練樣本的總和）。但實際上，我們會取資料集的小批次稱為**minibatches**，並基於這些子集計算梯度。由於每次子集是隨機抽取的，這種方法稱為**隨機梯度下降**（SGD）。

## 多層感知器與反向傳播

如前所述，單層網路只能分類線性可分的類別。為了建立更豐富的模型，我們可以結合多層網路。數學上，函數 *f* 會更複雜，並分多步計算：
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

其中，α 是**非線性啟動函數**，σ 是 softmax 函數，參數 θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*>。

梯度下降演算法不變，但計算梯度會更複雜。根據鏈式法則，我們可以計算導數為：

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ 鏈式法則用於計算損失函數對參數的導數。

注意這些表達式中最左邊的部分相同，因此我們可以從損失函數開始，沿著計算圖「反向」有效地計算導數。這種訓練多層感知器的方法稱為**反向傳播**，或簡稱「backprop」。

> TODO: image citation

> ✅ 我們會在筆記本範例中更詳細地介紹反向傳播。

## 結論

本課中，我們建立了自己的神經網路函式庫，並用它完成了一個簡單的二維分類任務。

## 🚀 挑戰

在附帶的筆記本中，你將實作自己的多層感知器建構與訓練框架。你將能詳細了解現代神經網路的運作方式。

請前往 OwnFramework 筆記本並完成練習。

## 複習與自學

反向傳播是 AI 和機器學習中常用的演算法，值得深入學習。

## 作業

本實驗要求你使用本課所建構的框架，完成 MNIST 手寫數字分類任務。

* 說明
* 筆記本

**免責聲明**：  
本文件係使用 AI 翻譯服務 [Co-op Translator](https://github.com/Azure/co-op-translator) 進行翻譯。雖然我們致力於確保準確性，但請注意，自動翻譯可能包含錯誤或不準確之處。原始文件的母語版本應視為權威來源。對於重要資訊，建議採用專業人工翻譯。我們不對因使用本翻譯而產生的任何誤解或誤釋負責。