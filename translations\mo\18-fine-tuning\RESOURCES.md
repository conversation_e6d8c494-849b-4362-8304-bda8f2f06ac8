<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:56:00+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "mo"
}
-->
# 自學資源

本課程參考了 OpenAI 和 Azure OpenAI 的多個核心資源，作為術語和教學的依據。以下是非完整列表，供您自學參考。

## 1. 主要資源

| 標題/連結                                                                                                                                                                                                                 | 說明                                                                                                                                                                                                                                                                                                                                                                                        |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                     | 微調透過訓練更多範例，超越少量示範學習的限制，幫助節省成本、提升回應品質，並降低延遲。**了解 OpenAI 的微調概述。**                                                                                                                                                                                                                                   |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                     | 了解**微調的概念**、為何需要微調（動機問題）、應使用哪些資料（訓練）以及如何衡量品質。                                                                                                                                                                                                                                               |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI 服務讓您能透過微調，根據個人資料集調整模型。學習**如何微調（流程）**，並使用 Azure AI Studio、Python SDK 或 REST API 選擇模型。                                                                                                                                                                                                                 |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                  | 大型語言模型（LLM）在特定領域、任務或資料集上可能表現不佳，或產生不準確或誤導性輸出。**何時應考慮微調**作為解決方案？                                                                                                                                                                                                                             |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | 持續微調是指以已微調模型為基礎，**在新訓練資料上進行進一步微調**的反覆過程。                                                                                                                                                                                                                                                         |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | 透過**帶有函式呼叫範例的微調**，可提升模型輸出準確度與一致性，產生格式相似的回應，並節省成本。                                                                                                                                                                                                                                             |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | 查閱此表格了解**哪些模型可在 Azure OpenAI 進行微調**，以及可用區域。必要時查看其 token 限制和訓練資料過期日期。                                                                                                                                                                                                                               |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | 這集 2023 年 10 月的 AI Show（30 分鐘）討論微調的優缺點與實務見解，幫助您做出決策。                                                                                                                                                                                                                                                   |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | 這份**AI Playbook**資源帶您了解資料需求、格式化、超參數微調，以及您應該知道的挑戰與限制。                                                                                                                                                                                                                                               |
| **教學**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | 學習如何建立範例微調資料集、準備微調、建立微調工作，並在 Azure 上部署微調後的模型。                                                                                                                                                                                                                                                     |
| **教學**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio 讓您透過 UI 流程，為個人資料集調整大型語言模型，適合低程式碼開發者。參考此範例。                                                                                                                                                                                                                                         |
| **教學**: [Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | 本文說明如何使用 Hugging Face transformers 庫，在 Azure DataBricks 上利用單 GPU 和 Hugging Face Trainer 庫微調 Hugging Face 模型。                                                                                                                                                                                                         |
| **訓練課程:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Azure Machine Learning 的模型目錄提供多款開源模型，您可針對特定任務進行微調。此模組來自 [AzureML 生成式 AI 學習路徑](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)。                                                                                         |
| **教學:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | 在 Microsoft Azure 上使用 W&B 微調 GPT-3.5 或 GPT-4 模型，可詳細追蹤與分析模型效能。本指南擴展了 OpenAI 微調指南的概念，並提供 Azure OpenAI 的具體步驟與功能。                                                                                                                                                                               |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                                                                                             |

## 2. 次要資源

本節收錄了值得探索的額外資源，但本課程未涵蓋。未來課程或次要作業可能會涉及。您可利用這些資源自行增進相關知識與專業。

| 標題/連結                                                                                                                                                                                                                  | 說明                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Data preparation and analysis for chat model fine-tuning](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                            | 此筆記本用於預處理與分析用於微調聊天模型的資料集，檢查格式錯誤、提供基本統計數據，並估算微調所需的 token 數量與成本。參考：[gpt-3.5-turbo 微調方法](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)。                                                                                                                                                                                                                     |
| **OpenAI Cookbook**: [Fine-Tuning for Retrieval Augmented Generation (RAG) with Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)         | 本筆記本示範如何微調 OpenAI 模型以實現檢索增強生成（RAG），並整合 Qdrant 與少量示範學習，提升模型效能並減少虛構內容。                                                                                                                                                                                                                                                                                                                                                     |
| **OpenAI Cookbook**: [Fine-tuning GPT with Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                                 | Weights & Biases (W&B) 是 AI 開發平台，提供訓練、微調模型及利用基礎模型的工具。先閱讀其 [OpenAI 微調指南](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst)，再嘗試 Cookbook 練習。                                                                                                                                                                                                                                         |
| **社群教學** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - 小型語言模型微調                                                                                     | 認識 [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst)，微軟新款小型模型，體積小但效能強大。此教學引導您如何建立獨特資料集並使用 QLoRA 微調 Phi-2。                                                                                                                                                                                                                                   |
| **Hugging Face 教學** [2024 年如何用 Hugging Face 微調 LLM](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                                                   | 本文介紹如何使用 Hugging Face TRL、Transformers 與 datasets 微調開源大型語言模型。涵蓋用例定義、開發環境設置、資料集準備、模型微調、測試評估及部署。                                                                                                                                                                                                                                                                                                                   |
| **Hugging Face**: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)                                                                                                | 提供更快速且簡易的[尖端機器學習模型](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst)訓練與部署。此倉庫包含適合 Colab 的教學與 YouTube 影片指導，方便微調。**反映近期的[local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)更新**。詳見 [AutoTrain 文件](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst)。 |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**免責聲明**：  
本文件係使用 AI 翻譯服務 [Co-op Translator](https://github.com/Azure/co-op-translator) 進行翻譯。雖然我們致力於確保準確性，但請注意，自動翻譯可能包含錯誤或不準確之處。原始文件的母語版本應視為權威來源。對於重要資訊，建議採用專業人工翻譯。我們不對因使用本翻譯而產生的任何誤解或誤譯負責。