<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "7f8f4c11f8c1cb6e1794442dead414ea",
  "translation_date": "2025-07-09T08:52:37+00:00",
  "source_file": "03-using-generative-ai-responsibly/README.md",
  "language_code": "mr"
}
-->
# जनरेटिव AI जबाबदारीने वापरणे

[![Using Generative AI Responsibly](../../../translated_images/03-lesson-banner.1ed56067a452d97709d51f6cc8b6953918b2287132f4909ade2008c936cd4af9.mr.png)](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)

> _या धड्याचा व्हिडिओ पाहण्यासाठी वरील प्रतिमेवर क्लिक करा_

AI आणि विशेषतः जनरेटिव AI कडे आकर्षित होणे सोपे आहे, पण तुम्हाला ते जबाबदारीने कसे वापरायचे याचा विचार करणे आवश्यक आहे. तुम्हाला विचार करावा लागेल की आउटपुट कसे न्याय्य, हानिरहित आणि इतर बाबतीत योग्य आहे याची खात्री कशी करायची. हा अध्याय तुम्हाला या संदर्भात माहिती देण्याचा, काय विचार करायचा आणि तुमच्या AI वापरात सुधारणा करण्यासाठी सक्रिय पावले कशी उचलायची हे समजावून सांगण्याचा उद्देश ठेवतो.

## परिचय

हा धडा खालील गोष्टींचा आढावा घेईल:

- जनरेटिव AI अॅप्लिकेशन्स तयार करताना Responsible AI ला का प्राधान्य द्यावे.
- Responsible AI चे मुख्य तत्त्वे आणि ती जनरेटिव AI शी कशी संबंधित आहेत.
- या Responsible AI तत्त्वांना धोरण आणि साधनांद्वारे कसे अमलात आणायचे.

## शिकण्याचे उद्दिष्ट

हा धडा पूर्ण केल्यानंतर तुम्हाला कळेल:

- जनरेटिव AI अॅप्लिकेशन्स तयार करताना Responsible AI चे महत्त्व.
- जनरेटिव AI अॅप्लिकेशन्स तयार करताना Responsible AI चे मुख्य तत्त्वे कधी आणि कशी वापरायची.
- Responsible AI चा संकल्पना अमलात आणण्यासाठी कोणती साधने आणि धोरणे उपलब्ध आहेत.

## Responsible AI चे तत्त्वे

जनरेटिव AI ची उत्सुकता कधीही इतकी जास्त नव्हती. या उत्साहामुळे या क्षेत्रात नवीन विकसक, लक्ष आणि निधी मोठ्या प्रमाणात आले आहेत. जनरेटिव AI वापरून उत्पादने आणि कंपन्या तयार करण्यासाठी हे फारच सकारात्मक आहे, पण त्याचवेळी जबाबदारीने पुढे जाणे देखील महत्त्वाचे आहे.

या कोर्समध्ये, आपण आमचा स्टार्टअप आणि AI शिक्षण उत्पादन तयार करण्यावर लक्ष केंद्रित करत आहोत. आपण Responsible AI चे तत्त्वे वापरणार आहोत: न्याय्यपणा, समावेशकता, विश्वासार्हता/सुरक्षा, सुरक्षा आणि गोपनीयता, पारदर्शकता आणि जबाबदारी. या तत्त्वांद्वारे आपण जनरेटिव AI चा आमच्या उत्पादनांमध्ये कसा वापर होतो हे पाहणार आहोत.

## तुम्ही Responsible AI ला का प्राधान्य द्यावे

उत्पादन तयार करताना, वापरकर्त्यांच्या हिताचा विचार करून मानवी-केंद्रित दृष्टिकोन ठेवणे सर्वोत्तम निकाल देते.

जनरेटिव AI ची खासियत म्हणजे वापरकर्त्यांसाठी उपयुक्त उत्तरं, माहिती, मार्गदर्शन आणि सामग्री तयार करण्याची क्षमता. हे अनेक मॅन्युअल टप्प्यांशिवाय करता येते, ज्यामुळे फारच प्रभावी निकाल मिळू शकतात. पण योग्य नियोजन आणि धोरणांशिवाय, हे तुमच्या वापरकर्त्यांसाठी, उत्पादनासाठी आणि समाजासाठी हानिकारक परिणाम देखील करू शकते.

आता काही (सर्व नाही) अशा संभाव्य हानिकारक परिणामांकडे पाहूया:

### भ्रम (Hallucinations)

भ्रम म्हणजे जेव्हा एखादा LLM पूर्णपणे अर्थहीन किंवा इतर माहितीच्या स्रोतांवरून चुकीचे असलेले कंटेंट तयार करतो.

उदाहरणार्थ, आपण आमच्या स्टार्टअपने एक अशी सुविधा तयार केली आहे जिथे विद्यार्थी ऐतिहासिक प्रश्न विचारू शकतात. एखादा विद्यार्थी विचारतो `Who was the sole survivor of Titanic?`

मॉडेल खालीलप्रमाणे उत्तर देते:

![Prompt saying "Who was the sole survivor of the Titanic"](../../../03-using-generative-ai-responsibly/images/ChatGPT-titanic-survivor-prompt.webp)

> _(स्रोत: [Flying bisons](https://flyingbisons.com?WT.mc_id=academic-105485-koreyst))_

हे उत्तर अत्यंत आत्मविश्वासी आणि सविस्तर आहे. दुर्दैवाने, ते चुकीचे आहे. थोडक्याशा संशोधनाने लक्षात येईल की टायटॅनिक अपघातात एकाहून अधिक जण जिवंत राहिले होते. ज्याला या विषयावर संशोधन सुरू करायचे आहे, त्याला हे उत्तर पुरेसे पटणारे वाटू शकते आणि ते सत्य मानले जाऊ शकते. यामुळे AI प्रणाली अविश्वसनीय ठरू शकते आणि आमच्या स्टार्टअपनेची प्रतिमा खराब होऊ शकते.

प्रत्येक LLM च्या आवृत्तीमध्ये, भ्रम कमी करण्यासाठी कामगिरी सुधारण्यात आली आहे. तरीही, आम्हाला अॅप्लिकेशन तयार करणाऱ्यांनाही आणि वापरकर्त्यांनाही या मर्यादा लक्षात ठेवणे आवश्यक आहे.

### हानिकारक सामग्री

आधीच्या विभागात आपण पाहिले की LLM चुकीची किंवा अर्थहीन उत्तरे देऊ शकतो. आणखी एक धोका म्हणजे मॉडेल हानिकारक सामग्री देणे.

हानिकारक सामग्री म्हणजे:

- स्वतःला किंवा विशिष्ट गटांना हानी पोहोचवण्याचे निर्देश देणे किंवा प्रोत्साहन देणे.
- द्वेषपूर्ण किंवा अपमानजनक सामग्री.
- कोणत्याही प्रकारच्या हल्ल्याची किंवा हिंसक कृत्यांची योजना आखण्याचे मार्गदर्शन.
- बेकायदेशीर सामग्री शोधण्याचे किंवा बेकायदेशीर कृत्ये करण्याचे निर्देश.
- लैंगिकदृष्ट्या स्पष्ट सामग्री दाखवणे.

आमच्या स्टार्टअपने विद्यार्थ्यांना अशा प्रकारची सामग्री दिसू नये यासाठी योग्य साधने आणि धोरणे वापरायची आहेत.

### न्याय्यतेचा अभाव

न्याय्यता म्हणजे “AI प्रणाली पूर्वग्रह आणि भेदभावापासून मुक्त असावी आणि सर्वांना समान आणि न्याय्य वागणूक द्यावी.” जनरेटिव AI च्या जगात, आम्हाला खात्री करायची आहे की वंचित गटांच्या वगळणाऱ्या दृष्टिकोनांना मॉडेलच्या आउटपुटद्वारे बळकट केले जात नाही.

अशा प्रकारचे आउटपुट केवळ वापरकर्त्यांसाठी सकारात्मक उत्पादन अनुभव तयार करण्यात अडथळा आणत नाहीत, तर ते समाजासाठीही हानिकारक ठरतात. म्हणून, अॅप्लिकेशन तयार करताना आम्हाला नेहमीच विस्तृत आणि विविध वापरकर्त्यांचा विचार करावा लागतो.

## जनरेटिव AI जबाबदारीने कसा वापरायचा

आता आपण Responsible Generative AI चे महत्त्व ओळखले आहे, तर पाहूया की AI सोल्यूशन्स जबाबदारीने तयार करण्यासाठी आपण कोणती 4 पावले उचलू शकतो:

![Mitigate Cycle](../../../translated_images/mitigate-cycle.babcd5a5658e1775d5f2cb47f2ff305cca090400a72d98d0f9e57e9db5637c72.mr.png)

### संभाव्य हानी मोजा

सॉफ्टवेअर चाचणीमध्ये, आपण वापरकर्त्याच्या अपेक्षित क्रियांचा अॅप्लिकेशनवर चाचणी करतो. त्याचप्रमाणे, वापरकर्ते ज्या विविध प्रकारच्या प्रॉम्प्ट्सचा वापर करणार आहेत त्यांची चाचणी करणे संभाव्य हानी मोजण्याचा चांगला मार्ग आहे.

आमचा स्टार्टअप शिक्षण उत्पादन तयार करत असल्याने, शिक्षणाशी संबंधित प्रॉम्प्ट्सची यादी तयार करणे उपयुक्त ठरेल. यात विशिष्ट विषय, ऐतिहासिक तथ्ये आणि विद्यार्थी जीवनाशी संबंधित प्रॉम्प्ट्स असू शकतात.

### संभाव्य हानी कमी करा

आता मॉडेल आणि त्याच्या प्रतिसादांमुळे होणारी संभाव्य हानी टाळण्यासाठी किंवा मर्यादित करण्याचे मार्ग शोधण्याची वेळ आली आहे. आपण हे 4 वेगवेगळ्या स्तरांवर पाहू शकतो:

![Mitigation Layers](../../../translated_images/mitigation-layers.377215120b9a1159a8c3982c6bbcf41b6adf8c8fa04ce35cbaeeb13b4979cdfc.mr.png)

- **मॉडेल**. योग्य वापरासाठी योग्य मॉडेल निवडणे. GPT-4 सारखे मोठे आणि जटिल मॉडेल्स लहान आणि विशिष्ट वापरासाठी वापरल्यास हानिकारक सामग्रीचा धोका वाढू शकतो. तुमच्या प्रशिक्षण डेटाचा वापर करून फाइन-ट्यूनिंग केल्यास हानिकारक सामग्रीचा धोका कमी होतो.

- **सुरक्षा प्रणाली**. सुरक्षा प्रणाली म्हणजे प्लॅटफॉर्मवर असलेली साधने आणि कॉन्फिगरेशन जे मॉडेलच्या हानी कमी करण्यात मदत करतात. उदाहरणार्थ, Azure OpenAI सेवेमधील सामग्री फिल्टरिंग प्रणाली. या प्रणालींनी जेलब्रेक हल्ले आणि बॉट्सकडून येणाऱ्या अवांछित विनंत्या ओळखाव्यात.

- **मेटाप्रॉम्प्ट**. मेटाप्रॉम्प्ट्स आणि ग्राउंडिंग हे मार्ग आहेत ज्याद्वारे आपण मॉडेलला विशिष्ट वर्तन आणि माहितीच्या आधारे निर्देशित किंवा मर्यादित करू शकतो. यामध्ये सिस्टम इनपुट्सचा वापर करून मॉडेलच्या काही मर्यादा निश्चित करणे समाविष्ट आहे. तसेच, आउटपुट्स अधिक संबंधित आणि प्रणालीच्या क्षेत्राशी सुसंगत बनवणे.

तसेच Retrieval Augmented Generation (RAG) सारख्या तंत्रांचा वापर करून मॉडेलला फक्त विश्वासार्ह स्रोतांमधून माहिती घेण्यास सांगता येते. या कोर्समध्ये नंतर [शोध अॅप्लिकेशन्स तयार करण्याचा धडा](../08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst) आहे.

- **वापरकर्ता अनुभव**. अंतिम स्तर म्हणजे वापरकर्ता आमच्या अॅप्लिकेशनच्या इंटरफेसद्वारे थेट मॉडेलशी संवाद साधतो. या पद्धतीने आपण UI/UX डिझाइन करू शकतो ज्यामुळे वापरकर्त्यांना मॉडेलला पाठवायच्या इनपुट्सवर मर्यादा घालता येतात तसेच वापरकर्त्यांना दाखवण्यात येणाऱ्या मजकूर किंवा प्रतिमांवर नियंत्रण ठेवता येते. AI अॅप्लिकेशन तैनात करताना, आपल्याला आपल्या जनरेटिव AI अॅप्लिकेशन काय करू शकते आणि काय करू शकत नाही याबाबत पारदर्शक असणे आवश्यक आहे.

आमच्याकडे [AI अॅप्लिकेशन्ससाठी UX डिझाइन](../12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst) या विषयावर संपूर्ण धडा आहे.

- **मॉडेलचे मूल्यमापन करा**. LLM सोबत काम करणे आव्हानात्मक असू शकते कारण आपल्याला नेहमीच मॉडेलच्या प्रशिक्षण डेटावर नियंत्रण नसते. तरीही, आपल्याला नेहमी मॉडेलच्या कामगिरीचे आणि आउटपुटचे मूल्यमापन करणे आवश्यक आहे. मॉडेलची अचूकता, सादृश्यता, आधारभूतता आणि आउटपुटची सुसंगतता मोजणे महत्त्वाचे आहे. यामुळे भागधारक आणि वापरकर्त्यांना पारदर्शकता आणि विश्वास मिळतो.

### जबाबदार जनरेटिव AI सोल्यूशन चालवा

तुमच्या AI अॅप्लिकेशन्सभोवती एक ऑपरेशनल प्रॅक्टिस तयार करणे अंतिम टप्पा आहे. यात आमच्या स्टार्टअपनेतील कायदेशीर आणि सुरक्षा विभागांसोबत भागीदारी करणे समाविष्ट आहे जेणेकरून सर्व नियमांचे पालन होईल. लॉन्च करण्यापूर्वी, वितरण, घटना हाताळणी आणि रोलबॅक यांसाठी योजना तयार करणे आवश्यक आहे जेणेकरून वापरकर्त्यांना होणारी हानी टाळता येईल.

## साधने

Responsible AI सोल्यूशन्स विकसित करण्याचे काम जरी मोठे वाटत असले तरी ते नक्कीच फायदेशीर आहे. जनरेटिव AI क्षेत्र वाढत असताना, विकसकांना जबाबदारीने काम करण्यास मदत करणारी अधिक साधने विकसित होत आहेत. उदाहरणार्थ, [Azure AI Content Safety](https://learn.microsoft.com/azure/ai-services/content-safety/overview?WT.mc_id=academic-105485-koreyst) API विनंतीद्वारे हानिकारक सामग्री आणि प्रतिमा ओळखण्यात मदत करू शकते.

## ज्ञान तपासणी

Responsible AI वापर सुनिश्चित करण्यासाठी तुम्हाला कोणत्या गोष्टींची काळजी घ्यावी लागते?

1. उत्तर बरोबर आहे याची खात्री करणे.
1. हानिकारक वापर टाळणे, म्हणजे AI गुन्हेगारी हेतूसाठी वापरले जात नाही.
1. AI पूर्वग्रह आणि भेदभावापासून मुक्त आहे याची खात्री करणे.

उत्तर: 2 आणि 3 बरोबर आहेत. Responsible AI तुम्हाला हानिकारक परिणाम आणि पूर्वग्रह कमी करण्याचा विचार करण्यास मदत करते.

## 🚀 आव्हान

[Azure AI Content Safety](https://learn.microsoft.com/azure/ai-services/content-safety/overview?WT.mc_id=academic-105485-koreyst) बद्दल वाचा आणि तुमच्या वापरासाठी काय स्वीकारू शकता ते पाहा.

## छान काम, तुमचे शिक्षण सुरू ठेवा

हा धडा पूर्ण केल्यानंतर, आमच्या [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) मध्ये जाऊन तुमचे जनरेटिव AI ज्ञान अधिक वाढवा!

पुढील धड्यात जा जिथे आपण [Prompt Engineering Fundamentals](../04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst) पाहणार आहोत!

**अस्वीकरण**:  
हा दस्तऐवज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) वापरून अनुवादित केला आहे. आम्ही अचूकतेसाठी प्रयत्नशील असलो तरी, कृपया लक्षात घ्या की स्वयंचलित अनुवादांमध्ये चुका किंवा अचूकतेची कमतरता असू शकते. मूळ दस्तऐवज त्याच्या स्थानिक भाषेत अधिकृत स्रोत मानला जावा. महत्त्वाच्या माहितीसाठी व्यावसायिक मानवी अनुवाद करण्याची शिफारस केली जाते. या अनुवादाच्या वापरामुळे उद्भवणाऱ्या कोणत्याही गैरसमजुती किंवा चुकीच्या अर्थलागी आम्ही जबाबदार नाही.