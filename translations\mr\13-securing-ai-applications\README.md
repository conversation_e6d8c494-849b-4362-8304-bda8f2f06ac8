<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "f3cac698e9eea47dd563633bd82daf8c",
  "translation_date": "2025-07-09T15:20:07+00:00",
  "source_file": "13-securing-ai-applications/README.md",
  "language_code": "mr"
}
-->
# तुमच्या जनरेटिव्ह AI अनुप्रयोगांचे संरक्षण

[![तुमच्या जनरेटिव्ह AI अनुप्रयोगांचे संरक्षण](../../../translated_images/13-lesson-banner.14103e36b4bbf17398b64ed2b0531f6f2c6549e7f7342f797c40bcae5a11862e.mr.png)](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst)

## परिचय

या धड्यात खालील गोष्टींचा समावेश असेल:

- AI प्रणालींच्या संदर्भातील सुरक्षा.
- AI प्रणालींना होणारे सामान्य धोके आणि जोखमी.
- AI प्रणाली सुरक्षित करण्याच्या पद्धती आणि विचार.

## शिकण्याचे उद्दिष्टे

हा धडा पूर्ण केल्यानंतर, तुम्हाला समजेल:

- AI प्रणालींना होणारे धोके आणि जोखमी.
- AI प्रणाली सुरक्षित करण्याच्या सामान्य पद्धती आणि सराव.
- सुरक्षा चाचणी कशी अमलात आणल्याने अनपेक्षित परिणाम आणि वापरकर्त्यांचा विश्वास कमी होण्यापासून कसे टाळता येते.

## जनरेटिव्ह AI च्या संदर्भात सुरक्षा म्हणजे काय?

जसे की आर्टिफिशियल इंटेलिजन्स (AI) आणि मशीन लर्निंग (ML) तंत्रज्ञान आपल्या जीवनावर अधिकाधिक प्रभाव टाकत आहेत, तसंच केवळ ग्राहकांचा डेटा नाही तर AI प्रणालींचे संरक्षण करणेही अत्यंत महत्त्वाचे आहे. AI/ML उच्च-मूल्य निर्णय प्रक्रियांसाठी वापरले जात आहे अशा उद्योगांमध्ये, जिथे चुकीचा निर्णय गंभीर परिणाम करू शकतो.

येथे काही महत्त्वाच्या बाबी आहेत:

- **AI/ML चा प्रभाव**: AI/ML रोजच्या जीवनावर मोठा प्रभाव टाकतात आणि त्यामुळे त्यांचे संरक्षण करणे आवश्यक झाले आहे.
- **सुरक्षा आव्हाने**: AI/ML चा हा प्रभाव योग्य लक्ष देऊन हाताळणे गरजेचे आहे, जेणेकरून ट्रोल्स किंवा संघटित गटांकडून होणाऱ्या प्रगत हल्ल्यांपासून AI-आधारित उत्पादनांचे संरक्षण करता येईल.
- **धोरणात्मक समस्या**: तंत्रज्ञान उद्योगाने दीर्घकालीन ग्राहक सुरक्षा आणि डेटा संरक्षण सुनिश्चित करण्यासाठी धोरणात्मक आव्हानांवर सक्रियपणे काम करणे आवश्यक आहे.

याशिवाय, मशीन लर्निंग मॉडेल्स बहुतेक वेळा दुर्भावनायुक्त इनपुट आणि सामान्य विचित्र डेटामध्ये फरक ओळखू शकत नाहीत. प्रशिक्षणासाठी वापरल्या जाणाऱ्या डेटाचा मोठा भाग सार्वजनिक, अनियंत्रित, आणि तृतीय पक्षांच्या योगदानासाठी खुला असतो. हल्लेखोरांना डेटासेट्स तुटवण्याची गरज नाही, कारण ते त्यात योगदान देऊ शकतात. वेळेनुसार, कमी विश्वासार्ह दुर्भावनायुक्त डेटा उच्च विश्वासार्ह डेटा बनतो, जर डेटा संरचना/फॉरमॅटिंग योग्य राहिले.

म्हणूनच, तुमच्या मॉडेल्सना निर्णय घेण्यासाठी वापरल्या जाणाऱ्या डेटाच्या साठ्यांची अखंडता आणि संरक्षण सुनिश्चित करणे अत्यंत महत्त्वाचे आहे.

## AI च्या धोके आणि जोखमी समजून घेणे

AI आणि संबंधित प्रणालींच्या संदर्भात, डेटा विषबाधा (Data Poisoning) आजच्या काळातील सर्वात मोठा सुरक्षा धोका आहे. डेटा विषबाधा म्हणजे कोणीही जाणूनबुजून AI ला प्रशिक्षण देण्यासाठी वापरल्या जाणाऱ्या माहितीमध्ये बदल करणे, ज्यामुळे AI चुकीचे निर्णय घेते. हे मानक शोध आणि प्रतिबंधक पद्धतींच्या अभावामुळे तसेच अविश्वसनीय किंवा अनियंत्रित सार्वजनिक डेटासेट्सवर अवलंबून असल्यामुळे होते. डेटा अखंडता राखण्यासाठी आणि चुकीच्या प्रशिक्षण प्रक्रियेस प्रतिबंध करण्यासाठी, तुमच्या डेटाचा स्रोत आणि वारसा ट्रॅक करणे अत्यंत आवश्यक आहे. अन्यथा, “garbage in, garbage out” हे म्हणणं खरी ठरते, ज्यामुळे मॉडेलची कार्यक्षमता प्रभावित होते.

डेटा विषबाधा तुमच्या मॉडेल्सवर कशी परिणाम करू शकते याची उदाहरणे:

1. **लेबल फ्लिपिंग**: द्वि-श्रेणी वर्गीकरण कार्यात, विरोधक जाणूनबुजून प्रशिक्षण डेटाच्या एका लहान भागाचे लेबल उलटवतो. उदाहरणार्थ, सामान्य नमुने दुर्भावनायुक्त म्हणून लेबल केले जातात, ज्यामुळे मॉडेल चुकीचे संबंध शिकते.\
   **उदाहरण**: स्पॅम फिल्टर योग्य ईमेल्सना स्पॅम म्हणून चुकीचे वर्गीकरण करतो.
2. **फीचर विषबाधा**: हल्लेखोर प्रशिक्षण डेटामधील वैशिष्ट्ये सूक्ष्मपणे बदलतो, ज्यामुळे मॉडेलला भ्रामक माहिती मिळते.\
   **उदाहरण**: उत्पादन वर्णनात अनावश्यक कीवर्ड जोडून शिफारस प्रणालींना प्रभावित करणे.
3. **डेटा इंजेक्शन**: प्रशिक्षण सेटमध्ये दुर्भावनायुक्त डेटा घालून मॉडेलच्या वर्तनावर परिणाम करणे.\
   **उदाहरण**: खोटे वापरकर्ता पुनरावलोकने जोडून भावना विश्लेषण परिणाम बदलणे.
4. **बॅकडोअर हल्ले**: विरोधक प्रशिक्षण डेटामध्ये लपलेला नमुना (बॅकडोअर) घालतो. मॉडेल हा नमुना ओळखते आणि सक्रिय झाल्यावर दुर्भावनायुक्त वर्तन करते.\
   **उदाहरण**: बॅकडोअर असलेल्या प्रतिमांसह प्रशिक्षित चेहरा ओळख प्रणाली विशिष्ट व्यक्तीला चुकीचे ओळखते.

MITRE Corporation ने [ATLAS (Adversarial Threat Landscape for Artificial-Intelligence Systems)](https://atlas.mitre.org/?WT.mc_id=academic-105485-koreyst) तयार केला आहे, जो AI प्रणालींवर होणाऱ्या प्रत्यक्ष हल्ल्यांमध्ये विरोधक वापरतात अशा तंत्र आणि पद्धतींचा ज्ञानकोश आहे.

> AI-सक्षम प्रणालींमध्ये वाढत्या संख्येने असुरक्षितता आढळत आहेत, कारण AI चा समावेश पारंपरिक सायबर-हल्ल्यांच्या पलीकडे हल्ल्याचा पृष्ठभाग वाढवतो. आम्ही ATLAS तयार केला आहे जेणेकरून या अनोख्या आणि विकसित होत असलेल्या असुरक्षिततेबाबत जागरूकता वाढेल, कारण जागतिक समुदाय विविध प्रणालींमध्ये AI अधिकाधिक समाविष्ट करत आहे. ATLAS हे MITRE ATT&CK® फ्रेमवर्कवर आधारित आहे आणि त्याचे तंत्र, पद्धती, आणि प्रक्रिये ATT&CK मधील तंत्रांस पूरक आहेत.

MITRE ATT&CK® फ्रेमवर्क प्रमाणे, जो पारंपरिक सायबरसुरक्षेत प्रगत हल्ल्यांच्या अनुकरणासाठी वापरला जातो, ATLAS देखील सहज शोधता येणारा TTPs संच प्रदान करतो, ज्यामुळे उदयोन्मुख हल्ल्यांपासून संरक्षणासाठी तयारी करता येते.

याशिवाय, Open Web Application Security Project (OWASP) ने LLM वापरणाऱ्या अनुप्रयोगांमध्ये आढळलेल्या सर्वात गंभीर असुरक्षिततेची "[Top 10 list](https://llmtop10.com/?WT.mc_id=academic-105485-koreyst)" तयार केली आहे. या यादीत डेटा विषबाधा तसेच खालील धोके समाविष्ट आहेत:

- **प्रॉम्प्ट इंजेक्शन**: हल्लेखोर काळजीपूर्वक तयार केलेल्या इनपुटद्वारे मोठ्या भाषा मॉडेल (LLM) ला प्रभावित करतात, ज्यामुळे ते अपेक्षित वर्तनापासून वेगळे वागतं.
- **सप्लाय चेन असुरक्षितता**: LLM वापरल्या जाणाऱ्या अनुप्रयोगांचे घटक आणि सॉफ्टवेअर, जसे की Python मॉड्यूल्स किंवा बाह्य डेटासेट्स, स्वतः असुरक्षित असू शकतात, ज्यामुळे अनपेक्षित परिणाम, पूर्वग्रह आणि पायाभूत संरचनेतील असुरक्षितता उद्भवू शकते.
- **अतिविश्वास**: LLM अपूर्ण आहेत आणि hallucinate होण्याची शक्यता असते, ज्यामुळे चुकीचे किंवा असुरक्षित परिणाम मिळू शकतात. अनेक नोंदवलेल्या प्रकरणांमध्ये लोकांनी निकालांना तोंड देऊन खऱ्या जगात नकारात्मक परिणाम झाले आहेत.

Microsoft Cloud Advocate Rod Trent यांनी एक मोफत ईबुक लिहिला आहे, [Must Learn AI Security](https://github.com/rod-trent/OpenAISecurity/tree/main/Must_Learn/Book_Version?WT.mc_id=academic-105485-koreyst), ज्यात या आणि इतर उदयोन्मुख AI धोक्यांवर सखोल चर्चा केली आहे आणि या परिस्थितीशी कसे सामना करायचा याबाबत सविस्तर मार्गदर्शन दिले आहे.

## AI प्रणाली आणि LLM साठी सुरक्षा चाचणी

कृत्रिम बुद्धिमत्ता (AI) विविध क्षेत्रे आणि उद्योगांमध्ये बदल घडवत आहे, समाजासाठी नवीन संधी आणि फायदे देत आहे. मात्र, AI काही महत्त्वाच्या आव्हानांसह येतो, जसे की डेटा गोपनीयता, पूर्वग्रह, स्पष्टतेचा अभाव, आणि संभाव्य गैरवापर. त्यामुळे, AI प्रणाली सुरक्षित आणि जबाबदार असणे आवश्यक आहे, म्हणजे त्या नैतिक आणि कायदेशीर मानकांचे पालन करतात आणि वापरकर्ते व हितधारक त्यावर विश्वास ठेवू शकतात.

सुरक्षा चाचणी म्हणजे AI प्रणाली किंवा LLM ची सुरक्षा मूल्यांकन करण्याची प्रक्रिया, ज्यात त्यातील असुरक्षितता शोधून त्याचा फायदा घेण्याचा प्रयत्न केला जातो. ही चाचणी विकसक, वापरकर्ते किंवा तृतीय-पक्ष ऑडिटर्स करू शकतात, चाचणीच्या उद्देशानुसार आणि व्याप्तीनुसार. AI प्रणाली आणि LLM साठी काही सामान्य सुरक्षा चाचणी पद्धती:

- **डेटा स्वच्छता**: प्रशिक्षण डेटा किंवा AI प्रणाली/LLM च्या इनपुटमधून संवेदनशील किंवा खाजगी माहिती काढून टाकणे किंवा गुप्त करणे. डेटा स्वच्छता डेटा लीक आणि दुर्भावनायुक्त हस्तक्षेप टाळण्यास मदत करते.
- **विरोधी चाचणी**: AI प्रणाली किंवा LLM च्या इनपुट किंवा आउटपुटवर विरोधी उदाहरणे तयार करून त्याची मजबुती आणि हल्ल्यांपासून टिकाऊपणा तपासणे. विरोधी चाचणी हल्लेखोरांकडून होणाऱ्या दुर्बलता शोधून त्यावर उपाययोजना करण्यास मदत करते.
- **मॉडेल पडताळणी**: AI प्रणाली किंवा LLM च्या मॉडेल पॅरामीटर्स किंवा आर्किटेक्चरची अचूकता आणि पूर्णता तपासणे. मॉडेल पडताळणीने मॉडेल चोरी होण्यापासून संरक्षण होते.
- **आउटपुट पडताळणी**: AI प्रणाली किंवा LLM च्या आउटपुटची गुणवत्ता आणि विश्वासार्हता तपासणे. आउटपुट पडताळणीने दुर्भावनायुक्त हस्तक्षेप शोधून दुरुस्त करण्यास मदत होते.

OpenAI, AI प्रणालींमध्ये आघाडीवर असलेली संस्था, त्यांच्या रेड टीमिंग नेटवर्क उपक्रमाचा भाग म्हणून _सुरक्षा मूल्यांकन_ सत्रे आयोजित करते, ज्याचा उद्देश AI सुरक्षा वाढविण्यासाठी AI प्रणालींच्या आउटपुटची चाचणी करणे आहे.

> मूल्यांकन साधे प्रश्नोत्तरे पासून अधिक गुंतागुंतीच्या सिम्युलेशन्सपर्यंत असू शकतात. खाली OpenAI ने AI वर्तनांचे विविध पैलूंनी मूल्यांकन करण्यासाठी तयार केलेली काही उदाहरणे दिली आहेत:

#### पटवून देणे

- [MakeMeSay](https://github.com/openai/evals/tree/main/evals/elsuite/make_me_say/readme.md?WT.mc_id=academic-105485-koreyst): एखाद्या AI प्रणालीला दुसऱ्या AI प्रणालीला गुप्त शब्द बोलायला कसे फसवू शकते?
- [MakeMePay](https://github.com/openai/evals/tree/main/evals/elsuite/make_me_pay/readme.md?WT.mc_id=academic-105485-koreyst): एखादी AI प्रणाली दुसऱ्या AI प्रणालीला पैसे दान करण्यासाठी कशी पटवू शकते?
- [Ballot Proposal](https://github.com/openai/evals/tree/main/evals/elsuite/ballots/readme.md?WT.mc_id=academic-105485-koreyst): एखादी AI प्रणाली दुसऱ्या AI प्रणालीवर राजकीय प्रस्तावाला समर्थन देण्यासाठी कसा प्रभाव टाकू शकते?

#### स्टेगॅनोग्राफी (लपलेली संदेशवहन)

- [Steganography](https://github.com/openai/evals/tree/main/evals/elsuite/steganography/readme.md?WT.mc_id=academic-105485-koreyst): एखादी AI प्रणाली दुसऱ्या AI प्रणालीला पकडल्या न जाता गुप्त संदेश कसे पाठवू शकते?
- [Text Compression](https://github.com/openai/evals/tree/main/evals/elsuite/text_compression/readme.md?WT.mc_id=academic-105485-koreyst): एखादी AI प्रणाली संदेश कसे संकुचित आणि पुन्हा विस्तृत करू शकते, ज्यामुळे गुप्त संदेश लपवता येतात?
- [Schelling Point](https://github.com/openai/evals/blob/main/evals/elsuite/schelling_point/README.md?WT.mc_id=academic-105485-koreyst): एखादी AI प्रणाली दुसऱ्या AI प्रणालीशी थेट संवाद न करता कशी समन्वय साधू शकते?

### AI सुरक्षा

AI प्रणालींना दुर्भावनायुक्त हल्ले, गैरवापर किंवा अनपेक्षित परिणामांपासून संरक्षण करणे अत्यंत गरजेचे आहे. यामध्ये खालील बाबींचा समावेश होतो:

- AI मॉडेल्सना प्रशिक्षण देण्यासाठी आणि चालवण्यासाठी वापरल्या जाणाऱ्या डेटा आणि अल्गोरिदमचे संरक्षण करणे
- AI प्रणालींमध्ये अनधिकृत प्रवेश, हस्तक्षेप किंवा तोडफोड टाळणे
- AI प्रणालींमधील पूर्वग्रह, भेदभाव किंवा नैतिक समस्या शोधणे आणि कमी करणे
- AI निर्णय आणि क्रियांची जबाबदारी, पारदर्शकता आणि स्पष्टता सुनिश्चित करणे
- AI प्रणालींचे उद्दिष्टे आणि मूल्ये मानव आणि समाजाच्या मूल्यांशी जुळवून घेणे

AI सुरक्षा ही AI प्रणाली आणि डेटाच्या अखंडता, उपलब्धता आणि गोपनीयता सुनिश्चित करण्यासाठी महत्त्वाची आहे. AI सुरक्षेच्या काही आव्हाने आणि संधी:

- संधी: सायबरसुरक्षा धोरणांमध्ये AI समाविष्ट करणे, कारण AI धोके ओळखण्यात आणि प्रतिसाद वेळ सुधारण्यात महत्त्वाची भूमिका बजावू शकते. AI फिशिंग, मालवेअर, किंवा रॅन्समवेअर सारख्या सायबरहल्ल्यांच्या शोध आणि प्रतिबंधासाठी स्वयंचलित आणि सुधारित उपाय करू शकते.
- आव्हान: विरोधक AI चा वापर करून प्रगत हल्ले करू शकतात, जसे की खोटे किंवा भ्रामक सामग्री तयार करणे, वापरकर्त्यांचे impersonate करणे, किंवा AI प्रणालीतील असुरक्षितता वापरणे. त्यामुळे AI विकसकांवर अशी जबाबदारी आहे की ते मजबूत आणि गैरवापरापासून टिकाऊ प्रणाली डिझाइन करतील.

### डेटा संरक्षण

LLM वापरल्या जाणाऱ्या डेटाच्या गोपनीयता आणि सुरक्षेस धोके असू शकतात. उदाहरणार्थ, LLM त्यांच्या प्रशिक्षण डेटामधून संवेदनशील माहिती जसे की वैयक्तिक नावे, पत्ते, पासवर्ड किंवा क्रेडिट कार्ड नंबर लक्षात ठेवू शकतात आणि लीक करू शकतात. तसेच, दुर्भावनायुक्त व्यक्ती त्याच्या असुरक्षितता किंवा पूर्वग्रहांचा गैरवापर करू शकतात. त्यामुळे, या धोका जाणून घेऊन योग्य उपाय करणे आवश्यक आहे. LLM सोबत वापरल्या जाणाऱ्या डेटाचे संरक्षण करण्यासाठी काही पावले:

- **LLM सोबत शेअर होणाऱ्या डेटाची मात्रा आणि प्रकार मर्यादित करणे**: फक्त आवश्यक आणि संबंधित डेटा शेअर करा, संवेदनशील, गोपनीय किंवा वैयक्तिक डेटा शेअर करण्याचे टाळा. वापरकर्त्यांनी डेटाला गुप्त किंवा एन्क्रिप्ट करावे, जसे की ओळख पटवणारी माहिती काढून टाकणे किंवा सुरक्षित संवाद माध्यमांचा वापर करणे.
- **LLM तयार केलेल्या डेटाची पडताळणी करणे**: LLM कडून मिळालेल्या आउटपुटची अचूकता आणि गुणवत्ता तपासा, जेणेकरून त्यात अनावश्यक किंवा अनुचित माहिती नसेल.
- **डेटा उल्लंघन किंवा घटना ताबडतोब नोंदवणे आणि सतर्क राहणे**: LLM कडून संशयास्पद किंवा असामान्य वर्तन, जसे की असंबंधित, चुकीचा, अपमानास्पद किंवा हानिकारक मजकूर तयार करणे, याकडे लक्ष द्या. हे डेटा उल्लंघन किंवा सुरक्षा घटनेचे संकेत असू शकतात.

डेटा सुरक्षा, शासन आणि अनुपालन हे कोणत्याही संस्थेसाठी अत्यंत महत्त्वाचे आहेत जे डेटा आणि AI चा वापर मल्टी-क्लाउड वातावरणात करतात. तुमचा डेटा सुरक्षित आणि नियंत्रित करणे एक गुंतागुंतीचे आणि बहुआयामी काम आहे. तुम्हाला वेगवेगळ्या प्रकारच्या डेटाचे (संरचित, असंरचित, आणि AI द्वारे तयार केलेला डेटा) विविध ठिकाणी आणि अनेक क्लाउडमध्ये संरक्षण आणि शासन करावे लागते, तसेच विद्यमान आणि भविष्यातील डेटा सुरक्षा, शासन, आणि AI नियमांचे पालन करावे लागते. तुमच्या डेटाचे संरक्षण करण्यासाठी काही सर्वोत्तम पद्धती आणि खबरदारी घ्या, जसे की:

- डेटा संरक्षण आणि गोपनीयता वैशिष्ट्ये देणाऱ्या क्लाउड सेवा किंवा प्लॅटफॉर्मचा वापर करा.
- तुमच्या डेटामधील चुका, विसंगती किंवा विचित्रता तपासण्यासाठी डेटा गुणवत्ता आणि पडताळणी साधने वापरा.
- तुमच्या डेटाचा जबाबदार आणि पारदर्शक वापर सुनिश्चित करण्यासाठी डेटा शासन आणि
> AI रेड टीमिंगचा सराव आता अधिक विस्तृत अर्थ घेऊन विकसित झाला आहे: यामध्ये फक्त सुरक्षा कमकुवतपणांची तपासणी करणेच नाही, तर संभाव्य हानिकारक सामग्री तयार होण्यासारख्या इतर प्रणालीतील त्रुटींचीही तपासणी समाविष्ट आहे. AI प्रणाली नवीन धोके घेऊन येतात, आणि रेड टीमिंग हे अशा नवीन धोके समजून घेण्यासाठी अत्यंत महत्त्वाचे आहे, जसे की प्रॉम्प्ट इंजेक्शन आणि आधाररहित सामग्री तयार करणे. - [Microsoft AI Red Team building future of safer AI](https://www.microsoft.com/security/blog/2023/08/07/microsoft-ai-red-team-building-future-of-safer-ai/?WT.mc_id=academic-105485-koreyst)
[![Guidance and resources for red teaming](../../../translated_images/13-AI-red-team.642ed54689d7e8a4d83bdf0635768c4fd8aa41ea539d8e3ffe17514aec4b4824.mr.png)]()

खाली Microsoft च्या AI Red Team कार्यक्रमाला आकार देणाऱ्या मुख्य अंतर्दृष्टी दिल्या आहेत.

1. **AI Red Teaming चा विस्तृत क्षेत्र:**
   AI red teaming आता सुरक्षा आणि Responsible AI (RAI) परिणाम दोन्हींचा समावेश करतो. पारंपरिकपणे, red teaming मुख्यतः सुरक्षा बाबींवर लक्ष केंद्रित करत असे, जिथे मॉडेलला एक व्हेक्टर म्हणून पाहिले जात असे (उदा. underlying मॉडेल चोरी करणे). मात्र, AI प्रणाली नवीन सुरक्षा धोके (उदा. prompt injection, poisoning) आणतात, ज्यासाठी विशेष लक्ष देणे आवश्यक आहे. सुरक्षेबाहेर, AI red teaming न्याय्यतेच्या समस्या (उदा. stereotyping) आणि हानिकारक सामग्री (उदा. हिंसाचाराचे गौरवकरण) यांचाही शोध घेतो. या समस्यांची लवकर ओळख करून संरक्षणासाठी गुंतवणूक प्राधान्यक्रमित करता येतो.
2. **दुष्ट आणि निरपराध अपयश:**
   AI red teaming दुष्ट तसेच निरपराध दृष्टीकोनातून अपयशांचा विचार करतो. उदाहरणार्थ, नवीन Bing चे red teaming करताना, आम्ही फक्त दुष्ट प्रतिस्पर्धी कसे प्रणालीला फसवू शकतात हेच नव्हे तर सामान्य वापरकर्ते कसे समस्याग्रस्त किंवा हानिकारक सामग्रीला सामोरे जाऊ शकतात हेही तपासतो. पारंपरिक सुरक्षा red teaming मुख्यतः दुष्ट घटकांवर लक्ष केंद्रित करते, तर AI red teaming अधिक व्यापक व्यक्तिमत्वे आणि संभाव्य अपयशांचा विचार करतो.
3. **AI प्रणालींचा गतिशील स्वभाव:**
   AI अनुप्रयोग सतत विकसित होत असतात. मोठ्या भाषा मॉडेल अनुप्रयोगांमध्ये, विकसक बदलत्या गरजांनुसार जुळवून घेतात. सतत red teaming केल्याने सतत जागरूकता आणि विकसित होत असलेल्या धोका समजून घेणे शक्य होते.

AI red teaming सर्वसमावेशक नाही आणि ते [role-based access control (RBAC)](https://learn.microsoft.com/azure/ai-services/openai/how-to/role-based-access-control?WT.mc_id=academic-105485-koreyst) आणि व्यापक डेटा व्यवस्थापन उपायांसारख्या अतिरिक्त नियंत्रणांसाठी पूरक म्हणून पाहिले पाहिजे. हे एक सुरक्षा धोरण पूरक आहे जे सुरक्षित आणि जबाबदार AI उपाय वापरण्यावर लक्ष केंद्रित करते, ज्यात गोपनीयता आणि सुरक्षा यांचा विचार केला जातो, तसेच पूर्वग्रह, हानिकारक सामग्री आणि चुकीची माहिती कमी करण्याचा प्रयत्न केला जातो, ज्यामुळे वापरकर्त्यांचा विश्वास टिकून राहतो.

AI प्रणालींमधील धोके ओळखण्यासाठी आणि कमी करण्यासाठी red teaming कशी मदत करू शकते हे चांगल्या प्रकारे समजून घेण्यासाठी खालील अतिरिक्त वाचनाची यादी आहे:

- [मोठ्या भाषा मॉडेल्स (LLMs) आणि त्यांच्या अनुप्रयोगांसाठी red teaming चे नियोजन](https://learn.microsoft.com/azure/ai-services/openai/concepts/red-teaming?WT.mc_id=academic-105485-koreyst)
- [OpenAI Red Teaming Network म्हणजे काय?](https://openai.com/blog/red-teaming-network?WT.mc_id=academic-105485-koreyst)
- [AI Red Teaming - सुरक्षित आणि जबाबदार AI उपाय तयार करण्यासाठी एक महत्त्वाचा सराव](https://rodtrent.substack.com/p/ai-red-teaming?WT.mc_id=academic-105485-koreyst)
- MITRE [ATLAS (Adversarial Threat Landscape for Artificial-Intelligence Systems)](https://atlas.mitre.org/?WT.mc_id=academic-105485-koreyst), AI प्रणालींवर प्रत्यक्ष हल्ल्यांमध्ये प्रतिस्पर्ध्यांनी वापरलेल्या तंत्र आणि पद्धतींचा ज्ञानकोश.

## ज्ञान तपासणी

डेटा अखंडता राखण्यासाठी आणि गैरवापर टाळण्यासाठी चांगला दृष्टिकोन काय असू शकतो?

1. डेटा प्रवेश आणि डेटा व्यवस्थापनासाठी मजबूत role-based नियंत्रण असणे
1. डेटा चुकीच्या प्रकारे सादर होण्यापासून किंवा गैरवापर होण्यापासून प्रतिबंध करण्यासाठी डेटा लेबलिंगची अंमलबजावणी आणि ऑडिट करणे
1. तुमच्या AI पायाभूत सुविधेमध्ये सामग्री फिल्टरिंगला समर्थन देणे

उत्तर: 1, तीनही शिफारसी चांगल्या आहेत, पण योग्य डेटा प्रवेश अधिकार वापरकर्त्यांना देणे हे LLMs वापरल्या जाणाऱ्या डेटाच्या गैरवापर आणि चुकीच्या सादरीकरणाला प्रतिबंध करण्यासाठी फार महत्त्वाचे आहे.

## 🚀 आव्हान

AI च्या युगात तुम्ही [संवेदनशील माहितीचे संरक्षण आणि शासन](https://learn.microsoft.com/training/paths/purview-protect-govern-ai/?WT.mc_id=academic-105485-koreyst) कसे करू शकता याबद्दल अधिक वाचा.

## छान काम, तुमचे शिक्षण सुरू ठेवा

हा धडा पूर्ण केल्यानंतर, आमच्या [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) मध्ये जा आणि तुमचे Generative AI ज्ञान अधिक वाढवा!

पुढील धडा 14 कडे जा जिथे आपण [Generative AI Application Lifecycle](../14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst) पाहणार आहोत!

**अस्वीकरण**:  
हा दस्तऐवज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) वापरून अनुवादित केला आहे. आम्ही अचूकतेसाठी प्रयत्नशील असलो तरी, कृपया लक्षात घ्या की स्वयंचलित अनुवादांमध्ये चुका किंवा अचूकतेची कमतरता असू शकते. मूळ दस्तऐवज त्याच्या स्थानिक भाषेत अधिकृत स्रोत मानला जावा. महत्त्वाच्या माहितीसाठी व्यावसायिक मानवी अनुवाद करण्याची शिफारस केली जाते. या अनुवादाच्या वापरामुळे उद्भवणाऱ्या कोणत्याही गैरसमजुती किंवा चुकीच्या अर्थलागी आम्ही जबाबदार नाही.