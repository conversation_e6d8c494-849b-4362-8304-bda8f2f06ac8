<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "e2861bbca91c0567ef32bc77fe054f9e",
  "translation_date": "2025-07-09T16:08:13+00:00",
  "source_file": "15-rag-and-vector-databases/README.md",
  "language_code": "mr"
}
-->
# Retrieval Augmented Generation (RAG) आणि व्हेक्टर डेटाबेस

[![Retrieval Augmented Generation (RAG) आणि व्हेक्टर डेटाबेस](../../../translated_images/15-lesson-banner.ac49e59506175d4fc6ce521561dab2f9ccc6187410236376cfaed13cde371b90.mr.png)](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst)

सर्च अ‍ॅप्लिकेशन्सच्या धड्यात, आपण थोडक्यात शिकले की Large Language Models (LLMs) मध्ये आपले स्वतःचे डेटा कसे समाकलित करायचे. या धड्यात, आपण आपल्या LLM अ‍ॅप्लिकेशनमध्ये डेटा ग्राउंडिंगच्या संकल्पना, प्रक्रियेची यांत्रिकी आणि डेटा साठवण्याच्या पद्धती, ज्यात एम्बेडिंग्ज आणि मजकूर दोन्हीचा समावेश आहे, यावर अधिक सखोल चर्चा करू.

> **व्हिडिओ लवकरच येत आहे**

## परिचय

या धड्यात आपण खालील गोष्टी शिकू:

- RAG म्हणजे काय आणि AI (कृत्रिम बुद्धिमत्ता) मध्ये त्याचा वापर का होतो याचा परिचय.

- व्हेक्टर डेटाबेस काय असतात हे समजून घेणे आणि आपल्या अ‍ॅप्लिकेशनसाठी एक तयार करणे.

- RAG कसे अ‍ॅप्लिकेशनमध्ये समाकलित करायचे यावर एक व्यावहारिक उदाहरण.

## शिकण्याचे उद्दिष्टे

हा धडा पूर्ण केल्यावर, आपण सक्षम असाल:

- डेटा पुनर्प्राप्ती आणि प्रक्रियेत RAG चे महत्त्व समजावून सांगणे.

- RAG अ‍ॅप्लिकेशन सेटअप करणे आणि आपला डेटा LLM शी ग्राउंड करणे.

- LLM अ‍ॅप्लिकेशन्समध्ये RAG आणि व्हेक्टर डेटाबेसचे प्रभावी समाकलन करणे.

## आपली परिस्थिती: आपल्या स्वतःच्या डेटाने LLM सुधारित करणे

या धड्यासाठी, आपण शिक्षण स्टार्टअपमध्ये आपले स्वतःचे नोट्स जोडू इच्छितो, ज्यामुळे चॅटबॉट विविध विषयांवर अधिक माहिती मिळवू शकेल. आपल्याकडे असलेल्या नोट्सचा वापर करून, शिकणाऱ्यांना अभ्यास करणे सोपे होईल आणि विविध विषय समजून घेणे सुलभ होईल, ज्यामुळे त्यांना परीक्षेची तयारी करणे सोपे जाईल. आपली परिस्थिती तयार करण्यासाठी, आपण वापरणार आहोत:

- `Azure OpenAI:` आपला चॅटबॉट तयार करण्यासाठी वापरले जाणारे LLM

- `AI for beginners' lesson on Neural Networks:` हा डेटा ज्यावर आपला LLM ग्राउंड होईल

- `Azure AI Search` आणि `Azure Cosmos DB:` व्हेक्टर डेटाबेस जे आपला डेटा साठवेल आणि सर्च इंडेक्स तयार करेल

वापरकर्ते त्यांच्या नोट्समधून प्रॅक्टिस क्विझ तयार करू शकतील, पुनरावलोकनासाठी फ्लॅश कार्ड्स तयार करू शकतील आणि त्याचा सारांश संक्षिप्त आढाव म्हणून घेऊ शकतील. सुरुवात करण्यासाठी, पाहूया RAG काय आहे आणि ते कसे कार्य करते:

## Retrieval Augmented Generation (RAG)

LLM-शक्तीने चालणारा चॅटबॉट वापरकर्त्यांच्या प्रश्नांना प्रक्रिया करून प्रतिसाद तयार करतो. तो संवादात्मक असतो आणि वापरकर्त्यांशी विविध विषयांवर संवाद साधतो. मात्र, त्याचे प्रतिसाद दिलेल्या संदर्भ आणि त्याच्या मूलभूत प्रशिक्षण डेटावर मर्यादित असतात. उदाहरणार्थ, GPT-4 ची ज्ञानाची मर्यादा सप्टेंबर 2021 पर्यंत आहे, म्हणजे त्याला त्यानंतर घडलेल्या घटनांची माहिती नाही. शिवाय, LLM प्रशिक्षणासाठी वापरलेला डेटा गोपनीय माहिती जसे की वैयक्तिक नोट्स किंवा कंपनीच्या उत्पादन मॅन्युअलचा समावेश करत नाही.

### RAG (Retrieval Augmented Generation) कसे कार्य करते

![RAG कसे कार्य करते हे दाखवणारे चित्र](../../../translated_images/how-rag-works.f5d0ff63942bd3a638e7efee7a6fce7f0787f6d7a1fca4e43f2a7a4d03cde3e0.mr.png)

समजा तुम्हाला तुमच्या नोट्समधून क्विझ तयार करणारा चॅटबॉट तयार करायचा आहे, तर तुम्हाला ज्ञानाधाराशी कनेक्शन आवश्यक आहे. इथे RAG मदतीला येतो. RAG खालीलप्रमाणे कार्य करतो:

- **ज्ञानाधार:** पुनर्प्राप्तीपूर्वी, या दस्तऐवजांना इन्गेस्ट आणि प्रीप्रोसेस करणे आवश्यक असते, सहसा मोठ्या दस्तऐवजांना लहान तुकड्यांमध्ये विभागणे, त्यांना टेक्स्ट एम्बेडिंगमध्ये रूपांतरित करणे आणि डेटाबेसमध्ये साठवणे.

- **वापरकर्ता क्वेरी:** वापरकर्ता प्रश्न विचारतो

- **पुनर्प्राप्ती:** वापरकर्ता प्रश्न विचारल्यावर, एम्बेडिंग मॉडेल आमच्या ज्ञानाधारातून संबंधित माहिती शोधून काढते, जी प्रॉम्प्टमध्ये समाविष्ट केली जाते.

- **वर्धित जनरेशन:** LLM त्याच्या प्रतिसादात पुनर्प्राप्त केलेल्या डेटावर आधारित सुधारणा करते. त्यामुळे प्रतिसाद फक्त पूर्व-प्रशिक्षित डेटावर आधारित नसून, जोडलेल्या संदर्भातील संबंधित माहितीवरही आधारित असतो. पुनर्प्राप्त केलेला डेटा LLM च्या प्रतिसादांना वर्धित करण्यासाठी वापरला जातो. नंतर LLM वापरकर्त्याच्या प्रश्नाला उत्तर देते.

![RAG आर्किटेक्चर दाखवणारे चित्र](../../../translated_images/encoder-decode.f2658c25d0eadee2377bb28cf3aee8b67aa9249bf64d3d57bb9be077c4bc4e1a.mr.png)

RAG चे आर्किटेक्चर ट्रान्सफॉर्मर्स वापरून राबवले जाते ज्यात दोन भाग असतात: एन्कोडर आणि डिकोडर. उदाहरणार्थ, जेव्हा वापरकर्ता प्रश्न विचारतो, तेव्हा इनपुट मजकूर 'एन्कोड' होतो ज्यात शब्दांचे अर्थ कॅप्चर केले जातात आणि व्हेक्टर 'डिकोड' होऊन आमच्या दस्तऐवज इंडेक्समध्ये रूपांतरित होतो आणि वापरकर्त्याच्या क्वेरीवर आधारित नवीन मजकूर तयार करतो. LLM आउटपुट तयार करण्यासाठी एन्कोडर-डिकोडर मॉडेल दोन्ही वापरतो.

प्रस्तावित पेपरनुसार RAG अंमलबजावणीसाठी दोन पद्धती आहेत: [Retrieval-Augmented Generation for Knowledge intensive NLP Tasks](https://arxiv.org/pdf/2005.11401.pdf?WT.mc_id=academic-105485-koreyst):

- **_RAG-Sequence_**: वापरकर्त्याच्या प्रश्नासाठी सर्वोत्तम उत्तर भाकीत करण्यासाठी पुनर्प्राप्त दस्तऐवज वापरतो

- **RAG-Token**: पुढील टोकन तयार करण्यासाठी दस्तऐवज वापरतो, नंतर वापरकर्त्याच्या प्रश्नाचे उत्तर देण्यासाठी त्यांना पुनर्प्राप्त करतो

### तुम्ही RAG का वापराल?

- **माहितीची समृद्धी:** मजकूर प्रतिसाद अद्ययावत आणि वर्तमान ठेवतो. त्यामुळे विशिष्ट क्षेत्रातील कार्यक्षमतेत सुधारणा होते कारण तो अंतर्गत ज्ञानाधारात प्रवेश करतो.

- वापरकर्त्यांच्या क्वेरीस संदर्भ देण्यासाठी **तपासण्यायोग्य डेटाचा** वापर करून बनावट माहिती कमी करतो.

- LLM चे फाइन-ट्यूनिंग करण्याच्या तुलनेत **कमी खर्चिक** आहे.

## ज्ञानाधार तयार करणे

आपले अ‍ॅप्लिकेशन आपल्या वैयक्तिक डेटावर आधारित आहे, म्हणजे AI For Beginners च्या Neural Network धड्याचा डेटा.

### व्हेक्टर डेटाबेस

व्हेक्टर डेटाबेस पारंपरिक डेटाबेसपेक्षा वेगळा असतो, तो एम्बेड केलेल्या व्हेक्टरसाठी खास तयार केलेला डेटाबेस आहे. तो दस्तऐवजांचे संख्यात्मक प्रतिनिधित्व साठवतो. डेटाला संख्यात्मक एम्बेडिंगमध्ये विभागल्याने आपल्या AI प्रणालीसाठी डेटा समजणे आणि प्रक्रिया करणे सोपे होते.

आम्ही आमचे एम्बेडिंग्ज व्हेक्टर डेटाबेसमध्ये साठवतो कारण LLM ला इनपुट म्हणून स्वीकारण्याची टोकन्सची मर्यादा असते. संपूर्ण एम्बेडिंग्ज LLM कडे देता येत नाहीत, त्यामुळे त्यांना तुकड्यांमध्ये विभागणे आवश्यक असते आणि जेव्हा वापरकर्ता प्रश्न विचारतो, तेव्हा प्रश्नाशी सर्वात जास्त सुसंगत एम्बेडिंग्ज प्रॉम्प्टसह परत दिल्या जातात. तुकड्यांमध्ये विभागल्याने LLM कडे पाठवलेल्या टोकन्सची संख्या कमी होते आणि खर्चही कमी होतो.

काही लोकप्रिय व्हेक्टर डेटाबेसमध्ये Azure Cosmos DB, Clarifyai, Pinecone, Chromadb, ScaNN, Qdrant आणि DeepLake यांचा समावेश आहे. तुम्ही Azure CLI वापरून Azure Cosmos DB मॉडेल तयार करू शकता:

```bash
az login
az group create -n <resource-group-name> -l <location>
az cosmosdb create -n <cosmos-db-name> -r <resource-group-name>
az cosmosdb list-keys -n <cosmos-db-name> -g <resource-group-name>
```

### मजकूरापासून एम्बेडिंग्जपर्यंत

आपला डेटा साठवण्यापूर्वी, तो व्हेक्टर एम्बेडिंग्जमध्ये रूपांतरित करणे आवश्यक आहे. जर तुम्ही मोठ्या दस्तऐवजांवर किंवा लांब मजकूरावर काम करत असाल, तर तुम्ही अपेक्षित क्वेरींनुसार त्यांना तुकड्यांमध्ये विभागू शकता. तुकडे वाक्य पातळीवर किंवा परिच्छेद पातळीवर करता येतात. तुकड्यांमध्ये शब्दांच्या आसपासचा अर्थ समाविष्ट असल्याने, तुम्ही तुकड्याला काही अतिरिक्त संदर्भ देखील जोडू शकता, जसे की दस्तऐवज शीर्षक किंवा तुकड्यापूर्वी किंवा नंतरचा काही मजकूर. तुम्ही डेटा खालीलप्रमाणे तुकड्यांमध्ये विभागू शकता:

```python
def split_text(text, max_length, min_length):
    words = text.split()
    chunks = []
    current_chunk = []

    for word in words:
        current_chunk.append(word)
        if len(' '.join(current_chunk)) < max_length and len(' '.join(current_chunk)) > min_length:
            chunks.append(' '.join(current_chunk))
            current_chunk = []

    # If the last chunk didn't reach the minimum length, add it anyway
    if current_chunk:
        chunks.append(' '.join(current_chunk))

    return chunks
```

तुकड्यांमध्ये विभागल्यानंतर, आपण वेगवेगळ्या एम्बेडिंग मॉडेल्स वापरून मजकूर एम्बेड करू शकतो. काही मॉडेल्स जसे की word2vec, OpenAI चे ada-002, Azure Computer Vision आणि बरेच काही वापरता येतात. कोणते मॉडेल वापरायचे हे तुम्ही वापरत असलेल्या भाषांवर, एन्कोड होणाऱ्या सामग्रीच्या प्रकारावर (मजकूर/प्रतिमा/ऑडिओ), इनपुट आकारावर आणि एम्बेडिंग आउटपुटच्या लांबीवर अवलंबून असते.

OpenAI च्या `text-embedding-ada-002` मॉडेलने एम्बेड केलेल्या मजकूराचे उदाहरण:

![cat या शब्दाचे एम्बेडिंग](../../../translated_images/cat.74cbd7946bc9ca380a8894c4de0c706a4f85b16296ffabbf52d6175df6bf841e.mr.png)

## पुनर्प्राप्ती आणि व्हेक्टर सर्च

जेव्हा वापरकर्ता प्रश्न विचारतो, तेव्हा रिट्रीव्हर क्वेरी एन्कोडर वापरून त्याला व्हेक्टरमध्ये रूपांतरित करतो, नंतर आमच्या दस्तऐवज सर्च इंडेक्समध्ये संबंधित व्हेक्टर शोधतो जे इनपुटशी संबंधित असतात. नंतर इनपुट व्हेक्टर आणि दस्तऐवज व्हेक्टर दोन्ही मजकूरात रूपांतरित करून LLM कडे पाठवले जातात.

### पुनर्प्राप्ती

पुनर्प्राप्ती म्हणजे सिस्टम जलदपणे त्या दस्तऐवजांना शोधण्याचा प्रयत्न करते जे शोध निकष पूर्ण करतात. रिट्रीव्हरचे उद्दिष्ट असे दस्तऐवज मिळवणे आहे जे संदर्भ पुरवतील आणि LLM ला तुमच्या डेटावर ग्राउंड करतील.

आमच्या डेटाबेसमध्ये शोध करण्याचे काही मार्ग आहेत:

- **कीवर्ड सर्च** - मजकूर शोधासाठी वापरले जाते

- **सिमॅंटिक सर्च** - शब्दांच्या अर्थाचा वापर करते

- **व्हेक्टर सर्च** - दस्तऐवजांना एम्बेडिंग मॉडेल्स वापरून टेक्स्टमधून व्हेक्टरमध्ये रूपांतरित करते. पुनर्प्राप्ती वापरकर्त्याच्या प्रश्नाशी सर्वात जवळच्या व्हेक्टर असलेल्या दस्तऐवजांवर केली जाते.

- **हायब्रिड** - कीवर्ड आणि व्हेक्टर सर्चचा संयोजन.

जर डेटाबेसमध्ये क्वेरीशी सुसंगत उत्तर नसेल, तर सिस्टम सर्वोत्तम माहिती परत करते, पण तुम्ही संदर्भासाठी कमाल अंतर सेट करू शकता किंवा हायब्रिड सर्च वापरू शकता ज्यात कीवर्ड आणि व्हेक्टर सर्च दोन्ही असतात. या धड्यात आपण हायब्रिड सर्च वापरणार आहोत, ज्यात दोन्ही प्रकारचा शोध असतो. आपण आपला डेटा डेटा फ्रेममध्ये साठवू ज्यात तुकडे आणि एम्बेडिंग्ज असतील.

### व्हेक्टर सादृश्यता

रिट्रीव्हर ज्ञानाधारातून जवळपास असलेले एम्बेडिंग्ज शोधतो, म्हणजेच सर्वात जवळचा शेजारी, कारण ते मजकूर समान असतात. वापरकर्ता क्वेरी विचारल्यावर, ती प्रथम एम्बेड केली जाते आणि नंतर समान एम्बेडिंग्जशी जुळवली जाते. दोन वेगवेगळ्या व्हेक्टर किती सादृश्य आहेत हे मोजण्यासाठी सामान्यतः कोसाइन सादृश्यता वापरली जाते, जी दोन व्हेक्टरमधील कोनावर आधारित असते.

आपण सादृश्यता मोजण्यासाठी इतर पर्याय देखील वापरू शकतो जसे की युक्लिडियन अंतर (व्हेक्टरच्या टोकांमधील सरळ रेषा) आणि डॉट प्रॉडक्ट (दोन व्हेक्टरमधील अनुक्रमिक घटकांच्या गुणाकारांची बेरीज).

### सर्च इंडेक्स

पुनर्प्राप्ती करताना, आपल्याला शोध करण्यापूर्वी ज्ञानाधारासाठी सर्च इंडेक्स तयार करणे आवश्यक आहे. इंडेक्स आपले एम्बेडिंग्ज साठवतो आणि मोठ्या डेटाबेसमध्येही सर्वात सादृश्य तुकडे पटकन शोधू शकतो. आपण स्थानिकपणे आपला इंडेक्स तयार करू शकतो:

```python
from sklearn.neighbors import NearestNeighbors

embeddings = flattened_df['embeddings'].to_list()

# Create the search index
nbrs = NearestNeighbors(n_neighbors=5, algorithm='ball_tree').fit(embeddings)

# To query the index, you can use the kneighbors method
distances, indices = nbrs.kneighbors(embeddings)
```

### पुनःमूल्यांकन (Re-ranking)

डेटाबेस क्वेरी केल्यानंतर, तुम्हाला कदाचित निकाल सर्वात संबंधिततेनुसार क्रमवारी लावावी लागेल. पुनःमूल्यांकन करणारा LLM मशीन लर्निंग वापरून शोध निकालांची संबंधितता सुधारतो आणि त्यांना सर्वात संबंधिततेनुसार क्रमवारी लावतो. Azure AI Search वापरून, पुनःमूल्यांकन आपोआप सिमॅंटिक रीरँकर वापरून केले जाते. जवळच्या शेजाऱ्यांचा वापर करून पुनःमूल्यांकन कसे कार्य करते याचे उदाहरण:

```python
# Find the most similar documents
distances, indices = nbrs.kneighbors([query_vector])

index = []
# Print the most similar documents
for i in range(3):
    index = indices[0][i]
    for index in indices[0]:
        print(flattened_df['chunks'].iloc[index])
        print(flattened_df['path'].iloc[index])
        print(flattened_df['distances'].iloc[index])
    else:
        print(f"Index {index} not found in DataFrame")
```

## सर्व एकत्र आणणे

शेवटचा टप्पा म्हणजे आपला LLM समाविष्ट करणे जेणेकरून आपल्याला आपल्या डेटावर आधारित प्रतिसाद मिळू शकतील. आपण ते खालीलप्रमाणे अंमलात आणू शकतो:

```python
user_input = "what is a perceptron?"

def chatbot(user_input):
    # Convert the question to a query vector
    query_vector = create_embeddings(user_input)

    # Find the most similar documents
    distances, indices = nbrs.kneighbors([query_vector])

    # add documents to query  to provide context
    history = []
    for index in indices[0]:
        history.append(flattened_df['chunks'].iloc[index])

    # combine the history and the user input
    history.append(user_input)

    # create a message object
    messages=[
        {"role": "system", "content": "You are an AI assistant that helps with AI questions."},
        {"role": "user", "content": history[-1]}
    ]

    # use chat completion to generate a response
    response = openai.chat.completions.create(
        model="gpt-4",
        temperature=0.7,
        max_tokens=800,
        messages=messages
    )

    return response.choices[0].message

chatbot(user_input)
```

## आपले अ‍ॅप्लिकेशन मूल्यांकन करणे

### मूल्यांकन मेट्रिक्स

- दिलेल्या प्रतिसादांची गुणवत्ता: ते नैसर्गिक, प्रवाही आणि मानवीसारखे वाटतात का

- डेटाचा ग्राउंडिंग: प्रतिसाद पुरवलेल्या दस्तऐवजांवर आधारित आहे का हे तपासणे

- संबंधितता: प्रतिसाद प्रश्नाशी जुळतो आणि संबंधित आहे का

- प्रवाहीपणा - प्रतिसाद व्याकरणदृष्ट्या योग्य आहे का

## RAG (Retrieval Augmented Generation) आणि व्हेक्टर डेटाबेस वापरण्याचे उपयोग

अनेक वेगवेगळे उपयोग आहेत जिथे फंक्शन कॉल्स तुमच्या अ‍ॅपमध्ये सुधारणा करू शकतात जसे की:

- प्रश्नोत्तरे: तुमच्या कंपनीच्या डेटाला चॅटमध्ये ग्राउंड करणे जे कर्मचारी प्रश्न विचारण्यासाठी वापरू शकतात.

- शिफारस प्रणाली: जिथे तुम्ही अशा प्रणाली तयार करू शकता जी सर्वात सादृश्य मूल्ये जसे की चित्रपट, रेस्टॉरंट्स इत्यादी जुळवते.

- चॅटबॉट सेवा: तुम्ही चॅट इतिहास साठवू शकता आणि वापरकर्त्याच्या डेटावर आधारित संभाषण वैयक्तिकृत करू शकता.

- व्हेक्टर एम्बेडिंग्जवर आधारित प्रतिमा शोध, जे प्रतिमा ओळख आणि अपवाद शोधण्यासाठी उपयुक्त आहे.

## सारांश

आपण RAG चे मूलभूत भाग समजून घेतले आहेत, जसे की आपला डेटा अ‍ॅप्लिकेशनमध्ये जोडणे, वापरकर्ता क्वेरी आणि आउटपुट. RAG तयार करणे सोपे करण्यासाठी, तुम्ही Semanti Kernel, Langchain किंवा Autogen सारखे फ्रेमवर्क वापरू शकता.

## असाइनमेंट

Retrieval Augmented Generation (RAG) चे शिक्षण सुरू ठेवण्यासाठी तुम्ही तयार करू शकता:

- तुमच्या पसंतीच्या फ्रेमवर्कचा वापर करून अ‍ॅप्लिकेशनसाठी फ्रंट-एंड तयार करा

- LangChain किंवा Semantic Kernel पैकी कोणतेही फ्रेमवर्क वापरून तुमचे अ‍ॅप्लिकेशन पुन्हा तयार करा.

धडा पूर्ण केल्याबद्दल अभिनंदन 👏.

## शिक्षण येथे थांबत नाही, प्रवास सुरू ठेवा

हा धडा पूर्ण केल्यानंतर, आमच्या [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) मध्ये भेट द्या आणि तुमचे Generative AI ज्ञान अधिक वाढवा!

**अस्वीकरण**:  
हा दस्तऐवज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) वापरून अनुवादित केला आहे. आम्ही अचूकतेसाठी प्रयत्नशील असलो तरी, कृपया लक्षात घ्या की स्वयंचलित अनुवादांमध्ये चुका किंवा अचूकतेचा अभाव असू शकतो. मूळ दस्तऐवज त्याच्या स्थानिक भाषेत अधिकृत स्रोत मानला जावा. महत्त्वाच्या माहितीसाठी व्यावसायिक मानवी अनुवाद करण्याची शिफारस केली जाते. या अनुवादाच्या वापरामुळे उद्भवलेल्या कोणत्याही गैरसमजुती किंवा चुकीच्या अर्थलागी आम्ही जबाबदार नाही.