<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:29:12+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "mr"
}
-->
# न्यूरल नेटवर्क फ्रेमवर्क्स

आपण आधीच शिकले आहे की, न्यूरल नेटवर्क्स प्रभावीपणे ट्रेन करण्यासाठी आपल्याला दोन गोष्टी कराव्या लागतात:

* टेन्सरवर ऑपरेशन करणे, उदा. गुणाकार, बेरीज करणे, आणि सिग्मॉइड किंवा सॉफ्टमॅक्स सारख्या काही फंक्शन्सची गणना करणे
* सर्व अभिव्यक्तींचे ग्रेडियंट्स काढणे, जेणेकरून ग्रेडियंट डिसेंट ऑप्टिमायझेशन करता येईल

`numpy` लायब्ररी पहिला भाग करू शकते, पण ग्रेडियंट्स काढण्यासाठी आपल्याला काही यंत्रणा हवी असते. मागील विभागात आपण तयार केलेल्या फ्रेमवर्कमध्ये `backward` मेथडमध्ये सर्व डेरिव्हेटिव्ह फंक्शन्स मॅन्युअली प्रोग्राम करावे लागले, जी बॅकप्रॉपगेशन करते. आदर्शपणे, एखाद्या फ्रेमवर्कने आपल्याला *कोणत्याही अभिव्यक्तीचे* ग्रेडियंट्स काढण्याची संधी द्यावी.

दुसरी महत्त्वाची गोष्ट म्हणजे GPU किंवा इतर विशेष संगणकीय युनिट्स, जसे की TPU, वर गणना करता येणे. डीप न्यूरल नेटवर्क ट्रेनिंगसाठी *खूप* गणना आवश्यक असते, आणि GPU वर त्या गणनांची समांतर प्रक्रिया करणे फार महत्त्वाचे आहे.

> ✅ 'parallelize' म्हणजे गणना अनेक उपकरणांवर विभागणे.

सध्या, दोन सर्वात लोकप्रिय न्यूरल फ्रेमवर्क्स आहेत: TensorFlow आणि PyTorch. दोन्ही CPU आणि GPU वर टेन्सरवर ऑपरेशन करण्यासाठी लो-लेव्हल API देतात. या लो-लेव्हल API च्या वर, उच्च-स्तरीय API देखील आहे, ज्याला अनुक्रमे Keras आणि PyTorch Lightning म्हणतात.

Low-Level API | TensorFlow | PyTorch
--------------|-------------------------------|---------------------------
High-level API| Keras | PyTorch

दोन्ही फ्रेमवर्क्समधील **लो-लेव्हल API** आपल्याला **कंप्युटेशनल ग्राफ्स** तयार करण्याची परवानगी देतात. हा ग्राफ इनपुट पॅरामीटर्ससह आउटपुट (साधारणपणे लॉस फंक्शन) कसा काढायचा हे ठरवतो, आणि GPU उपलब्ध असल्यास त्यावर गणना करता येते. या ग्राफचे डिफरेंशिएशन करून ग्रेडियंट्स काढण्यासाठी फंक्शन्स आहेत, जे नंतर मॉडेल पॅरामीटर्स ऑप्टिमायझ करण्यासाठी वापरले जातात.

**उच्च-स्तरीय API** मुख्यतः न्यूरल नेटवर्क्सना **लेयर्सच्या अनुक्रम** म्हणून पाहतात, आणि त्यामुळे बहुतेक न्यूरल नेटवर्क्स तयार करणे खूप सोपे होते. मॉडेल ट्रेन करण्यासाठी सामान्यतः डेटा तयार करावा लागतो आणि नंतर `fit` फंक्शन कॉल करावे लागते.

उच्च-स्तरीय API आपल्याला अनेक तपशीलांबद्दल काळजी न करता सामान्य न्यूरल नेटवर्क्स पटकन तयार करण्याची परवानगी देतो. त्याच वेळी, लो-लेव्हल API ट्रेनिंग प्रक्रियेवर अधिक नियंत्रण देतो, त्यामुळे संशोधनात नवीन न्यूरल नेटवर्क आर्किटेक्चरवर काम करताना त्याचा अधिक वापर होतो.

हे देखील महत्त्वाचे आहे की आपण दोन्ही API एकत्र वापरू शकता, उदा. आपण लो-लेव्हल API वापरून स्वतःचा नेटवर्क लेयर आर्किटेक्चर विकसित करू शकता आणि नंतर तो उच्च-स्तरीय API वापरून तयार आणि ट्रेन केलेल्या मोठ्या नेटवर्कमध्ये वापरू शकता. किंवा आपण उच्च-स्तरीय API वापरून लेयर्सचा अनुक्रम तयार करू शकता आणि नंतर स्वतःचा लो-लेव्हल ट्रेनिंग लूप वापरून ऑप्टिमायझेशन करू शकता. दोन्ही API एकाच मूलभूत संकल्पनांवर आधारित आहेत आणि एकत्र चांगले काम करण्यासाठी डिझाइन केलेले आहेत.

## शिकणे

या कोर्समध्ये, आम्ही PyTorch आणि TensorFlow दोन्हीसाठी बहुतेक सामग्री देतो. आपण आपला पसंतीचा फ्रेमवर्क निवडू शकता आणि फक्त त्याच्याशी संबंधित नोटबुक्स पाहू शकता. जर कोणता फ्रेमवर्क निवडायचा हे ठरवता नसेल, तर इंटरनेटवर **PyTorch विरुद्ध TensorFlow** या विषयावर चर्चा वाचा. तसेच दोन्ही फ्रेमवर्क्स पाहून अधिक चांगले समजून घेऊ शकता.

जिथे शक्य असेल, तिथे सोपेपणासाठी उच्च-स्तरीय API वापरू. मात्र, न्यूरल नेटवर्क्स कसे काम करतात हे मूळपासून समजून घेणे महत्त्वाचे असल्याने सुरुवातीला आपण लो-लेव्हल API आणि टेन्सरवर काम करतो. पण जर तुम्हाला लवकर सुरुवात करायची असेल आणि तपशील शिकण्यात वेळ घालवायचा नसेल, तर तुम्ही ते वगळून थेट उच्च-स्तरीय API नोटबुक्समध्ये जाऊ शकता.

## ✍️ सराव: फ्रेमवर्क्स

खालील नोटबुक्समध्ये तुमचे शिक्षण सुरू ठेवा:

Low-Level API | TensorFlow+Keras नोटबुक | PyTorch
--------------|-------------------------------|---------------------------
High-level API| Keras | *PyTorch Lightning*

फ्रेमवर्क्समध्ये पारंगत झाल्यानंतर, आपण ओव्हरफिटिंगची संकल्पना पुन्हा पाहूया.

# ओव्हरफिटिंग

ओव्हरफिटिंग हा मशीन लर्निंगमधील अत्यंत महत्त्वाचा विषय आहे, आणि त्याला योग्य प्रकारे समजून घेणे फार गरजेचे आहे!

खालील समस्येचा विचार करा, ज्यात 5 डॉट्स (खालील ग्राफमध्ये `x` ने दर्शविलेले) चे सादृश्य काढायचे आहे:

!linear | overfit
-------------------------|--------------------------
**रेषीय मॉडेल, 2 पॅरामीटर्स** | **नॉन-रेषीय मॉडेल, 7 पॅरामीटर्स**
ट्रेनिंग त्रुटी = 5.3 | ट्रेनिंग त्रुटी = 0
व्हॅलिडेशन त्रुटी = 5.1 | व्हॅलिडेशन त्रुटी = 20

* डावीकडे, आपल्याला चांगली सरळ रेषा दिसते. कारण पॅरामीटर्सची संख्या योग्य आहे, मॉडेल पॉइंट्सच्या वितरणाचा योग्य अर्थ लावते.
* उजवीकडे, मॉडेल खूप शक्तिशाली आहे. आपल्याकडे फक्त 5 पॉइंट्स आहेत आणि मॉडेलमध्ये 7 पॅरामीटर्स आहेत, त्यामुळे ते सर्व पॉइंट्सवरून जाऊ शकते, ज्यामुळे ट्रेनिंग त्रुटी 0 होते. पण त्यामुळे मॉडेलला डेटामागील योग्य नमुना समजत नाही, त्यामुळे व्हॅलिडेशन त्रुटी खूप जास्त आहे.

मॉडेलच्या समृद्धी (पॅरामीटर्सची संख्या) आणि ट्रेनिंग सॅम्पल्सच्या संख्येमध्ये योग्य संतुलन राखणे फार महत्त्वाचे आहे.

## ओव्हरफिटिंग का होते

  * ट्रेनिंग डेटाची कमतरता
  * खूप शक्तिशाली मॉडेल
  * इनपुट डेटामध्ये खूप आवाज

## ओव्हरफिटिंग कसे ओळखायचे

वरील ग्राफवरून दिसते की, ओव्हरफिटिंगची ओळख ट्रेनिंग त्रुटी खूप कमी आणि व्हॅलिडेशन त्रुटी जास्त असल्यावर होते. सामान्यतः ट्रेनिंग दरम्यान आपण दोन्ही ट्रेनिंग आणि व्हॅलिडेशन त्रुटी कमी होताना पाहतो, पण नंतर काही वेळा व्हॅलिडेशन त्रुटी कमी होणे थांबते आणि वाढू लागते. हे ओव्हरफिटिंगचे संकेत असतात, आणि यावरून आपण ट्रेनिंग थांबवावे (किंवा किमान मॉडेलचा स्नॅपशॉट घ्यावा).

overfitting

## ओव्हरफिटिंग कसे टाळायचे

जर तुम्हाला ओव्हरफिटिंग होत असल्याचे दिसले, तर खालीलपैकी काही करा:

 * ट्रेनिंग डेटाची मात्रा वाढवा
 * मॉडेलची जटिलता कमी करा
 * काही रेग्युलरायझेशन तंत्र वापरा, जसे की Dropout, ज्याचा विचार आपण नंतर करू.

## ओव्हरफिटिंग आणि बायस-व्हेरियन्स ट्रेडऑफ

ओव्हरफिटिंग हा आकडेवारीतील एक सामान्य समस्या, बायस-व्हेरियन्स ट्रेडऑफचा एक प्रकार आहे. जर आपण मॉडेलमधील संभाव्य त्रुटींचे स्रोत पाहिले, तर दोन प्रकारच्या त्रुटी दिसतात:

* **बायस त्रुटी** आपल्या अल्गोरिदममुळे येते, जे ट्रेनिंग डेटामधील संबंध योग्यरित्या पकडू शकत नाही. हे मॉडेल पुरेसे शक्तिशाली नसल्यामुळे होते (**अंडरफिटिंग**).
* **व्हेरियन्स त्रुटी** मॉडेल इनपुट डेटातील आवाजाचा अंदाज लावते, खऱ्या संबंधाऐवजी (**ओव्हरफिटिंग**).

ट्रेनिंग दरम्यान, बायस त्रुटी कमी होते (मॉडेल डेटाचा अंदाज लावायला शिकतो), आणि व्हेरियन्स त्रुटी वाढते. ओव्हरफिटिंग टाळण्यासाठी ट्रेनिंग थांबवणे महत्त्वाचे आहे - मॅन्युअली (ओव्हरफिटिंग दिसल्यावर) किंवा आपोआप (रेग्युलरायझेशन वापरून).

## निष्कर्ष

या धड्यात, तुम्ही दोन सर्वात लोकप्रिय AI फ्रेमवर्क्स, TensorFlow आणि PyTorch, यांचे विविध API मधील फरक शिकले. तसेच, ओव्हरफिटिंग या अत्यंत महत्त्वाच्या विषयाबद्दलही माहिती मिळवली.

## 🚀 आव्हान

संबंधित नोटबुक्सच्या शेवटी 'टास्क' दिलेले आहेत; नोटबुक्स पूर्ण करा आणि टास्क पूर्ण करा.

## पुनरावलोकन आणि स्वअध्ययन

खालील विषयांवर थोडे संशोधन करा:

- TensorFlow
- PyTorch
- ओव्हरफिटिंग

खालील प्रश्न स्वतःला विचारा:

- TensorFlow आणि PyTorch मधील फरक काय आहे?
- ओव्हरफिटिंग आणि अंडरफिटिंग यामध्ये काय फरक आहे?

## असाइनमेंट

या लॅबमध्ये, तुम्हाला PyTorch किंवा TensorFlow वापरून सिंगल- आणि मल्टी-लेयर्ड फुली-कनेक्टेड नेटवर्क्स वापरून दोन वर्गीकरण समस्या सोडवायच्या आहेत.

**अस्वीकरण**:  
हा दस्तऐवज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) वापरून अनुवादित केला आहे. आम्ही अचूकतेसाठी प्रयत्नशील असलो तरी, कृपया लक्षात घ्या की स्वयंचलित अनुवादांमध्ये चुका किंवा अचूकतेची कमतरता असू शकते. मूळ दस्तऐवज त्याच्या स्थानिक भाषेत अधिकृत स्रोत मानला जावा. महत्त्वाच्या माहितीसाठी व्यावसायिक मानवी अनुवाद करण्याची शिफारस केली जाते. या अनुवादाच्या वापरामुळे उद्भवलेल्या कोणत्याही गैरसमजुती किंवा चुकीच्या अर्थलागी आम्ही जबाबदार नाही.