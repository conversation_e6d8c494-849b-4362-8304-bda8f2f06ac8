<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:44:07+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "mr"
}
-->
# न्यूरल नेटवर्क्सची ओळख. मल्टी-लेयर्ड पर्सेप्ट्रॉन

मागील विभागात, तुम्ही सर्वात सोपा न्यूरल नेटवर्क मॉडेल - एक-लेयर पर्सेप्ट्रॉन, जो एक रेषीय दोन-वर्ग वर्गीकरण मॉडेल आहे, याबद्दल शिकलात.

या विभागात आपण या मॉडेलला अधिक लवचिक फ्रेमवर्कमध्ये विस्तारित करू, ज्यामुळे आपण:

* दोन-वर्गांव्यतिरिक्त **मल्टी-क्लास वर्गीकरण** करू शकू
* वर्गीकरणाशिवाय **रिग्रेशन समस्या** सोडवू शकू
* रेषीयरित्या वेगळे न होणारे वर्ग वेगळे करू शकू

आपण Python मध्ये आपला स्वतःचा मॉड्युलर फ्रेमवर्क देखील विकसित करू, जो वेगवेगळ्या न्यूरल नेटवर्क आर्किटेक्चर तयार करण्यास मदत करेल.

## मशीन लर्निंगचे औपचारिकीकरण

मशीन लर्निंग समस्येचे औपचारिकीकरण करून सुरुवात करूया. समजा आपल्याकडे प्रशिक्षण डेटासेट **X** आहे ज्याला लेबल्स **Y** दिलेले आहेत, आणि आपल्याला एक मॉडेल *f* तयार करायचे आहे जे सर्वात अचूक भाकिते करेल. भाकितांची गुणवत्ता **Loss function** ℒ द्वारे मोजली जाते. खालील लॉस फंक्शन्स सामान्यतः वापरले जातात:

* रिग्रेशन समस्येसाठी, जिथे आपल्याला एखादा संख्या भाकीत करायची आहे, आपण **absolute error** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>| किंवा **squared error** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup> वापरू शकतो
* वर्गीकरणासाठी, आपण **0-1 loss** (जो मॉडेलच्या **accuracy** सारखा आहे) किंवा **logistic loss** वापरतो.

एक-लेवल पर्सेप्ट्रॉनसाठी, फंक्शन *f* हे एक रेषीय फंक्शन *f(x)=wx+b* म्हणून परिभाषित केले गेले होते (इथे *w* हे वजन मॅट्रिक्स आहे, *x* इनपुट फिचर्सचा व्हेक्टर आहे, आणि *b* हे बायस व्हेक्टर आहे). वेगवेगळ्या न्यूरल नेटवर्क आर्किटेक्चरसाठी, हे फंक्शन अधिक जटिल स्वरूप घेऊ शकते.

> वर्गीकरणाच्या बाबतीत, नेटवर्क आउटपुट म्हणून संबंधित वर्गांचे संभाव्यते मिळवणे आवश्यक असते. कोणत्याही संख्यांना संभाव्यतेत रूपांतरित करण्यासाठी (उदा. आउटपुटचे normalization करण्यासाठी), आपण सहसा **softmax** फंक्शन σ वापरतो, आणि फंक्शन *f* बनते *f(x)=σ(wx+b)*

वरील *f* च्या व्याख्येत, *w* आणि *b* यांना **parameters** θ=⟨*w,b*⟩ म्हणतात. दिलेल्या डेटासेट ⟨**X**,**Y**⟩ वर, आपण parameters θ च्या फंक्शन म्हणून संपूर्ण डेटासेटवरील एकूण त्रुटी मोजू शकतो.

> ✅ **न्यूरल नेटवर्क प्रशिक्षणाचा उद्देश म्हणजे parameters θ बदलून त्रुटी कमी करणे**

## ग्रेडियंट डिसेंट ऑप्टिमायझेशन

फंक्शन ऑप्टिमायझेशनसाठी एक प्रसिद्ध पद्धत आहे ज्याला **gradient descent** म्हणतात. कल्पना अशी आहे की आपण लॉस फंक्शनचा parameters च्या संदर्भात अवकलन (multi-dimensional केस मध्ये **gradient**) काढू शकतो, आणि parameters अशा प्रकारे बदलू शकतो की त्रुटी कमी होईल. हे पुढीलप्रमाणे औपचारिक करता येते:

* parameters काही यादृच्छिक मूल्यांनी प्रारंभ करा w<sup>(0)</sup>, b<sup>(0)</sup>
* खालील पायरी अनेक वेळा पुनरावृत्ती करा:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

प्रशिक्षणादरम्यान, ऑप्टिमायझेशन पायऱ्या संपूर्ण डेटासेट लक्षात घेऊन मोजल्या जातात (लॉस सर्व प्रशिक्षण नमुन्यांवरून जमा केल्याने). पण प्रत्यक्षात आपण डेटासेटचे लहान भाग घेतो ज्याला **minibatches** म्हणतात, आणि त्या उपसमूहावरून gradients काढतो. कारण प्रत्येक वेळी उपसमूह यादृच्छिकपणे घेतला जातो, अशा पद्धतीला **stochastic gradient descent** (SGD) म्हणतात.

## मल्टी-लेयर्ड पर्सेप्ट्रॉन आणि बॅकप्रॉपगेशन

एक-लेयर नेटवर्क, जसे आपण वर पाहिले, रेषीयरित्या वेगळे होणारे वर्ग वर्गीकृत करू शकते. अधिक समृद्ध मॉडेल तयार करण्यासाठी, आपण नेटवर्कच्या अनेक लेयर्स एकत्र करू शकतो. गणितीदृष्ट्या याचा अर्थ असा की फंक्शन *f* अधिक जटिल स्वरूपाचा असेल, आणि तो अनेक टप्प्यांत मोजला जाईल:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

इथे, α ही **नॉन-लिनिअर अ‍ॅक्टिव्हेशन फंक्शन** आहे, σ ही softmax फंक्शन आहे, आणि parameters θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*> आहेत.

ग्रेडियंट डिसेंट अल्गोरिदम तसाच राहील, पण gradients काढणे अधिक कठीण होईल. चेन डिफरेंशिएशन नियम वापरून, आपण अवकलने काढू शकतो:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ चेन डिफरेंशिएशन नियमाचा वापर करून लॉस फंक्शनच्या अवकलने parameters च्या संदर्भात काढली जातात.

लक्षात ठेवा की या सर्व अभिव्यक्तींचा डावीकडील भाग सारखा असतो, त्यामुळे आपण लॉस फंक्शनपासून सुरुवात करून "मागे" जाऊन अवकलने प्रभावीपणे काढू शकतो. म्हणूनच मल्टी-लेयर्ड पर्सेप्ट्रॉनचे प्रशिक्षण करण्याची पद्धत **backpropagation**, किंवा 'backprop' म्हणतात.

> TODO: image citation

> ✅ आपल्या नोटबुक उदाहरणात आपण backprop अधिक सविस्तर पाहणार आहोत.

## निष्कर्ष

या धड्यात, आपण आपली स्वतःची न्यूरल नेटवर्क लायब्ररी तयार केली, आणि ती सोप्या दोन-आयामी वर्गीकरण कार्यासाठी वापरली.

## 🚀 आव्हान

सहाय्यक नोटबुकमध्ये, तुम्ही मल्टी-लेयर्ड पर्सेप्ट्रॉन तयार करण्यासाठी आणि प्रशिक्षण देण्यासाठी आपला स्वतःचा फ्रेमवर्क तयार कराल. तुम्हाला आधुनिक न्यूरल नेटवर्क्स कसे कार्य करतात हे सविस्तर पाहायला मिळेल.

OwnFramework नोटबुककडे पुढे जा आणि त्यावर काम करा.

## पुनरावलोकन आणि स्वअध्ययन

Backpropagation ही AI आणि ML मध्ये वापरली जाणारी सामान्य अल्गोरिदम आहे, ज्याचा सविस्तर अभ्यास करणे फायदेशीर आहे.

## असाइनमेंट

या लॅबमध्ये, तुम्हाला या धड्यात तयार केलेल्या फ्रेमवर्कचा वापर करून MNIST हस्तलिखित अंक वर्गीकरण सोडवायचे आहे.

* सूचना
* नोटबुक

**अस्वीकरण**:  
हा दस्तऐवज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) वापरून अनुवादित केला आहे. आम्ही अचूकतेसाठी प्रयत्नशील असलो तरी, कृपया लक्षात घ्या की स्वयंचलित अनुवादांमध्ये चुका किंवा अचूकतेची कमतरता असू शकते. मूळ दस्तऐवज त्याच्या स्थानिक भाषेत अधिकृत स्रोत मानला जावा. महत्त्वाच्या माहितीसाठी व्यावसायिक मानवी अनुवाद करण्याची शिफारस केली जाते. या अनुवादाच्या वापरामुळे उद्भवलेल्या कोणत्याही गैरसमजुती किंवा चुकीच्या अर्थलागी आम्ही जबाबदार नाही.