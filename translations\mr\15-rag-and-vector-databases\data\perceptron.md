<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T16:56:08+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "mr"
}
-->
# न्यूरल नेटवर्क्सची ओळख: पर्सेप्ट्रॉन

आधुनिक न्यूरल नेटवर्कसारखे काहीतरी तयार करण्याचा पहिला प्रयत्न 1957 मध्ये कॉर्नेल एरोनॉटिकल लॅबोरेटरीमधील फ्रँक रोझेनब्लॅट यांनी केला होता. त्याला "Mark-1" नावाचा हार्डवेअर इम्प्लिमेंटेशन म्हणतात, जो त्रिकोण, चौकोन आणि वर्तुळ यांसारख्या प्राथमिक भूमितीय आकृती ओळखण्यासाठी डिझाइन केला गेला होता.

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='<PERSON>'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> विकिपीडियामधून चित्रे

इनपुट इमेज 20x20 फोटोसेंल अ‍ॅरेने दर्शवली जात असे, त्यामुळे न्यूरल नेटवर्कमध्ये 400 इनपुट्स आणि एक बायनरी आउटपुट होता. एक सोपा नेटवर्क एक न्यूरॉनचा समावेश करतो, ज्याला **threshold logic unit** असेही म्हणतात. न्यूरल नेटवर्कचे वजन पोटेन्शिओमीटरसारखे काम करत होते, ज्याला प्रशिक्षणाच्या टप्प्यात मॅन्युअली समायोजित करावे लागायचे.

> ✅ पोटेन्शिओमीटर हा एक असा उपकरण आहे जो वापरकर्त्याला सर्किटचा प्रतिकार समायोजित करण्याची परवानगी देतो.

> न्यू यॉर्क टाइम्सने त्या काळी पर्सेप्ट्रॉनबद्दल लिहिले होते: *हा इलेक्ट्रॉनिक संगणकाचा भ्रूण आहे ज्याला [नौदल] अशी अपेक्षा आहे की तो चालू शकेल, बोलू शकेल, पाहू शकेल, लिहू शकेल, स्वतःची पुनरुत्पत्ती करू शकेल आणि आपल्या अस्तित्वाची जाणीव ठेवेल.*

## पर्सेप्ट्रॉन मॉडेल

समजा आपल्या मॉडेलमध्ये N वैशिष्ट्ये आहेत, अशा परिस्थितीत इनपुट व्हेक्टरचा आकार N असेल. पर्सेप्ट्रॉन हा एक **द्वि-वर्गीकरण** मॉडेल आहे, म्हणजे तो इनपुट डेटाच्या दोन वर्गांमध्ये फरक करू शकतो. आपण असे गृहीत धरू की प्रत्येक इनपुट व्हेक्टर x साठी आपल्या पर्सेप्ट्रॉनचा आउटपुट +1 किंवा -1 असेल, वर्गानुसार. आउटपुट खालील सूत्राने गणना केली जाईल:

y(x) = f(w<sup>T</sup>x)

जिथे f ही एक स्टेप अ‍ॅक्टिव्हेशन फंक्शन आहे.

## पर्सेप्ट्रॉनचे प्रशिक्षण

पर्सेप्ट्रॉनचे प्रशिक्षण करण्यासाठी आपल्याला असे वजनांचे व्हेक्टर w शोधावे लागेल जे बहुतेक मूल्ये बरोबर वर्गीकृत करेल, म्हणजेच सर्वात कमी **त्रुटी** निर्माण करेल. ही त्रुटी **perceptron criterion** द्वारे खालीलप्रमाणे परिभाषित केली जाते:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

जिथे:

* बेरीज त्या प्रशिक्षण डेटाच्या i पॉइंट्सवर केली जाते ज्यामुळे चुकीचे वर्गीकरण होते
* x<sub>i</sub> हे इनपुट डेटा आहे, आणि t<sub>i</sub> हे नकारात्मक आणि सकारात्मक उदाहरणांसाठी अनुक्रमे -1 किंवा +1 आहे.

ही निकष वजन w च्या फंक्शन म्हणून पाहिली जाते, आणि आपल्याला ती कमी करायची आहे. अनेकदा, **gradient descent** नावाची पद्धत वापरली जाते, ज्यात आपण काही प्रारंभिक वजन w<sup>(0)</sup> पासून सुरुवात करतो, आणि नंतर प्रत्येक टप्प्यावर खालील सूत्रानुसार वजन अपडेट करतो:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

इथे η हा **learning rate** म्हणतात, आणि ∇E(w) म्हणजे E चा **gradient**. gradient काढल्यानंतर आपल्याला मिळते:

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

Python मधील अल्गोरिदम असे दिसतो:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## निष्कर्ष

या धड्यात, आपण पर्सेप्ट्रॉनबद्दल शिकलात, जो एक द्वि-वर्गीकरण मॉडेल आहे, आणि त्याचे प्रशिक्षण कसे करायचे हे वजनांच्या व्हेक्टरचा वापर करून कसे करायचे ते समजले.

## 🚀 आव्हान

जर तुम्हाला स्वतःचा पर्सेप्ट्रॉन तयार करून पाहायचा असेल, तर Microsoft Learn वरील Azure ML designer वापरून हा लॅब करून पहा.

## पुनरावलोकन आणि स्वअध्ययन

पर्सेप्ट्रॉनचा वापर करून खेळणी समस्या तसेच प्रत्यक्ष जीवनातील समस्या कशा सोडवता येतील हे पाहण्यासाठी आणि शिकत राहण्यासाठी - Perceptron नोटबुक पहा.

पर्सेप्ट्रॉनबद्दल एक मनोरंजक लेख देखील येथे आहे.

## असाइनमेंट

या धड्यात, आपण द्वि-वर्गीकरणासाठी पर्सेप्ट्रॉन इम्प्लिमेंट केला आहे, आणि दोन हस्तलिखित अंकांमध्ये वर्गीकरण करण्यासाठी त्याचा वापर केला आहे. या लॅबमध्ये, तुम्हाला पूर्णपणे अंक वर्गीकरणाची समस्या सोडवायची आहे, म्हणजे दिलेल्या प्रतिमेस सर्वात जास्त शक्यता असलेला अंक ठरवायचा आहे.

* सूचना
* नोटबुक

**अस्वीकरण**:  
हा दस्तऐवज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) वापरून अनुवादित केला आहे. आम्ही अचूकतेसाठी प्रयत्नशील असलो तरी, कृपया लक्षात घ्या की स्वयंचलित अनुवादांमध्ये चुका किंवा अचूकतेची कमतरता असू शकते. मूळ दस्तऐवज त्याच्या स्थानिक भाषेत अधिकृत स्रोत मानला जावा. महत्त्वाच्या माहितीसाठी व्यावसायिक मानवी अनुवाद करण्याची शिफारस केली जाते. या अनुवादाच्या वापरामुळे उद्भवलेल्या कोणत्याही गैरसमजुती किंवा चुकीच्या अर्थलागी आम्ही जबाबदार नाही.