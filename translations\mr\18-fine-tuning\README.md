<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "68664f7e754a892ae1d8d5e2b7bd2081",
  "translation_date": "2025-07-09T17:39:48+00:00",
  "source_file": "18-fine-tuning/README.md",
  "language_code": "mr"
}
-->
[![Open Source Models](../../../translated_images/18-lesson-banner.f30176815b1a5074fce9cceba317720586caa99e24001231a92fd04eeb54a121.mr.png)](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst)

# तुमच्या LLM चे फाइन-ट्यूनिंग

मोठ्या भाषा मॉडेल्सचा वापर करून जनरेटिव्ह AI अनुप्रयोग तयार करताना नवीन आव्हाने येतात. मुख्य समस्या म्हणजे वापरकर्त्याच्या विनंतीसाठी मॉडेलने तयार केलेल्या सामग्रीतील प्रतिसादाची गुणवत्ता (अचूकता आणि सुसंगतता) सुनिश्चित करणे. मागील धड्यांमध्ये, आपण प्रॉम्प्ट इंजिनिअरिंग आणि रिट्रीव्हल-ऑगमेंटेड जनरेशनसारख्या तंत्रांचा अभ्यास केला, जे विद्यमान मॉडेलच्या प्रॉम्प्ट इनपुटमध्ये बदल करून समस्या सोडवण्याचा प्रयत्न करतात.

आजच्या धड्यात, आपण तिसऱ्या तंत्राबद्दल चर्चा करू, म्हणजेच **फाइन-ट्यूनिंग**, जे अतिरिक्त डेटासह मॉडेल स्वतःला पुन्हा प्रशिक्षण देऊन या आव्हानाचा सामना करण्याचा प्रयत्न करते. चला तपशीलात जाऊया.

## शिकण्याचे उद्दिष्टे

हा धडा प्री-ट्रेन केलेल्या भाषा मॉडेलसाठी फाइन-ट्यूनिंगची संकल्पना सादर करतो, या पद्धतीचे फायदे आणि आव्हाने तपासतो, आणि तुमच्या जनरेटिव्ह AI मॉडेल्सच्या कार्यक्षमतेत सुधारणा करण्यासाठी फाइन-ट्यूनिंग कधी आणि कसे वापरायचे याबाबत मार्गदर्शन करतो.

या धड्याच्या शेवटी, तुम्हाला खालील प्रश्नांची उत्तरे देता यायला हवीत:

- भाषा मॉडेलसाठी फाइन-ट्यूनिंग म्हणजे काय?
- फाइन-ट्यूनिंग कधी आणि का उपयुक्त आहे?
- मी प्री-ट्रेन केलेले मॉडेल कसे फाइन-ट्यून करू शकतो?
- फाइन-ट्यूनिंगची मर्यादा काय आहेत?

तयार आहात? तर सुरुवात करूया.

## चित्रांसह मार्गदर्शक

आपण खोलात जाऊन शिकण्यापूर्वी एकूण चित्र पाहू इच्छिता का? या चित्रांसह मार्गदर्शकात या धड्याचा शिकण्याचा प्रवास दाखवलेला आहे - फाइन-ट्यूनिंगच्या मुख्य संकल्पना आणि प्रेरणा समजून घेणे, प्रक्रिया आणि सर्वोत्तम पद्धती समजून घेणे. हा एक आकर्षक विषय आहे, त्यामुळे तुमच्या स्व-मार्गदर्शित शिकण्याच्या प्रवासाला मदत करणाऱ्या अतिरिक्त दुव्यांसाठी [Resources](./RESOURCES.md?WT.mc_id=academic-105485-koreyst) पृष्ठ नक्की पहा!

![Illustrated Guide to Fine Tuning Language Models](../../../translated_images/18-fine-tuning-sketchnote.11b21f9ec8a703467a120cb79a28b5ac1effc8d8d9d5b31bbbac6b8640432e14.mr.png)

## भाषा मॉडेलसाठी फाइन-ट्यूनिंग म्हणजे काय?

व्याख्येनुसार, मोठ्या भाषा मॉडेल्स हे इंटरनेटसह विविध स्रोतांमधून मोठ्या प्रमाणात मजकूरावर _प्री-ट्रेन_ केलेले असतात. मागील धड्यांमध्ये आपण शिकले आहे की, वापरकर्त्याच्या प्रश्नांना ("प्रॉम्प्ट") उत्तम प्रतिसाद देण्यासाठी _प्रॉम्प्ट इंजिनिअरिंग_ आणि _रिट्रीव्हल-ऑगमेंटेड जनरेशन_ सारख्या तंत्रांची गरज असते.

एक लोकप्रिय प्रॉम्प्ट इंजिनिअरिंग तंत्र म्हणजे मॉडेलला अपेक्षित प्रतिसादाबाबत अधिक मार्गदर्शन देणे, जसे की _सूचना देणे_ (स्पष्ट मार्गदर्शन) किंवा _काही उदाहरणे देणे_ (अप्रत्यक्ष मार्गदर्शन). याला _फ्यू-शॉट लर्निंग_ म्हणतात, पण त्याला दोन मर्यादा आहेत:

- मॉडेलच्या टोकन मर्यादेमुळे तुम्ही दिली जाणारी उदाहरणांची संख्या मर्यादित होते, ज्यामुळे प्रभावीपणा कमी होतो.
- प्रत्येक प्रॉम्प्टसाठी उदाहरणे जोडल्यामुळे टोकन खर्च वाढतो आणि लवचिकता कमी होते.

फाइन-ट्यूनिंग ही मशीन लर्निंग प्रणालींमध्ये एक सामान्य पद्धत आहे जिथे आपण प्री-ट्रेन केलेले मॉडेल घेऊन नवीन डेटासह पुन्हा प्रशिक्षण देतो, ज्यामुळे विशिष्ट कार्यावर त्याची कार्यक्षमता सुधारते. भाषा मॉडेलच्या संदर्भात, आपण प्री-ट्रेन केलेल्या मॉडेलला _विशिष्ट कार्य किंवा अनुप्रयोग क्षेत्रासाठी निवडक उदाहरणांसह_ फाइन-ट्यून करू शकतो, ज्यामुळे एक **कस्टम मॉडेल** तयार होते जे त्या विशिष्ट कार्यासाठी अधिक अचूक आणि सुसंगत असू शकते. फाइन-ट्यूनिंगचा एक अतिरिक्त फायदा म्हणजे फ्यू-शॉट लर्निंगसाठी आवश्यक उदाहरणांची संख्या कमी होते - ज्यामुळे टोकन वापर आणि संबंधित खर्च कमी होतो.

## आपण कधी आणि का मॉडेल फाइन-ट्यून करावे?

_या_ संदर्भात, जेव्हा आपण फाइन-ट्यूनिंगबद्दल बोलतो, तेव्हा आपण **सुपरवाइज्ड** फाइन-ट्यूनिंगचा उल्लेख करतो, जिथे पुन्हा प्रशिक्षण नवीन डेटा जोडून केले जाते जो मूळ प्रशिक्षण डेटासेटचा भाग नव्हता. हे अनसुपरवाइज्ड फाइन-ट्यूनिंगपासून वेगळे आहे, जिथे मॉडेल मूळ डेटावर वेगवेगळ्या हायपरपॅरामीटर्ससह पुन्हा प्रशिक्षण दिले जाते.

महत्त्वाचे म्हणजे फाइन-ट्यूनिंग ही एक प्रगत तंत्र आहे ज्यासाठी अपेक्षित निकाल मिळवण्यासाठी विशिष्ट कौशल्य आवश्यक असते. चुकीच्या पद्धतीने केल्यास अपेक्षित सुधारणा मिळू शकत नाहीत, तर तुमच्या लक्षित क्षेत्रासाठी मॉडेलची कार्यक्षमता कमी होऊ शकते.

म्हणून, "कसे" फाइन-ट्यून करायचे हे शिकण्यापूर्वी, तुम्हाला "का" हा मार्ग घ्यायचा आहे आणि "कधी" फाइन-ट्यूनिंग प्रक्रिया सुरू करायची आहे हे जाणून घेणे आवश्यक आहे. स्वतःला हे प्रश्न विचारा:

- **वापर प्रकरण**: तुमचा फाइन-ट्यूनिंगसाठी _वापर प्रकरण_ काय आहे? सध्याच्या प्री-ट्रेन मॉडेलच्या कोणत्या बाबतीत सुधारणा करायची आहे?
- **पर्याय**: तुम्ही अपेक्षित निकालांसाठी _इतर तंत्रे_ वापरून पाहिली आहेत का? त्यांचा वापर करून तुलना करण्यासाठी बेसलाइन तयार करा.
  - प्रॉम्प्ट इंजिनिअरिंग: संबंधित प्रॉम्प्ट प्रतिसादांसह फ्यू-शॉट प्रॉम्प्टिंगसारखे तंत्र वापरून पहा. प्रतिसादांची गुणवत्ता तपासा.
  - रिट्रीव्हल ऑगमेंटेड जनरेशन: तुमच्या डेटावर शोध घेऊन मिळालेल्या क्वेरी निकालांसह प्रॉम्प्ट वाढवा. प्रतिसादांची गुणवत्ता तपासा.
- **खर्च**: फाइन-ट्यूनिंगसाठी खर्च ओळखले आहेत का?
  - ट्यून करण्यायोग्यता - प्री-ट्रेन मॉडेल फाइन-ट्यूनिंगसाठी उपलब्ध आहे का?
  - प्रयत्न - प्रशिक्षण डेटा तयार करणे, मॉडेलचे मूल्यमापन आणि सुधारणा करणे.
  - संगणकीय संसाधने - फाइन-ट्यूनिंग जॉब चालवण्यासाठी आणि फाइन-ट्यून केलेले मॉडेल तैनात करण्यासाठी.
  - डेटा - फाइन-ट्यूनिंगसाठी पुरेशी दर्जेदार उदाहरणे उपलब्ध आहेत का?
- **फायदे**: फाइन-ट्यूनिंगचे फायदे पुष्टी केले आहेत का?
  - गुणवत्ता - फाइन-ट्यून केलेले मॉडेल बेसलाइनपेक्षा चांगले आहे का?
  - खर्च - प्रॉम्प्ट सोप्या करून टोकन वापर कमी होतो का?
  - विस्तारक्षमता - बेस मॉडेल नवीन क्षेत्रांसाठी पुनर्प्रयोजित करता येते का?

या प्रश्नांची उत्तरे देऊन तुम्ही ठरवू शकता की फाइन-ट्यूनिंग तुमच्या वापर प्रकरणासाठी योग्य आहे का. आदर्शपणे, हा मार्ग फक्त तेव्हाच योग्य आहे जेव्हा फायदे खर्चांपेक्षा जास्त असतात. एकदा तुम्ही पुढे जाण्याचा निर्णय घेतला की, मग प्री-ट्रेन मॉडेल कसे फाइन-ट्यून करायचे याचा विचार करा.

निर्णय प्रक्रियेबद्दल अधिक माहिती हवी आहे का? [To fine-tune or not to fine-tune](https://www.youtube.com/watch?v=0Jo-z-MFxJs) पाहा.

## आपण प्री-ट्रेन केलेले मॉडेल कसे फाइन-ट्यून करू शकतो?

प्री-ट्रेन केलेले मॉडेल फाइन-ट्यून करण्यासाठी, तुमच्याकडे असणे आवश्यक आहे:

- फाइन-ट्यून करण्यासाठी प्री-ट्रेन केलेले मॉडेल
- फाइन-ट्यूनिंगसाठी वापरण्याचा डेटासेट
- फाइन-ट्यूनिंग जॉब चालवण्यासाठी प्रशिक्षण वातावरण
- फाइन-ट्यून केलेले मॉडेल तैनात करण्यासाठी होस्टिंग वातावरण

## फाइन-ट्यूनिंग प्रत्यक्षात

खालील संसाधने तुम्हाला निवडलेल्या मॉडेलसह निवडक डेटासेट वापरून प्रत्यक्ष उदाहरणाद्वारे टप्प्याटप्प्याने मार्गदर्शन करतात. या ट्युटोरियल्सवर काम करण्यासाठी, तुम्हाला संबंधित प्रदात्याच्या खात्याची गरज आहे, तसेच संबंधित मॉडेल आणि डेटासेट्सचा प्रवेश असणे आवश्यक आहे.

| प्रदाता       | ट्युटोरियल                                                                                                                                                                   | वर्णन                                                                                                                                                                                                                                                                                                                                                                                                                            |
| ------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| OpenAI        | [How to fine-tune chat models](https://github.com/openai/openai-cookbook/blob/main/examples/How_to_finetune_chat_models.ipynb?WT.mc_id=academic-105485-koreyst)                | विशिष्ट क्षेत्रासाठी ("रेसिपी असिस्टंट") `gpt-35-turbo` मॉडेल कसे फाइन-ट्यून करायचे ते शिकून घ्या, प्रशिक्षण डेटा तयार करा, फाइन-ट्यूनिंग जॉब चालवा, आणि फाइन-ट्यून केलेले मॉडेल वापरून अनुमान करा.                                                                                                                                                                                                                              |
| Azure OpenAI  | [GPT 3.5 Turbo fine-tuning tutorial](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python-new%2Ccommand-line?WT.mc_id=academic-105485-koreyst) | Azure वर `gpt-35-turbo-0613` मॉडेल कसे फाइन-ट्यून करायचे ते शिका, प्रशिक्षण डेटा तयार करा आणि अपलोड करा, फाइन-ट्यूनिंग जॉब चालवा, नवीन मॉडेल तैनात करा आणि वापरा.                                                                                                                                                                                                                                                           |
| Hugging Face  | [Fine-tuning LLMs with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | हा ब्लॉग पोस्ट तुम्हाला _ओपन LLM_ (उदा. `CodeLlama 7B`) कसे फाइन-ट्यून करायचे ते दाखवतो, [transformers](https://huggingface.co/docs/transformers/index?WT.mc_id=academic-105485-koreyst) लायब्ररी आणि [Transformer Reinforcement Learning (TRL)](https://huggingface.co/docs/trl/index?WT.mc_id=academic-105485-koreyst) वापरून, तसेच Hugging Face वरील खुले [datasets](https://huggingface.co/docs/datasets/index?WT.mc_id=academic-105485-koreyst) वापरून. |
|               |                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| 🤗 AutoTrain  | [Fine-tuning LLMs with AutoTrain](https://github.com/huggingface/autotrain-advanced/?WT.mc_id=academic-105485-koreyst)                                                         | AutoTrain (किंवा AutoTrain Advanced) ही Hugging Face द्वारे विकसित केलेली पायथन लायब्ररी आहे जी अनेक वेगवेगळ्या कार्यांसाठी, त्यात LLM फाइन-ट्यूनिंगसाठी देखील, फाइन-ट्यूनिंगची सुविधा देते. AutoTrain ही नो-कोड सोल्यूशन आहे आणि फाइन-ट्यूनिंग तुम्ही तुमच्या स्वतःच्या क्लाउडमध्ये, Hugging Face Spaces वर किंवा स्थानिकपणे करू शकता. यात वेब-आधारित GUI, CLI आणि yaml कॉन्फिग फाइल्सद्वारे प्रशिक्षण यांचा समावेश आहे. |
|               |                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                                                                  |

## असाइनमेंट

वरील ट्युटोरियल्सपैकी एक निवडा आणि त्यावर काम करा. _आम्ही या ट्युटोरियल्सच्या आवृत्ती जुपिटर नोटबुक्समध्ये या रेपोमध्ये संदर्भासाठी तयार करू शकतो. कृपया नवीनतम आवृत्त्या मिळवण्यासाठी मूळ स्रोतांचा थेट वापर करा_.

## छान काम! तुमचे शिक्षण सुरू ठेवा.

हा धडा पूर्ण केल्यानंतर, आमच्या [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) मध्ये जाऊन तुमचे जनरेटिव्ह AI ज्ञान अधिक वाढवा!

अभिनंदन!! तुम्ही या कोर्सच्या v2 मालिकेतील अंतिम धडा पूर्ण केला आहे! शिकणे आणि तयार करणे थांबवू नका. \*\*या विषयासाठी अतिरिक्त सूचना पाहण्यासाठी [RESOURCES](RESOURCES.md?WT.mc_id=academic-105485-koreyst) पृष्ठ नक्की पहा.

आमच्या v1 मालिकेतील धडे देखील अधिक असाइनमेंट्स आणि संकल्पनांसह अद्ययावत केले गेले आहेत. त्यामुळे थोडा वेळ काढून तुमचे ज्ञान ताजे करा - आणि कृपया [तुमचे प्रश्न आणि अभिप्राय](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) शेअर करा, जेणेकरून आम्ही समुदायासाठी हे धडे सुधारू शकू.

**अस्वीकरण**:  
हा दस्तऐवज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) वापरून अनुवादित केला आहे. आम्ही अचूकतेसाठी प्रयत्नशील असलो तरी, कृपया लक्षात घ्या की स्वयंचलित अनुवादांमध्ये चुका किंवा अचूकतेची कमतरता असू शकते. मूळ दस्तऐवज त्याच्या स्थानिक भाषेत अधिकृत स्रोत मानला जावा. महत्त्वाच्या माहितीसाठी व्यावसायिक मानवी अनुवाद करण्याची शिफारस केली जाते. या अनुवादाच्या वापरामुळे उद्भवलेल्या कोणत्याही गैरसमजुती किंवा चुकीच्या अर्थलागी आम्ही जबाबदार नाही.