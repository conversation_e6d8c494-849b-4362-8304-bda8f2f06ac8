<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:58:54+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "mr"
}
-->
# स्व-मार्गदर्शित शिक्षणासाठी संसाधने

हा धडा OpenAI आणि Azure OpenAI कडून मिळालेल्या अनेक मुख्य संसाधनांचा संदर्भ घेऊन तयार करण्यात आला आहे, ज्यात संज्ञाशास्त्र आणि ट्युटोरियल्सचा समावेश आहे. तुमच्या स्वतःच्या स्व-मार्गदर्शित शिक्षण प्रवासासाठी ही एक संपूर्ण नसलेली यादी आहे.

## 1. प्राथमिक संसाधने

| शीर्षक/दुवा                                                                                                                                                                                                                 | वर्णन                                                                                                                                                                                                                                                                                                                                                                                        |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [OpenAI मॉडेल्ससह फाइन-ट्यूनिंग](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | फाइन-ट्यूनिंग हे फ्यू-शॉट लर्निंगपेक्षा अधिक उदाहरणांवर प्रशिक्षण देऊन सुधारणा करते, ज्यामुळे खर्च कमी होतो, प्रतिसादाची गुणवत्ता वाढते आणि कमी विलंब असलेल्या विनंत्या शक्य होतात. **OpenAI कडून फाइन-ट्यूनिंगचा आढावा घ्या.**                                                                                                                                                             |
| [Azure OpenAI सह फाइन-ट्यूनिंग म्हणजे काय?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | **फाइन-ट्यूनिंग म्हणजे काय (संकल्पना)**, का पाहावे (प्रेरणादायी समस्या), कोणता डेटा वापरावा (प्रशिक्षण) आणि गुणवत्ता मोजण्याबाबत समजून घ्या.                                                                                                                                                                                                                                               |
| [फाइन-ट्यूनिंगसह मॉडेल सानुकूलित करा](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI सेवा तुम्हाला फाइन-ट्यूनिंग वापरून तुमच्या वैयक्तिक डेटासेटनुसार मॉडेल सानुकूलित करण्याची परवानगी देते. Azure AI Studio, Python SDK किंवा REST API वापरून **फाइन-ट्यूनिंग कसे करायचे (प्रक्रिया)** हे शिका.                                                                                                                                    |
| [LLM फाइन-ट्यूनिंगसाठी शिफारसी](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM विशिष्ट क्षेत्र, कार्य किंवा डेटासेटवर चांगले काम करू शकत नाहीत किंवा चुकीचे किंवा भ्रामक परिणाम देऊ शकतात. **कधी फाइन-ट्यूनिंगचा विचार करावा** हे जाणून घ्या.                                                                                                                                                                                                                     |
| [सतत फाइन-ट्यूनिंग](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | सतत फाइन-ट्यूनिंग म्हणजे आधीच फाइन-ट्यून केलेल्या मॉडेलला बेस मॉडेल म्हणून निवडून नवीन प्रशिक्षण उदाहरणांवर **पुन्हा फाइन-ट्यूनिंग करणे** हा पुनरावृत्तीचा प्रक्रिया आहे.                                                                                                                                                                                                                   |
| [फाइन-ट्यूनिंग आणि फंक्शन कॉलिंग](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | फंक्शन कॉलिंग उदाहरणांसह तुमचे मॉडेल फाइन-ट्यून केल्याने अधिक अचूक आणि सुसंगत प्रतिसाद मिळू शकतो - ज्यामुळे समान स्वरूपाचे प्रतिसाद आणि खर्च बचत होते.                                                                                                                                                                                                                                   |
| [फाइन-ट्यूनिंग मॉडेल्स: Azure OpenAI मार्गदर्शन](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Azure OpenAI मध्ये **कोणती मॉडेल्स फाइन-ट्यून केली जाऊ शकतात** आणि कोणत्या प्रदेशांमध्ये उपलब्ध आहेत हे समजून घेण्यासाठी ही तक्ता पहा. आवश्यक असल्यास त्यांची टोकन मर्यादा आणि प्रशिक्षण डेटा कालबाह्यता तपासा.                                                                                                                                                                            |
| [फाइन-ट्यून करायचे की नाही? हा प्रश्न आहे](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | AI Show चा हा ३० मिनिटांचा **ऑक्टोबर २०२३** एपिसोड फायदे, तोटे आणि व्यावहारिक अंतर्दृष्टी यावर चर्चा करतो, ज्यामुळे तुम्हाला निर्णय घेण्यास मदत होते.                                                                                                                                                                                                                                      |
| [LLM फाइन-ट्यूनिंगसह सुरुवात](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | हा **AI Playbook** संसाधन तुम्हाला डेटा गरजा, स्वरूपन, हायपरपॅरामीटर फाइन-ट्यूनिंग आणि तुम्हाला माहित असाव्या अशा आव्हाने/मर्यादा याबाबत मार्गदर्शन करतो.                                                                                                                                                                                                                                  |
| **ट्युटोरियल**: [Azure OpenAI GPT3.5 Turbo फाइन-ट्यूनिंग](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | फाइन-ट्यूनिंगसाठी नमुना डेटासेट तयार करणे, फाइन-ट्यूनिंगसाठी तयारी करणे, फाइन-ट्यूनिंग जॉब तयार करणे आणि Azure वर फाइन-ट्यून केलेले मॉडेल तैनात करणे शिका.                                                                                                                                                                                                                                  |
| **ट्युटोरियल**: [Azure AI Studio मध्ये Llama 2 मॉडेल फाइन-ट्यून करा](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio तुम्हाला मोठ्या भाषा मॉडेल्सना तुमच्या वैयक्तिक डेटासेटनुसार _लो-कोड डेव्हलपर्ससाठी योग्य UI-आधारित वर्कफ्लो वापरून_ सानुकूलित करण्याची परवानगी देते. हा उदाहरण पहा.                                                                                                                                                                                                       |
| **ट्युटोरियल**: [Azure वर सिंगल GPU साठी Hugging Face मॉडेल्स फाइन-ट्यून करा](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | हा लेख Azure DataBricks + Hugging Face Trainer लायब्ररी वापरून सिंगल GPU वर Hugging Face ट्रान्सफॉर्मर्स लायब्ररीसह Hugging Face मॉडेल कसे फाइन-ट्यून करायचे हे सांगतो.                                                                                                                                                                                                                      |
| **प्रशिक्षण:** [Azure Machine Learning सह फाउंडेशन मॉडेल फाइन-ट्यून करा](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Azure Machine Learning मधील मॉडेल कॅटलॉगमध्ये अनेक ओपन सोर्स मॉडेल्स आहेत जे तुम्ही तुमच्या विशिष्ट कार्यासाठी फाइन-ट्यून करू शकता. हा मॉड्यूल [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) मधून आहे.                                                                                  |
| **ट्युटोरियल:** [Azure OpenAI फाइन-ट्यूनिंग](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Microsoft Azure वर W&B वापरून GPT-3.5 किंवा GPT-4 मॉडेल्सचे फाइन-ट्यूनिंग केल्याने मॉडेलच्या कामगिरीचे सखोल ट्रॅकिंग आणि विश्लेषण शक्य होते. हा मार्गदर्शक OpenAI फाइन-ट्यूनिंग मार्गदर्शकातील संकल्पना Azure OpenAI साठी विशिष्ट पायऱ्यांसह आणि वैशिष्ट्यांसह विस्तारित करतो.                                                                                                                   |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                             |

## 2. दुय्यम संसाधने

हा विभाग अतिरिक्त संसाधने समाविष्ट करतो ज्यांचा अभ्यास करणे उपयुक्त आहे, पण ज्यांना या धड्यात समाविष्ट करण्यासाठी वेळ नव्हता. भविष्यातील धड्यात किंवा दुसऱ्या असाइनमेंट पर्याय म्हणून त्यांचा समावेश होऊ शकतो. सध्या, या संसाधनांचा वापर करून या विषयावर तुमची स्वतःची कौशल्ये आणि ज्ञान वाढवा.

| शीर्षक/दुवा                                                                                                                                                                                                            | वर्णन                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [चॅट मॉडेल फाइन-ट्यूनिंगसाठी डेटा तयारी आणि विश्लेषण](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | हा नोटबुक चॅट मॉडेल फाइन-ट्यूनिंगसाठी वापरल्या जाणाऱ्या चॅट डेटासेटची पूर्वतयारी आणि विश्लेषण करण्यासाठी साधन म्हणून काम करतो. तो स्वरूपातील चुका तपासतो, मूलभूत आकडेवारी देतो आणि फाइन-ट्यूनिंग खर्चासाठी टोकन मोजणीचा अंदाज लावतो. पहा: [gpt-3.5-turbo साठी फाइन-ट्यूनिंग पद्धत](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [Qdrant सह Retrieval Augmented Generation (RAG) साठी फाइन-ट्यूनिंग](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | या नोटबुकचा उद्देश OpenAI मॉडेल्सना Retrieval Augmented Generation (RAG) साठी कसे फाइन-ट्यून करायचे याचे सविस्तर उदाहरण देणे आहे. आम्ही Qdrant आणि Few-Shot Learning देखील एकत्रित करू जेणेकरून मॉडेलची कामगिरी सुधारेल आणि चुकीच्या माहितीची शक्यता कमी होईल.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Weights & Biases सह GPT फाइन-ट्यूनिंग](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) हे AI डेव्हलपर प्लॅटफॉर्म आहे, जे मॉडेल प्रशिक्षण, फाइन-ट्यूनिंग आणि फाउंडेशन मॉडेल्सचा वापर करण्यासाठी साधने पुरवते. प्रथम त्यांचा [OpenAI फाइन-ट्यूनिंग](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) मार्गदर्शक वाचा, नंतर Cookbook व्यायाम करा.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - लहान भाषा मॉडेल्ससाठी फाइन-ट्यूनिंग                                                   | Microsoft चा नवीन लहान मॉडेल [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst) परिचय करा, जो आश्चर्यकारकपणे शक्तिशाली आणि कॉम्पॅक्ट आहे. हा ट्युटोरियल तुम्हाला Phi-2 चे फाइन-ट्यूनिंग कसे करायचे हे दाखवेल, ज्यात एक अनोखा डेटासेट तयार करणे आणि QLoRA वापरून मॉडेल फाइन-ट्यून करणे यांचा समावेश आहे.                                                                                                                                                                       |
| **Hugging Face Tutorial** [2024 मध्ये Hugging Face सह LLM कसे फाइन-ट्यून करायचे](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | हा ब्लॉग पोस्ट तुम्हाला 2024 मध्ये Hugging Face TRL, Transformers आणि datasets वापरून खुले LLM कसे फाइन-ट्यून करायचे हे चरणबद्ध मार्गदर्शन करतो. तुम्ही वापर प्रकरण निश्चित करता, विकास वातावरण तयार करता, डेटासेट तयार करता, मॉडेल फाइन-ट्यून करता, चाचणी व मूल्यमापन करता आणि नंतर उत्पादनात तैनात करता.                                                                                                                                                                                                                                                                |
| **Hugging Face:** [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)                                                                                            | [अत्याधुनिक मशीन लर्निंग मॉडेल्स](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst) चे जलद आणि सोपे प्रशिक्षण व तैनाती आणते. या रेपॉमध्ये Colab-मैत्रीपूर्ण ट्युटोरियल्स आणि YouTube व्हिडिओ मार्गदर्शन आहे, फाइन-ट्यूनिंगसाठी. **अलीकडील [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) अपडेट प्रतिबिंबित करते**. [AutoTrain दस्तऐवज](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) वाचा. |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**अस्वीकरण**:  
हा दस्तऐवज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) वापरून अनुवादित केला आहे. आम्ही अचूकतेसाठी प्रयत्नशील असलो तरी, कृपया लक्षात घ्या की स्वयंचलित अनुवादांमध्ये चुका किंवा अचूकतेची कमतरता असू शकते. मूळ दस्तऐवज त्याच्या स्थानिक भाषेत अधिकृत स्रोत मानला जावा. महत्त्वाच्या माहितीसाठी व्यावसायिक मानवी अनुवाद करण्याची शिफारस केली जाते. या अनुवादाच्या वापरामुळे उद्भवलेल्या कोणत्याही गैरसमजुती किंवा चुकीच्या अर्थलागी आम्ही जबाबदार नाही.