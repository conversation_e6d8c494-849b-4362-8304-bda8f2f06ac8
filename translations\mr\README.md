<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:26:39+00:00",
  "source_file": "README.md",
  "language_code": "mr"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.mr.png)

### Generative AI अॅप्लिकेशन्स तयार करण्यासाठी आवश्यक असलेली सर्व काही शिकवणाऱ्या 21 धड्यांचा संच

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 बहुभाषिक समर्थन

#### GitHub Action द्वारे समर्थित (स्वयंचलित आणि नेहमी अद्ययावत)

[French](../fr/README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](../ja/README.md) | [Korean](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](./README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](../el/README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](../he/README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](../sk/README.md) | [Romanian](../ro/README.md) | [Bulgarian](../bg/README.md) | [Serbian (Cyrillic)](../sr/README.md) | [Croatian](../hr/README.md) | [Slovenian](../sl/README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Generative AI for Beginners (आवृत्ती 3) - एक कोर्स

Microsoft Cloud Advocates कडून तयार केलेल्या 21 धड्यांच्या या सर्वसमावेशक कोर्सद्वारे Generative AI अॅप्लिकेशन्स तयार करण्याच्या मूलभूत गोष्टी शिका.

## 🌱 सुरुवात कशी करावी

हा कोर्स 21 धड्यांचा आहे. प्रत्येक धडा वेगळ्या विषयावर आहे, त्यामुळे तुम्हाला जिथून सुरुवात करायची आहे तिथून सुरुवात करा!

धडे "Learn" (शिका) किंवा "Build" (बनवा) असे वर्गीकृत आहेत. "Learn" धडे Generative AI च्या संकल्पना समजावून सांगतात तर "Build" धडे संकल्पना आणि शक्य असल्यास **Python** आणि **TypeScript** मध्ये कोड उदाहरणे देतात.

.NET विकसकांसाठी [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst) पहा!

प्रत्येक धड्यात "Keep Learning" विभाग देखील आहे ज्यात अतिरिक्त शिकण्याचे साधने दिलेले आहेत.

## काय आवश्यक आहे
### या कोर्सचा कोड चालवण्यासाठी तुम्ही खालीलपैकी कोणतेही वापरू शकता: 
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **धडे:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **धडे:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **धडे:** "oai-assignment" 
   
- Python किंवा TypeScript ची प्राथमिक माहिती उपयुक्त आहे - \*पूर्ण नवशिक्यांसाठी हे [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) आणि [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) कोर्स पहा
- GitHub खाते जेणेकरून तुम्ही [हा संपूर्ण रेपो फोर्क](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) करू शकता

आम्ही तुमच्या विकास वातावरणाच्या सेटअपसाठी एक **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** धडा तयार केला आहे.

नंतर सोप्या शोधासाठी [हा रेपो स्टार (🌟) करायला विसरू नका](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst).

## 🧠 तैनात करण्यासाठी तयार आहात?

जर तुम्हाला अधिक प्रगत कोड नमुने पाहिजेत तर आमच्या [Generative AI Code Samples](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) संग्रहात पहा, जे **Python** आणि **TypeScript** मध्ये उपलब्ध आहेत.

## 🗣️ इतर शिकणाऱ्यांशी भेटा, मदत मिळवा

हा कोर्स घेणाऱ्या इतर शिकणाऱ्यांशी भेटण्यासाठी आणि मदत मिळवण्यासाठी आमच्या [अधिकृत Azure AI Foundry Discord सर्व्हर](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) मध्ये सहभागी व्हा.

GitHub वर आमच्या [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) मध्ये प्रश्न विचारा किंवा उत्पादनाबाबत अभिप्राय द्या.

## 🚀 स्टार्टअप तयार करत आहात?

[Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) मध्ये नोंदणी करा आणि **मुफ्त OpenAI क्रेडिट्स** तसेच Azure OpenAI Services द्वारे OpenAI मॉडेल्स वापरण्यासाठी **$150k पर्यंत Azure क्रेडिट्स** मिळवा.

## 🙏 मदत करायची आहे का?

तुमच्याकडे सूचना आहेत का किंवा स्पेलिंग किंवा कोडमध्ये चुका आढळल्या आहेत? [इश्यू उघडा](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) किंवा [पुल रिक्वेस्ट तयार करा](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 प्रत्येक धड्यात समाविष्ट आहे:

- विषयाची थोडक्यात व्हिडिओ ओळख
- README मध्ये लिहिलेला धडा
- Azure OpenAI आणि OpenAI API साठी Python आणि TypeScript कोड नमुने
- तुमच्या शिकण्याला पुढे नेण्यासाठी अतिरिक्त संसाधने

## 🗃️ धडे

| #   | **धड्याचा दुवा**                                                                                                                              | **वर्णन**                                                                                 | **व्हिडिओ**                                                                   | **अतिरिक्त शिकण्याचे साधन**                                                             |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | -------------------------------------------------------------------------------------- |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Learn:** तुमचे विकास वातावरण कसे सेट करायचे                                          | व्हिडिओ लवकरच येणार                                                                 | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)     |
| 01  | [Generative AI आणि LLMs ची ओळख](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                              | **Learn:** Generative AI म्हणजे काय आणि Large Language Models (LLMs) कसे कार्य करतात याचे समज | [व्हिडिओ](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)     |
| 02  | [वेगवेगळ्या LLMs ची तुलना आणि शोध](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** तुमच्या वापरासाठी योग्य मॉडेल कसे निवडायचे                                   | [व्हिडिओ](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)     |
| 03  | [Generative AI जबाबदारीने वापरणे](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                           | **Learn:** Generative AI अॅप्लिकेशन्स जबाबदारीने कशा तयार करायच्या                      | [व्हिडिओ](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)     |
| 04  | [Prompt Engineering च्या मूलभूत तत्त्वांची समज](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** Prompt Engineering च्या सर्वोत्तम पद्धती प्रत्यक्षात वापरणे                   | [व्हिडिओ](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)     |
| 05  | [प्रगत Prompts तयार करणे](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **Learn:** तुमच्या Prompts चा परिणाम सुधारण्यासाठी prompt engineering तंत्र कसे वापरायचे | [व्हिडिओ](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)     |
| 06  | [टेक्स्ट जनरेशन अॅप्लिकेशन्स तयार करणे](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **बनवा:** Azure OpenAI / OpenAI API वापरून टेक्स्ट जनरेशन अॅप्लिकेशन                                | [व्हिडिओ](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [चॅट अॅप्लिकेशन्स तयार करणे](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **बनवा:** चॅट अॅप्लिकेशन्स प्रभावीपणे तयार करण्याच्या आणि एकत्रित करण्याच्या तंत्रांचा वापर.               | [व्हिडिओ](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [सर्च अॅप्लिकेशन्स आणि व्हेक्टर डेटाबेस तयार करणे](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **बनवा:** एम्बेडिंग्ज वापरून डेटा शोधण्यासाठी सर्च अॅप्लिकेशन                        | [व्हिडिओ](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [इमेज जनरेशन अॅप्लिकेशन्स तयार करणे](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **बनवा:** इमेज जनरेशन अॅप्लिकेशन                                                       | [व्हिडिओ](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [लो कोड AI अॅप्लिकेशन्स तयार करणे](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **बनवा:** लो कोड टूल्स वापरून जनरेटिव्ह AI अॅप्लिकेशन                                     | [व्हिडिओ](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [फंक्शन कॉलिंगसह बाह्य अॅप्लिकेशन्स एकत्रित करणे](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **बनवा:** फंक्शन कॉलिंग म्हणजे काय आणि अॅप्लिकेशन्ससाठी त्याचा वापर कसा करतात                          | [व्हिडिओ](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [AI अॅप्लिकेशन्ससाठी UX डिझाइन करणे](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **शिका:** जनरेटिव्ह AI अॅप्लिकेशन्स विकसित करताना UX डिझाइन तत्त्वे कशी लागू करायची         | [व्हिडिओ](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [तुमच्या जनरेटिव्ह AI अॅप्लिकेशन्सचे संरक्षण करणे](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **शिका:** AI प्रणालींना धोके आणि जोखमी काय आहेत आणि त्यांचे संरक्षण कसे करायचे.             | [व्हिडिओ](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [जनरेटिव्ह AI अॅप्लिकेशनचा जीवनचक्र](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **शिका:** LLM जीवनचक्र आणि LLMOps व्यवस्थापित करण्यासाठी साधने आणि मेट्रिक्स                         | [व्हिडिओ](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) आणि व्हेक्टर डेटाबेस](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **बनवा:** RAG फ्रेमवर्क वापरून व्हेक्टर डेटाबेसमधून एम्बेडिंग्ज पुनर्प्राप्त करणारे अॅप्लिकेशन  | [व्हिडिओ](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [ओपन सोर्स मॉडेल्स आणि Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **बनवा:** Hugging Face वर उपलब्ध असलेल्या ओपन सोर्स मॉडेल्स वापरून अॅप्लिकेशन                    | [व्हिडिओ](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI एजंट्स](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **बनवा:** AI एजंट फ्रेमवर्क वापरून अॅप्लिकेशन                                           | [व्हिडिओ](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [LLMs चे फाइन-ट्यूनिंग](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **शिका:** LLMs चे फाइन-ट्यूनिंग म्हणजे काय, का आणि कसे करायचे                                            | [व्हिडिओ](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [SLMs वापरून तयार करणे](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **शिका:** Small Language Models वापरून तयार करण्याचे फायदे                                            | व्हिडिओ लवकरच येणार | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Mistral मॉडेल्स वापरून तयार करणे](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **शिका:** Mistral कुटुंबातील मॉडेल्सची वैशिष्ट्ये आणि फरक                                           | व्हिडिओ लवकरच येणार | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Meta मॉडेल्स वापरून तयार करणे](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **शिका:** Meta कुटुंबातील मॉडेल्सची वैशिष्ट्ये आणि फरक                                           | व्हिडिओ लवकरच येणार | [अधिक जाणून घ्या](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 विशेष आभार

GitHub Actions आणि workflows तयार करण्यासाठी [**John Aziz**](https://www.linkedin.com/in/john0isaac/) यांचे विशेष आभार

प्रत्येक धड्यासाठी शिकणाऱ्याचा आणि कोडचा अनुभव सुधारण्यासाठी महत्त्वपूर्ण योगदान दिल्याबद्दल [**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) यांचे आभार.

## 🎒 इतर कोर्सेस

आमची टीम इतर कोर्सेस देखील तयार करते! पाहा:

- [**नवीन** Model Context Protocol for Beginners](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI Agents for Beginners](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [.NET वापरून Generative AI for Beginners](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [JavaScript वापरून Generative AI for Beginners](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML for Beginners](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science for Beginners](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI for Beginners](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Cybersecurity for Beginners](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Web Dev for Beginners](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT for Beginners](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR Development for Beginners](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI Paired Programming साठी GitHub Copilot मध्ये प्रावीण्य मिळवा](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [C#/.NET डेव्हलपर्ससाठी GitHub Copilot मध्ये प्रावीण्य मिळवा](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [तुमची स्वतःची Copilot साहस निवडा](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**अस्वीकरण**:  
हा दस्तऐवज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) वापरून अनुवादित केला आहे. आम्ही अचूकतेसाठी प्रयत्नशील असलो तरी, कृपया लक्षात घ्या की स्वयंचलित अनुवादांमध्ये चुका किंवा अचूकतेची कमतरता असू शकते. मूळ दस्तऐवज त्याच्या स्थानिक भाषेत अधिकृत स्रोत मानला जावा. महत्त्वाच्या माहितीसाठी व्यावसायिक मानवी अनुवाद करण्याची शिफारस केली जाते. या अनुवादाच्या वापरामुळे उद्भवलेल्या कोणत्याही गैरसमजुती किंवा चुकीच्या अर्थलागी आम्ही जबाबदार नाही.