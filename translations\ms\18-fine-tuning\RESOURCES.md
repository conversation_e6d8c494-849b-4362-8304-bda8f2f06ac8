<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:06:11+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "ms"
}
-->
# Sumber Untuk Pembelajaran Kendiri

Pelajaran ini dibina menggunakan beberapa sumber utama dari OpenAI dan Azure OpenAI sebagai rujukan untuk terminologi dan tutorial. Berikut adalah senarai tidak menyeluruh, untuk perjalanan pembelajaran kendiri anda sendiri.

## 1. Sumber Utama

| Tajuk/Pautan                                                                                                                                                                                                                 | Penerangan                                                                                                                                                                                                                                                                                                                     |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning meningkatkan pembelajaran beberapa contoh dengan melatih pada lebih banyak contoh daripada yang boleh dimuatkan dalam prompt, menjimatkan kos, meningkatkan kualiti respons, dan membolehkan permintaan dengan latensi lebih rendah. **Dapatkan gambaran keseluruhan tentang fine-tuning dari OpenAI.**                      |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Fahami **apa itu fine-tuning (konsep)**, mengapa anda perlu mempertimbangkannya (masalah yang memotivasikan), data apa yang digunakan (latihan) dan cara mengukur kualiti                                                                                                                                                       |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service membolehkan anda menyesuaikan model kami dengan set data peribadi menggunakan fine-tuning. Pelajari **cara melakukan fine-tuning (proses)** memilih model menggunakan Azure AI Studio, Python SDK atau REST API.                                                                                              |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM mungkin tidak berprestasi baik dalam domain, tugasan, atau set data tertentu, atau mungkin menghasilkan output yang tidak tepat atau mengelirukan. **Bilakah anda harus mempertimbangkan fine-tuning** sebagai penyelesaian?                                                                                                   |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Continuous fine-tuning adalah proses berulang memilih model yang sudah di-fine-tune sebagai model asas dan **melakukan fine-tuning lebih lanjut** pada set contoh latihan baru.                                                                                                                                                 |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fine-tuning model anda **dengan contoh pemanggilan fungsi** boleh meningkatkan output model dengan mendapatkan respons yang lebih tepat dan konsisten - dengan respons yang diformat serupa & penjimatan kos                                                                                                                     |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Rujuk jadual ini untuk memahami **model mana yang boleh di-fine-tune** dalam Azure OpenAI, dan di wilayah mana ia tersedia. Semak had token dan tarikh luput data latihan jika perlu.                                                                                                                                          |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Episod 30 minit **Okt 2023** AI Show ini membincangkan manfaat, kekurangan dan pandangan praktikal yang membantu anda membuat keputusan ini.                                                                                                                                                                                  |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Sumber **AI Playbook** ini membimbing anda melalui keperluan data, format, fine-tuning hyperparameter dan cabaran/limitasi yang perlu anda ketahui.                                                                                                                                                                             |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Pelajari cara membuat dataset fine-tuning contoh, bersedia untuk fine-tuning, buat kerja fine-tuning, dan lancarkan model yang telah di-fine-tune di Azure.                                                                                                                                                                    |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio membolehkan anda menyesuaikan model bahasa besar kepada set data peribadi _menggunakan aliran kerja berasaskan UI yang sesuai untuk pembangun low-code_. Lihat contoh ini.                                                                                                                                      |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Artikel ini menerangkan cara fine-tune model Hugging Face dengan perpustakaan transformers Hugging Face pada satu GPU menggunakan Azure DataBricks + perpustakaan Hugging Face Trainer                                                                                                                                        |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Katalog model dalam Azure Machine Learning menawarkan banyak model sumber terbuka yang boleh anda fine-tune untuk tugasan khusus anda. Cuba modul ini dari [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fine-tuning model GPT-3.5 atau GPT-4 di Microsoft Azure menggunakan W&B membolehkan penjejakan dan analisis prestasi model secara terperinci. Panduan ini mengembangkan konsep dari panduan Fine-Tuning OpenAI dengan langkah dan ciri khusus untuk Azure OpenAI.                                                               |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. Sumber Sekunder

Bahagian ini mengandungi sumber tambahan yang patut diterokai, tetapi kami tidak sempat membincangkannya dalam pelajaran ini. Ia mungkin akan dibincangkan dalam pelajaran akan datang, atau sebagai pilihan tugasan sekunder pada masa hadapan. Buat masa ini, gunakan sumber ini untuk membina kepakaran dan pengetahuan anda sendiri mengenai topik ini.

| Tajuk/Pautan                                                                                                                                                                                                            | Penerangan                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Penyediaan dan analisis data untuk fine-tuning model chat](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | Notebook ini berfungsi sebagai alat untuk memproses dan menganalisis set data chat yang digunakan untuk fine-tuning model chat. Ia memeriksa kesilapan format, menyediakan statistik asas, dan menganggarkan bilangan token untuk kos fine-tuning. Lihat: [Kaedah fine-tuning untuk gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning untuk Retrieval Augmented Generation (RAG) dengan Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | Notebook ini bertujuan untuk menunjukkan contoh menyeluruh bagaimana fine-tune model OpenAI untuk Retrieval Augmented Generation (RAG). Kami juga akan mengintegrasikan Qdrant dan Few-Shot Learning untuk meningkatkan prestasi model dan mengurangkan rekaan maklumat.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT dengan Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) adalah platform pembangun AI, dengan alat untuk melatih model, fine-tuning model, dan menggunakan model asas. Baca panduan mereka [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) terlebih dahulu, kemudian cuba latihan Cookbook ini.                                                                                                                                                                                                                  |
| **Tutorial Komuniti** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning untuk Model Bahasa Kecil                                                   | Kenali [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), model kecil baru Microsoft yang sangat berkuasa tetapi padat. Tutorial ini akan membimbing anda melalui fine-tuning Phi-2, menunjukkan cara membina set data unik dan fine-tune model menggunakan QLoRA.                                                                                                                                                                       |
| **Tutorial Hugging Face** [Cara Fine-Tune LLM pada 2024 dengan Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Pos blog ini menerangkan cara fine-tune LLM terbuka menggunakan Hugging Face TRL, Transformers & dataset pada 2024. Anda akan menentukan kes penggunaan, sediakan persekitaran pembangunan, sediakan dataset, fine-tune model, uji dan nilai, kemudian lancarkan ke produksi.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Membawa latihan dan pelancaran model pembelajaran mesin terkini yang lebih pantas dan mudah. Repo ini mempunyai tutorial mesra Colab dengan panduan video YouTube, untuk fine-tuning. **Mencerminkan kemas kini [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) terkini**. Baca dokumentasi [AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Penafian**:  
Dokumen ini telah diterjemahkan menggunakan perkhidmatan terjemahan AI [Co-op Translator](https://github.com/Azure/co-op-translator). Walaupun kami berusaha untuk ketepatan, sila ambil perhatian bahawa terjemahan automatik mungkin mengandungi kesilapan atau ketidaktepatan. Dokumen asal dalam bahasa asalnya harus dianggap sebagai sumber yang sahih. Untuk maklumat penting, terjemahan profesional oleh manusia adalah disyorkan. Kami tidak bertanggungjawab atas sebarang salah faham atau salah tafsir yang timbul daripada penggunaan terjemahan ini.