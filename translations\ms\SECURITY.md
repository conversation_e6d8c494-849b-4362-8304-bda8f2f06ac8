<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:55:10+00:00",
  "source_file": "SECURITY.md",
  "language_code": "ms"
}
-->
## Keselamatan

Microsoft mengambil serius keselamatan produk perisian dan perkhidmatan kami, termasuk semua repositori kod sumber yang diuruskan melalui organisasi GitHub kami, yang merangkumi [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin), dan [organisasi GitHub kami](https://opensource.microsoft.com/).

Jika anda percaya anda telah menemui kelemahan keselamatan dalam mana-mana repositori milik Microsoft yang memenuhi [definisi kelemahan keselamatan Microsoft](https://aka.ms/opensource/security/definition), sila laporkan kepada kami seperti yang diterangkan di bawah.

## Melaporkan Isu Keselamatan

**Sila jangan laporkan kelemahan keselamatan melalui isu GitHub awam.**

Sebaliknya, sila laporkan kepada Microsoft Security Response Center (MSRC) di [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Jika anda lebih suka menghantar tanpa log masuk, hantar emel ke [<EMAIL>](mailto:<EMAIL>). Jika boleh, sila enkripsi mesej anda dengan kunci PGP kami; sila muat turun dari [halaman Kunci PGP Microsoft Security Response Center](https://aka.ms/opensource/security/pgpkey).

Anda sepatutnya menerima maklum balas dalam masa 24 jam. Jika atas sebab tertentu anda tidak menerimanya, sila ikuti semula melalui emel untuk memastikan kami telah menerima mesej asal anda. Maklumat tambahan boleh didapati di [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Sila sertakan maklumat yang diminta di bawah (seberapa banyak yang anda boleh berikan) untuk membantu kami memahami dengan lebih baik sifat dan skop isu yang mungkin:

  * Jenis isu (contoh: buffer overflow, suntikan SQL, cross-site scripting, dan lain-lain)
  * Laluan penuh fail sumber yang berkaitan dengan manifestasi isu
  * Lokasi kod sumber yang terjejas (tag/cabang/commit atau URL langsung)
  * Sebarang konfigurasi khas yang diperlukan untuk menghasilkan semula isu
  * Arahan langkah demi langkah untuk menghasilkan semula isu
  * Bukti konsep atau kod eksploitasi (jika boleh)
  * Kesan isu, termasuk bagaimana penyerang mungkin mengeksploitasi isu tersebut

Maklumat ini akan membantu kami menilai laporan anda dengan lebih cepat.

Jika anda melaporkan untuk bug bounty, laporan yang lebih lengkap boleh menyumbang kepada ganjaran bounty yang lebih tinggi. Sila lawati halaman [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) untuk maklumat lanjut mengenai program aktif kami.

## Bahasa Pilihan

Kami mengutamakan semua komunikasi dalam Bahasa Inggeris.

## Polisi

Microsoft mengikut prinsip [Pendedahan Kelemahan Berkoordinasi](https://aka.ms/opensource/security/cvd).

**Penafian**:  
Dokumen ini telah diterjemahkan menggunakan perkhidmatan terjemahan AI [Co-op Translator](https://github.com/Azure/co-op-translator). Walaupun kami berusaha untuk ketepatan, sila ambil perhatian bahawa terjemahan automatik mungkin mengandungi kesilapan atau ketidaktepatan. Dokumen asal dalam bahasa asalnya harus dianggap sebagai sumber yang sahih. Untuk maklumat penting, terjemahan profesional oleh manusia adalah disyorkan. Kami tidak bertanggungjawab atas sebarang salah faham atau salah tafsir yang timbul daripada penggunaan terjemahan ini.