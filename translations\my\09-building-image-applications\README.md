<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "1a7fd0f95f9eb673b79da47c0814f4d4",
  "translation_date": "2025-07-09T13:36:35+00:00",
  "source_file": "09-building-image-applications/README.md",
  "language_code": "my"
}
-->
# ပုံရိပ်ဖန်တီးခြင်း အက်ပလီကေးရှင်းများ တည်ဆောက်ခြင်း

[![ပုံရိပ်ဖန်တီးခြင်း အက်ပလီကေးရှင်းများ တည်ဆောက်ခြင်း](../../../translated_images/09-lesson-banner.906e408c741f44112ff5da17492a30d3872abb52b8530d6506c2631e86e704d0.my.png)](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)

LLM များသည် စာသားဖန်တီးခြင်းထက် ပိုမိုကျယ်ပြန့်သော အရာများကို လုပ်ဆောင်နိုင်သည်။ စာသားဖော်ပြချက်များမှ ပုံရိပ်များကို ဖန်တီးနိုင်ခြင်းလည်း ဖြစ်နိုင်သည်။ ပုံရိပ်များကို မော်ဒယ်တစ်ခုအဖြစ် အသုံးပြုခြင်းသည် MedTech၊ အင်ဂျင်နီယာ၊ ခရီးသွားလုပ်ငန်း၊ ဂိမ်းဖန်တီးခြင်းနှင့် အခြားနယ်ပယ်များတွင် အလွန်အသုံးဝင်နိုင်သည်။ ဤအခန်းတွင် ကျွန်ုပ်တို့သည် လူကြိုက်အများဆုံး ပုံရိပ်ဖန်တီးမှု မော်ဒယ်နှစ်ခုဖြစ်သော DALL-E နှင့် Midjourney ကို လေ့လာကြမည်ဖြစ်သည်။

## နိဒါန်း

ဤသင်ခန်းစာတွင် ကျွန်ုပ်တို့ လေ့လာမည့်အကြောင်းအရာများမှာ -

- ပုံရိပ်ဖန်တီးခြင်းနှင့် ၎င်း၏ အသုံးဝင်မှု။
- DALL-E နှင့် Midjourney ဆိုတာဘာလဲ၊ ၎င်းတို့ ဘယ်လိုအလုပ်လုပ်သလဲ။
- ပုံရိပ်ဖန်တီးမှု အက်ပလီကေးရှင်း တည်ဆောက်နည်း။

## သင်ယူရမည့် ရည်မှန်းချက်များ

ဤသင်ခန်းစာပြီးဆုံးပြီးနောက် -

- ပုံရိပ်ဖန်တီးမှု အက်ပလီကေးရှင်း တည်ဆောက်နိုင်မည်။
- မိမိ၏ အက်ပလီကေးရှင်းအတွက် meta prompt များဖြင့် နယ်နိမိတ် သတ်မှတ်နိုင်မည်။
- DALL-E နှင့် Midjourney နှစ်ခုလုံးကို အသုံးပြုနိုင်မည်။

## ပုံရိပ်ဖန်တီးမှု အက်ပလီကေးရှင်း တည်ဆောက်ရခြင်း၏ အကြောင်းရင်း

ပုံရိပ်ဖန်တီးမှု အက်ပလီကေးရှင်းများသည် Generative AI ၏ စွမ်းဆောင်ရည်များကို စူးစမ်းလေ့လာရန် အလွန်ကောင်းမွန်သော နည်းလမ်းတစ်ခုဖြစ်သည်။ ဥပမာ -

- **ပုံရိပ်တည်းဖြတ်ခြင်းနှင့် ပေါင်းစပ်ခြင်း**။ ပုံရိပ်တည်းဖြတ်ခြင်း၊ ပုံရိပ်ပေါင်းစပ်ခြင်း စသည့် အသုံးပြုမှုများအတွက် ပုံရိပ်များကို ဖန်တီးနိုင်သည်။

- **စက်မှုလုပ်ငန်းအမျိုးမျိုးတွင် အသုံးပြုနိုင်ခြင်း**။ Medtech၊ ခရီးသွားလုပ်ငန်း၊ ဂိမ်းဖန်တီးခြင်းနှင့် အခြားစက်မှုလုပ်ငန်းများအတွက် ပုံရိပ်များ ဖန်တီးရာတွင် အသုံးပြုနိုင်သည်။

## အခြေအနေ - Edu4All

ဤသင်ခန်းစာတွင် ကျွန်ုပ်တို့၏ စတားတပ်ဖြစ်သော Edu4All နှင့် ဆက်လက်လုပ်ဆောင်မည်ဖြစ်သည်။ ကျောင်းသားများသည် မိမိတို့၏ သင်ခန်းစာအတွက် ပုံရိပ်များ ဖန်တီးမည်ဖြစ်ပြီး၊ မည်သည့်ပုံရိပ်များ ဖြစ်မည်ဆိုသည်မှာ ကျောင်းသားများ၏ ဖန်တီးမှုအပေါ် မူတည်သည်။ ဥပမာ - မိမိတို့၏ ဇာတ်လမ်းအတွက် ပုံပြင်ပုံစံများ၊ ဇာတ်ကောင်အသစ်များ ဖန်တီးခြင်း သို့မဟုတ် မိမိစိတ်ကူးများကို မြင်ကွင်းဖော်ဆောင်ခြင်း စသဖြင့် ဖြစ်နိုင်သည်။

Edu4All ၏ ကျောင်းသားများသည် အောက်ပါအတိုင်း အတန်းတွင် အမွတ်တရ အဆောက်အအုံများအပေါ် အလုပ်လုပ်နေပါက ဖန်တီးနိုင်သည် -

![Edu4All startup, class on monuments, Eiffel Tower](../../../translated_images/startup.94d6b79cc4bb3f5afbf6e2ddfcf309aa5d1e256b5f30cc41d252024eaa9cc5dc.my.png)

အောက်ပါ prompt ဖြင့် -

> "Dog next to Eiffel Tower in early morning sunlight"

## DALL-E နှင့် Midjourney ဆိုတာဘာလဲ?

[DALL-E](https://openai.com/dall-e-2?WT.mc_id=academic-105485-koreyst) နှင့် [Midjourney](https://www.midjourney.com/?WT.mc_id=academic-105485-koreyst) သည် လူကြိုက်အများဆုံး ပုံရိပ်ဖန်တီးမှု မော်ဒယ်နှစ်ခုဖြစ်ပြီး၊ prompt များကို အသုံးပြု၍ ပုံရိပ်များ ဖန်တီးပေးသည်။

### DALL-E

DALL-E သည် စာသားဖော်ပြချက်များမှ ပုံရိပ်များ ဖန်တီးပေးသော Generative AI မော်ဒယ်တစ်ခုဖြစ်သည်။

> [DALL-E သည် CLIP နှင့် diffused attention မော်ဒယ်နှစ်ခု ပေါင်းစပ်ထားခြင်းဖြစ်သည်](https://towardsdatascience.com/openais-dall-e-and-clip-101-a-brief-introduction-3a4367280d4e?WT.mc_id=academic-105485-koreyst)။

- **CLIP** သည် ပုံရိပ်နှင့် စာသားမှ ဒေတာကို နံပါတ်အဖြစ် ကိုယ်စားပြုထားသော embedding များ ဖန်တီးပေးသော မော်ဒယ်ဖြစ်သည်။

- **Diffused attention** သည် embedding များမှ ပုံရိပ်များ ဖန်တီးပေးသော မော်ဒယ်ဖြစ်သည်။ DALL-E သည် ပုံရိပ်နှင့် စာသားများပါဝင်သည့် ဒေတာစုစည်းမှုတစ်ခုဖြင့် လေ့ကျင့်ထားပြီး စာသားဖော်ပြချက်များမှ ပုံရိပ်များ ဖန်တီးနိုင်သည်။ ဥပမာ - ခေါင်းစည်းတပ်ထားသော ကြောင်ပုံရိပ်၊ မိုဟော့ခ်ဆံပင်ရှိသော ခွေးပုံရိပ် စသဖြင့် ဖန်တီးနိုင်သည်။

### Midjourney

Midjourney သည် DALL-E နှင့် ဆင်တူပုံစံဖြင့် စာသား prompt များမှ ပုံရိပ်များ ဖန်တီးပေးသည်။ “ခေါင်းစည်းတပ်ထားသော ကြောင်” သို့မဟုတ် “မိုဟော့ခ်ဆံပင်ရှိသော ခွေး” ကဲ့သို့သော prompt များဖြင့် ပုံရိပ်များ ဖန်တီးနိုင်သည်။

![Midjourney ဖြင့် ဖန်တီးထားသော ပုံရိပ်၊ စက်ကိရိယာကြက်ပျော](https://upload.wikimedia.org/wikipedia/commons/thumb/8/8c/Rupert_Breheny_mechanical_dove_eca144e7-476d-4976-821d-a49c408e4f36.png/440px-Rupert_Breheny_mechanical_dove_eca144e7-476d-4976-821d-a49c408e4f36.png?WT.mc_id=academic-105485-koreyst)  
_ပုံရိပ်အရင်းအမြစ် Wikipedia၊ Midjourney ဖြင့် ဖန်တီးထားသည်_

## DALL-E နှင့် Midjourney မော်ဒယ်များ ဘယ်လိုအလုပ်လုပ်သလဲ

ပထမဦးစွာ [DALL-E](https://arxiv.org/pdf/2102.12092.pdf?WT.mc_id=academic-105485-koreyst) ကို ကြည့်မယ်။ DALL-E သည် transformer အခြေခံထားသော Generative AI မော်ဒယ်ဖြစ်ပြီး _autoregressive transformer_ ကို အသုံးပြုသည်။

_autoregressive transformer_ သည် စာသားဖော်ပြချက်မှ ပုံရိပ်ကို တစ်ပစ်ဆွဲချင်း ဖန်တီးပေးသည်။ ပထမဦးဆုံး ပစ်ဆွဲတစ်ခု ဖန်တီးပြီးနောက်၊ ထိုပစ်ဆွဲကို အသုံးပြု၍ နောက်တစ်ပစ်ဆွဲကို ဖန်တီးသည်။ ဤလုပ်ငန်းစဉ်ကို နယူးရယ်ကွန်ယက်အလွှာများဖြတ်သန်းပြီး ပုံရိပ် ပြည့်စုံသည်အထိ ဆက်လက်လုပ်ဆောင်သည်။

ဤလုပ်ငန်းစဉ်ဖြင့် DALL-E သည် ဖန်တီးသော ပုံရိပ်အတွင်းရှိ အင်္ဂါရပ်များ၊ ပစ္စည်းများ၊ လက္ခဏာများကို ထိန်းချုပ်နိုင်သည်။ သို့သော် DALL-E 2 နှင့် 3 တွင် ပိုမိုထိန်းချုပ်မှုရှိသည်။

## ပထမဆုံး ပုံရိပ်ဖန်တီးမှု အက်ပလီကေးရှင်း တည်ဆောက်ခြင်း

ပုံရိပ်ဖန်တီးမှု အက်ပလီကေးရှင်း တည်ဆောက်ရန် လိုအပ်သော library များမှာ -

- **python-dotenv** - သင့်ရဲ့ လျှို့ဝှက်ချက်များကို _.env_ ဖိုင်ထဲသိမ်းဆည်းရန် အကြံပြုသည်။
- **openai** - OpenAI API နှင့် ဆက်သွယ်ရန် အသုံးပြုမည့် library ဖြစ်သည်။
- **pillow** - Python တွင် ပုံရိပ်များကို ကိုင်တွယ်ရန်။
- **requests** - HTTP request များ ပြုလုပ်ရန်။

1. _.env_ ဖိုင်ကို အောက်ပါအတိုင်း ဖန်တီးပါ -

   ```text
   AZURE_OPENAI_ENDPOINT=<your endpoint>
   AZURE_OPENAI_API_KEY=<your key>
   ```

   Azure Portal တွင် သင့် resource ၏ "Keys and Endpoint" အပိုင်းမှ အချက်အလက်များ ရှာဖွေပါ။

1. အထက်ဖော်ပြထားသော library များကို _requirements.txt_ ဖိုင်တွင် စုစည်းပါ -

   ```text
   python-dotenv
   openai
   pillow
   requests
   ```

1. နောက်တစ်ဆင့်မှာ virtual environment ဖန်တီးပြီး library များကို ထည့်သွင်းပါ -

   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

   Windows အတွက် virtual environment ဖန်တီးခြင်းနှင့် ဖွင့်ခြင်းအတွက် အောက်ပါ command များကို အသုံးပြုပါ -

   ```bash
   python3 -m venv venv
   venv\Scripts\activate.bat
   ```

1. _app.py_ ဆိုသော ဖိုင်တွင် အောက်ပါ code များ ထည့်သွင်းပါ -

   ```python
   import openai
   import os
   import requests
   from PIL import Image
   import dotenv

   # import dotenv
   dotenv.load_dotenv()

   # Get endpoint and key from environment variables
   openai.api_base = os.environ['AZURE_OPENAI_ENDPOINT']
   openai.api_key = os.environ['AZURE_OPENAI_API_KEY']

   # Assign the API version (DALL-E is currently supported for the 2023-06-01-preview API version only)
   openai.api_version = '2023-06-01-preview'
   openai.api_type = 'azure'


   try:
       # Create an image by using the image generation API
       generation_response = openai.Image.create(
           prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
           size='1024x1024',
           n=2,
           temperature=0,
       )
       # Set the directory for the stored image
       image_dir = os.path.join(os.curdir, 'images')

       # If the directory doesn't exist, create it
       if not os.path.isdir(image_dir):
           os.mkdir(image_dir)

       # Initialize the image path (note the filetype should be png)
       image_path = os.path.join(image_dir, 'generated-image.png')

       # Retrieve the generated image
       image_url = generation_response["data"][0]["url"]  # extract image URL from response
       generated_image = requests.get(image_url).content  # download the image
       with open(image_path, "wb") as image_file:
           image_file.write(generated_image)

       # Display the image in the default image viewer
       image = Image.open(image_path)
       image.show()

   # catch exceptions
   except openai.InvalidRequestError as err:
       print(err)

   ```

ဒီ code ကို ရှင်းပြပါမယ် -

- ပထမဦးဆုံး လိုအပ်သော library များကို import လုပ်သည်။ OpenAI library၊ dotenv library၊ requests library နှင့် Pillow library ပါဝင်သည်။

  ```python
  import openai
  import os
  import requests
  from PIL import Image
  import dotenv
  ```

- နောက်တစ်ဆင့် _.env_ ဖိုင်မှ environment variable များကို load လုပ်သည်။

  ```python
  # import dotenv
  dotenv.load_dotenv()
  ```

- ထို့နောက် OpenAI API အတွက် endpoint၊ key၊ version နှင့် type ကို သတ်မှတ်သည်။

  ```python
  # Get endpoint and key from environment variables
  openai.api_base = os.environ['AZURE_OPENAI_ENDPOINT']
  openai.api_key = os.environ['AZURE_OPENAI_API_KEY']

  # add version and type, Azure specific
  openai.api_version = '2023-06-01-preview'
  openai.api_type = 'azure'
  ```

- နောက်တစ်ဆင့် ပုံရိပ်ကို ဖန်တီးသည် -

  ```python
  # Create an image by using the image generation API
  generation_response = openai.Image.create(
      prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
      size='1024x1024',
      n=2,
      temperature=0,
  )
  ```

  အထက်ပါ code သည် ဖန်တီးထားသော ပုံရိပ် URL ပါဝင်သည့် JSON object ဖြင့် ပြန်လည်တုံ့ပြန်သည်။ URL ကို အသုံးပြု၍ ပုံရိပ်ကို ဒေါင်းလုပ်လုပ်ပြီး ဖိုင်အဖြစ် သိမ်းဆည်းနိုင်သည်။

- နောက်ဆုံးတွင် ပုံရိပ်ကို ဖွင့်ပြီး ပုံရိပ်ကြည့်ရှုသူဖြင့် ပြသသည် -

  ```python
  image = Image.open(image_path)
  image.show()
  ```

### ပုံရိပ်ဖန်တီးမှု code ကို အသေးစိတ်ကြည့်ရှုခြင်း

ပုံရိပ်ဖန်တီးမှု code ကို အသေးစိတ်ကြည့်မယ် -

```python
generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2,
        temperature=0,
    )
```

- **prompt** သည် ပုံရိပ်ဖန်တီးရန် အသုံးပြုသော စာသား prompt ဖြစ်သည်။ ဤနေရာတွင် "Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils" ကို အသုံးပြုထားသည်။
- **size** သည် ဖန်တီးမည့် ပုံရိပ်၏ အရွယ်အစားဖြစ်ပြီး ဤနေရာတွင် 1024x1024 ပစ်ဆွဲဖြစ်သည်။
- **n** သည် ဖန်တီးမည့် ပုံရိပ်အရေအတွက်ဖြစ်ပြီး ဤနေရာတွင် ၂ ပုံ ဖန်တီးမည်။
- **temperature** သည် Generative AI မော်ဒယ်၏ ထွက်ရှိမှု random ဖြစ်မှုကို ထိန်းချုပ်သည့် ပါရာမီတာဖြစ်သည်။ 0 မှ 1 အတွင်းတန်ဖိုးရှိပြီး 0 ဆိုသည်မှာ ထွက်ရှိမှု သေချာတိကျမှုရှိခြင်း၊ 1 ဆိုသည်မှာ အလွန် random ဖြစ်ခြင်း ဖြစ်သည်။ ပုံမှန်တန်ဖိုးမှာ 0.7 ဖြစ်သည်။

နောက်ပိုင်းတွင် ပုံရိပ်များနှင့် ပတ်သက်၍ ပိုမိုလုပ်ဆောင်နိုင်သည့် အကြောင်းအရာများကို ဆက်လက်ဖော်ပြမည်။

## ပုံရိပ်ဖန်တီးမှု၏ ထပ်ဆောင်းစွမ်းရည်များ

Python တွင် အကြောင်းအရာအနည်းငယ်ဖြင့် ပုံရိပ်ဖန်တီးနိုင်ခဲ့ကြောင်း မြင်တွေ့ခဲ့ပါပြီ။ သို့သော် ပုံရိပ်များနှင့် ပိုမိုလုပ်ဆောင်နိုင်သည့် အရာများ ရှိသည်။

အောက်ပါအရာများကိုလည်း ပြုလုပ်နိုင်သည် -

- **တည်းဖြတ်ခြင်းများ ပြုလုပ်ခြင်း**။ ရှိပြီးသား ပုံရိပ်တစ်ပုံကို mask နှင့် prompt ဖြင့် ပုံရိပ်တည်းဖြတ်နိုင်သည်။ ဥပမာ - ကျွန်ုပ်တို့၏ ကြက်ကလေးပုံရိပ်တွင် ခေါင်းစည်းတပ်ပေးနိုင်သည်။ ၎င်းကို ပြုလုပ်ရန် ပုံရိပ်၊ mask (ပြောင်းလဲလိုသည့် အပိုင်းကို သတ်မှတ်ထားသော) နှင့် စာသား prompt တစ်ခု ပေးရမည်။

  ```python
  response = openai.Image.create_edit(
    image=open("base_image.png", "rb"),
    mask=open("mask.png", "rb"),
    prompt="An image of a rabbit with a hat on its head.",
    n=1,
    size="1024x1024"
  )
  image_url = response['data'][0]['url']
  ```

  အခြေခံပုံရိပ်တွင် ကြက်ကလေးသာ ပါဝင်မည်ဖြစ်ပြီး နောက်ဆုံးပုံရိပ်တွင် ခေါင်းစည်းပါဝင်မည်ဖြစ်သည်။

- **မျိုးစုံဖန်တီးခြင်း**။ ရှိပြီးသား ပုံရိပ်တစ်ပုံကို ယူပြီး မျိုးစုံပုံများ ဖန်တီးရန် တောင်းဆိုနိုင်သည်။ မျိုးစုံဖန်တီးရန် ပုံရိပ်နှင့် စာသား prompt တစ်ခု ပေးပြီး အောက်ပါ code ကဲ့သို့ အသုံးပြုနိုင်သည် -

  ```python
  response = openai.Image.create_variation(
    image=open("bunny-lollipop.png", "rb"),
    n=1,
    size="1024x1024"
  )
  image_url = response['data'][0]['url']
  ```

  > မှတ်ချက် - ဤလုပ်ဆောင်ချက်ကို OpenAI တွင်သာ ထောက်ပံ့သည်။

## Temperature

Temperature သည် Generative AI မော်ဒယ်၏ ထွက်ရှိမှု random ဖြစ်မှုကို ထိန်းချုပ်သည့် ပါရာမီတာဖြစ်သည်။ 0 မှ 1 အတွင်းတန်ဖိုးရှိပြီး 0 ဆိုသည်မှာ ထွက်ရှိမှု သေချာတိကျမှုရှိခြင်း၊ 1 ဆိုသည်မှာ အလွန် random ဖြစ်ခြင်း ဖြစ်သည်။ ပုံမှန်တန်ဖိုးမှာ 0.7 ဖြစ်သည်။

Temperature ၏ လုပ်ဆောင်ပုံကို နမူနာဖြင့် ကြည့်မယ်၊ အောက်ပါ prompt ကို နှစ်ကြိမ် လည်ပတ်ကြည့်မယ် -

> Prompt : "Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils"

![Bunny on a horse holding a lollipop, version 1](../../../translated_images/v1-generated-image.a295cfcffa3c13c2432eb1e41de7e49a78c814000fb1b462234be24b6e0db7ea.my.png)

ယခု အတူတူ prompt ကို ထပ်မံ လည်ပတ်ကြည့်မယ်၊ ပုံရိပ်တူညီမှု မရှိကြောင်း သေချာရန် -

![Generated image of bunny on horse](../../../translated_images/v2-generated-image.33f55a3714efe61dc19622c869ba6cd7d6e6de562e26e95b5810486187aace39.my.png)

မြင်တွေ့ရသလို ပုံရိပ်များသည် ဆင်တူသော်လည်း တူညီမှု မရှိကြပါ။ Temperature တန်ဖိုးကို 0.1 သို့ ပြောင်းလဲကြည့်မယ် -

```python
 generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2
    )
```

### Temperature ပြောင်းလဲခြင်း

ထို့ကြောင့် တုံ့ပြန်မှုကို ပိုမို သေချာတိကျစေရန် ကြိုးစားကြည့်မယ်။ ဖန်တီးထားသော ပုံရိပ်နှစ်ပုံကို ကြည့်လျှင် ပထမပုံတွင် ကြက်ကလေးရှိပြီး ဒုတိယပုံတွင် မြင်းရှိသည်။ ထို့ကြောင့် ပုံရိပ်များ ကြီးမားစွာ ကွဲပြားနေသည်။

ထို့ကြောင့် code ကို ပြောင်းလဲပြီး temperature ကို 0 သတ်မှတ်မည် -

```python
generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2,
        temperature=0
    )
```

ယခု code ကို လည်ပတ်သောအခါ အောက်ပါ ပုံရိပ်နှစ်ပုံ ရရှိမည် -

- ![Temperature 0, v1](./images/v1-temp-generated

**အကြောင်းကြားချက်**  
ဤစာတမ်းကို AI ဘာသာပြန်ဝန်ဆောင်မှု [Co-op Translator](https://github.com/Azure/co-op-translator) ဖြင့် ဘာသာပြန်ထားပါသည်။ ကျွန်ုပ်တို့သည် တိကျမှန်ကန်မှုအတွက် ကြိုးစားသော်လည်း အလိုအလျောက် ဘာသာပြန်ခြင်းတွင် အမှားများ သို့မဟုတ် မှားယွင်းချက်များ ပါဝင်နိုင်ကြောင်း သတိပြုပါရန် မေတ္တာရပ်ခံအပ်ပါသည်။ မူရင်းစာတမ်းကို မူလဘာသာဖြင့်သာ တရားဝင်အချက်အလက်အဖြစ် ယူဆသင့်ပါသည်။ အရေးကြီးသော အချက်အလက်များအတွက် လူ့ဘာသာပြန်ပညာရှင်မှ ဘာသာပြန်ခြင်းကို အကြံပြုပါသည်။ ဤဘာသာပြန်ချက်ကို အသုံးပြုရာမှ ဖြစ်ပေါ်လာနိုင်သည့် နားလည်မှုမှားယွင်းမှုများအတွက် ကျွန်ုပ်တို့သည် တာဝန်မယူပါ။