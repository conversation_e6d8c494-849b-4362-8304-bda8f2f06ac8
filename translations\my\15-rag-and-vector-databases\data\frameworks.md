<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:38:48+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "my"
}
-->
# Neural Network Frameworks

ကျွန်တော်တို့ အရင်က သင်ယူထားသလို၊ နယူးရယ်နက်ဝပ်တွေကို ထိရောက်စွာ လေ့ကျင့်နိုင်ဖို့အတွက် အောက်ပါ အချက်နှစ်ချက် လုပ်ဆောင်ရပါမယ်။

* Tensor တွေကို လုပ်ဆောင်နိုင်ဖို့၊ ဥပမာ များစွာဖြစ်တဲ့ မျိုးစုံမြှောက်ခြင်း၊ ပေါင်းခြင်း၊ sigmoid သို့မဟုတ် softmax ကဲ့သို့သော function များတွက်ချက်ခြင်း
* Gradient descent optimization လုပ်နိုင်ဖို့အတွက် အထွေထွေ အထွေထွေ ဖော်ပြချက်များ၏ gradient များတွက်ချက်ခြင်း

`numpy` library က ပထမအပိုင်းကို လုပ်နိုင်ပေမယ့် gradient တွေတွက်ချက်ဖို့ မည်သည့်စနစ်တစ်ခုခု လိုအပ်ပါတယ်။ ကျွန်တော်တို့ ယခင်အပိုင်းမှာ ဖန်တီးခဲ့တဲ့ framework မှာ `backward` method အတွင်းမှာ derivative function တွေကို လက်ဖြင့်ရေးသားရပြီး backpropagation လုပ်ခဲ့ရပါတယ်။ အကောင်းဆုံး framework တစ်ခုကတော့ ကျွန်တော်တို့ သတ်မှတ်နိုင်တဲ့ *မည်သည့်ဖော်ပြချက်* မဆို gradient တွေတွက်ချက်ခွင့်ပေးသင့်ပါတယ်။

အရေးကြီးတဲ့ အချက်တစ်ခုကတော့ GPU သို့မဟုတ် TPU ကဲ့သို့ အထူးပြု ကွန်ပျူတာယူနစ်များပေါ်မှာ တွက်ချက်မှုများ လုပ်ဆောင်နိုင်ဖို့ ဖြစ်ပါတယ်။ နက်ရှိုင်းတဲ့ နယူးရယ်နက်ဝပ် လေ့ကျင့်မှုမှာ တွက်ချက်မှုများ *အလွန်များပြား*ပြီး GPU များပေါ်မှာ parallelize လုပ်နိုင်ဖို့ အရေးကြီးပါတယ်။

> ✅ 'parallelize' ဆိုတာက တွက်ချက်မှုများကို စက်ပစ္စည်းများစွာပေါ်မှာ ဖြန့်ဝေခြင်းကို ဆိုလိုပါတယ်။

လက်ရှိမှာ နာမည်ကြီး neural frameworks နှစ်ခုမှာ TensorFlow နဲ့ PyTorch ဖြစ်ကြပြီး နှစ်ခုလုံး CPU နဲ့ GPU ပေါ်မှာ tensor တွေနဲ့ လုပ်ဆောင်ဖို့ low-level API ပေးထားပါတယ်။ low-level API အပေါ်မှာ Keras နဲ့ PyTorch Lightning ဆိုတဲ့ higher-level API များလည်း ရှိပါတယ်။

Low-Level API | TensorFlow| PyTorch
--------------|-------------------------------------|--------------------------------
High-level API| Keras| Pytorch

**Low-level APIs** နှစ်ခုလုံးမှာ **computational graphs** လို့ခေါ်တဲ့ အရာကို တည်ဆောက်နိုင်ပါတယ်။ ဒီ graph က input parameters တွေကို အသုံးပြုပြီး output (ပုံမှန်အားဖြင့် loss function) ကို ဘယ်လိုတွက်ချက်မလဲ ဆိုတာ သတ်မှတ်ပေးပါတယ်။ GPU ရှိရင် GPU ပေါ်မှာ တွက်ချက်ဖို့ တင်ပို့လို့ရပါတယ်။ ဒီ computational graph ကို ကွဲပြားခြားနားစေပြီး gradient တွေတွက်ချက်ဖို့ function များလည်း ရှိပြီး၊ ဒီ gradient တွေကို model parameters များ optimize ဖို့ အသုံးပြုနိုင်ပါတယ်။

**High-level APIs** က neural networks ကို **layers များစဉ်** အဖြစ် သတ်မှတ်ပြီး neural network များ တည်ဆောက်တာကို ပိုမိုလွယ်ကူစေပါတယ်။ မော်ဒယ်ကို လေ့ကျင့်ဖို့ data ပြင်ဆင်ပြီး `fit` function ကို ခေါ်ရုံနဲ့ လုပ်ဆောင်နိုင်ပါတယ်။

High-level API က neural network များကို အလျင်အမြန် တည်ဆောက်နိုင်စေပြီး အသေးစိတ်များကို စိတ်ပူစရာမလိုဘဲ လုပ်ဆောင်နိုင်ပါတယ်။ တစ်ဖက်မှာတော့ low-level API က လေ့ကျင့်မှု လုပ်ငန်းစဉ်ကို ပိုမိုထိန်းချုပ်ခွင့်ပေးပြီး သုတေသနလုပ်ရာမှာ အသုံးများပါတယ်၊ အထူးသဖြင့် နယူးရယ်နက်ဝပ် architecture အသစ်များကို စမ်းသပ်တဲ့အခါမှာပါ။

နှစ်ခုလုံး API ကို တွဲဖက်အသုံးပြုနိုင်တယ်ဆိုတာကိုလည်း နားလည်ဖို့ အရေးကြီးပါတယ်။ ဥပမာ low-level API ကို အသုံးပြုပြီး ကိုယ်ပိုင် network layer architecture တည်ဆောက်ပြီး high-level API နဲ့ တည်ဆောက်ပြီး လေ့ကျင့်ထားတဲ့ network အတွင်းမှာ အသုံးပြုနိုင်ပါတယ်။ ဒါမှမဟုတ် high-level API နဲ့ layer များစဉ်အဖြစ် network တစ်ခု သတ်မှတ်ပြီး ကိုယ်ပိုင် low-level training loop နဲ့ optimization လုပ်နိုင်ပါတယ်။ နှစ်ခုလုံး API တွေဟာ အခြေခံ အယူအဆတူညီပြီး အတူတကွ အလုပ်လုပ်ဖို့ ဒီဇိုင်းဆွဲထားတာပါ။

## Learning

ဒီသင်တန်းမှာ PyTorch နဲ့ TensorFlow နှစ်ခုလုံးအတွက် အကြောင်းအရာများကို ပေးထားပါတယ်။ သင်ကြိုက်နှစ်သက်တဲ့ framework ကို ရွေးပြီး သက်ဆိုင်ရာ notebook များကိုသာ လေ့လာနိုင်ပါတယ်။ ဘယ် framework ကို ရွေးရမလဲ မသေချာရင် အင်တာနက်မှာရှိတဲ့ **PyTorch vs. TensorFlow** ဆွေးနွေးချက်များကို ဖတ်ရှုနိုင်ပါတယ်။ နှစ်ခုလုံးကို ကြည့်ရှု၍ နားလည်မှု ပိုမိုရနိုင်ပါတယ်။

ဖြစ်နိုင်သမျှ High-Level APIs ကို လွယ်ကူစေရန် အသုံးပြုမှာ ဖြစ်ပါတယ်။ သို့သော် နယူးရယ်နက်ဝပ်တွေ ဘယ်လို အလုပ်လုပ်ကြောင်း အခြေခံမှ နားလည်ဖို့ အရေးကြီးလို့ အစမှာတော့ low-level API နဲ့ tensor တွေနဲ့ လုပ်ဆောင်ခြင်းကို စတင်ပါမယ်။ သို့သော် အချိန်မကုန်ချင်ဘူးဆိုရင်တော့ ဒီအသေးစိတ်တွေကို ကျော်လွှားပြီး high-level API notebook များကို တိုက်ရိုက်သွားနိုင်ပါတယ်။

## ✍️ Exercises: Frameworks

အောက်ပါ notebook များမှာ သင်ယူမှုကို ဆက်လက်လုပ်ဆောင်ပါ။

Low-Level API | TensorFlow+Keras Notebook | PyTorch
--------------|-------------------------------------|--------------------------------
High-level API| Keras | *PyTorch Lightning*

frameworks များကို ကျွမ်းကျင်ပြီးနောက်၊ overfitting ဆိုတဲ့ အကြောင်းအရာကို ပြန်လည်သုံးသပ်ကြမယ်။

# Overfitting

Overfitting ဆိုတာ machine learning မှာ အလွန်အရေးကြီးတဲ့ အယူအဆတစ်ခုဖြစ်ပြီး မှန်ကန်စွာ နားလည်ဖို့ အရေးကြီးပါတယ်။

အောက်ပါ ပြဿနာကို စဉ်းစားပါ၊ 5 ခုသော အမှတ်များကို (အောက်က ဂရပ်မှာ `x` နဲ့ ဖော်ပြထားသည်) ခန့်မှန်းဖို့ ဖြစ်ပါတယ်။

!linear | overfit
-------------------------|--------------------------
**Linear model, 2 parameters** | **Non-linear model, 7 parameters**
Training error = 5.3 | Training error = 0
Validation error = 5.1 | Validation error = 20

* ဘယ်ဘက်မှာတော့ parameter အရေအတွက် သင့်တော်လို့ ကောင်းမွန်တဲ့ တည့်တည့်လိုင်း ခန့်မှန်းမှုကို မြင်ရပါတယ်။ မော်ဒယ်က အမှတ်များ၏ ဖြန့်ဖြူးမှုကို မှန်ကန်စွာ နားလည်နိုင်ပါတယ်။
* ညာဘက်မှာတော့ မော်ဒယ်က အလွန်အစွမ်းထက်ပါတယ်။ အမှတ် 5 ခုသာရှိပြီး parameter 7 ခုရှိတဲ့အတွက် မော်ဒယ်က အမှတ်အားလုံးကို ဖြတ်သွားနိုင်ပြီး training error ကို 0 ဖြစ်အောင် ပြုလုပ်နိုင်ပါတယ်။ သို့သော် ဒါက မော်ဒယ်ကို ဒေတာနောက်ကွယ်ရှိ ပုံစံမှန်ကို နားလည်ခြင်းမှ ကာကွယ်ပြီး validation error ကို အလွန်မြင့်တက်စေပါတယ်။

မော်ဒယ်၏ စွမ်းဆောင်ရည် (parameter အရေအတွက်) နဲ့ training sample အရေအတွက်အကြား မှန်ကန်တဲ့ ညီမျှမှု ရှိဖို့ အလွန်အရေးကြီးပါတယ်။

## Why overfitting occurs

  * Training data မလုံလောက်ခြင်း
  * မော်ဒယ် အလွန်အစွမ်းထက်ခြင်း
  * Input data တွင် ဆူညံသံများ များပြားခြင်း

## How to detect overfitting

အထက်ပါ ဂရပ်မှ မြင်ရသလို၊ overfitting ကို training error နည်းပြီး validation error မြင့်မားခြင်းဖြင့် တွေ့နိုင်ပါတယ်။ ပုံမှန်အားဖြင့် training အတွင်း training error နဲ့ validation error နှစ်ခုလုံး လျော့နည်းလာပြီးနောက် validation error က တစ်ချိန်မှာတော့ လျော့နည်းမှု ရပ်တန့်ပြီး တက်လာနိုင်ပါတယ်။ ဒါက overfitting ရှိနေကြောင်း အချက်ပြဖြစ်ပြီး training ကို ရပ်တန့်သင့်တဲ့ အချိန်ဖြစ်ပါတယ် (သို့မဟုတ် မော်ဒယ် snapshot တစ်ခု ယူထားသင့်ပါတယ်)။

overfitting

## How to prevent overfitting

overfitting ဖြစ်နေကြောင်း တွေ့ရင် အောက်ပါနည်းလမ်းများထဲမှ တစ်ခုခု လုပ်ဆောင်နိုင်ပါတယ်။

 * Training data အရေအတွက် တိုးမြှင့်ခြင်း
 * မော်ဒယ်၏ ရှုပ်ထွေးမှု လျော့နည်းစေခြင်း
 * Dropout ကဲ့သို့ regularization နည်းလမ်းများ အသုံးပြုခြင်း (နောက်ပိုင်းတွင် ဆွေးနွေးပါမယ်)

## Overfitting and Bias-Variance Tradeoff

Overfitting ဟာ စာရင်းအင်းသိပ္ပံမှာ Bias-Variance Tradeoff လို့ခေါ်တဲ့ ပိုမိုသုံးသပ်ရခက်တဲ့ ပြဿနာတစ်ခုရဲ့ အခြေအနေတစ်ခုပါ။ မော်ဒယ်အမှားရင်းမြစ်များကို စဉ်းစားရင် အမှားအမျိုးအစား နှစ်မျိုးရှိပါတယ်။

* **Bias errors** ဆိုတာ training data နဲ့ ဆက်စပ်မှုကို မမှန်ကန်စွာ ဖမ်းယူနိုင်ခြင်းကြောင့် ဖြစ်ပြီး မော်ဒယ် အစွမ်းမပြည့်ခြင်း (**underfitting**) ဖြစ်စေပါတယ်။
* **Variance errors** ဆိုတာ မော်ဒယ်က input data ထဲရှိ ဆူညံသံကို အဓိပ္ပာယ်ရှိတဲ့ ဆက်စပ်မှုအစား ခန့်မှန်းခြင်းကြောင့် ဖြစ်ပြီး (**overfitting**) ဖြစ်စေပါတယ်။

Training အတွင်း bias error လျော့နည်းလာပြီး variance error တက်လာပါတယ်။ overfitting ဖြစ်ခြင်းကို ကာကွယ်ဖို့ training ကို ရပ်တန့်ဖို့ အရေးကြီးပြီး၊ ဒါကို လက်ဖြင့် (overfitting တွေ့ရင်) သို့မဟုတ် အလိုအလျောက် (regularization ထည့်သွင်းခြင်းဖြင့်) ပြုလုပ်နိုင်ပါတယ်။

## Conclusion

ဒီသင်ခန်းစာမှာ TensorFlow နဲ့ PyTorch ဆိုတဲ့ နာမည်ကြီး AI frameworks နှစ်ခုအတွက် API များအကြား ကွာခြားချက်များကို သင်ယူခဲ့ပြီး၊ အရေးကြီးတဲ့ အကြောင်းအရာတစ်ခုဖြစ်တဲ့ overfitting ကိုလည်း လေ့လာသိရှိခဲ့ပါတယ်။

## 🚀 Challenge

အတူတကွပါဝင်တဲ့ notebook များအောက်ဆုံးတွင် 'tasks' တွေရှိပါတယ်။ notebook များကို လေ့လာပြီး အလုပ်များကို ပြီးမြောက်အောင် လုပ်ဆောင်ပါ။

## Review & Self Study

အောက်ပါ ခေါင်းစဉ်များအပေါ် သုတေသန လုပ်ပါ။

- TensorFlow
- PyTorch
- Overfitting

ကိုယ်တိုင်ကို အောက်ပါမေးခွန်းများ မေးမြန်းပါ။

- TensorFlow နဲ့ PyTorch ကြားက ကွာခြားချက် ဘာလဲ?
- Overfitting နဲ့ underfitting ကြားက ကွာခြားချက် ဘာလဲ?

## Assignment

ဒီ lab မှာ PyTorch သို့မဟုတ် TensorFlow ကို အသုံးပြုပြီး single-layer နဲ့ multi-layer fully-connected network များဖြင့် classification ပြဿနာ နှစ်ခုကို ဖြေရှင်းဖို့ တာဝန်ပေးထားပါတယ်။

**အကြောင်းကြားချက်**  
ဤစာတမ်းကို AI ဘာသာပြန်ဝန်ဆောင်မှု [Co-op Translator](https://github.com/Azure/co-op-translator) ဖြင့် ဘာသာပြန်ထားပါသည်။ ကျွန်ုပ်တို့သည် တိကျမှန်ကန်မှုအတွက် ကြိုးပမ်းနေသော်လည်း၊ အလိုအလျောက် ဘာသာပြန်ခြင်းများတွင် အမှားများ သို့မဟုတ် မှားယွင်းချက်များ ပါဝင်နိုင်ကြောင်း သတိပြုပါရန် မေတ္တာရပ်ခံအပ်ပါသည်။ မူရင်းစာတမ်းကို မိမိဘာသာစကားဖြင့်သာ တရားဝင်အချက်အလက်အဖြစ် ယူဆသင့်ပါသည်။ အရေးကြီးသော အချက်အလက်များအတွက် လူ့ဘာသာပြန်ပညာရှင်မှ ဘာသာပြန်ခြင်းကို အကြံပြုပါသည်။ ဤဘာသာပြန်ချက်ကို အသုံးပြုမှုကြောင့် ဖြစ်ပေါ်လာနိုင်သည့် နားလည်မှုမှားယွင်းမှုများအတွက် ကျွန်ုပ်တို့ တာဝန်မယူပါ။