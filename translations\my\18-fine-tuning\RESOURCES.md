<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:10:36+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "my"
}
-->
# ကိုယ်တိုင်လေ့လာသင်ယူရန် အရင်းအမြစ်များ

ဒီသင်ခန်းစာကို OpenAI နှင့် Azure OpenAI မှ အဓိက အရင်းအမြစ်များကို အသုံးပြု၍ သင်္ချာနှင့် သင်ခန်းစာများအတွက် ကိုးကားထားသည်။ သင်၏ ကိုယ်တိုင်လေ့လာသင်ယူမှုများအတွက် အပြည့်အစုံမဟုတ်သော်လည်း အောက်ပါစာရင်းကို ဖော်ပြထားသည်။

## ၁။ အဓိက အရင်းအမြစ်များ

| ခေါင်းစဉ်/လင့်ခ်                                                                                                                                                                                                                   | ဖော်ပြချက်                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning သည် prompt ထဲသို့ ထည့်နိုင်သည့် နမူနာအရေအတွက်ထက် ပိုမိုများသော နမူနာများဖြင့် လေ့ကျင့်ခြင်းဖြင့် few-shot learning ကို တိုးတက်စေပြီး ကုန်ကျစရိတ်လျော့နည်းစေ၊ တုံ့ပြန်မှုအရည်အသွေး မြှင့်တင်ပေးပြီး တုံ့ပြန်မှုအချိန်လည်း လျော့နည်းစေသည်။ **OpenAI မှ fine-tuning အကြောင်း အကျဉ်းချုပ်ကို ရယူပါ။**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | **fine-tuning ဆိုတာဘာလဲ (အယူအဆ)**၊ ဘာကြောင့် စဉ်းစားသင့်သလဲ (အကြောင်းပြချက်)၊ ဘယ်ဒေတာကို အသုံးပြုရမလဲ (လေ့ကျင့်မှု) နှင့် အရည်အသွေးကို မည်သို့တိုင်းတာရမည်ကို နားလည်ပါ။                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service သည် fine-tuning ဖြင့် သင့်ကိုယ်ပိုင် ဒေတာစုံစမ်းမှုများအတွက် မော်ဒယ်များကို ကိုက်ညီစေရန် ခွင့်ပြုသည်။ Azure AI Studio, Python SDK သို့မဟုတ် REST API ကို အသုံးပြု၍ **fine-tuning လုပ်နည်း (လုပ်ငန်းစဉ်)** ကို သင်ယူပါ။                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM များသည် အထူးသီးသန့် ဒိုမိန်းများ၊ တာဝန်များ သို့မဟုတ် ဒေတာစုံများတွင် ကောင်းစွာ လုပ်ဆောင်နိုင်ခြင်းမရှိနိုင်သလို မှားယွင်းသော သို့မဟုတ် မမှန်ကန်သော အဖြေများ ထုတ်ပေးနိုင်သည်။ **ဘယ်အချိန်တွင် fine-tuning ကို စဉ်းစားသင့်သလဲ** ဆိုတာကို သိရှိပါ။                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Continuous fine-tuning သည် ရှိပြီးသား fine-tuned မော်ဒယ်ကို အခြေခံမော်ဒယ်အဖြစ် ရွေးချယ်ပြီး နမူနာအသစ်များဖြင့် **နောက်ထပ် fine-tuning ဆက်လက်လုပ်ဆောင်ခြင်း** ဖြစ်သည်။                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | မော်ဒယ်ကို **function calling နမူနာများဖြင့် fine-tuning လုပ်ခြင်း** သည် ပိုမိုတိကျပြီး တိကျမှန်ကန်သော အဖြေများ ထုတ်ပေးနိုင်စေပြီး တုံ့ပြန်မှုပုံစံတူညီမှုနှင့် ကုန်ကျစရိတ်လျော့နည်းမှုများကို ရရှိစေသည်။                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Azure OpenAI တွင် **မော်ဒယ်များကို ဘယ်လို fine-tuning လုပ်နိုင်သည်**၊ မည်သည့်ဒေသများတွင် ရရှိနိုင်သည်၊ token ကန့်သတ်ချက်များနှင့် လေ့ကျင့်မှုဒေတာ သက်တမ်းကုန်ဆုံးချိန်များကို ဒီဇယားမှ ကြည့်ရှုနိုင်ပါသည်။                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | ဒီ AI Show ၃၀ မိနစ်ကြာ **အောက်တိုဘာ ၂၀၂၃** အပိုင်းတွင် fine-tuning ၏ အကျိုးကျေးဇူးများ၊ အားနည်းချက်များနှင့် လက်တွေ့အမြင်များကို ဆွေးနွေးထားပြီး ဆုံးဖြတ်ချက်ချရာတွင် အကူအညီဖြစ်စေပါသည်။                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | ဒီ **AI Playbook** အရင်းအမြစ်သည် ဒေတာလိုအပ်ချက်များ၊ ဖော်မတ်ပြုလုပ်ခြင်း၊ hyperparameter fine-tuning နှင့် ကြုံတွေ့နိုင်သော စိန်ခေါ်မှုများ/ကန့်သတ်ချက်များကို လမ်းညွှန်ပေးသည်။                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | နမူနာ fine-tuning ဒေတာစုံတစ်ခု ဖန်တီးခြင်း၊ fine-tuning အတွက် ပြင်ဆင်ခြင်း၊ fine-tuning အလုပ်တစ်ခု ဖန်တီးခြင်းနှင့် Azure ပေါ်တွင် fine-tuned မော်ဒယ်ကို တပ်ဆင်အသုံးပြုခြင်းကို သင်ယူပါ။                                                                                                                                                                                    |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio သည် သင့်ကိုယ်ပိုင် ဒေတာစုံများအတွက် အကြီးစားဘာသာစကားမော်ဒယ်များကို _low-code developer များအတွက် သင့်တော်သော UI-based workflow ဖြင့်_ ကိုက်ညီစေရန် ခွင့်ပြုသည်။ ဤနမူနာကို ကြည့်ရှုပါ။                                                                                                                                                               |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | ဒီဆောင်းပါးတွင် Azure DataBricks နှင့် Hugging Face Trainer libraries ကို အသုံးပြု၍ Hugging Face transformers library ဖြင့် single GPU ပေါ်တွင် Hugging Face မော်ဒယ်ကို fine-tune လုပ်နည်းကို ဖော်ပြထားသည်။                                                                                                                                                |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Azure Machine Learning ၏ မော်ဒယ်စာရင်းတွင် သင့်တာဝန်အတွက် fine-tune လုပ်နိုင်သည့် အများအပြားသော open source မော်ဒယ်များ ပါဝင်သည်။ ဒီ module ကို [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) မှ လေ့လာနိုင်ပါသည်။ |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Microsoft Azure ပေါ်တွင် W&B ကို အသုံးပြု၍ GPT-3.5 သို့မဟုတ် GPT-4 မော်ဒယ်များကို fine-tuning လုပ်ခြင်းသည် မော်ဒယ်စွမ်းဆောင်ရည်ကို အသေးစိတ် စောင့်ကြည့်ခြင်းနှင့် ခွဲခြမ်းစိတ်ဖြာခြင်းများ ပြုလုပ်နိုင်စေသည်။ ဤလမ်းညွှန်သည် OpenAI Fine-Tuning လမ်းညွှန်မှ အယူအဆများကို Azure OpenAI အတွက် အထူးအဆင့်များနှင့် အဆင့်ဆင့် လုပ်ဆောင်ချက်များဖြင့် တိုးချဲ့ထားသည်။                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## ၂။ ဒုတိယအဆင့် အရင်းအမြစ်များ

ဤအပိုင်းတွင် သင်ခန်းစာတွင် မဖော်ပြနိုင်ခဲ့သည့် ထပ်ဆောင်း အရင်းအမြစ်များ ပါဝင်သည်။ အနာဂတ်သင်ခန်းစာများတွင် သို့မဟုတ် ဒုတိယအဆင့်တာဝန်အဖြစ် လေ့လာနိုင်ပါသည်။ ယခုအချိန်တွင် သင်၏ ကိုယ်ပိုင် ကျွမ်းကျင်မှုနှင့် အသိပညာ တိုးတက်စေရန် အသုံးပြုပါ။

| ခေါင်းစဉ်/လင့်ခ်                                                                                                                                                                                                            | ဖော်ပြချက်                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Data preparation and analysis for chat model fine-tuning](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | ဤ notebook သည် chat model fine-tuning အတွက် အသုံးပြုမည့် chat dataset ကို ကြိုတင်ပြင်ဆင်ခြင်းနှင့် ခွဲခြမ်းစိတ်ဖြာခြင်းအတွက် ကိရိယာတစ်ခုအဖြစ် အသုံးပြုသည်။ ဖော်မတ်အမှားများစစ်ဆေးပေးပြီး အခြေခံစာရင်းအင်းများနှင့် fine-tuning ကုန်ကျစရိတ်တွက်ချက်ရန် token အရေအတွက် ခန့်မှန်းချက်များ ပေးသည်။ ကြည့်ရှုရန်: [Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)။                                                                                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning for Retrieval Augmented Generation (RAG) with Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | ဤ notebook ၏ ရည်ရွယ်ချက်မှာ Retrieval Augmented Generation (RAG) အတွက် OpenAI မော်ဒယ်များကို fine-tune လုပ်နည်းကို လမ်းညွှန်ပေးခြင်းဖြစ်သည်။ Qdrant နှင့် Few-Shot Learning ကို ပေါင်းစပ်အသုံးပြု၍ မော်ဒယ်စွမ်းဆောင်ရည် မြှင့်တင်ခြင်းနှင့် မှားယွင်းချက်များ လျော့နည်းစေရန် လုပ်ဆောင်ပါမည်။                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT with Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) သည် AI developer များအတွက် မော်ဒယ်လေ့ကျင့်ခြင်း၊ fine-tuning နှင့် foundation မော်ဒယ်များကို အသုံးပြုနိုင်စေရန် ကိရိယာများပါဝင်သော ပလက်ဖောင်းဖြစ်သည်။ ပထမဦးဆုံး [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) လမ်းညွှန်ကို ဖတ်ရှုပြီးနောက် Cookbook လေ့ကျင့်ခန်းကို စမ်းသပ်ပါ။                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning for Small Language Models                                                   | Microsoft ၏ အသစ်ထွက်ရှိလာသော အသေးစားဘာသာစကားမော်ဒယ် Phi-2 ကို တွေ့ဆုံပါ။ အလွန်ထူးခြားပြီး စွမ်းအားကြီးမားသော်လည်း သေးငယ်သည်။ ဤသင်ခန်းစာသည် Phi-2 ကို fine-tune လုပ်နည်း၊ ထူးခြားသော ဒေတာစုံတစ်ခု ဖန်တီးနည်းနှင့် QLoRA အသုံးပြု၍ မော်ဒယ် fine-tune လုပ်နည်းကို လမ်းညွှန်ပေးပါသည်။                                                                                                                                                                       |
| **Hugging Face Tutorial** [How to Fine-Tune LLMs in 2024 with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | ၂၀၂၄ ခုနှစ်တွင် Hugging Face TRL, Transformers နှင့် datasets များကို အသုံးပြု၍ open LLM များကို fine-tune လုပ်နည်းကို ဤဘလော့ဂ်တွင် လမ်းညွှန်ထားသည်။ အသုံးပြုမှုအမျိုးအစား သတ်မှတ်ခြင်း၊ ဖွံ့ဖြိုးရေးပတ်ဝန်းကျင် ပြင်ဆင်ခြင်း၊ ဒေတာစုံ ပြင်ဆင်ခြင်း၊ မော်ဒယ် fine-tune လုပ်ခြင်း၊ စမ်းသပ်သုံးသပ်ခြင်းနှင့် ထုတ်လုပ်မှုသို့ တပ်ဆင်ခြင်းတို့ကို လုပ်ဆောင်ပါသည်။                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | [နောက်ဆုံးပေါ် စက်မှုသင်ယူမှု မော်ဒယ်များ](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst) ကို ပိုမိုလျင်မြန်ပြီး လွယ်ကူစွာ လေ့ကျင့်ခြင်းနှင့် တပ်ဆင်နိုင်စေရန် ဖန်တီးထားသည်။ Repo တွင် Colab အတွက် သင်ခန်းစာများနှင့် YouTube ဗီဒီယိုလမ်းညွှန်များ ပါဝင်ပြီး fine-tuning အတွက် အသုံးပြုနိုင်သည်။ **နောက်ဆုံး [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) အပ်ဒိတ်ကို ထည့်သွင်းထားသည်။** [AutoTrain စာရွက်စာတမ်း](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) ကို ဖတ်ရှုပါ။ |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**အကြောင်းကြားချက်**  
ဤစာတမ်းကို AI ဘာသာပြန်ဝန်ဆောင်မှု [Co-op Translator](https://github.com/Azure/co-op-translator) ဖြင့် ဘာသာပြန်ထားပါသည်။ ကျွန်ုပ်တို့သည် တိကျမှန်ကန်မှုအတွက် ကြိုးစားသော်လည်း အလိုအလျောက် ဘာသာပြန်ခြင်းတွင် အမှားများ သို့မဟုတ် မှားယွင်းချက်များ ပါဝင်နိုင်ကြောင်း သတိပြုပါရန် မေတ္တာရပ်ခံအပ်ပါသည်။ မူရင်းစာတမ်းကို မိမိဘာသာစကားဖြင့်သာ တရားဝင်အချက်အလက်အဖြစ် ယူဆသင့်ပါသည်။ အရေးကြီးသော အချက်အလက်များအတွက် လူ့ဘာသာပြန်ပညာရှင်မှ ဘာသာပြန်ခြင်းကို အကြံပြုပါသည်။ ဤဘာသာပြန်ချက်ကို အသုံးပြုရာမှ ဖြစ်ပေါ်လာနိုင်သည့် နားလည်မှုမှားယွင်းမှုများအတွက် ကျွန်ုပ်တို့သည် တာဝန်မယူပါ။