<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:48:26+00:00",
  "source_file": "README.md",
  "language_code": "my"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.my.png)

### Generative AI အပလီကေးရှင်းတွေ တည်ဆောက်ဖို့ လိုအပ်တဲ့ အရာအားလုံးကို သင်ကြားပေးမယ့် ၂၁ ခန်းစာ

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 ဘာသာစကားစုံကို ထောက်ပံ့မှု

#### GitHub Action ဖြင့် ထောက်ပံ့ထားပြီး (အလိုအလျောက်နဲ့ အမြဲတမ်းအသစ်)

[French](../fr/README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](../ja/README.md) | [Korean](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](../el/README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](../he/README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](../sk/README.md) | [Romanian](../ro/README.md) | [Bulgarian](../bg/README.md) | [Serbian (Cyrillic)](../sr/README.md) | [Croatian](../hr/README.md) | [Slovenian](../sl/README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](./README.md)

# Generative AI for Beginners (ဗားရှင်း ၃) - သင်တန်း

Microsoft Cloud Advocates မှ တင်ဆက်ပေးသည့် ၂၁ ခန်းစာပါဝင်သည့် Generative AI အပလီကေးရှင်းများ တည်ဆောက်ခြင်း အခြေခံများကို သင်ယူလိုက်ပါ။

## 🌱 စတင်ခြင်း

ဒီသင်တန်းမှာ ၂၁ ခန်းစာရှိပါတယ်။ ခန်းစာတိုင်းမှာ ကိုယ်ပိုင်ခေါင်းစဉ်ရှိပြီး သင်ကြိုက်သလို စတင်လေ့လာနိုင်ပါတယ်။

ခန်းစာတွေကို "Learn" ဆိုတဲ့ Generative AI အကြောင်းအရာကို ရှင်းပြတဲ့ခန်းစာတွေ၊ "Build" ဆိုတဲ့ အကြောင်းအရာနဲ့အတူ **Python** နဲ့ **TypeScript** ကို အသုံးပြုပြီး ကုဒ်နမူနာတွေပါဝင်တဲ့ခန်းစာတွေ အဖြစ် ခွဲခြားထားပါတယ်။

.NET Developer တွေအတွက် [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst) ကို ကြည့်ရှုနိုင်ပါတယ်။

ခန်းစာတိုင်းမှာ "Keep Learning" ဆိုတဲ့ အပိုင်းလည်း ပါဝင်ပြီး ပိုမိုလေ့လာနိုင်ဖို့ ကိရိယာတွေပါဝင်ပါတယ်။

## လိုအပ်ချက်များ
### ဒီသင်တန်းရဲ့ ကုဒ်တွေကို run ဖို့ အောက်ပါအရာတွေကို အသုံးပြုနိုင်ပါတယ် - 
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **ခန်းစာများ:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **ခန်းစာများ:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **ခန်းစာများ:** "oai-assignment" 
   
- Python သို့မဟုတ် TypeScript အခြေခံအသိပညာရှိခြင်းက အထောက်အကူဖြစ်ပါတယ် - \*အခြေခံသူတွေအတွက် [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) နဲ့ [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) သင်တန်းတွေကို ကြည့်ရှုနိုင်ပါတယ်
- GitHub အကောင့်တစ်ခုရှိပြီး ဒီ repo အားလုံးကို သင့် GitHub အကောင့်သို့ [fork](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) လုပ်ထားဖို့လိုပါတယ်

ဖွံ့ဖြိုးရေးပတ်ဝန်းကျင်ကို စတင်တပ်ဆင်ဖို့အတွက် **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** ခန်းစာကို ပြုလုပ်ပေးထားပါတယ်။

နောက်ပိုင်းမှာ ရှာဖွေဖို့ လွယ်ကူစေရန် ဒီ repo ကို [ကြယ် (🌟) ပေးဖို့ မမေ့ပါနဲ့](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst)။

## 🧠 ပြင်ဆင်ပြီး အသုံးပြုဖို့ အသင့်ဖြစ်ပြီလား?

ပိုမိုတိုးတက်တဲ့ ကုဒ်နမူနာတွေလိုချင်ရင် ကျွန်တော်တို့ရဲ့ [Generative AI Code Samples စုစည်းမှု](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) ကို **Python** နဲ့ **TypeScript** နှစ်မျိုးလုံးမှာ ကြည့်ရှုနိုင်ပါတယ်။

## 🗣️ အခြားသင်ယူသူတွေနဲ့ တွေ့ဆုံ၊ အထောက်အပံ့ရယူပါ

ဒီသင်တန်းကို လေ့လာနေသူ အခြားသူတွေနဲ့ တွေ့ဆုံဆက်ဆံဖို့နဲ့ အထောက်အပံ့ရဖို့ [Azure AI Foundry Discord server](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) ကို ဝင်ရောက်ပါ။

မေးခွန်းမေးရန် သို့မဟုတ် ထုတ်ကုန်အကြံပြုချက်မျှဝေရန် [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) ကို GitHub ပေါ်မှာ အသုံးပြုနိုင်ပါတယ်။

## 🚀 စတားတပ်တည်ဆောက်နေပါသလား?

[Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) မှာ စာရင်းသွင်းပြီး **အခမဲ့ OpenAI ခရက်ဒစ်များ** နဲ့ **Azure OpenAI Services မှတဆင့် OpenAI မော်ဒယ်များကို အသုံးပြုရန် Azure ခရက်ဒစ် $150,000 အထိ** ရယူနိုင်ပါတယ်။

## 🙏 ကူညီချင်ပါသလား?

အကြံပြုချက်များရှိပါသလား၊ စာလုံးပေါင်းမှားများ သို့မဟုတ် ကုဒ်အမှားတွေတွေ့ရှိပါသလား? [Issue တင်ပါ](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) သို့မဟုတ် [Pull Request တင်ပါ](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 ခန်းစာတိုင်းတွင် ပါဝင်သောအရာများ

- ခေါင်းစဉ်အပေါ် မျက်နှာဖုံး ဗီဒီယိုတို
- README ဖိုင်ထဲရှိ စာသားသင်ခန်းစာ
- Azure OpenAI နဲ့ OpenAI API ကို ထောက်ပံ့တဲ့ Python နဲ့ TypeScript ကုဒ်နမူနာများ
- ပိုမိုလေ့လာနိုင်ဖို့ အပိုဆောင်းအရင်းအမြစ်များ လင့်ခ်များ

## 🗃️ ခန်းစာများ

| #   | **ခန်းစာလင့်ခ်**                                                                                                                              | **ဖော်ပြချက်**                                                                                 | **ဗီဒီယို**                                                                   | **အပိုဆောင်းလေ့လာမှု**                                                             |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **သင်ယူရန်:** ဖွံ့ဖြိုးရေးပတ်ဝန်းကျင်ကို ဘယ်လိုတပ်ဆင်ရမလဲဆိုတာ                                      | ဗီဒီယို မကြာမီထွက်ပါမည်                                                                 | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Introduction to Generative AI and LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                              | **သင်ယူရန်:** Generative AI ဆိုတာဘာလဲ၊ Large Language Models (LLMs) များ ဘယ်လိုအလုပ်လုပ်ကြတာလဲ။       | [ဗီဒီယို](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Exploring and comparing different LLMs](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)             | **သင်ယူရန်:** သင့်အသုံးပြုမှုအတွက် သင့်တော်တဲ့ မော်ဒယ်ကို ဘယ်လိုရွေးချယ်ရမလဲ                          | [ဗီဒီယို](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Using Generative AI Responsibly](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                           | **သင်ယူရန်:** Generative AI အပလီကေးရှင်းတွေကို တာဝန်ရှိရှိ တည်ဆောက်နည်း                          | [ဗီဒီယို](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Understanding Prompt Engineering Fundamentals](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)             | **သင်ယူရန်:** Prompt Engineering အခြေခံနည်းလမ်းများကို လက်တွေ့ကျကျ လေ့လာခြင်း                         | [ဗီဒီယို](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Creating Advanced Prompts](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **သင်ယူရန်:** Prompt များရဲ့ ရလဒ်ကို တိုးတက်စေဖို့ Prompt Engineering နည်းလမ်းတွေကို ဘယ်လိုအသုံးချမလဲ။ | [ဗီဒီယို](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [စာသားဖန်တီးမှု အက်ပလီကေးရှင်းများ တည်ဆောက်ခြင်း](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **တည်ဆောက်ရန်:** Azure OpenAI / OpenAI API ကို အသုံးပြု၍ စာသားဖန်တီးမှု အက်ပလီကေးရှင်း တစ်ခု တည်ဆောက်ခြင်း                                | [ဗီဒီယို](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [စကားပြော အက်ပလီကေးရှင်းများ တည်ဆောက်ခြင်း](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **တည်ဆောက်ရန်:** စကားပြော အက်ပလီကေးရှင်းများကို ထိရောက်စွာ တည်ဆောက်ခြင်းနှင့် ပေါင်းစပ်ခြင်းနည်းလမ်းများ               | [ဗီဒီယို](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [ရှာဖွေရေး အက်ပလီကေးရှင်းများနှင့် Vector ဒေတာဘေ့စ်များ တည်ဆောက်ခြင်း](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **တည်ဆောက်ရန်:** Embeddings ကို အသုံးပြု၍ ဒေတာရှာဖွေရေး အက်ပလီကေးရှင်း တစ်ခု တည်ဆောက်ခြင်း                        | [ဗီဒီယို](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [ပုံဖန်တီးမှု အက်ပလီကေးရှင်းများ တည်ဆောက်ခြင်း](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **တည်ဆောက်ရန်:** ပုံဖန်တီးမှု အက်ပလီကေးရှင်း တစ်ခု                                                       | [ဗီဒီယို](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Low Code AI အက်ပလီကေးရှင်းများ တည်ဆောက်ခြင်း](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **တည်ဆောက်ရန်:** Low Code ကိရိယာများကို အသုံးပြု၍ Generative AI အက်ပလီကေးရှင်း တစ်ခု တည်ဆောက်ခြင်း                                     | [ဗီဒီယို](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Function Calling ဖြင့် ပြင်ပ အက်ပလီကေးရှင်းများ ပေါင်းစပ်ခြင်း](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **တည်ဆောက်ရန်:** function calling ဆိုတာဘာလဲ၊ အက်ပလီကေးရှင်းများတွင် ဘယ်လိုအသုံးပြုကြသည်ဆိုတာ                          | [ဗီဒီယို](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [AI အက်ပလီကေးရှင်းများအတွက် UX ဒီဇိုင်း ရေးဆွဲခြင်း](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **သင်ယူရန်:** Generative AI အက်ပလီကေးရှင်းများ ဖန်တီးရာတွင် UX ဒီဇိုင်း 원칙များကို ဘယ်လိုအသုံးချရမလဲဆိုတာ         | [ဗီဒီယို](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [သင့် Generative AI အက်ပလီကေးရှင်းများကို လုံခြုံစေခြင်း](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **သင်ယူရန်:** AI စနစ်များကို ခြိမ်းခြောက်မှုများနှင့် အန္တရာယ်များ၊ ထိုစနစ်များကို လုံခြုံစေသည့် နည်းလမ်းများ             | [ဗီဒီယို](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Generative AI အက်ပလီကေးရှင်း၏ အသက်တာစက်ဝန်း](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **သင်ယူရန်:** LLM အသက်တာစက်ဝန်းနှင့် LLMOps ကို စီမံခန့်ခွဲရန် ကိရိယာများနှင့် တိုင်းတာချက်များ                         | [ဗီဒီယို](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) နှင့် Vector ဒေတာဘေ့စ်များ](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **တည်ဆောက်ရန်:** RAG Framework ကို အသုံးပြု၍ Vector ဒေတာဘေ့စ်များမှ embeddings များကို ရယူသုံးစွဲသည့် အက်ပလီကေးရှင်း  | [ဗီဒီယို](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Open Source မော်ဒယ်များနှင့် Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **တည်ဆောက်ရန်:** Hugging Face တွင် ရရှိနိုင်သည့် open source မော်ဒယ်များကို အသုံးပြု၍ အက်ပလီကေးရှင်း တစ်ခု တည်ဆောက်ခြင်း                    | [ဗီဒီယို](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI Agents](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **တည်ဆောက်ရန်:** AI Agent Framework ကို အသုံးပြု၍ အက်ပလီကေးရှင်း တစ်ခု တည်ဆောက်ခြင်း                                           | [ဗီဒီယို](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [LLMs ကို Fine-Tuning ပြုလုပ်ခြင်း](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **သင်ယူရန်:** LLMs ကို fine-tuning ပြုလုပ်ခြင်း၏ အကြောင်းအရာ၊ အကြောင်းရင်းနှင့် နည်းလမ်းများ                                            | [ဗီဒီယို](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [SLMs ဖြင့် တည်ဆောက်ခြင်း](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **သင်ယူရန်:** Small Language Models ဖြင့် တည်ဆောက်ခြင်း၏ အကျိုးကျေးဇူးများ                                            | ဗီဒီယို မကြာမီ ရရှိပါမည် | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Mistral မော်ဒယ်များဖြင့် တည်ဆောက်ခြင်း](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **သင်ယူရန်:** Mistral မော်ဒယ် မိသားစု၏ လက္ခဏာများနှင့် ကွာခြားချက်များ                                           | ဗီဒီယို မကြာမီ ရရှိပါမည် | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Meta မော်ဒယ်များဖြင့် တည်ဆောက်ခြင်း](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **သင်ယူရန်:** Meta မော်ဒယ် မိသားစု၏ လက္ခဏာများနှင့် ကွာခြားချက်များ                                           | ဗီဒီယို မကြာမီ ရရှိပါမည် | [ပိုမိုသိရှိရန်](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 အထူးကျေးဇူးတင်ရှိပါသည်

GitHub Actions နှင့် workflows အားလုံးကို ဖန်တီးပေးသော [**John Aziz**](https://www.linkedin.com/in/john0isaac/) ကို အထူးကျေးဇူးတင်ရှိပါသည်။

သင်ယူသူနှင့် ကုဒ်အတွေ့အကြုံ တိုးတက်စေရန် သင်ခန်းစာတိုင်းတွင် အဓိက အထောက်အကူပြုခဲ့သော [**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) ကိုလည်း ကျေးဇူးအထူးတင်ရှိပါသည်။

## 🎒 အခြားသင်တန်းများ

ကျွန်ုပ်တို့အဖွဲ့သည် အခြားသင်တန်းများကိုလည်း ထုတ်လုပ်ပါသည်။ စစ်ဆေးကြည့်ပါ-

- [**အသစ်** စတင်သူများအတွက် Model Context Protocol](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [စတင်သူများအတွက် AI Agents](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [.NET ဖြင့် စတင်သူများအတွက် Generative AI](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [JavaScript ဖြင့် စတင်သူများအတွက် Generative AI](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [စတင်သူများအတွက် ML](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [စတင်သူများအတွက် ဒေတာသိပ္ပံ](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [စတင်သူများအတွက် AI](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [စတင်သူများအတွက် Cybersecurity](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [စတင်သူများအတွက် ဝက်ဘ်ဖွံ့ဖြိုးတိုးတက်မှု](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [စတင်သူများအတွက် IoT](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [စတင်သူများအတွက် XR ဖွံ့ဖြိုးတိုးတက်မှု](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI နှင့် တွဲဖက်ရေးသားခြင်းအတွက် GitHub Copilot ကျွမ်းကျင်မှု](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [C#/.NET Developer များအတွက် GitHub Copilot ကျွမ်းကျင်မှု](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [သင့်ကိုယ်ပိုင် Copilot စွန့်စားခန်း ရွေးချယ်ပါ](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**အကြောင်းကြားချက်**  
ဤစာတမ်းကို AI ဘာသာပြန်ဝန်ဆောင်မှု [Co-op Translator](https://github.com/Azure/co-op-translator) ဖြင့် ဘာသာပြန်ထားပါသည်။ ကျွန်ုပ်တို့သည် တိကျမှန်ကန်မှုအတွက် ကြိုးစားသော်လည်း အလိုအလျောက် ဘာသာပြန်ခြင်းတွင် အမှားများ သို့မဟုတ် မှားယွင်းချက်များ ပါဝင်နိုင်ကြောင်း သတိပြုပါရန် မေတ္တာရပ်ခံအပ်ပါသည်။ မူရင်းစာတမ်းကို မိမိဘာသာစကားဖြင့်သာ တရားဝင်အချက်အလက်အဖြစ် ယူဆသင့်ပါသည်။ အရေးကြီးသော အချက်အလက်များအတွက် လူ့ဘာသာပြန်ပညာရှင်မှ ဘာသာပြန်ခြင်းကို အကြံပြုပါသည်။ ဤဘာသာပြန်ချက်ကို အသုံးပြုရာမှ ဖြစ်ပေါ်လာနိုင်သည့် နားလည်မှုမှားယွင်းမှုများအတွက် ကျွန်ုပ်တို့သည် တာဝန်မယူပါ။