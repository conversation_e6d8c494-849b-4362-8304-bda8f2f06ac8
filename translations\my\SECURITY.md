<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:56:51+00:00",
  "source_file": "SECURITY.md",
  "language_code": "my"
}
-->
## လုံခြုံရေး

Microsoft သည် ကျွန်ုပ်တို့၏ ဆော့ဖ်ဝဲထုတ်ကုန်များနှင့် ဝန်ဆောင်မှုများ၏ လုံခြုံရေးကို အလေးထားပါသည်၊ ၎င်းတွင် ကျွန်ုပ်တို့ GitHub အဖွဲ့အစည်းများမှ စီမံခန့်ခွဲသော အရင်းအမြစ်ကုဒ်သိုလှောင်ရာများအားလုံး ပါဝင်သည်။ ၎င်းတွင် [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) နှင့် [ကျွန်ုပ်တို့၏ GitHub အဖွဲ့အစည်းများ](https://opensource.microsoft.com/) ပါဝင်သည်။

Microsoft ပိုင်ဆိုင်သော repository များတွင် [Microsoft ၏ လုံခြုံရေးအားနည်းချက် အဓိပ္ပါယ်](https://aka.ms/opensource/security/definition) နှင့် ကိုက်ညီသော လုံခြုံရေးအားနည်းချက်တစ်ခုကို တွေ့ရှိခဲ့သည်ဟု ယူဆပါက အောက်ပါအတိုင်း ကျွန်ုပ်တို့ထံ အစီရင်ခံပေးပါ။

## လုံခြုံရေးပြဿနာများ အစီရင်ခံခြင်း

**လုံခြုံရေးအားနည်းချက်များကို အများပြည်သူ GitHub issues မှတဆင့် မအစီရင်ခံပါနှင့်။**

အစား Microsoft Security Response Center (MSRC) သို့ [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report) မှတဆင့် အစီရင်ခံပေးပါ။

Login မလိုအပ်ဘဲ တင်သွင်းလိုပါက [<EMAIL>](mailto:<EMAIL>) သို့ အီးမေးလ်ပို့ပါ။ ဖြစ်နိုင်ပါက ကျွန်ုပ်တို့၏ PGP key ဖြင့် သင့်စာကို စာလုံခြုံစွာ စာလုံးကူးပေးပါ။ ၎င်းကို [Microsoft Security Response Center PGP Key စာမျက်နှာ](https://aka.ms/opensource/security/pgpkey) မှ ဒေါင်းလုပ်ဆွဲနိုင်ပါသည်။

၂၄ နာရီအတွင်း တုံ့ပြန်ချက်ရရှိမည်ဖြစ်သည်။ မရရှိပါက ကျွန်ုပ်တို့ထံ သင့်မူရင်းစာကို လက်ခံရရှိကြောင်း အတည်ပြုရန် အီးမေးလ်ဖြင့် ဆက်သွယ်ပါ။ နောက်ထပ်အချက်အလက်များကို [microsoft.com/msrc](https://aka.ms/opensource/security/msrc) တွင် ရှာဖွေနိုင်ပါသည်။

အောက်ပါ အချက်အလက်များကို (ရနိုင်သမျှ) ထည့်သွင်းပေးပါက ပြဿနာ၏ သဘောတရားနှင့် အကျယ်အဝန်းကို ပိုမိုနားလည်နိုင်ရန် ကူညီပါမည်-

  * ပြဿနာအမျိုးအစား (ဥပမာ buffer overflow, SQL injection, cross-site scripting စသည်)
  * ပြဿနာဖြစ်ပေါ်မှုနှင့် ဆက်စပ်သော အရင်းအမြစ်ဖိုင်(များ)၏ လမ်းကြောင်းအပြည့်အစုံ
  * ထိခိုက်သော အရင်းအမြစ်ကုဒ်တည်နေရာ (tag/branch/commit သို့မဟုတ် တိုက်ရိုက် URL)
  * ပြဿနာကို ထပ်မံဖြစ်ပေါ်စေရန် လိုအပ်သော အထူးပြင်ဆင်မှုများ
  * ပြဿနာကို ထပ်မံဖြစ်ပေါ်စေရန် အဆင့်ဆင့် လမ်းညွှန်ချက်များ
  * သက်သေပြသည့် အကြံပြုချက် သို့မဟုတ် exploit ကုဒ် (ဖြစ်နိုင်ပါက)
  * ပြဿနာ၏ သက်ရောက်မှု၊ အပြစ်တင်သူတစ်ဦးက ပြဿနာကို မည်သို့ အသုံးချနိုင်သည်ကိုပါ

ဤအချက်အလက်များသည် သင့်အစီရင်ခံစာကို ပိုမိုမြန်ဆန်စွာ စစ်ဆေးနိုင်ရန် ကူညီပါမည်။

Bug bounty အတွက် အစီရင်ခံပါက ပိုမိုပြည့်စုံသောအစီရင်ခံစာများသည် ပိုမိုမြင့်မားသော ဆုကြေးငွေ ရရှိစေနိုင်ပါသည်။ ကျွန်ုပ်တို့၏ [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) စာမျက်နှာတွင် လက်ရှိ အစီအစဉ်များအကြောင်း အသေးစိတ် ရှာဖွေနိုင်ပါသည်။

## နှစ်သက်သော ဘာသာစကားများ

ကျွန်ုပ်တို့သည် ဆက်သွယ်မှုအားလုံးကို အင်္ဂလိပ်ဘာသာဖြင့် ပြုလုပ်ရန် နှစ်သက်ပါသည်။

## မူဝါဒ

Microsoft သည် [Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd) 원칙ကို လိုက်နာပါသည်။

**အကြောင်းကြားချက်**  
ဤစာတမ်းကို AI ဘာသာပြန်ဝန်ဆောင်မှု [Co-op Translator](https://github.com/Azure/co-op-translator) ဖြင့် ဘာသာပြန်ထားပါသည်။ ကျွန်ုပ်တို့သည် တိကျမှန်ကန်မှုအတွက် ကြိုးစားသော်လည်း အလိုအလျောက် ဘာသာပြန်ခြင်းတွင် အမှားများ သို့မဟုတ် မှားယွင်းချက်များ ပါဝင်နိုင်ကြောင်း သတိပြုပါရန် မေတ္တာရပ်ခံအပ်ပါသည်။ မူရင်းစာတမ်းကို မိမိဘာသာစကားဖြင့်သာ တရားဝင်အရင်းအမြစ်အဖြစ် ယူဆသင့်ပါသည်။ အရေးကြီးသော အချက်အလက်များအတွက် လူ့ဘာသာပြန်ပညာရှင်မှ ဘာသာပြန်ခြင်းကို အကြံပြုပါသည်။ ဤဘာသာပြန်ချက်ကို အသုံးပြုရာမှ ဖြစ်ပေါ်လာနိုင်သည့် နားလည်မှုမှားယွင်းမှုများအတွက် ကျွန်ုပ်တို့ တာဝန်မယူပါ။