<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "f53ba0fa49164f9323043f1c6b11f2b1",
  "translation_date": "2025-07-09T07:48:35+00:00",
  "source_file": "01-introduction-to-genai/README.md",
  "language_code": "ne"
}
-->
# जेनेरेटिभ एआई र ठूला भाषा मोडेलहरूको परिचय

[![जेनेरेटिभ एआई र ठूला भाषा मोडेलहरूको परिचय](../../../translated_images/01-lesson-banner.2424cfd092f43366707ee2d15749f62f76f80ea3cb0816f4f31d0abd5ffd4dd1.ne.png)](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst)

_(यो पाठको भिडियो हेर्न माथिको तस्बिरमा क्लिक गर्नुहोस्)_

जेनेरेटिभ एआई भनेको यस्तो कृत्रिम बुद्धिमत्ता हो जसले पाठ, तस्बिर र अन्य प्रकारका सामग्रीहरू सिर्जना गर्न सक्छ। यसलाई अद्भुत प्रविधि बनाउने कुरा के हो भने यसले एआईलाई सबैका लागि सजिलो बनाउँछ, जसलाई प्रयोग गर्नका लागि केवल एउटा पाठ संकेत, अर्थात् प्राकृतिक भाषामा लेखिएको एउटा वाक्य पर्याप्त हुन्छ। तपाईंलाई Java वा SQL जस्ता भाषा सिक्न आवश्यक छैन, तपाईंले आफ्नो भाषा प्रयोग गरेर के चाहनुहुन्छ भन्नुहोस् र एआई मोडेलबाट सुझाव प्राप्त गर्नुहोस्। यसको प्रयोग र प्रभाव विशाल छ, तपाईं रिपोर्ट लेख्न वा बुझ्न, आवेदनहरू तयार पार्न र धेरै कामहरू सेकेन्डमै गर्न सक्नुहुन्छ।

यस पाठ्यक्रममा, हामी हाम्रो स्टार्टअपले कसरी जेनेरेटिभ एआईलाई शिक्षा क्षेत्रमा नयाँ सम्भावनाहरू खोल्न प्रयोग गरिरहेको छ र यसको सामाजिक प्रभाव र प्रविधि सीमाहरूका चुनौतीहरू कसरी समाधान गरिरहेको छ भन्ने कुरा अन्वेषण गर्नेछौं।

## परिचय

यस पाठले समेट्ने विषयहरू:

- व्यवसायिक परिदृश्यको परिचय: हाम्रो स्टार्टअपको विचार र मिशन।
- जेनेरेटिभ एआई र हालको प्रविधि परिदृश्यमा हाम्रो यात्रा।
- ठूला भाषा मोडेलको भित्री कार्यप्रणाली।
- ठूला भाषा मोडेलहरूको मुख्य क्षमता र व्यावहारिक प्रयोगहरू।

## सिकाइका लक्ष्यहरू

यस पाठ पूरा गरेपछि तपाईं बुझ्ने हुनुहुनेछ:

- जेनेरेटिभ एआई के हो र ठूला भाषा मोडेलहरू कसरी काम गर्छन्।
- ठूला भाषा मोडेलहरूलाई विभिन्न प्रयोगका लागि कसरी उपयोग गर्न सकिन्छ, विशेष गरी शिक्षा सम्बन्धी परिदृश्यमा।

## परिदृश्य: हाम्रो शैक्षिक स्टार्टअप

जेनेरेटिभ कृत्रिम बुद्धिमत्ता (एआई) एआई प्रविधिको शिखर हो, जसले पहिले असम्भव ठानेका कुराहरूलाई सम्भव बनाउँदैछ। जेनेरेटिभ एआई मोडेलहरूका धेरै क्षमता र प्रयोगहरू छन्, तर यस पाठ्यक्रममा हामी यसले शिक्षा क्षेत्रमा कसरी क्रान्ति ल्याइरहेको छ भन्ने कुरा काल्पनिक स्टार्टअप मार्फत अन्वेषण गर्नेछौं। हामी यस स्टार्टअपलाई _हाम्रो स्टार्टअप_ भनी सम्बोधन गर्नेछौं। हाम्रो स्टार्टअप शिक्षा क्षेत्रमा काम गर्छ र यसको महत्वाकांक्षी मिशन छ:

> _शिक्षामा पहुँच सुधार गर्दै, विश्वव्यापी स्तरमा समान पहुँच सुनिश्चित गर्दै र प्रत्येक सिक्ने व्यक्तिको आवश्यकताअनुसार व्यक्तिगत सिकाइ अनुभव प्रदान गर्ने_।

हाम्रो स्टार्टअप टोलीलाई थाहा छ कि यो लक्ष्य प्राप्त गर्न हामीले आधुनिक समयका सबैभन्दा शक्तिशाली उपकरणहरू मध्ये एक – ठूला भाषा मोडेलहरू (LLMs) – को उपयोग गर्नैपर्नेछ।

जेनेरेटिभ एआईले आजको सिकाइ र शिक्षण तरिकामा क्रान्ति ल्याउने अपेक्षा गरिएको छ, जहाँ विद्यार्थीहरूलाई २४ घण्टा भर्चुअल शिक्षक उपलब्ध हुन्छन् जसले विशाल मात्रामा जानकारी र उदाहरणहरू प्रदान गर्छन्, र शिक्षकहरूले आफ्ना विद्यार्थीहरूको मूल्याङ्कन र प्रतिक्रिया दिन नवीन उपकरणहरू प्रयोग गर्न सक्छन्।

![पाँच जना युवा विद्यार्थी मोनिटर हेर्दै - DALLE2 द्वारा तस्बिर](../../../translated_images/students-by-DALLE2.b70fddaced1042ee47092320243050c4c9a7da78b31eeba515b09b2f0dca009b.ne.png)

सुरुमा, हामी केही आधारभूत अवधारणा र शब्दावली परिभाषित गरौं जुन हामी सम्पूर्ण पाठ्यक्रममा प्रयोग गर्नेछौं।

## हामीले जेनेरेटिभ एआई कसरी पाएँ?

हालै जेनेरेटिभ एआई मोडेलहरूको घोषणा पछि भएको अत्यधिक _हाइप_ को बावजुद, यो प्रविधि दशकौंदेखि विकास हुँदै आएको हो, जसको पहिलो अनुसन्धान प्रयास ६० को दशकसम्म पुग्छ। अहिले हामी यस्तो एआईको युगमा छौं जसले मानव संज्ञानात्मक क्षमता जस्तै संवाद गर्न सक्छ, जस्तै [OpenAI ChatGPT](https://openai.com/chatgpt) वा [Bing Chat](https://www.microsoft.com/edge/features/bing-chat?WT.mc_id=academic-105485-koreyst) जसले वेब खोजका लागि GPT मोडेल प्रयोग गर्छ।

थोरै पछाडि फर्केर हेर्दा, एआईका पहिलो प्रोटोटाइपहरू टाइप गरिएको च्याटबोटहरू थिए, जसले विशेषज्ञहरूको ज्ञान आधारमा काम गर्थे र इनपुट पाठमा देखिएका कुञ्जीशब्दहरूद्वारा जवाफ दिन्थे। तर चाँडै यो स्पष्ट भयो कि यस्तो तरिका राम्रोसँग विस्तार गर्न सकिँदैन।

### एआईमा सांख्यिकीय दृष्टिकोण: मेशिन लर्निङ

९० को दशकमा, पाठ विश्लेषणमा सांख्यिकीय दृष्टिकोण लागू भएपछि नयाँ एल्गोरिदमहरू विकास भए – जसलाई मेशिन लर्निङ भनिन्छ – जसले स्पष्ट रूपमा प्रोग्राम नगरिकनै डाटाबाट ढाँचाहरू सिक्न सक्छ। यसले मेसिनलाई मानव भाषाको बुझाइ नक्कल गर्न सक्षम बनायो: एउटा सांख्यिकीय मोडेल पाठ-लेबल जोडीहरूमा तालिम दिइन्छ, जसले अज्ञात इनपुट पाठलाई पूर्वनिर्धारित लेबलमा वर्गीकरण गर्न सक्षम बनाउँछ।

### न्यूरल नेटवर्क र आधुनिक भर्चुअल सहायकहरू

हालका वर्षहरूमा, ठूलो मात्रामा डाटा र जटिल गणनाहरू गर्न सक्ने हार्डवेयरको विकासले एआई अनुसन्धानलाई प्रोत्साहित गर्यो, जसले न्यूरल नेटवर्क वा डीप लर्निङ एल्गोरिदमहरू विकास गरायो।

न्यूरल नेटवर्कहरू (विशेष गरी Recurrent Neural Networks – RNNs) ले प्राकृतिक भाषा प्रशोधनलाई धेरै सुधार गर्यो, जसले वाक्यभित्र शब्दको सन्दर्भलाई महत्त्व दिन सक्षम बनायो।

यो प्रविधिले नयाँ शताब्दीको पहिलो दशकमा जन्मिएका भर्चुअल सहायकहरूलाई शक्ति दियो, जसले मानव भाषालाई राम्रोसँग व्याख्या गर्न, आवश्यकतालाई चिन्हित गर्न र त्यसलाई पूरा गर्न कार्य गर्न सक्षम थिए – जस्तै पूर्वनिर्धारित स्क्रिप्ट अनुसार जवाफ दिन वा तेस्रो पक्षको सेवा प्रयोग गर्न।

### वर्तमान समय, जेनेरेटिभ एआई

यसरी हामी आजको जेनेरेटिभ एआईसम्म आइपुगेका हौं, जुन डीप लर्निङको एउटा उपश्रेणी हो।

![एआई, एमएल, डीएल र जेनेरेटिभ एआई](../../../translated_images/AI-diagram.c391fa518451a40de58d4f792c88adb8568d8cb4c48eed6e97b6b16e621eeb77.ne.png)

एआई क्षेत्रमा दशकौंको अनुसन्धानपछि, नयाँ मोडेल संरचना – जसलाई _Transformer_ भनिन्छ – ले RNN को सीमाहरू पार गर्यो, जसले धेरै लामो पाठ अनुक्रमहरू इनपुटको रूपमा लिन सक्छ। Transformer हरू ध्यान (attention) मेकानिजममा आधारित छन्, जसले मोडेललाई प्राप्त इनपुटहरूलाई फरक-फरक तौल दिन सक्षम बनाउँछ, जहाँ सबैभन्दा सान्दर्भिक जानकारी केन्द्रित हुन्छ त्यहाँ ‘ध्यान दिन्छ’, पाठ अनुक्रममा तिनीहरूको क्रम जस्तो भए पनि।

अधिकांश हालका जेनेरेटिभ एआई मोडेलहरू – जसलाई ठूला भाषा मोडेलहरू (LLMs) पनि भनिन्छ, किनभने तिनीहरू पाठ इनपुट र आउटपुटसँग काम गर्छन् – वास्तवमा यस संरचनामा आधारित छन्। यी मोडेलहरू – जुन पुस्तक, लेख र वेबसाइटहरू जस्ता विविध स्रोतहरूबाट ठूलो मात्रामा लेबलरहित डाटामा तालिम दिइएका छन् – विभिन्न कार्यहरूमा अनुकूलन गर्न सकिन्छ र व्याकरणिक रूपमा सही र सृजनात्मक जस्तो देखिने पाठ उत्पन्न गर्न सक्छन्। यसले मेसिनलाई इनपुट पाठ ‘बुझ्न’ मात्र होइन, मानव भाषामा मौलिक प्रतिक्रिया उत्पन्न गर्न पनि सक्षम बनायो।

## ठूला भाषा मोडेलहरू कसरी काम गर्छन्?

अर्को अध्यायमा हामी विभिन्न प्रकारका जेनेरेटिभ एआई मोडेलहरू अन्वेषण गर्नेछौं, तर अहिले हामी ठूला भाषा मोडेलहरू कसरी काम गर्छन् भन्ने कुरा, विशेष गरी OpenAI GPT (Generative Pre-trained Transformer) मोडेलहरूमा केन्द्रित भएर हेर्नेछौं।

- **Tokenizer, पाठलाई संख्यामा रूपान्तरण**: ठूला भाषा मोडेलहरूले पाठलाई इनपुटको रूपमा लिन्छन् र पाठलाई आउटपुटको रूपमा दिन्छन्। तर, सांख्यिकीय मोडेल भएकाले, तिनीहरूलाई पाठ अनुक्रमभन्दा संख्याहरूले राम्रो काम गर्छ। त्यसैले मोडेलमा पठाइएको प्रत्येक इनपुटलाई पहिले tokenizer ले प्रक्रिया गर्छ। टोकन भनेको पाठको एउटा टुक्रा हो – जसमा चरित्रहरूको संख्या फरक हुन सक्छ, त्यसैले tokenizer को मुख्य काम इनपुटलाई टोकनहरूको एरेमा विभाजन गर्नु हो। त्यसपछि प्रत्येक टोकनलाई टोकन इन्डेक्ससँग मिलाइन्छ, जुन मूल पाठ टुक्राको पूर्णांक कोडिङ हो।

![टोकनाइजेशनको उदाहरण](../../../translated_images/tokenizer-example.80a5c151ee7d1bd485eff5aca60ac3d2c1eaaff4c0746e09b98c696c959afbfa.ne.png)

- **आउटपुट टोकनको पूर्वानुमान**: n टोकनहरू इनपुटको रूपमा दिइएपछि (अधिकतम n मोडेल अनुसार फरक हुन्छ), मोडेलले एउटा टोकन आउटपुटको रूपमा पूर्वानुमान गर्न सक्छ। यो टोकन अर्को पुनरावृत्तिको इनपुटमा समावेश गरिन्छ, विस्तार हुँदै जाने विन्डो ढाँचामा, जसले प्रयोगकर्तालाई एक वा धेरै वाक्यको जवाफ प्राप्त गर्न सहज बनाउँछ। यसैले, यदि तपाईंले कहिल्यै ChatGPT सँग खेल्नुभएको छ भने कहिलेकाहीं यो वाक्यको बीचमा रोकिएको जस्तो देखिन सक्छ।

- **चयन प्रक्रिया, सम्भाव्यता वितरण**: आउटपुट टोकन मोडेलले हालको पाठ अनुक्रमपछि आउने सम्भावनाको आधारमा चयन गर्छ। मोडेलले सबै सम्भावित ‘अर्को टोकन’ को सम्भाव्यता वितरण पूर्वानुमान गर्छ। तर सधैं सबैभन्दा उच्च सम्भाव्यता भएको टोकन चयन हुँदैन। यसमा केही अनियमितता थपिन्छ, जसले मोडेललाई गैर-नियतात्मक व्यवहार गर्न प्रेरित गर्छ – एउटै इनपुटका लागि सधैं एउटै आउटपुट नआओस्। यो अनियमितता सिर्जनात्मक सोचको प्रक्रिया नक्कल गर्न थपिन्छ र यसलाई temperature नामक मोडेल प्यारामिटरले नियन्त्रण गर्न सकिन्छ।

## हाम्रो स्टार्टअपले ठूला भाषा मोडेलहरू कसरी उपयोग गर्न सक्छ?

अब हामी ठूला भाषा मोडेलको भित्री कार्यप्रणाली राम्रोसँग बुझिसकेका छौं, आउनुहोस् केही व्यावहारिक उदाहरणहरू हेरौं जुन यी मोडेलहरूले राम्ररी गर्न सक्छन्, हाम्रो व्यवसायिक परिदृश्यलाई ध्यानमा राख्दै।

हामीले भनेका छौं कि ठूला भाषा मोडेलको मुख्य क्षमता भनेको _प्राकृतिक भाषामा लेखिएको पाठ इनपुटबाट नयाँ पाठ सिर्जना गर्नु हो_।

तर कस्तो प्रकारको पाठ इनपुट र आउटपुट?
ठूला भाषा मोडेलको इनपुटलाई prompt भनिन्छ, र आउटपुटलाई completion भनिन्छ, जुन मोडेलले हालको इनपुट पूरा गर्न अर्को टोकन सिर्जना गर्ने प्रक्रिया हो। हामी prompt के हो र यसलाई कसरी डिजाइन गर्ने भन्ने विषयमा गहिराइमा जानेछौं। अहिलेका लागि, prompt मा समावेश हुन सक्छ:

- एउटा **निर्देशन** जसले मोडेलबाट अपेक्षित आउटपुटको प्रकार बताउँछ। कहिलेकाहीं यस निर्देशनमा केही उदाहरण वा अतिरिक्त डाटा पनि समावेश हुन सक्छ।

  1. लेख, पुस्तक, उत्पादन समीक्षा आदि को सारांश बनाउने र असंरचित डाटाबाट जानकारी निकाल्ने।
    
    ![सारांश बनाउने उदाहरण](../../../translated_images/summarization-example.7b7ff97147b3d790477169f442b5e3f8f78079f152450e62c45dbdc23b1423c1.ne.png)
  
  2. रचनात्मक विचार र लेख, निबन्ध, असाइनमेन्ट आदि को डिजाइन।
      
     ![रचनात्मक लेखनको उदाहरण](../../../translated_images/creative-writing-example.e24a685b5a543ad1287ad8f6c963019518920e92a1cf7510f354e85b0830fbe8.ne.png)

- एउटा **प्रश्न**, जुन एजेन्टसँग संवादको रूपमा सोधिन्छ।
  
  ![संवादको उदाहरण](../../../translated_images/conversation-example.60c2afc0f595fa599f367d36ccc3909ffc15e1d5265cb33b907d3560f3d03116.ne.png)

- लेख्न सहयोगको लागि एउटा **पाठको टुक्रा पूरा गर्ने** अनुरोध।
  
  ![पाठ पूरा गर्ने उदाहरण](../../../translated_images/text-completion-example.cbb0f28403d427524f8f8c935f84d084a9765b683a6bf37f977df3adb868b0e7.ne.png)

- कोडको टुक्रा र त्यसलाई व्याख्या र दस्तावेजीकरण गर्ने, वा कुनै विशेष कार्य गर्ने कोड सिर्जना गर्न अनुरोध गर्ने टिप्पणी।
  
  ![कोडिङको उदाहरण](../../../translated_images/coding-example.50ebabe8a6afff20267c91f18aab1957ddd9561ee2988b2362b7365aa6796935.ne.png)

माथिका उदाहरणहरू सरल छन् र ठूला भाषा मोडेलहरूको क्षमताको पूर्ण प्रदर्शन होइनन्। यी उदाहरणहरूले विशेष गरी शिक्षा सम्बन्धी सन्दर्भमा जेनेरेटिभ एआईको सम्भावना देखाउन खोजेका हुन्।

त्यसैगरी, जेनेरेटिभ एआई मोडेलको आउटपुट पूर्ण छैन र कहिलेकाहीं यसको सृजनात्मकता यसका विरुद्ध काम गर्न सक्छ, जसले मानव प्रयोगकर्ताले वास्तविकताको भ्रम वा अपमानजनक सामग्रीको रूपमा व्याख्या गर्न सक्ने परिणाम दिन सक्छ। जेनेरेटिभ एआई बुद्धिमान छैन – कम्तिमा व्यापक अर्थमा बुद्धिमत्ता, जसमा आलोचनात्मक र रचनात्मक तर्क वा भावनात्मक बुद्धिमत्ता समावेश छ, यो deterministic छैन र भरोसायोग्य पनि होइन, किनभने गलत सन्दर्भ, सामग्री र कथनहरू सही जानकारीसँग मिसाएर विश्वसनीय र आत्मविश्वासी तरिकाले प्रस्तुत गर्न सक्छ। आगामी पाठहरूमा हामी यी सबै सीमाहरूको सामना गर्नेछौं र तिनीहरूलाई कसरी कम गर्ने भन्ने कुरा सिक्नेछौं।

## कार्य

तपाईंको कार्य हो [जेनेरेटिभ एआई](https://en.wikipedia.org/wiki/Generative_artificial_intelligence?WT.mc_id=academic-105485-koreyst) बारे थप पढ्नु र यस्तो क्षेत्र पहिचान गर्नु जहाँ आज जेनेरेटिभ एआई छैन तर तपाईं थप्न चाहनुहुन्छ। पुरानो तरिकाबाट गर्दा प्रभाव कस्तो हुन्थ्यो, के तपाईंले पहिले गर्न नसकेको केही गर्न सक्नुहुन्छ वा छिटो गर्न सक्नुहुन्छ? आफ्नो सपनाको एआई स्टार्टअप कस्तो देखिन्छ भन्ने विषयमा ३०० शब्दको सारांश लेख्नुहोस् र "समस्या", "म एआई कसरी प्रयोग गर्ने", "प्रभाव" जस्ता शीर्षकहरू समावेश गर्नुहोस्, र चाहनुहुन्छ भने व्यवसाय योजना पनि।

यदि तपाईंले यो कार्य गर्नुभयो भने, तपाईं Microsoft को इनक्युबेटर, [Microsoft for Startups Founders Hub](https://www.microsoft.com/startups?WT.mc_id=academic-105485-koreyst) मा आवेदन दिन तयार हुन सक्नुहुन्छ। हामी Azure, OpenAI, मार्गदर्शन र धेरै कुराहरूका लागि क्रेडिटहरू प्रदान गर्छौं, अवश्य जाँच गर्नुहोस्!

## ज्ञान जाँच

ठूला भाषा मोडेलहरूबारे के सत्य हो?

1. तपाईंले हरेक पटक बिल्कुल एउटै जवाफ पाउनुहुन्छ।
2. यो सबै कुरा पूर्ण रूपमा गर्छ, अंक जोड्न, काम गर्ने कोड उत्पादन गर्न आदि।
3. एउटै prompt प्रयोग गर्दा पनि जवाफ फरक हुन सक्छ। यो तपाईंलाई कुनै पाठ वा कोडको पहिलो मसौदा दिन राम्रो छ। तर तपाईंले परिणाम सुधार्न आवश्यक छ।

उत्तर: 3, LLM गैर-नियतात्मक हुन्छ, जवाफ फरक हुन्छ, तर तपाईं यसको फरकपनलाई temperature सेटिङबाट नियन्त्रण गर्न सक्नुहुन्छ। तपाईंले यसले सबै कुरा पूर्ण रूपमा गर्ने अपेक्षा गर्नु हुँदैन, यो तपाईंको लागि भारी काम गर्ने उपकरण हो जसले तपाईंलाई राम्रो पहिलो प्रयास दिन्छ र तपाईंले त्यसलाई क्रमशः सुधार्नुपर्छ।

## राम्रो काम! यात्रा जारी राख्नुहोस्

यस पाठ पूरा गरेपछि, हाम्रो [जेनेरेटिभ एआई सिकाइ संग्रह](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) हेर्नुहोस् र आफ्नो जेनेरेटिभ एआई ज्ञानलाई अझ उचाइमा पुर्‍याउनुहोस्!
पाठ २ मा जानुहोस् जहाँ हामी [विभिन्न LLM प्रकारहरू अन्वेषण र तुलना गर्ने](../02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst) तरिका हेर्नेछौं!

**अस्वीकरण**:  
यो दस्तावेज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) प्रयोग गरी अनुवाद गरिएको हो। हामी शुद्धताका लागि प्रयासरत छौं, तर कृपया ध्यान दिनुहोस् कि स्वचालित अनुवादमा त्रुटि वा अशुद्धता हुनसक्छ। मूल दस्तावेज यसको मूल भाषामा नै अधिकारिक स्रोत मानिनुपर्छ। महत्वपूर्ण जानकारीका लागि व्यावसायिक मानव अनुवाद सिफारिस गरिन्छ। यस अनुवादको प्रयोगबाट उत्पन्न कुनै पनि गलतफहमी वा गलत व्याख्याका लागि हामी जिम्मेवार छैनौं।