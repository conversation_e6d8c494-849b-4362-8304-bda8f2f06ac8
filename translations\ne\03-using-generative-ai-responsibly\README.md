<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "7f8f4c11f8c1cb6e1794442dead414ea",
  "translation_date": "2025-07-09T08:53:08+00:00",
  "source_file": "03-using-generative-ai-responsibly/README.md",
  "language_code": "ne"
}
-->
# जिम्मेवार तरिकाले Generative AI को प्रयोग

[![Using Generative AI Responsibly](../../../translated_images/03-lesson-banner.1ed56067a452d97709d51f6cc8b6953918b2287132f4909ade2008c936cd4af9.ne.png)](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)

> _यो पाठको भिडियो हेर्न माथिको तस्बिरमा क्लिक गर्नुहोस्_

AI र विशेष गरी Generative AI प्रति आकर्षित हुनु सजिलो छ, तर तपाईंले यसलाई जिम्मेवार तरिकाले कसरी प्रयोग गर्ने भन्ने कुरामा विचार गर्नुपर्छ। तपाईंले सुनिश्चित गर्नुपर्ने कुरा जस्तै नतिजा न्यायसंगत, हानिकारक नभएको र अन्य कुराहरू हुन्। यस अध्यायले तपाईंलाई उक्त सन्दर्भ, विचार गर्नुपर्ने विषयहरू, र AI को प्रयोग सुधार गर्न सक्रिय कदमहरू कसरी चाल्ने भन्ने बारे जानकारी दिन्छ।

## परिचय

यस पाठले समेट्ने विषयहरू:

- Generative AI एप्लिकेसनहरू बनाउँदा किन जिम्मेवार AI लाई प्राथमिकता दिनुपर्छ।
- जिम्मेवार AI का मुख्य सिद्धान्तहरू र ती कसरी Generative AI सँग सम्बन्धित छन्।
- रणनीति र उपकरणहरूको माध्यमबाट यी जिम्मेवार AI सिद्धान्तहरूलाई व्यवहारमा कसरी लागू गर्ने।

## सिकाइका लक्ष्यहरू

यस पाठ पूरा गरेपछि तपाईंलाई थाहा हुनेछ:

- Generative AI एप्लिकेसनहरू बनाउँदा जिम्मेवार AI को महत्त्व।
- Generative AI एप्लिकेसनहरू बनाउँदा जिम्मेवार AI का मुख्य सिद्धान्तहरू कहिले सोच्ने र लागू गर्ने।
- जिम्मेवार AI को अवधारणा व्यवहारमा ल्याउन उपलब्ध उपकरण र रणनीतिहरू।

## जिम्मेवार AI का सिद्धान्तहरू

Generative AI को उत्साह कहिल्यै यति उच्च भएको थिएन। यसले धेरै नयाँ विकासकर्ताहरू, ध्यान र लगानी यस क्षेत्रमा ल्याएको छ। यो Generative AI प्रयोग गरेर उत्पादन र कम्पनीहरू बनाउन चाहनेहरूका लागि धेरै सकारात्मक छ, तर हामीले जिम्मेवार तरिकाले अघि बढ्नु पनि आवश्यक छ।

यस कोर्सभरि हामी हाम्रो स्टार्टअप र AI शिक्षा उत्पादन निर्माणमा केन्द्रित छौं। हामी जिम्मेवार AI का सिद्धान्तहरू प्रयोग गर्नेछौं: न्याय, समावेशिता, विश्वसनीयता/सुरक्षा, सुरक्षा र गोपनीयता, पारदर्शिता र जवाफदेहिता। यी सिद्धान्तहरूसँग हामी हाम्रो उत्पादनहरूमा Generative AI को प्रयोग कसरी सम्बन्धित छ भनेर अन्वेषण गर्नेछौं।

## किन जिम्मेवार AI लाई प्राथमिकता दिनुपर्छ

उत्पादन बनाउँदा, प्रयोगकर्ताको हितलाई ध्यानमा राखेर मानव-केंद्रित दृष्टिकोण अपनाउँदा सबैभन्दा राम्रो नतिजा आउँछ।

Generative AI को विशेषता यसको क्षमता हो जसले प्रयोगकर्ताहरूलाई उपयोगी उत्तर, जानकारी, मार्गदर्शन र सामग्री सिर्जना गर्न सक्छ। यो धेरै म्यानुअल चरणहरू बिना नै गर्न सकिन्छ जसले प्रभावशाली नतिजा दिन सक्छ। तर उचित योजना र रणनीतिबिना, यसले दुर्भाग्यवश तपाईंका प्रयोगकर्ता, उत्पादन र समाजका लागि हानिकारक नतिजा पनि ल्याउन सक्छ।

यहाँ केही (तर सबै होइन) सम्भावित हानिकारक नतिजाहरू हेरौं:

### भ्रम (Hallucinations)

Hallucinations भन्नाले LLM ले यस्तो सामग्री उत्पादन गर्नु हो जुन पूर्ण रूपमा अर्थहीन वा तथ्यगत रूपमा गलत हुन्छ जुन हामीलाई अन्य स्रोतहरूबाट थाहा छ।

उदाहरणका लागि, हाम्रो स्टार्टअपले एउटा यस्तो सुविधा बनाएको छ जहाँ विद्यार्थीहरूले ऐतिहासिक प्रश्न सोध्न सक्छन्। एउटा विद्यार्थीले सोध्छ `Who was the sole survivor of Titanic?`

मोडेलले तलको जस्तो उत्तर दिन्छ:

![Prompt saying "Who was the sole survivor of the Titanic"](../../../03-using-generative-ai-responsibly/images/ChatGPT-titanic-survivor-prompt.webp)

> _(स्रोत: [Flying bisons](https://flyingbisons.com?WT.mc_id=academic-105485-koreyst))_

यो उत्तर धेरै आत्मविश्वासी र विस्तृत छ। दुर्भाग्यवश, यो गलत हो। थोरै अनुसन्धानले पनि थाहा हुन्छ कि Titanic दुर्घटनामा एकभन्दा बढी बाँच्नेहरू थिए। जसले यो विषयमा अनुसन्धान सुरु गरेको छ, उसलाई यो उत्तर प्रश्न नगरी तथ्य जस्तो लाग्न सक्छ। यसको परिणाम स्वरूप AI प्रणाली अविश्वसनीय बन्न सक्छ र हाम्रो स्टार्टअपको प्रतिष्ठामा नकारात्मक प्रभाव पर्न सक्छ।

हरेक LLM को नयाँ संस्करणसँग hallucinations कम गर्ने प्रदर्शन सुधार देखिएको छ। यद्यपि सुधार भए पनि, हामी विकासकर्ता र प्रयोगकर्ताहरूले यी सीमाहरूलाई सदैव ध्यानमा राख्नुपर्छ।

### हानिकारक सामग्री

पहिलेको खण्डमा हामीले LLM ले गलत वा अर्थहीन उत्तर दिने कुरा चर्चा गर्यौं। अर्को जोखिम भनेको मोडेलले हानिकारक सामग्री प्रदान गर्नु हो।

हानिकारक सामग्रीको परिभाषा:

- आत्म-हानि वा कुनै समूहलाई हानि पुर्‍याउन निर्देशन वा प्रोत्साहन दिनु।
- घृणास्पद वा अपमानजनक सामग्री।
- कुनै प्रकारको आक्रमण वा हिंसात्मक कार्यको योजना बनाउने निर्देशन।
- अवैध सामग्री खोज्ने वा अवैध कार्य गर्ने तरिका बताउने।
- यौनसम्बन्धी स्पष्ट सामग्री देखाउने।

हाम्रो स्टार्टअपका लागि, हामीले यस्तो सामग्री विद्यार्थीहरूले नदेखुन् भनेर सही उपकरण र रणनीति राख्न चाहन्छौं।

### न्यायको अभाव

न्याय भन्नाले “AI प्रणाली पूर्वाग्रह र भेदभावबाट मुक्त होस् र सबैलाई समान र न्यायसंगत व्यवहार गरियोस्” भन्ने हो। Generative AI को दुनियाँमा, हामीले सुनिश्चित गर्नुपर्छ कि मोडेलको नतिजाले हाशिएमा रहेका समूहहरूको बहिष्कार गर्ने विश्वदृष्टिकोणलाई पुनः पुष्टि नगरोस्।

यस्ता नतिजाहरूले मात्र प्रयोगकर्ताका लागि सकारात्मक उत्पादन अनुभव निर्माणमा बाधा पुर्‍याउँदैनन्, समाजमा पनि थप हानि पुर्‍याउँछन्। विकासकर्ताहरूले Generative AI प्रयोग गरेर समाधान बनाउँदा सधैं फराकिलो र विविध प्रयोगकर्ता आधारलाई ध्यानमा राख्नुपर्छ।

## Generative AI जिम्मेवार तरिकाले कसरी प्रयोग गर्ने

अब जब हामीले जिम्मेवार Generative AI को महत्त्व पहिचान गरिसकेका छौं, आउनुहोस् ४ कदम हेरौं जसले हामीलाई AI समाधानहरू जिम्मेवार तरिकाले बनाउन मद्दत गर्छ:

![Mitigate Cycle](../../../translated_images/mitigate-cycle.babcd5a5658e1775d5f2cb47f2ff305cca090400a72d98d0f9e57e9db5637c72.ne.png)

### सम्भावित हानि मापन गर्नुहोस्

सफ्टवेयर परीक्षणमा, हामी प्रयोगकर्ताले एप्लिकेसनमा गर्ने अपेक्षित क्रियाकलापहरू परीक्षण गर्छौं। त्यसैगरी, प्रयोगकर्ताले सबैभन्दा बढी प्रयोग गर्ने विविध प्रकारका प्रॉम्प्टहरू परीक्षण गर्नु सम्भावित हानि मापन गर्ने राम्रो तरिका हो।

हाम्रो स्टार्टअपले शिक्षा सम्बन्धी उत्पादन बनाइरहेको हुँदा, शिक्षा सम्बन्धी प्रॉम्प्टहरूको सूची तयार पार्नु राम्रो हुन्छ। यसले कुनै विषय, ऐतिहासिक तथ्यहरू, र विद्यार्थी जीवनका प्रॉम्प्टहरू समेट्न सक्छ।

### सम्भावित हानि कम गर्नुहोस्

अब मोडेल र यसको प्रतिक्रियाबाट हुन सक्ने सम्भावित हानि रोक्न वा सीमित गर्न उपायहरू खोज्ने समय हो। हामी यसलाई ४ तहमा हेर्न सक्छौं:

![Mitigation Layers](../../../translated_images/mitigation-layers.377215120b9a1159a8c3982c6bbcf41b6adf8c8fa04ce35cbaeeb13b4979cdfc.ne.png)

- **मोडेल**। सही प्रयोगका लागि सही मोडेल छान्नु। GPT-4 जस्ता ठूला र जटिल मोडेलहरू साना र विशिष्ट प्रयोगका लागि लागू गर्दा हानिकारक सामग्रीको जोखिम बढी हुन्छ। प्रशिक्षण डाटाको प्रयोग गरेर फाइन-ट्यून गर्दा पनि हानिकारक सामग्रीको जोखिम कम हुन्छ।

- **सुरक्षा प्रणाली**। सुरक्षा प्रणाली भनेको मोडेल सेवा गर्ने प्लेटफर्ममा रहेका उपकरण र कन्फिगरेसनहरूको सेट हो जसले हानि कम गर्न मद्दत गर्छ। उदाहरणका लागि Azure OpenAI सेवामा रहेको सामग्री फिल्टरिङ प्रणाली। प्रणालीहरूले जेलब्रेक आक्रमण र बोटबाट आउने अवाञ्छित अनुरोधहरू पनि पत्ता लगाउनुपर्छ।

- **मेटाप्रॉम्प्ट**। मेटाप्रॉम्प्ट र ग्राउन्डिङले मोडेललाई निश्चित व्यवहार र जानकारीको आधारमा निर्देशन वा सीमित गर्न मद्दत गर्छ। यसमा प्रणाली इनपुट प्रयोग गरेर मोडेलका केही सीमा निर्धारण गर्न सकिन्छ। साथै, प्रणालीको दायरा वा डोमेनसँग सम्बन्धित नतिजा दिन सकिन्छ।

यसमा Retrieval Augmented Generation (RAG) जस्ता प्रविधिहरू प्रयोग गरेर मोडेललाई विश्वसनीय स्रोतहरूबाट मात्र जानकारी तान्न लगाउन सकिन्छ। यस कोर्समा पछि [सर्च एप्लिकेसनहरू बनाउने](../08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst) पाठ छ।

- **प्रयोगकर्ता अनुभव**। अन्तिम तह जहाँ प्रयोगकर्ता हाम्रो एप्लिकेसनको इन्टरफेसमार्फत मोडेलसँग प्रत्यक्ष अन्तरक्रिया गर्छ। यसरी हामी UI/UX डिजाइन गरेर प्रयोगकर्ताले मोडेलमा पठाउन सक्ने इनपुटका प्रकारहरू सीमित गर्न सक्छौं र प्रयोगकर्तालाई देखाइने पाठ वा तस्बिरहरू पनि नियन्त्रण गर्न सक्छौं। AI एप्लिकेसन तैनाथ गर्दा, हामीले हाम्रो Generative AI एप्लिकेसनले के गर्न सक्छ र के गर्न सक्दैन भन्ने कुरा पारदर्शी हुनुपर्छ।

हामीसँग [AI एप्लिकेसनहरूको UX डिजाइन गर्ने](../12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst) समर्पित पाठ छ।

- **मोडेलको मूल्याङ्कन**। LLM सँग काम गर्दा चुनौतीपूर्ण हुन्छ किनभने हामीसँग मोडेलले प्रशिक्षण लिएको डाटामा पूर्ण नियन्त्रण हुँदैन। त्यसैले हामीले मोडेलको प्रदर्शन र नतिजाहरू सधैं मूल्याङ्कन गर्नुपर्छ। मोडेलको शुद्धता, समानता, ग्राउन्डेडनेस र नतिजाको सान्दर्भिकता मापन गर्नु महत्त्वपूर्ण छ। यसले सरोकारवालाहरू र प्रयोगकर्ताहरूलाई पारदर्शिता र विश्वास दिन्छ।

### जिम्मेवार Generative AI समाधान सञ्चालन गर्नुहोस्

AI एप्लिकेसनहरू वरिपरि सञ्चालन अभ्यास निर्माण गर्नु अन्तिम चरण हो। यसमा हाम्रो स्टार्टअपका अन्य विभागहरू जस्तै कानूनी र सुरक्षा विभागसँग साझेदारी गरेर सबै नियामक नीतिहरूको पालना सुनिश्चित गर्नु पर्छ। सुरु गर्नु अघि, वितरण, घटना व्यवस्थापन, र रोलब्याक योजना बनाउनु आवश्यक छ ताकि प्रयोगकर्तालाई हुने कुनै पनि हानि बढ्न नदिन सकियोस्।

## उपकरणहरू

जिम्मेवार AI समाधान विकास गर्ने काम धेरै देखिए पनि यो प्रयास गर्न लायक छ। Generative AI क्षेत्र बढ्दै जाँदा, विकासकर्ताहरूलाई जिम्मेवारी workflow मा सहज रूपमा समावेश गर्न मद्दत गर्ने उपकरणहरू पनि विकसित हुँदैछन्। उदाहरणका लागि, [Azure AI Content Safety](https://learn.microsoft.com/azure/ai-services/content-safety/overview?WT.mc_id=academic-105485-koreyst) ले API अनुरोधमार्फत हानिकारक सामग्री र तस्बिर पत्ता लगाउन मद्दत गर्छ।

## ज्ञान जाँच

जिम्मेवार AI प्रयोग सुनिश्चित गर्न तपाईंले के कुरामा ध्यान दिनुपर्छ?

1. उत्तर सही होस्।
1. हानिकारक प्रयोग, AI अपराधमा प्रयोग नगरियोस्।
1. AI पूर्वाग्रह र भेदभावबाट मुक्त होस्।

उत्तर: २ र ३ सही हुन्। जिम्मेवार AI ले तपाईंलाई हानिकारक प्रभाव र पूर्वाग्रहहरू कसरी कम गर्ने भन्ने विचार गर्न मद्दत गर्छ।

## 🚀 चुनौती

[Azure AI Content Safety](https://learn.microsoft.com/azure/ai-services/content-safety/overview?WT.mc_id=academic-105485-koreyst) बारे पढ्नुहोस् र तपाईंको प्रयोगका लागि के अपनाउन सकिन्छ हेर्नुहोस्।

## राम्रो काम, सिकाइ जारी राख्नुहोस्

यस पाठ पूरा गरेपछि, हाम्रो [Generative AI सिकाइ संग्रह](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) हेर्नुहोस् र आफ्नो Generative AI ज्ञान अझ बढाउनुहोस्!

पाठ ४ मा जानुहोस् जहाँ हामी [Prompt Engineering Fundamentals](../04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst) हेरौं!

**अस्वीकरण**:  
यो दस्तावेज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) प्रयोग गरी अनुवाद गरिएको हो। हामी शुद्धताका लागि प्रयासरत छौं, तर कृपया ध्यान दिनुहोस् कि स्वचालित अनुवादमा त्रुटि वा अशुद्धता हुन सक्छ। मूल दस्तावेज यसको मूल भाषामा आधिकारिक स्रोत मानिनुपर्छ। महत्वपूर्ण जानकारीका लागि व्यावसायिक मानव अनुवाद सिफारिस गरिन्छ। यस अनुवादको प्रयोगबाट उत्पन्न कुनै पनि गलतफहमी वा गलत व्याख्याका लागि हामी जिम्मेवार छैनौं।