<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "f3cac698e9eea47dd563633bd82daf8c",
  "translation_date": "2025-07-09T15:20:54+00:00",
  "source_file": "13-securing-ai-applications/README.md",
  "language_code": "ne"
}
-->
# तपाईंको जेनेरेटिभ AI अनुप्रयोगहरूको सुरक्षा

[![तपाईंको जेनेरेटिभ AI अनुप्रयोगहरूको सुरक्षा](../../../translated_images/13-lesson-banner.14103e36b4bbf17398b64ed2b0531f6f2c6549e7f7342f797c40bcae5a11862e.ne.png)](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst)

## परिचय

यस पाठले समेट्नेछ:

- AI प्रणालीहरूको सन्दर्भमा सुरक्षा।
- AI प्रणालीहरूमा सामान्य जोखिम र खतरा।
- AI प्रणालीहरूको सुरक्षा गर्ने तरिका र विचारहरू।

## सिकाइका लक्ष्यहरू

यस पाठ पूरा गरेपछि, तपाईंले बुझ्न सक्नुहुनेछ:

- AI प्रणालीहरूमा हुने खतरा र जोखिमहरू।
- AI प्रणालीहरूको सुरक्षा गर्ने सामान्य तरिका र अभ्यासहरू।
- सुरक्षा परीक्षण लागू गर्दा कसरी अप्रत्याशित परिणाम र प्रयोगकर्ताको विश्वासमा कमी आउनबाट रोक्न सकिन्छ।

## जेनेरेटिभ AI को सन्दर्भमा सुरक्षा भनेको के हो?

कृत्रिम बुद्धिमत्ता (AI) र मेसिन लर्निङ (ML) प्रविधिहरू हाम्रो जीवनमा बढ्दो प्रभाव पार्दै गर्दा, ग्राहकको डाटा मात्र होइन, AI प्रणालीहरूलाई पनि सुरक्षित राख्नु अत्यावश्यक छ। AI/ML उच्च मूल्य निर्णय प्रक्रियाहरूमा प्रयोग भइरहेका छन् जहाँ गलत निर्णयले गम्भीर परिणामहरू निम्त्याउन सक्छ।

यहाँ ध्यान दिनुपर्ने मुख्य बुँदाहरू छन्:

- **AI/ML को प्रभाव**: AI/ML ले दैनिक जीवनमा ठूलो प्रभाव पार्छ र त्यसैले तिनीहरूको सुरक्षा आवश्यक छ।
- **सुरक्षा चुनौतीहरू**: AI/ML को प्रभावलाई ध्यानमा राख्दै, तिनीहरूलाई जटिल आक्रमणहरूबाट जोगाउन उचित सुरक्षा आवश्यक छ, चाहे ती ट्रोलहरू हुन् वा संगठित समूहहरू।
- **रणनीतिक समस्या**: प्रविधि उद्योगले दीर्घकालीन ग्राहक सुरक्षा र डाटा सुरक्षाका लागि रणनीतिक चुनौतीहरूलाई सक्रिय रूपमा सम्बोधन गर्नुपर्छ।

थप रूपमा, मेसिन लर्निङ मोडेलहरूले दुर्भावनापूर्ण इनपुट र सामान्य असामान्य डाटाबीच फरक गर्न सक्दैनन्। धेरै प्रशिक्षण डाटा सार्वजनिक, अनक्युरेटेड, अनमोडरेटेड स्रोतहरूबाट आउँछ जुन तेस्रो पक्षले योगदान गर्न सक्छ। आक्रमणकारीहरूले डाटासेटहरूलाई ह्याक गर्न आवश्यक छैन जब उनीहरू स्वतन्त्र रूपमा योगदान गर्न सक्छन्। समयसँगै, कम विश्वासयोग्य दुर्भावनापूर्ण डाटा उच्च विश्वासयोग्य विश्वसनीय डाटामा परिणत हुन सक्छ यदि डाटा संरचना/फर्म्याट सही छ भने।

त्यसैले, तपाईंका मोडेलहरूले निर्णय गर्न प्रयोग गर्ने डाटा स्टोरहरूको अखण्डता र सुरक्षा सुनिश्चित गर्नु अत्यन्त महत्वपूर्ण छ।

## AI का खतरा र जोखिमहरू बुझ्न

AI र सम्बन्धित प्रणालीहरूको सन्दर्भमा, डाटा पोइजनिङ आजको सबैभन्दा ठूलो सुरक्षा खतरा हो। डाटा पोइजनिङ भनेको कसैले जानाजानी AI लाई प्रशिक्षण दिन प्रयोग हुने जानकारी परिवर्तन गर्नु हो जसले मोडेललाई गल्ती गर्न बाध्य पार्छ। यो मानकीकृत पत्ता लगाउने र न्यूनीकरण विधिहरूको अभाव र अविश्वसनीय वा अनक्युरेटेड सार्वजनिक डाटासेटहरूमा निर्भरता कारणले हुन्छ। डाटाको अखण्डता कायम राख्न र त्रुटिपूर्ण प्रशिक्षण प्रक्रियाबाट बच्न, तपाईंले आफ्नो डाटाको उत्पत्ति र इतिहास ट्र्याक गर्नुपर्छ। नत्र, पुरानो भनाइ “फोहोर डाटा इनपुट, फोहोर नतिजा आउटपुट” लागू हुन्छ, जसले मोडेलको प्रदर्शनमा असर पुर्‍याउँछ।

यहाँ डाटा पोइजनिङले तपाईंका मोडेलहरूलाई कसरी असर गर्न सक्छ भन्ने उदाहरणहरू छन्:

1. **लेबल फ्लिपिङ**: द्विआधारी वर्गीकरण कार्यमा, एक आक्रमणकारीले प्रशिक्षण डाटाको सानो भागका लेबलहरू जानाजानी उल्टो पार्छ। जस्तै, सामान्य नमूनाहरूलाई दुर्भावनापूर्ण भनेर लेबल गर्ने, जसले मोडेललाई गलत सम्बन्ध सिक्न बाध्य पार्छ।\
   **उदाहरण**: स्प्याम फिल्टरले वैध इमेलहरूलाई स्प्याम भनेर गलत वर्गीकरण गर्नु।
2. **फिचर पोइजनिङ**: आक्रमणकारीले प्रशिक्षण डाटाका विशेषताहरूलाई सानो रूपमा परिवर्तन गरेर मोडेललाई भ्रमित पार्ने वा पक्षपाती बनाउने प्रयास गर्छ।\
   **उदाहरण**: उत्पादन विवरणमा अप्रासंगिक कुञ्जीशब्दहरू थपेर सिफारिस प्रणालीलाई प्रभावित पार्नु।
3. **डाटा इन्जेक्सन**: प्रशिक्षण सेटमा दुर्भावनापूर्ण डाटा थपेर मोडेलको व्यवहारलाई प्रभावित पार्ने।\
   **उदाहरण**: नक्कली प्रयोगकर्ता समीक्षा थपेर भावना विश्लेषणलाई विकृत पार्नु।
4. **ब्याकडोर आक्रमणहरू**: आक्रमणकारीले प्रशिक्षण डाटामा लुकेको नमूना (ब्याकडोर) राख्छ। मोडेलले यो नमूनालाई चिन्ने र ट्रिगर हुँदा दुर्भावनापूर्ण व्यवहार देखाउने।\
   **उदाहरण**: ब्याकडोर भएका तस्बिरहरूसँग प्रशिक्षित अनुहार पहिचान प्रणालीले विशेष व्यक्तिलाई गलत पहिचान गर्नु।

MITRE Corporation ले [ATLAS (Adversarial Threat Landscape for Artificial-Intelligence Systems)](https://atlas.mitre.org/?WT.mc_id=academic-105485-koreyst) तयार पारेको छ, जुन AI प्रणालीहरूमा वास्तविक आक्रमणहरूमा प्रयोग हुने रणनीति र प्रविधिहरूको ज्ञानकोष हो।

> AI-सक्षम प्रणालीहरूमा बढ्दो सङ्ख्यामा कमजोरीहरू छन्, किनभने AI को समावेशीकरणले परम्परागत साइबर-आक्रमणहरू भन्दा बढी आक्रमण सतह बढाउँछ। हामीले ATLAS विकास गरेका छौं ताकि यी अनौठा र विकासशील कमजोरीहरूको बारेमा सचेतना बढाउन सकियोस्, किनभने विश्व समुदायले विभिन्न प्रणालीहरूमा AI समावेश गर्दैछ। ATLAS MITRE ATT&CK® फ्रेमवर्कको आधारमा मोडेल गरिएको छ र यसको रणनीति, प्रविधि, र प्रक्रियाहरू ATT&CK सँग पूरक छन्।

MITRE ATT&CK® फ्रेमवर्क जस्तै, जुन परम्परागत साइबरसुरक्षामा उन्नत खतरा अनुकरण योजना बनाउन व्यापक रूपमा प्रयोग हुन्छ, ATLAS ले सजिलै खोज्न मिल्ने TTPs सेट प्रदान गर्छ जसले नयाँ आक्रमणहरू विरुद्ध रक्षा गर्न सहयोग पुर्‍याउँछ।

थप रूपमा, Open Web Application Security Project (OWASP) ले LLM प्रयोग गर्ने अनुप्रयोगहरूमा पाइने सबैभन्दा महत्वपूर्ण कमजोरीहरूको "[शीर्ष १० सूची](https://llmtop10.com/?WT.mc_id=academic-105485-koreyst)" तयार पारेको छ। यसले डाटा पोइजनिङ जस्ता खतरा साथै अन्य जोखिमहरूलाई पनि उजागर गर्छ, जस्तै:

- **प्रम्प्ट इन्जेक्सन**: आक्रमणकारीहरूले सावधानीपूर्वक तयार पारिएका इनपुटहरू मार्फत ठूलो भाषा मोडेल (LLM) लाई नियन्त्रण गर्ने प्रविधि, जसले मोडेललाई यसको निर्धारित व्यवहारभन्दा बाहिर काम गर्न बाध्य पार्छ।
- **सप्लाई चेन कमजोरीहरू**: LLM मा प्रयोग हुने कम्पोनेन्ट र सफ्टवेयरहरू, जस्तै Python मोड्युलहरू वा बाह्य डाटासेटहरू, आफैंमा कमजोर हुन सक्छन् जसले अप्रत्याशित परिणाम, पक्षपात, र आधारभूत पूर्वाधारमा कमजोरी ल्याउन सक्छ।
- **अत्यधिक निर्भरता**: LLM हरू त्रुटिपूर्ण हुन सक्छन् र कहिलेकाहीं गलत वा असुरक्षित नतिजा दिन सक्छन्। धेरै घटनाहरूमा मानिसहरूले ती नतिजालाई साँचो मान्दा अनपेक्षित नकारात्मक परिणामहरू आएका छन्।

Microsoft Cloud Advocate Rod Trent ले एउटा निःशुल्क ईबुक, [Must Learn AI Security](https://github.com/rod-trent/OpenAISecurity/tree/main/Must_Learn/Book_Version?WT.mc_id=academic-105485-koreyst) लेखेका छन्, जसले यी र अन्य नयाँ AI खतरा र तिनीहरूलाई कसरी सामना गर्ने बारे विस्तृत मार्गदर्शन दिन्छ।

## AI प्रणालीहरू र LLM का लागि सुरक्षा परीक्षण

कृत्रिम बुद्धिमत्ता (AI) विभिन्न क्षेत्र र उद्योगहरूमा रूपान्तरण गर्दैछ, समाजका लागि नयाँ सम्भावना र लाभहरू ल्याउँदै। तर, AI ले डाटा गोपनीयता, पक्षपात, व्याख्यायोग्यताको अभाव, र दुरुपयोग जस्ता चुनौती र जोखिमहरू पनि ल्याउँछ। त्यसैले, AI प्रणालीहरूलाई सुरक्षित र जिम्मेवार बनाउन आवश्यक छ, जसले नैतिक र कानुनी मापदण्डहरू पालना गर्छ र प्रयोगकर्ता तथा सरोकारवालाहरूले विश्वास गर्न सकून्।

सुरक्षा परीक्षण भनेको AI प्रणाली वा LLM को सुरक्षा मूल्याङ्कन गर्ने प्रक्रिया हो, जसमा तिनीहरूको कमजोरीहरू पहिचान गरी तिनीहरूलाई परीक्षण गरिन्छ। यो विकासकर्ता, प्रयोगकर्ता, वा तेस्रो पक्षका अडिटरहरूले गर्न सक्छन्, परीक्षणको उद्देश्य र दायरा अनुसार। AI प्रणाली र LLM का लागि केही सामान्य सुरक्षा परीक्षण विधिहरू:

- **डाटा सफाई**: प्रशिक्षण डाटा वा AI प्रणाली वा LLM को इनपुटबाट संवेदनशील वा निजी जानकारी हटाउने वा अनामिकरण गर्ने प्रक्रिया। यसले डाटा चुहावट र दुर्भावनापूर्ण हेरफेर रोक्न मद्दत गर्छ।
- **विपरीत परीक्षण**: AI प्रणाली वा LLM को इनपुट वा आउटपुटमा विपरीत उदाहरणहरू सिर्जना गरी यसको मजबुती र आक्रमण प्रतिरोध क्षमता जाँच्ने प्रक्रिया। यसले कमजोरीहरू पत्ता लगाउन र न्यूनीकरण गर्न मद्दत गर्छ।
- **मोडेल प्रमाणीकरण**: AI प्रणाली वा LLM को मोडेल प्यारामिटर वा संरचनाको शुद्धता र पूर्णता जाँच्ने प्रक्रिया। यसले मोडेल चोरी रोक्न र सुरक्षा सुनिश्चित गर्न मद्दत गर्छ।
- **आउटपुट प्रमाणीकरण**: AI प्रणाली वा LLM को आउटपुटको गुणस्तर र विश्वसनीयता जाँच्ने प्रक्रिया। यसले दुर्भावनापूर्ण हेरफेर पत्ता लगाउन र सुधार गर्न मद्दत गर्छ।

OpenAI, AI प्रणालीहरूको अग्रणी, ले आफ्नो रेड टीमिङ नेटवर्क पहलको रूपमा _सुरक्षा मूल्याङ्कन_ को श्रृंखला स्थापना गरेको छ, जसले AI प्रणालीहरूको आउटपुट परीक्षण गरेर AI सुरक्षा मा योगदान पुर्‍याउने लक्ष्य राख्छ।

> मूल्याङ्कनहरू सरल प्रश्नोत्तर परीक्षणदेखि जटिल सिमुलेशनसम्म हुन सक्छन्। यहाँ OpenAI द्वारा विभिन्न कोणबाट AI व्यवहार मूल्याङ्कन गर्न विकसित केही नमूना मूल्याङ्कनहरू छन्:

#### मनाउने प्रयास

- [MakeMeSay](https://github.com/openai/evals/tree/main/evals/elsuite/make_me_say/readme.md?WT.mc_id=academic-105485-koreyst): कसरी एउटा AI प्रणाली अर्को AI प्रणालीलाई गोप्य शब्द भन्न बाध्य पार्न सक्छ?
- [MakeMePay](https://github.com/openai/evals/tree/main/evals/elsuite/make_me_pay/readme.md?WT.mc_id=academic-105485-koreyst): कसरी एउटा AI प्रणाली अर्को AI प्रणालीलाई पैसा दान गर्न मनाउन सक्छ?
- [Ballot Proposal](https://github.com/openai/evals/tree/main/evals/elsuite/ballots/readme.md?WT.mc_id=academic-105485-koreyst): कसरी एउटा AI प्रणाली अर्को AI प्रणालीलाई राजनीतिक प्रस्तावको समर्थन गर्न प्रभाव पार्न सक्छ?

#### स्टेगानोग्राफी (लुकेको सन्देश)

- [Steganography](https://github.com/openai/evals/tree/main/evals/elsuite/steganography/readme.md?WT.mc_id=academic-105485-koreyst): कसरी एउटा AI प्रणाली अर्को AI प्रणालीलाई पत्ता नलागी गोप्य सन्देश पठाउन सक्छ?
- [Text Compression](https://github.com/openai/evals/tree/main/evals/elsuite/text_compression/readme.md?WT.mc_id=academic-105485-koreyst): कसरी एउटा AI प्रणाली सन्देशहरू कम्प्रेस र डिकम्प्रेस गरेर गोप्य सन्देश लुकाउन सक्छ?
- [Schelling Point](https://github.com/openai/evals/blob/main/evals/elsuite/schelling_point/README.md?WT.mc_id=academic-105485-koreyst): कसरी एउटा AI प्रणाली अर्को AI प्रणालीसँग प्रत्यक्ष संवाद बिना समन्वय गर्न सक्छ?

### AI सुरक्षा

हामीले AI प्रणालीहरूलाई दुर्भावनापूर्ण आक्रमण, दुरुपयोग, वा अनपेक्षित परिणामहरूबाट जोगाउन आवश्यक छ। यसमा समावेश छन्:

- AI मोडेलहरूलाई प्रशिक्षण र सञ्चालन गर्न प्रयोग हुने डाटा र एल्गोरिदमहरूको सुरक्षा।
- AI प्रणालीहरूमा अनधिकृत पहुँच, हेरफेर, वा तोडफोड रोकथाम।
- AI प्रणालीहरूमा पक्षपात, भेदभाव, वा नैतिक समस्याहरू पत्ता लगाउने र न्यूनीकरण गर्ने।
- AI निर्णय र कार्यहरूको जवाफदेहिता, पारदर्शिता, र व्याख्यायोग्यताको सुनिश्चितता।
- AI प्रणालीका लक्ष्य र मानहरूलाई मानव र समाजका मूल्यहरूसँग मेल खाने बनाउने।

AI सुरक्षा AI प्रणाली र डाटाको अखण्डता, उपलब्धता, र गोपनीयता सुनिश्चित गर्न महत्त्वपूर्ण छ। AI सुरक्षाका केही चुनौती र अवसरहरू:

- अवसर: साइबरसुरक्षा रणनीतिमा AI समावेश गर्नु, किनभने यसले खतरा पहिचान र प्रतिक्रिया समय सुधार्न महत्वपूर्ण भूमिका खेल्न सक्छ। AI ले फिशिङ, मालवेयर, वा र्यानसमवेयर जस्ता साइबर आक्रमणहरूको पत्ता लगाउने र न्यूनीकरण स्वचालित र वृद्धि गर्न मद्दत गर्छ।
- चुनौती: AI लाई आक्रमणकारीहरूले जटिल आक्रमणहरू गर्न प्रयोग गर्न सक्छन्, जस्तै नक्कली वा भ्रमपूर्ण सामग्री सिर्जना, प्रयोगकर्ताको नक्कल, वा AI प्रणालीहरूको कमजोरीहरू फाइदा उठाउने। त्यसैले, AI विकासकर्ताहरूले यस्ता प्रणालीहरू डिजाइन गर्ने जिम्मेवारी लिन्छन् जुन दुरुपयोग विरुद्ध मजबुत र लचिलो हुन्छन्।

### डाटा संरक्षण

LLM हरूले प्रयोग गर्ने डाटाको गोपनीयता र सुरक्षामा जोखिम ल्याउन सक्छन्। उदाहरणका लागि, LLM हरूले आफ्नो प्रशिक्षण डाटाबाट संवेदनशील जानकारीहरू, जस्तै व्यक्तिगत नाम, ठेगाना, पासवर्ड, वा क्रेडिट कार्ड नम्बरहरू सम्झन र चुहाउन सक्छन्। तिनीहरूलाई दुर्भावनापूर्ण व्यक्तिहरूले पनि नियन्त्रण गर्न सक्छन् जसले तिनीहरूको कमजोरी वा पक्षपातलाई फाइदा उठाउन चाहन्छन्। त्यसैले, यी जोखिमहरूलाई बुझ्न र LLM सँग प्रयोग हुने डाटाको सुरक्षा गर्न उपयुक्त कदम चाल्न आवश्यक छ। तपाईंले लिन सक्ने केही कदमहरू:

- **LLM सँग साझा गर्ने डाटाको मात्रा र प्रकार सीमित गर्नु**: केवल आवश्यक र सान्दर्भिक डाटा मात्र साझा गर्नुहोस्, र संवेदनशील, गोप्य, वा व्यक्तिगत डाटा साझा नगर्नुहोस्। प्रयोगकर्ताले डाटा अनामिकरण वा इन्क्रिप्सन पनि गर्नुपर्छ, जस्तै पहिचानयोग्य जानकारी हटाउने वा सुरक्षित सञ्चार माध्यम प्रयोग गर्ने।
- **LLM ले उत्पादन गर्ने डाटाको प्रमाणीकरण गर्नु**: LLM ले उत्पादन गर्ने आउटपुटको शुद्धता र गुणस्तर जाँच्नुहोस् ताकि अवाञ्छित वा अनुपयुक्त जानकारी नहोस्।
- **कुनै पनि डाटा चुहावट वा घटना रिपोर्ट र सचेत गर्नु**: LLM बाट कुनै पनि संदिग्ध वा असामान्य गतिविधि वा व्यवहार, जस्तै अप्रासंगिक, गलत, अपमानजनक, वा हानिकारक पाठ उत्पादन, मा सतर्क रहनुहोस्। यो डाटा चुहावट वा सुरक्षा घटनाको संकेत हुन सक्छ।

डाटा सुरक्षा, शासन, र अनुपालन कुनै पनि संस्थाका लागि अत्यावश्यक छन् जसले बहु-क्लाउड वातावरणमा डाटा र AI को शक्ति उपयोग गर्न चाहन्छ। तपाईंले विभिन्न प्रकारका डाटा (संरचित, असंरचित, र AI द्वारा उत्पन्न) लाई विभिन्न स्थानहरूमा सुरक्षित र शासित गर्नुपर्छ, साथै वर्तमान र भविष्यका डाटा सुरक्षा, शासन, र AI नियमहरूलाई ध्यानमा राख्नुपर्छ। तपाईंले केही उत्तम अभ्यास र सावधानी अपनाउनुपर्छ, जस्तै:

- डाटा सुरक्षा र गोपनीयता सुविधाहरू प्रदान गर्ने क्लाउड सेवा वा प्लेटफर्महरू प्रयोग गर्नुहोस्।
- डाटामा त्रुटि, असंगति, वा असामान्यता जाँच्न डाटा गुणस्तर र प्रमाणीकरण उपकरणहरू प्रयोग गर्नुहोस्।
- डाटा जिम्मेवार र पारदर्शी रूपमा प्रयोग गर्न डाटा शासन र नैतिकता फ्रेमवर्कहरू अपनाउनुहोस्।

### वास्तविक संसारका खतरा अनुकरण - AI रेड टीमिङ

वास्तविक संसारका खतरा अनुकरण अब लचिलो AI प्रणालीहरू निर्माण गर्ने मानक अभ्यास मानिन्छ, जसमा समान उपकरण, रणनीति, र प्रक्रियाहरू प्रयोग गरी प्रणालीहरूमा जोखिम पहिचान र रक्षात्मक प्रतिक्रिया परीक्षण गरिन्छ।
> AI रेड टीमिङको अभ्यासले व्यापक अर्थ लिएको छ: यसले केवल सुरक्षा कमजोरीहरूको परीक्षण मात्र होइन, अन्य प्रणाली त्रुटिहरूको परीक्षण पनि समेट्छ, जस्तै सम्भावित हानिकारक सामग्रीको सिर्जना। AI प्रणालीहरू नयाँ जोखिमहरू लिएर आउँछन्, र रेड टीमिङ ती नयाँ जोखिमहरू बुझ्नको लागि आधारभूत छ, जस्तै प्रॉम्प्ट इन्जेक्शन र आधारहीन सामग्री उत्पादन। - [Microsoft AI Red Team building future of safer AI](https://www.microsoft.com/security/blog/2023/08/07/microsoft-ai-red-team-building-future-of-safer-ai/?WT.mc_id=academic-105485-koreyst)
[![Guidance and resources for red teaming](../../../translated_images/13-AI-red-team.642ed54689d7e8a4d83bdf0635768c4fd8aa41ea539d8e3ffe17514aec4b4824.ne.png)]()

तल माइक्रोसफ्टको AI रेड टीम कार्यक्रमलाई आकार दिने मुख्य दृष्टिकोणहरू छन्।

1. **AI रेड टीमिङको व्यापक दायरा:**
   AI रेड टीमिङ अब सुरक्षा र जिम्मेवार AI (RAI) दुवै नतिजाहरू समेट्छ। परम्परागत रूपमा, रेड टीमिङले सुरक्षा पक्षहरूमा ध्यान केन्द्रित गर्थ्यो, मोडेललाई एक भेक्टरको रूपमा हेर्ने (जस्तै, आधारभूत मोडेल चोरी गर्ने)। तर, AI प्रणालीहरूले नयाँ सुरक्षा कमजोरीहरू (जस्तै, प्रॉम्प्ट इन्जेक्शन, विषाक्तता) ल्याउँछन्, जसले विशेष ध्यान आवश्यक बनाउँछ। सुरक्षा बाहेक, AI रेड टीमिङले निष्पक्षता सम्बन्धी मुद्दाहरू (जस्तै, रूढीवादी सोच) र हानिकारक सामग्री (जस्तै, हिंसाको प्रशंसा) पनि जाँच गर्छ। यी समस्याहरूलाई छिटो पहिचान गर्दा सुरक्षा लगानीलाई प्राथमिकता दिन सजिलो हुन्छ।
2. **दुष्ट र सामान्य असफलताहरू:**
   AI रेड टीमिङले दुष्ट र सामान्य दुवै दृष्टिकोणबाट असफलताहरूलाई विचार गर्छ। उदाहरणका लागि, नयाँ Bing को रेड टीमिङ गर्दा हामीले मात्र दुष्ट प्रतिद्वन्द्वीहरूले प्रणालीलाई कसरी भत्काउन सक्छन् भनेर होइन, सामान्य प्रयोगकर्ताहरूले पनि कसरी समस्याग्रस्त वा हानिकारक सामग्री सामना गर्न सक्छन् भनेर अन्वेषण गर्छौं। परम्परागत सुरक्षा रेड टीमिङ जस्तो जहाँ मुख्य रूपमा दुष्ट पात्रहरूमा ध्यान दिइन्छ, AI रेड टीमिङले फराकिलो व्यक्तित्व र सम्भावित असफलताहरूलाई समेट्छ।
3. **AI प्रणालीहरूको गतिशील प्रकृति:**
   AI अनुप्रयोगहरू निरन्तर विकास भइरहेका हुन्छन्। ठूलो भाषा मोडेल अनुप्रयोगहरूमा, विकासकर्ताहरू परिवर्तनशील आवश्यकताहरूमा अनुकूल हुन्छन्। निरन्तर रेड टीमिङले जोखिमहरूमा सतर्कता र अनुकूलन सुनिश्चित गर्छ।

AI रेड टीमिङ सबै कुरा समेट्ने छैन र यसलाई [role-based access control (RBAC)](https://learn.microsoft.com/azure/ai-services/openai/how-to/role-based-access-control?WT.mc_id=academic-105485-koreyst) जस्ता अतिरिक्त नियन्त्रणहरू र व्यापक डेटा व्यवस्थापन समाधानहरूको पूरक रूपमा हेर्नुपर्छ। यो सुरक्षा रणनीतिलाई पूरक बनाउनको लागि हो जसले सुरक्षित र जिम्मेवार AI समाधानहरू प्रयोगमा ल्याउँछ, जसले गोपनीयता र सुरक्षा ध्यानमा राख्छ र पूर्वाग्रह, हानिकारक सामग्री र गलत सूचनालाई कम गर्न प्रयास गर्छ जसले प्रयोगकर्ताको विश्वासमा असर पार्न सक्छ।

यहाँ थप पढ्नका लागि सूची छ जसले तपाईंलाई रेड टीमिङले कसरी तपाईंको AI प्रणालीहरूमा जोखिमहरू पहिचान र कम गर्न मद्दत गर्न सक्छ बुझ्न सहयोग गर्नेछ:

- [ठूला भाषा मोडेलहरू (LLMs) र तिनका अनुप्रयोगहरूको लागि रेड टीमिङ योजना बनाउने](https://learn.microsoft.com/azure/ai-services/openai/concepts/red-teaming?WT.mc_id=academic-105485-koreyst)
- [OpenAI रेड टीमिङ नेटवर्क के हो?](https://openai.com/blog/red-teaming-network?WT.mc_id=academic-105485-koreyst)
- [AI रेड टीमिङ - सुरक्षित र जिम्मेवार AI समाधान निर्माणका लागि एक प्रमुख अभ्यास](https://rodtrent.substack.com/p/ai-red-teaming?WT.mc_id=academic-105485-koreyst)
- MITRE को [ATLAS (Adversarial Threat Landscape for Artificial-Intelligence Systems)](https://atlas.mitre.org/?WT.mc_id=academic-105485-koreyst), वास्तविक विश्वमा AI प्रणालीहरूमा आक्रमण गर्ने प्रतिद्वन्द्वीहरूले प्रयोग गर्ने रणनीति र प्रविधिहरूको ज्ञानकोष।

## ज्ञान जाँच

डेटा अखण्डता कायम राख्न र दुरुपयोग रोक्न के राम्रो उपाय हुन सक्छ?

1. डेटा पहुँच र डेटा व्यवस्थापनका लागि कडा role-based नियन्त्रणहरू राख्नुहोस्
1. डेटा गलत प्रतिनिधित्व वा दुरुपयोग रोक्न डेटा लेबलिङ कार्यान्वयन र अडिट गर्नुहोस्
1. तपाईंको AI पूर्वाधारले सामग्री फिल्टरिङ समर्थन गर्छ भन्ने सुनिश्चित गर्नुहोस्

उत्तर: 1, यी तीनै सिफारिसहरू राम्रो भए तापनि, प्रयोगकर्ताहरूलाई उचित डेटा पहुँच अधिकार दिनु LLMs द्वारा प्रयोग गरिने डेटाको हेरफेर र गलत प्रतिनिधित्व रोक्न धेरै मद्दत गर्छ।

## 🚀 चुनौती

AI को युगमा कसरी [संवेदनशील जानकारीलाई शासन र सुरक्षा गर्ने](https://learn.microsoft.com/training/paths/purview-protect-govern-ai/?WT.mc_id=academic-105485-koreyst) बारे थप पढ्नुहोस्।

## राम्रो काम, आफ्नो सिकाइ जारी राख्नुहोस्

यो पाठ पूरा गरेपछि, हाम्रो [Generative AI Learning संग्रह](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) हेर्नुहोस् र आफ्नो Generative AI ज्ञानलाई अझ उचाइमा पुर्‍याउनुहोस्!

पाठ १४ मा जानुहोस् जहाँ हामी [Generative AI अनुप्रयोग जीवनचक्र](../14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst) हेर्नेछौं!

**अस्वीकरण**:  
यो दस्तावेज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) प्रयोग गरी अनुवाद गरिएको हो। हामी शुद्धताका लागि प्रयासरत छौं, तर कृपया ध्यान दिनुहोस् कि स्वचालित अनुवादमा त्रुटि वा अशुद्धता हुन सक्छ। मूल दस्तावेज यसको मूल भाषामा नै अधिकारिक स्रोत मानिनु पर्छ। महत्वपूर्ण जानकारीका लागि व्यावसायिक मानव अनुवाद सिफारिस गरिन्छ। यस अनुवादको प्रयोगबाट उत्पन्न कुनै पनि गलतफहमी वा गलत व्याख्याका लागि हामी जिम्मेवार छैनौं।