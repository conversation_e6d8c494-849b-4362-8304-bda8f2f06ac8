<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "e2861bbca91c0567ef32bc77fe054f9e",
  "translation_date": "2025-07-09T16:08:48+00:00",
  "source_file": "15-rag-and-vector-databases/README.md",
  "language_code": "ne"
}
-->
# Retrieval Augmented Generation (RAG) र भेक्टर डेटाबेसहरू

[![Retrieval Augmented Generation (RAG) र भेक्टर डेटाबेसहरू](../../../translated_images/15-lesson-banner.ac49e59506175d4fc6ce521561dab2f9ccc6187410236376cfaed13cde371b90.ne.png)](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst)

सर्च एप्लिकेसनको पाठमा, हामीले छोटकरीमा सिक्यौं कि कसरी आफ्नो डाटा ठूलो भाषा मोडेलहरू (LLMs) मा समावेश गर्ने। यस पाठमा, हामी आफ्नो LLM एप्लिकेसनमा डाटा कसरी आधारभूत बनाउने, प्रक्रिया कसरी काम गर्छ र डाटा भण्डारण गर्ने तरिकाहरू, जस्तै embeddings र टेक्स्ट दुवै, बारेमा थप गहिराइमा जान्छौं।

> **भिडियो चाँडै आउनेछ**

## परिचय

यस पाठमा हामीले निम्न विषयहरू समेट्नेछौं:

- RAG के हो र किन AI (कृत्रिम बुद्धिमत्ता) मा यसको प्रयोग गरिन्छ भन्ने परिचय।

- भेक्टर डेटाबेसहरू के हुन् बुझ्ने र हाम्रो एप्लिकेसनका लागि एउटा बनाउने।

- RAG लाई एप्लिकेसनमा कसरी समावेश गर्ने व्यावहारिक उदाहरण।

## सिकाइका लक्ष्यहरू

यस पाठ पूरा गरेपछि, तपाईं सक्षम हुनुहुनेछ:

- डाटा पुनःप्राप्ति र प्रशोधनमा RAG को महत्व व्याख्या गर्न।

- RAG एप्लिकेसन सेटअप गर्न र आफ्नो डाटालाई LLM मा आधारभूत बनाउन।

- LLM एप्लिकेसनहरूमा RAG र भेक्टर डेटाबेसहरूको प्रभावकारी समावेश।

## हाम्रो परिदृश्य: आफ्नै डाटासँग हाम्रो LLM हरूलाई सशक्त बनाउँदै

यस पाठका लागि, हामी हाम्रो आफ्नै नोटहरू शिक्षा स्टार्टअपमा थप्न चाहन्छौं, जसले च्याटबोटलाई विभिन्न विषयहरूमा थप जानकारी प्राप्त गर्न मद्दत गर्छ। हामीसँग भएका नोटहरू प्रयोग गरेर, सिक्नेहरूले राम्रोसँग अध्ययन गर्न र विभिन्न विषयहरू बुझ्न सक्नेछन्, जसले परीक्षाको तयारी सजिलो बनाउँछ। हाम्रो परिदृश्य बनाउन हामीले प्रयोग गर्नेछौं:

- `Azure OpenAI:` हाम्रो च्याटबोट बनाउन प्रयोग हुने LLM

- `AI for beginners' lesson on Neural Networks:` यो डाटा हो जसमा हामी हाम्रो LLM लाई आधारभूत बनाउनेछौं

- `Azure AI Search` र `Azure Cosmos DB:` हाम्रो डाटा भण्डारण गर्न र सर्च इन्डेक्स बनाउन प्रयोग हुने भेक्टर डेटाबेस

प्रयोगकर्ताहरूले आफ्ना नोटहरूबाट अभ्यास क्विजहरू बनाउन, पुनरावृत्ति फ्ल्यास कार्डहरू तयार पार्न र संक्षिप्त सारांशहरू बनाउन सक्नेछन्। सुरु गर्नका लागि, RAG के हो र कसरी काम गर्छ हेर्नुहोस्:

## Retrieval Augmented Generation (RAG)

LLM संचालित च्याटबोटले प्रयोगकर्ताको प्रश्नहरूलाई प्रक्रिया गरी जवाफहरू उत्पादन गर्छ। यो अन्तरक्रियात्मक हुन डिजाइन गरिएको छ र प्रयोगकर्तासँग विभिन्न विषयहरूमा संवाद गर्छ। तर यसको जवाफहरू प्रदान गरिएको सन्दर्भ र यसको आधारभूत प्रशिक्षण डाटामा सीमित हुन्छन्। उदाहरणका लागि, GPT-4 को ज्ञान कटअफ सेप्टेम्बर 2021 हो, जसको अर्थ यो समयपछि भएका घटनाहरूको जानकारी छैन। साथै, LLM प्रशिक्षणमा प्रयोग गरिएको डाटामा व्यक्तिगत नोटहरू वा कम्पनीको उत्पादन म्यानुअल जस्ता गोप्य जानकारीहरू समावेश हुँदैनन्।

### RAG (Retrieval Augmented Generation) कसरी काम गर्छ

![RAG कसरी काम गर्छ देखाउने चित्र](../../../translated_images/how-rag-works.f5d0ff63942bd3a638e7efee7a6fce7f0787f6d7a1fca4e43f2a7a4d03cde3e0.ne.png)

मानौं तपाईंले आफ्नो नोटहरूबाट क्विजहरू बनाउने च्याटबोट तैनाथ गर्न चाहनुहुन्छ, तब तपाईंलाई ज्ञान आधारसँग जडान आवश्यक पर्छ। यहीँ RAG मद्दत गर्छ। RAG यसरी काम गर्छ:

- **ज्ञान आधार:** पुनःप्राप्ति अघि, यी कागजातहरूलाई सङ्कलन र पूर्वप्रक्रिया गर्नुपर्छ, सामान्यतया ठूलो कागजातलाई साना भागहरूमा विभाजन गरी, तिनीहरूलाई टेक्स्ट embedding मा रूपान्तरण गरी डेटाबेसमा भण्डारण गरिन्छ।

- **प्रयोगकर्ता प्रश्न:** प्रयोगकर्ताले प्रश्न सोध्छ।

- **पुनःप्राप्ति:** प्रयोगकर्ताले प्रश्न सोध्दा, embedding मोडेलले हाम्रो ज्ञान आधारबाट सान्दर्भिक जानकारी खोज्छ र थप सन्दर्भ प्रदान गर्छ जुन प्रॉम्प्टमा समावेश गरिन्छ।

- **वृद्धि गरिएको उत्पादन:** LLM ले प्राप्त डाटाको आधारमा आफ्नो जवाफ सुधार गर्छ। यसले पूर्व-प्रशिक्षित डाटा मात्र नभई थपिएको सन्दर्भबाट प्राप्त सान्दर्भिक जानकारीमा आधारित जवाफ दिन अनुमति दिन्छ। LLM ले त्यसपछि प्रयोगकर्ताको प्रश्नको जवाफ फर्काउँछ।

![RAG को आर्किटेक्चर देखाउने चित्र](../../../translated_images/encoder-decode.f2658c25d0eadee2377bb28cf3aee8b67aa9249bf64d3d57bb9be077c4bc4e1a.ne.png)

RAG को आर्किटेक्चर ट्रान्सफर्मरहरू प्रयोग गरेर दुई भागमा बनाइएको हुन्छ: एक encoder र एक decoder। उदाहरणका लागि, प्रयोगकर्ताले प्रश्न सोध्दा, इनपुट टेक्स्टलाई शब्दहरूको अर्थ समेट्ने भेक्टरहरूमा 'encode' गरिन्छ र ती भेक्टरहरूलाई हाम्रो कागजात इन्डेक्समा 'decode' गरी प्रयोगकर्ताको प्रश्नमा आधारित नयाँ टेक्स्ट उत्पादन गरिन्छ। LLM ले आउटपुट उत्पादन गर्न encoder-decoder मोडेल दुवै प्रयोग गर्छ।

प्रस्तावित कागजात अनुसार RAG लागू गर्दा दुई तरिका छन्: [Retrieval-Augmented Generation for Knowledge intensive NLP Tasks](https://arxiv.org/pdf/2005.11401.pdf?WT.mc_id=academic-105485-koreyst):

- **_RAG-Sequence_**: प्राप्त कागजातहरू प्रयोग गरेर प्रयोगकर्ताको प्रश्नको सबैभन्दा उपयुक्त जवाफ अनुमान लगाउने।

- **RAG-Token**: कागजातहरू प्रयोग गरेर अर्को टोकन उत्पादन गर्ने, त्यसपछि प्रयोगकर्ताको प्रश्नको जवाफ दिन तिनीहरूलाई पुनःप्राप्त गर्ने।

### किन RAG प्रयोग गर्ने?

- **जानकारीको समृद्धि:** टेक्स्ट जवाफहरू अद्यावधिक र वर्तमान राख्छ। यसले डोमेन-विशेष कार्यहरूमा प्रदर्शन सुधार गर्छ किनभने यसले आन्तरिक ज्ञान आधारमा पहुँच दिन्छ।

- **गलत जानकारी कम गर्ने:** ज्ञान आधारमा रहेको **परीक्षणयोग्य डाटा** प्रयोग गरेर प्रयोगकर्ताको प्रश्नलाई सन्दर्भ प्रदान गर्छ।

- **लागत-कुशल:** LLM लाई फाइन-ट्युन गर्ने भन्दा यो अधिक आर्थिक हुन्छ।

## ज्ञान आधार सिर्जना गर्दै

हाम्रो एप्लिकेसन हाम्रो व्यक्तिगत डाटामा आधारित छ, जस्तै AI For Beginners को Neural Network पाठ।

### भेक्टर डेटाबेसहरू

परम्परागत डेटाबेसहरू भन्दा फरक, भेक्टर डेटाबेसहरू विशेष गरी embedded भेक्टरहरू भण्डारण, व्यवस्थापन र खोज गर्न डिजाइन गरिएका डेटाबेस हुन्। यसले कागजातहरूको सङ्ख्यात्मक प्रतिनिधित्व भण्डारण गर्छ। डाटालाई सङ्ख्यात्मक embeddings मा विभाजन गर्दा हाम्रो AI प्रणालीलाई डाटा बुझ्न र प्रक्रिया गर्न सजिलो हुन्छ।

हामी embeddings लाई भेक्टर डेटाबेसहरूमा भण्डारण गर्छौं किनभने LLM हरूले स्वीकार गर्ने टोकनहरूको संख्या सीमित हुन्छ। सम्पूर्ण embeddings LLM मा पठाउन नसकिने भएकाले, हामीले तिनीहरूलाई साना भागहरूमा विभाजन गर्नुपर्छ र प्रयोगकर्ताले प्रश्न सोध्दा, सबैभन्दा मिल्दोजुल्दो embeddings प्रॉम्प्टसँगै फर्काइन्छ। chunking ले LLM मार्फत पठाइने टोकनहरूको संख्या घटाएर लागत पनि कम गर्छ।

केही लोकप्रिय भेक्टर डेटाबेसहरूमा Azure Cosmos DB, Clarifyai, Pinecone, Chromadb, ScaNN, Qdrant र DeepLake समावेश छन्। तपाईं Azure CLI प्रयोग गरेर Azure Cosmos DB मोडेल निम्न आदेशबाट बनाउन सक्नुहुन्छ:

```bash
az login
az group create -n <resource-group-name> -l <location>
az cosmosdb create -n <cosmos-db-name> -r <resource-group-name>
az cosmosdb list-keys -n <cosmos-db-name> -g <resource-group-name>
```

### टेक्स्टबाट embeddings सम्म

हामीले डाटा भण्डारण गर्नु अघि, यसलाई भेक्टर embeddings मा रूपान्तरण गर्नुपर्छ। यदि तपाईं ठूलो कागजात वा लामो टेक्स्टसँग काम गर्दै हुनुहुन्छ भने, तपाईंले अपेक्षित प्रश्नहरूका आधारमा तिनीहरूलाई chunk गर्न सक्नुहुन्छ। chunking वाक्य स्तरमा वा अनुच्छेद स्तरमा गर्न सकिन्छ। chunking ले वरिपरिका शब्दहरूबाट अर्थ निकाल्ने भएकाले, तपाईं chunk मा थप सन्दर्भ थप्न सक्नुहुन्छ, जस्तै कागजातको शीर्षक वा chunk अघि वा पछि केही टेक्स्ट समावेश गरेर। तपाईं यसरी डाटा chunk गर्न सक्नुहुन्छ:

```python
def split_text(text, max_length, min_length):
    words = text.split()
    chunks = []
    current_chunk = []

    for word in words:
        current_chunk.append(word)
        if len(' '.join(current_chunk)) < max_length and len(' '.join(current_chunk)) > min_length:
            chunks.append(' '.join(current_chunk))
            current_chunk = []

    # If the last chunk didn't reach the minimum length, add it anyway
    if current_chunk:
        chunks.append(' '.join(current_chunk))

    return chunks
```

chunk गरेपछि, हामी विभिन्न embedding मोडेलहरू प्रयोग गरेर टेक्स्टलाई embed गर्न सक्छौं। केही मोडेलहरूमा word2vec, OpenAI को ada-002, Azure Computer Vision र अन्य धेरै समावेश छन्। कुन मोडेल प्रयोग गर्ने भन्ने कुरा तपाईंले प्रयोग गर्ने भाषा, समावेश गरिएको सामग्रीको प्रकार (टेक्स्ट/छवि/अडियो), इनपुटको आकार र embedding आउटपुटको लम्बाइमा निर्भर गर्दछ।

OpenAI को `text-embedding-ada-002` मोडेल प्रयोग गरेर embed गरिएको टेक्स्टको उदाहरण:

![cat शब्दको embedding](../../../translated_images/cat.74cbd7946bc9ca380a8894c4de0c706a4f85b16296ffabbf52d6175df6bf841e.ne.png)

## पुनःप्राप्ति र भेक्टर खोज

प्रयोगकर्ताले प्रश्न सोध्दा, retriever ले यसलाई query encoder प्रयोग गरी भेक्टरमा रूपान्तरण गर्छ, त्यसपछि हाम्रो कागजात सर्च इन्डेक्समा सम्बन्धित भेक्टरहरू खोज्छ जुन इनपुटसँग मेल खान्छ। पूरा भएपछि, यो इनपुट भेक्टर र कागजात भेक्टरहरूलाई टेक्स्टमा रूपान्तरण गरी LLM मा पठाउँछ।

### पुनःप्राप्ति

पुनःप्राप्ति तब हुन्छ जब प्रणाली छिटो खोजी मापदण्ड पूरा गर्ने कागजातहरू फेला पार्न खोज्छ। retriever को लक्ष्य त्यस्ता कागजातहरू ल्याउनु हो जसले सन्दर्भ प्रदान गर्छ र तपाईंको डाटामा LLM लाई आधारभूत बनाउँछ।

हाम्रो डेटाबेसमा खोजी गर्ने विभिन्न तरिकाहरू छन्:

- **Keyword search** - टेक्स्ट खोजीका लागि प्रयोग गरिन्छ।

- **Semantic search** - शब्दहरूको अर्थ प्रयोग गर्छ।

- **Vector search** - कागजातहरूलाई embedding मोडेल प्रयोग गरी भेक्टर प्रतिनिधित्वमा रूपान्तरण गर्छ। पुनःप्राप्ति प्रयोगकर्ताको प्रश्नसँग सबैभन्दा नजिकका भेक्टर भएका कागजातहरू खोजेर गरिन्छ।

- **Hybrid** - keyword र vector खोजी दुवैको संयोजन।

पुनःप्राप्तिमा चुनौती तब आउँछ जब डेटाबेसमा प्रश्नसँग मिल्दोजुल्दो जवाफ हुँदैन, प्रणालीले सबैभन्दा उपयुक्त जानकारी फर्काउँछ। यद्यपि, तपाईं सान्दर्भिकताको अधिकतम दूरी सेट गर्न वा keyword र vector खोजी दुवैलाई मिलाउने hybrid खोजी प्रयोग गर्न सक्नुहुन्छ। यस पाठमा हामी hybrid खोजी प्रयोग गर्नेछौं, जसले दुवै vector र keyword खोजीलाई संयोजन गर्छ। हामी हाम्रो डाटालाई dataframe मा भण्डारण गर्नेछौं जसमा chunk र embeddings दुवै समावेश हुनेछन्।

### भेक्टर समानता

retriever ले ज्ञान डेटाबेसमा नजिक रहेका embeddings खोज्छ, सबैभन्दा नजिकको छिमेकी, किनभने ती टेक्स्टहरू समान हुन्छन्। प्रयोगकर्ताले प्रश्न सोध्दा, त्यो पहिले embed गरिन्छ र त्यसपछि समान embeddings सँग मिलाइन्छ। विभिन्न भेक्टरहरू कति समान छन् मापन गर्न सामान्यतया cosine similarity प्रयोग गरिन्छ, जुन दुई भेक्टरबीचको कोणमा आधारित हुन्छ।

हामी समानता मापन गर्न अन्य विकल्पहरू पनि प्रयोग गर्न सक्छौं, जस्तै Euclidean distance जुन भेक्टरको अन्त्य बिन्दुहरू बीचको सिधा रेखा हो र dot product जुन दुई भेक्टरका सम्बन्धित तत्त्वहरूको गुणनफलको योग हो।

### सर्च इन्डेक्स

पुनःप्राप्ति गर्दा, हामीले खोजी गर्नु अघि हाम्रो ज्ञान आधारको लागि सर्च इन्डेक्स बनाउनुपर्छ। इन्डेक्सले हाम्रो embeddings भण्डारण गर्छ र ठूलो डेटाबेसमा पनि सबैभन्दा समान chunk छिटो फेला पार्न सक्छ। हामी हाम्रो इन्डेक्स स्थानीय रूपमा यसरी बनाउन सक्छौं:

```python
from sklearn.neighbors import NearestNeighbors

embeddings = flattened_df['embeddings'].to_list()

# Create the search index
nbrs = NearestNeighbors(n_neighbors=5, algorithm='ball_tree').fit(embeddings)

# To query the index, you can use the kneighbors method
distances, indices = nbrs.kneighbors(embeddings)
```

### पुनःक्रमबद्ध (Re-ranking)

डेटाबेसमा प्रश्न सोधिसकेपछि, तपाईंले परिणामहरू सबैभन्दा सान्दर्भिकबाट क्रमबद्ध गर्न आवश्यक पर्न सक्छ। पुनःक्रमबद्ध गर्ने LLM ले मेशिन लर्निङ प्रयोग गरी खोजी परिणामहरूको सान्दर्भिकता सुधार गर्छ र तिनीहरूलाई सबैभन्दा सान्दर्भिकबाट क्रमबद्ध गर्छ। Azure AI Search प्रयोग गर्दा, पुनःक्रमबद्ध स्वचालित रूपमा semantic reranker द्वारा गरिन्छ। नजिकका छिमेकीहरू प्रयोग गरेर पुनःक्रमबद्ध कसरी काम गर्छ भन्ने उदाहरण:

```python
# Find the most similar documents
distances, indices = nbrs.kneighbors([query_vector])

index = []
# Print the most similar documents
for i in range(3):
    index = indices[0][i]
    for index in indices[0]:
        print(flattened_df['chunks'].iloc[index])
        print(flattened_df['path'].iloc[index])
        print(flattened_df['distances'].iloc[index])
    else:
        print(f"Index {index} not found in DataFrame")
```

## सबै कुरा एकसाथ ल्याउँदै

अन्तिम चरणमा, हामी हाम्रो LLM लाई समावेश गर्छौं ताकि हामीले प्राप्त गर्ने जवाफहरू हाम्रो डाटामा आधारित हुन सकून्। हामी यसलाई यसरी कार्यान्वयन गर्न सक्छौं:

```python
user_input = "what is a perceptron?"

def chatbot(user_input):
    # Convert the question to a query vector
    query_vector = create_embeddings(user_input)

    # Find the most similar documents
    distances, indices = nbrs.kneighbors([query_vector])

    # add documents to query  to provide context
    history = []
    for index in indices[0]:
        history.append(flattened_df['chunks'].iloc[index])

    # combine the history and the user input
    history.append(user_input)

    # create a message object
    messages=[
        {"role": "system", "content": "You are an AI assistant that helps with AI questions."},
        {"role": "user", "content": history[-1]}
    ]

    # use chat completion to generate a response
    response = openai.chat.completions.create(
        model="gpt-4",
        temperature=0.7,
        max_tokens=800,
        messages=messages
    )

    return response.choices[0].message

chatbot(user_input)
```

## हाम्रो एप्लिकेसनको मूल्याङ्कन

### मूल्याङ्कन मेट्रिक्स

- प्रदान गरिएका जवाफहरूको गुणस्तर: जवाफ प्राकृतिक, प्रवाहपूर्ण र मानवीय जस्तो सुनिन्छ कि छैन।

- डाटाको आधारभूतता: जवाफ प्रदान गरिएको कागजातहरूबाट आएको हो कि होइन मूल्याङ्कन।

- सान्दर्भिकता: जवाफ सोधिएको प्रश्नसँग मेल खान्छ र सम्बन्धित छ कि छैन मूल्याङ्कन।

- प्रवाहशीलता - जवाफ व्याकरणिक रूपमा बुझ्न योग्य छ कि छैन।

## RAG (Retrieval Augmented Generation) र भेक्टर डेटाबेसहरू प्रयोगका केसहरू

धेरै फरक-फरक प्रयोगका केसहरू छन् जहाँ function calls ले तपाईंको एप्लिकेसन सुधार गर्न सक्छ, जस्तै:

- प्रश्न र उत्तर: तपाईंको कम्पनीको डाटालाई च्याटमा आधारभूत बनाउने जसलाई कर्मचारीहरूले प्रश्न सोध्न प्रयोग गर्न सक्छन्।

- सिफारिस प्रणालीहरू: जहाँ तपाईं सबैभन्दा मिल्दोजुल्दो मानहरू जस्तै चलचित्र, रेस्टुरेन्टहरू आदि मिलाउने प्रणाली बनाउन सक्नुहुन्छ।

- च्याटबोट सेवा: तपाईंले च्याट इतिहास भण्डारण गर्न र प्रयोगकर्ताको डाटामा आधारित संवादलाई व्यक्तिगत बनाउन सक्नुहुन्छ।

- भेक्टर embeddings मा आधारित छवि खोज, छवि पहिचान र असामान्यता पत्ता लगाउन उपयोगी।

## सारांश

हामीले RAG का आधारभूत क्षेत्रहरू समेट्यौं, जस्तै हाम्रो डाटा एप्लिकेसनमा थप्ने, प्रयोगकर्ता प्रश्न र आउटपुट। RAG सिर्जना सजिलो बनाउन, तपाईं Semanti Kernel, Langchain वा Autogen जस्ता फ्रेमवर्कहरू प्रयोग गर्न सक्नुहुन्छ।

## असाइनमेन्ट

Retrieval Augmented Generation (RAG) को सिकाइ जारी राख्न तपाईंले बनाउन सक्नुहुन्छ:

- आफ्नो रोजाइको फ्रेमवर्क प्रयोग गरी एप्लिकेसनको फ्रन्ट-एन्ड बनाउनुहोस्।

- LangChain वा Semantic Kernel मध्ये कुनै एक फ्रेमवर्क प्रयोग गरी आफ्नो एप्लिकेसन पुनःनिर्माण गर्नुहोस्।

पाठ पूरा गर्नुभएकोमा बधाई छ 👏।

## सिकाइ यहाँ रोकिँदैन, यात्रा जारी राख्नुहोस्

यस पाठ पूरा गरेपछि, हाम्रो [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) हेर्नुहोस् र आफ्नो Generative AI ज्ञानलाई अझ उचाइमा पुर्‍याउनुहोस्!

**अस्वीकरण**:  
यो दस्तावेज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) प्रयोग गरी अनुवाद गरिएको हो। हामी शुद्धताका लागि प्रयासरत छौं, तर कृपया ध्यान दिनुहोस् कि स्वचालित अनुवादमा त्रुटि वा अशुद्धता हुन सक्छ। मूल दस्तावेज यसको मूल भाषामा नै अधिकारिक स्रोत मानिनुपर्छ। महत्वपूर्ण जानकारीका लागि व्यावसायिक मानव अनुवाद सिफारिस गरिन्छ। यस अनुवादको प्रयोगबाट उत्पन्न कुनै पनि गलतफहमी वा गलत व्याख्याका लागि हामी जिम्मेवार छैनौं।