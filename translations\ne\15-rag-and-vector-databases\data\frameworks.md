<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:29:35+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "ne"
}
-->
# न्यूरल नेटवर्क फ्रेमवर्कहरू

जसरी हामीले पहिले नै सिकिसकेका छौं, न्यूरल नेटवर्कहरूलाई प्रभावकारी रूपमा प्रशिक्षण दिनका लागि हामीले दुई कामहरू गर्नुपर्छ:

* टेन्सरहरूमा अपरेसन गर्न, जस्तै गुणा, जोड, र केही फङ्सनहरू जस्तै सिग्मोइड वा सोफ्टम्याक्स गणना गर्न
* सबै अभिव्यक्तिहरूको ग्रेडियन्टहरू गणना गर्न, जसले ग्रेडियन्ट डिसेन्ट अप्टिमाइजेसन गर्न मद्दत गर्छ

`numpy` लाइब्रेरीले पहिलो भाग गर्न सक्छ, तर हामीलाई ग्रेडियन्टहरू गणना गर्ने कुनै मेकानिजम चाहिन्छ। हाम्रो फ्रेमवर्कमा जुन हामीले अघिल्लो भागमा विकास गरेका थियौं, हामीले सबै डेरिभेटिभ फङ्सनहरू `backward` मेथड भित्र म्यानुअली प्रोग्राम गर्नुपर्थ्यो, जुन ब्याकप्रोपागेसन गर्छ। आदर्श रूपमा, फ्रेमवर्कले हामीलाई *कुनै पनि अभिव्यक्ति* को ग्रेडियन्ट गणना गर्ने अवसर दिनुपर्छ जुन हामी परिभाषित गर्न सक्छौं।

अर्को महत्वपूर्ण कुरा भनेको GPU वा अन्य विशेष कम्प्युटिङ युनिटहरू, जस्तै TPU, मा गणना गर्न सक्नु हो। गहिरो न्यूरल नेटवर्क प्रशिक्षणमा *धेरै* गणना आवश्यक पर्छ, र ती गणनाहरू GPU मा समानान्तर रूपमा चलाउन सक्नु धेरै महत्वपूर्ण छ।

> ✅ 'समानान्तर बनाउनु' भनेको गणनाहरूलाई धेरै उपकरणहरूमा वितरण गर्नु हो।

हालका सबैभन्दा लोकप्रिय दुई न्यूरल फ्रेमवर्कहरू हुन्: TensorFlow र PyTorch। दुवैले CPU र GPU दुवैमा टेन्सरहरूसँग काम गर्नको लागि लो-लेभल API प्रदान गर्छन्। लो-लेभल API माथि, उच्च-लेभल API पनि छ, जसलाई क्रमशः Keras र PyTorch Lightning भनिन्छ।

Low-Level API | TensorFlow| PyTorch
--------------|-------------------------------------|--------------------------------
High-level API| Keras| PyTorch

**लो-लेभल API** दुवै फ्रेमवर्कमा तपाईंलाई **कम्प्युटेशनल ग्राफहरू** बनाउन अनुमति दिन्छ। यो ग्राफले कसरी आउटपुट (सामान्यतया लस फङ्सन) दिइएको इनपुट प्यारामिटरहरूसँग गणना गर्ने भनेर परिभाषित गर्छ, र GPU उपलब्ध भएमा त्यसमा गणना गर्न पठाउन सकिन्छ। यस कम्प्युटेशनल ग्राफलाई डिफरेन्सियेट गर्ने र ग्रेडियन्टहरू गणना गर्ने फङ्सनहरू छन्, जुन पछि मोडेल प्यारामिटरहरू अप्टिमाइज गर्न प्रयोग गर्न सकिन्छ।

**उच्च-लेभल API** ले न्यूरल नेटवर्कहरूलाई प्रायः **लेयरहरूको अनुक्रम** को रूपमा हेर्छ, र धेरै न्यूरल नेटवर्कहरू बनाउन सजिलो बनाउँछ। मोडेल प्रशिक्षण प्रायः डाटा तयार पार्न र त्यसपछि `fit` फङ्सन कल गर्न आवश्यक पर्छ।

उच्च-लेभल API ले तपाईंलाई धेरै छिटो सामान्य न्यूरल नेटवर्कहरू बनाउन अनुमति दिन्छ, धेरै विवरणहरूमा चिन्ता नगरी। त्यस्तै, लो-लेभल API ले प्रशिक्षण प्रक्रियामा धेरै नियन्त्रण दिन्छ, त्यसैले अनुसन्धानमा नयाँ न्यूरल नेटवर्क आर्किटेक्चरहरूमा धेरै प्रयोग गरिन्छ।

यो बुझ्न पनि महत्वपूर्ण छ कि तपाईं दुवै API सँगै प्रयोग गर्न सक्नुहुन्छ, जस्तै तपाईंले आफ्नो नेटवर्क लेयर आर्किटेक्चर लो-लेभल API प्रयोग गरेर विकास गर्न सक्नुहुन्छ, र त्यसलाई उच्च-लेभल API प्रयोग गरेर बनेको ठूलो नेटवर्क भित्र प्रयोग गर्न सक्नुहुन्छ। वा तपाईंले उच्च-लेभल API प्रयोग गरेर लेयरहरूको अनुक्रमको रूपमा नेटवर्क परिभाषित गर्न सक्नुहुन्छ, र त्यसपछि आफ्नो लो-लेभल प्रशिक्षण लूप प्रयोग गरेर अप्टिमाइजेसन गर्न सक्नुहुन्छ। दुवै API ले एउटै आधारभूत अवधारणाहरू प्रयोग गर्छन् र सँगै राम्रोसँग काम गर्न डिजाइन गरिएका छन्।

## सिकाइ

यस कोर्समा, हामीले अधिकांश सामग्री PyTorch र TensorFlow दुवैका लागि उपलब्ध गराएका छौं। तपाईं आफ्नो मनपर्ने फ्रेमवर्क छान्न सक्नुहुन्छ र त्यससँग सम्बन्धित नोटबुकहरू मात्र हेर्न सक्नुहुन्छ। यदि तपाईं कुन फ्रेमवर्क छान्ने निश्चित हुनुहुन्न भने, इन्टरनेटमा रहेको **PyTorch vs. TensorFlow** सम्बन्धी छलफलहरू पढ्न सक्नुहुन्छ। तपाईं दुवै फ्रेमवर्कहरूलाई पनि हेरेर राम्रो बुझ्न सक्नुहुन्छ।

जहाँ सम्भव छ, हामी सजिलो बनाउन उच्च-लेभल API प्रयोग गर्नेछौं। तर हामीलाई लाग्छ न्यूरल नेटवर्कहरू कसरी काम गर्छन् भन्ने आधारभूत कुरा बुझ्न महत्त्वपूर्ण छ, त्यसैले सुरुमा हामी लो-लेभल API र टेन्सरहरूसँग काम गरेर सुरु गर्छौं। यदि तपाईं छिटो अघि बढ्न चाहनुहुन्छ र यी विवरणहरूमा धेरै समय खर्च गर्न चाहनुहुन्न भने, तपाईं ती भागहरू छोडेर सिधै उच्च-लेभल API नोटबुकहरूमा जान सक्नुहुन्छ।

## ✍️ अभ्यासहरू: फ्रेमवर्कहरू

तपाईंको सिकाइलाई तलका नोटबुकहरूमा जारी राख्नुहोस्:

Low-Level API | TensorFlow+Keras नोटबुक | PyTorch
--------------|-------------------------------------|--------------------------------
High-level API| Keras | *PyTorch Lightning*

फ्रेमवर्कहरूमा दक्ष भएपछि, आउनुहोस् ओभरफिटिङको अवधारणा पुनः सम्झौं।

# ओभरफिटिङ

ओभरफिटिङ मेसिन लर्निङमा अत्यन्तै महत्वपूर्ण अवधारणा हो, र यसलाई सही तरिकाले बुझ्नु धेरै आवश्यक छ!

तलको समस्या विचार गर्नुहोस्, जसमा ५ वटा डटहरू (ग्राफमा `x` द्वारा देखाइएको) लाई नजिकबाट मिलाउने प्रयास गरिएको छ:

!linear | overfit
-------------------------|--------------------------
**रेखीय मोडेल, २ प्यारामिटरहरू** | **गैर-रेखीय मोडेल, ७ प्यारामिटरहरू**
प्रशिक्षण त्रुटि = 5.3 | प्रशिक्षण त्रुटि = 0
मान्यकरण त्रुटि = 5.1 | मान्यकरण त्रुटि = 20

* बाँया तर्फ, हामी राम्रो सिधा रेखा मिलाएको देख्छौं। किनभने प्यारामिटरहरूको संख्या उपयुक्त छ, मोडेलले पोइन्ट वितरणको आधारभूत विचार सही तरिकाले बुझ्छ।
* दायाँ तर्फ, मोडेल धेरै शक्तिशाली छ। किनभने हामीसँग मात्र ५ पोइन्टहरू छन् र मोडेलमा ७ प्यारामिटरहरू छन्, यसले सबै पोइन्टहरूमा जान मिलाएर प्रशिक्षण त्रुटि ० बनाउन सक्छ। तर यसले मोडेललाई डाटाको सही ढाँचा बुझ्नबाट रोक्छ, त्यसैले मान्यकरण त्रुटि धेरै उच्च हुन्छ।

मोडेलको समृद्धि (प्यारामिटरहरूको संख्या) र प्रशिक्षण नमूनाहरूको संख्या बीच सही सन्तुलन मिलाउनु धेरै महत्वपूर्ण छ।

## किन ओभरफिटिङ हुन्छ

  * पर्याप्त प्रशिक्षण डाटा नहुनु
  * मोडेल धेरै शक्तिशाली हुनु
  * इनपुट डाटामा धेरै आवाज (noise) हुनु

## ओभरफिटिङ कसरी पत्ता लगाउने

माथिको ग्राफबाट देख्न सकिन्छ, ओभरफिटिङलाई धेरै कम प्रशिक्षण त्रुटि र धेरै उच्च मान्यकरण त्रुटिद्वारा पत्ता लगाउन सकिन्छ। सामान्यतया प्रशिक्षणको क्रममा हामीले प्रशिक्षण र मान्यकरण दुवै त्रुटिहरू घट्दै गएको देख्छौं, र कुनै बिन्दुमा मान्यकरण त्रुटि घट्न रोक्छ र बढ्न थाल्छ। यो ओभरफिटिङको संकेत हो, र यो संकेत हो कि हामीले सम्भवतः यस बिन्दुमा प्रशिक्षण रोक्नु पर्छ (वा कम्तिमा मोडेलको स्न्यापशट लिनु पर्छ)।

overfitting

## ओभरफिटिङ कसरी रोक्ने

यदि तपाईंले ओभरफिटिङ देख्नुभयो भने, तपाईंले निम्न मध्ये कुनै एक गर्न सक्नुहुन्छ:

 * प्रशिक्षण डाटाको मात्रा बढाउनुहोस्
 * मोडेलको जटिलता घटाउनुहोस्
 * Dropout जस्ता कुनै नियमितीकरण प्रविधि प्रयोग गर्नुहोस्, जुन हामी पछि हेर्नेछौं।

## ओभरफिटिङ र बायस-भेरिएन्स ट्रेडअफ

ओभरफिटिङ वास्तवमा तथ्याङ्कमा रहेको एउटा सामान्य समस्या हो जसलाई बायस-भेरिएन्स ट्रेडअफ भनिन्छ। यदि हामी मोडेलमा सम्भावित त्रुटिका स्रोतहरू विचार गर्छौं भने, दुई प्रकारका त्रुटिहरू देख्न सकिन्छ:

* **बायस त्रुटिहरू** हाम्रो एल्गोरिदमले प्रशिक्षण डाटाबीचको सम्बन्ध सही तरिकाले समात्न नसक्दा हुन्छन्। यो हाम्रो मोडेल पर्याप्त शक्तिशाली नभएको कारण हुन सक्छ (**अन्डरफिटिङ**)।
* **भेरिएन्स त्रुटिहरू** मोडेलले इनपुट डाटामा रहेको आवाजलाई अर्थपूर्ण सम्बन्धको सट्टा नक्कल गर्दा हुन्छ (**ओभरफिटिङ**)।

प्रशिक्षणको क्रममा, बायस त्रुटि घट्छ (हाम्रो मोडेल डाटालाई नजिकबाट मिलाउन सिक्दैछ), र भेरिएन्स त्रुटि बढ्छ। ओभरफिटिङ रोक्न प्रशिक्षण रोक्नु महत्त्वपूर्ण हुन्छ - वा त म्यानुअली (जब हामी ओभरफिटिङ पत्ता लगाउँछौं) वा स्वचालित रूपमा (नियमितीकरण प्रविधि प्रयोग गरेर)।

## निष्कर्ष

यस पाठमा, तपाईंले दुई सबैभन्दा लोकप्रिय AI फ्रेमवर्कहरू, TensorFlow र PyTorch का विभिन्न API हरू बीचको भिन्नता सिक्नुभयो। साथै, तपाईंले एक अत्यन्तै महत्वपूर्ण विषय, ओभरफिटिङ, को बारेमा पनि जान्नुभयो।

## 🚀 चुनौती

साथै रहेका नोटबुकहरूमा तल 'tasks' पाइनेछ; नोटबुकहरूमा काम गर्दै ती कार्यहरू पूरा गर्नुहोस्।

## समीक्षा र आत्म-अध्ययन

तलका विषयहरूमा केही अनुसन्धान गर्नुहोस्:

- TensorFlow
- PyTorch
- ओभरफिटिङ

आफैलाई यी प्रश्नहरू सोध्नुहोस्:

- TensorFlow र PyTorch बीच के फरक छ?
- ओभरफिटिङ र अन्डरफिटिङ बीच के फरक छ?

## असाइनमेन्ट

यस ल्याबमा, तपाईंलाई PyTorch वा TensorFlow प्रयोग गरेर एकल र बहु-लेयर पूर्ण रूपमा जडित नेटवर्कहरू प्रयोग गरी दुई वर्गीकरण समस्याहरू समाधान गर्न भनिएको छ।

**अस्वीकरण**:  
यो दस्तावेज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) प्रयोग गरी अनुवाद गरिएको हो। हामी शुद्धताका लागि प्रयासरत छौं, तर कृपया ध्यान दिनुहोस् कि स्वचालित अनुवादमा त्रुटि वा अशुद्धता हुन सक्छ। मूल दस्तावेज यसको मूल भाषामा आधिकारिक स्रोत मानिनु पर्छ। महत्वपूर्ण जानकारीका लागि व्यावसायिक मानव अनुवाद सिफारिस गरिन्छ। यस अनुवादको प्रयोगबाट उत्पन्न कुनै पनि गलतफहमी वा गलत व्याख्याका लागि हामी जिम्मेवार छैनौं।