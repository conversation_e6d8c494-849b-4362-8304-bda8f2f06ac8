<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:44:27+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "ne"
}
-->
# न्यूरल नेटवर्कहरूको परिचय। बहु-स्तरीय पर्सेप्ट्रोन

अघिल्लो भागमा, तपाईंले सबैभन्दा सरल न्यूरल नेटवर्क मोडेल - एक-स्तरीय पर्सेप्ट्रोन, जुन एक रेखीय दुई-वर्ग वर्गीकरण मोडेल हो, सिक्नुभयो।

यस भागमा हामी यो मोडेललाई अझ लचिलो फ्रेमवर्कमा विस्तार गर्नेछौं, जसले हामीलाई अनुमति दिनेछ:

* दुई-वर्गको अतिरिक्त **बहु-वर्ग वर्गीकरण** गर्न
* वर्गीकरणको अतिरिक्त **रेग्रेशन समस्या** समाधान गर्न
* रेखीय रूपमा अलग गर्न नसकिने वर्गहरूलाई अलग गर्न

हामी पायथनमा हाम्रो आफ्नै मोड्युलर फ्रेमवर्क पनि विकास गर्नेछौं जसले विभिन्न न्यूरल नेटवर्क आर्किटेक्चरहरू निर्माण गर्न मद्दत गर्नेछ।

## मेशिन लर्निङको औपचारिकता

मेशिन लर्निङ समस्यालाई औपचारिक बनाउन सुरु गरौं। मानौं हामीसँग प्रशिक्षण डेटासेट **X** र लेबलहरू **Y** छन्, र हामीले यस्तो मोडेल *f* बनाउनुपर्छ जुन सबैभन्दा सही पूर्वानुमान गर्न सकोस्। पूर्वानुमानको गुणस्तर **लस फंक्शन** ℒ द्वारा मापन गरिन्छ। तलका लस फंक्शनहरू प्रायः प्रयोग गरिन्छन्:

* रेग्रेशन समस्याका लागि, जहाँ हामीले कुनै संख्या पूर्वानुमान गर्नुपर्छ, हामी **पूर्ण त्रुटि** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>| वा **वर्ग त्रुटि** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup> प्रयोग गर्न सक्छौं
* वर्गीकरणका लागि, हामी **0-1 लस** (जुन मोडेलको **सटीकता** जस्तै हो) वा **लजिस्टिक लस** प्रयोग गर्छौं।

एक-स्तरीय पर्सेप्ट्रोनको लागि, फंक्शन *f* लाई रेखीय फंक्शन *f(x)=wx+b* को रूपमा परिभाषित गरिएको थियो (यहाँ *w* तौल म्याट्रिक्स हो, *x* इनपुट विशेषताहरूको भेक्टर हो, र *b* बायस भेक्टर हो)। विभिन्न न्यूरल नेटवर्क आर्किटेक्चरहरूका लागि, यो फंक्शन अझ जटिल रूप लिन सक्छ।

> वर्गीकरणको अवस्थामा, प्रायः नेटवर्कको आउटपुटको रूपमा सम्बन्धित वर्गहरूको सम्भावना प्राप्त गर्न चाहिन्छ। कुनै पनि संख्यालाई सम्भावनामा रूपान्तरण गर्न (जस्तै आउटपुटलाई सामान्यीकरण गर्न), हामी प्रायः **softmax** फंक्शन σ प्रयोग गर्छौं, र फंक्शन *f* हुन्छ *f(x)=σ(wx+b)*

माथिको *f* को परिभाषामा, *w* र *b* लाई **प्यारामिटरहरू** θ=⟨*w,b*⟩ भनिन्छ। डेटासेट ⟨**X**,**Y**⟩ दिइएपछि, हामी प्यारामिटर θ को रूपमा सम्पूर्ण डेटासेटमा कुल त्रुटि गणना गर्न सक्छौं।

> ✅ **न्यूरल नेटवर्क प्रशिक्षणको लक्ष्य प्यारामिटर θ परिवर्तन गरेर त्रुटि न्यूनतम गर्नु हो**

## ग्रेडियन्ट डिसेन्ट अप्टिमाइजेशन

फंक्शन अप्टिमाइजेशनको एक प्रसिद्ध विधि छ जसलाई **ग्रेडियन्ट डिसेन्ट** भनिन्छ। यसको विचार हो कि हामी प्यारामिटरहरूसँग सम्बन्धित लस फंक्शनको व्युत्पन्न (बहु-आयामी अवस्थामा यसलाई **ग्रेडियन्ट** भनिन्छ) गणना गर्न सक्छौं, र प्यारामिटरहरू यसरी परिवर्तन गर्न सक्छौं कि त्रुटि घटोस्। यसलाई यसरी औपचारिक बनाउन सकिन्छ:

* प्यारामिटरहरूलाई केही यादृच्छिक मान w<sup>(0)</sup>, b<sup>(0)</sup> बाट सुरु गर्नुहोस्
* तलको चरण धेरै पटक दोहोर्याउनुहोस्:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

प्रशिक्षणको क्रममा, अप्टिमाइजेशन चरणहरू सम्पूर्ण डेटासेटलाई ध्यानमा राखेर गणना गरिनुपर्छ (लस सबै प्रशिक्षण नमूनाहरूको योगफलको रूपमा गणना हुन्छ भन्ने सम्झनुहोस्)। तर वास्तविक जीवनमा हामी डेटासेटका साना भागहरू लिन्छौं जसलाई **मिनीब्याचहरू** भनिन्छ, र डाटाको उपसमूहमा आधारित ग्रेडियन्टहरू गणना गर्छौं। किनभने प्रत्येक पटक उपसमूह यादृच्छिक रूपमा लिइन्छ, यस्तो विधिलाई **स्टोकेस्टिक ग्रेडियन्ट डिसेन्ट** (SGD) भनिन्छ।

## बहु-स्तरीय पर्सेप्ट्रोन र ब्याकप्रोपागेसन

जसरी माथि देखियो, एक-स्तरीय नेटवर्कले रेखीय रूपमा अलग गर्न सकिने वर्गहरूलाई वर्गीकरण गर्न सक्छ। धनी मोडेल बनाउन, हामी नेटवर्कका धेरै तहहरू संयोजन गर्न सक्छौं। गणितीय रूपमा यसको अर्थ हुन्छ कि फंक्शन *f* अझ जटिल रूप लिन्छ र धेरै चरणहरूमा गणना गरिन्छ:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

यहाँ, α एक **अरेखीय सक्रियता फंक्शन** हो, σ softmax फंक्शन हो, र प्यारामिटरहरू θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*> हुन्।

ग्रेडियन्ट डिसेन्ट एल्गोरिदम उस्तै रहनेछ, तर ग्रेडियन्टहरू गणना गर्न गाह्रो हुनेछ। चेन डिफरेन्सिएसन नियम अनुसार, हामी व्युत्पन्नहरू यसरी गणना गर्न सक्छौं:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ प्यारामिटरहरूसँग सम्बन्धित लस फंक्शनका व्युत्पन्नहरू गणना गर्न चेन डिफरेन्सिएसन नियम प्रयोग गरिन्छ।

ध्यान दिनुहोस् कि यी सबै अभिव्यक्तिहरूको बाँया भाग उस्तै हुन्छ, त्यसैले हामी प्रभावकारी रूपमा लस फंक्शनबाट सुरु गरेर कम्प्युटेशनल ग्राफमा "पछाडि" जाँदै व्युत्पन्नहरू गणना गर्न सक्छौं। यसैले बहु-स्तरीय पर्सेप्ट्रोन प्रशिक्षण विधिलाई **ब्याकप्रोपागेसन** वा 'ब्याकप्रोप' भनिन्छ।

> TODO: छवि उद्धरण

> ✅ हामी हाम्रो नोटबुक उदाहरणमा ब्याकप्रोपलाई धेरै विस्तृत रूपमा समेट्नेछौं।

## निष्कर्ष

यस पाठमा, हामीले हाम्रो आफ्नै न्यूरल नेटवर्क लाइब्रेरी बनायौं, र यसलाई सरल दुई-आयामी वर्गीकरण कार्यका लागि प्रयोग गर्यौं।

## 🚀 चुनौती

साथै रहेको नोटबुकमा, तपाईंले बहु-स्तरीय पर्सेप्ट्रोनहरू निर्माण र प्रशिक्षण गर्न आफ्नो फ्रेमवर्क कार्यान्वयन गर्नुहुनेछ। तपाईंले आधुनिक न्यूरल नेटवर्कहरू कसरी काम गर्छन् भन्ने विस्तृत रूपमा देख्न सक्नुहुनेछ।

OwnFramework नोटबुकमा जानुहोस् र त्यसलाई पूरा गर्नुहोस्।

## समीक्षा र आत्म-अध्ययन

ब्याकप्रोपागेसन AI र ML मा सामान्य एल्गोरिदम हो, जसलाई थप गहिराइमा अध्ययन गर्नु लाभदायक हुन्छ।

## असाइनमेन्ट

यस प्रयोगशालामा, तपाईंले यस पाठमा निर्माण गरेको फ्रेमवर्क प्रयोग गरेर MNIST हस्तलिखित अंक वर्गीकरण समस्या समाधान गर्नुपर्नेछ।

* निर्देशनहरू
* नोटबुक

**अस्वीकरण**:  
यो दस्तावेज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) प्रयोग गरी अनुवाद गरिएको हो। हामी शुद्धताका लागि प्रयासरत छौं, तर कृपया ध्यान दिनुहोस् कि स्वचालित अनुवादमा त्रुटि वा अशुद्धता हुन सक्छ। मूल दस्तावेज यसको मूल भाषामा नै अधिकारिक स्रोत मानिनुपर्छ। महत्वपूर्ण जानकारीका लागि व्यावसायिक मानव अनुवाद सिफारिस गरिन्छ। यस अनुवादको प्रयोगबाट उत्पन्न कुनै पनि गलतफहमी वा गलत व्याख्याका लागि हामी जिम्मेवार छैनौं।