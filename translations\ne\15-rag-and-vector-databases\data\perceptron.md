<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T16:56:21+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "ne"
}
-->
# न्यूरल नेटवर्कहरू परिचय: पर्सेप्ट्रोन

आधुनिक न्यूरल नेटवर्कसँग मिल्दोजुल्दो केही लागू गर्ने पहिलो प्रयासहरू मध्ये एक 1957 मा कर्नेल एरोनॉटिकल ल्याबोरेटरीका फ्र्यांक रोसेनब्लाटले गरेका थिए। यो एउटा हार्डवेयर कार्यान्वयन थियो जसलाई "Mark-1" भनिन्थ्यो, जसले त्रिभुज, वर्ग र वृत्तजस्ता प्रारम्भिक ज्यामितीय आकृतिहरूलाई चिन्ने डिजाइन गरिएको थियो।

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='<PERSON>'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> विकिपिडियाबाट तस्वीरहरू

एक इनपुट छवि 20x20 फोटोसेल एर्रेले प्रतिनिधित्व गरिएको थियो, त्यसैले न्यूरल नेटवर्कसँग 400 इनपुटहरू र एउटा द्विआधारी आउटपुट थियो। एउटा सरल नेटवर्कमा एउटा न्यूरोन थियो, जसलाई **थ्रेसहोल्ड लजिक युनिट** पनि भनिन्छ। न्यूरल नेटवर्कका तौलहरू पोटेन्सियोमिटर जस्तै काम गर्थे जसलाई प्रशिक्षण चरणमा म्यानुअली समायोजन गर्नुपर्थ्यो।

> ✅ पोटेन्सियोमिटर एउटा उपकरण हो जसले प्रयोगकर्तालाई सर्किटको प्रतिरोध समायोजन गर्न अनुमति दिन्छ।

> द न्यूयोर्क टाइम्सले त्यो समयमा पर्सेप्ट्रोनबारे लेखेको थियो: *इलेक्ट्रोनिक कम्प्युटरको भ्रूण जसलाई [नेभी] ले आशा गरेको छ कि यो हिँड्न, बोल्न, हेर्न, लेख्न, आफैंलाई पुनरुत्पादन गर्न र आफ्नो अस्तित्वको चेतना राख्न सक्षम हुनेछ।*

## पर्सेप्ट्रोन मोडेल

मानौं हाम्रो मोडेलमा N वटा विशेषताहरू छन्, जस अवस्थामा इनपुट भेक्टर N आकारको भेक्टर हुनेछ। पर्सेप्ट्रोन एउटा **द्विआधारी वर्गीकरण** मोडेल हो, अर्थात् यसले दुई वर्गका इनपुट डाटाबीच फरक गर्न सक्छ। हामी मान्नेछौं कि प्रत्येक इनपुट भेक्टर x को लागि हाम्रो पर्सेप्ट्रोनको आउटपुट +1 वा -1 मध्ये एक हुनेछ, वर्ग अनुसार। आउटपुट निम्न सूत्र प्रयोग गरेर गणना गरिन्छ:

y(x) = f(w<sup>T</sup>x)

जहाँ f एउटा स्टेप सक्रियता कार्य हो

## पर्सेप्ट्रोन प्रशिक्षण

पर्सेप्ट्रोनलाई प्रशिक्षण दिन हामीलाई तौल भेक्टर w पत्ता लगाउनुपर्छ जसले अधिकांश मानहरूलाई सही वर्गीकरण गर्छ, अर्थात् सबैभन्दा सानो **त्रुटि** ल्याउँछ। यो त्रुटि **पर्सेप्ट्रोन मापदण्ड** द्वारा यसरी परिभाषित गरिएको छ:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

जहाँ:

* योगफल ती प्रशिक्षण डाटा बिन्दुहरू i मा लिइन्छ जसले गलत वर्गीकरण गर्छन्
* x<sub>i</sub> इनपुट डाटा हो, र t<sub>i</sub> नकारात्मक र सकारात्मक उदाहरणहरूको लागि क्रमशः -1 वा +1 हुन्छ।

यो मापदण्डलाई तौल w को कार्यको रूपमा मानिन्छ, र हामी यसलाई न्यूनतम बनाउनुपर्छ। प्रायः, **ग्रेडियन्ट डिसेन्ट** नामक विधि प्रयोग गरिन्छ, जसमा हामी केही प्रारम्भिक तौल w<sup>(0)</sup> बाट सुरु गर्छौं, र प्रत्येक चरणमा तौलहरू निम्न सूत्र अनुसार अपडेट गर्छौं:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

यहाँ η लाई **शिक्षण दर** भनिन्छ, र ∇E(w) E को **ग्रेडियन्ट** जनाउँछ। ग्रेडियन्ट गणना गरेपछि, हामीसँग हुन्छ:

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

पाइथनमा एल्गोरिदम यसरी देखिन्छ:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## निष्कर्ष

यस पाठमा, तपाईंले पर्सेप्ट्रोनबारे सिक्नुभयो, जुन एउटा द्विआधारी वर्गीकरण मोडेल हो, र तौल भेक्टर प्रयोग गरेर यसलाई कसरी प्रशिक्षण दिने भन्ने कुरा।

## 🚀 चुनौती

यदि तपाईं आफ्नो पर्सेप्ट्रोन बनाउन चाहनुहुन्छ भने, Microsoft Learn मा रहेको Azure ML डिजाइनर प्रयोग गर्ने यो प्रयोगशाला प्रयास गर्नुहोस्।

## समीक्षा र आत्म-अध्ययन

हामी कसरी पर्सेप्ट्रोनलाई खेलौना समस्या र वास्तविक जीवनका समस्याहरू समाधान गर्न प्रयोग गर्न सक्छौं भनेर हेर्न र सिकाइ जारी राख्न - Perceptron नोटबुकमा जानुहोस्।

यहाँ पर्सेप्ट्रोनहरूबारे एउटा रोचक लेख पनि छ।

## असाइनमेन्ट

यस पाठमा, हामीले द्विआधारी वर्गीकरण कार्यका लागि पर्सेप्ट्रोन लागू गरेका छौं, र यसलाई दुई हस्तलिखित अंकहरू बीच वर्गीकरण गर्न प्रयोग गरेका छौं। यस प्रयोगशालामा, तपाईंलाई पूर्ण रूपमा अंक वर्गीकरणको समस्या समाधान गर्न भनिएको छ, अर्थात् दिइएको छविसँग सबैभन्दा सम्भावित अंक कुन हो निर्धारण गर्नुहोस्।

* निर्देशनहरू
* नोटबुक

**अस्वीकरण**:  
यो दस्तावेज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) प्रयोग गरी अनुवाद गरिएको हो। हामी शुद्धताका लागि प्रयासरत छौं भने पनि, कृपया ध्यान दिनुहोस् कि स्वचालित अनुवादमा त्रुटि वा अशुद्धता हुन सक्छ। मूल दस्तावेज यसको मूल भाषामा नै अधिकारिक स्रोत मानिनु पर्छ। महत्वपूर्ण जानकारीका लागि व्यावसायिक मानव अनुवाद सिफारिस गरिन्छ। यस अनुवादको प्रयोगबाट उत्पन्न कुनै पनि गलतफहमी वा गलत व्याख्याका लागि हामी जिम्मेवार छैनौं।