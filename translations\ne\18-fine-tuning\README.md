<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "68664f7e754a892ae1d8d5e2b7bd2081",
  "translation_date": "2025-07-09T17:40:15+00:00",
  "source_file": "18-fine-tuning/README.md",
  "language_code": "ne"
}
-->
[![Open Source Models](../../../translated_images/18-lesson-banner.f30176815b1a5074fce9cceba317720586caa99e24001231a92fd04eeb54a121.ne.png)](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst)

# तपाईंको LLM लाई फाइन-ट्यून गर्ने तरिका

ठूला भाषा मोडेलहरू प्रयोग गरेर जेनेरेटिभ AI अनुप्रयोगहरू निर्माण गर्दा नयाँ चुनौतीहरू आउँछन्। एउटा मुख्य समस्या भनेको मोडेलले प्रयोगकर्ताको अनुरोध अनुसार उत्पादन गरेको सामग्रीको प्रतिक्रिया गुणस्तर (सटीकता र सान्दर्भिकता) सुनिश्चित गर्नु हो। अघिल्ला पाठहरूमा, हामीले प्रॉम्प्ट इन्जिनियरिङ र रिट्रिभल-अग्मेन्टेड जेनेरेशन जस्ता प्रविधिहरूको चर्चा गर्यौं जसले समस्या समाधान गर्नको लागि _मोडेलको इनपुट प्रॉम्प्टलाई परिमार्जन_ गर्ने प्रयास गर्छन्।

आजको पाठमा, हामी तेस्रो प्रविधि, **फाइन-ट्यूनिङ** को बारेमा छलफल गर्नेछौं, जसले _थप डाटाको साथ मोडेललाई पुन: प्रशिक्षण_ गरेर चुनौती समाधान गर्ने प्रयास गर्छ। अब विस्तारमा जानौं।

## सिकाइका उद्देश्यहरू

यस पाठले पूर्व-प्रशिक्षित भाषा मोडेलहरूको फाइन-ट्यूनिङको अवधारणा परिचय गराउँछ, यस विधिको फाइदा र चुनौतीहरू अन्वेषण गर्छ, र तपाईंका जेनेरेटिभ AI मोडेलहरूको प्रदर्शन सुधार गर्न फाइन-ट्यूनिङ कहिले र कसरी प्रयोग गर्ने भन्ने बारे मार्गदर्शन प्रदान गर्छ।

यस पाठको अन्त्यसम्म, तपाईंले यी प्रश्नहरूको उत्तर दिन सक्नुपर्छ:

- भाषा मोडेलहरूको फाइन-ट्यूनिङ के हो?
- फाइन-ट्यूनिङ कहिले र किन उपयोगी हुन्छ?
- म पूर्व-प्रशिक्षित मोडेललाई कसरी फाइन-ट्यून गर्न सक्छु?
- फाइन-ट्यूनिङका सीमितताहरू के के हुन्?

तयार हुनुहुन्छ? सुरु गरौं।

## चित्र सहित मार्गदर्शन

हामीले के कभर गर्नेछौं भन्ने ठूलो तस्वीर जान्न चाहनुहुन्छ? यो चित्रित मार्गदर्शन हेर्नुहोस् जसले फाइन-ट्यूनिङको मुख्य अवधारणा र प्रेरणा सिक्नदेखि लिएर प्रक्रिया र उत्तम अभ्यासहरू बुझ्नसम्मको सिकाइ यात्रा वर्णन गर्छ। यो अन्वेषणका लागि रोचक विषय हो, त्यसैले आफ्नो स्व-अध्ययन यात्रालाई सहयोग गर्न थप लिंकहरूको लागि [स्रोतहरू](./RESOURCES.md?WT.mc_id=academic-105485-koreyst) पृष्ठ अवश्य हेर्नुहोस्!

![Illustrated Guide to Fine Tuning Language Models](../../../translated_images/18-fine-tuning-sketchnote.11b21f9ec8a703467a120cb79a28b5ac1effc8d8d9d5b31bbbac6b8640432e14.ne.png)

## भाषा मोडेलहरूको फाइन-ट्यूनिङ के हो?

परिभाषा अनुसार, ठूला भाषा मोडेलहरू इन्टरनेट लगायत विभिन्न स्रोतहरूबाट सङ्कलित ठूलो मात्रामा पाठमा _पूर्व-प्रशिक्षित_ हुन्छन्। हामीले अघिल्ला पाठहरूमा सिक्यौं कि प्रयोगकर्ताका प्रश्नहरू ("प्रॉम्प्टहरू") का लागि मोडेलको प्रतिक्रियाको गुणस्तर सुधार गर्न _प्रॉम्प्ट इन्जिनियरिङ_ र _रिट्रिभल-अग्मेन्टेड जेनेरेशन_ जस्ता प्रविधिहरू आवश्यक हुन्छन्।

एक लोकप्रिय प्रॉम्प्ट-इन्जिनियरिङ प्रविधि भनेको मोडेललाई प्रतिक्रिया के अपेक्षित छ भनेर थप निर्देशन दिनु हो, जसमा _निर्देशनहरू_ (स्पष्ट मार्गदर्शन) वा _केही उदाहरणहरू_ (अस्पष्ट मार्गदर्शन) दिन सकिन्छ। यसलाई _फ्यु-शट लर्निङ_ भनिन्छ तर यसका दुई सीमितताहरू छन्:

- मोडेलको टोकन सीमा तपाईंले दिन सक्ने उदाहरणहरूको संख्या सीमित गर्न सक्छ र प्रभावकारितामा बाधा पुर्‍याउन सक्छ।
- मोडेल टोकन लागतले प्रत्येक प्रॉम्प्टमा उदाहरण थप्न महँगो बनाउन सक्छ र लचिलोपन कम गर्न सक्छ।

फाइन-ट्यूनिङ भनेको मेसिन लर्निङ प्रणालीहरूमा सामान्य अभ्यास हो जहाँ हामी पूर्व-प्रशिक्षित मोडेललाई नयाँ डाटाको साथ पुन: प्रशिक्षण गरेर कुनै विशेष कार्यमा यसको प्रदर्शन सुधार गर्छौं। भाषा मोडेलहरूको सन्दर्भमा, हामी पूर्व-प्रशिक्षित मोडेललाई _कुनै विशेष कार्य वा अनुप्रयोग क्षेत्रका लागि चयनित उदाहरणहरूको सेटसँग_ फाइन-ट्यून गर्न सक्छौं र यसरी एउटा **अनुकूलित मोडेल** बनाउँछौं जुन त्यो कार्य वा क्षेत्रका लागि बढी सटीक र सान्दर्भिक हुन सक्छ। फाइन-ट्यूनिङको एउटा अतिरिक्त फाइदा भनेको यसले फ्यु-शट लर्निङका लागि आवश्यक उदाहरणहरूको संख्या घटाउन सक्छ - जसले टोकन प्रयोग र सम्बन्धित लागतहरू कम गर्छ।

## फाइन-ट्यूनिङ कहिले र किन गर्नुपर्छ?

_यस_ सन्दर्भमा, जब हामी फाइन-ट्यूनिङको कुरा गर्छौं, हामी **सुपरभाइज्ड** फाइन-ट्यूनिङको कुरा गर्दैछौं जहाँ पुन: प्रशिक्षण नयाँ डाटा थपेर गरिन्छ जुन मूल प्रशिक्षण डाटासेटको भाग थिएन। यो अनसुपरभाइज्ड फाइन-ट्यूनिङबाट फरक छ जहाँ मोडेललाई मूल डाटामा पुन: प्रशिक्षण गरिन्छ तर फरक हाइपरप्यारामिटरहरूसँग।

महत्त्वपूर्ण कुरा याद राख्नुपर्ने हो कि फाइन-ट्यूनिङ एक उन्नत प्रविधि हो जसले इच्छित परिणामहरू प्राप्त गर्न निश्चित स्तरको विशेषज्ञता आवश्यक पर्छ। यदि गलत तरिकाले गरियो भने, यसले अपेक्षित सुधार नदिन सक्छ र तपाईंको लक्षित क्षेत्रमा मोडेलको प्रदर्शनमा गिरावट पनि आउन सक्छ।

त्यसैले, "कसरी" फाइन-ट्यून गर्ने सिक्नु अघि, तपाईंले "किन" यो बाटो अपनाउने र "कहिले" फाइन-ट्यूनिङ प्रक्रिया सुरु गर्ने भन्ने जान्न आवश्यक छ। यी प्रश्नहरू आफैंलाई सोध्नुहोस्:

- **प्रयोग केस**: तपाईंको फाइन-ट्यूनिङको _प्रयोग केस_ के हो? हालको पूर्व-प्रशिक्षित मोडेलको कुन पक्ष सुधार गर्न चाहनुहुन्छ?
- **वैकल्पिकहरू**: तपाईंले चाहिएको नतिजा प्राप्त गर्न _अन्य प्रविधिहरू_ प्रयास गर्नुभएको छ? तिनीहरूलाई तुलना गर्न आधारको रूपमा प्रयोग गर्नुहोस्।
  - प्रॉम्प्ट इन्जिनियरिङ: सान्दर्भिक प्रॉम्प्ट प्रतिक्रियाका उदाहरणहरूसँग फ्यु-शट प्रॉम्प्टिङ जस्ता प्रविधिहरू प्रयास गर्नुहोस्। प्रतिक्रियाको गुणस्तर मूल्याङ्कन गर्नुहोस्।
  - रिट्रिभल अग्मेन्टेड जेनेरेशन: तपाईंको डाटाबाट खोजी गरेर प्राप्त प्रश्न परिणामहरूसँग प्रॉम्प्टहरू बढाउनुहोस्। प्रतिक्रियाको गुणस्तर मूल्याङ्कन गर्नुहोस्।
- **लागतहरू**: फाइन-ट्यूनिङका लागि लागतहरू पहिचान गर्नुभएको छ?
  - ट्युनबिलिटी - के पूर्व-प्रशिक्षित मोडेल फाइन-ट्यूनिङका लागि उपलब्ध छ?
  - प्रयास - प्रशिक्षण डाटा तयार पार्न, मोडेल मूल्याङ्कन र सुधार गर्न कति मेहनत लाग्छ?
  - कम्प्युट - फाइन-ट्यूनिङ काम चलाउन र फाइन-ट्यून गरिएको मोडेल तैनाथ गर्न कति कम्प्युट आवश्यक पर्छ?
  - डाटा - फाइन-ट्यूनिङ प्रभावका लागि पर्याप्त गुणस्तरका उदाहरणहरूमा पहुँच छ?
- **फाइदाहरू**: फाइन-ट्यूनिङका फाइदाहरू पुष्टि गर्नुभएको छ?
  - गुणस्तर - के फाइन-ट्यून गरिएको मोडेलले आधारभूत मोडेललाई पार गर्‍यो?
  - लागत - के यसले प्रॉम्प्टहरू सरल बनाउँदै टोकन प्रयोग घटाउँछ?
  - विस्तारयोग्यता - के तपाईं आधार मोडेललाई नयाँ क्षेत्रहरूका लागि पुनः प्रयोग गर्न सक्नुहुन्छ?

यी प्रश्नहरूको उत्तर दिएर, तपाईंले फाइन-ट्यूनिङ तपाईंको प्रयोग केसका लागि उपयुक्त हो कि होइन निर्णय गर्न सक्नुहुन्छ। आदर्श रूपमा, यो विधि तब मात्र मान्य हुन्छ जब फाइदाहरू लागतभन्दा बढी हुन्छन्। निर्णय गरेपछि, अब सोच्नुपर्ने हुन्छ कि तपाईं पूर्व-प्रशिक्षित मोडेललाई _कसरी_ फाइन-ट्यून गर्ने।

निर्णय प्रक्रियामा थप जानकारी चाहनुहुन्छ? [To fine-tune or not to fine-tune](https://www.youtube.com/watch?v=0Jo-z-MFxJs) हेर्नुहोस्।

## पूर्व-प्रशिक्षित मोडेललाई कसरी फाइन-ट्यून गर्ने?

पूर्व-प्रशिक्षित मोडेललाई फाइन-ट्यून गर्न तपाईंलाई चाहिन्छ:

- फाइन-ट्यून गर्नको लागि पूर्व-प्रशिक्षित मोडेल
- फाइन-ट्यूनिङका लागि प्रयोग गर्ने डाटासेट
- फाइन-ट्यूनिङ काम चलाउन प्रशिक्षण वातावरण
- फाइन-ट्यून गरिएको मोडेल तैनाथ गर्न होस्टिङ वातावरण

## फाइन-ट्यूनिङ कार्यान्वयनमा

तलका स्रोतहरूले चयनित मोडेल र चयनित डाटासेट प्रयोग गरेर वास्तविक उदाहरणमा तपाईंलाई चरण-दर-चरण ट्यूटोरियलहरू प्रदान गर्छन्। यी ट्यूटोरियलहरूमा काम गर्न, तपाईंलाई सम्बन्धित प्रदायकमा खाता र सम्बन्धित मोडेल तथा डाटासेटहरूमा पहुँच आवश्यक पर्छ।

| प्रदायक       | ट्यूटोरियल                                                                                                                                                                    | विवरण                                                                                                                                                                                                                                                                                                                                                                                                                             |
| ------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| OpenAI        | [How to fine-tune chat models](https://github.com/openai/openai-cookbook/blob/main/examples/How_to_finetune_chat_models.ipynb?WT.mc_id=academic-105485-koreyst)                 | `gpt-35-turbo` लाई विशेष क्षेत्र ("रेसिपी सहायक") का लागि प्रशिक्षण डाटा तयार पारेर, फाइन-ट्यूनिङ काम चलाएर, र फाइन-ट्यून गरिएको मोडेललाई इन्फरेन्सका लागि प्रयोग गर्ने तरिका सिक्नुहोस्।                                                                                                                                                                                                                                         |
| Azure OpenAI  | [GPT 3.5 Turbo fine-tuning tutorial](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python-new%2Ccommand-line?WT.mc_id=academic-105485-koreyst)    | Azure मा `gpt-35-turbo-0613` मोडेललाई फाइन-ट्यून गर्ने तरिका सिक्नुहोस्, जसमा प्रशिक्षण डाटा सिर्जना र अपलोड गर्ने, फाइन-ट्यूनिङ काम चलाउने, नयाँ मोडेल तैनाथ गर्ने र प्रयोग गर्ने चरणहरू समावेश छन्।                                                                                                                                                                                                                                   |
| Hugging Face  | [Fine-tuning LLMs with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                              | यो ब्लग पोस्टले तपाईंलाई खुला LLM (जस्तै `CodeLlama 7B`) लाई [transformers](https://huggingface.co/docs/transformers/index?WT.mc_id=academic-105485-koreyst) लाइब्रेरी र [Transformer Reinforcement Learning (TRL)](https://huggingface.co/docs/trl/index?WT.mc_id=academic-105485-koreyst) प्रयोग गरी Hugging Face मा खुला [datasets](https://huggingface.co/docs/datasets/index?WT.mc_id=academic-105485-koreyst) सँग कसरी फाइन-ट्यून गर्ने देखाउँछ। |
|               |                                                                                                                                                                               |                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| 🤗 AutoTrain  | [Fine-tuning LLMs with AutoTrain](https://github.com/huggingface/autotrain-advanced/?WT.mc_id=academic-105485-koreyst)                                                        | AutoTrain (वा AutoTrain Advanced) Hugging Face द्वारा विकास गरिएको पायथन लाइब्रेरी हो जसले धेरै फरक कार्यहरूका लागि फाइन-ट्यूनिङ गर्न अनुमति दिन्छ, जसमा LLM फाइन-ट्यूनिङ पनि समावेश छ। AutoTrain नो-कोड समाधान हो र फाइन-ट्यूनिङ तपाईंको आफ्नै क्लाउड, Hugging Face Spaces वा स्थानीय रूपमा गर्न सकिन्छ। यसले वेब-आधारित GUI, CLI र yaml कन्फिग फाइलमार्फत प्रशिक्षण समर्थन गर्छ।                                                                                 |
|               |                                                                                                                                                                               |                                                                                                                                                                                                                                                                                                                                                                                                                                   |

## असाइनमेन्ट

माथिका ट्यूटोरियलहरू मध्ये कुनै एक छान्नुहोस् र त्यसलाई पालना गर्नुहोस्। _हामी यी ट्यूटोरियलहरूको संस्करण Jupyter Notebooks मा यस रिपोजिटरीमा सन्दर्भका लागि राख्न सक्छौं। कृपया नवीनतम संस्करणहरूका लागि सिधै मूल स्रोतहरू प्रयोग गर्नुहोस्_।

## राम्रो काम! आफ्नो सिकाइ जारी राख्नुहोस्।

यस पाठ पूरा गरेपछि, हाम्रो [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) हेरेर आफ्नो जेनेरेटिभ AI ज्ञानलाई अझ उचाइमा पुर्‍याउनुहोस्!

बधाई छ!! तपाईंले यस कोर्सको v2 श्रृंखलाको अन्तिम पाठ पूरा गर्नुभयो! सिक्न र निर्माण गर्न कहिल्यै नरोकिनुहोस्। \*\*यस विषयका लागि थप सुझावहरूको सूचीका लागि [RESOURCES](RESOURCES.md?WT.mc_id=academic-105485-koreyst) पृष्ठ अवश्य हेर्नुहोस्।

हाम्रो v1 श्रृंखलाका पाठहरू पनि थप असाइनमेन्ट र अवधारणाहरू सहित अपडेट गरिएको छ। त्यसैले आफ्नो ज्ञान ताजा पार्न एक मिनेट निकाल्नुहोस् - र कृपया [तपाईंका प्रश्न र प्रतिक्रिया साझा गर्नुहोस्](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) जसले हामीलाई समुदायका लागि यी पाठहरू सुधार गर्न मद्दत गर्नेछ।

**अस्वीकरण**:  
यो दस्तावेज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) प्रयोग गरी अनुवाद गरिएको हो। हामी शुद्धताका लागि प्रयासरत छौं, तर कृपया ध्यान दिनुहोस् कि स्वचालित अनुवादमा त्रुटि वा अशुद्धता हुन सक्छ। मूल दस्तावेज यसको मूल भाषामा नै अधिकारिक स्रोत मानिनुपर्छ। महत्वपूर्ण जानकारीका लागि व्यावसायिक मानव अनुवाद सिफारिस गरिन्छ। यस अनुवादको प्रयोगबाट उत्पन्न कुनै पनि गलतफहमी वा गलत व्याख्याका लागि हामी जिम्मेवार छैनौं।