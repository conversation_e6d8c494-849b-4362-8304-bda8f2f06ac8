<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:59:22+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "ne"
}
-->
# आत्म-निर्देशित सिकाइका लागि स्रोतहरू

यो पाठ OpenAI र Azure OpenAI का मुख्य स्रोतहरूलाई सन्दर्भका रूपमा प्रयोग गरी तयार गरिएको हो, जसले शब्दावली र ट्युटोरियलहरू समेट्छ। यहाँ तपाईंको आफ्नै आत्म-निर्देशित सिकाइ यात्राका लागि केही गैर-सम्पूर्ण सूची प्रस्तुत गरिएको छ।

## १. प्राथमिक स्रोतहरू

| शीर्षक/लिङ्क                                                                                                                                                                                                                   | विवरण                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [OpenAI मोडेलहरूसँग फाइन-ट्युनिङ](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | फाइन-ट्युनिङले थोरै उदाहरणमा आधारित सिकाइभन्दा बढी उदाहरणहरूमा प्रशिक्षण गरेर सुधार गर्छ, जसले लागत बचत गर्छ, प्रतिक्रिया गुणस्तर सुधार्छ, र कम विलम्बता अनुरोधहरू सक्षम बनाउँछ। **OpenAI बाट फाइन-ट्युनिङको अवलोकन प्राप्त गर्नुहोस्।**                                                                                    |
| [Azure OpenAI सँग फाइन-ट्युनिङ के हो?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | **फाइन-ट्युनिङ के हो (धारणा)** बुझ्नुहोस्, किन यसमा ध्यान दिनुपर्छ (प्रेरणादायक समस्या), कुन डाटा प्रयोग गर्ने (प्रशिक्षण) र गुणस्तर मापन गर्ने तरिका।                                                                                                                                                                           |
| [फाइन-ट्युनिङसँग मोडेल अनुकूलन गर्नुहोस्](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI सेवा तपाईंलाई फाइन-ट्युनिङ प्रयोग गरी आफ्नै डाटासेटहरूमा मोडेलहरू अनुकूलन गर्न अनुमति दिन्छ। Azure AI Studio, Python SDK वा REST API प्रयोग गरी **फाइन-ट्युनिङ कसरी गर्ने (प्रक्रिया)** सिक्नुहोस्।                                                                                                                                |
| [LLM फाइन-ट्युनिङका लागि सिफारिसहरू](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM हरूले विशेष डोमेन, कार्य वा डाटासेटहरूमा राम्रो प्रदर्शन नगर्न सक्छन्, वा गलत वा भ्रमपूर्ण नतिजा दिन सक्छन्। **कहिले फाइन-ट्युनिङ विचार गर्ने** भन्ने कुरा बुझ्नुहोस्।                                                                                                                                  |
| [लगातार फाइन-ट्युनिङ](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | लगातार फाइन-ट्युनिङ भनेको पहिले नै फाइन-ट्युन गरिएको मोडेललाई आधार मोडेलको रूपमा चयन गरी नयाँ प्रशिक्षण उदाहरणहरूमा थप फाइन-ट्युनिङ गर्ने पुनरावृत्त प्रक्रिया हो।                                                                                                                                                     |
| [फाइन-ट्युनिङ र फंक्शन कलिङ](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | फंक्शन कलिङ उदाहरणहरूसँग आफ्नो मोडेल फाइन-ट्युन गर्दा, मोडेलको आउटपुट अझ सटीक र सुसंगत हुन्छ - समान ढाँचाका प्रतिक्रियाहरू र लागत बचतका साथ।                                                                                                                                        |
| [फाइन-ट्युनिङ मोडेलहरू: Azure OpenAI मार्गदर्शन](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Azure OpenAI मा **कुन मोडेलहरू फाइन-ट्युन गर्न सकिन्छ** र ती कुन क्षेत्रहरूमा उपलब्ध छन् भनेर बुझ्न यो तालिका हेर्नुहोस्। आवश्यक परे टोकन सीमा र प्रशिक्षण डाटा समाप्ति मिति पनि जाँच्नुहोस्।                                                                                                                            |
| [फाइन-ट्युन गर्ने कि नगर्ने? त्यो नै प्रश्न हो](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | यो ३० मिनेटको **अक्टोबर २०२३** को AI Show एपिसोडले फाइदाहरू, बेफाइदाहरू र व्यावहारिक सुझावहरू छलफल गर्छ, जसले तपाईंलाई निर्णय लिन मद्दत गर्छ।                                                                                                                                                                                        |
| [LLM फाइन-ट्युनिङ सुरु गर्ने तरिका](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | यो **AI Playbook** स्रोतले तपाईंलाई डाटा आवश्यकताहरू, ढाँचा बनाउने तरिका, हाइपरप्यारामिटर फाइन-ट्युनिङ र चुनौतीहरू/सीमाहरू बारे मार्गदर्शन गर्छ।                                                                                                                                                                         |
| **ट्युटोरियल**: [Azure OpenAI GPT3.5 Turbo फाइन-ट्युनिङ](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | नमूना फाइन-ट्युनिङ डाटासेट कसरी बनाउने, फाइन-ट्युनिङको तयारी गर्ने, फाइन-ट्युनिङ काम सिर्जना गर्ने, र Azure मा फाइन-ट्युन गरिएको मोडेल तैनाथ गर्ने तरिका सिक्नुहोस्।                                                                                                                                                                                    |
| **ट्युटोरियल**: [Azure AI Studio मा Llama 2 मोडेल फाइन-ट्युन गर्ने](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio ले तपाईंलाई ठूलो भाषा मोडेलहरूलाई आफ्नै डाटासेटहरूमा अनुकूलन गर्न अनुमति दिन्छ _कम कोड विकासकर्ताहरूका लागि उपयुक्त UI-आधारित कार्यप्रवाह प्रयोग गरी_। यो उदाहरण हेर्नुहोस्।                                                                                                                                                               |
| **ट्युटोरियल**: [Azure मा एकल GPU मा Hugging Face मोडेलहरू फाइन-ट्युन गर्ने](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | यो लेखले Azure DataBricks र Hugging Face Trainer पुस्तकालयहरू प्रयोग गरी Hugging Face ट्रान्सफर्मर पुस्तकालयमार्फत एकल GPU मा मोडेल कसरी फाइन-ट्युन गर्ने वर्णन गर्छ।                                                                                                                                                |
| **प्रशिक्षण:** [Azure Machine Learning सँग फाउन्डेशन मोडेल फाइन-ट्युन गर्ने](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Azure Machine Learning को मोडेल क्याटलगमा धेरै खुला स्रोत मोडेलहरू छन् जुन तपाईंले आफ्नो विशेष कार्यका लागि फाइन-ट्युन गर्न सक्नुहुन्छ। यो मोड्युल AzureML Generative AI सिकाइ मार्गबाट हो।                                                                                                                            |
| **ट्युटोरियल:** [Azure OpenAI फाइन-ट्युनिङ](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Microsoft Azure मा W&B प्रयोग गरी GPT-3.5 वा GPT-4 मोडेलहरू फाइन-ट्युन गर्दा मोडेल प्रदर्शनको विस्तृत ट्र्याकिङ र विश्लेषण सम्भव हुन्छ। यो मार्गदर्शिकाले OpenAI फाइन-ट्युनिङ गाइडका अवधारणाहरूलाई Azure OpenAI का लागि विशेष चरणहरू र सुविधाहरू सहित विस्तार गर्छ।                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## २. द्वितीयक स्रोतहरू

यो खण्डमा थप स्रोतहरू समावेश छन् जुन अन्वेषण गर्न लायक छन्, तर हामीले यस पाठमा समय नपाएर समेट्न सकेनौं। भविष्यमा यीलाई अर्को पाठमा वा द्वितीयक असाइनमेन्ट विकल्पको रूपमा समेट्न सकिन्छ। अहिलेका लागि, यी स्रोतहरूलाई प्रयोग गरी यस विषयमा आफ्नो विशेषज्ञता र ज्ञान बढाउनुहोस्।

| शीर्षक/लिङ्क                                                                                                                                                                                                            | विवरण                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI कुकबुक**: [च्याट मोडेल फाइन-ट्युनिङका लागि डाटा तयारी र विश्लेषण](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | यो नोटबुकले फाइन-ट्युनिङका लागि प्रयोग हुने च्याट डाटासेटलाई पूर्वप्रक्रिया र विश्लेषण गर्न उपकरणको रूपमा काम गर्छ। यसले ढाँचा त्रुटिहरू जाँच्छ, आधारभूत तथ्याङ्क दिन्छ, र फाइन-ट्युनिङ लागतका लागि टोकन गणना अनुमान लगाउँछ। हेर्नुहोस्: [gpt-3.5-turbo को फाइन-ट्युनिङ विधि](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)।                                                                                                                                                                   |
| **OpenAI कुकबुक**: [Qdrant सँग Retrieval Augmented Generation (RAG) को लागि फाइन-ट्युनिङ](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | यस नोटबुकको उद्देश्य OpenAI मोडेलहरूलाई Retrieval Augmented Generation (RAG) का लागि कसरी फाइन-ट्युन गर्ने विस्तृत उदाहरण प्रस्तुत गर्नु हो। हामी Qdrant र Few-Shot Learning पनि समावेश गर्नेछौं जसले मोडेल प्रदर्शन सुधार्ने र गलत जानकारी कम गर्नेछ।                                                                                                                                                                                                                                                                |
| **OpenAI कुकबुक**: [Weights & Biases सँग GPT फाइन-ट्युनिङ](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) AI विकासकर्ता प्लेटफर्म हो, जसले मोडेल प्रशिक्षण, फाइन-ट्युनिङ, र फाउन्डेशन मोडेलहरूको उपयोगका लागि उपकरणहरू प्रदान गर्छ। पहिले उनीहरूको [OpenAI फाइन-ट्युनिङ](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) गाइड पढ्नुहोस्, त्यसपछि कुकबुक अभ्यास गर्नुहोस्।                                                                                                                                                                                                                  |
| **समुदाय ट्युटोरियल** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - साना भाषा मोडेलहरूको लागि फाइन-ट्युनिङ                                                   | [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst) सँग भेट्नुहोस्, माइक्रोसफ्टको नयाँ सानो मोडेल, अत्यन्त शक्तिशाली र सानो। यो ट्युटोरियलले तपाईंलाई Phi-2 फाइन-ट्युन गर्ने तरिका देखाउनेछ, जसमा अनौठो डाटासेट बनाउने र QLoRA प्रयोग गरी मोडेल फाइन-ट्युन गर्ने प्रक्रिया समावेश छ।                                                                                                                                                                       |
| **Hugging Face ट्युटोरियल** [२०२४ मा Hugging Face सँग LLM कसरी फाइन-ट्युन गर्ने](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | यो ब्लग पोस्टले तपाईंलाई Hugging Face TRL, Transformers र डाटासेटहरू प्रयोग गरी खुला LLM हरू कसरी फाइन-ट्युन गर्ने देखाउँछ। तपाईंले प्रयोग केस परिभाषित गर्नुहुन्छ, विकास वातावरण सेटअप गर्नुहुन्छ, डाटासेट तयार गर्नुहुन्छ, मोडेल फाइन-ट्युन गर्नुहुन्छ, परीक्षण र मूल्याङ्कन गर्नुहुन्छ, र अन्ततः उत्पादनमा तैनाथ गर्नुहुन्छ।                                                                                                                                                                                                                                                                |
| **Hugging Face**: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)                                                                                            | [अत्याधुनिक मेसिन लर्निङ मोडेलहरूको छिटो र सजिलो प्रशिक्षण र तैनाथीकरण](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst) ल्याउँछ। रिपोमा Colab-अनुकूल ट्युटोरियलहरू र YouTube भिडियो मार्गदर्शन छन्, फाइन-ट्युनिङका लागि। **हालैको [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) अपडेट समावेश छ।** [AutoTrain कागजात](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) पढ्नुहोस्। |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**अस्वीकरण**:  
यो दस्तावेज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) प्रयोग गरी अनुवाद गरिएको हो। हामी शुद्धताका लागि प्रयासरत छौं, तर कृपया ध्यान दिनुहोस् कि स्वचालित अनुवादमा त्रुटि वा अशुद्धता हुन सक्छ। मूल दस्तावेज यसको मूल भाषामा नै अधिकारिक स्रोत मानिनु पर्छ। महत्वपूर्ण जानकारीका लागि व्यावसायिक मानव अनुवाद सिफारिस गरिन्छ। यस अनुवादको प्रयोगबाट उत्पन्न कुनै पनि गलतफहमी वा गलत व्याख्याका लागि हामी जिम्मेवार छैनौं।