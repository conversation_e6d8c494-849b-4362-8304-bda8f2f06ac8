<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:27:28+00:00",
  "source_file": "README.md",
  "language_code": "ne"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.ne.png)

### Generative AI एप्लिकेसनहरू बनाउन सुरु गर्न आवश्यक सबै कुरा सिकाउने २१ पाठहरू

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 बहुभाषी समर्थन

#### GitHub Action मार्फत समर्थित (स्वचालित र सधैं अपडेट हुने)

[French](../fr/README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](../ja/README.md) | [Korean](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](./README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](../el/README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](../he/README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](../sk/README.md) | [Romanian](../ro/README.md) | [Bulgarian](../bg/README.md) | [Serbian (Cyrillic)](../sr/README.md) | [Croatian](../hr/README.md) | [Slovenian](../sl/README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Generative AI for Beginners (संस्करण ३) - एक कोर्स

Microsoft Cloud Advocates द्वारा तयार गरिएको हाम्रो २१-पाठको व्यापक कोर्समार्फत Generative AI एप्लिकेसनहरू बनाउनका लागि आधारभूत कुरा सिक्नुहोस्।

## 🌱 सुरु गर्दै

यस कोर्समा २१ पाठहरू छन्। प्रत्येक पाठले आफ्नो विषय समेट्छ, त्यसैले जहाँ मन लाग्यो त्यहाँबाट सुरु गर्न सक्नुहुन्छ!

पाठहरूलाई "Learn" (सिक्ने) र "Build" (बनाउने) मा वर्गीकृत गरिएको छ। "Learn" पाठहरूले Generative AI को अवधारणा बुझाउँछन् भने "Build" पाठहरूले अवधारणा र सम्भव भएमा **Python** र **TypeScript** मा कोड उदाहरणहरू पनि दिन्छन्।

.NET विकासकर्ताहरूका लागि [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst) पनि उपलब्ध छ!

हरेक पाठमा थप सिक्नका लागि "Keep Learning" खण्ड पनि समावेश गरिएको छ।

## के चाहिन्छ
### यस कोर्सको कोड चलाउन तपाईंले यी मध्ये कुनै एक प्रयोग गर्न सक्नुहुन्छ: 
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **पाठहरू:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **पाठहरू:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **पाठहरू:** "oai-assignment" 
   
- Python वा TypeScript को आधारभूत ज्ञान उपयोगी हुन्छ - \*पूर्ण नयाँहरूका लागि यी [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) र [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) कोर्सहरू हेर्नुहोस्
- आफ्नो GitHub खातामा [यो सम्पूर्ण रिपो फोर्क गर्न](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) GitHub खाता आवश्यक छ

हामीले तपाईंको विकास वातावरण सेटअप गर्न सहयोग पुर्‍याउन **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** नामक पाठ तयार पारेका छौं।

पछाडि सजिलै फेला पार्न [यो रिपोमा स्टार (🌟) दिन नबिर्सनुहोस्](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst)।

## 🧠 तयार हुनुहोस् डिप्लोय गर्न?

यदि तपाईं थप उन्नत कोड नमूनाहरू खोज्दै हुनुहुन्छ भने, हाम्रो [Generative AI कोड नमूनाहरूको संग्रह](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) हेर्नुहोस्, जसमा **Python** र **TypeScript** दुवै छन्।

## 🗣️ अन्य सिक्नेहरूलाई भेट्नुहोस्, सहयोग पाउनुहोस्

यस कोर्स लिइरहेका अन्य सिक्नेहरूसँग भेटघाट र नेटवर्किङ गर्न र सहयोग पाउन हाम्रो [अधिकारिक Azure AI Foundry Discord सर्भर](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) मा सामेल हुनुहोस्।

प्रश्न सोध्न वा उत्पादन प्रतिक्रिया दिन हाम्रो [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) मा GitHub मा सहभागी हुनुहोस्।

## 🚀 स्टार्टअप बनाउँदै हुनुहुन्छ?

[Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) मा साइन अप गरेर **निःशुल्क OpenAI क्रेडिटहरू** र Azure OpenAI Services मार्फत OpenAI मोडेलहरू पहुँच गर्न **Azure क्रेडिटहरूमा $१५०,००० सम्म** प्राप्त गर्नुहोस्।

## 🙏 सहयोग गर्न चाहनुहुन्छ?

के तपाईं सल्लाह दिन चाहनुहुन्छ वा स्पेलिङ वा कोडमा त्रुटि भेट्टाउनुभएको छ? [इश्यू उठाउनुहोस्](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) वा [पुल रिक्वेस्ट बनाउनुहोस्](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 प्रत्येक पाठमा समावेश छ:

- विषयको छोटो भिडियो परिचय
- README मा लेखिएको पाठ
- Azure OpenAI र OpenAI API समर्थन गर्ने Python र TypeScript कोड नमूनाहरू
- थप सिक्नका लागि स्रोतहरूका लिंकहरू

## 🗃️ पाठहरू

| #   | **पाठ लिंक**                                                                                                                              | **विवरण**                                                                                 | **भिडियो**                                                                   | **थप सिकाइ**                                                             |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **सिक्नुहोस्:** आफ्नो विकास वातावरण कसरी सेटअप गर्ने                                      | भिडियो चाँडै आउनेछ                                                                 | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Generative AI र LLMs परिचय](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                              | **सिक्नुहोस्:** Generative AI के हो र Large Language Models (LLMs) कसरी काम गर्छन् बुझ्नुहोस्       | [भिडियो](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [विभिन्न LLMs अन्वेषण र तुलना](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)             | **सिक्नुहोस्:** आफ्नो प्रयोगका लागि सही मोडेल कसरी छान्ने                                      | [भिडियो](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [जिम्मेवारीपूर्वक Generative AI प्रयोग](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                           | **सिक्नुहोस्:** Generative AI एप्लिकेसनहरू जिम्मेवारीपूर्वक कसरी बनाउने                      | [भिडियो](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Prompt Engineering का आधारभूत कुरा बुझ्नुहोस्](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)             | **सिक्नुहोस्:** Prompt Engineering का उत्कृष्ट अभ्यासहरू हातमा लिएर                         | [भिडियो](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [उन्नत Prompts सिर्जना गर्दै](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **सिक्नुहोस्:** आफ्नो prompts को परिणाम सुधार्न Prompt Engineering प्रविधिहरू कसरी लागू गर्ने | [भिडियो](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [टेक्स्ट जेनेरेसन एप्लिकेसनहरू बनाउँदै](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **बनाउनुहोस्:** Azure OpenAI / OpenAI API प्रयोग गरेर टेक्स्ट जेनेरेसन एप्लिकेसन                                | [भिडियो](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [च्याट एप्लिकेसनहरू बनाउँदै](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **बनाउनुहोस्:** च्याट एप्लिकेसनहरू कुशलतापूर्वक निर्माण र एकीकृत गर्ने प्रविधिहरू।               | [भिडियो](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [सर्च एप्लिकेसनहरू र भेक्टर डाटाबेसहरू बनाउँदै](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **बनाउनुहोस्:** डाटाको खोजी गर्न Embeddings प्रयोग गर्ने सर्च एप्लिकेसन।                        | [भिडियो](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [इमेज जेनेरेसन एप्लिकेसनहरू बनाउँदै](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **बनाउनुहोस्:** एउटा इमेज जेनेरेसन एप्लिकेसन                                                       | [भिडियो](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [लो कोड AI एप्लिकेसनहरू बनाउँदै](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **बनाउनुहोस्:** Low Code उपकरणहरू प्रयोग गरेर Generative AI एप्लिकेसन                                     | [भिडियो](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [फंक्शन कलिङसँग बाह्य एप्लिकेसनहरू एकीकृत गर्दै](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **बनाउनुहोस्:** फंक्शन कलिङ के हो र एप्लिकेसनहरूमा यसको प्रयोगका केसहरू                          | [भिडियो](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [AI एप्लिकेसनहरूको लागि UX डिजाइन गर्दै](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **सिक्नुहोस्:** Generative AI एप्लिकेसन विकास गर्दा UX डिजाइन सिद्धान्तहरू कसरी लागू गर्ने         | [भिडियो](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [तपाईंको Generative AI एप्लिकेसनहरू सुरक्षित गर्दै](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **सिक्नुहोस्:** AI प्रणालीहरूमा हुने खतरा र जोखिमहरू र ती प्रणालीहरूलाई कसरी सुरक्षित गर्ने।             | [भिडियो](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Generative AI एप्लिकेसनको जीवनचक्र](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **सिक्नुहोस्:** LLM जीवनचक्र र LLMOps व्यवस्थापन गर्नका लागि उपकरण र मेट्रिक्सहरू                         | [भिडियो](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) र भेक्टर डाटाबेसहरू](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **बनाउनुहोस्:** RAG Framework प्रयोग गरेर भेक्टर डाटाबेसबाट embeddings पुनः प्राप्त गर्ने एप्लिकेसन  | [भिडियो](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [ओपन सोर्स मोडेलहरू र Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **बनाउनुहोस्:** Hugging Face मा उपलब्ध ओपन सोर्स मोडेलहरू प्रयोग गरेर एप्लिकेसन                    | [भिडियो](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI एजेन्टहरू](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **बनाउनुहोस्:** AI Agent Framework प्रयोग गरेर एप्लिकेसन                                           | [भिडियो](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [LLM हरूलाई फाइन-ट्युन गर्दै](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **सिक्नुहोस्:** LLM हरूलाई फाइन-ट्युन गर्ने के हो, किन र कसरी गर्ने बारेमा                                            | [भिडियो](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [SLM हरूसँग बनाउँदै](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **सिक्नुहोस्:** साना भाषा मोडेलहरूसँग निर्माण गर्दा हुने फाइदाहरू                                            | भिडियो चाँडै आउनेछ | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Mistral मोडेलहरूसँग बनाउँदै](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **सिक्नुहोस्:** Mistral परिवारका मोडेलहरूको विशेषताहरू र भिन्नताहरू                                           | भिडियो चाँडै आउनेछ | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Meta मोडेलहरूसँग बनाउँदै](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **सिक्नुहोस्:** Meta परिवारका मोडेलहरूको विशेषताहरू र भिन्नताहरू                                           | भिडियो चाँडै आउनेछ | [थप जान्नुहोस्](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 विशेष धन्यवाद

[**John Aziz**](https://www.linkedin.com/in/john0isaac/) लाई सबै GitHub Actions र workflows सिर्जना गरेकोमा विशेष धन्यवाद

[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) लाई प्रत्येक पाठमा सिक्ने र कोड अनुभव सुधार गर्न महत्वपूर्ण योगदान दिएकोमा धन्यवाद। 

## 🎒 अन्य कोर्सहरू

हाम्रो टोलीले अन्य कोर्सहरू पनि उत्पादन गर्छ! हेर्नुहोस्:

- [**नयाँ** Model Context Protocol for Beginners](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI Agents for Beginners](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Generative AI for Beginners using .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Generative AI for Beginners using JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML for Beginners](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science for Beginners](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI for Beginners](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Cybersecurity for Beginners](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Web Dev for Beginners](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT for Beginners](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR Development for Beginners](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Mastering GitHub Copilot for AI Paired Programming](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Mastering GitHub Copilot for C#/.NET Developers](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Choose Your Own Copilot Adventure](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**अस्वीकरण**:  
यो दस्तावेज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) प्रयोग गरी अनुवाद गरिएको हो। हामी शुद्धताका लागि प्रयासरत छौं, तर कृपया ध्यान दिनुहोस् कि स्वचालित अनुवादमा त्रुटि वा अशुद्धता हुन सक्छ। मूल दस्तावेज यसको मूल भाषामा नै अधिकारिक स्रोत मानिनुपर्छ। महत्वपूर्ण जानकारीका लागि व्यावसायिक मानव अनुवाद सिफारिस गरिन्छ। यस अनुवादको प्रयोगबाट उत्पन्न कुनै पनि गलतफहमी वा गलत व्याख्याका लागि हामी जिम्मेवार छैनौं।