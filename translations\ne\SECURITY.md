<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:52:37+00:00",
  "source_file": "SECURITY.md",
  "language_code": "ne"
}
-->
## सुरक्षा

Microsoft ले हाम्रा सफ्टवेयर उत्पादन र सेवाहरूको सुरक्षा गम्भीरतापूर्वक लिन्छ, जसमा हाम्रा GitHub संगठनहरू मार्फत व्यवस्थापन गरिएका सबै स्रोत कोड रिपोजिटोरीहरू समावेश छन्, जसमध्ये [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin), र [हाम्रा GitHub संगठनहरू](https://opensource.microsoft.com/) पर्दछन्।

यदि तपाईंले कुनै Microsoft-स्वामित्वको रिपोजिटोरीमा [Microsoft को सुरक्षा कमजोरीको परिभाषा](https://aka.ms/opensource/security/definition) अनुसार सुरक्षा कमजोरी पत्ता लगाउनुभएको छ भनी विश्वास गर्नुहुन्छ भने, कृपया तल वर्णन गरिएको अनुसार हामीलाई रिपोर्ट गर्नुहोस्।

## सुरक्षा समस्याहरू रिपोर्ट गर्ने तरिका

**कृपया सार्वजनिक GitHub मुद्दाहरू मार्फत सुरक्षा कमजोरीहरू रिपोर्ट नगर्नुहोस्।**

त्यसको सट्टा, कृपया Microsoft Security Response Center (MSRC) मा [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report) मा रिपोर्ट गर्नुहोस्।

यदि तपाईं लगइन नगरी पेश गर्न चाहनुहुन्छ भने, [<EMAIL>](mailto:<EMAIL>) मा इमेल पठाउनुहोस्। सम्भव भएमा, हाम्रो PGP कुञ्जी प्रयोग गरी आफ्नो सन्देश इन्क्रिप्ट गर्नुहोस्; कृपया यसलाई [Microsoft Security Response Center PGP Key पृष्ठ](https://aka.ms/opensource/security/pgpkey) बाट डाउनलोड गर्नुहोस्।

तपाईंले २४ घण्टाभित्र जवाफ पाउनु पर्नेछ। कुनै कारणले जवाफ नआएमा, कृपया इमेल मार्फत फलोअप गर्नुहोस् ताकि हामीले तपाईंको मूल सन्देश प्राप्त गरेको सुनिश्चित गर्न सकौं। थप जानकारीका लागि [microsoft.com/msrc](https://aka.ms/opensource/security/msrc) मा जानुहोस्।

कृपया तल सूचीबद्ध आवश्यक जानकारी (जति सक्नुहुन्छ) समावेश गर्नुहोस् ताकि हामी सम्भावित समस्याको प्रकृति र दायरा राम्रोसँग बुझ्न सकौं:

  * समस्याको प्रकार (जस्तै buffer overflow, SQL injection, cross-site scripting, आदि)
  * समस्यासँग सम्बन्धित स्रोत फाइल(हरू) को पूर्ण पथहरू
  * प्रभावित स्रोत कोडको स्थान (tag/branch/commit वा प्रत्यक्ष URL)
  * समस्या पुन: उत्पन्न गर्न आवश्यक कुनै विशेष कन्फिगरेसन
  * समस्या पुन: उत्पन्न गर्न चरण-दर-चरण निर्देशनहरू
  * प्रमाण-कोन्सेप्ट वा exploit कोड (यदि सम्भव छ भने)
  * समस्याको प्रभाव, जसमा कसरी आक्रमणकारीले यसलाई दुरुपयोग गर्न सक्छ

यो जानकारीले हामीलाई तपाईंको रिपोर्ट छिटो मूल्यांकन गर्न मद्दत गर्नेछ।

यदि तपाईं bug bounty को लागि रिपोर्ट गर्दै हुनुहुन्छ भने, थप पूर्ण रिपोर्टहरूले उच्च पुरस्कार प्राप्त गर्न मद्दत गर्न सक्छ। कृपया हाम्रा सक्रिय कार्यक्रमहरूको थप विवरणका लागि [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) पृष्ठ भ्रमण गर्नुहोस्।

## प्राथमिक भाषा

हामी सबै सञ्चार अंग्रेजीमा हुन चाहन्छौं।

## नीति

Microsoft ले [Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd) को सिद्धान्त अनुसरण गर्दछ।

**अस्वीकरण**:  
यो दस्तावेज AI अनुवाद सेवा [Co-op Translator](https://github.com/Azure/co-op-translator) प्रयोग गरी अनुवाद गरिएको हो। हामी शुद्धताका लागि प्रयासरत छौं भने पनि, कृपया ध्यान दिनुहोस् कि स्वचालित अनुवादमा त्रुटि वा अशुद्धता हुन सक्छ। मूल दस्तावेज यसको मूल भाषामा नै अधिकारिक स्रोत मानिनुपर्छ। महत्वपूर्ण जानकारीका लागि व्यावसायिक मानव अनुवाद सिफारिस गरिन्छ। यस अनुवादको प्रयोगबाट उत्पन्न कुनै पनि गलतफहमी वा गलत व्याख्याका लागि हामी जिम्मेवार छैनौं।