<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:04:43+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "nl"
}
-->
# Bronnen voor Zelfgestuurd Leren

De les is opgebouwd met behulp van een aantal kernbronnen van OpenAI en Azure OpenAI als referentie voor terminologie en tutorials. Hier is een niet-uitputtende lijst, voor je eigen zelfgestuurde leertrajecten.

## 1. Prima<PERSON> Bronnen

| Titel/Link                                                                                                                                                                                                                   | Beschrijving                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning verbetert few-shot learning door te trainen op veel meer voorbeelden dan in de prompt passen, wat kosten bespaart, de kwaliteit van de respons verbetert en lagere latentie bij verzoeken mogelijk maakt. **Krijg een overzicht van fine-tuning van OpenAI.**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Begrijp **wat fine-tuning is (concept)**, waarom je het zou moeten overwegen (motiverend probleem), welke data je moet gebruiken (training) en hoe je de kwaliteit meet                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service stelt je in staat onze modellen aan te passen aan je eigen datasets met fine-tuning. Leer **hoe je fine-tuning uitvoert (proces)** en modellen selecteert via Azure AI Studio, Python SDK of REST API.                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM’s presteren mogelijk niet goed op specifieke domeinen, taken of datasets, of kunnen onnauwkeurige of misleidende output geven. **Wanneer zou je fine-tuning moeten overwegen** als mogelijke oplossing?                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Continue fine-tuning is het iteratieve proces waarbij een al gefinetuned model als basis wordt genomen en **verder wordt gefinetuned** op nieuwe trainingsvoorbeelden.                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fine-tuning van je model **met voorbeelden van function calling** kan de output verbeteren door nauwkeurigere en consistentere antwoorden te krijgen - met vergelijkbare formatteerbare reacties en kostenbesparing                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Raadpleeg deze tabel om te begrijpen **welke modellen gefinetuned kunnen worden** in Azure OpenAI, en in welke regio’s ze beschikbaar zijn. Bekijk ook de tokenlimieten en vervaldatums van trainingsdata indien nodig.                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Deze 30-minuten durende **aflevering van oktober 2023** van de AI Show bespreekt voordelen, nadelen en praktische inzichten die je helpen bij het maken van deze keuze.                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Deze **AI Playbook** bron begeleidt je door data-eisen, formattering, hyperparameter fine-tuning en uitdagingen/beperkingen die je moet kennen.                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Leer hoe je een voorbeeld fine-tuning dataset maakt, je voorbereidt op fine-tuning, een fine-tuning taak aanmaakt en het gefinetunede model op Azure uitrolt.                                                                                                                                                                                    |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio stelt je in staat grote taalmodellen aan te passen aan je eigen datasets _met een UI-gebaseerde workflow die geschikt is voor low-code ontwikkelaars_. Bekijk dit voorbeeld.                                                                                                                                                               |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Dit artikel beschrijft hoe je een Hugging Face model finetunet met de Hugging Face transformers bibliotheek op een enkele GPU met Azure DataBricks + Hugging Face Trainer bibliotheken                                                                                                                                                |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | De modelcatalogus in Azure Machine Learning biedt veel open source modellen die je kunt finetunen voor je specifieke taak. Probeer deze module uit, onderdeel van het [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fine-tuning van GPT-3.5 of GPT-4 modellen op Microsoft Azure met W&B maakt gedetailleerde tracking en analyse van modelprestaties mogelijk. Deze gids bouwt voort op de concepten uit de OpenAI Fine-Tuning handleiding met specifieke stappen en functies voor Azure OpenAI.                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. Secundaire Bronnen

Deze sectie bevat aanvullende bronnen die de moeite waard zijn om te verkennen, maar die we niet hebben behandeld in deze les. Ze kunnen in een toekomstige les aan bod komen, of als een secundaire opdrachtoptie op een later moment. Gebruik ze voorlopig om je eigen expertise en kennis over dit onderwerp uit te breiden.

| Titel/Link                                                                                                                                                                                                            | Beschrijving                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Data preparation and analysis for chat model fine-tuning](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | Deze notebook dient als hulpmiddel om de chatdataset voor fine-tuning van een chatmodel voor te bereiden en te analyseren. Het controleert op formatfouten, geeft basisstatistieken en schat het aantal tokens voor fine-tuning kosten. Zie: [Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning for Retrieval Augmented Generation (RAG) with Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | Het doel van deze notebook is om een uitgebreid voorbeeld te geven van hoe je OpenAI modellen kunt finetunen voor Retrieval Augmented Generation (RAG). We integreren ook Qdrant en Few-Shot Learning om de modelprestaties te verbeteren en verzinsels te verminderen.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT with Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) is het AI ontwikkelaarsplatform met tools voor het trainen van modellen, fine-tuning en het benutten van foundation modellen. Lees eerst hun [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) gids, en probeer daarna de Cookbook oefening.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning for Small Language Models                                                   | Maak kennis met [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), Microsoft’s nieuwe kleine model, verrassend krachtig en compact. Deze tutorial begeleidt je bij het finetunen van Phi-2, laat zien hoe je een unieke dataset bouwt en het model finetunet met QLoRA.                                                                                                                                                                       |
| **Hugging Face Tutorial** [How to Fine-Tune LLMs in 2024 with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Deze blogpost leidt je door het finetunen van open LLM’s met Hugging Face TRL, Transformers en datasets in 2024. Je definieert een use case, zet een ontwikkelomgeving op, bereidt een dataset voor, finetunet het model, test en evalueert het, en zet het daarna in productie.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Biedt snellere en eenvoudigere training en uitrol van [state-of-the-art machine learning modellen](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). De repo bevat Colab-vriendelijke tutorials met YouTube video’s voor begeleiding bij fine-tuning. **Reflecteert recente [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) update**. Lees de [AutoTrain documentatie](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Disclaimer**:  
Dit document is vertaald met behulp van de AI-vertalingsdienst [Co-op Translator](https://github.com/Azure/co-op-translator). Hoewel we streven naar nauwkeurigheid, dient u er rekening mee te houden dat geautomatiseerde vertalingen fouten of onnauwkeurigheden kunnen bevatten. Het originele document in de oorspronkelijke taal moet als de gezaghebbende bron worden beschouwd. Voor cruciale informatie wordt professionele menselijke vertaling aanbevolen. Wij zijn niet aansprakelijk voor eventuele misverstanden of verkeerde interpretaties die voortvloeien uit het gebruik van deze vertaling.