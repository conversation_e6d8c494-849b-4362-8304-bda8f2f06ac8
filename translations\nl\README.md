<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:37:27+00:00",
  "source_file": "README.md",
  "language_code": "nl"
}
-->
![Generative AI Voor Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.nl.png)

### 21 Lessen die alles behandelen wat je moet weten om Generative AI-toepassingen te bouwen

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Meertalige Ondersteuning

#### Ondersteund via GitHub Action (Geautomatiseerd & Altijd Up-to-Date)

[Frans](../fr/README.md) | [Spaans](../es/README.md) | [Duits](../de/README.md) | [Russisch](../ru/README.md) | [Arabisch](../ar/README.md) | [Perzisch (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinees (Vereenvoudigd)](../zh/README.md) | [Chinees (Traditioneel, Macau)](../mo/README.md) | [Chinees (Traditioneel, Hong Kong)](../hk/README.md) | [Chinees (Traditioneel, Taiwan)](../tw/README.md) | [Japans](../ja/README.md) | [Koreaans](../ko/README.md) | [Hindi](../hi/README.md) | [Bengaals](../bn/README.md) | [Marathi](../mr/README.md) | [Nepalees](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portugees (Portugal)](../pt/README.md) | [Portugees (Brazilië)](../br/README.md) | [Italiaans](../it/README.md) | [Pools](../pl/README.md) | [Turks](../tr/README.md) | [Grieks](../el/README.md) | [Thais](../th/README.md) | [Zweeds](../sv/README.md) | [Deens](../da/README.md) | [Noors](../no/README.md) | [Fins](../fi/README.md) | [Nederlands](./README.md) | [Hebreeuws](../he/README.md) | [Vietnamees](../vi/README.md) | [Indonesisch](../id/README.md) | [Maleis](../ms/README.md) | [Tagalog (Filipijns)](../tl/README.md) | [Swahili](../sw/README.md) | [Hongaars](../hu/README.md) | [Tsjechisch](../cs/README.md) | [Slowaaks](../sk/README.md) | [Roemeens](../ro/README.md) | [Bulgaars](../bg/README.md) | [Servisch (Cyrillisch)](../sr/README.md) | [Kroatisch](../hr/README.md) | [Sloveens](../sl/README.md) | [Oekraïens](../uk/README.md) | [Birmaans (Myanmar)](../my/README.md)

# Generative AI voor Beginners (Versie 3) - Een Cursus

Leer de basisprincipes van het bouwen van Generative AI-toepassingen met onze uitgebreide cursus van 21 lessen door Microsoft Cloud Advocates.

## 🌱 Aan de Slag

Deze cursus bestaat uit 21 lessen. Elke les behandelt een eigen onderwerp, dus begin waar je maar wilt!

Lessen zijn gelabeld als "Learn" lessen die een Generative AI-concept uitleggen, of "Build" lessen die een concept uitleggen en codevoorbeelden geven in zowel **Python** als **TypeScript** waar mogelijk.

Voor .NET-ontwikkelaars is er [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

Elke les bevat ook een sectie "Keep Learning" met extra leermiddelen.

## Wat Je Nodig Hebt
### Om de code van deze cursus uit te voeren, kun je gebruikmaken van: 
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **Lessen:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **Lessen:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **Lessen:** "oai-assignment" 
   
- Basiskennis van Python of TypeScript is handig - \*Voor absolute beginners zijn er deze [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) en [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) cursussen
- Een GitHub-account om [deze hele repo te forken](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) naar je eigen GitHub-account

We hebben een **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** les gemaakt om je te helpen bij het opzetten van je ontwikkelomgeving.

Vergeet niet om [deze repo te voorzien van een ster (🌟)](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst) zodat je hem later makkelijker terugvindt.

## 🧠 Klaar om te Deployen?

Als je op zoek bent naar meer geavanceerde codevoorbeelden, bekijk dan onze [collectie van Generative AI Code Samples](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) in zowel **Python** als **TypeScript**.

## 🗣️ Ontmoet Andere Leerlingen, Krijg Ondersteuning

Word lid van onze [officiële Azure AI Foundry Discord-server](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) om andere cursisten te ontmoeten, te netwerken en ondersteuning te krijgen.

Stel vragen of deel productfeedback in ons [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) op Github.

## 🚀 Een Startup Bouwen?

Meld je aan bij [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) om **gratis OpenAI-tegoed** te ontvangen en tot **$150k aan Azure-tegoed om toegang te krijgen tot OpenAI-modellen via Azure OpenAI Services**.

## 🙏 Wil je helpen?

Heb je suggesties of fouten in spelling of code gevonden? [Maak een issue aan](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) of [Doe een pull request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Elke les bevat:

- Een korte video-introductie over het onderwerp
- Een geschreven les in de README
- Python- en TypeScript-codevoorbeelden die Azure OpenAI en OpenAI API ondersteunen
- Links naar extra bronnen om je leerproces voort te zetten

## 🗃️ Lessen

| #   | **Les Link**                                                                                                                                | **Beschrijving**                                                                              | **Video**                                                                   | **Extra Leren**                                                               |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Learn:** Hoe je je ontwikkelomgeving opzet                                               | Video Binnenkort Beschikbaar                                                  | [Meer Leren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Introductie tot Generative AI en LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                              | **Learn:** Begrijpen wat Generative AI is en hoe Large Language Models (LLMs) werken.        | [Video](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Meer Leren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Verkennen en vergelijken van verschillende LLMs](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)     | **Learn:** Hoe je het juiste model kiest voor jouw toepassing                                | [Video](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Meer Leren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Verantwoord gebruik van Generative AI](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                      | **Learn:** Hoe je Generative AI-toepassingen op een verantwoorde manier bouwt                 | [Video](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Meer Leren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Begrijpen van de basisprincipes van Prompt Engineering](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)     | **Learn:** Praktische best practices voor Prompt Engineering                                 | [Video](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Meer Leren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Geavanceerde Prompts maken](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **Learn:** Hoe je prompt engineering technieken toepast die de uitkomst van je prompts verbeteren | [Video](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Meer Leren](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Tekstgeneratie-applicaties bouwen](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Bouwen:** Een tekstgeneratie-app met Azure OpenAI / OpenAI API                                | [Video](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Chatapplicaties bouwen](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Bouwen:** Technieken voor het efficiënt bouwen en integreren van chatapplicaties.             | [Video](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Zoekapps bouwen met Vector Databases](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                      | **Bouwen:** Een zoekapplicatie die Embeddings gebruikt om data te doorzoeken.                   | [Video](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Beeldgeneratie-applicaties bouwen](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                          | **Bouwen:** Een applicatie voor beeldgeneratie                                               | [Video](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Low Code AI-applicaties bouwen](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Bouwen:** Een Generative AI-applicatie met Low Code tools                                   | [Video](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Externe applicaties integreren met Function Calling](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst)   | **Bouwen:** Wat is function calling en de toepassingen ervan voor apps                        | [Video](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [UX ontwerpen voor AI-applicaties](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Leren:** Hoe UX-ontwerpprincipes toe te passen bij het ontwikkelen van Generative AI-apps    | [Video](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Je Generative AI-applicaties beveiligen](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Leren:** De bedreigingen en risico’s voor AI-systemen en hoe je deze systemen beveiligt      | [Video](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [De levenscyclus van Generative AI-applicaties](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)   | **Leren:** De tools en metrics om de LLM-leven cyclus en LLMOps te beheren                     | [Video](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) en Vector Databases](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **Bouwen:** Een applicatie die een RAG-framework gebruikt om embeddings uit Vector Databases op te halen | [Video](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Open Source Modellen en Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                  | **Bouwen:** Een applicatie met open source modellen beschikbaar op Hugging Face               | [Video](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI Agents](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **Bouwen:** Een applicatie met een AI Agent Framework                                         | [Video](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [Fine-Tuning van LLMs](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                          | **Leren:** Wat, waarom en hoe van fine-tuning van LLMs                                       | [Video](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Bouwen met SLMs](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **Leren:** De voordelen van bouwen met Small Language Models                                  | Video Binnenkort Beschikbaar | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Bouwen met Mistral Modellen](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                       | **Leren:** De kenmerken en verschillen van de Mistral Family Modellen                        | Video Binnenkort Beschikbaar | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Bouwen met Meta Modellen](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                             | **Leren:** De kenmerken en verschillen van de Meta Family Modellen                          | Video Binnenkort Beschikbaar | [Meer informatie](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Speciale dank

Speciale dank aan [**John Aziz**](https://www.linkedin.com/in/john0isaac/) voor het maken van alle GitHub Actions en workflows

[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) voor zijn belangrijke bijdragen aan elke les om de leer- en code-ervaring te verbeteren.

## 🎒 Andere cursussen

Ons team maakt ook andere cursussen! Bekijk:

- [**NIEUW** Model Context Protocol voor Beginners](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI Agents voor Beginners](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Generative AI voor Beginners met .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Generative AI voor Beginners met JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML voor Beginners](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science voor Beginners](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI voor Beginners](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Cybersecurity voor Beginners](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Webontwikkeling voor Beginners](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT voor Beginners](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR-ontwikkeling voor Beginners](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [GitHub Copilot voor AI Paired Programming beheersen](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [GitHub Copilot voor C#/.NET Developers beheersen](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Kies je eigen Copilot Avontuur](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Disclaimer**:  
Dit document is vertaald met behulp van de AI-vertalingsdienst [Co-op Translator](https://github.com/Azure/co-op-translator). Hoewel we streven naar nauwkeurigheid, dient u er rekening mee te houden dat geautomatiseerde vertalingen fouten of onnauwkeurigheden kunnen bevatten. Het originele document in de oorspronkelijke taal moet als de gezaghebbende bron worden beschouwd. Voor cruciale informatie wordt professionele menselijke vertaling aanbevolen. Wij zijn niet aansprakelijk voor eventuele misverstanden of verkeerde interpretaties die voortvloeien uit het gebruik van deze vertaling.