<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:54:35+00:00",
  "source_file": "SECURITY.md",
  "language_code": "nl"
}
-->
## Beveiliging

Microsoft neemt de beveiliging van onze softwareproducten en -diensten serieus, inclusief alle broncode-repositories die worden beheerd via onze GitHub-organisaties, waaronder [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) en [onze GitHub-organisaties](https://opensource.microsoft.com/).

Als je denkt een beveiligingslek te hebben gevonden in een Microsoft-eigendom repository die voldoet aan [Microsofts definitie van een beveiligingslek](https://aka.ms/opensource/security/definition), meld dit dan aan ons zoals hieronder beschreven.

## Beveiligingsproblemen Melden

**Meld beveiligingslekken alstublieft niet via openbare GitHub-issues.**

Meld ze in plaats daarvan bij het Microsoft Security Response Center (MSRC) via [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Als je liever zonder inloggen wilt melden, stuur dan een e-mail naar [<EMAIL>](mailto:<EMAIL>). Indien mogelijk, versleutel je bericht met onze PGP-sleutel; je kunt deze downloaden van de [Microsoft Security Response Center PGP Key pagina](https://aka.ms/opensource/security/pgpkey).

Je zou binnen 24 uur een reactie moeten ontvangen. Mocht dat om welke reden dan ook niet het geval zijn, neem dan per e-mail contact op om te bevestigen dat we je oorspronkelijke bericht hebben ontvangen. Meer informatie is te vinden op [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Voeg alstublieft de gevraagde informatie hieronder toe (zo volledig mogelijk) om ons te helpen de aard en omvang van het mogelijke probleem beter te begrijpen:

  * Type probleem (bijv. buffer overflow, SQL-injectie, cross-site scripting, enz.)
  * Volledige paden van bronbestand(en) gerelateerd aan het optreden van het probleem
  * De locatie van de getroffen broncode (tag/branch/commit of directe URL)
  * Eventuele speciale configuratie die nodig is om het probleem te reproduceren
  * Stapsgewijze instructies om het probleem te reproduceren
  * Proof-of-concept of exploitcode (indien mogelijk)
  * Impact van het probleem, inclusief hoe een aanvaller het probleem zou kunnen misbruiken

Deze informatie helpt ons om je melding sneller te beoordelen.

Als je meldt voor een bug bounty, kunnen meer complete rapporten bijdragen aan een hogere beloning. Bezoek onze [Microsoft Bug Bounty Programma](https://aka.ms/opensource/security/bounty) pagina voor meer details over onze actieve programma’s.

## Voorkeurs Talen

Wij geven de voorkeur aan alle communicatie in het Engels.

## Beleid

Microsoft volgt het principe van [Gecoördineerde Kwetsbaarheidsmelding](https://aka.ms/opensource/security/cvd).

**Disclaimer**:  
Dit document is vertaald met behulp van de AI-vertalingsdienst [Co-op Translator](https://github.com/Azure/co-op-translator). Hoewel we streven naar nauwkeurigheid, dient u er rekening mee te houden dat geautomatiseerde vertalingen fouten of onnauwkeurigheden kunnen bevatten. Het originele document in de oorspronkelijke taal moet als de gezaghebbende bron worden beschouwd. Voor cruciale informatie wordt professionele menselijke vertaling aanbevolen. Wij zijn niet aansprakelijk voor eventuele misverstanden of verkeerde interpretaties die voortvloeien uit het gebruik van deze vertaling.