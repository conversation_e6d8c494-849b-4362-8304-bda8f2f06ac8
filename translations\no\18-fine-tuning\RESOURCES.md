<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:03:57+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "no"
}
-->
# Ressurser for selvstyrt læring

Leksjonen er bygget ved hjelp av en rekke kjerne­ressurser fra OpenAI og Azure OpenAI som referanser for terminologi og veiledninger. Her er en ikke-uttømmende liste for dine egne selvstyrte læringsreiser.

## 1. Primære ressurser

| Tittel/Lenke                                                                                                                                                                                                                 | Beskrivelse                                                                                                                                                                                                                                                                                                                    |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning forbedrer few-shot læring ved å trene på mange flere eksempler enn det som får plass i prompten, noe som sparer kostnader, forbedrer responskvaliteten og muliggjør lavere ventetid på forespørsler. **Få en oversikt over fine-tuning fra OpenAI.**                                                                     |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Forstå **hva fine-tuning er (konsept)**, hvorfor du bør vurdere det (motiverende problem), hvilken data som skal brukes (trening) og hvordan man måler kvaliteten                                                                                                                                                              |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service lar deg tilpasse modellene våre til dine egne datasett ved hjelp av fine-tuning. Lær **hvordan du finjusterer (prosess)** og velger modeller ved bruk av Azure AI Studio, Python SDK eller REST API.                                                                                                         |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM-er kan ha dårlig ytelse på spesifikke domener, oppgaver eller datasett, eller kan produsere unøyaktige eller misvisende svar. **Når bør du vurdere fine-tuning** som en mulig løsning på dette?                                                                                                                             |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Kontinuerlig fine-tuning er en iterativ prosess hvor man velger en allerede finjustert modell som grunnlag og **finjusterer den videre** på nye treningssett.                                                                                                                                                                  |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fine-tuning av modellen din **med eksempler på funksjonskall** kan forbedre modellens output ved å gi mer nøyaktige og konsistente svar – med likt format på responsene og kostnadsbesparelser                                                                                                                                |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Sjekk denne tabellen for å forstå **hvilke modeller som kan finjusteres** i Azure OpenAI, og hvilke regioner de er tilgjengelige i. Se også deres token-grenser og utløpsdatoer for treningsdata ved behov.                                                                                                                     |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Denne 30-minutters episoden fra AI Show i oktober 2023 diskuterer fordeler, ulemper og praktiske innsikter som hjelper deg å ta denne avgjørelsen.                                                                                                                                                                            |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Denne **AI Playbook**-ressursen guider deg gjennom datakrav, formatering, hyperparameter-fine-tuning og utfordringer/begrensninger du bør kjenne til.                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Lær hvordan du lager et eksempel på et fine-tuning datasett, forbereder for fine-tuning, oppretter en fine-tuning jobb og distribuerer den finjusterte modellen på Azure.                                                                                                                                                        |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio lar deg tilpasse store språkmodeller til dine egne datasett _ved hjelp av en UI-basert arbeidsflyt som passer for lavkode-utviklere_. Se dette eksempelet.                                                                                                                                                     |
| **Tutorial**: [Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Denne artikkelen beskriver hvordan du finjusterer en Hugging Face-modell med Hugging Face transformers-biblioteket på en enkelt GPU med Azure DataBricks + Hugging Face Trainer-biblioteker                                                                                                                                    |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Modellkatalogen i Azure Machine Learning tilbyr mange open source-modeller du kan finjustere for din spesifikke oppgave. Prøv denne modulen som er [fra AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fine-tuning av GPT-3.5 eller GPT-4 modeller på Microsoft Azure med W&B gir detaljert sporing og analyse av modellens ytelse. Denne guiden bygger videre på konseptene fra OpenAI Fine-Tuning-guiden med spesifikke steg og funksjoner for Azure OpenAI.                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. Sekundære ressurser

Denne seksjonen inneholder tilleggsmateriale som er verdt å utforske, men som vi ikke rakk å dekke i denne leksjonen. De kan bli tatt opp i en fremtidig leksjon, eller som et sekundært oppgavealternativ senere. For nå kan du bruke dem til å bygge din egen kompetanse og kunnskap rundt dette temaet.

| Tittel/Lenke                                                                                                                                                                                                            | Beskrivelse                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Data preparation and analysis for chat model fine-tuning](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | Denne notatboken fungerer som et verktøy for å forhåndsbehandle og analysere chat-datasettet som brukes til finjustering av en chatmodell. Den sjekker for formatfeil, gir grunnleggende statistikk og estimerer token-telling for kostnadsberegning ved fine-tuning. Se: [Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning for Retrieval Augmented Generation (RAG) with Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | Målet med denne notatboken er å gå gjennom et omfattende eksempel på hvordan man finjusterer OpenAI-modeller for Retrieval Augmented Generation (RAG). Vi vil også integrere Qdrant og Few-Shot Learning for å øke modellens ytelse og redusere feilinformasjon.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT with Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) er en plattform for AI-utviklere med verktøy for trening, finjustering og utnyttelse av grunnmodeller. Les først deres [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) guide, og prøv deretter Cookbook-øvelsen.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - finjustering for små språkmodeller                                                   | Møt [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), Microsofts nye lille modell, overraskende kraftig og kompakt. Denne veiledningen tar deg gjennom finjustering av Phi-2, og viser hvordan du bygger et unikt datasett og finjusterer modellen med QLoRA.                                                                                                                                                                       |
| **Hugging Face Tutorial** [How to Fine-Tune LLMs in 2024 with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Dette blogginnlegget viser deg hvordan du finjusterer åpne LLM-er ved hjelp av Hugging Face TRL, Transformers og datasett i 2024. Du definerer et bruksområde, setter opp et utviklingsmiljø, forbereder et datasett, finjusterer modellen, tester og evaluerer den, og til slutt distribuerer den i produksjon.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Gjør trening og distribusjon av [moderne maskinlæringsmodeller](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst) raskere og enklere. Repoen har Colab-vennlige veiledninger med YouTube-videoer for finjustering. **Reflekterer nylig [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) oppdatering**. Les [AutoTrain-dokumentasjonen](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Ansvarsfraskrivelse**:  
Dette dokumentet er oversatt ved hjelp av AI-oversettelsestjenesten [Co-op Translator](https://github.com/Azure/co-op-translator). Selv om vi streber etter nøyaktighet, vennligst vær oppmerksom på at automatiske oversettelser kan inneholde feil eller unøyaktigheter. Det opprinnelige dokumentet på originalspråket skal anses som den autoritative kilden. For kritisk informasjon anbefales profesjonell menneskelig oversettelse. Vi er ikke ansvarlige for eventuelle misforståelser eller feiltolkninger som oppstår ved bruk av denne oversettelsen.