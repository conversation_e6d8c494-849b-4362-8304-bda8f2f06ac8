<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:54:18+00:00",
  "source_file": "SECURITY.md",
  "language_code": "no"
}
-->
## Sikkerhet

Microsoft tar sikkerheten til våre programvareprodukter og tjenester på alvor, inkludert alle kildekoderepositorier som administreres gjennom våre GitHub-organisasjoner, som inkluderer [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin), og [våre GitHub-organisasjoner](https://opensource.microsoft.com/).

Hvis du mener du har funnet en sikkerhetssårbarhet i et Microsoft-eid repository som oppfyller [Microsofts definisjon av en sikkerhetssårbarhet](https://aka.ms/opensource/security/definition), vennligst rapporter det til oss som beskrevet nedenfor.

## Rapportering av sikkerhetsproblemer

**Vennligst ikke rapporter sikkerhetssårbarheter gjennom offentlige GitHub-issues.**

Rapporter dem i stedet til Microsoft Security Response Center (MSRC) på [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Hvis du foretrekker å sende inn uten å logge inn, send e-post til [<EMAIL>](mailto:<EMAIL>). Hvis mulig, krypter meldingen din med vår PGP-nøkkel; last den ned fra [Microsoft Security Response Center PGP Key-siden](https://aka.ms/opensource/security/pgpkey).

Du bør få svar innen 24 timer. Hvis du av en eller annen grunn ikke gjør det, vennligst følg opp via e-post for å sikre at vi har mottatt din opprinnelige melding. Mer informasjon finnes på [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Vennligst inkluder den forespurte informasjonen nedenfor (så mye som mulig) for å hjelpe oss med å bedre forstå arten og omfanget av det mulige problemet:

  * Type problem (f.eks. buffer overflow, SQL-injeksjon, cross-site scripting, osv.)
  * Fullstendige stier til kildefil(er) relatert til manifestasjonen av problemet
  * Plasseringen av den berørte kildekoden (tag/branch/commit eller direkte URL)
  * Eventuell spesiell konfigurasjon som kreves for å gjenskape problemet
  * Trinnvise instruksjoner for å gjenskape problemet
  * Proof-of-concept eller exploit-kode (hvis mulig)
  * Konsekvenser av problemet, inkludert hvordan en angriper kan utnytte det

Denne informasjonen vil hjelpe oss med å prioritere rapporten din raskere.

Hvis du rapporterer for en bug bounty, kan mer fullstendige rapporter bidra til høyere belønning. Besøk vår [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty)-side for mer informasjon om våre aktive programmer.

## Foretrukne språk

Vi foretrekker at all kommunikasjon foregår på engelsk.

## Retningslinjer

Microsoft følger prinsippet om [Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd).

**Ansvarsfraskrivelse**:  
Dette dokumentet er oversatt ved hjelp av AI-oversettelsestjenesten [Co-op Translator](https://github.com/Azure/co-op-translator). Selv om vi streber etter nøyaktighet, vennligst vær oppmerksom på at automatiske oversettelser kan inneholde feil eller unøyaktigheter. Det opprinnelige dokumentet på originalspråket skal anses som den autoritative kilden. For kritisk informasjon anbefales profesjonell menneskelig oversettelse. Vi er ikke ansvarlige for eventuelle misforståelser eller feiltolkninger som oppstår ved bruk av denne oversettelsen.