<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "00f2643fec1571acc5d38cc1a3b972d5",
  "translation_date": "2025-07-09T07:06:43+00:00",
  "source_file": "00-course-setup/README.md",
  "language_code": "pa"
}
-->
# ਇਸ ਕੋਰਸ ਨਾਲ ਸ਼ੁਰੂਆਤ

ਅਸੀਂ ਬਹੁਤ ਉਤਸ਼ਾਹਿਤ ਹਾਂ ਕਿ ਤੁਸੀਂ ਇਸ ਕੋਰਸ ਨੂੰ ਸ਼ੁਰੂ ਕਰੋ ਅਤੇ ਦੇਖੋ ਕਿ ਤੁਸੀਂ Generative AI ਨਾਲ ਕੀ ਬਣਾਉਣ ਲਈ ਪ੍ਰੇਰਿਤ ਹੋ!

ਤੁਹਾਡੇ ਸਫਲਤਾ ਨੂੰ ਯਕੀਨੀ ਬਣਾਉਣ ਲਈ, ਇਹ ਪੰਨਾ ਸੈਟਅਪ ਕਦਮ, ਤਕਨੀਕੀ ਲੋੜਾਂ ਅਤੇ ਜੇ ਲੋੜ ਹੋਵੇ ਤਾਂ ਮਦਦ ਕਿੱਥੇ ਮਿਲੇਗੀ, ਇਹ ਸਾਰਾ ਵੇਰਵਾ ਦਿੰਦਾ ਹੈ।

## ਸੈਟਅਪ ਕਦਮ

ਇਸ ਕੋਰਸ ਨੂੰ ਲੈਣ ਲਈ, ਤੁਹਾਨੂੰ ਹੇਠਾਂ ਦਿੱਤੇ ਕਦਮ ਪੂਰੇ ਕਰਨੇ ਪੈਣਗੇ।

### 1. ਇਸ ਰੇਪੋ ਨੂੰ Fork ਕਰੋ

ਆਪਣੇ GitHub ਖਾਤੇ ਵਿੱਚ [ਇਸ ਪੂਰੇ ਰੇਪੋ ਨੂੰ Fork ਕਰੋ](https://github.com/microsoft/generative-ai-for-beginners/fork?WT.mc_id=academic-105485-koreyst) ਤਾਂ ਜੋ ਤੁਸੀਂ ਕੋਈ ਵੀ ਕੋਡ ਬਦਲ ਸਕੋ ਅਤੇ ਚੈਲੈਂਜ ਪੂਰੇ ਕਰ ਸਕੋ। ਤੁਸੀਂ ਇਸ ਰੇਪੋ ਨੂੰ [ਸਟਾਰ (🌟) ਵੀ ਕਰ ਸਕਦੇ ਹੋ](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst) ਤਾਂ ਜੋ ਇਸਨੂੰ ਅਤੇ ਸੰਬੰਧਿਤ ਰੇਪੋਜ਼ ਨੂੰ ਆਸਾਨੀ ਨਾਲ ਲੱਭ ਸਕੋ।

### 2. ਇੱਕ codespace ਬਣਾਓ

ਕੋਡ ਚਲਾਉਂਦੇ ਸਮੇਂ ਕਿਸੇ ਵੀ ਡਿਪੈਂਡੈਂਸੀ ਸਮੱਸਿਆ ਤੋਂ ਬਚਣ ਲਈ, ਅਸੀਂ ਸਿਫਾਰਸ਼ ਕਰਦੇ ਹਾਂ ਕਿ ਤੁਸੀਂ ਇਸ ਕੋਰਸ ਨੂੰ [GitHub Codespaces](https://github.com/features/codespaces?WT.mc_id=academic-105485-koreyst) ਵਿੱਚ ਚਲਾਓ।

ਇਹ ਤੁਹਾਡੇ Fork ਕੀਤੇ ਰੇਪੋ ਦੇ `Code` ਵਿਕਲਪ ਨੂੰ ਚੁਣ ਕੇ ਅਤੇ ਫਿਰ **Codespaces** ਵਿਕਲਪ ਨੂੰ ਚੁਣ ਕੇ ਬਣਾਇਆ ਜਾ ਸਕਦਾ ਹੈ।

![Dialog showing buttons to create a codespace](../../../00-course-setup/images/who-will-pay.webp)

### 3. ਆਪਣੇ API Keys ਸੁਰੱਖਿਅਤ ਰੱਖੋ

ਕਿਸੇ ਵੀ ਕਿਸਮ ਦੀ ਐਪਲੀਕੇਸ਼ਨ ਬਣਾਉਂਦੇ ਸਮੇਂ ਆਪਣੇ API keys ਨੂੰ ਸੁਰੱਖਿਅਤ ਰੱਖਣਾ ਬਹੁਤ ਜਰੂਰੀ ਹੈ। ਅਸੀਂ ਸਿਫਾਰਸ਼ ਕਰਦੇ ਹਾਂ ਕਿ ਤੁਸੀਂ ਆਪਣੇ ਕੋਡ ਵਿੱਚ ਸਿੱਧਾ ਕੋਈ ਵੀ API key ਨਾ ਰੱਖੋ। ਜੇ ਤੁਸੀਂ ਇਹ ਜਾਣਕਾਰੀ ਕਿਸੇ ਪਬਲਿਕ ਰੇਪੋ ਵਿੱਚ ਕਮਿਟ ਕਰਦੇ ਹੋ ਤਾਂ ਇਸ ਨਾਲ ਸੁਰੱਖਿਆ ਸਮੱਸਿਆਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ ਅਤੇ ਬੁਰੇ ਇਰਾਦੇ ਵਾਲੇ ਇਸਦਾ ਗਲਤ ਇਸਤੇਮਾਲ ਕਰਕੇ ਅਣਚਾਹੇ ਖਰਚੇ ਵੀ ਹੋ ਸਕਦੇ ਹਨ।  
ਇੱਥੇ ਇੱਕ ਕਦਮ-ਦਰ-ਕਦਮ ਗਾਈਡ ਹੈ ਕਿ ਕਿਵੇਂ Python ਲਈ `.env` ਫਾਇਲ ਬਣਾਈਏ ਅਤੇ `GITHUB_TOKEN` ਸ਼ਾਮਲ ਕਰੀਏ:

1. **ਆਪਣੇ ਪ੍ਰੋਜੈਕਟ ਡਾਇਰੈਕਟਰੀ ਵਿੱਚ ਜਾਓ**: ਆਪਣਾ ਟਰਮੀਨਲ ਜਾਂ ਕਮਾਂਡ ਪ੍ਰਾਂਪਟ ਖੋਲ੍ਹੋ ਅਤੇ ਉਸ ਡਾਇਰੈਕਟਰੀ ਵਿੱਚ ਜਾਓ ਜਿੱਥੇ ਤੁਸੀਂ `.env` ਫਾਇਲ ਬਣਾਉਣੀ ਹੈ।

   ```bash
   cd path/to/your/project
   ```

2. **`.env` ਫਾਇਲ ਬਣਾਓ**: ਆਪਣੀ ਮਨਪਸੰਦ ਟੈਕਸਟ ਐਡੀਟਰ ਨਾਲ `.env` ਨਾਮ ਦੀ ਨਵੀਂ ਫਾਇਲ ਬਣਾਓ। ਜੇ ਤੁਸੀਂ ਕਮਾਂਡ ਲਾਈਨ ਵਰਤ ਰਹੇ ਹੋ ਤਾਂ `touch` (Unix-ਅਧਾਰਿਤ ਸਿਸਟਮਾਂ ਲਈ) ਜਾਂ `echo` (Windows ਲਈ) ਵਰਤ ਸਕਦੇ ਹੋ:

   Unix-ਅਧਾਰਿਤ ਸਿਸਟਮ:

   ```bash
   touch .env
   ```

   Windows:

   ```cmd
   echo . > .env
   ```

3. **`.env` ਫਾਇਲ ਨੂੰ ਸੋਧੋ**: `.env` ਫਾਇਲ ਨੂੰ ਕਿਸੇ ਟੈਕਸਟ ਐਡੀਟਰ (ਜਿਵੇਂ VS Code, Notepad++, ਜਾਂ ਹੋਰ) ਵਿੱਚ ਖੋਲ੍ਹੋ। ਹੇਠਾਂ ਦਿੱਤੀ ਲਾਈਨ ਸ਼ਾਮਲ ਕਰੋ, `your_github_token_here` ਨੂੰ ਆਪਣੇ ਅਸਲੀ GitHub ਟੋਕਨ ਨਾਲ ਬਦਲੋ:

   ```env
   GITHUB_TOKEN=your_github_token_here
   ```

4. **ਫਾਇਲ ਸੇਵ ਕਰੋ**: ਬਦਲਾਅ ਸੇਵ ਕਰੋ ਅਤੇ ਟੈਕਸਟ ਐਡੀਟਰ ਬੰਦ ਕਰੋ।

5. **`python-dotenv` ਇੰਸਟਾਲ ਕਰੋ**: ਜੇ ਤੁਸੀਂ ਪਹਿਲਾਂ ਨਹੀਂ ਕੀਤਾ, ਤਾਂ `.env` ਫਾਇਲ ਤੋਂ ਵਾਤਾਵਰਣ ਵੈਰੀਏਬਲ ਲੋਡ ਕਰਨ ਲਈ `python-dotenv` ਪੈਕੇਜ ਇੰਸਟਾਲ ਕਰੋ। ਤੁਸੀਂ ਇਸਨੂੰ `pip` ਨਾਲ ਇੰਸਟਾਲ ਕਰ ਸਕਦੇ ਹੋ:

   ```bash
   pip install python-dotenv
   ```

6. **ਆਪਣੇ Python ਸਕ੍ਰਿਪਟ ਵਿੱਚ ਵਾਤਾਵਰਣ ਵੈਰੀਏਬਲ ਲੋਡ ਕਰੋ**: ਆਪਣੇ Python ਸਕ੍ਰਿਪਟ ਵਿੱਚ `python-dotenv` ਪੈਕੇਜ ਦੀ ਵਰਤੋਂ ਕਰਕੇ `.env` ਫਾਇਲ ਤੋਂ ਵਾਤਾਵਰਣ ਵੈਰੀਏਬਲ ਲੋਡ ਕਰੋ:

   ```python
   from dotenv import load_dotenv
   import os

   # Load environment variables from .env file
   load_dotenv()

   # Access the GITHUB_TOKEN variable
   github_token = os.getenv("GITHUB_TOKEN")

   print(github_token)
   ```

ਇੰਨਾ ਹੀ! ਤੁਸੀਂ ਸਫਲਤਾਪੂਰਵਕ `.env` ਫਾਇਲ ਬਣਾਈ, ਆਪਣਾ GitHub ਟੋਕਨ ਸ਼ਾਮਲ ਕੀਤਾ ਅਤੇ ਇਸਨੂੰ ਆਪਣੇ Python ਐਪਲੀਕੇਸ਼ਨ ਵਿੱਚ ਲੋਡ ਕੀਤਾ।

## ਆਪਣੇ ਕੰਪਿਊਟਰ 'ਤੇ ਲੋਕਲ ਤੌਰ 'ਤੇ ਕਿਵੇਂ ਚਲਾਉਣਾ

ਆਪਣੇ ਕੰਪਿਊਟਰ 'ਤੇ ਕੋਡ ਚਲਾਉਣ ਲਈ, ਤੁਹਾਡੇ ਕੋਲ [Python ਦਾ ਕੋਈ ਵਰਜਨ ਇੰਸਟਾਲ ਹੋਣਾ](https://www.python.org/downloads/?WT.mc_id=academic-105485-koreyst) ਜਰੂਰੀ ਹੈ।

ਫਿਰ ਰੇਪੋ ਨੂੰ ਵਰਤਣ ਲਈ, ਤੁਹਾਨੂੰ ਇਸਨੂੰ ਕਲੋਨ ਕਰਨਾ ਪਵੇਗਾ:

```shell
git clone https://github.com/microsoft/generative-ai-for-beginners
cd generative-ai-for-beginners
```

ਜਦੋਂ ਸਾਰਾ ਕੁਝ ਚੈੱਕ ਆਊਟ ਹੋ ਜਾਵੇ, ਤਾਂ ਤੁਸੀਂ ਸ਼ੁਰੂਆਤ ਕਰ ਸਕਦੇ ਹੋ!

## ਵਿਕਲਪਿਕ ਕਦਮ

### Miniconda ਇੰਸਟਾਲ ਕਰਨਾ

[Miniconda](https://conda.io/en/latest/miniconda.html?WT.mc_id=academic-105485-koreyst) ਇੱਕ ਹਲਕਾ ਫੁਲਕਾ ਇੰਸਟਾਲਰ ਹੈ ਜੋ [Conda](https://docs.conda.io/en/latest?WT.mc_id=academic-105485-koreyst), Python ਅਤੇ ਕੁਝ ਪੈਕੇਜਾਂ ਨੂੰ ਇੰਸਟਾਲ ਕਰਨ ਲਈ ਵਰਤਿਆ ਜਾਂਦਾ ਹੈ।  
Conda ਖੁਦ ਇੱਕ ਪੈਕੇਜ ਮੈਨੇਜਰ ਹੈ, ਜੋ ਵੱਖ-ਵੱਖ Python [**ਵਰਚੁਅਲ ਵਾਤਾਵਰਣਾਂ**](https://docs.python.org/3/tutorial/venv.html?WT.mc_id=academic-105485-koreyst) ਅਤੇ ਪੈਕੇਜਾਂ ਨੂੰ ਸੈਟਅਪ ਅਤੇ ਬਦਲਣਾ ਆਸਾਨ ਬਣਾਉਂਦਾ ਹੈ। ਇਹ ਉਹਨਾਂ ਪੈਕੇਜਾਂ ਨੂੰ ਇੰਸਟਾਲ ਕਰਨ ਵਿੱਚ ਵੀ ਮਦਦਗਾਰ ਹੈ ਜੋ `pip` ਰਾਹੀਂ ਉਪਲਬਧ ਨਹੀਂ ਹੁੰਦੇ।

ਤੁਸੀਂ [MiniConda ਇੰਸਟਾਲੇਸ਼ਨ ਗਾਈਡ](https://docs.anaconda.com/free/miniconda/#quick-command-line-install?WT.mc_id=academic-105485-koreyst) ਦੀ ਪਾਲਣਾ ਕਰਕੇ ਇਸਨੂੰ ਸੈਟਅਪ ਕਰ ਸਕਦੇ ਹੋ।

Miniconda ਇੰਸਟਾਲ ਹੋਣ ਤੋਂ ਬਾਅਦ, ਤੁਹਾਨੂੰ [ਰੇਪੋ](https://github.com/microsoft/generative-ai-for-beginners/fork?WT.mc_id=academic-105485-koreyst) ਕਲੋਨ ਕਰਨਾ ਪਵੇਗਾ (ਜੇ ਪਹਿਲਾਂ ਨਹੀਂ ਕੀਤਾ)।

ਫਿਰ, ਤੁਹਾਨੂੰ ਇੱਕ ਵਰਚੁਅਲ ਵਾਤਾਵਰਣ ਬਣਾਉਣਾ ਪਵੇਗਾ। Conda ਨਾਲ ਇਹ ਕਰਨ ਲਈ, ਇੱਕ ਨਵੀਂ ਵਾਤਾਵਰਣ ਫਾਇਲ (_environment.yml_) ਬਣਾਓ। ਜੇ ਤੁਸੀਂ Codespaces ਵਰਤ ਰਹੇ ਹੋ, ਤਾਂ ਇਹ `.devcontainer` ਡਾਇਰੈਕਟਰੀ ਵਿੱਚ ਬਣਾਓ, ਮਤਲਬ `.devcontainer/environment.yml`।

ਹੇਠਾਂ ਦਿੱਤੇ ਸਨਿੱਪੇਟ ਨਾਲ ਆਪਣੀ ਵਾਤਾਵਰਣ ਫਾਇਲ ਭਰੋ:

```yml
name: <environment-name>
channels:
  - defaults
  - microsoft
dependencies:
  - python=<python-version>
  - openai
  - python-dotenv
  - pip
  - pip:
      - azure-ai-ml
```

ਜੇ ਤੁਹਾਨੂੰ Conda ਵਰਤਣ ਵਿੱਚ ਕੋਈ ਗਲਤੀ ਆ ਰਹੀ ਹੈ, ਤਾਂ ਤੁਸੀਂ ਹੱਥੋਂ Microsoft AI Libraries ਨੂੰ ਟਰਮੀਨਲ ਵਿੱਚ ਹੇਠਾਂ ਦਿੱਤੇ ਕਮਾਂਡ ਨਾਲ ਇੰਸਟਾਲ ਕਰ ਸਕਦੇ ਹੋ।

```
conda install -c microsoft azure-ai-ml
```

ਵਾਤਾਵਰਣ ਫਾਇਲ ਉਹ ਡਿਪੈਂਡੈਂਸੀਜ਼ ਦਰਸਾਉਂਦੀ ਹੈ ਜੋ ਸਾਨੂੰ ਚਾਹੀਦੀਆਂ ਹਨ। `<environment-name>` ਉਸ ਨਾਮ ਨੂੰ ਦਰਸਾਉਂਦਾ ਹੈ ਜੋ ਤੁਸੀਂ ਆਪਣੇ Conda ਵਾਤਾਵਰਣ ਲਈ ਚਾਹੁੰਦੇ ਹੋ, ਅਤੇ `<python-version>` ਉਹ Python ਦਾ ਵਰਜਨ ਹੈ ਜੋ ਤੁਸੀਂ ਵਰਤਣਾ ਚਾਹੁੰਦੇ ਹੋ, ਉਦਾਹਰਨ ਵਜੋਂ, `3` Python ਦਾ ਤਾਜ਼ਾ ਮੁੱਖ ਵਰਜਨ ਹੈ।

ਇਹ ਸਭ ਹੋਣ ਤੋਂ ਬਾਅਦ, ਤੁਸੀਂ ਹੇਠਾਂ ਦਿੱਤੇ ਕਮਾਂਡਾਂ ਨੂੰ ਆਪਣੇ ਕਮਾਂਡ ਲਾਈਨ/ਟਰਮੀਨਲ ਵਿੱਚ ਚਲਾ ਕੇ ਆਪਣਾ Conda ਵਾਤਾਵਰਣ ਬਣਾ ਸਕਦੇ ਹੋ:

```bash
conda env create --name ai4beg --file .devcontainer/environment.yml # .devcontainer sub path applies to only Codespace setups
conda activate ai4beg
```

ਜੇ ਕੋਈ ਸਮੱਸਿਆ ਆਵੇ ਤਾਂ [Conda ਵਾਤਾਵਰਣਾਂ ਦੀ ਗਾਈਡ](https://docs.conda.io/projects/conda/en/latest/user-guide/tasks/manage-environments.html?WT.mc_id=academic-105485-koreyst) ਨੂੰ ਵੇਖੋ।

### Visual Studio Code ਨੂੰ Python ਸਹਾਇਤਾ ਐਕਸਟੈਂਸ਼ਨ ਨਾਲ ਵਰਤਣਾ

ਅਸੀਂ ਸਿਫਾਰਸ਼ ਕਰਦੇ ਹਾਂ ਕਿ ਤੁਸੀਂ ਇਸ ਕੋਰਸ ਲਈ [Visual Studio Code (VS Code)](https://code.visualstudio.com/?WT.mc_id=academic-105485-koreyst) ਐਡੀਟਰ ਨੂੰ [Python ਸਹਾਇਤਾ ਐਕਸਟੈਂਸ਼ਨ](https://marketplace.visualstudio.com/items?itemName=ms-python.python&WT.mc_id=academic-105485-koreyst) ਨਾਲ ਵਰਤੋਂ। ਇਹ ਸਿਰਫ਼ ਸਿਫਾਰਸ਼ ਹੈ, ਜ਼ਰੂਰੀ ਨਹੀਂ।

> **Note**: ਜਦੋਂ ਤੁਸੀਂ ਕੋਰਸ ਰੇਪੋ ਨੂੰ VS Code ਵਿੱਚ ਖੋਲ੍ਹਦੇ ਹੋ, ਤਾਂ ਤੁਹਾਡੇ ਕੋਲ ਪ੍ਰੋਜੈਕਟ ਨੂੰ ਇੱਕ ਕੰਟੇਨਰ ਵਿੱਚ ਸੈਟਅਪ ਕਰਨ ਦਾ ਵਿਕਲਪ ਹੁੰਦਾ ਹੈ। ਇਹ ਇਸ ਲਈ ਹੈ ਕਿਉਂਕਿ ਕੋਰਸ ਰੇਪੋ ਵਿੱਚ ਇੱਕ ਖਾਸ `.devcontainer` ਡਾਇਰੈਕਟਰੀ ਹੁੰਦੀ ਹੈ। ਇਸ ਬਾਰੇ ਅੱਗੇ ਹੋਰ ਜਾਣਕਾਰੀ ਮਿਲੇਗੀ।

> **Note**: ਜਦੋਂ ਤੁਸੀਂ ਰੇਪੋ ਕਲੋਨ ਕਰਕੇ VS Code ਵਿੱਚ ਖੋਲ੍ਹਦੇ ਹੋ, ਤਾਂ ਇਹ ਆਪਣੇ ਆਪ ਤੁਹਾਨੂੰ Python ਸਹਾਇਤਾ ਐਕਸਟੈਂਸ਼ਨ ਇੰਸਟਾਲ ਕਰਨ ਦੀ ਸਿਫਾਰਸ਼ ਕਰੇਗਾ।

> **Note**: ਜੇ VS Code ਤੁਹਾਨੂੰ ਰੇਪੋ ਨੂੰ ਕੰਟੇਨਰ ਵਿੱਚ ਮੁੜ ਖੋਲ੍ਹਣ ਲਈ ਕਹਿੰਦਾ ਹੈ, ਤਾਂ ਇਸ ਬੇਨਤੀ ਨੂੰ ਮਨਜ਼ੂਰ ਨਾ ਕਰੋ ਤਾਂ ਜੋ ਤੁਸੀਂ ਲੋਕਲ ਤੌਰ 'ਤੇ ਇੰਸਟਾਲ ਕੀਤੇ Python ਵਰਜਨ ਨੂੰ ਵਰਤ ਸਕੋ।

### ਬ੍ਰਾਊਜ਼ਰ ਵਿੱਚ Jupyter ਵਰਤਣਾ

ਤੁਸੀਂ ਪ੍ਰੋਜੈਕਟ 'ਤੇ ਕੰਮ ਕਰਨ ਲਈ ਆਪਣੇ ਬ੍ਰਾਊਜ਼ਰ ਵਿੱਚ ਹੀ [Jupyter ਵਾਤਾਵਰਣ](https://jupyter.org?WT.mc_id=academic-105485-koreyst) ਵਰਤ ਸਕਦੇ ਹੋ। ਕਲਾਸਿਕ Jupyter ਅਤੇ [Jupyter Hub](https://jupyter.org/hub?WT.mc_id=academic-105485-koreyst) ਦੋਹਾਂ ਇੱਕ ਸੁਖਦਾਇਕ ਵਿਕਾਸ ਵਾਤਾਵਰਣ ਪ੍ਰਦਾਨ ਕਰਦੇ ਹਨ, ਜਿਸ ਵਿੱਚ ਆਟੋ-ਕੰਪਲੀਸ਼ਨ, ਕੋਡ ਹਾਈਲਾਈਟਿੰਗ ਆਦਿ ਵਰਗੀਆਂ ਵਿਸ਼ੇਸ਼ਤਾਵਾਂ ਹੁੰਦੀਆਂ ਹਨ।

Jupyter ਨੂੰ ਲੋਕਲ ਤੌਰ 'ਤੇ ਸ਼ੁਰੂ ਕਰਨ ਲਈ, ਟਰਮੀਨਲ/ਕਮਾਂਡ ਲਾਈਨ 'ਤੇ ਜਾਓ, ਕੋਰਸ ਡਾਇਰੈਕਟਰੀ ਵਿੱਚ ਜਾਓ ਅਤੇ ਇਹ ਕਮਾਂਡ ਚਲਾਓ:

```bash
jupyter notebook
```

ਜਾਂ

```bash
jupyterhub
```

ਇਸ ਨਾਲ ਇੱਕ Jupyter ਇੰਸਟੈਂਸ ਸ਼ੁਰੂ ਹੋਵੇਗਾ ਅਤੇ ਇਸਨੂੰ ਐਕਸੈਸ ਕਰਨ ਲਈ URL ਕਮਾਂਡ ਲਾਈਨ ਵਿੰਡੋ ਵਿੱਚ ਦਿਖਾਇਆ ਜਾਵੇਗਾ।

ਜਦੋਂ ਤੁਸੀਂ URL ਖੋਲ੍ਹੋਗੇ, ਤਾਂ ਤੁਹਾਨੂੰ ਕੋਰਸ ਦਾ ਢਾਂਚਾ ਵੇਖਾਈ ਦੇਵੇਗਾ ਅਤੇ ਤੁਸੀਂ ਕਿਸੇ ਵੀ `*.ipynb` ਫਾਇਲ ਤੇ ਜਾ ਸਕੋਗੇ। ਉਦਾਹਰਨ ਵਜੋਂ, `08-building-search-applications/python/oai-solution.ipynb`।

### ਕੰਟੇਨਰ ਵਿੱਚ ਚਲਾਉਣਾ

ਆਪਣੇ ਕੰਪਿਊਟਰ ਜਾਂ Codespace 'ਤੇ ਸਾਰਾ ਕੁਝ ਸੈਟਅਪ ਕਰਨ ਦੇ ਬਦਲੇ, ਤੁਸੀਂ [ਕੰਟੇਨਰ](../../../00-course-setup/<https:/en.wikipedia.org/wiki/Containerization_(computing)?WT.mc_id=academic-105485-koreyst>) ਵਰਤ ਸਕਦੇ ਹੋ। ਕੋਰਸ ਰੇਪੋ ਵਿੱਚ ਖਾਸ `.devcontainer` ਫੋਲਡਰ VS Code ਨੂੰ ਪ੍ਰੋਜੈਕਟ ਨੂੰ ਕੰਟੇਨਰ ਵਿੱਚ ਸੈਟਅਪ ਕਰਨ ਦੀ ਆਗਿਆ ਦਿੰਦਾ ਹੈ। Codespaces ਤੋਂ ਬਾਹਰ, ਇਸ ਲਈ Docker ਇੰਸਟਾਲ ਕਰਨਾ ਪਵੇਗਾ, ਅਤੇ ਇਹ ਕੁਝ ਮਿਹਨਤ ਵਾਲਾ ਕੰਮ ਹੈ, ਇਸ ਲਈ ਅਸੀਂ ਇਹ ਸਿਰਫ਼ ਉਹਨਾਂ ਲਈ ਸਿਫਾਰਸ਼ ਕਰਦੇ ਹਾਂ ਜਿਨ੍ਹਾਂ ਕੋਲ ਕੰਟੇਨਰਾਂ ਨਾਲ ਕੰਮ ਕਰਨ ਦਾ ਤਜਰਬਾ ਹੈ।

GitHub Codespaces ਵਰਤਦੇ ਸਮੇਂ ਆਪਣੇ API keys ਨੂੰ ਸੁਰੱਖਿਅਤ ਰੱਖਣ ਦਾ ਸਭ ਤੋਂ ਵਧੀਆ ਤਰੀਕਾ Codespace Secrets ਦੀ ਵਰਤੋਂ ਕਰਨਾ ਹੈ। ਇਸ ਬਾਰੇ ਹੋਰ ਜਾਣਨ ਲਈ ਕਿਰਪਾ ਕਰਕੇ [Codespaces secrets management](https://docs.github.com/en/codespaces/managing-your-codespaces/managing-secrets-for-your-codespaces?WT.mc_id=academic-105485-koreyst) ਗਾਈਡ ਦੀ ਪਾਲਣਾ ਕਰੋ।

## ਪਾਠ ਅਤੇ ਤਕਨੀਕੀ ਲੋੜਾਂ

ਕੋਰਸ ਵਿੱਚ 6 ਸੰਕਲਪਕ ਪਾਠ ਅਤੇ 6 ਕੋਡਿੰਗ ਪਾਠ ਹਨ।

ਕੋਡਿੰਗ ਪਾਠਾਂ ਲਈ, ਅਸੀਂ Azure OpenAI Service ਵਰਤ ਰਹੇ ਹਾਂ। ਤੁਹਾਨੂੰ Azure OpenAI ਸੇਵਾ ਅਤੇ API key ਦੀ ਲੋੜ ਹੋਵੇਗੀ ਤਾਂ ਜੋ ਇਹ ਕੋਡ ਚਲਾ ਸਕੋ। ਤੁਸੀਂ [ਇਸ ਅਰਜ਼ੀ ਨੂੰ ਪੂਰਾ ਕਰਕੇ](https://azure.microsoft.com/products/ai-services/openai-service?WT.mc_id=academic-105485-koreyst) ਐਕਸੈਸ ਲਈ ਅਰਜ਼ੀ ਦੇ ਸਕਦੇ ਹੋ।

ਜਦੋਂ ਤੱਕ ਤੁਹਾਡੀ ਅਰਜ਼ੀ ਪ੍ਰਕਿਰਿਆ ਵਿੱਚ ਹੈ, ਹਰ ਕੋਡਿੰਗ ਪਾਠ ਵਿੱਚ ਇੱਕ `README.md` ਫਾਇਲ ਵੀ ਹੁੰਦੀ ਹੈ ਜਿੱਥੇ ਤੁਸੀਂ ਕੋਡ ਅਤੇ ਨਤੀਜੇ ਵੇਖ ਸਕਦੇ ਹੋ।

## Azure OpenAI Service ਨੂੰ ਪਹਿਲੀ ਵਾਰੀ ਵਰਤਣਾ

ਜੇ ਇਹ ਤੁਹਾਡੀ ਪਹਿਲੀ ਵਾਰੀ ਹੈ ਕਿ ਤੁਸੀਂ Azure OpenAI ਸੇਵਾ ਨਾਲ ਕੰਮ ਕਰ ਰਹੇ ਹੋ, ਤਾਂ ਕਿਰਪਾ ਕਰਕੇ ਇਸ ਗਾਈਡ ਦੀ ਪਾਲਣਾ ਕਰੋ ਕਿ ਕਿਵੇਂ [Azure OpenAI Service resource ਬਣਾਈਏ ਅਤੇ ਡਿਪਲੋਇ ਕਰੋ।](https://learn.microsoft.com/azure/ai-services/openai/how-to/create-resource?pivots=web-portal&WT.mc_id=academic-105485-koreyst)

## OpenAI API ਨੂੰ ਪਹਿਲੀ ਵਾਰੀ ਵਰਤਣਾ

ਜੇ ਇਹ ਤੁਹਾਡੀ ਪਹਿਲੀ ਵਾਰੀ ਹੈ ਕਿ ਤੁਸੀਂ OpenAI API ਨਾਲ ਕੰਮ ਕਰ ਰਹੇ ਹੋ, ਤਾਂ ਕਿਰਪਾ ਕਰਕੇ ਇਸ ਗਾਈਡ ਦੀ ਪਾਲਣਾ ਕਰੋ ਕਿ ਕਿਵੇਂ [Interface ਬਣਾਈਏ ਅਤੇ ਵਰਤੋਂ।](https://platform.openai.com/docs/quickstart?context=pythont&WT.mc_id=academic-105485-koreyst)

## ਹੋਰ ਸਿੱਖਣ ਵਾਲਿਆਂ ਨਾਲ ਮਿਲੋ

ਅਸੀਂ ਆਪਣੇ ਅਧਿਕਾਰਿਕ [AI Community Discord ਸਰਵਰ](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) ਵਿੱਚ ਹੋਰ ਸਿੱਖਣ ਵਾਲਿਆਂ ਨਾਲ ਮਿਲਣ ਲਈ ਚੈਨਲ ਬਣਾਏ ਹਨ। ਇਹ ਉਹਨਾਂ ਲਈ ਵਧੀਆ ਜਗ੍ਹਾ ਹੈ ਜੋ ਹੋਰ ਸਮਾਨ ਸੋਚ ਵਾਲੇ ਉਦਯੋਗਪਤੀਆਂ, ਨਿਰਮਾਤਾ, ਵਿਦਿਆਰਥੀ ਅਤੇ ਕੋਈ ਵੀ ਜੋ Generative AI ਵਿੱਚ ਆਪਣਾ ਦਰਜਾ ਵਧਾਉਣਾ ਚਾਹੁੰਦਾ ਹੈ, ਨਾਲ ਜੁੜਨਾ ਚਾਹੁੰਦੇ ਹਨ।

[![Join discord channel](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

ਪ੍ਰੋਜੈਕਟ ਟੀਮ ਵੀ ਇਸ Discord ਸਰਵਰ 'ਤੇ ਸਿੱਖਣ ਵਾਲਿਆਂ ਦੀ ਮਦਦ ਲਈ ਮੌਜੂਦ ਰਹੇਗੀ।

## ਯੋਗਦਾਨ ਪਾਉਣਾ

ਇਹ ਕੋਰਸ ਇੱਕ ਖੁੱਲਾ ਸਰੋਤ ਪ੍ਰਯਾਸ ਹੈ। ਜੇ ਤੁਸੀਂ ਸੁਧਾਰ ਜਾਂ ਸਮੱਸਿਆਵਾਂ ਵੇਖਦੇ ਹੋ, ਤਾਂ ਕਿਰਪਾ ਕਰਕੇ [Pull Request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst) ਬਣਾਓ ਜਾਂ [GitHub issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) ਲਾਗ ਕਰੋ।

ਪ੍ਰੋਜੈਕਟ ਟੀਮ ਸਾਰੇ ਯੋਗਦ

**ਅਸਵੀਕਾਰੋਪਣ**:  
ਇਹ ਦਸਤਾਵੇਜ਼ AI ਅਨੁਵਾਦ ਸੇਵਾ [Co-op Translator](https://github.com/Azure/co-op-translator) ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਅਨੁਵਾਦਿਤ ਕੀਤਾ ਗਿਆ ਹੈ। ਜਦੋਂ ਕਿ ਅਸੀਂ ਸਹੀਤਾ ਲਈ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ, ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਰੱਖੋ ਕਿ ਸਵੈਚਾਲਿਤ ਅਨੁਵਾਦਾਂ ਵਿੱਚ ਗਲਤੀਆਂ ਜਾਂ ਅਸਮਰਥਤਾਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ। ਮੂਲ ਦਸਤਾਵੇਜ਼ ਆਪਣੀ ਮੂਲ ਭਾਸ਼ਾ ਵਿੱਚ ਪ੍ਰਮਾਣਿਕ ਸਰੋਤ ਮੰਨਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ। ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਲਈ, ਪੇਸ਼ੇਵਰ ਮਨੁੱਖੀ ਅਨੁਵਾਦ ਦੀ ਸਿਫਾਰਸ਼ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਅਸੀਂ ਇਸ ਅਨੁਵਾਦ ਦੀ ਵਰਤੋਂ ਤੋਂ ਉਤਪੰਨ ਕਿਸੇ ਵੀ ਗਲਤਫਹਿਮੀ ਜਾਂ ਗਲਤ ਵਿਆਖਿਆ ਲਈ ਜ਼ਿੰਮੇਵਾਰ ਨਹੀਂ ਹਾਂ।