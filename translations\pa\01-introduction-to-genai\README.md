<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "f53ba0fa49164f9323043f1c6b11f2b1",
  "translation_date": "2025-07-09T07:49:53+00:00",
  "source_file": "01-introduction-to-genai/README.md",
  "language_code": "pa"
}
-->
# ਜਨਰੇਟਿਵ AI ਅਤੇ ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲਾਂ ਦਾ ਪਰਿਚਯ

[![ਜਨਰੇਟਿਵ AI ਅਤੇ ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲਾਂ ਦਾ ਪਰਿਚਯ](../../../translated_images/01-lesson-banner.2424cfd092f43366707ee2d15749f62f76f80ea3cb0816f4f31d0abd5ffd4dd1.pa.png)](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst)

_(ਇਸ ਪਾਠ ਦਾ ਵੀਡੀਓ ਦੇਖਣ ਲਈ ਉਪਰ ਦਿੱਤੀ ਤਸਵੀਰ 'ਤੇ ਕਲਿੱਕ ਕਰੋ)_

ਜਨਰੇਟਿਵ AI ਇੱਕ ਐਸਾ ਕ੍ਰਿਤ੍ਰਿਮ ਬੁੱਧੀ ਹੈ ਜੋ ਲਿਖਤ, ਤਸਵੀਰਾਂ ਅਤੇ ਹੋਰ ਕਿਸਮ ਦੇ ਸਮੱਗਰੀ ਤਿਆਰ ਕਰ ਸਕਦੀ ਹੈ। ਇਸ ਤਕਨਾਲੋਜੀ ਦੀ ਖੂਬੀ ਇਹ ਹੈ ਕਿ ਇਹ AI ਨੂੰ ਸਭ ਲਈ ਉਪਲਬਧ ਕਰਵਾਉਂਦੀ ਹੈ, ਕੋਈ ਵੀ ਸਿਰਫ ਇੱਕ ਸਧਾਰਣ ਲਿਖਤ ਜਾਂ ਕੁਝ ਵਾਕਾਂਸ਼ ਲਿਖ ਕੇ ਇਸਦਾ ਇਸਤੇਮਾਲ ਕਰ ਸਕਦਾ ਹੈ। ਤੁਹਾਨੂੰ ਜਾਵਾ ਜਾਂ SQL ਵਰਗੀਆਂ ਭਾਸ਼ਾਵਾਂ ਸਿੱਖਣ ਦੀ ਲੋੜ ਨਹੀਂ, ਸਿਰਫ ਆਪਣੀ ਭਾਸ਼ਾ ਵਿੱਚ ਆਪਣੀ ਮੰਗ ਦੱਸੋ ਅਤੇ AI ਮਾਡਲ ਤੁਹਾਨੂੰ ਸੁਝਾਅ ਦੇਵੇਗਾ। ਇਸਦੇ ਐਪਲੀਕੇਸ਼ਨ ਅਤੇ ਪ੍ਰਭਾਵ ਬਹੁਤ ਵੱਡੇ ਹਨ, ਤੁਸੀਂ ਰਿਪੋਰਟਾਂ ਲਿਖ ਸਕਦੇ ਹੋ, ਐਪਲੀਕੇਸ਼ਨ ਤਿਆਰ ਕਰ ਸਕਦੇ ਹੋ ਅਤੇ ਹੋਰ ਬਹੁਤ ਕੁਝ, ਸਿਰਫ ਕੁਝ ਸਕਿੰਟਾਂ ਵਿੱਚ।

ਇਸ ਕੋਰਸ ਵਿੱਚ, ਅਸੀਂ ਵੇਖਾਂਗੇ ਕਿ ਸਾਡਾ ਸਟਾਰਟਅਪ ਜਨਰੇਟਿਵ AI ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਸਿੱਖਿਆ ਖੇਤਰ ਵਿੱਚ ਨਵੇਂ ਮੌਕੇ ਕਿਵੇਂ ਖੋਲ੍ਹਦਾ ਹੈ ਅਤੇ ਇਸਦੇ ਸਮਾਜਿਕ ਪ੍ਰਭਾਵਾਂ ਅਤੇ ਤਕਨਾਲੋਜੀ ਦੀਆਂ ਸੀਮਾਵਾਂ ਨਾਲ ਕਿਵੇਂ ਨਜਿੱਠਦਾ ਹੈ।

## ਪਰਿਚਯ

ਇਸ ਪਾਠ ਵਿੱਚ ਅਸੀਂ ਕਵਰ ਕਰਾਂਗੇ:

- ਕਾਰੋਬਾਰੀ ਸੰਦਰਭ ਦਾ ਪਰਿਚਯ: ਸਾਡਾ ਸਟਾਰਟਅਪ ਵਿਚਾਰ ਅਤੇ ਮਿਸ਼ਨ।
- ਜਨਰੇਟਿਵ AI ਅਤੇ ਮੌਜੂਦਾ ਤਕਨਾਲੋਜੀ ਦਾ ਸੰਦਰਭ।
- ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲ ਦੀ ਅੰਦਰੂਨੀ ਕਾਰਗੁਜ਼ਾਰੀ।
- ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲਾਂ ਦੀਆਂ ਮੁੱਖ ਖੂਬੀਆਂ ਅਤੇ ਵਰਤੋਂ ਦੇ ਮਾਮਲੇ।

## ਸਿੱਖਣ ਦੇ ਲਕੜੇ

ਇਸ ਪਾਠ ਨੂੰ ਪੂਰਾ ਕਰਨ ਤੋਂ ਬਾਅਦ, ਤੁਸੀਂ ਸਮਝ ਪਾਓਗੇ:

- ਜਨਰੇਟਿਵ AI ਕੀ ਹੈ ਅਤੇ ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲ ਕਿਵੇਂ ਕੰਮ ਕਰਦੇ ਹਨ।
- ਵੱਖ-ਵੱਖ ਵਰਤੋਂ ਦੇ ਮਾਮਲਿਆਂ ਲਈ ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲਾਂ ਦੀ ਵਰਤੋਂ ਕਿਵੇਂ ਕੀਤੀ ਜਾ ਸਕਦੀ ਹੈ, ਖਾਸ ਕਰਕੇ ਸਿੱਖਿਆ ਖੇਤਰ ਵਿੱਚ।

## ਸੰਦਰਭ: ਸਾਡਾ ਸਿੱਖਿਆ ਸਟਾਰਟਅਪ

ਜਨਰੇਟਿਵ ਕ੍ਰਿਤ੍ਰਿਮ ਬੁੱਧੀ (AI) AI ਤਕਨਾਲੋਜੀ ਦੀ ਚੋਟੀ ਹੈ, ਜੋ ਪਹਿਲਾਂ ਅਸੰਭਵ ਸਮਝੀਆਂ ਗਈਆਂ ਚੀਜ਼ਾਂ ਨੂੰ ਸੰਭਵ ਬਣਾਉਂਦੀ ਹੈ। ਜਨਰੇਟਿਵ AI ਮਾਡਲਾਂ ਕੋਲ ਕਈ ਖੂਬੀਆਂ ਅਤੇ ਵਰਤੋਂ ਦੇ ਮਾਮਲੇ ਹਨ, ਪਰ ਇਸ ਕੋਰਸ ਵਿੱਚ ਅਸੀਂ ਵੇਖਾਂਗੇ ਕਿ ਇਹ ਸਿੱਖਿਆ ਖੇਤਰ ਵਿੱਚ ਕਿਵੇਂ ਕ੍ਰਾਂਤੀ ਲਿਆ ਰਹੇ ਹਨ, ਇੱਕ ਕਲਪਨਾਤਮਕ ਸਟਾਰਟਅਪ ਰਾਹੀਂ। ਅਸੀਂ ਇਸ ਸਟਾਰਟਅਪ ਨੂੰ _ਸਾਡਾ ਸਟਾਰਟਅਪ_ ਕਹਾਂਗੇ। ਸਾਡਾ ਸਟਾਰਟਅਪ ਸਿੱਖਿਆ ਖੇਤਰ ਵਿੱਚ ਕੰਮ ਕਰਦਾ ਹੈ ਅਤੇ ਇਸਦਾ ਉਦੇਸ਼ ਹੈ

> _ਸਿੱਖਣ ਦੀ ਪਹੁੰਚ ਨੂੰ ਦੁਨੀਆ ਭਰ ਵਿੱਚ ਸੁਧਾਰਨਾ, ਸਿੱਖਿਆ ਵਿੱਚ ਬਰਾਬਰੀ ਯਕੀਨੀ ਬਣਾਉਣਾ ਅਤੇ ਹਰ ਵਿਦਿਆਰਥੀ ਨੂੰ ਉਸਦੀ ਜ਼ਰੂਰਤ ਮੁਤਾਬਕ ਨਿੱਜੀ ਸਿੱਖਣ ਦਾ ਤਜਰਬਾ ਦੇਣਾ_।

ਸਾਡੀ ਟੀਮ ਜਾਣਦੀ ਹੈ ਕਿ ਇਹ ਮਕਸਦ ਪੂਰਾ ਕਰਨ ਲਈ ਸਾਨੂੰ ਆਧੁਨਿਕ ਸਮੇਂ ਦੇ ਸਭ ਤੋਂ ਸ਼ਕਤੀਸ਼ਾਲੀ ਸੰਦਾਂ ਵਿੱਚੋਂ ਇੱਕ – ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲ (LLMs) – ਦੀ ਵਰਤੋਂ ਕਰਨੀ ਪਵੇਗੀ।

ਜਨਰੇਟਿਵ AI ਅੱਜ ਦੇ ਸਿੱਖਣ ਅਤੇ ਸਿਖਾਉਣ ਦੇ ਤਰੀਕੇ ਨੂੰ ਬਦਲ ਦੇਵੇਗਾ, ਜਿੱਥੇ ਵਿਦਿਆਰਥੀਆਂ ਕੋਲ 24 ਘੰਟੇ ਵਰਚੁਅਲ ਅਧਿਆਪਕ ਹੋਣਗੇ ਜੋ ਵੱਡੀ ਮਾਤਰਾ ਵਿੱਚ ਜਾਣਕਾਰੀ ਅਤੇ ਉਦਾਹਰਣਾਂ ਦਿੰਦੇ ਹਨ, ਅਤੇ ਅਧਿਆਪਕ ਆਪਣੇ ਵਿਦਿਆਰਥੀਆਂ ਦਾ ਮੁਲਾਂਕਣ ਕਰਨ ਅਤੇ ਫੀਡਬੈਕ ਦੇਣ ਲਈ ਨਵੇਂ ਸੰਦ ਵਰਤ ਸਕਦੇ ਹਨ।

![ਪੰਜ ਨੌਜਵਾਨ ਵਿਦਿਆਰਥੀ ਮਾਨੀਟਰ ਵੱਲ ਦੇਖ ਰਹੇ ਹਨ - DALLE2 ਵੱਲੋਂ ਤਸਵੀਰ](../../../translated_images/students-by-DALLE2.b70fddaced1042ee47092320243050c4c9a7da78b31eeba515b09b2f0dca009b.pa.png)

ਸ਼ੁਰੂਆਤ ਲਈ, ਆਓ ਕੁਝ ਬੁਨਿਆਦੀ ਧਾਰਣਾਵਾਂ ਅਤੇ ਸ਼ਬਦਾਵਲੀ ਨੂੰ ਪਰਿਭਾਸ਼ਿਤ ਕਰੀਏ ਜੋ ਅਸੀਂ ਇਸ ਕੋਰਸ ਵਿੱਚ ਵਰਤਾਂਗੇ।

## ਜਨਰੇਟਿਵ AI ਕਿਵੇਂ ਆਇਆ?

ਹਾਲ ਹੀ ਵਿੱਚ ਜਨਰੇਟਿਵ AI ਮਾਡਲਾਂ ਦੀ ਘੋਸ਼ਣਾ ਨਾਲ ਬਣੀ ਵੱਡੀ ਚਰਚਾ ਦੇ ਬਾਵਜੂਦ, ਇਹ ਤਕਨਾਲੋਜੀ ਕਈ ਦਹਾਕਿਆਂ ਤੋਂ ਵਿਕਸਤ ਹੋ ਰਹੀ ਹੈ, ਜਿਸਦੀ ਸ਼ੁਰੂਆਤ 1960 ਦੇ ਦਹਾਕੇ ਵਿੱਚ ਹੋਈ ਸੀ। ਅਸੀਂ ਹੁਣ ਇਸ ਮੋੜ 'ਤੇ ਹਾਂ ਜਿੱਥੇ AI ਕੋਲ ਮਨੁੱਖੀ ਸਮਝਦਾਰੀ ਵਾਲੀਆਂ ਖੂਬੀਆਂ ਹਨ, ਜਿਵੇਂ ਕਿ ਗੱਲਬਾਤ, ਜਿਸਦਾ ਉਦਾਹਰਣ ਹੈ [OpenAI ChatGPT](https://openai.com/chatgpt) ਜਾਂ [Bing Chat](https://www.microsoft.com/edge/features/bing-chat?WT.mc_id=academic-105485-koreyst), ਜੋ Bing ਵੈੱਬ ਖੋਜ ਲਈ GPT ਮਾਡਲ ਵਰਤਦਾ ਹੈ।

ਥੋੜ੍ਹਾ ਪਿੱਛੇ ਜਾ ਕੇ ਵੇਖੀਏ ਤਾਂ ਪਹਿਲੇ AI ਪ੍ਰੋਟੋਟਾਈਪ ਟਾਈਪਲਿਖਤ ਚੈਟਬੋਟ ਸਨ, ਜੋ ਮਾਹਿਰਾਂ ਦੇ ਗਿਆਨ ਦੇ ਆਧਾਰ 'ਤੇ ਕੰਮ ਕਰਦੇ ਸਨ। ਇਨ੍ਹਾਂ ਦੇ ਜਵਾਬ ਕੁੰਜੀ ਸ਼ਬਦਾਂ 'ਤੇ ਨਿਰਭਰ ਕਰਦੇ ਸਨ। ਪਰ ਇਹ ਤਰੀਕਾ ਵੱਡੇ ਪੱਧਰ 'ਤੇ ਕੰਮ ਨਹੀਂ ਕਰਦਾ ਸੀ।

### AI ਲਈ ਸਾਂਖਿਆਕੀ ਤਰੀਕਾ: ਮਸ਼ੀਨ ਲਰਨਿੰਗ

1990 ਦੇ ਦਹਾਕੇ ਵਿੱਚ ਇੱਕ ਮੋੜ ਆਇਆ, ਜਦੋਂ ਲਿਖਤ ਵਿਸ਼ਲੇਸ਼ਣ ਲਈ ਸਾਂਖਿਆਕੀ ਤਰੀਕੇ ਦੀ ਵਰਤੋਂ ਹੋਈ। ਇਸ ਨਾਲ ਨਵੇਂ ਅਲਗੋਰਿਦਮ ਬਣੇ – ਜਿਨ੍ਹਾਂ ਨੂੰ ਮਸ਼ੀਨ ਲਰਨਿੰਗ ਕਹਿੰਦੇ ਹਨ – ਜੋ ਡਾਟਾ ਤੋਂ ਪੈਟਰਨ ਸਿੱਖ ਸਕਦੇ ਹਨ ਬਿਨਾਂ ਸਪਸ਼ਟ ਤੌਰ 'ਤੇ ਪ੍ਰੋਗਰਾਮ ਕੀਤੇ ਹੋਏ। ਇਹ ਤਰੀਕਾ ਮਸ਼ੀਨਾਂ ਨੂੰ ਮਨੁੱਖੀ ਭਾਸ਼ਾ ਸਮਝਣ ਦੀ ਨਕਲ ਕਰਨ ਯੋਗ ਬਣਾਉਂਦਾ ਹੈ: ਇੱਕ ਸਾਂਖਿਆਕੀ ਮਾਡਲ ਲਿਖਤ-ਲੇਬਲ ਜੋੜਿਆਂ 'ਤੇ ਟ੍ਰੇਨ ਕੀਤਾ ਜਾਂਦਾ ਹੈ, ਜੋ ਅਣਜਾਣ ਲਿਖਤ ਨੂੰ ਪਹਿਲਾਂ ਤੋਂ ਨਿਰਧਾਰਿਤ ਲੇਬਲ ਨਾਲ ਵਰਗੀਕ੍ਰਿਤ ਕਰਦਾ ਹੈ।

### ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਅਤੇ ਆਧੁਨਿਕ ਵਰਚੁਅਲ ਸਹਾਇਕ

ਹਾਲੀਆ ਸਾਲਾਂ ਵਿੱਚ, ਹਾਰਡਵੇਅਰ ਦੀ ਤਕਨਾਲੋਜੀ ਵਿੱਚ ਵਿਕਾਸ ਨੇ ਵੱਡੇ ਡਾਟਾ ਅਤੇ ਜਟਿਲ ਗਣਨਾਵਾਂ ਨੂੰ ਸੰਭਾਲਣ ਯੋਗ ਬਣਾਇਆ, ਜਿਸ ਨਾਲ ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਜਾਂ ਡੀਪ ਲਰਨਿੰਗ ਅਲਗੋਰਿਦਮ ਵਿਕਸਤ ਹੋਏ।

ਨਿਊਰਲ ਨੈੱਟਵਰਕ (ਖਾਸ ਕਰਕੇ Recurrent Neural Networks – RNNs) ਨੇ ਕੁਦਰਤੀ ਭਾਸ਼ਾ ਪ੍ਰਕਿਰਿਆ ਨੂੰ ਬਹੁਤ ਸੁਧਾਰਿਆ, ਜਿਸ ਨਾਲ ਲਿਖਤ ਦੇ ਅਰਥ ਨੂੰ ਬਿਹਤਰ ਢੰਗ ਨਾਲ ਦਰਸਾਇਆ ਜਾ ਸਕਦਾ ਹੈ, ਜਿਵੇਂ ਕਿ ਇੱਕ ਸ਼ਬਦ ਦਾ ਸੰਦਰਭ ਵਿੱਚ ਕੀ ਮਤਲਬ ਹੈ।

ਇਹੀ ਤਕਨਾਲੋਜੀ ਪਹਿਲੇ ਦਹਾਕੇ ਵਿੱਚ ਜਨਮੇ ਵਰਚੁਅਲ ਸਹਾਇਕਾਂ ਨੂੰ ਚਲਾਉਂਦੀ ਹੈ, ਜੋ ਮਨੁੱਖੀ ਭਾਸ਼ਾ ਨੂੰ ਸਮਝਣ, ਲੋੜ ਪਛਾਣਨ ਅਤੇ ਕਾਰਵਾਈ ਕਰਨ ਵਿੱਚ ਮਾਹਿਰ ਹਨ – ਜਿਵੇਂ ਕਿ ਪਹਿਲਾਂ ਤੋਂ ਤਿਆਰ ਕੀਤੇ ਜਵਾਬ ਦੇਣਾ ਜਾਂ ਤੀਜੀ ਪੱਖੀ ਸੇਵਾ ਵਰਤਣਾ।

### ਅੱਜ ਦਾ ਜਨਰੇਟਿਵ AI

ਇਸ ਤਰ੍ਹਾਂ ਅਸੀਂ ਅੱਜ ਦੇ ਜਨਰੇਟਿਵ AI ਤਕ ਪਹੁੰਚੇ ਹਾਂ, ਜੋ ਡੀਪ ਲਰਨਿੰਗ ਦਾ ਇੱਕ ਹਿੱਸਾ ਹੈ।

![AI, ML, DL ਅਤੇ ਜਨਰੇਟਿਵ AI](../../../translated_images/AI-diagram.c391fa518451a40de58d4f792c88adb8568d8cb4c48eed6e97b6b16e621eeb77.pa.png)

ਕਈ ਦਹਾਕਿਆਂ ਦੀ ਖੋਜ ਤੋਂ ਬਾਅਦ, ਇੱਕ ਨਵਾਂ ਮਾਡਲ ਆਰਕੀਟੈਕਚਰ – _Transformer_ – ਨੇ RNN ਦੀਆਂ ਸੀਮਾਵਾਂ ਨੂੰ ਪਾਰ ਕਰ ਲਿਆ, ਜੋ ਲੰਬੇ ਲਿਖਤ ਦੇ ਕ੍ਰਮ ਨੂੰ ਇਨਪੁਟ ਵਜੋਂ ਲੈ ਸਕਦਾ ਹੈ। Transformer ਧਿਆਨ ਮਕੈਨਿਜ਼ਮ 'ਤੇ ਆਧਾਰਿਤ ਹੈ, ਜੋ ਮਾਡਲ ਨੂੰ ਵੱਖ-ਵੱਖ ਇਨਪੁਟ ਨੂੰ ਵੱਖਰੇ ਵਜ਼ਨ ਦੇਣ ਦੀ ਆਗਿਆ ਦਿੰਦਾ ਹੈ, ਜਿੱਥੇ ਸਭ ਤੋਂ ਜ਼ਿਆਦਾ ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਹੁੰਦੀ ਹੈ, ਉਥੇ 'ਧਿਆਨ' ਕੇਂਦ੍ਰਿਤ ਕਰਦਾ ਹੈ, ਭਾਵੇਂ ਉਹ ਲਿਖਤ ਵਿੱਚ ਕਿੱਥੇ ਵੀ ਹੋਵੇ।

ਅਧਿਕਤਰ ਹਾਲੀਆ ਜਨਰੇਟਿਵ AI ਮਾਡਲ – ਜਿਨ੍ਹਾਂ ਨੂੰ ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲ (LLMs) ਵੀ ਕਹਿੰਦੇ ਹਨ, ਕਿਉਂਕਿ ਇਹ ਲਿਖਤੀ ਇਨਪੁਟ ਅਤੇ ਆਉਟਪੁਟ ਨਾਲ ਕੰਮ ਕਰਦੇ ਹਨ – ਇਸ ਆਰਕੀਟੈਕਚਰ 'ਤੇ ਆਧਾਰਿਤ ਹਨ। ਇਹ ਮਾਡਲ ਬਹੁਤ ਵੱਡੀ ਮਾਤਰਾ ਵਿੱਚ ਅਣਲੇਬਲਡ ਡਾਟਾ (ਕਿਤਾਬਾਂ, ਲੇਖਾਂ ਅਤੇ ਵੈੱਬਸਾਈਟਾਂ ਤੋਂ) 'ਤੇ ਟ੍ਰੇਨ ਕੀਤੇ ਜਾਂਦੇ ਹਨ ਅਤੇ ਵੱਖ-ਵੱਖ ਕੰਮਾਂ ਲਈ ਅਨੁਕੂਲਿਤ ਕੀਤੇ ਜਾ ਸਕਦੇ ਹਨ। ਇਹ ਗ੍ਰੈਮਰ ਅਨੁਸਾਰ ਸਹੀ ਲਿਖਤ ਤਿਆਰ ਕਰ ਸਕਦੇ ਹਨ ਅਤੇ ਕੁਝ ਹੱਦ ਤੱਕ ਰਚਨਾਤਮਕਤਾ ਵੀ ਦਿਖਾ ਸਕਦੇ ਹਨ। ਇਸ ਤਰ੍ਹਾਂ, ਇਹ ਮਸ਼ੀਨ ਨੂੰ ਲਿਖਤ ਨੂੰ ਸਮਝਣ ਦੇ ਨਾਲ-ਨਾਲ ਮਨੁੱਖੀ ਭਾਸ਼ਾ ਵਿੱਚ ਅਸਲੀ ਜਵਾਬ ਤਿਆਰ ਕਰਨ ਯੋਗ ਬਣਾਉਂਦੇ ਹਨ।

## ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲ ਕਿਵੇਂ ਕੰਮ ਕਰਦੇ ਹਨ?

ਅਗਲੇ ਅਧਿਆਇ ਵਿੱਚ ਅਸੀਂ ਵੱਖ-ਵੱਖ ਜਨਰੇਟਿਵ AI ਮਾਡਲਾਂ ਦੀ ਚਰਚਾ ਕਰਾਂਗੇ, ਪਰ ਹੁਣ ਲਈ ਆਓ ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲਾਂ ਦੀ ਕਾਰਗੁਜ਼ਾਰੀ ਨੂੰ ਵੇਖੀਏ, ਖਾਸ ਕਰਕੇ OpenAI GPT (Generative Pre-trained Transformer) ਮਾਡਲਾਂ 'ਤੇ ਧਿਆਨ ਦੇ ਕੇ।

- **Tokenizer, ਲਿਖਤ ਨੂੰ ਨੰਬਰਾਂ ਵਿੱਚ ਬਦਲਣਾ**: ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲ ਇੱਕ ਲਿਖਤ ਨੂੰ ਇਨਪੁਟ ਵਜੋਂ ਲੈਂਦੇ ਹਨ ਅਤੇ ਲਿਖਤ ਹੀ ਆਉਟਪੁਟ ਵਜੋਂ ਦਿੰਦੇ ਹਨ। ਪਰ ਕਿਉਂਕਿ ਇਹ ਸਾਂਖਿਆਕੀ ਮਾਡਲ ਹਨ, ਇਹ ਨੰਬਰਾਂ ਨਾਲ ਬਿਹਤਰ ਕੰਮ ਕਰਦੇ ਹਨ। ਇਸ ਲਈ ਹਰ ਇਨਪੁਟ ਨੂੰ ਪਹਿਲਾਂ tokenizer ਦੁਆਰਾ ਪ੍ਰਕਿਰਿਆ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਇੱਕ ਟੋਕਨ ਲਿਖਤ ਦਾ ਇੱਕ ਹਿੱਸਾ ਹੁੰਦਾ ਹੈ – ਜੋ ਕਈ ਅੱਖਰਾਂ ਦਾ ਸਮੂਹ ਹੋ ਸਕਦਾ ਹੈ। tokenizer ਦਾ ਮੁੱਖ ਕੰਮ ਇਨਪੁਟ ਨੂੰ ਟੋਕਨਾਂ ਦੀ ਲੜੀ ਵਿੱਚ ਵੰਡਣਾ ਹੈ। ਫਿਰ ਹਰ ਟੋਕਨ ਨੂੰ ਇੱਕ ਟੋਕਨ ਇੰਡੈਕਸ ਨਾਲ ਜੋੜਿਆ ਜਾਂਦਾ ਹੈ, ਜੋ ਉਸ ਲਿਖਤ ਦੇ ਹਿੱਸੇ ਦਾ ਪੂਰਾ ਨੰਬਰੀ ਰੂਪ ਹੈ।

![ਟੋਕਨਾਈਜ਼ੇਸ਼ਨ ਦਾ ਉਦਾਹਰਣ](../../../translated_images/tokenizer-example.80a5c151ee7d1bd485eff5aca60ac3d2c1eaaff4c0746e09b98c696c959afbfa.pa.png)

- **ਆਉਟਪੁਟ ਟੋਕਨਾਂ ਦੀ ਭਵਿੱਖਬਾਣੀ**: ਮਾਡਲ n ਟੋਕਨਾਂ ਨੂੰ ਇਨਪੁਟ ਵਜੋਂ ਲੈਂਦਾ ਹੈ (ਜਿੱਥੇ n ਮਾਡਲ ਦੇ ਅਨੁਸਾਰ ਵੱਖਰਾ ਹੋ ਸਕਦਾ ਹੈ) ਅਤੇ ਇੱਕ ਟੋਕਨ ਨੂੰ ਆਉਟਪੁਟ ਵਜੋਂ ਭਵਿੱਖਬਾਣੀ ਕਰਦਾ ਹੈ। ਇਹ ਟੋਕਨ ਫਿਰ ਅਗਲੇ ਚੱਕਰ ਦੇ ਇਨਪੁਟ ਵਿੱਚ ਸ਼ਾਮਲ ਕੀਤਾ ਜਾਂਦਾ ਹੈ, ਜਿਸ ਨਾਲ ਇੱਕ ਜਾਂ ਵੱਧ ਵਾਕਾਂਸ਼ਾਂ ਦਾ ਜਵਾਬ ਮਿਲਦਾ ਹੈ। ਇਸ ਕਰਕੇ ਜੇ ਤੁਸੀਂ ਕਦੇ ChatGPT ਨਾਲ ਖੇਡਿਆ ਹੈ, ਤਾਂ ਤੁਸੀਂ ਦੇਖਿਆ ਹੋਵੇਗਾ ਕਿ ਕਈ ਵਾਰੀ ਇਹ ਵਾਕ ਦੇ ਵਿਚਕਾਰ ਹੀ ਰੁਕ ਜਾਂਦਾ ਹੈ।

- **ਚੋਣ ਪ੍ਰਕਿਰਿਆ, ਸੰਭਾਵਨਾ ਵੰਡ**: ਆਉਟਪੁਟ ਟੋਕਨ ਮਾਡਲ ਵੱਲੋਂ ਇਸਦੀ ਸੰਭਾਵਨਾ ਦੇ ਅਧਾਰ 'ਤੇ ਚੁਣਿਆ ਜਾਂਦਾ ਹੈ ਕਿ ਇਹ ਮੌਜੂਦਾ ਲਿਖਤ ਦੇ ਬਾਅਦ ਕਿੰਨਾ ਆਮ ਹੈ। ਮਾਡਲ ਸਾਰੇ ਸੰਭਾਵਿਤ ‘ਅਗਲੇ ਟੋਕਨਾਂ’ ਦੀ ਸੰਭਾਵਨਾ ਵੰਡ ਦੀ ਭਵਿੱਖਬਾਣੀ ਕਰਦਾ ਹੈ। ਪਰ ਹਮੇਸ਼ਾ ਸਭ ਤੋਂ ਵੱਧ ਸੰਭਾਵਨਾ ਵਾਲਾ ਟੋਕਨ ਨਹੀਂ ਚੁਣਿਆ ਜਾਂਦਾ। ਇਸ ਚੋਣ ਵਿੱਚ ਕੁਝ ਅਣਿਸ਼ਚਿਤਤਾ ਸ਼ਾਮਲ ਕੀਤੀ ਜਾਂਦੀ ਹੈ, ਜਿਸ ਨਾਲ ਮਾਡਲ ਇੱਕ ਨਿਰਧਾਰਿਤ ਤਰੀਕੇ ਨਾਲ ਨਹੀਂ ਚਲਦਾ – ਇੱਕੋ ਜਿਹਾ ਇਨਪੁਟ ਦੇਣ 'ਤੇ ਵੀ ਵੱਖ-ਵੱਖ ਜਵਾਬ ਮਿਲ ਸਕਦੇ ਹਨ। ਇਹ ਅਣਿਸ਼ਚਿਤਤਾ ਰਚਨਾਤਮਕ ਸੋਚ ਦੀ ਨਕਲ ਕਰਨ ਲਈ ਜੋੜੀ ਜਾਂਦੀ ਹੈ ਅਤੇ ਇਸਨੂੰ temperature ਨਾਮਕ ਪੈਰਾਮੀਟਰ ਨਾਲ ਸਮਝਿਆ ਜਾਂਦਾ ਹੈ।

## ਸਾਡਾ ਸਟਾਰਟਅਪ ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲਾਂ ਦੀ ਵਰਤੋਂ ਕਿਵੇਂ ਕਰ ਸਕਦਾ ਹੈ?

ਹੁਣ ਜਦੋਂ ਸਾਨੂੰ ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲ ਦੀ ਅੰਦਰੂਨੀ ਕਾਰਗੁਜ਼ਾਰੀ ਦੀ ਸਮਝ ਹੋ ਗਈ ਹੈ, ਆਓ ਕੁਝ ਅਮਲੀ ਉਦਾਹਰਣ ਵੇਖੀਏ ਜਿੱਥੇ ਇਹ ਮਾਡਲ ਬਹੁਤ ਵਧੀਆ ਕੰਮ ਕਰਦੇ ਹਨ, ਖਾਸ ਕਰਕੇ ਸਾਡੇ ਕਾਰੋਬਾਰੀ ਸੰਦਰਭ ਨੂੰ ਧਿਆਨ ਵਿੱਚ ਰੱਖਦੇ ਹੋਏ।

ਅਸੀਂ ਕਿਹਾ ਸੀ ਕਿ ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲ ਦੀ ਮੁੱਖ ਖੂਬੀ ਹੈ _ਕਿਸੇ ਲਿਖਤੀ ਇਨਪੁਟ ਤੋਂ ਨਵਾਂ ਲਿਖਤ ਤਿਆਰ ਕਰਨਾ_।

ਪਰ ਕਿਹੜਾ ਲਿਖਤੀ ਇਨਪੁਟ ਅਤੇ ਆਉਟਪੁਟ?

ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲ ਦਾ ਇਨਪੁਟ ਇੱਕ prompt ਕਹਿੰਦੇ ਹਨ, ਜਦਕਿ ਆਉਟਪੁਟ ਨੂੰ completion ਕਹਿੰਦੇ ਹਨ, ਜੋ ਮਾਡਲ ਦਾ ਅਗਲਾ ਟੋਕਨ ਤਿਆਰ ਕਰਨ ਦਾ ਤਰੀਕਾ ਹੈ। ਅਸੀਂ ਅੱਗੇ ਵੇਖਾਂਗੇ ਕਿ prompt ਕੀ ਹੁੰਦਾ ਹੈ ਅਤੇ ਇਸਨੂੰ ਕਿਵੇਂ ਡਿਜ਼ਾਈਨ ਕਰਨਾ ਹੈ ਤਾਂ ਜੋ ਮਾਡਲ ਤੋਂ ਵਧੀਆ ਨਤੀਜੇ ਮਿਲਣ। ਪਰ ਹੁਣ ਲਈ, ਇੱਕ prompt ਵਿੱਚ ਸ਼ਾਮਲ ਹੋ ਸਕਦਾ ਹੈ:

- ਇੱਕ **ਹਦਾਇਤ** ਜੋ ਮਾਡਲ ਤੋਂ ਉਮੀਦ ਕੀਤੀ ਗਈ ਆਉਟਪੁਟ ਦੀ ਕਿਸਮ ਦੱਸਦੀ ਹੈ। ਕਈ ਵਾਰੀ ਇਸ ਹਦਾਇਤ ਵਿੱਚ ਕੁਝ ਉਦਾਹਰਣ ਜਾਂ ਵਾਧੂ ਡਾਟਾ ਵੀ ਹੁੰਦਾ ਹੈ।

  1. ਲੇਖ, ਕਿਤਾਬ, ਉਤਪਾਦ ਸਮੀਖਿਆਵਾਂ ਆਦਿ ਦਾ ਸਾਰ, ਅਤੇ ਅਣਸੰਰਚਿਤ ਡਾਟਾ ਤੋਂ ਜਾਣਕਾਰੀਆਂ ਕੱਢਣਾ।
    
    ![ਸਾਰ ਦਾ ਉਦਾਹਰਣ](../../../translated_images/summarization-example.7b7ff97147b3d790477169f442b5e3f8f78079f152450e62c45dbdc23b1423c1.pa.png)
  
  2. ਲੇਖ, ਨਿਬੰਧ, ਅਸਾਈਨਮੈਂਟ ਆਦਿ ਦੀ ਰਚਨਾਤਮਕ ਸੋਚ ਅਤੇ ਡਿਜ਼ਾਈਨ।
      
     ![ਰਚਨਾਤਮਕ ਲਿਖਤ ਦਾ ਉਦਾਹਰਣ](../../../translated_images/creative-writing-example.e24a685b5a543ad1287ad8f6c963019518920e92a1cf7510f354e85b0830fbe8.pa.png)

- ਇੱਕ **ਸਵਾਲ**, ਜੋ ਕਿਸੇ ਏਜੰਟ ਨਾਲ ਗੱਲਬਾਤ ਦੇ ਰੂਪ ਵਿੱਚ ਪੁੱਛਿਆ ਗਿਆ ਹੋਵੇ।
  
  ![ਗੱਲਬਾਤ ਦਾ
Lesson 2 ਵੱਲ ਜਾਓ ਜਿੱਥੇ ਅਸੀਂ ਵੇਖਾਂਗੇ ਕਿ ਕਿਵੇਂ ਵੱਖ-ਵੱਖ LLM ਕਿਸਮਾਂ ਨੂੰ [ਖੋਜਿਆ ਅਤੇ ਤੁਲਨਾ ਕੀਤੀ ਜਾ ਸਕਦੀ ਹੈ](../02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)!

**ਅਸਵੀਕਾਰੋਪਣ**:  
ਇਹ ਦਸਤਾਵੇਜ਼ AI ਅਨੁਵਾਦ ਸੇਵਾ [Co-op Translator](https://github.com/Azure/co-op-translator) ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਅਨੁਵਾਦਿਤ ਕੀਤਾ ਗਿਆ ਹੈ। ਜਦੋਂ ਕਿ ਅਸੀਂ ਸਹੀਅਤ ਲਈ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ, ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਰੱਖੋ ਕਿ ਸਵੈਚਾਲਿਤ ਅਨੁਵਾਦਾਂ ਵਿੱਚ ਗਲਤੀਆਂ ਜਾਂ ਅਸਮਰਥਤਾਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ। ਮੂਲ ਦਸਤਾਵੇਜ਼ ਆਪਣੀ ਮੂਲ ਭਾਸ਼ਾ ਵਿੱਚ ਪ੍ਰਮਾਣਿਕ ਸਰੋਤ ਮੰਨਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ। ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਲਈ, ਪੇਸ਼ੇਵਰ ਮਨੁੱਖੀ ਅਨੁਵਾਦ ਦੀ ਸਿਫਾਰਸ਼ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਅਸੀਂ ਇਸ ਅਨੁਵਾਦ ਦੀ ਵਰਤੋਂ ਤੋਂ ਉਤਪੰਨ ਕਿਸੇ ਵੀ ਗਲਤਫਹਿਮੀ ਜਾਂ ਗਲਤ ਵਿਆਖਿਆ ਲਈ ਜ਼ਿੰਮੇਵਾਰ ਨਹੀਂ ਹਾਂ।