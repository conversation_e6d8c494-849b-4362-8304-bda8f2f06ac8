<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "a45c318dc6ebc2604f35b8b829f93af2",
  "translation_date": "2025-07-09T09:59:36+00:00",
  "source_file": "04-prompt-engineering-fundamentals/README.md",
  "language_code": "pa"
}
-->
# ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਦੇ ਮੂਲ ਤੱਤ

[![Prompt Engineering Fundamentals](../../../translated_images/04-lesson-banner.a2c90deba7fedacda69f35b41636a8951ec91c2e33f5420b1254534ac85bc18e.pa.png)](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)

## ਪਰਿਚਯ  
ਇਹ ਮੋਡੀਊਲ ਜਨਰੇਟਿਵ AI ਮਾਡਲਾਂ ਵਿੱਚ ਪ੍ਰਭਾਵਸ਼ਾਲੀ ਪ੍ਰਾਂਪਟ ਬਣਾਉਣ ਲਈ ਜਰੂਰੀ ਧਾਰਣਾਵਾਂ ਅਤੇ ਤਕਨੀਕਾਂ ਨੂੰ ਕਵਰ ਕਰਦਾ ਹੈ। ਤੁਸੀਂ ਜਿਵੇਂ LLM ਨੂੰ ਪ੍ਰਾਂਪਟ ਲਿਖਦੇ ਹੋ, ਉਹ ਵੀ ਮਹੱਤਵਪੂਰਨ ਹੁੰਦਾ ਹੈ। ਧਿਆਨ ਨਾਲ ਬਣਾਇਆ ਗਿਆ ਪ੍ਰਾਂਪਟ ਬਿਹਤਰ ਜਵਾਬ ਪ੍ਰਾਪਤ ਕਰਨ ਵਿੱਚ ਸਹਾਇਕ ਹੁੰਦਾ ਹੈ। ਪਰ _ਪ੍ਰਾਂਪਟ_ ਅਤੇ _ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ_ ਵਰਗੇ ਸ਼ਬਦਾਂ ਦਾ ਕੀ ਅਰਥ ਹੈ? ਅਤੇ ਮੈਂ LLM ਨੂੰ ਭੇਜੇ ਜਾਣ ਵਾਲੇ ਪ੍ਰਾਂਪਟ _ਇਨਪੁੱਟ_ ਨੂੰ ਕਿਵੇਂ ਸੁਧਾਰ ਸਕਦਾ ਹਾਂ? ਇਹ ਉਹ ਸਵਾਲ ਹਨ ਜਿਨ੍ਹਾਂ ਦੇ ਜਵਾਬ ਅਸੀਂ ਇਸ ਅਤੇ ਅਗਲੇ ਅਧਿਆਇ ਵਿੱਚ ਲੱਭਣ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰਾਂਗੇ।

_ਜਨਰੇਟਿਵ AI_ ਯੂਜ਼ਰ ਦੀਆਂ ਬੇਨਤੀਆਂ ਦੇ ਜਵਾਬ ਵਿੱਚ ਨਵਾਂ ਸਮੱਗਰੀ (ਜਿਵੇਂ ਕਿ ਟੈਕਸਟ, ਚਿੱਤਰ, ਆਡੀਓ, ਕੋਡ ਆਦਿ) ਬਣਾਉਣ ਦੇ ਯੋਗ ਹੈ। ਇਹ _ਲਾਰਜ ਲੈਂਗਵੇਜ ਮਾਡਲ_ ਜਿਵੇਂ OpenAI ਦੇ GPT ("Generative Pre-trained Transformer") ਸੀਰੀਜ਼ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਹੁੰਦਾ ਹੈ, ਜੋ ਕੁਦਰਤੀ ਭਾਸ਼ਾ ਅਤੇ ਕੋਡ ਲਈ ਟ੍ਰੇਨ ਕੀਤੇ ਜਾਂਦੇ ਹਨ।

ਹੁਣ ਯੂਜ਼ਰ ਇਨ੍ਹਾਂ ਮਾਡਲਾਂ ਨਾਲ ਚੈਟ ਵਰਗੇ ਜਾਣੂ ਢੰਗ ਨਾਲ ਗੱਲਬਾਤ ਕਰ ਸਕਦੇ ਹਨ, ਬਿਨਾਂ ਕਿਸੇ ਤਕਨੀਕੀ ਗਿਆਨ ਜਾਂ ਟ੍ਰੇਨਿੰਗ ਦੀ ਲੋੜ ਦੇ। ਮਾਡਲ _ਪ੍ਰਾਂਪਟ-ਆਧਾਰਿਤ_ ਹੁੰਦੇ ਹਨ - ਯੂਜ਼ਰ ਟੈਕਸਟ ਇਨਪੁੱਟ (ਪ੍ਰਾਂਪਟ) ਭੇਜਦੇ ਹਨ ਅਤੇ AI ਤੋਂ ਜਵਾਬ (ਕੰਪਲੀਸ਼ਨ) ਪ੍ਰਾਪਤ ਕਰਦੇ ਹਨ। ਫਿਰ ਉਹ "AI ਨਾਲ ਚੈਟ" ਕਰਦੇ ਹੋਏ ਕਈ ਵਾਰੀ ਗੱਲਬਾਤ ਕਰ ਸਕਦੇ ਹਨ, ਆਪਣੇ ਪ੍ਰਾਂਪਟ ਨੂੰ ਸੁਧਾਰਦੇ ਰਹਿੰਦੇ ਹਨ ਜਦ ਤੱਕ ਜਵਾਬ ਉਮੀਦਾਂ ਦੇ ਅਨੁਕੂਲ ਨਾ ਹੋ ਜਾਵੇ।

ਹੁਣ "ਪ੍ਰਾਂਪਟ" ਜਨਰੇਟਿਵ AI ਐਪਸ ਲਈ ਮੁੱਖ _ਪ੍ਰੋਗ੍ਰਾਮਿੰਗ ਇੰਟਰਫੇਸ_ ਬਣ ਗਏ ਹਨ, ਜੋ ਮਾਡਲਾਂ ਨੂੰ ਦੱਸਦੇ ਹਨ ਕਿ ਕੀ ਕਰਨਾ ਹੈ ਅਤੇ ਵਾਪਸ ਆਉਣ ਵਾਲੇ ਜਵਾਬਾਂ ਦੀ ਗੁਣਵੱਤਾ ਨੂੰ ਪ੍ਰਭਾਵਿਤ ਕਰਦੇ ਹਨ। "ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ" ਇੱਕ ਤੇਜ਼ੀ ਨਾਲ ਵਧਦਾ ਖੇਤਰ ਹੈ ਜੋ ਪ੍ਰਾਂਪਟਾਂ ਦੇ _ਡਿਜ਼ਾਈਨ ਅਤੇ ਅਪਟੀਮਾਈਜ਼ੇਸ਼ਨ_ 'ਤੇ ਧਿਆਨ ਕੇਂਦਰਿਤ ਕਰਦਾ ਹੈ ਤਾਂ ਜੋ ਵੱਡੇ ਪੱਧਰ 'ਤੇ ਲਗਾਤਾਰ ਅਤੇ ਗੁਣਵੱਤਾ ਵਾਲੇ ਜਵਾਬ ਮਿਲ ਸਕਣ।

## ਸਿੱਖਣ ਦੇ ਲਕੜੇ

ਇਸ ਪਾਠ ਵਿੱਚ ਅਸੀਂ ਸਿੱਖਾਂਗੇ ਕਿ ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਕੀ ਹੈ, ਇਹ ਕਿਉਂ ਜਰੂਰੀ ਹੈ, ਅਤੇ ਕਿਸ ਤਰ੍ਹਾਂ ਅਸੀਂ ਕਿਸੇ ਮਾਡਲ ਅਤੇ ਐਪਲੀਕੇਸ਼ਨ ਦੇ ਉਦੇਸ਼ ਲਈ ਪ੍ਰਭਾਵਸ਼ਾਲੀ ਪ੍ਰਾਂਪਟ ਤਿਆਰ ਕਰ ਸਕਦੇ ਹਾਂ। ਅਸੀਂ ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਦੇ ਮੁੱਖ ਧਾਰਣਾਵਾਂ ਅਤੇ ਸਭ ਤੋਂ ਵਧੀਆ ਅਭਿਆਸਾਂ ਨੂੰ ਸਮਝਾਂਗੇ - ਅਤੇ ਇੱਕ ਇੰਟਰਐਕਟਿਵ Jupyter Notebook "ਸੈਂਡਬਾਕਸ" ਵਾਤਾਵਰਣ ਬਾਰੇ ਜਾਣੂ ਹੋਵਾਂਗੇ ਜਿੱਥੇ ਅਸੀਂ ਇਹ ਧਾਰਣਾਵਾਂ ਅਸਲੀ ਉਦਾਹਰਣਾਂ 'ਤੇ ਲਾਗੂ ਕਰ ਸਕਦੇ ਹਾਂ।

ਇਸ ਪਾਠ ਦੇ ਅੰਤ ਤੱਕ ਅਸੀਂ ਸਮਰੱਥ ਹੋਵਾਂਗੇ:

1. ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਕੀ ਹੈ ਅਤੇ ਇਹ ਕਿਉਂ ਜਰੂਰੀ ਹੈ, ਸਮਝਾਉਣਾ।  
2. ਪ੍ਰਾਂਪਟ ਦੇ ਹਿੱਸਿਆਂ ਦਾ ਵਰਣਨ ਕਰਨਾ ਅਤੇ ਉਹ ਕਿਵੇਂ ਵਰਤੇ ਜਾਂਦੇ ਹਨ।  
3. ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਲਈ ਸਭ ਤੋਂ ਵਧੀਆ ਅਭਿਆਸ ਅਤੇ ਤਕਨੀਕਾਂ ਸਿੱਖਣਾ।  
4. ਸਿੱਖੀਆਂ ਹੋਈਆਂ ਤਕਨੀਕਾਂ ਨੂੰ ਅਸਲੀ ਉਦਾਹਰਣਾਂ 'ਤੇ ਲਾਗੂ ਕਰਨਾ, OpenAI ਐਂਡਪੌਇੰਟ ਦੀ ਵਰਤੋਂ ਕਰਕੇ।

## ਮੁੱਖ ਸ਼ਬਦ

ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ: AI ਮਾਡਲਾਂ ਨੂੰ ਚਾਹੀਦੇ ਨਤੀਜੇ ਦੇਣ ਲਈ ਇਨਪੁੱਟ ਡਿਜ਼ਾਈਨ ਅਤੇ ਸੁਧਾਰਨ ਦੀ ਪ੍ਰਕਿਰਿਆ।  
ਟੋਕਨਾਈਜ਼ੇਸ਼ਨ: ਟੈਕਸਟ ਨੂੰ ਛੋਟੇ ਹਿੱਸਿਆਂ (ਟੋਕਨ) ਵਿੱਚ ਬਦਲਣ ਦੀ ਪ੍ਰਕਿਰਿਆ, ਜਿਸਨੂੰ ਮਾਡਲ ਸਮਝ ਸਕਦਾ ਹੈ ਅਤੇ ਪ੍ਰਕਿਰਿਆ ਕਰ ਸਕਦਾ ਹੈ।  
ਇੰਸਟ੍ਰਕਸ਼ਨ-ਟਿਊਨਡ LLMs: ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲ ਜੋ ਖਾਸ ਹੁਕਮਾਂ ਨਾਲ ਸੁਧਾਰੇ ਗਏ ਹਨ ਤਾਂ ਜੋ ਜਵਾਬਾਂ ਦੀ ਸਹੀਅਤ ਅਤੇ ਸਬੰਧਤਾ ਵਧੇ।

## ਸਿੱਖਣ ਦਾ ਸੈਂਡਬਾਕਸ

ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਅਜੇ ਵੀ ਵਿਗਿਆਨ ਨਾਲੋਂ ਜ਼ਿਆਦਾ ਕਲਾ ਹੈ। ਇਸ ਵਿੱਚ ਆਪਣੀ ਸਮਝ ਨੂੰ ਸੁਧਾਰਨ ਦਾ ਸਭ ਤੋਂ ਵਧੀਆ ਤਰੀਕਾ ਹੈ _ਵਧੇਰੇ ਅਭਿਆਸ_ ਕਰਨਾ ਅਤੇ ਟ੍ਰਾਇਲ-ਐਂਡ-ਐਰਰ ਅਪ੍ਰੋਚ ਅਪਣਾਉਣਾ ਜੋ ਐਪਲੀਕੇਸ਼ਨ ਖੇਤਰ ਦੀ ਮਹਾਰਤ ਨੂੰ ਸਿਫਾਰਸ਼ੀ ਤਕਨੀਕਾਂ ਅਤੇ ਮਾਡਲ-ਵਿਸ਼ੇਸ਼ ਸੁਧਾਰਾਂ ਨਾਲ ਜੋੜਦਾ ਹੈ।

ਇਸ ਪਾਠ ਨਾਲ ਜੁੜਿਆ Jupyter Notebook ਇੱਕ _ਸੈਂਡਬਾਕਸ_ ਵਾਤਾਵਰਣ ਪ੍ਰਦਾਨ ਕਰਦਾ ਹੈ ਜਿੱਥੇ ਤੁਸੀਂ ਜੋ ਸਿੱਖਦੇ ਹੋ, ਉਸਨੂੰ ਅਮਲ ਵਿੱਚ ਲਿਆ ਸਕਦੇ ਹੋ - ਜਾਂ ਤਾਂ ਜਦੋਂ ਤੁਸੀਂ ਅੱਗੇ ਵਧਦੇ ਹੋ ਜਾਂ ਅੰਤ ਵਿੱਚ ਕੋਡ ਚੈਲੰਜ ਦੇ ਹਿੱਸੇ ਵਜੋਂ। ਅਭਿਆਸ ਕਰਨ ਲਈ ਤੁਹਾਨੂੰ ਲੋੜ ਹੋਵੇਗੀ:

1. **ਇੱਕ Azure OpenAI API ਕੁੰਜੀ** - ਇੱਕ ਤੈਨਾਤ LLM ਲਈ ਸਰਵਿਸ ਐਂਡਪੌਇੰਟ।  
2. **ਇੱਕ Python ਰਨਟਾਈਮ** - ਜਿਸ ਵਿੱਚ ਨੋਟਬੁੱਕ ਚਲਾਇਆ ਜਾ ਸਕੇ।  
3. **ਲੋਕਲ Env ਵੈਰੀਏਬਲਜ਼** - _ਹੁਣੇ [SETUP](./../00-course-setup/SETUP.md?WT.mc_id=academic-105485-koreyst) ਕਦਮ ਪੂਰੇ ਕਰੋ ਤਾ ਕਿ ਤਿਆਰ ਰਹੋ_।

ਨੋਟਬੁੱਕ ਵਿੱਚ _ਸ਼ੁਰੂਆਤੀ_ ਅਭਿਆਸ ਹਨ - ਪਰ ਤੁਹਾਨੂੰ ਆਪਣੇ _Markdown_ (ਵਰਣਨ) ਅਤੇ _Code_ (ਪ੍ਰਾਂਪਟ ਬੇਨਤੀਆਂ) ਹਿੱਸੇ ਸ਼ਾਮਲ ਕਰਨ ਦੀ ਸਲਾਹ ਦਿੱਤੀ ਜਾਂਦੀ ਹੈ ਤਾਂ ਜੋ ਹੋਰ ਉਦਾਹਰਣ ਜਾਂ ਵਿਚਾਰਾਂ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰ ਸਕੋ ਅਤੇ ਪ੍ਰਾਂਪਟ ਡਿਜ਼ਾਈਨ ਲਈ ਆਪਣੀ ਸਮਝ ਬਣਾਉ।

## ਚਿੱਤਰਾਂ ਨਾਲ ਸਮਝਾਉਣ ਵਾਲਾ ਮਾਰਗਦਰਸ਼ਕ

ਕੀ ਤੁਸੀਂ ਇਸ ਪਾਠ ਵਿੱਚ ਕਵਰ ਕੀਤੇ ਮੁੱਖ ਵਿਸ਼ਿਆਂ ਦੀ ਇੱਕ ਵੱਡੀ ਤਸਵੀਰ ਦੇਖਣਾ ਚਾਹੁੰਦੇ ਹੋ? ਇਸ ਚਿੱਤਰਾਂ ਵਾਲੇ ਮਾਰਗਦਰਸ਼ਕ ਨੂੰ ਵੇਖੋ, ਜੋ ਤੁਹਾਨੂੰ ਮੁੱਖ ਵਿਸ਼ਿਆਂ ਅਤੇ ਹਰ ਇੱਕ ਵਿੱਚ ਸੋਚਣ ਲਈ ਮੁੱਖ ਬਿੰਦੂ ਦਿੰਦਾ ਹੈ। ਪਾਠ ਰੋਡਮੈਪ ਤੁਹਾਨੂੰ ਮੁੱਖ ਧਾਰਣਾਵਾਂ ਅਤੇ ਚੁਣੌਤੀਆਂ ਨੂੰ ਸਮਝਣ ਤੋਂ ਲੈ ਕੇ ਉਨ੍ਹਾਂ ਨੂੰ ਸੰਬੋਧਨ ਕਰਨ ਲਈ ਸਬੰਧਤ ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਤਕਨੀਕਾਂ ਅਤੇ ਸਭ ਤੋਂ ਵਧੀਆ ਅਭਿਆਸਾਂ ਤੱਕ ਲੈ ਜਾਂਦਾ ਹੈ। ਧਿਆਨ ਦਿਓ ਕਿ ਇਸ ਮਾਰਗਦਰਸ਼ਕ ਵਿੱਚ "ਅਡਵਾਂਸਡ ਤਕਨੀਕਾਂ" ਭਾਗ ਇਸ ਕਰਿਕੁਲਮ ਦੇ _ਅਗਲੇ_ ਅਧਿਆਇ ਵਿੱਚ ਕਵਰ ਕੀਤੇ ਸਮੱਗਰੀ ਨੂੰ ਦਰਸਾਉਂਦਾ ਹੈ।

![Illustrated Guide to Prompt Engineering](../../../translated_images/04-prompt-engineering-sketchnote.d5f33336957a1e4f623b826195c2146ef4cc49974b72fa373de6929b474e8b70.pa.png)

## ਸਾਡਾ ਸਟਾਰਟਅਪ

ਹੁਣ, ਆਓ ਗੱਲ ਕਰੀਏ ਕਿ _ਇਹ ਵਿਸ਼ਾ_ ਸਾਡੇ ਸਟਾਰਟਅਪ ਦੇ ਮਿਸ਼ਨ ਨਾਲ ਕਿਵੇਂ ਜੁੜਦਾ ਹੈ ਜੋ [ਸਿੱਖਿਆ ਵਿੱਚ AI ਨਵੀਨਤਾ ਲਿਆਉਣ](https://educationblog.microsoft.com/2023/06/collaborating-to-bring-ai-innovation-to-education?WT.mc_id=academic-105485-koreyst) ਦਾ ਹੈ। ਅਸੀਂ _ਵਿਅਕਤੀਗਤ ਸਿੱਖਿਆ_ ਵਾਲੀਆਂ AI-ਚਲਿਤ ਐਪਲੀਕੇਸ਼ਨਾਂ ਨੂੰ ਬਣਾਉਣਾ ਚਾਹੁੰਦੇ ਹਾਂ - ਤਾਂ ਆਓ ਸੋਚੀਏ ਕਿ ਸਾਡੇ ਐਪਲੀਕੇਸ਼ਨ ਦੇ ਵੱਖ-ਵੱਖ ਯੂਜ਼ਰ ਕਿਵੇਂ ਪ੍ਰਾਂਪਟ "ਡਿਜ਼ਾਈਨ" ਕਰ ਸਕਦੇ ਹਨ:

- **ਐਡਮਿਨਿਸਟ੍ਰੇਟਰ** AI ਨੂੰ ਕਹਿ ਸਕਦੇ ਹਨ ਕਿ _ਕਰੀਕੁਲਮ ਡੇਟਾ ਦਾ ਵਿਸ਼ਲੇਸ਼ਣ ਕਰਕੇ ਕਵਰੇਜ ਵਿੱਚ ਖਾਮੀਆਂ ਪਛਾਣੋ_। AI ਨਤੀਜੇ ਸੰਖੇਪ ਕਰ ਸਕਦਾ ਹੈ ਜਾਂ ਕੋਡ ਨਾਲ ਵਿਜ਼ੂਅਲਾਈਜ਼ ਕਰ ਸਕਦਾ ਹੈ।  
- **ਅਧਿਆਪਕ** AI ਨੂੰ ਕਹਿ ਸਕਦੇ ਹਨ ਕਿ _ਇੱਕ ਨਿਸ਼ਚਿਤ ਦਰਸ਼ਕ ਅਤੇ ਵਿਸ਼ੇ ਲਈ ਪਾਠ ਯੋਜਨਾ ਤਿਆਰ ਕਰੋ_। AI ਨਿਯਤ ਫਾਰਮੈਟ ਵਿੱਚ ਵਿਅਕਤੀਗਤ ਯੋਜਨਾ ਬਣਾਉਂਦਾ ਹੈ।  
- **ਵਿਦਿਆਰਥੀ** AI ਨੂੰ ਕਹਿ ਸਕਦੇ ਹਨ ਕਿ _ਉਹਨਾਂ ਨੂੰ ਮੁਸ਼ਕਲ ਵਿਸ਼ੇ ਵਿੱਚ ਟਿਊਟਰ ਕਰੋ_। AI ਹੁਣ ਵਿਦਿਆਰਥੀਆਂ ਨੂੰ ਪਾਠ, ਸੁਝਾਅ ਅਤੇ ਉਦਾਹਰਣਾਂ ਨਾਲ ਉਹਨਾਂ ਦੇ ਪੱਧਰ ਅਨੁਸਾਰ ਮਦਦ ਕਰਦਾ ਹੈ।

ਇਹ ਤਾਂ ਸਿਰਫ਼ ਸ਼ੁਰੂਆਤ ਹੈ। [Prompts For Education](https://github.com/microsoft/prompts-for-edu/tree/main?WT.mc_id=academic-105485-koreyst) ਨੂੰ ਵੇਖੋ - ਇੱਕ ਖੁੱਲ੍ਹਾ ਸਰੋਤ ਪ੍ਰਾਂਪਟ ਲਾਇਬ੍ਰੇਰੀ ਜੋ ਸਿੱਖਿਆ ਵਿਸ਼ੇਸ਼ਜ્ઞਾਂ ਵੱਲੋਂ ਸੰਭਾਲੀ ਗਈ ਹੈ - ਤਾਂ ਜੋ ਸੰਭਾਵਨਾਵਾਂ ਬਾਰੇ ਵੱਡਾ ਅੰਦਾਜ਼ਾ ਲਗਾਇਆ ਜਾ ਸਕੇ! _ਕੁਝ ਪ੍ਰਾਂਪਟਾਂ ਨੂੰ ਸੈਂਡਬਾਕਸ ਵਿੱਚ ਜਾਂ OpenAI Playground ਵਿੱਚ ਚਲਾਕੇ ਦੇਖੋ ਕਿ ਕੀ ਹੁੰਦਾ ਹੈ!_

<!--  
LESSON TEMPLATE:  
This unit should cover core concept #1.  
Reinforce the concept with examples and references.  

CONCEPT #1:  
Prompt Engineering.  
Define it and explain why it is needed.  
-->

## ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਕੀ ਹੈ?

ਅਸੀਂ ਇਸ ਪਾਠ ਦੀ ਸ਼ੁਰੂਆਤ ਕੀਤੀ ਸੀ **ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ** ਨੂੰ ਇਸ ਪ੍ਰਕਿਰਿਆ ਵਜੋਂ ਪਰਿਭਾਸ਼ਿਤ ਕਰਕੇ ਜੋ ਕਿਸੇ ਦਿੱਤੇ ਮਾਡਲ ਅਤੇ ਐਪਲੀਕੇਸ਼ਨ ਦੇ ਉਦੇਸ਼ ਲਈ ਲਗਾਤਾਰ ਅਤੇ ਗੁਣਵੱਤਾ ਵਾਲੇ ਜਵਾਬ (ਕੰਪਲੀਸ਼ਨ) ਦੇਣ ਲਈ ਟੈਕਸਟ ਇਨਪੁੱਟ (ਪ੍ਰਾਂਪਟ) ਨੂੰ _ਡਿਜ਼ਾਈਨ ਅਤੇ ਅਪਟੀਮਾਈਜ਼_ ਕਰਦੀ ਹੈ। ਅਸੀਂ ਇਸਨੂੰ ਦੋ ਕਦਮਾਂ ਦੀ ਪ੍ਰਕਿਰਿਆ ਵਜੋਂ ਸੋਚ ਸਕਦੇ ਹਾਂ:

- ਕਿਸੇ ਮਾਡਲ ਅਤੇ ਉਦੇਸ਼ ਲਈ ਸ਼ੁਰੂਆਤੀ ਪ੍ਰਾਂਪਟ ਨੂੰ _ਡਿਜ਼ਾਈਨ_ ਕਰਨਾ  
- ਜਵਾਬ ਦੀ ਗੁਣਵੱਤਾ ਸੁਧਾਰਨ ਲਈ ਪ੍ਰਾਂਪਟ ਨੂੰ ਕਈ ਵਾਰੀ _ਸੁਧਾਰਨਾ_

ਇਹ ਜ਼ਰੂਰੀ ਤੌਰ 'ਤੇ ਇੱਕ ਟ੍ਰਾਇਲ-ਐਂਡ-ਐਰਰ ਪ੍ਰਕਿਰਿਆ ਹੈ ਜਿਸ ਵਿੱਚ ਯੂਜ਼ਰ ਦੀ ਸਮਝ ਅਤੇ ਕੋਸ਼ਿਸ਼ ਲੋੜੀਂਦੀ ਹੈ ਤਾਂ ਜੋ ਵਧੀਆ ਨਤੀਜੇ ਮਿਲ ਸਕਣ। ਤਾਂ ਇਹ ਕਿਉਂ ਜਰੂਰੀ ਹੈ? ਇਸ ਸਵਾਲ ਦਾ ਜਵਾਬ ਦੇਣ ਲਈ ਸਾਨੂੰ ਪਹਿਲਾਂ ਤਿੰਨ ਧਾਰਣਾਵਾਂ ਨੂੰ ਸਮਝਣਾ ਪਵੇਗਾ:

- _ਟੋਕਨਾਈਜ਼ੇਸ਼ਨ_ = ਮਾਡਲ ਪ੍ਰਾਂਪਟ ਨੂੰ ਕਿਵੇਂ "ਦੇਖਦਾ" ਹੈ  
- _ਬੇਸ LLMs_ = ਫਾਊਂਡੇਸ਼ਨ ਮਾਡਲ ਪ੍ਰਾਂਪਟ ਨੂੰ ਕਿਵੇਂ "ਪ੍ਰਕਿਰਿਆ" ਕਰਦਾ ਹੈ  
- _ਇੰਸਟ੍ਰਕਸ਼ਨ-ਟਿਊਨਡ LLMs_ = ਮਾਡਲ ਹੁਣ "ਟਾਸਕ" ਕਿਵੇਂ ਸਮਝਦਾ ਹੈ

### ਟੋਕਨਾਈਜ਼ੇਸ਼ਨ

ਇੱਕ LLM ਪ੍ਰਾਂਪਟਾਂ ਨੂੰ _ਟੋਕਨਾਂ ਦੀ ਲੜੀ_ ਵਜੋਂ ਵੇਖਦਾ ਹੈ ਜਿੱਥੇ ਵੱਖ-ਵੱਖ ਮਾਡਲ (ਜਾਂ ਮਾਡਲ ਦੇ ਵੱਖ-ਵੱਖ ਵਰਜਨ) ਇੱਕੋ ਪ੍ਰਾਂਪਟ ਨੂੰ ਵੱਖ-ਵੱਖ ਤਰੀਕਿਆਂ ਨਾਲ ਟੋਕਨਾਈਜ਼ ਕਰ ਸਕਦੇ ਹਨ। ਕਿਉਂਕਿ LLMs ਟੋਕਨਾਂ 'ਤੇ ਟ੍ਰੇਨ ਹੁੰਦੇ ਹਨ (ਕੱਚੇ ਟੈਕਸਟ 'ਤੇ ਨਹੀਂ), ਇਸ ਲਈ ਪ੍ਰਾਂਪਟਾਂ ਦਾ ਟੋਕਨਾਈਜ਼ ਹੋਣਾ ਜਵਾਬ ਦੀ ਗੁਣਵੱਤਾ 'ਤੇ ਸਿੱਧਾ ਪ੍ਰਭਾਵ ਪਾਉਂਦਾ ਹੈ।

ਟੋਕਨਾਈਜ਼ੇਸ਼ਨ ਕਿਵੇਂ ਕੰਮ ਕਰਦੀ ਹੈ, ਇਹ ਸਮਝਣ ਲਈ [OpenAI Tokenizer](https://platform.openai.com/tokenizer?WT.mc_id=academic-105485-koreyst) ਵਰਗੇ ਟੂਲਜ਼ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰੋ। ਆਪਣਾ ਪ੍ਰਾਂਪਟ ਕਾਪੀ ਕਰੋ ਅਤੇ ਦੇਖੋ ਕਿ ਇਹ ਕਿਵੇਂ ਟੋਕਨਾਂ ਵਿੱਚ ਬਦਲਦਾ ਹੈ, ਖ਼ਾਸ ਕਰਕੇ ਖਾਲੀ ਥਾਵਾਂ ਅਤੇ ਵਿਸ਼ੇਸ਼ ਚਿੰਨ੍ਹਾਂ ਨੂੰ ਕਿਵੇਂ ਸੰਭਾਲਿਆ ਜਾਂਦਾ ਹੈ। ਧਿਆਨ ਦਿਓ ਕਿ ਇਹ ਉਦਾਹਰਣ ਇੱਕ ਪੁਰਾਣੇ LLM (GPT-3) ਨੂੰ ਦਿਖਾਉਂਦਾ ਹੈ - ਇਸ ਲਈ ਨਵੇਂ ਮਾਡਲ ਨਾਲ ਕੋਸ਼ਿਸ਼ ਕਰਨ 'ਤੇ ਨਤੀਜੇ ਵੱਖਰੇ ਹੋ ਸਕਦੇ ਹਨ।

![Tokenization](../../../translated_images/04-tokenizer-example.e71f0a0f70356c5c7d80b21e8753a28c18a7f6d4aaa1c4b08e65d17625e85642.pa.png)

### ਧਾਰਣਾ: ਫਾਊਂਡੇਸ਼ਨ ਮਾਡਲ

ਜਦੋਂ ਪ੍ਰਾਂਪਟ ਟੋਕਨਾਈਜ਼ ਹੋ ਜਾਂਦਾ ਹੈ, ਤਾਂ ["ਬੇਸ LLM"](https://blog.gopenai.com/an-introduction-to-base-and-instruction-tuned-large-language-models-8de102c785a6?WT.mc_id=academic-105485-koreyst) (ਜਾਂ ਫਾਊਂਡੇਸ਼ਨ ਮਾਡਲ) ਦਾ ਮੁੱਖ ਕੰਮ ਉਸ ਲੜੀ ਵਿੱਚ ਅਗਲਾ ਟੋਕਨ ਭਵਿੱਖਵਾਣੀ ਕਰਨਾ ਹੁੰਦਾ ਹੈ। ਕਿਉਂਕਿ LLMs ਵੱਡੇ ਟੈਕਸਟ ਡੇਟਾਸੈੱਟ 'ਤੇ ਟ੍ਰੇਨ ਹੁੰਦੇ ਹਨ, ਉਹ ਟੋਕਨਾਂ ਦੇ ਅੰਕੜਿਆਂ ਵਾਲੇ ਸੰਬੰਧਾਂ ਨੂੰ ਚੰਗੀ ਤਰ੍ਹਾਂ ਸਮਝਦੇ ਹਨ ਅਤੇ ਭਵਿੱਖਵਾਣੀ ਵਿਸ਼ਵਾਸਯੋਗ ਤਰੀਕੇ ਨਾਲ ਕਰ ਸਕਦੇ ਹਨ। ਉਹ ਪ੍ਰਾਂਪਟ ਜਾਂ ਟੋਕਨ ਦੇ ਸ਼ਬਦਾਂ ਦਾ _ਅਰਥ_ ਨਹੀਂ ਸਮਝਦੇ; ਉਹ ਸਿਰਫ਼ ਇੱਕ ਪੈਟਰਨ ਵੇਖਦੇ ਹਨ ਜਿਸਨੂੰ ਉਹ ਆਪਣੀ ਅਗਲੀ ਭਵਿੱਖਵਾਣੀ ਨਾਲ "ਪੂਰਾ" ਕਰ ਸਕਦੇ ਹਨ। ਉਹ ਇਸ ਲੜੀ ਦੀ ਭਵਿੱਖਵਾਣੀ ਜਾਰੀ ਰੱਖ ਸਕਦੇ ਹਨ ਜਦ ਤੱਕ ਯੂਜ਼ਰ ਰੁਕਾਵਟ ਨਾ ਕਰੇ ਜਾਂ ਕੋਈ ਪਹਿਲਾਂ ਤੋਂ ਨਿਰਧਾਰਿਤ ਸ਼ਰਤ ਨਾ ਪੂਰੀ ਹੋ ਜਾਵੇ।

ਤੁਸੀਂ ਦੇਖਣਾ ਚਾਹੁੰਦੇ ਹੋ ਕਿ ਪ੍ਰਾਂਪਟ-ਆਧਾਰਿਤ ਕੰਪਲੀਸ਼ਨ ਕਿਵੇਂ ਕੰਮ ਕਰਦਾ ਹੈ? ਉਪਰੋਕਤ ਪ੍ਰਾਂਪਟ ਨੂੰ Azure OpenAI Studio ਦੇ [_Chat Playground_](https://oai.azure.com/playground?WT.mc_id=academic-105485-koreyst) ਵਿੱਚ ਡਿਫਾਲਟ ਸੈਟਿੰਗਾਂ ਨਾਲ ਦਰਜ ਕਰੋ। ਸਿਸਟਮ ਪ੍ਰਾਂਪਟਾਂ ਨੂੰ ਜਾਣਕਾਰੀ ਦੀ ਬੇਨਤੀ ਵਜੋਂ ਸਮਝਦਾ ਹੈ - ਇਸ ਲਈ ਤੁਹਾਨੂੰ ਇਸ ਸੰਦਰਭ ਨੂੰ ਪੂਰਾ ਕਰਨ ਵਾਲਾ ਜਵਾਬ ਮਿਲੇਗਾ।

ਪਰ ਜੇ ਯੂਜ਼ਰ ਕੁਝ ਖਾਸ ਦੇਖਣਾ ਚਾਹੁੰਦਾ ਹੈ ਜੋ ਕਿਸੇ ਮਾਪਦੰਡ ਜਾਂ ਟਾਸਕ ਦੇ ਉਦੇਸ਼ ਨੂੰ ਪੂਰਾ ਕਰਦਾ ਹੋਵੇ? ਇੱਥੇ _ਇੰਸਟ
IMPORTANT RULES:
1. ਅਨੁਵਾਦ ਦੇ ਆਲੇ-ਦੁਆਲੇ '''markdown ਜਾਂ ਹੋਰ ਕਿਸੇ ਵੀ ਟੈਗ ਨੂੰ ਨਾ ਜੋੜੋ
2. ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਅਨੁਵਾਦ ਬਹੁਤ ਜ਼ਿਆਦਾ ਸ਼ਾਬਦਿਕ ਨਾ ਲੱਗੇ
3. ਟਿੱਪਣੀਆਂ ਨੂੰ ਵੀ ਅਨੁਵਾਦ ਕਰੋ
4. ਇਹ ਫਾਈਲ Markdown ਫਾਰਮੈਟ ਵਿੱਚ ਲਿਖੀ ਗਈ ਹੈ - ਇਸਨੂੰ XML ਜਾਂ HTML ਵਾਂਗ ਨਾ ਸਮਝੋ
5. ਅਨੁਵਾਦ ਨਾ ਕਰੋ:
   - [!NOTE], [!WARNING], [!TIP], [!IMPORTANT], [!CAUTION]
   - ਵੈਰੀਏਬਲ ਨਾਮ, ਫੰਕਸ਼ਨ ਨਾਮ, ਕਲਾਸ ਨਾਮ
   - ਜਿਹੜੇ ਪਲੇਸਹੋਲਡਰ ਜਿਵੇਂ @@INLINE_CODE_x@@ ਜਾਂ @@CODE_BLOCK_x@@ ਹਨ
   - URLs ਜਾਂ ਪਾਥ
6. ਸਾਰੇ ਮੂਲ markdown ਫਾਰਮੈਟਿੰਗ ਨੂੰ ਬਰਕਰਾਰ ਰੱਖੋ
7. ਸਿਰਫ ਅਨੁਵਾਦਿਤ ਸਮੱਗਰੀ ਵਾਪਸ ਕਰੋ, ਕਿਸੇ ਹੋਰ ਟੈਗ ਜਾਂ ਮਾਰਕਅੱਪ ਦੇ ਬਿਨਾਂ

> **ਪ੍ਰੰਪਟ:** 2076 ਦੇ ਮਾਰਸ਼ੀਅਨ ਯੁੱਧ 'ਤੇ ਇੱਕ ਪਾਠ ਯੋਜਨਾ ਤਿਆਰ ਕਰੋ।
ਇੱਕ ਵੈੱਬ ਖੋਜ ਨੇ ਮੈਨੂੰ ਦੱਸਿਆ ਕਿ ਮਾਰਸ਼ੀਅਨ ਜੰਗਾਂ ਬਾਰੇ ਕਲਪਨਾਤਮਕ ਕਹਾਣੀਆਂ (ਜਿਵੇਂ ਕਿ ਟੈਲੀਵਿਜ਼ਨ ਸੀਰੀਜ਼ ਜਾਂ ਕਿਤਾਬਾਂ) ਹਨ - ਪਰ 2076 ਵਿੱਚ ਕੋਈ ਨਹੀਂ। ਸਧਾਰਣ ਸਮਝ ਵੀ ਸਾਨੂੰ ਦੱਸਦੀ ਹੈ ਕਿ 2076 _ਭਵਿੱਖ ਵਿੱਚ_ ਹੈ ਅਤੇ ਇਸ ਲਈ, ਇਸਨੂੰ ਕਿਸੇ ਅਸਲੀ ਘਟਨਾ ਨਾਲ ਜੋੜਿਆ ਨਹੀਂ ਜਾ ਸਕਦਾ।

ਤਾਂ ਫਿਰ ਕੀ ਹੁੰਦਾ ਹੈ ਜਦੋਂ ਅਸੀਂ ਇਹ ਪ੍ਰਾਂਪਟ ਵੱਖ-ਵੱਖ LLM ਪ੍ਰਦਾਤਾਵਾਂ ਨਾਲ ਚਲਾਉਂਦੇ ਹਾਂ?

> **Response 1**: OpenAI Playground (GPT-35)

![Response 1](../../../translated_images/04-fabrication-oai.5818c4e0b2a2678c40e0793bf873ef4a425350dd0063a183fb8ae02cae63aa0c.pa.png)

> **Response 2**: Azure OpenAI Playground (GPT-35)

![Response 2](../../../translated_images/04-fabrication-aoai.b14268e9ecf25caf613b7d424c16e2a0dc5b578f8f960c0c04d4fb3a68e6cf61.pa.png)

> **Response 3**: : Hugging Face Chat Playground (LLama-2)

![Response 3](../../../translated_images/04-fabrication-huggingchat.faf82a0a512789565e410568bce1ac911075b943dec59b1ef4080b61723b5bf4.pa.png)

ਜਿਵੇਂ ਉਮੀਦ ਸੀ, ਹਰ ਮਾਡਲ (ਜਾਂ ਮਾਡਲ ਵਰਜਨ) ਥੋੜ੍ਹੇ ਬਹੁਤ ਵੱਖਰੇ ਜਵਾਬ ਦਿੰਦਾ ਹੈ, ਜੋ ਕਿ ਸਟੋਚਾਸਟਿਕ ਵਿਹਾਰ ਅਤੇ ਮਾਡਲ ਦੀ ਸਮਰੱਥਾ ਵਿੱਚ ਫਰਕਾਂ ਕਰਕੇ ਹੁੰਦਾ ਹੈ। ਉਦਾਹਰਨ ਵਜੋਂ, ਇੱਕ ਮਾਡਲ 8ਵੀਂ ਕਲਾਸ ਦੇ ਦਰਸ਼ਕਾਂ ਨੂੰ ਨਿਸ਼ਾਨਾ ਬਣਾਉਂਦਾ ਹੈ ਜਦਕਿ ਦੂਜਾ ਹਾਈ-ਸਕੂਲ ਦੇ ਵਿਦਿਆਰਥੀ ਨੂੰ ਮੰਨਦਾ ਹੈ। ਪਰ ਤਿੰਨੋ ਮਾਡਲਾਂ ਨੇ ਅਜਿਹੇ ਜਵਾਬ ਦਿੱਤੇ ਜੋ ਇੱਕ ਅਣਜਾਣ ਵਰਤੋਂਕਾਰ ਨੂੰ ਇਹ ਯਕੀਨ ਦਿਵਾ ਸਕਦੇ ਹਨ ਕਿ ਇਹ ਘਟਨਾ ਅਸਲੀ ਸੀ।

ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਤਕਨੀਕਾਂ ਜਿਵੇਂ ਕਿ _ਮੈਟਾਪ੍ਰਾਂਪਟਿੰਗ_ ਅਤੇ _ਟੈਮਪਰੇਚਰ ਕਨਫਿਗਰੇਸ਼ਨ_ ਕੁਝ ਹੱਦ ਤੱਕ ਮਾਡਲ ਦੀਆਂ ਗਲਤੀਆਂ ਨੂੰ ਘਟਾ ਸਕਦੀਆਂ ਹਨ। ਨਵੀਆਂ ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ _ਆਰਕੀਟੈਕਚਰਜ਼_ ਵੀ ਨਵੇਂ ਟੂਲ ਅਤੇ ਤਕਨੀਕਾਂ ਨੂੰ ਪ੍ਰਾਂਪਟ ਫਲੋ ਵਿੱਚ ਬਿਨਾਂ ਰੁਕਾਵਟ ਦੇ ਸ਼ਾਮਲ ਕਰਦੀਆਂ ਹਨ, ਤਾਂ ਜੋ ਇਹ ਪ੍ਰਭਾਵ ਘਟਾਏ ਜਾ ਸਕਣ।

## ਕੇਸ ਸਟਡੀ: GitHub Copilot

ਆਓ ਇਸ ਭਾਗ ਨੂੰ ਖਤਮ ਕਰੀਏ ਅਤੇ ਵੇਖੀਏ ਕਿ ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਅਸਲੀ ਦੁਨੀਆ ਦੇ ਹੱਲਾਂ ਵਿੱਚ ਕਿਵੇਂ ਵਰਤੀ ਜਾਂਦੀ ਹੈ, ਇੱਕ ਕੇਸ ਸਟਡੀ ਦੇ ਰੂਪ ਵਿੱਚ: [GitHub Copilot](https://github.com/features/copilot?WT.mc_id=academic-105485-koreyst)।

GitHub Copilot ਤੁਹਾਡਾ "AI ਜੋੜੀ ਪ੍ਰੋਗ੍ਰਾਮਰ" ਹੈ - ਇਹ ਟੈਕਸਟ ਪ੍ਰਾਂਪਟਾਂ ਨੂੰ ਕੋਡ ਪੂਰਨਤਾ ਵਿੱਚ ਬਦਲਦਾ ਹੈ ਅਤੇ ਤੁਹਾਡੇ ਵਿਕਾਸ ਵਾਤਾਵਰਣ (ਜਿਵੇਂ ਕਿ Visual Studio Code) ਵਿੱਚ ਇੰਟਿਗ੍ਰੇਟ ਕੀਤਾ ਗਿਆ ਹੈ, ਤਾਂ ਜੋ ਵਰਤੋਂਕਾਰ ਨੂੰ ਬਿਨਾਂ ਰੁਕਾਵਟ ਦਾ ਅਨੁਭਵ ਮਿਲੇ। ਹੇਠਾਂ ਦਿੱਤੇ ਬਲੌਗ ਸੀਰੀਜ਼ ਵਿੱਚ ਦਰਜ ਹੈ ਕਿ ਸਭ ਤੋਂ ਪਹਿਲਾ ਵਰਜਨ OpenAI Codex ਮਾਡਲ 'ਤੇ ਆਧਾਰਿਤ ਸੀ - ਇੰਜੀਨੀਅਰਾਂ ਨੇ ਜਲਦੀ ਹੀ ਸਮਝਿਆ ਕਿ ਮਾਡਲ ਨੂੰ ਫਾਈਨ-ਟਿਊਨ ਕਰਨ ਅਤੇ ਬਿਹਤਰ ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਤਕਨੀਕਾਂ ਵਿਕਸਿਤ ਕਰਨ ਦੀ ਲੋੜ ਹੈ, ਤਾਂ ਜੋ ਕੋਡ ਦੀ ਗੁਣਵੱਤਾ ਸੁਧਰੇ। ਜੁਲਾਈ ਵਿੱਚ, ਉਹਨਾਂ ਨੇ [ਇੱਕ ਸੁਧਾਰਿਆ ਹੋਇਆ AI ਮਾਡਲ ਜਾਰੀ ਕੀਤਾ ਜੋ Codex ਤੋਂ ਅੱਗੇ ਵਧਦਾ ਹੈ](https://github.blog/2023-07-28-smarter-more-efficient-coding-github-copilot-goes-beyond-codex-with-improved-ai-model/?WT.mc_id=academic-105485-koreyst) ਤੇਜ਼ ਸਿਫਾਰਸ਼ਾਂ ਲਈ।

ਉਹਨਾਂ ਦੀ ਸਿੱਖਣ ਯਾਤਰਾ ਨੂੰ ਸਮਝਣ ਲਈ ਪੋਸਟਾਂ ਨੂੰ ਕ੍ਰਮਵਾਰ ਪੜ੍ਹੋ।

- **ਮਈ 2023** | [GitHub Copilot ਤੁਹਾਡੇ ਕੋਡ ਨੂੰ ਸਮਝਣ ਵਿੱਚ ਹੋਰ ਬਿਹਤਰ ਹੋ ਰਿਹਾ ਹੈ](https://github.blog/2023-05-17-how-github-copilot-is-getting-better-at-understanding-your-code/?WT.mc_id=academic-105485-koreyst)
- **ਮਈ 2023** | [GitHub ਦੇ ਅੰਦਰ: GitHub Copilot ਦੇ ਪਿੱਛੇ LLMs ਨਾਲ ਕੰਮ ਕਰਨਾ](https://github.blog/2023-05-17-inside-github-working-with-the-llms-behind-github-copilot/?WT.mc_id=academic-105485-koreyst)
- **ਜੂਨ 2023** | [GitHub Copilot ਲਈ ਬਿਹਤਰ ਪ੍ਰਾਂਪਟ ਕਿਵੇਂ ਲਿਖੇ ਜਾਣ](https://github.blog/2023-06-20-how-to-write-better-prompts-for-github-copilot/?WT.mc_id=academic-105485-koreyst)
- **ਜੁਲਾਈ 2023** | [GitHub Copilot Codex ਤੋਂ ਅੱਗੇ ਵਧਦਾ ਹੈ ਸੁਧਾਰਿਆ ਹੋਇਆ AI ਮਾਡਲ ਨਾਲ](https://github.blog/2023-07-28-smarter-more-efficient-coding-github-copilot-goes-beyond-codex-with-improved-ai-model/?WT.mc_id=academic-105485-koreyst)
- **ਜੁਲਾਈ 2023** | [ਡਿਵੈਲਪਰ ਲਈ ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਅਤੇ LLMs ਦੀ ਗਾਈਡ](https://github.blog/2023-07-17-prompt-engineering-guide-generative-ai-llms/?WT.mc_id=academic-105485-koreyst)
- **ਸਤੰਬਰ 2023** | [ਇੰਟਰਪ੍ਰਾਈਜ਼ LLM ਐਪ ਕਿਵੇਂ ਬਣਾਈਏ: GitHub Copilot ਤੋਂ ਸਿੱਖਿਆ](https://github.blog/2023-09-06-how-to-build-an-enterprise-llm-application-lessons-from-github-copilot/?WT.mc_id=academic-105485-koreyst)

ਤੁਸੀਂ ਉਹਨਾਂ ਦੇ [ਇੰਜੀਨੀਅਰਿੰਗ ਬਲੌਗ](https://github.blog/category/engineering/?WT.mc_id=academic-105485-koreyst) ਨੂੰ ਵੀ ਵੇਖ ਸਕਦੇ ਹੋ, ਜਿਵੇਂ ਕਿ [ਇਹ](https://github.blog/2023-09-27-how-i-used-github-copilot-chat-to-build-a-reactjs-gallery-prototype/?WT.mc_id=academic-105485-koreyst) ਜੋ ਦਿਖਾਉਂਦਾ ਹੈ ਕਿ ਇਹ ਮਾਡਲ ਅਤੇ ਤਕਨੀਕਾਂ ਅਸਲੀ ਦੁਨੀਆ ਦੇ ਐਪਲੀਕੇਸ਼ਨਾਂ ਨੂੰ ਚਲਾਉਣ ਲਈ ਕਿਵੇਂ _ਲਾਗੂ_ ਕੀਤੀਆਂ ਜਾਂਦੀਆਂ ਹਨ।

---

<!--
LESSON TEMPLATE:
ਇਹ ਯੂਨਿਟ ਮੁੱਖ ਧਾਰਾ #2 ਨੂੰ ਕਵਰ ਕਰੇਗੀ।
ਧਾਰਨਾ ਨੂੰ ਉਦਾਹਰਨਾਂ ਅਤੇ ਹਵਾਲਿਆਂ ਨਾਲ ਮਜ਼ਬੂਤ ਕਰੋ।

CONCEPT #2:
ਪ੍ਰਾਂਪਟ ਡਿਜ਼ਾਈਨ।
ਉਦਾਹਰਨਾਂ ਨਾਲ ਦਰਸਾਇਆ ਗਿਆ।
-->

## ਪ੍ਰਾਂਪਟ ਬਣਾਉਣਾ

ਅਸੀਂ ਵੇਖਿਆ ਕਿ ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਕਿਉਂ ਮਹੱਤਵਪੂਰਨ ਹੈ - ਹੁਣ ਆਓ ਸਮਝੀਏ ਕਿ ਪ੍ਰਾਂਪਟ ਕਿਵੇਂ _ਬਣਾਏ_ ਜਾਂਦੇ ਹਨ ਤਾਂ ਜੋ ਅਸੀਂ ਵੱਖ-ਵੱਖ ਤਕਨੀਕਾਂ ਦਾ ਮੁਲਾਂਕਣ ਕਰ ਸਕੀਏ ਅਤੇ ਬਿਹਤਰ ਪ੍ਰਾਂਪਟ ਡਿਜ਼ਾਈਨ ਕਰ ਸਕੀਏ।

### ਬੁਨਿਆਦੀ ਪ੍ਰਾਂਪਟ

ਆਓ ਬੁਨਿਆਦੀ ਪ੍ਰਾਂਪਟ ਨਾਲ ਸ਼ੁਰੂ ਕਰੀਏ: ਮਾਡਲ ਨੂੰ ਭੇਜਿਆ ਗਿਆ ਸਿਰਫ਼ ਟੈਕਸਟ ਇਨਪੁੱਟ ਬਿਨਾਂ ਕਿਸੇ ਹੋਰ ਸੰਦਰਭ ਦੇ। ਇਹ ਇੱਕ ਉਦਾਹਰਨ ਹੈ - ਜਦੋਂ ਅਸੀਂ US ਦੇ ਰਾਸ਼ਟਰੀ ਗੀਤ ਦੇ ਪਹਿਲੇ ਕੁਝ ਸ਼ਬਦ OpenAI [Completion API](https://platform.openai.com/docs/api-reference/completions?WT.mc_id=academic-105485-koreyst) ਨੂੰ ਭੇਜੇ, ਤਾਂ ਇਹ ਤੁਰੰਤ ਅਗਲੇ ਕੁਝ ਲਾਈਨਾਂ ਨਾਲ ਜਵਾਬ ਪੂਰਾ ਕਰਦਾ ਹੈ, ਜੋ ਬੁਨਿਆਦੀ ਅਨੁਮਾਨ ਲਗਾਉਣ ਵਾਲੇ ਵਿਹਾਰ ਨੂੰ ਦਰਸਾਉਂਦਾ ਹੈ।

| ਪ੍ਰਾਂਪਟ (ਇਨਪੁੱਟ)     | ਪੂਰਨਤਾ (ਆਉਟਪੁੱਟ)                                                                                                                        |
| :----------------- | :----------------------------------------------------------------------------------------------------------------------------------------- |
| Oh say can you see | ਇਹ ਲੱਗਦਾ ਹੈ ਕਿ ਤੁਸੀਂ "The Star-Spangled Banner," ਜੋ ਕਿ ਸੰਯੁਕਤ ਰਾਜ ਦਾ ਰਾਸ਼ਟਰੀ ਗੀਤ ਹੈ, ਦੇ ਬੋਲ ਸ਼ੁਰੂ ਕਰ ਰਹੇ ਹੋ। ਪੂਰੇ ਬੋਲ ਹਨ ... |

### ਜਟਿਲ ਪ੍ਰਾਂਪਟ

ਹੁਣ ਆਓ ਉਸ ਬੁਨਿਆਦੀ ਪ੍ਰਾਂਪਟ ਵਿੱਚ ਸੰਦਰਭ ਅਤੇ ਹਦਾਇਤਾਂ ਸ਼ਾਮਲ ਕਰੀਏ। [Chat Completion API](https://learn.microsoft.com/azure/ai-services/openai/how-to/chatgpt?WT.mc_id=academic-105485-koreyst) ਸਾਨੂੰ ਇੱਕ ਜਟਿਲ ਪ੍ਰਾਂਪਟ ਬਣਾਉਣ ਦੀ ਆਗਿਆ ਦਿੰਦਾ ਹੈ ਜੋ ਕਿ _ਸੰਦੇਸ਼ਾਂ_ ਦੇ ਸੰਗ੍ਰਹਿ ਵਜੋਂ ਹੁੰਦਾ ਹੈ:

- ਇਨਪੁੱਟ/ਆਉਟਪੁੱਟ ਜੋੜੇ ਜੋ _ਉਪਭੋਗਤਾ_ ਦੇ ਇਨਪੁੱਟ ਅਤੇ _ਸਹਾਇਕ_ ਦੇ ਜਵਾਬ ਨੂੰ ਦਰਸਾਉਂਦੇ ਹਨ।
- ਸਿਸਟਮ ਸੰਦੇਸ਼ ਜੋ ਸਹਾਇਕ ਦੇ ਵਿਹਾਰ ਜਾਂ ਵਿਅਕਤੀਗਤ ਲੱਛਣ ਲਈ ਸੰਦਰਭ ਸੈੱਟ ਕਰਦਾ ਹੈ।

ਹੁਣ ਬੇਨਤੀ ਹੇਠਾਂ ਦਿੱਤੇ ਰੂਪ ਵਿੱਚ ਹੈ, ਜਿੱਥੇ _ਟੋਕਨਾਈਜ਼ੇਸ਼ਨ_ ਪ੍ਰਭਾਵਸ਼ਾਲੀ ਤਰੀਕੇ ਨਾਲ ਸੰਦਰਭ ਅਤੇ ਗੱਲਬਾਤ ਤੋਂ ਸੰਬੰਧਿਤ ਜਾਣਕਾਰੀ ਕੈਪਚਰ ਕਰਦਾ ਹੈ। ਹੁਣ, ਸਿਸਟਮ ਸੰਦਰਭ ਬਦਲਣਾ ਪੂਰਨਤਾਵਾਂ ਦੀ ਗੁਣਵੱਤਾ 'ਤੇ ਉਤਨਾ ਹੀ ਪ੍ਰਭਾਵਸ਼ਾਲੀ ਹੋ ਸਕਦਾ ਹੈ, ਜਿੰਨਾ ਕਿ ਉਪਭੋਗਤਾ ਦੇ ਇਨਪੁੱਟ।

```python
response = openai.chat.completions.create(
    model="gpt-3.5-turbo",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Who won the world series in 2020?"},
        {"role": "assistant", "content": "The Los Angeles Dodgers won the World Series in 2020."},
        {"role": "user", "content": "Where was it played?"}
    ]
)
```

### ਹਦਾਇਤੀ ਪ੍ਰਾਂਪਟ

ਉਪਰ ਦਿੱਤੇ ਉਦਾਹਰਣਾਂ ਵਿੱਚ, ਉਪਭੋਗਤਾ ਦਾ ਪ੍ਰਾਂਪਟ ਇੱਕ ਸਧਾਰਣ ਟੈਕਸਟ ਸਵਾਲ ਸੀ ਜੋ ਜਾਣਕਾਰੀ ਦੀ ਮੰਗ ਵਜੋਂ ਸਮਝਿਆ ਜਾ ਸਕਦਾ ਹੈ। _ਹਦਾਇਤੀ_ ਪ੍ਰਾਂਪਟਾਂ ਨਾਲ, ਅਸੀਂ ਉਸ ਟੈਕਸਟ ਨੂੰ ਇੱਕ ਕੰਮ ਨੂੰ ਵਧੇਰੇ ਵਿਸਥਾਰ ਨਾਲ ਦਰਸਾਉਣ ਲਈ ਵਰਤ ਸਕਦੇ ਹਾਂ, AI ਨੂੰ ਬਿਹਤਰ ਮਾਰਗਦਰਸ਼ਨ ਦੇਣ ਲਈ। ਇਹ ਇੱਕ ਉਦਾਹਰਨ ਹੈ:

| ਪ੍ਰਾਂਪਟ (ਇਨਪੁੱਟ)                                                                                                                                                                                                                         | ਪੂਰਨਤਾ (ਆਉਟਪੁੱਟ)                                                                                                        | ਹਦਾਇਤ ਕਿਸਮ    |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------- | :------------------ |
| Write a description of the Civil War                                                                                                                                                                                                   | _ਇੱਕ ਸਧਾਰਣ ਪੈਰਾ ਵਾਪਸ ਕੀਤਾ_                                                                                              | ਸਧਾਰਣ              |
| Write a description of the Civil War. Provide key dates and events and describe their significance                                                                                                                                     | _ਇੱਕ ਪੈਰਾ ਅਤੇ ਮੁੱਖ ਘਟਨਾਵਾਂ ਦੀ ਸੂਚੀ ਵਾਪਸ ਕੀਤੀ_                                             | ਜਟਿਲ             |
| Write a description of the Civil War in 1 paragraph. Provide 3 bullet points with key dates and their significance. Provide 3 more bullet points with key historical figures and their contributions. Return the output as a JSON file | _ਵਧੇਰੇ ਵਿਸਥਾਰ ਨਾਲ ਟੈਕਸਟ ਬਾਕਸ ਵਿੱਚ JSON ਫਾਰਮੈਟ ਵਿੱਚ ਵਾਪਸ ਕਰਦਾ ਹੈ ਜੋ ਤੁਸੀਂ ਕਾਪੀ-ਪੇਸਟ ਕਰਕੇ ਫਾਇਲ ਵਜੋਂ ਵਰਤ ਸਕਦੇ ਹੋ ਅਤੇ ਜ਼ਰੂਰਤ ਪੈਣ 'ਤੇ ਵੈਰੀਫਾਈ ਕਰ ਸਕਦੇ ਹੋ_ | ਜਟਿਲ। ਫਾਰਮੈਟ ਕੀਤਾ ਹੋਇਆ। |

## ਮੁੱਖ ਸਮੱਗਰੀ

ਉਪਰ ਦਿੱਤੇ ਉਦਾਹਰਣਾਂ ਵਿੱਚ, ਪ੍ਰਾਂਪਟ ਅਜੇ ਵੀ ਕਾਫੀ ਖੁੱਲ੍ਹਾ ਸੀ, ਜਿਸ ਨਾਲ LLM ਨੂੰ ਇਹ ਫੈਸਲਾ ਕਰਨ ਦੀ ਆਜ਼ਾਦੀ ਮਿਲਦੀ ਸੀ ਕਿ ਉਸਦੇ ਪ੍ਰੀ-ਟ੍ਰੇਨਡ ਡੇਟਾਸੈੱਟ ਦਾ ਕਿਹੜਾ ਹਿੱਸਾ ਸੰਬੰਧਿਤ ਹੈ। _ਮੁੱਖ ਸਮੱਗਰੀ_ ਡਿਜ਼ਾਈਨ ਪੈਟਰਨ ਵਿੱਚ, ਇਨਪੁੱਟ ਟੈਕਸਟ ਨੂੰ ਦੋ ਹਿੱਸਿਆਂ ਵਿੱਚ ਵੰਡਿਆ ਜਾਂਦਾ ਹੈ:

- ਇੱਕ ਹਦਾਇਤ (ਕਿਰਿਆ)
- ਸੰਬੰਧਿਤ ਸਮੱਗਰੀ (ਜੋ ਕਿਰਿਆ ਨੂੰ ਪ੍ਰਭਾਵਿਤ ਕਰਦੀ ਹੈ)

ਇਹ ਇੱਕ ਉਦਾਹਰਨ ਹੈ ਜਿੱਥੇ ਹਦਾਇਤ ਹੈ "ਇਸ ਨੂੰ 2 ਵਾਕਾਂ ਵਿੱਚ ਸੰਖੇਪ ਕਰੋ"।

| ਪ੍ਰਾਂਪਟ (ਇਨਪੁੱਟ)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | ਪੂਰਨਤਾ (ਆਉਟਪੁੱਟ)                                                                                                                                                                                                                                                                             |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Jupiter is the fifth planet from the Sun and the largest in the Solar System. It is a gas giant with a mass one-thousandth that of the Sun, but two-and-a-half times that of all the other planets in the Solar System combined. Jupiter is one of the brightest objects visible to the naked eye in the night sky, and has been known to ancient civilizations since before recorded history. It is named after the Roman god Jupiter.[19] When viewed from Earth, Jupiter can be bright enough for its reflected light to cast visible shadows,[20] and is on average the third-brightest natural object in the night sky after the Moon and Venus. <br/> **Summarize this in 2 short sentences** | Jupiter, ਸੂਰਜ ਤੋਂ ਪੰਜਵਾਂ ਗ੍ਰਹਿ, ਸੂਰਜੀ ਪ੍ਰਣਾਲੀ ਵਿੱਚ ਸਭ ਤੋਂ ਵੱਡਾ ਹੈ ਅਤੇ ਰਾਤ ਦੇ ਅਸਮਾਨ ਵਿੱਚ ਸਭ ਤੋਂ ਚਮਕਦਾਰ ਪਦਾਰਥਾਂ ਵਿੱਚੋਂ ਇੱਕ ਹੈ। ਇਹ ਰੋਮਨ ਦੇਵਤਾ Jupiter ਦੇ ਨਾਮ 'ਤੇ ਹੈ ਅਤੇ ਇਹ ਗੈਸ ਜਾਇੰਟ ਹੈ ਜਿਸਦਾ ਭਾਰ ਸਾਰੇ ਹੋਰ ਗ੍ਰਹਾਂ ਦੇ ਭਾਰ ਦਾ ਦੋ-ਅੱਧਾ ਗੁਣਾ ਹੈ। |

ਮੁੱਖ ਸਮੱਗਰੀ ਹਿੱਸਾ ਵੱਖ-ਵੱਖ ਤਰੀਕਿਆਂ ਨਾਲ ਵਰਤਿਆ ਜਾ ਸਕਦਾ ਹੈ ਤਾਂ ਜੋ ਬਿਹਤਰ ਹਦਾਇਤਾਂ ਦਿੱਤੀਆਂ ਜਾ ਸਕਣ:

- **ਉਦਾਹਰਨਾਂ** - ਮਾਡਲ ਨੂੰ ਸਪਸ਼ਟ ਹਦਾਇਤ ਦੇਣ ਦੀ ਬਜਾਏ, ਉਸਨੂੰ ਕੁਝ ਉਦਾਹਰਨਾਂ ਦਿਓ ਅਤੇ ਮਾਡਲ ਪੈਟਰਨ ਨੂੰ ਸਮਝ ਕੇ ਕੰਮ ਕਰੇ।
- **ਸੰਕੇਤ** - ਹਦਾਇਤ ਦੇ ਨਾਲ ਇੱਕ "ਸੰਕੇਤ" ਦਿਓ ਜੋ ਪੂਰਨਤਾ ਨੂੰ ਪ੍ਰੇਰਿਤ ਕਰਦਾ ਹੈ, ਮਾਡਲ ਨੂੰ ਵਧੇਰੇ ਸੰਬੰਧਿਤ ਜਵਾਬਾਂ ਵੱਲ ਮਾਰਗਦਰਸ਼ਨ ਕਰਦਾ ਹੈ।
- **ਟੈਮਪਲੇਟ** - ਇਹ ਦੁਹਰਾਏ ਜਾ ਸਕਣ ਵਾਲੇ 'ਰੇਸਿਪੀ' ਹਨ ਜਿਨ੍ਹਾਂ ਵਿੱਚ ਪਲੇਸਹੋਲਡਰ (ਵੈਰੀਏਬਲ) ਹੁੰਦੇ ਹਨ ਜੋ ਖਾਸ ਡੇਟਾ ਨਾਲ ਕਸਟਮਾਈਜ਼ ਕੀਤੇ ਜਾ ਸਕਦੇ ਹਨ।

ਆਓ ਇਨ੍ਹਾਂ ਨੂੰ ਕਾਰਵਾਈ ਵਿੱਚ ਵੇਖੀਏ।

### ਉਦਾਹਰਨਾਂ ਦੀ ਵਰਤੋਂ

ਇਹ ਇੱਕ ਤਰੀਕਾ ਹੈ ਜਿੱਥੇ ਤੁਸੀਂ ਮੁੱਖ ਸਮੱਗਰੀ ਨੂੰ ਵਰਤ ਕੇ ਮਾਡਲ ਨੂੰ ਇੱਕ ਹਦਾਇਤ ਲਈ ਚਾਹੀਦਾ ਨਤੀਜਾ ਦੇ ਕੁਝ ਉਦਾਹਰਨ ਦਿੰਦੇ ਹੋ, ਅਤੇ ਮਾਡਲ ਪੈਟਰਨ ਨੂੰ ਸਮਝ ਕੇ ਨਤੀਜਾ ਤਿਆਰ ਕਰਦਾ ਹੈ। ਦਿੱਤੀਆਂ ਗਈਆਂ ਉਦਾਹਰਨਾਂ ਦੀ ਗਿਣਤੀ ਦੇ ਅਧਾਰ 'ਤੇ, ਸਾਨੂੰ ਜ਼ੀਰੋ-ਸ਼ਾਟ, ਵਨ-ਸ਼ਾਟ, ਫਿਊ-ਸ਼ਾਟ ਪ੍ਰਾਂਪਟਿੰਗ ਮਿਲਦੀ ਹੈ।

ਹੁਣ ਪ੍ਰਾਂਪਟ ਵਿੱਚ ਤਿੰਨ ਹਿੱਸੇ ਹੁੰਦੇ ਹਨ:

- ਇੱਕ ਕੰਮ ਦਾ ਵੇਰਵਾ
- ਚਾਹੀਦੇ ਨਤੀਜੇ ਦੀਆਂ ਕੁਝ ਉਦਾਹਰਨਾਂ
- ਇੱਕ ਨਵੀਂ ਉਦਾਹਰਨ ਦੀ ਸ਼ੁਰੂਆਤ (ਜੋ ਗੁਪਤ ਤੌਰ 'ਤੇ ਕੰਮ ਦਾ ਵੇਰਵਾ ਬਣ ਜਾਂਦੀ ਹੈ)

| ਸਿੱਖਣ ਦੀ ਕਿਸਮ | ਪ੍ਰਾਂਪਟ (ਇਨਪੁੱਟ)                                                                                                                                        | ਪੂਰਨਤਾ (ਆਉਟਪੁੱਟ)         |
| :------------ | :---------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------- |
| ਜ਼ੀਰੋ-ਸ਼ਾਟ     | "The Sun is Shining". Translate to Spanish                                                                                                            | "El Sol está brillando".    |
| ਵਨ-ਸ਼ਾਟ        | "The Sun is Shining" => ""El Sol está brillando". <br> "It's a Cold and Windy Day" =>                                                                 | "Es un día frío y ventoso". |
| ਫਿਊ-ਸ਼ਾਟ       | The player ran the bases => Baseball <br/> The player hit an ace => Tennis <br/> The player hit a six => Cricket <br/> The player made a slam-dunk => | Basketball                  |
|               |                                                                                                                                                       |                             |

ਦਿੱਖੋ ਕਿ ਜਿੱਥੇ ਜ਼ੀਰੋ-ਸ਼ਾਟ ਪ੍ਰਾਂ

IMPORTANT RULES:  
1. ਅਨੁਵਾਦ ਦੇ ਆਲੇ-ਦੁਆਲੇ '''markdown ਜਾਂ ਹੋਰ ਕਿਸੇ ਟੈਗ ਨੂੰ ਨਾ ਜੋੜੋ  
2. ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਅਨੁਵਾਦ ਬਹੁਤ ਜ਼ਿਆਦਾ ਸ਼ਾਬਦਿਕ ਨਾ ਲੱਗੇ  
3. ਟਿੱਪਣੀਆਂ ਦਾ ਵੀ ਅਨੁਵਾਦ ਕਰੋ  
4. ਇਹ ਫਾਈਲ Markdown ਫਾਰਮੈਟ ਵਿੱਚ ਹੈ - ਇਸਨੂੰ XML ਜਾਂ HTML ਵਾਂਗ ਨਾ ਸਮਝੋ  
5. ਅਨੁਵਾਦ ਨਾ ਕਰੋ:  
   - [!NOTE], [!WARNING], [!TIP], [!IMPORTANT], [!CAUTION]  
   - ਵੈਰੀਏਬਲ ਨਾਮ, ਫੰਕਸ਼ਨ ਨਾਮ, ਕਲਾਸ ਨਾਮ  
   - ਜਿਹੜੇ ਪਲੇਸਹੋਲਡਰ ਹਨ ਜਿਵੇਂ @@INLINE_CODE_x@@ ਜਾਂ @@CODE_BLOCK_x@@  
   - URLs ਜਾਂ ਪਾਥ  
6. ਸਾਰੇ ਮੂਲ Markdown ਫਾਰਮੈਟਿੰਗ ਨੂੰ ਬਰਕਰਾਰ ਰੱਖੋ  
7. ਸਿਰਫ ਅਨੁਵਾਦਿਤ ਸਮੱਗਰੀ ਵਾਪਸ ਦਿਓ, ਕੋਈ ਵਾਧੂ ਟੈਗ ਜਾਂ ਮਾਰਕਅੱਪ ਨਾ ਜੋੜੋ  
ਕਿਰਪਾ ਕਰਕੇ ਆਉਟਪੁੱਟ ਖੱਬੇ ਤੋਂ ਸੱਜੇ ਲਿਖੋ।  

**Summarize This**                                       | ਜੂਪੀਟਰ ਸਾਡੇ ਸੂਰਜ ਮੰਡਲ ਦਾ ਸਭ ਤੋਂ ਵੱਡਾ ਗ੍ਰਹਿ ਹੈ ਅਤੇ ਸੂਰਜ ਤੋਂ ਪੰਜਵਾਂ ਗ੍ਰਹਿ ਹੈ। ਇਹ ਇੱਕ ਗੈਸ ਜਾਇੰਟ ਹੈ ਜਿਸਦਾ ਭਾਰ ਸੂਰਜ ਦੇ ਭਾਰ ਦਾ 1/1000ਵਾਂ ਹੈ, ਪਰ ਇਹ ਸਾਰੇ ਹੋਰ ਗ੍ਰਹਿਆਂ ਦੇ ਭਾਰ ਤੋਂ ਵੱਧ ਭਾਰੀ ਹੈ। ਪ੍ਰਾਚੀਨ ਸਭਿਆਚਾਰਾਂ ਨੂੰ ਜੂਪੀਟਰ ਬਾਰੇ ਕਾਫੀ ਸਮੇਂ ਤੋਂ ਪਤਾ ਹੈ, ਅਤੇ ਇਹ ਰਾਤ ਦੇ ਆਸਮਾਨ ਵਿੱਚ ਆਸਾਨੀ ਨਾਲ ਦਿੱਖਾਈ ਦਿੰਦਾ ਹੈ। |  
| 1              | ਜੂਪੀਟਰ ਸੂਰਜ ਤੋਂ ਪੰਜਵਾਂ ਗ੍ਰਹਿ ਹੈ ਅਤੇ ਸੂਰਜ ਮੰਡਲ ਵਿੱਚ ਸਭ ਤੋਂ ਵੱਡਾ ਗ੍ਰਹਿ ਹੈ। ਇਹ ਇੱਕ ਗੈਸ ਜਾਇੰਟ ਹੈ ਜਿਸਦਾ ਭਾਰ ਸੂਰਜ ਦੇ ਭਾਰ ਦਾ ਇੱਕ ਹਜ਼ਾਰਵਾਂ ਹਿੱਸਾ ਹੈ, ਪਰ ਸਾਰੇ ਹੋਰ ਗ੍ਰਹਿਆਂ ਦੇ ਭਾਰ ਦੇ ਮੁਕਾਬਲੇ ਦੋ ਤੇ ਅੱਧ ਗੁਣਾ ਵੱਧ ਹੈ। ਜੂਪੀਟਰ ਰਾਤ ਦੇ ਆਸਮਾਨ ਵਿੱਚ ਨੰਗੀ ਅੱਖ ਨਾਲ ਸਭ ਤੋਂ ਚਮਕਦਾਰ ਪਦਾਰਥਾਂ ਵਿੱਚੋਂ ਇੱਕ ਹੈ, ਅਤੇ ਇਹ ਪ੍ਰਾਚੀਨ ਸਭਿਆਚਾਰਾਂ ਨੂੰ ਇਤਿਹਾਸ ਤੋਂ ਪਹਿਲਾਂ ਤੋਂ ਜਾਣਿਆ ਗਿਆ ਹੈ। <br/>**Summarize This** <br/> ਸਾਨੂੰ ਇਹ ਸਿੱਖਣ ਨੂੰ ਮਿਲਿਆ ਕਿ ਜੂਪੀਟਰ | ਸੂਰਜ ਤੋਂ ਪੰਜਵਾਂ ਗ੍ਰਹਿ ਹੈ ਅਤੇ ਸੂਰਜ ਮੰਡਲ ਵਿੱਚ ਸਭ ਤੋਂ ਵੱਡਾ ਗ੍ਰਹਿ ਹੈ। ਇਹ ਇੱਕ ਗੈਸ ਜਾਇੰਟ ਹੈ ਜਿਸਦਾ ਭਾਰ ਸੂਰਜ ਦੇ ਭਾਰ ਦਾ ਇੱਕ ਹਜ਼ਾਰਵਾਂ ਹਿੱਸਾ ਹੈ, ਪਰ ਸਾਰੇ ਹੋਰ ਗ੍ਰਹਿਆਂ ਦੇ ਭਾਰ ਦੇ ਮੁਕਾਬਲੇ ਦੋ ਤੇ ਅੱਧ ਗੁਣਾ ਵੱਧ ਹੈ। ਇਹ ਨੰਗੀ ਅੱਖ ਨਾਲ ਆਸਾਨੀ ਨਾਲ ਦਿੱਖਾਈ ਦਿੰਦਾ ਹੈ ਅਤੇ ਪ੍ਰਾਚੀਨ ਸਮਿਆਂ ਤੋਂ ਜਾਣਿਆ ਜਾਂਦਾ ਹੈ।                        |  
| 2              | ਜੂਪੀਟਰ ਸੂਰਜ ਤੋਂ ਪੰਜਵਾਂ ਗ੍ਰਹਿ ਹੈ ਅਤੇ ਸੂਰਜ ਮੰਡਲ ਵਿੱਚ ਸਭ ਤੋਂ ਵੱਡਾ ਗ੍ਰਹਿ ਹੈ। ਇਹ ਇੱਕ ਗੈਸ ਜਾਇੰਟ ਹੈ ਜਿਸਦਾ ਭਾਰ ਸੂਰਜ ਦੇ ਭਾਰ ਦਾ ਇੱਕ ਹਜ਼ਾਰਵਾਂ ਹਿੱਸਾ ਹੈ, ਪਰ ਸਾਰੇ ਹੋਰ ਗ੍ਰਹਿਆਂ ਦੇ ਭਾਰ ਦੇ ਮੁਕਾਬਲੇ ਦੋ ਤੇ ਅੱਧ ਗੁਣਾ ਵੱਧ ਹੈ। ਜੂਪੀਟਰ ਰਾਤ ਦੇ ਆਸਮਾਨ ਵਿੱਚ ਨੰਗੀ ਅੱਖ ਨਾਲ ਸਭ ਤੋਂ ਚਮਕਦਾਰ ਪਦਾਰਥਾਂ ਵਿੱਚੋਂ ਇੱਕ ਹੈ, ਅਤੇ ਇਹ ਪ੍ਰਾਚੀਨ ਸਭਿਆਚਾਰਾਂ ਨੂੰ ਇਤਿਹਾਸ ਤੋਂ ਪਹਿਲਾਂ ਤੋਂ ਜਾਣਿਆ ਗਿਆ ਹੈ। <br/>**Summarize This** <br/> ਸਿਖੇ ਗਏ ਸਿਖਲਾਈ ਦੇ ਸਿਖਰਲੇ 3 ਤੱਥ:         | 1. ਜੂਪੀਟਰ ਸੂਰਜ ਤੋਂ ਪੰਜਵਾਂ ਗ੍ਰਹਿ ਹੈ ਅਤੇ ਸੂਰਜ ਮੰਡਲ ਵਿੱਚ ਸਭ ਤੋਂ ਵੱਡਾ ਗ੍ਰਹਿ ਹੈ। <br/> 2. ਇਹ ਇੱਕ ਗੈਸ ਜਾਇੰਟ ਹੈ ਜਿਸਦਾ ਭਾਰ ਸੂਰਜ ਦੇ ਭਾਰ ਦਾ ਇੱਕ ਹਜ਼ਾਰਵਾਂ ਹਿੱਸਾ ਹੈ...<br/> 3. ਜੂਪੀਟਰ ਪ੍ਰਾਚੀਨ ਸਮਿਆਂ ਤੋਂ ਨੰਗੀ ਅੱਖ ਨਾਲ ਦਿੱਖਾਈ ਦਿੰਦਾ ਹੈ ...                                                                       |  
|                |                                                                                                                                                                                                                                                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                           |  

### Prompt Templates

ਇੱਕ ਪ੍ਰਾਂਪਟ ਟੈਮਪਲੇਟ ਇੱਕ _ਪਹਿਲਾਂ ਤੋਂ ਤਿਆਰ ਕੀਤਾ ਹੋਇਆ ਨੁਸਖਾ_ ਹੁੰਦਾ ਹੈ ਜੋ ਜਰੂਰਤ ਪੈਣ ‘ਤੇ ਸਟੋਰ ਅਤੇ ਦੁਬਾਰਾ ਵਰਤਿਆ ਜਾ ਸਕਦਾ ਹੈ, ਤਾਂ ਜੋ ਵੱਡੇ ਪੱਧਰ ‘ਤੇ ਯੂਜ਼ਰ ਅਨੁਭਵਾਂ ਨੂੰ ਜ਼ਿਆਦਾ ਸਥਿਰ ਬਣਾਇਆ ਜਾ ਸਕੇ। ਇਸਦਾ ਸਭ ਤੋਂ ਸਧਾਰਣ ਰੂਪ ਇੱਕ ਪ੍ਰਾਂਪਟ ਉਦਾਹਰਣਾਂ ਦਾ ਸੰਗ੍ਰਹਿ ਹੁੰਦਾ ਹੈ ਜਿਵੇਂ [OpenAI ਵੱਲੋਂ ਇਹ ਉਦਾਹਰਣ](https://platform.openai.com/examples?WT.mc_id=academic-105485-koreyst) ਜੋ ਇੰਟਰਐਕਟਿਵ ਪ੍ਰਾਂਪਟ ਹਿੱਸਿਆਂ (ਯੂਜ਼ਰ ਅਤੇ ਸਿਸਟਮ ਸੁਨੇਹੇ) ਅਤੇ API-ਚਲਿਤ ਬੇਨਤੀ ਫਾਰਮੈਟ ਦੋਹਾਂ ਨੂੰ ਮੁਹੱਈਆ ਕਰਵਾਉਂਦਾ ਹੈ - ਦੁਬਾਰਾ ਵਰਤੋਂ ਲਈ ਸਹਾਇਕ।  

ਇਸਦੇ ਵਧੇਰੇ ਜਟਿਲ ਰੂਪ ਵਿੱਚ ਜਿਵੇਂ [LangChain ਵੱਲੋਂ ਇਹ ਉਦਾਹਰਣ](https://python.langchain.com/docs/concepts/prompt_templates/?WT.mc_id=academic-105485-koreyst) ਵਿੱਚ _ਪਲੇਸਹੋਲਡਰ_ ਹੁੰਦੇ ਹਨ ਜੋ ਵੱਖ-ਵੱਖ ਸਰੋਤਾਂ (ਯੂਜ਼ਰ ਇਨਪੁੱਟ, ਸਿਸਟਮ ਸੰਦਰਭ, ਬਾਹਰੀ ਡੇਟਾ ਆਦਿ) ਤੋਂ ਡੇਟਾ ਨਾਲ ਬਦਲੇ ਜਾ ਸਕਦੇ ਹਨ, ਤਾਂ ਜੋ ਪ੍ਰਾਂਪਟ ਨੂੰ ਗਤੀਸ਼ੀਲ ਤਰੀਕੇ ਨਾਲ ਤਿਆਰ ਕੀਤਾ ਜਾ ਸਕੇ। ਇਸ ਨਾਲ ਅਸੀਂ ਦੁਬਾਰਾ ਵਰਤਣ ਯੋਗ ਪ੍ਰਾਂਪਟਾਂ ਦੀ ਲਾਇਬ੍ਰੇਰੀ ਬਣਾਉਂਦੇ ਹਾਂ ਜੋ **ਕਾਰਜਕਾਰੀ ਤੌਰ ‘ਤੇ** ਵੱਡੇ ਪੱਧਰ ‘ਤੇ ਸਥਿਰ ਯੂਜ਼ਰ ਅਨੁਭਵ ਚਲਾਉਣ ਲਈ ਵਰਤੀ ਜਾ ਸਕਦੀ ਹੈ।  

ਅੰਤ ਵਿੱਚ, ਟੈਮਪਲੇਟਾਂ ਦੀ ਅਸਲ ਕੀਮਤ ਇਸ ਗੱਲ ਵਿੱਚ ਹੈ ਕਿ ਅਸੀਂ ਵਰਟੀਕਲ ਐਪਲੀਕੇਸ਼ਨ ਖੇਤਰਾਂ ਲਈ _ਪ੍ਰਾਂਪਟ ਲਾਇਬ੍ਰੇਰੀਆਂ_ ਤਿਆਰ ਅਤੇ ਪ੍ਰਕਾਸ਼ਿਤ ਕਰ ਸਕੀਏ - ਜਿੱਥੇ ਪ੍ਰਾਂਪਟ ਟੈਮਪਲੇਟ ਹੁਣ ਐਪਲੀਕੇਸ਼ਨ-ਵਿਸ਼ੇਸ਼ ਸੰਦਰਭ ਜਾਂ ਉਦਾਹਰਣਾਂ ਨੂੰ ਧਿਆਨ ਵਿੱਚ ਰੱਖ ਕੇ _ਸੰਵਾਰਿਆ_ ਜਾਂਦਾ ਹੈ, ਜੋ ਟਾਰਗੇਟ ਕੀਤੇ ਯੂਜ਼ਰ ਦਰਸ਼ਕਾਂ ਲਈ ਜ਼ਿਆਦਾ ਸਬੰਧਤ ਅਤੇ ਸਹੀ ਜਵਾਬ ਦਿੰਦਾ ਹੈ। [Prompts For Edu](https://github.com/microsoft/prompts-for-edu?WT.mc_id=academic-105485-koreyst) ਰਿਪੋਜ਼ਿਟਰੀ ਇਸ ਤਰੀਕੇ ਦੀ ਇੱਕ ਵਧੀਆ ਮਿਸਾਲ ਹੈ, ਜੋ ਸਿੱਖਿਆ ਖੇਤਰ ਲਈ ਪ੍ਰਾਂਪਟਾਂ ਦੀ ਲਾਇਬ੍ਰੇਰੀ ਬਣਾਉਂਦੀ ਹੈ ਜਿਸ ਵਿੱਚ ਪਾਠ ਯੋਜਨਾ, ਕਰਿਕੁਲਮ ਡਿਜ਼ਾਈਨ, ਵਿਦਿਆਰਥੀ ਟਿਊਟੋਰਿੰਗ ਵਰਗੇ ਮੁੱਖ ਉਦੇਸ਼ਾਂ ‘ਤੇ ਜ਼ੋਰ ਦਿੱਤਾ ਗਿਆ ਹੈ।  

## Supporting Content

ਜੇ ਅਸੀਂ ਪ੍ਰਾਂਪਟ ਬਣਾਉਣ ਨੂੰ ਇੱਕ ਨਿਰਦੇਸ਼ (ਟਾਸਕ) ਅਤੇ ਇੱਕ ਟਾਰਗੇਟ (ਮੁੱਖ ਸਮੱਗਰੀ) ਵਜੋਂ ਵੇਖੀਏ, ਤਾਂ _ਦੁਸਰੀ ਸਮੱਗਰੀ_ ਉਹ ਵਾਧੂ ਸੰਦਰਭ ਹੁੰਦੀ ਹੈ ਜੋ ਅਸੀਂ **ਕਿਸੇ ਤਰੀਕੇ ਨਾਲ ਨਤੀਜੇ ਨੂੰ ਪ੍ਰਭਾਵਿਤ ਕਰਨ ਲਈ** ਦਿੰਦੇ ਹਾਂ। ਇਹ ਟਿਊਨਿੰਗ ਪੈਰਾਮੀਟਰ, ਫਾਰਮੈਟਿੰਗ ਨਿਰਦੇਸ਼, ਵਿਸ਼ਾ ਵਰਗੀਕਰਨ ਆਦਿ ਹੋ ਸਕਦੇ ਹਨ ਜੋ ਮਾਡਲ ਨੂੰ ਆਪਣਾ ਜਵਾਬ ਯੂਜ਼ਰ ਦੀਆਂ ਉਮੀਦਾਂ ਜਾਂ ਲਕੜਾਂ ਦੇ ਅਨੁਕੂਲ ਬਣਾਉਣ ਵਿੱਚ ਮਦਦ ਕਰਦੇ ਹਨ।  

ਉਦਾਹਰਣ ਵਜੋਂ: ਜੇ ਸਾਡੇ ਕੋਲ ਕੋਰਸ ਕੈਟਾਲੌਗ ਹੈ ਜਿਸ ਵਿੱਚ ਵਿਸਤ੍ਰਿਤ ਮੈਟਾਡੇਟਾ (ਨਾਂ, ਵਰਣਨ, ਪੱਧਰ, ਮੈਟਾਡੇਟਾ ਟੈਗ, ਇੰਸਟਰਕਟਰ ਆਦਿ) ਹੈ:  

- ਅਸੀਂ ਨਿਰਦੇਸ਼ ਦੇ ਸਕਦੇ ਹਾਂ "Fall 2023 ਲਈ ਕੋਰਸ ਕੈਟਾਲੌਗ ਦਾ ਸਾਰ ਸੰਖੇਪ ਕਰੋ"  
- ਅਸੀਂ ਮੁੱਖ ਸਮੱਗਰੀ ਵਜੋਂ ਕੁਝ ਉਦਾਹਰਣਾਂ ਦੇ ਸਕਦੇ ਹਾਂ ਜੋ ਚਾਹੀਦੇ ਨਤੀਜੇ ਨੂੰ ਦਰਸਾਉਂਦੀਆਂ ਹਨ  
- ਅਸੀਂ ਦੁਸਰੀ ਸਮੱਗਰੀ ਵਜੋਂ ਸਭ ਤੋਂ ਵੱਧ ਦਿਲਚਸਪੀ ਵਾਲੇ 5 "ਟੈਗ" ਪਛਾਣ ਸਕਦੇ ਹਾਂ।  

ਹੁਣ, ਮਾਡਲ ਕੁਝ ਉਦਾਹਰਣਾਂ ਵੱਲੋਂ ਦਿੱਤੇ ਫਾਰਮੈਟ ਵਿੱਚ ਸਾਰ ਦੇ ਸਕਦਾ ਹੈ - ਪਰ ਜੇ ਨਤੀਜੇ ਵਿੱਚ ਕਈ ਟੈਗ ਹਨ, ਤਾਂ ਇਹ ਦੁਸਰੀ ਸਮੱਗਰੀ ਵਿੱਚ ਪਛਾਣੇ 5 ਟੈਗਾਂ ਨੂੰ ਤਰਜੀਹ ਦੇ ਸਕਦਾ ਹੈ।  

---

<!--  
LESSON TEMPLATE:  
ਇਹ ਯੂਨਿਟ ਮੁੱਖ ਧਾਰਾ ਸੰਕਲਪ #1 ਨੂੰ ਕਵਰ ਕਰੇ।  
ਸੰਕਲਪ ਨੂੰ ਉਦਾਹਰਣਾਂ ਅਤੇ ਹਵਾਲਿਆਂ ਨਾਲ ਮਜ਼ਬੂਤ ਕਰੋ।  

CONCEPT #3:  
Prompt Engineering Techniques.  
Prompt engineering ਲਈ ਕੁਝ ਬੁਨਿਆਦੀ ਤਕਨੀਕਾਂ ਕੀ ਹਨ?  
ਇਸਨੂੰ ਕੁਝ ਅਭਿਆਸਾਂ ਨਾਲ ਦਰਸਾਓ।  
-->  

## Prompting Best Practices

ਹੁਣ ਜਦੋਂ ਅਸੀਂ ਜਾਣਦੇ ਹਾਂ ਕਿ ਪ੍ਰਾਂਪਟ ਕਿਵੇਂ _ਤਿਆਰ_ ਕੀਤੇ ਜਾਂਦੇ ਹਨ, ਅਸੀਂ ਸੋਚ ਸਕਦੇ ਹਾਂ ਕਿ ਉਨ੍ਹਾਂ ਨੂੰ _ਡਿਜ਼ਾਈਨ_ ਕਿਵੇਂ ਕਰਨਾ ਹੈ ਤਾਂ ਜੋ ਵਧੀਆ ਅਭਿਆਸਾਂ ਨੂੰ ਦਰਸਾਇਆ ਜਾ ਸਕੇ। ਅਸੀਂ ਇਸਨੂੰ ਦੋ ਹਿੱਸਿਆਂ ਵਿੱਚ ਸੋਚ ਸਕਦੇ ਹਾਂ - ਸਹੀ _ਮਨੋਭਾਵ_ ਰੱਖਣਾ ਅਤੇ ਸਹੀ _ਤਕਨੀਕਾਂ_ ਲਾਗੂ ਕਰਨਾ।  

### Prompt Engineering Mindset

Prompt Engineering ਇੱਕ ਟ੍ਰਾਇਲ-ਐਂਡ-ਐਰਰ ਪ੍ਰਕਿਰਿਆ ਹੈ, ਇਸ ਲਈ ਤਿੰਨ ਵੱਡੇ ਮਾਰਗਦਰਸ਼ਕ ਤੱਤਾਂ ਨੂੰ ਧਿਆਨ ਵਿੱਚ ਰੱਖੋ:  

1. **ਡੋਮੇਨ ਦੀ ਸਮਝ ਜਰੂਰੀ ਹੈ।** ਜਵਾਬ ਦੀ ਸਹੀਤਾ ਅਤੇ ਸਬੰਧਤਾ ਉਸ _ਡੋਮੇਨ_ ‘ਤੇ ਨਿਰਭਰ ਕਰਦੀ ਹੈ ਜਿਸ ਵਿੱਚ ਐਪਲੀਕੇਸ਼ਨ ਜਾਂ ਯੂਜ਼ਰ ਕੰਮ ਕਰਦਾ ਹੈ। ਆਪਣੀ ਅੰਦਰੂਨੀ ਸਮਝ ਅਤੇ ਡੋਮੇਨ ਵਿਸ਼ੇਸ਼ ਗਿਆਨ ਨਾਲ **ਤਕਨੀਕਾਂ ਨੂੰ ਹੋਰ ਕਸਟਮਾਈਜ਼ ਕਰੋ**। ਉਦਾਹਰਣ ਵਜੋਂ, ਆਪਣੇ ਸਿਸਟਮ ਪ੍ਰਾਂਪਟਾਂ ਵਿੱਚ _ਡੋਮੇਨ-ਵਿਸ਼ੇਸ਼ ਪर्सਨਾਲਿਟੀਜ਼_ ਬਣਾਓ, ਜਾਂ ਯੂਜ਼ਰ ਪ੍ਰਾਂਪਟਾਂ ਵਿੱਚ _ਡੋਮੇਨ-ਵਿਸ਼ੇਸ਼ ਟੈਮਪਲੇਟ_ ਵਰਤੋ। ਦੁਸਰੀ ਸਮੱਗਰੀ ਵਿੱਚ ਡੋਮੇਨ-ਵਿਸ਼ੇਸ਼ ਸੰਦਰਭ ਦਿਓ, ਜਾਂ ਮਾਡਲ ਨੂੰ ਜਾਣੂ ਵਰਤੋਂ ਦੇ ਨਮੂਨੇ ਵੱਲ ਮਾਰਗਦਰਸ਼ਨ ਕਰਨ ਲਈ _ਡੋਮੇਨ-ਵਿਸ਼ੇਸ਼ ਸੰਕੇਤ ਅਤੇ ਉਦਾਹਰਣ_ ਵਰਤੋ।  

2. **ਮਾਡਲ ਦੀ ਸਮਝ ਜਰੂਰੀ ਹੈ।** ਅਸੀਂ ਜਾਣਦੇ ਹਾਂ ਕਿ ਮਾਡਲ ਕੁਦਰਤੀ ਤੌਰ ‘ਤੇ ਅਣਿਸ਼ਚਿਤ ਹੁੰਦੇ ਹਨ। ਪਰ ਮਾਡਲ ਦੀਆਂ ਇੰਪਲੀਮੈਂਟੇਸ਼ਨਾਂ ਵਿੱਚ ਵੀ ਫਰਕ ਹੋ ਸਕਦਾ ਹੈ, ਜਿਵੇਂ ਕਿ ਉਹ ਕਿਹੜਾ ਟ੍ਰੇਨਿੰਗ ਡੇਟਾਸੈੱਟ ਵਰਤਦੇ ਹਨ (ਪਹਿਲਾਂ ਤੋਂ ਸਿੱਖਿਆ ਗਿਆ ਗਿਆਨ), ਉਹ ਕਿਹੜੀਆਂ ਸਮਰੱਥਾਵਾਂ ਦਿੰਦੇ ਹਨ (ਜਿਵੇਂ API ਜਾਂ SDK ਰਾਹੀਂ) ਅਤੇ ਉਹ ਕਿਸ ਕਿਸਮ ਦੀ ਸਮੱਗਰੀ ਲਈ ਅਨੁਕੂਲਿਤ ਹਨ (ਕੋਡ, ਚਿੱਤਰ, ਲਿਖਤ ਆਦਿ)। ਤੁਸੀਂ ਜੋ ਮਾਡਲ ਵਰਤ ਰਹੇ ਹੋ ਉਸ ਦੀਆਂ ਤਾਕਤਾਂ ਅਤੇ ਸੀਮਾਵਾਂ ਨੂੰ ਸਮਝੋ, ਅਤੇ ਇਸ ਗਿਆਨ ਨਾਲ _ਟਾਸਕਾਂ ਨੂੰ ਤਰਜੀਹ ਦਿਓ_ ਜਾਂ _ਕਸਟਮ ਟੈਮਪਲੇਟ_ ਬਣਾਓ ਜੋ ਮਾਡਲ ਦੀਆਂ ਸਮਰੱਥਾਵਾਂ ਲਈ ਅਨੁਕੂਲਿਤ ਹੋਣ।  

3. **ਦੋਹਰਾਈ ਅਤੇ ਪ੍ਰਮਾਣਿਕਤਾ ਜਰੂਰੀ ਹੈ।** ਮਾਡਲ ਤੇਜ਼ੀ ਨਾਲ ਵਿਕਸਤ ਹੋ ਰਹੇ ਹਨ, ਅਤੇ ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਲਈ ਤਕਨੀਕਾਂ ਵੀ। ਇੱਕ ਡੋਮੇਨ ਵਿਸ਼ੇਸ਼ਜ્ઞ ਹੋਣ ਦੇ ਨਾਤੇ, ਤੁਹਾਡੇ ਕੋਲ ਹੋਰ ਸੰਦਰਭ ਜਾਂ ਮਾਪਦੰਡ ਹੋ ਸਕਦੇ ਹਨ ਜੋ ਵਿਆਪਕ ਸਮੁਦਾਇ ਲਈ ਲਾਗੂ ਨਹੀਂ ਹੁੰਦੇ। ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਟੂਲਜ਼ ਅਤੇ ਤਕਨੀਕਾਂ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਪ੍ਰਾਂਪਟ ਬਣਾਉਣ ਦੀ ਸ਼ੁਰੂਆਤ ਕਰੋ, ਫਿਰ ਆਪਣੇ ਅੰਦਰੂਨੀ ਗਿਆਨ ਅਤੇ ਡੋਮੇਨ ਵਿਸ਼ੇਸ਼ਤਾ ਨਾਲ ਨਤੀਜਿਆਂ ਨੂੰ ਦੋਹਰਾਓ ਅਤੇ ਪ੍ਰਮਾਣਿਤ ਕਰੋ। ਆਪਣੇ ਅਨੁਭਵਾਂ ਨੂੰ ਦਰਜ ਕਰੋ ਅਤੇ ਇੱਕ **ਗਿਆਨ ਅਧਾਰ** (ਜਿਵੇਂ ਪ੍ਰਾਂਪਟ ਲਾਇਬ੍ਰੇਰੀਆਂ) ਬਣਾਓ ਜੋ ਹੋਰਾਂ ਵੱਲੋਂ ਤੇਜ਼ ਦੁਹਰਾਈ ਲਈ ਨਵਾਂ ਮਿਆਰ ਬਣ ਸਕੇ।  

## Best Practices

ਹੁਣ ਆਓ ਕੁਝ ਆਮ ਵਧੀਆ ਅਭਿਆਸਾਂ ਨੂੰ ਵੇਖੀਏ ਜੋ [OpenAI](https://help.openai.com/en/articles/6654000-best-practices-for-prompt-engineering-with-openai-api?WT.mc_id=academic-105485-koreyst) ਅਤੇ [Azure OpenAI](https://learn.microsoft.com/azure/ai-services/openai/concepts/prompt-engineering#best-practices?WT.mc_id=academic-105485-koreyst) ਦੇ ਪ੍ਰੈਕਟੀਸ਼ਨਰਾਂ ਵੱਲੋਂ ਸਿਫਾਰਸ਼ ਕੀਤੇ ਜਾਂਦੇ ਹਨ।  

| ਕੀ ਹੈ                              | ਕਿਉਂ                                                                                                                                                                                                                                               |  
| :-------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |  
| ਨਵੇਂ ਮਾਡਲਾਂ ਦਾ ਮੁਲਾਂਕਣ ਕਰੋ       | ਨਵੇਂ ਮਾਡਲ ਜਨਰੇਸ਼ਨ ਵਿੱਚ ਬਿਹਤਰ ਫੀਚਰ ਅਤੇ ਗੁਣਵੱਤਾ ਹੋ ਸਕਦੀ ਹੈ - ਪਰ ਇਹ ਮਹਿੰਗੇ ਵੀ ਹੋ ਸਕਦੇ ਹਨ। ਪ੍ਰਭਾਵ ਦਾ ਮੁਲਾਂਕਣ ਕਰੋ, ਫਿਰ ਮਾਈਗ੍ਰੇਸ਼ਨ ਫੈਸਲੇ ਕਰੋ।                                                                                                         |  
| ਨਿਰਦੇਸ਼ ਅਤੇ ਸੰਦਰਭ ਨੂੰ ਵੱਖਰਾ ਕਰੋ | ਦੇਖੋ ਕਿ ਤੁਹਾਡਾ ਮਾਡਲ/ਪ੍ਰਦਾਤਾ ਨਿਰਦੇਸ਼, ਮੁੱਖ ਅਤੇ ਦੁਸਰੀ ਸਮੱਗਰੀ ਨੂੰ ਵੱਖ ਕਰਨ ਲਈ _ਡੈਲੀਮੀਟਰ_ ਦਿੰਦਾ ਹੈ ਕਿ ਨਹੀਂ। ਇਹ ਮਾਡਲਾਂ ਨੂੰ ਟੋਕਨਜ਼ ਨੂੰ ਜ਼ਿਆਦਾ ਸਹੀ ਤੌਰ ‘ਤੇ ਵਜ਼ਨ ਦੇਣ ਵਿੱਚ ਮਦਦ ਕਰ ਸਕਦਾ ਹੈ।                                                                     |  
| ਵਿਸ਼ੇਸ਼ ਅਤੇ ਸਪਸ਼ਟ ਰਹੋ             | ਚਾਹੀਦੇ ਸੰਦਰਭ, ਨਤੀਜਾ, ਲੰਬਾਈ, ਫਾਰਮੈਟ, ਅੰਦਾਜ਼ ਆਦਿ ਬਾਰੇ ਹੋਰ ਵੇਰਵੇ ਦਿਓ। ਇਸ ਨਾਲ ਜਵਾਬਾਂ ਦੀ ਗੁਣਵੱਤਾ ਅਤੇ ਸਥਿਰਤਾ ਦੋਹਾਂ ਵਿੱਚ ਸੁਧਾਰ ਆਵੇਗਾ। ਰੀਯੂਜ਼ੇਬਲ ਟੈਮਪਲੇਟਾਂ ਵਿੱਚ ਨੁਸਖੇ ਕੈਪਚਰ ਕਰੋ।                                                                    |  
| ਵਰਣਨਾਤਮਕ ਰਹੋ, ਉਦਾਹਰਣ ਵਰਤੋ      | ਮਾਡਲਾਂ ਨੂੰ "ਦਿਖਾਓ ਅਤੇ ਦੱਸੋ" ਤਰੀਕੇ ਨਾਲ ਜਵਾਬ ਦੇਣਾ ਵਧੀਆ ਲੱਗਦਾ ਹੈ। ਪਹਿਲਾਂ `zero-shot` ਤਰੀਕੇ ਨਾਲ ਨਿਰਦੇਸ਼ ਦਿਓ (ਬਿਨਾਂ ਉਦਾਹਰਣਾਂ ਦੇ), ਫਿਰ `few-shot` ਨਾਲ ਸੁਧਾਰ ਕਰੋ, ਜਿੱਥੇ ਕੁਝ ਉਦਾਹਰਣ ਦਿੱਤੇ ਜਾਂਦੇ ਹਨ। ਤੁਲਨਾਵਾਂ ਵਰਤੋ।                                               |  
| ਜਵਾਬ ਸ਼ੁਰੂ ਕਰਨ ਲਈ ਸੰਕੇਤ ਦਿਓ      | ਮਾਡਲ ਨੂੰ ਚਾਹੀਦਾ ਨਤੀਜਾ ਵੱਲ ਧੱਕਣ ਲਈ ਕੁਝ ਸ਼ੁਰੂਆਤੀ ਸ਼ਬਦ ਜਾਂ ਵਾਕਾਂਸ਼ ਦਿਓ ਜੋ ਇਹ ਜਵਾਬ ਦੀ ਸ਼ੁਰੂਆਤ ਵਜੋਂ ਵਰਤ ਸਕੇ।                                                                                                                                        |  
| ਦੁਹਰਾਈ ਕਰੋ                       | ਕਈ ਵਾਰੀ ਮਾਡਲ ਨੂੰ ਸਮਝਾਉਣ ਲਈ ਆਪਣੀ ਗੱਲ ਦੁਹਰਾਉਣੀ ਪੈਂਦੀ ਹੈ। ਮੁੱਖ ਸਮੱਗਰੀ ਤੋਂ ਪਹਿਲਾਂ ਅਤੇ ਬਾਅਦ ਨਿਰਦੇਸ਼ ਦਿਓ, ਨਿਰਦੇਸ਼ ਅਤੇ ਸੰਕੇਤ ਦੋਹਾਂ ਵਰ

**ਅਸਵੀਕਾਰੋਪਣ**:  
ਇਹ ਦਸਤਾਵੇਜ਼ AI ਅਨੁਵਾਦ ਸੇਵਾ [Co-op Translator](https://github.com/Azure/co-op-translator) ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਅਨੁਵਾਦਿਤ ਕੀਤਾ ਗਿਆ ਹੈ। ਜਦੋਂ ਕਿ ਅਸੀਂ ਸਹੀਅਤ ਲਈ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ, ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਰੱਖੋ ਕਿ ਸਵੈਚਾਲਿਤ ਅਨੁਵਾਦਾਂ ਵਿੱਚ ਗਲਤੀਆਂ ਜਾਂ ਅਸਮਰਥਤਾਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ। ਮੂਲ ਦਸਤਾਵੇਜ਼ ਆਪਣੀ ਮੂਲ ਭਾਸ਼ਾ ਵਿੱਚ ਪ੍ਰਮਾਣਿਕ ਸਰੋਤ ਮੰਨਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ। ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਲਈ, ਪੇਸ਼ੇਵਰ ਮਨੁੱਖੀ ਅਨੁਵਾਦ ਦੀ ਸਿਫਾਰਸ਼ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਇਸ ਅਨੁਵਾਦ ਦੀ ਵਰਤੋਂ ਤੋਂ ਉਤਪੰਨ ਕਿਸੇ ਵੀ ਗਲਤਫਹਿਮੀ ਜਾਂ ਗਲਤ ਵਿਆਖਿਆ ਲਈ ਅਸੀਂ ਜ਼ਿੰਮੇਵਾਰ ਨਹੀਂ ਹਾਂ।