<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "ea4bbe640847aafbbba14dae4625e9af",
  "translation_date": "2025-07-09T12:24:46+00:00",
  "source_file": "07-building-chat-applications/README.md",
  "language_code": "pa"
}
-->
# ਜਨਰੇਟਿਵ AI-ਸਮਰੱਥ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ ਦਾ ਨਿਰਮਾਣ

[![ਜਨਰੇਟਿਵ AI-ਸਮਰੱਥ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ ਦਾ ਨਿਰਮਾਣ](../../../translated_images/07-lesson-banner.a279b937f2843833fe28b4597f51bdef92d0ad03efee7ba52d0f166dea7574e5.pa.png)](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst)

> _(ਇਸ ਪਾਠ ਦਾ ਵੀਡੀਓ ਦੇਖਣ ਲਈ ਉਪਰ ਦਿੱਤੀ ਤਸਵੀਰ 'ਤੇ ਕਲਿੱਕ ਕਰੋ)_

ਹੁਣ ਜਦੋਂ ਅਸੀਂ ਵੇਖ ਲਿਆ ਕਿ ਅਸੀਂ ਟੈਕਸਟ-ਜਨਰੇਸ਼ਨ ਐਪਸ ਕਿਵੇਂ ਬਣਾ ਸਕਦੇ ਹਾਂ, ਆਓ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ ਬਾਰੇ ਜਾਣਕਾਰੀ ਲੈਂਦੇ ਹਾਂ।

ਚੈਟ ਐਪਲੀਕੇਸ਼ਨ ਸਾਡੇ ਰੋਜ਼ਾਨਾ ਜੀਵਨ ਦਾ ਹਿੱਸਾ ਬਣ ਗਈਆਂ ਹਨ, ਜੋ ਸਿਰਫ਼ ਆਮ ਗੱਲਬਾਤ ਦਾ ਸਾਧਨ ਹੀ ਨਹੀਂ, ਬਲਕਿ ਗਾਹਕ ਸੇਵਾ, ਤਕਨੀਕੀ ਸਹਾਇਤਾ ਅਤੇ ਇੱਥੋਂ ਤੱਕ ਕਿ ਸੁਧਰੇ ਹੋਏ ਸਲਾਹਕਾਰ ਪ੍ਰਣਾਲੀਆਂ ਦਾ ਵੀ ਅਹੰਕਾਰ ਹਨ। ਸੰਭਵ ਹੈ ਕਿ ਤੁਸੀਂ ਹਾਲ ਹੀ ਵਿੱਚ ਕਿਸੇ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨ ਤੋਂ ਮਦਦ ਲਈ ਹੋ। ਜਿਵੇਂ ਜਿਵੇਂ ਅਸੀਂ ਜਨਰੇਟਿਵ AI ਵਰਗੀਆਂ ਅਗੇਤਰ ਤਕਨੀਕਾਂ ਨੂੰ ਇਨ੍ਹਾਂ ਪਲੇਟਫਾਰਮਾਂ ਵਿੱਚ ਸ਼ਾਮਲ ਕਰਦੇ ਹਾਂ, ਉਨ੍ਹਾਂ ਦੀ ਜਟਿਲਤਾ ਅਤੇ ਚੁਣੌਤੀਆਂ ਵੀ ਵੱਧਦੀਆਂ ਹਨ।

ਕੁਝ ਸਵਾਲ ਜਿਨ੍ਹਾਂ ਦੇ ਜਵਾਬ ਲੱਭਣੇ ਜਰੂਰੀ ਹਨ:

- **ਐਪ ਬਣਾਉਣਾ**। ਅਸੀਂ ਕਿਸ ਤਰ੍ਹਾਂ ਪ੍ਰਭਾਵਸ਼ਾਲੀ ਢੰਗ ਨਾਲ ਇਹ AI-ਸਮਰੱਥ ਐਪਲੀਕੇਸ਼ਨ ਖਾਸ ਵਰਤੋਂ ਲਈ ਬਣਾ ਕੇ ਬਿਨਾਂ ਰੁਕਾਵਟ ਦੇ ਜੋੜ ਸਕਦੇ ਹਾਂ?
- **ਨਿਗਰਾਨੀ**। ਜਦੋਂ ਇਹ ਤਿਆਰ ਹੋ ਜਾਣ, ਤਾਂ ਅਸੀਂ ਕਿਵੇਂ ਨਿਗਰਾਨੀ ਕਰ ਸਕਦੇ ਹਾਂ ਕਿ ਇਹ ਐਪਲੀਕੇਸ਼ਨ ਕਾਰਗੁਜ਼ਾਰੀ ਅਤੇ [ਜਿੰਮੇਵਾਰ AI ਦੇ ਛੇ ਸਿਧਾਂਤਾਂ](https://www.microsoft.com/ai/responsible-ai?WT.mc_id=academic-105485-koreyst) ਦੀ ਪਾਲਣਾ ਕਰਦੇ ਹੋਏ ਸਭ ਤੋਂ ਉੱਚੇ ਮਿਆਰ 'ਤੇ ਕੰਮ ਕਰ ਰਹੇ ਹਨ?

ਜਿਵੇਂ ਅਸੀਂ ਆਟੋਮੇਸ਼ਨ ਅਤੇ ਮਨੁੱਖ-ਮਸ਼ੀਨ ਇੰਟਰੈਕਸ਼ਨਾਂ ਦੇ ਯੁੱਗ ਵਿੱਚ ਅੱਗੇ ਵਧ ਰਹੇ ਹਾਂ, ਜਨਰੇਟਿਵ AI ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ ਦੀ ਗਹਿਰਾਈ, ਵਿਸ਼ਾਲਤਾ ਅਤੇ ਲਚਕੀਲਾਪਣ ਨੂੰ ਬਦਲਦਾ ਹੈ, ਇਹ ਸਮਝਣਾ ਬਹੁਤ ਜਰੂਰੀ ਹੈ। ਇਹ ਪਾਠ ਇਨ੍ਹਾਂ ਜਟਿਲ ਪ੍ਰਣਾਲੀਆਂ ਦੇ ਆਰਕੀਟੈਕਚਰ ਦੇ ਪੱਖਾਂ ਦੀ ਜਾਂਚ ਕਰੇਗਾ, ਖੇਤਰ-ਵਿਸ਼ੇਸ਼ ਕੰਮਾਂ ਲਈ ਉਨ੍ਹਾਂ ਨੂੰ ਫਾਈਨ-ਟਿਊਨ ਕਰਨ ਦੇ ਤਰੀਕੇ ਸਮਝਾਏਗਾ ਅਤੇ ਜਿੰਮੇਵਾਰ AI ਤਾਇਨਾਤੀ ਲਈ ਜ਼ਰੂਰੀ ਮਾਪਦੰਡਾਂ ਅਤੇ ਵਿਚਾਰਾਂ ਦਾ ਮੁਲਾਂਕਣ ਕਰੇਗਾ।

## ਪਰਿਚਯ

ਇਸ ਪਾਠ ਵਿੱਚ ਸ਼ਾਮਲ ਹਨ:

- ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ ਨੂੰ ਪ੍ਰਭਾਵਸ਼ਾਲੀ ਢੰਗ ਨਾਲ ਬਣਾਉਣ ਅਤੇ ਜੋੜਨ ਦੀਆਂ ਤਕਨੀਕਾਂ।
- ਐਪਲੀਕੇਸ਼ਨਾਂ ਨੂੰ ਕਸਟਮਾਈਜ਼ ਅਤੇ ਫਾਈਨ-ਟਿਊਨ ਕਰਨ ਦੇ ਤਰੀਕੇ।
- ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ ਦੀ ਨਿਗਰਾਨੀ ਕਰਨ ਲਈ ਰਣਨੀਤੀਆਂ ਅਤੇ ਵਿਚਾਰ।

## ਸਿੱਖਣ ਦੇ ਲਕੜੇ

ਇਸ ਪਾਠ ਦੇ ਅੰਤ ਤੱਕ, ਤੁਸੀਂ ਸਮਰੱਥ ਹੋਵੋਗੇ:

- ਮੌਜੂਦਾ ਪ੍ਰਣਾਲੀਆਂ ਵਿੱਚ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ ਨੂੰ ਬਣਾਉਣ ਅਤੇ ਜੋੜਨ ਲਈ ਵਿਚਾਰਾਂ ਦਾ ਵਰਣਨ ਕਰਨ।
- ਖਾਸ ਵਰਤੋਂ-ਕੇਸਾਂ ਲਈ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ ਨੂੰ ਕਸਟਮਾਈਜ਼ ਕਰਨ।
- AI-ਸਮਰੱਥ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ ਦੀ ਗੁਣਵੱਤਾ ਨੂੰ ਪ੍ਰਭਾਵਸ਼ਾਲੀ ਢੰਗ ਨਾਲ ਨਿਗਰਾਨੀ ਅਤੇ ਸੰਭਾਲਣ ਲਈ ਮੁੱਖ ਮਾਪਦੰਡਾਂ ਅਤੇ ਵਿਚਾਰਾਂ ਦੀ ਪਹਿਚਾਣ ਕਰਨ।
- ਇਹ ਯਕੀਨੀ ਬਣਾਉਣ ਕਿ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨ AI ਨੂੰ ਜਿੰਮੇਵਾਰੀ ਨਾਲ ਵਰਤ ਰਹੇ ਹਨ।

## ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ ਵਿੱਚ ਜਨਰੇਟਿਵ AI ਦਾ ਇੰਟੀਗ੍ਰੇਸ਼ਨ

ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ ਨੂੰ ਜਨਰੇਟਿਵ AI ਨਾਲ ਉੱਚਾ ਕਰਨਾ ਸਿਰਫ਼ ਉਨ੍ਹਾਂ ਨੂੰ ਹੋਸ਼ਿਆਰ ਬਣਾਉਣ ਬਾਰੇ ਨਹੀਂ ਹੈ; ਇਹ ਉਨ੍ਹਾਂ ਦੀ ਆਰਕੀਟੈਕਚਰ, ਕਾਰਗੁਜ਼ਾਰੀ ਅਤੇ ਯੂਜ਼ਰ ਇੰਟਰਫੇਸ ਨੂੰ ਇਸ ਤਰ੍ਹਾਂ ਸੁਧਾਰਨ ਬਾਰੇ ਹੈ ਕਿ ਉਪਭੋਗਤਾ ਨੂੰ ਉੱਚ ਮਿਆਰੀ ਅਨੁਭਵ ਮਿਲੇ। ਇਸ ਵਿੱਚ ਆਰਕੀਟੈਕਚਰਲ ਬੁਨਿਆਦਾਂ, API ਇੰਟੀਗ੍ਰੇਸ਼ਨ ਅਤੇ ਯੂਜ਼ਰ ਇੰਟਰਫੇਸ ਦੇ ਵਿਚਾਰ ਸ਼ਾਮਲ ਹਨ। ਇਹ ਭਾਗ ਤੁਹਾਨੂੰ ਇਹਨਾਂ ਜਟਿਲ ਖੇਤਰਾਂ ਵਿੱਚ ਰਾਹਦਾਰੀ ਦੇਣ ਲਈ ਬਣਾਇਆ ਗਿਆ ਹੈ, ਚਾਹੇ ਤੁਸੀਂ ਉਨ੍ਹਾਂ ਨੂੰ ਮੌਜੂਦਾ ਪ੍ਰਣਾਲੀਆਂ ਵਿੱਚ ਜੋੜ ਰਹੇ ਹੋ ਜਾਂ ਖੁਦਮੁਖਤਿਆਰ ਪਲੇਟਫਾਰਮ ਵਜੋਂ ਬਣਾ ਰਹੇ ਹੋ।

ਇਸ ਭਾਗ ਦੇ ਅੰਤ ਤੱਕ, ਤੁਹਾਡੇ ਕੋਲ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ ਨੂੰ ਪ੍ਰਭਾਵਸ਼ਾਲੀ ਢੰਗ ਨਾਲ ਬਣਾਉਣ ਅਤੇ ਸ਼ਾਮਲ ਕਰਨ ਦੀ ਮਹਾਰਤ ਹੋਵੇਗੀ।

### ਚੈਟਬੋਟ ਜਾਂ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨ?

ਚੈਟ ਐਪਲੀਕੇਸ਼ਨ ਬਣਾਉਣ ਤੋਂ ਪਹਿਲਾਂ, ਆਓ 'ਚੈਟਬੋਟ' ਅਤੇ 'AI-ਸਮਰੱਥ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨ' ਦੀ ਤੁਲਨਾ ਕਰੀਏ, ਜੋ ਵੱਖ-ਵੱਖ ਭੂਮਿਕਾਵਾਂ ਅਤੇ ਕਾਰਜ ਕਰਦੇ ਹਨ। ਇੱਕ ਚੈਟਬੋਟ ਦਾ ਮੁੱਖ ਮਕਸਦ ਖਾਸ ਗੱਲਬਾਤੀ ਕੰਮਾਂ ਨੂੰ ਆਟੋਮੇਟ ਕਰਨਾ ਹੁੰਦਾ ਹੈ, ਜਿਵੇਂ ਕਿ ਅਕਸਰ ਪੁੱਛੇ ਜਾਣ ਵਾਲੇ ਸਵਾਲਾਂ ਦੇ ਜਵਾਬ ਦੇਣਾ ਜਾਂ ਪੈਕੇਜ ਟਰੈਕ ਕਰਨਾ। ਇਹ ਆਮ ਤੌਰ 'ਤੇ ਨਿਯਮ-ਆਧਾਰਿਤ ਲਾਜਿਕ ਜਾਂ ਜਟਿਲ AI ਅਲਗੋਰਿਦਮਾਂ ਦੁਆਰਾ ਚਲਾਇਆ ਜਾਂਦਾ ਹੈ। ਇਸਦੇ ਉਲਟ, AI-ਸਮਰੱਥ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨ ਇੱਕ ਬਹੁਤ ਵੱਡਾ ਮਾਹੌਲ ਹੁੰਦਾ ਹੈ ਜੋ ਵੱਖ-ਵੱਖ ਕਿਸਮਾਂ ਦੀ ਡਿਜੀਟਲ ਸੰਚਾਰ, ਜਿਵੇਂ ਕਿ ਟੈਕਸਟ, ਆਵਾਜ਼ ਅਤੇ ਵੀਡੀਓ ਚੈਟ ਨੂੰ ਮਨੁੱਖੀ ਉਪਭੋਗਤਾਵਾਂ ਵਿਚਕਾਰ ਸਹੂਲਤ ਦਿੰਦਾ ਹੈ। ਇਸਦੀ ਖਾਸੀਅਤ ਜਨਰੇਟਿਵ AI ਮਾਡਲ ਦਾ ਇੰਟੀਗ੍ਰੇਸ਼ਨ ਹੈ ਜੋ ਸੁਖਮ ਅਤੇ ਮਨੁੱਖੀ-ਜਿਹਾ ਗੱਲਬਾਤ ਦਾ ਨਕਲ ਕਰਦਾ ਹੈ, ਵੱਖ-ਵੱਖ ਇਨਪੁੱਟ ਅਤੇ ਸੰਦਰਭਿਕ ਸੰਕੇਤਾਂ ਦੇ ਆਧਾਰ 'ਤੇ ਜਵਾਬ ਤਿਆਰ ਕਰਦਾ ਹੈ। ਜਨਰੇਟਿਵ AI-ਸਮਰੱਥ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨ ਖੁੱਲ੍ਹੇ ਖੇਤਰ ਦੀਆਂ ਗੱਲਬਾਤਾਂ ਕਰ ਸਕਦਾ ਹੈ, ਬਦਲਦੇ ਗੱਲਬਾਤੀ ਸੰਦਰਭਾਂ ਨਾਲ ਅਨੁਕੂਲ ਹੋ ਸਕਦਾ ਹੈ ਅਤੇ ਰਚਨਾਤਮਕ ਜਾਂ ਜਟਿਲ ਸੰਵਾਦ ਵੀ ਤਿਆਰ ਕਰ ਸਕਦਾ ਹੈ।

ਹੇਠਾਂ ਦਿੱਤੀ ਟੇਬਲ ਮੁੱਖ ਫਰਕ ਅਤੇ ਸਮਾਨਤਾਵਾਂ ਨੂੰ ਦਰਸਾਉਂਦੀ ਹੈ, ਜੋ ਸਾਨੂੰ ਉਨ੍ਹਾਂ ਦੀਆਂ ਵਿਲੱਖਣ ਭੂਮਿਕਾਵਾਂ ਨੂੰ ਸਮਝਣ ਵਿੱਚ ਮਦਦ ਕਰੇਗੀ।

| ਚੈਟਬੋਟ                               | ਜਨਰੇਟਿਵ AI-ਸਮਰੱਥ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨ          |
| ------------------------------------- | -------------------------------------- |
| ਕੰਮ-ਕੇਂਦਰਿਤ ਅਤੇ ਨਿਯਮ-ਆਧਾਰਿਤ          | ਸੰਦਰਭ-ਸੂਚਿਤ                          |
| ਅਕਸਰ ਵੱਡੀਆਂ ਪ੍ਰਣਾਲੀਆਂ ਵਿੱਚ ਸ਼ਾਮਲ        | ਇੱਕ ਜਾਂ ਕਈ ਚੈਟਬੋਟਾਂ ਦੀ ਮੇਜ਼ਬਾਨੀ ਕਰ ਸਕਦਾ ਹੈ |
| ਪ੍ਰੋਗਰਾਮ ਕੀਤੀਆਂ ਗਈਆਂ ਫੰਕਸ਼ਨਾਂ ਤੱਕ ਸੀਮਿਤ | ਜਨਰੇਟਿਵ AI ਮਾਡਲ ਸ਼ਾਮਲ ਕਰਦਾ ਹੈ          |
| ਵਿਸ਼ੇਸ਼ ਅਤੇ ਸੰਰਚਿਤ ਇੰਟਰੈਕਸ਼ਨ          | ਖੁੱਲ੍ਹੇ ਖੇਤਰ ਦੀਆਂ ਗੱਲਬਾਤਾਂ ਕਰਨ ਯੋਗ      |

### SDKs ਅਤੇ APIs ਨਾਲ ਪਹਿਲਾਂ ਤੋਂ ਬਣੇ ਫੰਕਸ਼ਨਲਿਟੀ ਦਾ ਲਾਭ ਉਠਾਉਣਾ

ਚੈਟ ਐਪਲੀਕੇਸ਼ਨ ਬਣਾਉਂਦੇ ਸਮੇਂ, ਪਹਿਲਾ ਵਧੀਆ ਕਦਮ ਇਹ ਜਾਣਚ ਕਰਨਾ ਹੈ ਕਿ ਪਹਿਲਾਂ ਤੋਂ ਕੀ ਉਪਲਬਧ ਹੈ। SDKs ਅਤੇ APIs ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨ ਬਣਾਉਣਾ ਕਈ ਕਾਰਨਾਂ ਕਰਕੇ ਲਾਭਦਾਇਕ ਹੈ। ਚੰਗੀ ਤਰ੍ਹਾਂ ਦਸਤਾਵੇਜ਼ਬੱਧ SDKs ਅਤੇ APIs ਨੂੰ ਸ਼ਾਮਲ ਕਰਕੇ, ਤੁਸੀਂ ਆਪਣੀ ਐਪਲੀਕੇਸ਼ਨ ਨੂੰ ਲੰਬੇ ਸਮੇਂ ਲਈ ਸਫਲਤਾ ਲਈ ਸਥਿਤ ਕਰ ਰਹੇ ਹੋ, ਜਿਸ ਨਾਲ ਸਕੇਲਬਿਲਟੀ ਅਤੇ ਰਖ-ਰਖਾਅ ਦੇ ਮੁੱਦੇ ਹੱਲ ਹੁੰਦੇ ਹਨ।

- **ਵਿਕਾਸ ਪ੍ਰਕਿਰਿਆ ਨੂੰ ਤੇਜ਼ ਕਰਦਾ ਹੈ ਅਤੇ ਓਵਰਹੈੱਡ ਘਟਾਉਂਦਾ ਹੈ**: ਖੁਦ ਤੋਂ ਮਹਿੰਗਾ ਫੰਕਸ਼ਨਲਿਟੀ ਬਣਾਉਣ ਦੀ ਬਜਾਏ ਪਹਿਲਾਂ ਤੋਂ ਬਣੇ ਫੰਕਸ਼ਨਲਿਟੀ 'ਤੇ ਨਿਰਭਰ ਹੋਣਾ ਤੁਹਾਨੂੰ ਆਪਣੀ ਐਪਲੀਕੇਸ਼ਨ ਦੇ ਹੋਰ ਮਹੱਤਵਪੂਰਨ ਪੱਖਾਂ, ਜਿਵੇਂ ਕਿ ਬਿਜ਼ਨਸ ਲਾਜਿਕ, 'ਤੇ ਧਿਆਨ ਕੇਂਦਰਿਤ ਕਰਨ ਦੀ ਆਜ਼ਾਦੀ ਦਿੰਦਾ ਹੈ।
- **ਵਧੀਆ ਕਾਰਗੁਜ਼ਾਰੀ**: ਜਦੋਂ ਤੁਸੀਂ ਫੰਕਸ਼ਨਲਿਟੀ ਸ਼ੁਰੂ ਤੋਂ ਬਣਾਉਂਦੇ ਹੋ, ਤਾਂ ਅਕਸਰ ਇਹ ਸਵਾਲ ਉਠਦਾ ਹੈ ਕਿ "ਇਹ ਕਿਵੇਂ ਸਕੇਲ ਕਰਦਾ ਹੈ? ਕੀ ਇਹ ਐਪਲੀਕੇਸ਼ਨ ਅਚਾਨਕ ਵੱਧਦੇ ਉਪਭੋਗਤਾਵਾਂ ਨੂੰ ਸੰਭਾਲ ਸਕਦਾ ਹੈ?" ਚੰਗੀ ਤਰ੍ਹਾਂ ਸੰਭਾਲੇ ਗਏ SDKs ਅਤੇ APIs ਅਕਸਰ ਇਨ੍ਹਾਂ ਮੁੱਦਿਆਂ ਲਈ ਅੰਦਰੂਨੀ ਹੱਲ ਰੱਖਦੇ ਹਨ।
- **ਸੌਖਾ ਰਖ-ਰਖਾਅ**: ਜਦੋਂ ਨਵਾਂ ਵਰਜਨ ਆਉਂਦਾ ਹੈ, ਤਾਂ ਅੱਪਡੇਟ ਅਤੇ ਸੁਧਾਰ ਸੌਖੇ ਹੁੰਦੇ ਹਨ ਕਿਉਂਕਿ ਜ਼ਿਆਦਾਤਰ APIs ਅਤੇ SDKs ਨੂੰ ਸਿਰਫ਼ ਲਾਇਬ੍ਰੇਰੀ ਅੱਪਡੇਟ ਕਰਨ ਦੀ ਲੋੜ ਹੁੰਦੀ ਹੈ।
- **ਅਗੇਤਰ ਤਕਨੀਕ ਤੱਕ ਪਹੁੰਚ**: ਵੱਡੇ ਡੇਟਾਸੈੱਟਾਂ 'ਤੇ ਟ੍ਰੇਨ ਕੀਤੇ ਅਤੇ ਫਾਈਨ-ਟਿਊਨ ਕੀਤੇ ਮਾਡਲਾਂ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਤੁਹਾਡੀ ਐਪਲੀਕੇਸ਼ਨ ਨੂੰ ਕੁਦਰਤੀ ਭਾਸ਼ਾ ਦੀ ਸਮਰੱਥਾ ਮਿਲਦੀ ਹੈ।

SDK ਜਾਂ API ਦੀ ਫੰਕਸ਼ਨਲਿਟੀ ਤੱਕ ਪਹੁੰਚ ਪ੍ਰਾਪਤ ਕਰਨ ਲਈ ਆਮ ਤੌਰ 'ਤੇ ਪ੍ਰਦਾਨ ਕੀਤੀਆਂ ਸੇਵਾਵਾਂ ਦੀ ਵਰਤੋਂ ਦੀ ਆਗਿਆ ਲੈਣੀ ਪੈਂਦੀ ਹੈ, ਜੋ ਅਕਸਰ ਇੱਕ ਵਿਲੱਖਣ ਕੀ ਜਾਂ ਪ੍ਰਮਾਣਿਕਤਾ ਟੋਕਨ ਰਾਹੀਂ ਹੁੰਦੀ ਹੈ। ਅਸੀਂ OpenAI Python ਲਾਇਬ੍ਰੇਰੀ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਵੇਖਾਂਗੇ ਕਿ ਇਹ ਕਿਵੇਂ ਹੁੰਦਾ ਹੈ। ਤੁਸੀਂ ਇਸਨੂੰ ਆਪਣੇ ਆਪ ਵੀ ਹੇਠਾਂ ਦਿੱਤੇ [OpenAI ਲਈ ਨੋਟਬੁੱਕ](../../../07-building-chat-applications/python/oai-assignment.ipynb) ਜਾਂ [Azure OpenAI ਸੇਵਾਵਾਂ ਲਈ ਨੋਟਬੁੱਕ](../../../07-building-chat-applications/python/aoai-assignment.ipynb) ਵਿੱਚ ਕੋਸ਼ਿਸ਼ ਕਰ ਸਕਦੇ ਹੋ।

```python
import os
from openai import OpenAI

API_KEY = os.getenv("OPENAI_API_KEY","")

client = OpenAI(
    api_key=API_KEY
    )

chat_completion = client.chat.completions.create(model="gpt-3.5-turbo", messages=[{"role": "user", "content": "Suggest two titles for an instructional lesson on chat applications for generative AI."}])
```

ਉਪਰ ਦਿੱਤਾ ਉਦਾਹਰਨ GPT-3.5 Turbo ਮਾਡਲ ਦੀ ਵਰਤੋਂ ਕਰਦਾ ਹੈ ਪ੍ਰਾਂਪਟ ਨੂੰ ਪੂਰਾ ਕਰਨ ਲਈ, ਪਰ ਧਿਆਨ ਦਿਓ ਕਿ API ਕੀ ਪਹਿਲਾਂ ਸੈੱਟ ਕੀਤੀ ਗਈ ਹੈ। ਜੇ ਤੁਸੀਂ ਕੀ ਸੈੱਟ ਨਹੀਂ ਕਰਦੇ, ਤਾਂ ਤੁਹਾਨੂੰ ਗਲਤੀ ਮਿਲੇਗੀ।

## ਯੂਜ਼ਰ ਅਨੁਭਵ (UX)

ਆਮ UX ਸਿਧਾਂਤ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ 'ਤੇ ਲਾਗੂ ਹੁੰਦੇ ਹਨ, ਪਰ ਇੱਥੇ ਕੁਝ ਵਾਧੂ ਵਿਚਾਰ ਹਨ ਜੋ ਮਸ਼ੀਨ ਲਰਨਿੰਗ ਦੇ ਹਿੱਸਿਆਂ ਕਾਰਨ ਖਾਸ ਤੌਰ 'ਤੇ ਮਹੱਤਵਪੂਰਨ ਬਣ ਜਾਂਦੇ ਹਨ।

- **ਅਸਪਸ਼ਟਤਾ ਨੂੰ ਹੱਲ ਕਰਨ ਦਾ ਤਰੀਕਾ**: ਜਨਰੇਟਿਵ AI ਮਾਡਲ ਕਈ ਵਾਰੀ ਅਸਪਸ਼ਟ ਜਵਾਬ ਦਿੰਦੇ ਹਨ। ਇੱਕ ਐਸਾ ਫੀਚਰ ਜੋ ਉਪਭੋਗਤਾਵਾਂ ਨੂੰ ਸਪਸ਼ਟੀਕਰਨ ਮੰਗਣ ਦੀ ਆਗਿਆ ਦਿੰਦਾ ਹੈ, ਜਦੋਂ ਉਹ ਇਸ ਸਮੱਸਿਆ ਦਾ ਸਾਹਮਣਾ ਕਰਦੇ ਹਨ, ਮਦਦਗਾਰ ਹੋ ਸਕਦਾ ਹੈ।
- **ਸੰਦਰਭ ਸੰਭਾਲਣਾ**: ਅਗੇਤਰ ਜਨਰੇਟਿਵ AI ਮਾਡਲ ਗੱਲਬਾਤ ਦੇ ਸੰਦਰਭ ਨੂੰ ਯਾਦ ਰੱਖਣ ਦੀ ਸਮਰੱਥਾ ਰੱਖਦੇ ਹਨ, ਜੋ ਯੂਜ਼ਰ ਅਨੁਭਵ ਲਈ ਜ਼ਰੂਰੀ ਹੋ ਸਕਦੀ ਹੈ। ਉਪਭੋਗਤਾਵਾਂ ਨੂੰ ਸੰਦਰਭ ਨੂੰ ਨਿਯੰਤਰਿਤ ਕਰਨ ਅਤੇ ਸੰਭਾਲਣ ਦੀ ਆਜ਼ਾਦੀ ਦੇਣਾ ਯੂਜ਼ਰ ਅਨੁਭਵ ਨੂੰ ਸੁਧਾਰਦਾ ਹੈ, ਪਰ ਇਸ ਨਾਲ ਸੰਵੇਦਨਸ਼ੀਲ ਜਾਣਕਾਰੀ ਰੱਖਣ ਦਾ ਖਤਰਾ ਵੀ ਹੁੰਦਾ ਹੈ। ਇਸ ਜਾਣਕਾਰੀ ਨੂੰ ਕਿੰਨੀ ਦੇਰ ਲਈ ਸਟੋਰ ਕੀਤਾ ਜਾਵੇ, ਜਿਵੇਂ ਕਿ ਰੀਟੇਨਸ਼ਨ ਨੀਤੀ ਲਾਗੂ ਕਰਨਾ, ਇਸ ਗੱਲ ਦਾ ਸੰਤੁਲਨ ਬਣਾਉਂਦਾ ਹੈ ਕਿ ਸੰਦਰਭ ਦੀ ਲੋੜ ਅਤੇ ਗੋਪਨੀਯਤਾ ਦੋਹਾਂ ਦਾ ਧਿਆਨ ਰੱਖਿਆ ਜਾਵੇ।
- **ਵਿਅਕਤੀਗਤ ਬਣਾਉਣਾ**: ਸਿੱਖਣ ਅਤੇ ਅਨੁਕੂਲ ਹੋਣ ਦੀ ਸਮਰੱਥਾ ਨਾਲ, AI ਮਾਡਲ ਉਪਭੋਗਤਾ ਲਈ ਵਿਅਕਤੀਗਤ ਅਨੁਭਵ ਪੇਸ਼ ਕਰਦੇ ਹਨ। ਯੂਜ਼ਰ ਪ੍ਰੋਫਾਈਲ ਵਰਗੇ ਫੀਚਰਾਂ ਰਾਹੀਂ ਅਨੁਭਵ ਨੂੰ ਵਿਅਕਤੀਗਤ ਬਣਾਉਣਾ ਨਾ ਸਿਰਫ਼ ਯੂਜ਼ਰ ਨੂੰ ਸਮਝਿਆ ਹੋਇਆ ਮਹਿਸੂਸ ਕਰਵਾਉਂਦਾ ਹੈ, ਸਗੋਂ ਉਹਨਾਂ ਦੀ ਖਾਸ ਜਵਾਬ ਲੱਭਣ ਦੀ ਕੋਸ਼ਿਸ਼ ਨੂੰ ਵੀ ਤੇਜ਼ ਅਤੇ ਸੰਤੁਸ਼ਟਿਕਰ ਬਣਾਉਂਦਾ ਹੈ।

ਇੱਕ ਵਿਅਕਤੀਗਤ ਬਣਾਉਣ ਦਾ ਉਦਾਹਰਨ OpenAI ਦੇ ChatGPT ਵਿੱਚ "Custom instructions" ਸੈਟਿੰਗ ਹੈ। ਇਹ ਤੁਹਾਨੂੰ ਆਪਣੇ ਬਾਰੇ ਜਾਣਕਾਰੀ ਦੇਣ ਦੀ ਆਗਿਆ ਦਿੰਦਾ ਹੈ ਜੋ ਤੁਹਾਡੇ ਪ੍ਰਾਂਪਟਾਂ ਲਈ ਮਹੱਤਵਪੂਰਨ ਸੰਦਰਭ ਹੋ ਸਕਦੀ ਹੈ। ਹੇਠਾਂ ਇੱਕ ਕਸਟਮ ਇੰਸਟ੍ਰਕਸ਼ਨ ਦਾ ਉਦਾਹਰਨ ਦਿੱਤਾ ਗਿਆ ਹੈ।

![ChatGPT ਵਿੱਚ Custom Instructions ਸੈਟਿੰਗ](../../../translated_images/custom-instructions.b96f59aa69356fcfed456414221919e8996f93c90c20d0d58d1bc0221e3c909f.pa.png)

ਇਹ "ਪ੍ਰੋਫਾਈਲ" ChatGPT ਨੂੰ ਲਿੰਕਡ ਲਿਸਟਾਂ 'ਤੇ ਇੱਕ ਪਾਠ ਯੋਜਨਾ ਬਣਾਉਣ ਲਈ ਪ੍ਰੇਰਿਤ ਕਰਦਾ ਹੈ। ਧਿਆਨ ਦਿਓ ਕਿ ChatGPT ਇਸ ਗੱਲ ਨੂੰ ਧਿਆਨ ਵਿੱਚ ਰੱਖਦਾ ਹੈ ਕਿ ਯੂਜ਼ਰ ਆਪਣੀ ਤਜਰਬੇ ਦੇ ਆਧਾਰ 'ਤੇ ਹੋਰ ਵਿਸਥਾਰਪੂਰਕ ਪਾਠ ਯੋਜਨਾ ਚਾਹੁੰਦਾ ਹੈ।

![ਲਿੰਕਡ ਲਿਸਟਾਂ ਬਾਰੇ ਪਾਠ ਯੋਜਨਾ ਲਈ ChatGPT ਵਿੱਚ ਪ੍ਰਾਂਪਟ](../../../translated_images/lesson-plan-prompt.cc47c488cf1343df5d67aa796a1acabca32c380e5b782971e289f6ab8b21cf5a.pa.png)

### ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲਾਂ ਲਈ Microsoft ਦਾ System Message Framework

[Microsoft ਨੇ LLMs ਤੋਂ ਜਵਾਬ ਤਿਆਰ ਕਰਨ ਲਈ ਪ੍ਰਭਾਵਸ਼ਾਲੀ ਸਿਸਟਮ ਸੁਨੇਹੇ ਲਿਖਣ ਲਈ ਮਾਰਗਦਰਸ਼ਨ ਦਿੱਤਾ ਹੈ](https://learn.microsoft.com/azure/ai-services/openai/concepts/system-message#define-the-models-output-format?WT.mc_id=academic-105485-koreyst), ਜੋ 4 ਖੇਤਰਾਂ ਵਿੱਚ ਵੰਡਿਆ ਗਿਆ ਹੈ:

1. ਮਾਡਲ ਕਿਸ ਲਈ ਹੈ, ਅਤੇ ਇਸ ਦੀਆਂ ਸਮਰੱਥਾਵਾਂ ਅਤੇ ਸੀਮਾਵਾਂ ਨੂੰ ਪਰਿਭਾਸ਼ਿਤ ਕਰਨਾ।
2. ਮਾਡਲ ਦੇ ਆਉਟਪੁੱਟ ਫਾਰਮੈਟ ਨੂੰ ਪਰਿਭਾਸ਼ਿਤ ਕਰਨਾ।
3. ਮਾਡਲ ਦੇ ਇਰਾਦੇ ਬਰਤਾਅ ਨੂੰ ਦਰਸਾਉਂਦੇ ਖਾਸ ਉਦਾਹ
| **ਅਸਧਾਰਣਤਾ ਪਛਾਣ**         | ਅਜਿਹੇ ਅਸਧਾਰਣ ਪੈਟਰਨਾਂ ਦੀ ਪਛਾਣ ਲਈ ਸੰਦ ਅਤੇ ਤਕਨੀਕਾਂ ਜੋ ਉਮੀਦ ਕੀਤੇ ਗਏ ਵਿਹਾਰ ਨਾਲ ਮੇਲ ਨਹੀਂ ਖਾਂਦੇ।                        | ਤੁਸੀਂ ਅਸਧਾਰਣਤਾਵਾਂ ਦਾ ਕਿਵੇਂ ਜਵਾਬ ਦੋਗੇ?                                        |

### ਚੈਟ ਐਪਲੀਕੇਸ਼ਨਾਂ ਵਿੱਚ ਜ਼ਿੰਮੇਵਾਰ AI ਅਮਲ ਲਾਗੂ ਕਰਨਾ

Microsoft ਦਾ ਜ਼ਿੰਮੇਵਾਰ AI ਲਈ ਰਵੱਈਆ ਛੇ ਸਿਧਾਂਤਾਂ ਦੀ ਪਹਚਾਣ ਕਰਦਾ ਹੈ ਜੋ AI ਵਿਕਾਸ ਅਤੇ ਇਸਦੇ ਉਪਯੋਗ ਨੂੰ ਮਾਰਗਦਰਸ਼ਨ ਦੇਣੇ ਚਾਹੀਦੇ ਹਨ। ਹੇਠਾਂ ਸਿਧਾਂਤ, ਉਹਨਾਂ ਦੀ ਪਰਿਭਾਸ਼ਾ, ਅਤੇ ਚੈਟ ਡਿਵੈਲਪਰ ਲਈ ਵਿਚਾਰ ਕਰਨ ਵਾਲੀਆਂ ਗੱਲਾਂ ਦਿੱਤੀਆਂ ਗਈਆਂ ਹਨ ਅਤੇ ਇਹ ਕਿਉਂ ਜ਼ਰੂਰੀ ਹਨ।

| ਸਿਧਾਂਤ               | Microsoft ਦੀ ਪਰਿਭਾਸ਼ਾ                                | ਚੈਟ ਡਿਵੈਲਪਰ ਲਈ ਵਿਚਾਰ                                      | ਇਹ ਕਿਉਂ ਜ਼ਰੂਰੀ ਹੈ                                                                     |
| ---------------------- | ----------------------------------------------------- | ---------------------------------------------------------------------- | -------------------------------------------------------------------------------------- |
| ਨਿਆਂਸੰਗਤਾ               | AI ਸਿਸਟਮ ਸਾਰੇ ਲੋਕਾਂ ਨਾਲ ਨਿਆਂਸੰਗਤਾ ਨਾਲ ਪੇਸ਼ ਆਉਣ।            | ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਚੈਟ ਐਪਲੀਕੇਸ਼ਨ ਉਪਭੋਗਤਾ ਡੇਟਾ ਦੇ ਆਧਾਰ 'ਤੇ ਭੇਦਭਾਵ ਨਾ ਕਰੇ।  | ਉਪਭੋਗਤਾਵਾਂ ਵਿੱਚ ਭਰੋਸਾ ਅਤੇ ਸ਼ਾਮਿਲ ਹੋਣ ਦੀ ਭਾਵਨਾ ਬਣਾਉਣ ਲਈ; ਕਾਨੂੰਨੀ ਨਤੀਜਿਆਂ ਤੋਂ ਬਚਾਅ।                |
| ਭਰੋਸੇਯੋਗਤਾ ਅਤੇ ਸੁਰੱਖਿਆ | AI ਸਿਸਟਮ ਭਰੋਸੇਯੋਗ ਅਤੇ ਸੁਰੱਖਿਅਤ ਤਰੀਕੇ ਨਾਲ ਕੰਮ ਕਰਨ।        | ਗਲਤੀਆਂ ਅਤੇ ਖ਼ਤਰਿਆਂ ਨੂੰ ਘਟਾਉਣ ਲਈ ਟੈਸਟਿੰਗ ਅਤੇ ਫੇਲ-ਸੇਫ਼ ਲਾਗੂ ਕਰੋ।         | ਉਪਭੋਗਤਾ ਸੰਤੁਸ਼ਟੀ ਯਕੀਨੀ ਬਣਾਉਂਦਾ ਹੈ ਅਤੇ ਸੰਭਾਵਿਤ ਨੁਕਸਾਨ ਤੋਂ ਬਚਾਉਂਦਾ ਹੈ।                                 |
| ਗੋਪਨੀਯਤਾ ਅਤੇ ਸੁਰੱਖਿਆ   | AI ਸਿਸਟਮ ਸੁਰੱਖਿਅਤ ਹੋਣ ਅਤੇ ਗੋਪਨੀਯਤਾ ਦਾ ਸਤਿਕਾਰ ਕਰਨ।      | ਮਜ਼ਬੂਤ ਇਨਕ੍ਰਿਪਸ਼ਨ ਅਤੇ ਡੇਟਾ ਸੁਰੱਖਿਆ ਉਪਾਅ ਲਾਗੂ ਕਰੋ।              | ਸੰਵੇਦਨਸ਼ੀਲ ਉਪਭੋਗਤਾ ਡੇਟਾ ਦੀ ਸੁਰੱਖਿਆ ਲਈ ਅਤੇ ਗੋਪਨੀਯਤਾ ਕਾਨੂੰਨਾਂ ਦੀ ਪਾਲਣਾ ਲਈ।                         |
| ਸ਼ਾਮਿਲਤਾ          | AI ਸਿਸਟਮ ਹਰ ਕਿਸੇ ਨੂੰ ਸਮਰੱਥ ਬਣਾਉਣ ਅਤੇ ਲੋਕਾਂ ਨੂੰ ਸ਼ਾਮਿਲ ਕਰਨ। | ਵੱਖ-ਵੱਖ ਦਰਸ਼ਕਾਂ ਲਈ ਪਹੁੰਚਯੋਗ ਅਤੇ ਆਸਾਨ ਵਰਤੋਂ ਵਾਲਾ UI/UX ਡਿਜ਼ਾਈਨ ਕਰੋ। | ਇਹ ਯਕੀਨੀ ਬਣਾਉਂਦਾ ਹੈ ਕਿ ਵੱਧ ਤੋਂ ਵੱਧ ਲੋਕ ਐਪਲੀਕੇਸ਼ਨ ਨੂੰ ਪ੍ਰਭਾਵਸ਼ਾਲੀ ਤਰੀਕੇ ਨਾਲ ਵਰਤ ਸਕਣ।                   |
| ਪਾਰਦਰਸ਼ਤਾ           | AI ਸਿਸਟਮ ਸਮਝਣ ਯੋਗ ਹੋਣ।                  | AI ਜਵਾਬਾਂ ਲਈ ਸਾਫ਼ ਦਸਤਾਵੇਜ਼ ਅਤੇ ਤਰਕ ਦਿਓ।            | ਜੇ ਉਪਭੋਗਤਾ ਸਮਝ ਸਕਦੇ ਹਨ ਕਿ ਫੈਸਲੇ ਕਿਵੇਂ ਲਏ ਗਏ ਹਨ ਤਾਂ ਉਹ ਸਿਸਟਮ 'ਤੇ ਜ਼ਿਆਦਾ ਭਰੋਸਾ ਕਰਦੇ ਹਨ। |
| ਜ਼ਿੰਮੇਵਾਰੀ         | ਲੋਕ AI ਸਿਸਟਮਾਂ ਲਈ ਜ਼ਿੰਮੇਵਾਰ ਹੋਣ।          | AI ਫੈਸਲਿਆਂ ਦੀ ਆਡੀਟਿੰਗ ਅਤੇ ਸੁਧਾਰ ਲਈ ਸਾਫ਼ ਪ੍ਰਕਿਰਿਆ ਬਣਾਓ।     | ਗਲਤੀਆਂ ਦੀ ਸਥਿਤੀ ਵਿੱਚ ਲਗਾਤਾਰ ਸੁਧਾਰ ਅਤੇ ਸਹੀ ਕਾਰਵਾਈ ਨੂੰ ਯਕੀਨੀ ਬਣਾਉਂਦਾ ਹੈ।               |

## ਅਸਾਈਨਮੈਂਟ

ਦੇਖੋ [assignment](../../../07-building-chat-applications/python) ਜੋ ਤੁਹਾਨੂੰ ਪਹਿਲੇ ਚੈਟ ਪ੍ਰਾਂਪਟ ਚਲਾਉਣ ਤੋਂ ਲੈ ਕੇ ਟੈਕਸਟ ਦੀ ਵਰਗੀਕਰਨ ਅਤੇ ਸੰਖੇਪ ਕਰਨ ਤੱਕ ਕਈ ਅਭਿਆਸਾਂ ਰਾਹੀਂ ਲੈ ਜਾਵੇਗਾ। ਧਿਆਨ ਦਿਓ ਕਿ ਅਸਾਈਨਮੈਂਟ ਵੱਖ-ਵੱਖ ਪ੍ਰੋਗ੍ਰਾਮਿੰਗ ਭਾਸ਼ਾਵਾਂ ਵਿੱਚ ਉਪਲਬਧ ਹਨ!

## ਸ਼ਾਬਾਸ਼! ਯਾਤਰਾ ਜਾਰੀ ਰੱਖੋ

ਇਸ ਪਾਠ ਨੂੰ ਪੂਰਾ ਕਰਨ ਤੋਂ ਬਾਅਦ, ਸਾਡੇ [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) ਨੂੰ ਵੇਖੋ ਤਾਂ ਜੋ ਤੁਸੀਂ ਆਪਣੀ Generative AI ਦੀ ਜਾਣਕਾਰੀ ਨੂੰ ਹੋਰ ਵਧਾ ਸਕੋ!

ਪਾਠ 8 ਵੱਲ ਜਾਓ ਅਤੇ ਦੇਖੋ ਕਿ ਤੁਸੀਂ ਕਿਵੇਂ [search applications ਬਣਾਉਣਾ ਸ਼ੁਰੂ ਕਰ ਸਕਦੇ ਹੋ](../08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)!

**ਅਸਵੀਕਾਰੋਪਣ**:  
ਇਹ ਦਸਤਾਵੇਜ਼ AI ਅਨੁਵਾਦ ਸੇਵਾ [Co-op Translator](https://github.com/Azure/co-op-translator) ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਅਨੁਵਾਦਿਤ ਕੀਤਾ ਗਿਆ ਹੈ। ਜਦੋਂ ਕਿ ਅਸੀਂ ਸਹੀਤਾ ਲਈ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ, ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਰੱਖੋ ਕਿ ਸਵੈਚਾਲਿਤ ਅਨੁਵਾਦਾਂ ਵਿੱਚ ਗਲਤੀਆਂ ਜਾਂ ਅਸਮਰਥਤਾਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ। ਮੂਲ ਦਸਤਾਵੇਜ਼ ਆਪਣੀ ਮੂਲ ਭਾਸ਼ਾ ਵਿੱਚ ਪ੍ਰਮਾਣਿਕ ਸਰੋਤ ਮੰਨਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ। ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਲਈ, ਪੇਸ਼ੇਵਰ ਮਨੁੱਖੀ ਅਨੁਵਾਦ ਦੀ ਸਿਫਾਰਸ਼ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਇਸ ਅਨੁਵਾਦ ਦੀ ਵਰਤੋਂ ਨਾਲ ਹੋਣ ਵਾਲੀਆਂ ਕਿਸੇ ਵੀ ਗਲਤਫਹਿਮੀਆਂ ਜਾਂ ਗਲਤ ਵਿਆਖਿਆਵਾਂ ਲਈ ਅਸੀਂ ਜ਼ਿੰਮੇਵਾਰ ਨਹੀਂ ਹਾਂ।