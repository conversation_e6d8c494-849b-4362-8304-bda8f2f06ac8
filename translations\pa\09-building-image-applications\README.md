<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "1a7fd0f95f9eb673b79da47c0814f4d4",
  "translation_date": "2025-07-09T13:22:52+00:00",
  "source_file": "09-building-image-applications/README.md",
  "language_code": "pa"
}
-->
# ਇਮੇਜ ਜਨਰੇਸ਼ਨ ਐਪਲੀਕੇਸ਼ਨ ਬਣਾਉਣਾ

[![Building Image Generation Applications](../../../translated_images/09-lesson-banner.906e408c741f44112ff5da17492a30d3872abb52b8530d6506c2631e86e704d0.pa.png)](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)

LLM ਸਿਰਫ਼ ਟੈਕਸਟ ਜਨਰੇਸ਼ਨ ਤੱਕ ਸੀਮਿਤ ਨਹੀਂ ਹਨ। ਟੈਕਸਟ ਵਰਣਨਾਂ ਤੋਂ ਤਸਵੀਰਾਂ ਬਣਾਉਣਾ ਵੀ ਸੰਭਵ ਹੈ। ਤਸਵੀਰਾਂ ਇੱਕ ਮੋਡੈਲਿਟੀ ਵਜੋਂ ਕਈ ਖੇਤਰਾਂ ਵਿੱਚ ਬਹੁਤ ਲਾਭਦਾਇਕ ਹੋ ਸਕਦੀਆਂ ਹਨ, ਜਿਵੇਂ ਕਿ MedTech, ਆਰਕੀਟੈਕਚਰ, ਟੂਰਿਜ਼ਮ, ਗੇਮ ਡਿਵੈਲਪਮੈਂਟ ਅਤੇ ਹੋਰ। ਇਸ ਅਧਿਆਇ ਵਿੱਚ, ਅਸੀਂ ਦੋ ਸਭ ਤੋਂ ਪ੍ਰਸਿੱਧ ਇਮੇਜ ਜਨਰੇਸ਼ਨ ਮਾਡਲਾਂ, DALL-E ਅਤੇ Midjourney, ਬਾਰੇ ਜਾਣੂ ਹੋਵਾਂਗੇ।

## ਪਰਿਚਯ

ਇਸ ਪਾਠ ਵਿੱਚ ਅਸੀਂ ਕਵਰ ਕਰਾਂਗੇ:

- ਇਮੇਜ ਜਨਰੇਸ਼ਨ ਅਤੇ ਇਸ ਦੀ ਲੋੜ ਕਿਉਂ ਹੈ।
- DALL-E ਅਤੇ Midjourney ਕੀ ਹਨ ਅਤੇ ਇਹ ਕਿਵੇਂ ਕੰਮ ਕਰਦੇ ਹਨ।
- ਤੁਸੀਂ ਕਿਵੇਂ ਇੱਕ ਇਮੇਜ ਜਨਰੇਸ਼ਨ ਐਪ ਬਣਾਉਂਦੇ ਹੋ।

## ਸਿੱਖਣ ਦੇ ਲਕੜੇ

ਇਸ ਪਾਠ ਨੂੰ ਪੂਰਾ ਕਰਨ ਤੋਂ ਬਾਅਦ, ਤੁਸੀਂ ਸਮਰੱਥ ਹੋਵੋਗੇ:

- ਇੱਕ ਇਮੇਜ ਜਨਰੇਸ਼ਨ ਐਪਲੀਕੇਸ਼ਨ ਬਣਾਉਣਾ।
- ਆਪਣੇ ਐਪ ਲਈ ਮੈਟਾ ਪ੍ਰਾਂਪਟਸ ਨਾਲ ਸੀਮਾਵਾਂ ਨਿਰਧਾਰਤ ਕਰਨਾ।
- DALL-E ਅਤੇ Midjourney ਨਾਲ ਕੰਮ ਕਰਨਾ।

## ਇਮੇਜ ਜਨਰੇਸ਼ਨ ਐਪ ਬਣਾਉਣ ਦੀ ਲੋੜ ਕਿਉਂ?

ਇਮੇਜ ਜਨਰੇਸ਼ਨ ਐਪਲੀਕੇਸ਼ਨ ਜਨਰੇਟਿਵ AI ਦੀਆਂ ਸਮਰੱਥਾਵਾਂ ਨੂੰ ਖੋਜਣ ਦਾ ਵਧੀਆ ਤਰੀਕਾ ਹਨ। ਇਹਨਾਂ ਨੂੰ ਉਦਾਹਰਨ ਵਜੋਂ ਵਰਤਿਆ ਜਾ ਸਕਦਾ ਹੈ:

- **ਇਮੇਜ ਐਡਿਟਿੰਗ ਅਤੇ ਸਿੰਥੇਸਿਸ**। ਤੁਸੀਂ ਵੱਖ-ਵੱਖ ਮਕਸਦਾਂ ਲਈ ਤਸਵੀਰਾਂ ਬਣਾ ਸਕਦੇ ਹੋ, ਜਿਵੇਂ ਕਿ ਤਸਵੀਰ ਸੰਪਾਦਨ ਅਤੇ ਤਸਵੀਰ ਸਿੰਥੇਸਿਸ।

- **ਕਈ ਉਦਯੋਗਾਂ ਵਿੱਚ ਲਾਗੂ**। ਇਹਨਾਂ ਨੂੰ Medtech, ਟੂਰਿਜ਼ਮ, ਗੇਮ ਡਿਵੈਲਪਮੈਂਟ ਅਤੇ ਹੋਰ ਉਦਯੋਗਾਂ ਲਈ ਤਸਵੀਰਾਂ ਬਣਾਉਣ ਲਈ ਵੀ ਵਰਤਿਆ ਜਾ ਸਕਦਾ ਹੈ।

## ਸਥਿਤੀ: Edu4All

ਇਸ ਪਾਠ ਦੇ ਹਿੱਸੇ ਵਜੋਂ, ਅਸੀਂ ਆਪਣੇ ਸਟਾਰਟਅਪ Edu4All ਨਾਲ ਕੰਮ ਜਾਰੀ ਰੱਖਾਂਗੇ। ਵਿਦਿਆਰਥੀ ਆਪਣੇ ਅਸਾਈਨਮੈਂਟ ਲਈ ਤਸਵੀਰਾਂ ਬਣਾਉਣਗੇ, ਜੋ ਕਿ ਉਹਨਾਂ ਦੀ ਰਚਨਾਤਮਕਤਾ 'ਤੇ ਨਿਰਭਰ ਕਰਦਾ ਹੈ। ਉਹ ਆਪਣੀ ਕਹਾਣੀ ਲਈ ਇਲਸਟਰੈਸ਼ਨ ਬਣਾ ਸਕਦੇ ਹਨ, ਨਵਾਂ ਕਿਰਦਾਰ ਤਿਆਰ ਕਰ ਸਕਦੇ ਹਨ ਜਾਂ ਆਪਣੇ ਵਿਚਾਰਾਂ ਅਤੇ ਸੰਕਲਪਾਂ ਨੂੰ ਵਿਜ਼ੂਅਲਾਈਜ਼ ਕਰ ਸਕਦੇ ਹਨ।

ਜੇਕਰ ਵਿਦਿਆਰਥੀ ਕਲਾਸ ਵਿੱਚ ਮੋਨੂਮੈਂਟਸ 'ਤੇ ਕੰਮ ਕਰ ਰਹੇ ਹਨ, ਤਾਂ ਉਹ ਕੁਝ ਇਸ ਤਰ੍ਹਾਂ ਤਸਵੀਰਾਂ ਬਣਾ ਸਕਦੇ ਹਨ:

![Edu4All startup, class on monuments, Eiffel Tower](../../../translated_images/startup.94d6b79cc4bb3f5afbf6e2ddfcf309aa5d1e256b5f30cc41d252024eaa9cc5dc.pa.png)

ਇਸ ਪ੍ਰਾਂਪਟ ਨਾਲ:

> "Dog next to Eiffel Tower in early morning sunlight"

## DALL-E ਅਤੇ Midjourney ਕੀ ਹਨ?

[DALL-E](https://openai.com/dall-e-2?WT.mc_id=academic-105485-koreyst) ਅਤੇ [Midjourney](https://www.midjourney.com/?WT.mc_id=academic-105485-koreyst) ਦੋ ਸਭ ਤੋਂ ਪ੍ਰਸਿੱਧ ਇਮੇਜ ਜਨਰੇਸ਼ਨ ਮਾਡਲ ਹਨ, ਜੋ ਪ੍ਰਾਂਪਟ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਤਸਵੀਰਾਂ ਬਣਾਉਂਦੇ ਹਨ।

### DALL-E

ਆਓ DALL-E ਨਾਲ ਸ਼ੁਰੂ ਕਰੀਏ, ਜੋ ਕਿ ਇੱਕ ਜਨਰੇਟਿਵ AI ਮਾਡਲ ਹੈ ਜੋ ਟੈਕਸਟ ਵਰਣਨਾਂ ਤੋਂ ਤਸਵੀਰਾਂ ਬਣਾਉਂਦਾ ਹੈ।

> [DALL-E ਦੋ ਮਾਡਲਾਂ, CLIP ਅਤੇ diffused attention, ਦਾ ਮਿਲਾਪ ਹੈ](https://towardsdatascience.com/openais-dall-e-and-clip-101-a-brief-introduction-3a4367280d4e?WT.mc_id=academic-105485-koreyst)।

- **CLIP**, ਇੱਕ ਮਾਡਲ ਹੈ ਜੋ ਤਸਵੀਰਾਂ ਅਤੇ ਟੈਕਸਟ ਤੋਂ ਡਾਟਾ ਦੇ ਨੰਬਰਾਤਮਕ ਪ੍ਰਤੀਨਿਧੀਆਂ (ਐਮਬੈਡਿੰਗਜ਼) ਬਣਾਉਂਦਾ ਹੈ।

- **Diffused attention**, ਇੱਕ ਮਾਡਲ ਹੈ ਜੋ ਐਮਬੈਡਿੰਗਜ਼ ਤੋਂ ਤਸਵੀਰਾਂ ਬਣਾਉਂਦਾ ਹੈ। DALL-E ਨੂੰ ਤਸਵੀਰਾਂ ਅਤੇ ਟੈਕਸਟ ਦੇ ਡਾਟਾਸੈੱਟ 'ਤੇ ਟ੍ਰੇਨ ਕੀਤਾ ਗਿਆ ਹੈ ਅਤੇ ਇਹ ਟੈਕਸਟ ਵਰਣਨਾਂ ਤੋਂ ਤਸਵੀਰਾਂ ਬਣਾਉਣ ਲਈ ਵਰਤਿਆ ਜਾ ਸਕਦਾ ਹੈ। ਉਦਾਹਰਨ ਵਜੋਂ, DALL-E ਇੱਕ ਟੋਪੀ ਵਾਲੀ ਬਿੱਲੀ ਜਾਂ ਮੋਹਾਕ ਵਾਲੇ ਕੁੱਤੇ ਦੀ ਤਸਵੀਰ ਬਣਾ ਸਕਦਾ ਹੈ।

### Midjourney

Midjourney ਵੀ DALL-E ਵਾਂਗ ਹੀ ਕੰਮ ਕਰਦਾ ਹੈ, ਇਹ ਟੈਕਸਟ ਪ੍ਰਾਂਪਟ ਤੋਂ ਤਸਵੀਰਾਂ ਬਣਾਉਂਦਾ ਹੈ। Midjourney ਨਾਲ ਵੀ ਤੁਸੀਂ “a cat in a hat” ਜਾਂ “dog with a mohawk” ਵਰਗੇ ਪ੍ਰਾਂਪਟ ਨਾਲ ਤਸਵੀਰਾਂ ਬਣਾ ਸਕਦੇ ਹੋ।

![Image generated by Midjourney, mechanical pigeon](https://upload.wikimedia.org/wikipedia/commons/thumb/8/8c/Rupert_Breheny_mechanical_dove_eca144e7-476d-4976-821d-a49c408e4f36.png/440px-Rupert_Breheny_mechanical_dove_eca144e7-476d-4976-821d-a49c408e4f36.png?WT.mc_id=academic-105485-koreyst)  
_ਤਸਵੀਰ ਦਾ ਸਰੋਤ: ਵਿਕੀਪੀਡੀਆ, Midjourney ਦੁਆਰਾ ਬਣਾਈ ਗਈ ਤਸਵੀਰ_

## DALL-E ਅਤੇ Midjourney ਕਿਵੇਂ ਕੰਮ ਕਰਦੇ ਹਨ

ਸਭ ਤੋਂ ਪਹਿਲਾਂ, [DALL-E](https://arxiv.org/pdf/2102.12092.pdf?WT.mc_id=academic-105485-koreyst)। DALL-E ਇੱਕ ਜਨਰੇਟਿਵ AI ਮਾਡਲ ਹੈ ਜੋ ਟ੍ਰਾਂਸਫਾਰਮਰ ਆਰਕੀਟੈਕਚਰ 'ਤੇ ਆਧਾਰਿਤ ਹੈ ਅਤੇ ਇਸ ਵਿੱਚ _autoregressive transformer_ ਹੁੰਦਾ ਹੈ।

ਇੱਕ _autoregressive transformer_ ਇਹ ਨਿਰਧਾਰਤ ਕਰਦਾ ਹੈ ਕਿ ਮਾਡਲ ਟੈਕਸਟ ਵਰਣਨਾਂ ਤੋਂ ਕਿਵੇਂ ਤਸਵੀਰਾਂ ਬਣਾਉਂਦਾ ਹੈ। ਇਹ ਇੱਕ ਵਾਰੀ ਇੱਕ ਪਿਕਸਲ ਬਣਾਉਂਦਾ ਹੈ, ਫਿਰ ਉਸ ਪਿਕਸਲ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਅਗਲਾ ਪਿਕਸਲ ਬਣਾਉਂਦਾ ਹੈ। ਇਹ ਪ੍ਰਕਿਰਿਆ ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਦੀਆਂ ਕਈ ਪਰਤਾਂ ਵਿੱਚੋਂ ਲੰਘਦੀ ਹੈ, ਜਦ ਤੱਕ ਤਸਵੀਰ ਪੂਰੀ ਨਹੀਂ ਹੋ ਜਾਂਦੀ।

ਇਸ ਪ੍ਰਕਿਰਿਆ ਨਾਲ, DALL-E ਤਸਵੀਰ ਵਿੱਚ ਵਿਸ਼ੇਸ਼ਤਾਵਾਂ, ਵਸਤੂਆਂ, ਲੱਛਣਾਂ ਅਤੇ ਹੋਰ ਬਹੁਤ ਕੁਝ ਨਿਯੰਤਰਿਤ ਕਰਦਾ ਹੈ। ਹਾਲਾਂਕਿ, DALL-E 2 ਅਤੇ 3 ਵਿੱਚ ਤਸਵੀਰ ਉੱਤੇ ਹੋਰ ਵਧੀਆ ਨਿਯੰਤਰਣ ਹੁੰਦਾ ਹੈ।

## ਆਪਣਾ ਪਹਿਲਾ ਇਮੇਜ ਜਨਰੇਸ਼ਨ ਐਪ ਬਣਾਉਣਾ

ਇਮੇਜ ਜਨਰੇਸ਼ਨ ਐਪ ਬਣਾਉਣ ਲਈ ਤੁਹਾਨੂੰ ਹੇਠਾਂ ਦਿੱਤੀਆਂ ਲਾਇਬ੍ਰੇਰੀਆਂ ਦੀ ਲੋੜ ਪਵੇਗੀ:

- **python-dotenv**, ਇਹ ਲਾਇਬ੍ਰੇਰੀ ਤੁਹਾਡੇ ਸਿਕ੍ਰੇਟ _.env_ ਫਾਇਲ ਵਿੱਚ ਸੁਰੱਖਿਅਤ ਰੱਖਣ ਲਈ ਬਹੁਤ ਸਿਫਾਰਸ਼ੀ ਹੈ।
- **openai**, ਇਹ ਲਾਇਬ੍ਰੇਰੀ OpenAI API ਨਾਲ ਇੰਟਰੈਕਟ ਕਰਨ ਲਈ ਵਰਤੀ ਜਾਂਦੀ ਹੈ।
- **pillow**, Python ਵਿੱਚ ਤਸਵੀਰਾਂ ਨਾਲ ਕੰਮ ਕਰਨ ਲਈ।
- **requests**, HTTP ਰਿਕਵੈਸਟ ਕਰਨ ਵਿੱਚ ਮਦਦ ਲਈ।

1. _.env_ ਨਾਮਕ ਫਾਇਲ ਬਣਾਓ ਅਤੇ ਹੇਠਾਂ ਦਿੱਤਾ ਸਮੱਗਰੀ ਪਾਓ:

   ```text
   AZURE_OPENAI_ENDPOINT=<your endpoint>
   AZURE_OPENAI_API_KEY=<your key>
   ```

   ਇਹ ਜਾਣਕਾਰੀ Azure Portal ਵਿੱਚ "Keys and Endpoint" ਸੈਕਸ਼ਨ ਵਿੱਚ ਮਿਲੇਗੀ।

1. ਉਪਰੋਕਤ ਲਾਇਬ੍ਰੇਰੀਆਂ ਨੂੰ _requirements.txt_ ਫਾਇਲ ਵਿੱਚ ਇਕੱਠਾ ਕਰੋ:

   ```text
   python-dotenv
   openai
   pillow
   requests
   ```

1. ਫਿਰ, ਵਰਚੁਅਲ ਇਨਵਾਇਰਨਮੈਂਟ ਬਣਾਓ ਅਤੇ ਲਾਇਬ੍ਰੇਰੀਆਂ ਇੰਸਟਾਲ ਕਰੋ:

   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

   Windows ਲਈ, ਵਰਚੁਅਲ ਇਨਵਾਇਰਨਮੈਂਟ ਬਣਾਉਣ ਅਤੇ ਐਕਟੀਵੇਟ ਕਰਨ ਲਈ ਹੇਠਾਂ ਦਿੱਤੇ ਕਮਾਂਡ ਵਰਤੋ:

   ```bash
   python3 -m venv venv
   venv\Scripts\activate.bat
   ```

1. _app.py_ ਨਾਮਕ ਫਾਇਲ ਵਿੱਚ ਹੇਠਾਂ ਦਿੱਤਾ ਕੋਡ ਸ਼ਾਮਲ ਕਰੋ:

   ```python
   import openai
   import os
   import requests
   from PIL import Image
   import dotenv

   # import dotenv
   dotenv.load_dotenv()

   # Get endpoint and key from environment variables
   openai.api_base = os.environ['AZURE_OPENAI_ENDPOINT']
   openai.api_key = os.environ['AZURE_OPENAI_API_KEY']

   # Assign the API version (DALL-E is currently supported for the 2023-06-01-preview API version only)
   openai.api_version = '2023-06-01-preview'
   openai.api_type = 'azure'


   try:
       # Create an image by using the image generation API
       generation_response = openai.Image.create(
           prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
           size='1024x1024',
           n=2,
           temperature=0,
       )
       # Set the directory for the stored image
       image_dir = os.path.join(os.curdir, 'images')

       # If the directory doesn't exist, create it
       if not os.path.isdir(image_dir):
           os.mkdir(image_dir)

       # Initialize the image path (note the filetype should be png)
       image_path = os.path.join(image_dir, 'generated-image.png')

       # Retrieve the generated image
       image_url = generation_response["data"][0]["url"]  # extract image URL from response
       generated_image = requests.get(image_url).content  # download the image
       with open(image_path, "wb") as image_file:
           image_file.write(generated_image)

       # Display the image in the default image viewer
       image = Image.open(image_path)
       image.show()

   # catch exceptions
   except openai.InvalidRequestError as err:
       print(err)

   ```

ਆਓ ਇਸ ਕੋਡ ਨੂੰ ਸਮਝੀਏ:

- ਸਭ ਤੋਂ ਪਹਿਲਾਂ, ਅਸੀਂ ਲਾਇਬ੍ਰੇਰੀਆਂ ਇੰਪੋਰਟ ਕਰਦੇ ਹਾਂ, ਜਿਵੇਂ ਕਿ OpenAI, dotenv, requests ਅਤੇ Pillow।

  ```python
  import openai
  import os
  import requests
  from PIL import Image
  import dotenv
  ```

- ਫਿਰ, ਅਸੀਂ _.env_ ਫਾਇਲ ਤੋਂ ਵਾਤਾਵਰਣ ਵੈਰੀਏਬਲ ਲੋਡ ਕਰਦੇ ਹਾਂ।

  ```python
  # import dotenv
  dotenv.load_dotenv()
  ```

- ਇਸ ਤੋਂ ਬਾਅਦ, ਅਸੀਂ OpenAI API ਲਈ endpoint, key, ਵਰਜਨ ਅਤੇ ਕਿਸਮ ਸੈੱਟ ਕਰਦੇ ਹਾਂ।

  ```python
  # Get endpoint and key from environment variables
  openai.api_base = os.environ['AZURE_OPENAI_ENDPOINT']
  openai.api_key = os.environ['AZURE_OPENAI_API_KEY']

  # add version and type, Azure specific
  openai.api_version = '2023-06-01-preview'
  openai.api_type = 'azure'
  ```

- ਫਿਰ, ਅਸੀਂ ਤਸਵੀਰ ਜਨਰੇਟ ਕਰਦੇ ਹਾਂ:

  ```python
  # Create an image by using the image generation API
  generation_response = openai.Image.create(
      prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
      size='1024x1024',
      n=2,
      temperature=0,
  )
  ```

  ਉਪਰੋਕਤ ਕੋਡ ਇੱਕ JSON ਆਬਜੈਕਟ ਵਾਪਸ ਕਰਦਾ ਹੈ ਜਿਸ ਵਿੱਚ ਬਣਾਈ ਗਈ ਤਸਵੀਰ ਦਾ URL ਹੁੰਦਾ ਹੈ। ਅਸੀਂ ਇਸ URL ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਤਸਵੀਰ ਡਾਊਨਲੋਡ ਕਰ ਸਕਦੇ ਹਾਂ ਅਤੇ ਫਾਇਲ ਵਿੱਚ ਸੇਵ ਕਰ ਸਕਦੇ ਹਾਂ।

- ਆਖਿਰ ਵਿੱਚ, ਅਸੀਂ ਤਸਵੀਰ ਖੋਲ੍ਹਦੇ ਹਾਂ ਅਤੇ ਸਧਾਰਣ ਇਮੇਜ ਵਿਊਅਰ ਨਾਲ ਦਿਖਾਉਂਦੇ ਹਾਂ:

  ```python
  image = Image.open(image_path)
  image.show()
  ```

### ਤਸਵੀਰ ਜਨਰੇਸ਼ਨ ਕੋਡ ਬਾਰੇ ਹੋਰ ਜਾਣਕਾਰੀ

ਆਓ ਤਸਵੀਰ ਬਣਾਉਣ ਵਾਲੇ ਕੋਡ ਨੂੰ ਵਧੇਰੇ ਵਿਸਥਾਰ ਨਾਲ ਵੇਖੀਏ:

```python
generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2,
        temperature=0,
    )
```

- **prompt**, ਉਹ ਟੈਕਸਟ ਪ੍ਰਾਂਪਟ ਹੈ ਜੋ ਤਸਵੀਰ ਬਣਾਉਣ ਲਈ ਵਰਤਿਆ ਜਾਂਦਾ ਹੈ। ਇਸ ਮਾਮਲੇ ਵਿੱਚ, ਅਸੀਂ ਪ੍ਰਾਂਪਟ "Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils" ਵਰਤ ਰਹੇ ਹਾਂ।
- **size**, ਤਸਵੀਰ ਦਾ ਆਕਾਰ ਹੈ ਜੋ ਬਣਾਈ ਜਾ ਰਹੀ ਹੈ। ਇਸ ਮਾਮਲੇ ਵਿੱਚ, ਅਸੀਂ 1024x1024 ਪਿਕਸਲ ਦੀ ਤਸਵੀਰ ਬਣਾ ਰਹੇ ਹਾਂ।
- **n**, ਤਸਵੀਰਾਂ ਦੀ ਗਿਣਤੀ ਹੈ ਜੋ ਬਣਾਈਆਂ ਜਾ ਰਹੀਆਂ ਹਨ। ਇਸ ਮਾਮਲੇ ਵਿੱਚ, ਅਸੀਂ ਦੋ ਤਸਵੀਰਾਂ ਬਣਾ ਰਹੇ ਹਾਂ।
- **temperature**, ਇਹ ਪੈਰਾਮੀਟਰ ਜਨਰੇਟਿਵ AI ਮਾਡਲ ਦੇ ਆਉਟਪੁੱਟ ਦੀ ਬੇਤਰਤੀਬੀ ਨੂੰ ਨਿਯੰਤਰਿਤ ਕਰਦਾ ਹੈ। ਇਹ 0 ਤੋਂ 1 ਦੇ ਵਿਚਕਾਰ ਹੁੰਦਾ ਹੈ, ਜਿੱਥੇ 0 ਦਾ ਮਤਲਬ ਹੈ ਕਿ ਨਤੀਜਾ ਨਿਰਧਾਰਿਤ ਹੈ ਅਤੇ 1 ਦਾ ਮਤਲਬ ਹੈ ਕਿ ਨਤੀਜਾ ਬੇਤਰਤੀਬੀ ਹੈ। ਡਿਫਾਲਟ ਮੁੱਲ 0.7 ਹੈ।

ਅਗਲੇ ਭਾਗ ਵਿੱਚ ਅਸੀਂ ਤਸਵੀਰਾਂ ਨਾਲ ਹੋਰ ਕੀ ਕਰ ਸਕਦੇ ਹਾਂ, ਇਸ ਬਾਰੇ ਜਾਣੂ ਹੋਵਾਂਗੇ।

## ਇਮੇਜ ਜਨਰੇਸ਼ਨ ਦੀਆਂ ਵਾਧੂ ਸਮਰੱਥਾਵਾਂ

ਹੁਣ ਤੱਕ ਤੁਸੀਂ ਦੇਖਿਆ ਕਿ ਅਸੀਂ ਕੁਝ ਲਾਈਨਾਂ ਕੋਡ ਨਾਲ ਕਿਵੇਂ ਤਸਵੀਰ ਬਣਾਈ। ਪਰ ਤਸਵੀਰਾਂ ਨਾਲ ਹੋਰ ਵੀ ਕਈ ਕੰਮ ਕੀਤੇ ਜਾ ਸਕਦੇ ਹਨ।

ਤੁਸੀਂ ਹੇਠਾਂ ਦਿੱਤੇ ਕੰਮ ਵੀ ਕਰ ਸਕਦੇ ਹੋ:

- **ਸੰਪਾਦਨ ਕਰੋ**। ਮੌਜੂਦਾ ਤਸਵੀਰ ਨੂੰ ਮਾਸਕ ਅਤੇ ਪ੍ਰਾਂਪਟ ਦੇ ਕੇ ਬਦਲਿਆ ਜਾ ਸਕਦਾ ਹੈ। ਉਦਾਹਰਨ ਵਜੋਂ, ਤੁਸੀਂ ਤਸਵੀਰ ਦੇ ਕਿਸੇ ਹਿੱਸੇ ਵਿੱਚ ਕੁਝ ਜੋੜ ਸਕਦੇ ਹੋ। ਸੋਚੋ ਕਿ ਸਾਡੀ ਖਰਗੋਸ਼ ਦੀ ਤਸਵੀਰ ਵਿੱਚ ਤੁਸੀਂ ਖਰਗੋਸ਼ ਨੂੰ ਟੋਪੀ ਪਹਿਨਾ ਸਕਦੇ ਹੋ। ਇਹ ਕਰਨ ਲਈ, ਤੁਸੀਂ ਤਸਵੀਰ, ਮਾਸਕ (ਜੋ ਬਦਲਾਅ ਵਾਲੇ ਹਿੱਸੇ ਨੂੰ ਦਰਸਾਉਂਦਾ ਹੈ) ਅਤੇ ਟੈਕਸਟ ਪ੍ਰਾਂਪਟ ਦਿੰਦੇ ਹੋ।

  ```python
  response = openai.Image.create_edit(
    image=open("base_image.png", "rb"),
    mask=open("mask.png", "rb"),
    prompt="An image of a rabbit with a hat on its head.",
    n=1,
    size="1024x1024"
  )
  image_url = response['data'][0]['url']
  ```

  ਮੁਢਲੀ ਤਸਵੀਰ ਵਿੱਚ ਸਿਰਫ ਖਰਗੋਸ਼ ਹੋਵੇਗਾ ਪਰ ਅੰਤਿਮ ਤਸਵੀਰ ਵਿੱਚ ਖਰਗੋਸ਼ ਦੇ ਸਿਰ 'ਤੇ ਟੋਪੀ ਹੋਵੇਗੀ।

- **ਵੈਰੀਏਸ਼ਨ ਬਣਾਓ**। ਮੌਜੂਦਾ ਤਸਵੀਰ ਲੈ ਕੇ ਉਸ ਦੀਆਂ ਵੱਖ-ਵੱਖ ਵੈਰੀਏਸ਼ਨ ਬਣਾਈਆਂ ਜਾ ਸਕਦੀਆਂ ਹਨ। ਵੈਰੀਏਸ਼ਨ ਬਣਾਉਣ ਲਈ, ਤੁਸੀਂ ਤਸਵੀਰ ਅਤੇ ਟੈਕਸਟ ਪ੍ਰਾਂਪਟ ਦਿੰਦੇ ਹੋ ਅਤੇ ਕੋਡ ਵਰਤਦੇ ਹੋ:

  ```python
  response = openai.Image.create_variation(
    image=open("bunny-lollipop.png", "rb"),
    n=1,
    size="1024x1024"
  )
  image_url = response['data'][0]['url']
  ```

  > ਨੋਟ: ਇਹ ਸਿਰਫ OpenAI 'ਤੇ ਸਮਰਥਿਤ ਹੈ

## Temperature

Temperature ਇੱਕ ਪੈਰਾਮੀਟਰ ਹੈ ਜੋ ਜਨਰੇਟਿਵ AI ਮਾਡਲ ਦੇ ਨਤੀਜੇ ਦੀ ਬੇਤਰਤੀਬੀ ਨੂੰ ਨਿਯੰਤਰਿਤ ਕਰਦਾ ਹੈ। ਇਹ 0 ਤੋਂ 1 ਦੇ ਵਿਚਕਾਰ ਹੁੰਦਾ ਹੈ, ਜਿੱਥੇ 0 ਦਾ ਮਤਲਬ ਹੈ ਨਤੀਜਾ ਨਿਰਧਾਰਿਤ ਅਤੇ 1 ਦਾ ਮਤਲਬ ਹੈ ਨਤੀਜਾ ਬੇਤਰਤੀਬੀ। ਡਿਫਾਲਟ ਮੁੱਲ 0.7 ਹੈ।

ਆਓ temperature ਦੇ ਕੰਮ ਕਰਨ ਦਾ ਉਦਾਹਰਨ ਦੇਖੀਏ, ਇਸ ਪ੍ਰਾਂਪਟ ਨੂੰ ਦੋ ਵਾਰੀ ਚਲਾਕੇ:

> ਪ੍ਰਾਂਪਟ: "Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils"

![Bunny on a horse holding a lollipop, version 1](../../../translated_images/v1-generated-image.a295cfcffa3c13c2432eb1e41de7e49a78c814000fb1b462234be24b6e0db7ea.pa.png)

ਹੁਣ ਇਸੇ ਪ੍ਰਾਂਪਟ ਨੂੰ ਦੁਬਾਰਾ ਚਲਾਈਏ ਤਾਂ ਕਿ ਅਸੀਂ ਵੇਖ ਸਕੀਏ ਕਿ ਦੋ ਵਾਰੀ ਇੱਕੋ ਤਸਵੀਰ ਨਹੀਂ ਮਿਲੇਗੀ:

![Generated image of bunny on horse](../../../translated_images/v2-generated-image.33f55a3714efe61dc19622c869ba6cd7d6e6de562e26e95b5810486187aace39.pa.png)

ਜਿਵੇਂ ਤੁਸੀਂ ਵੇਖ ਸਕਦੇ ਹੋ, ਤਸਵੀਰਾਂ ਮਿਲਦੀਆਂ-ਜੁਲਦੀਆਂ ਹਨ ਪਰ ਇਕੋ ਨਹੀਂ। ਹੁਣ temperature ਮੁੱਲ 0.1 ਕਰਕੇ ਦੇਖੀਏ ਕਿ ਕੀ ਹੁੰਦਾ ਹੈ:

```python
 generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2
    )
```

### Temperature ਬਦਲਣਾ

ਆਓ ਜਵਾਬ ਨੂੰ ਹੋਰ ਨਿਰਧਾਰਿਤ ਬਣਾਈਏ। ਅਸੀਂ ਦੋ ਤਸਵੀਰਾਂ ਵਿੱਚ ਦੇਖਿਆ ਕਿ ਪਹਿਲੀ ਵਿੱਚ ਖਰਗੋਸ਼ ਹੈ ਅਤੇ ਦੂਜੀ ਵਿੱਚ ਘੋੜਾ, ਇਸ ਲਈ ਤਸਵੀਰਾਂ ਵਿੱਚ ਵੱਡਾ ਫਰਕ ਹੈ।

ਇਸ ਲਈ ਅਸੀਂ ਕੋਡ ਬਦਲ ਕੇ temperature ਨੂੰ 0 ਕਰਦੇ ਹਾਂ:

```python
generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2,
        temperature=0
    )
```

ਹੁਣ ਜਦੋਂ ਤੁਸੀਂ ਇਹ ਕੋਡ ਚਲਾਓਗੇ, ਤਾਂ ਤੁਹਾਨੂੰ ਇਹ ਦੋ ਤਸਵੀਰਾਂ ਮਿਲਣਗੀਆਂ:

- ![Temperature 0, v1](../../../translated_images/v1-temp-generated-image.a4346e1d2360a056d855ee3dfcedcce91211747967cb882e7d2eff2076f90e4a.pa.png)
- ![Temperature 0 , v2](../../../translated_images/v2-temp-generated-image.871d0c920dbfb0f1cb5d9d80bffd52da9b41f83b386320d9a9998635630ec83d.pa.png)

ਇੱਥੇ ਤੁਸੀਂ ਸਾਫ਼ ਦੇਖ ਸਕਦੇ ਹੋ ਕਿ ਤਸਵੀਰਾਂ ਇੱਕ ਦੂਜੇ ਨਾਲ ਜ਼ਿਆਦਾ ਮਿਲਦੀਆਂ ਹਨ।

## ਮੈਟਾਪ੍ਰਾਂਪਟਸ ਨਾਲ ਆਪਣੇ ਐਪ ਲਈ ਸੀਮਾਵਾਂ ਨਿਰਧਾਰਤ ਕਰਨਾ

ਸਾਡੇ ਡੈਮੋ ਨਾਲ, ਅਸੀਂ ਪਹਿਲਾਂ ਹੀ ਆਪਣੇ ਕਲਾਇੰਟਾਂ ਲਈ ਤਸਵੀਰਾਂ ਬਣਾ ਸਕਦੇ ਹਾਂ। ਪਰ ਸਾਨੂੰ ਆਪਣੇ ਐਪ ਲਈ ਕੁਝ ਸੀਮਾਵਾਂ ਬਣਾਉਣੀਆਂ ਪੈਣਗੀਆਂ।

ਉਦਾਹਰਨ ਵਜੋਂ, ਅਸੀਂ ਐਸੀ ਤਸਵੀਰਾਂ ਨਹੀਂ ਬਣਾਉਣਾ ਚਾਹੁੰਦੇ ਜੋ ਕੰਮ ਲਈ ਸੁਰੱਖਿਅਤ ਨਾ ਹੋਣ ਜਾਂ ਬੱਚਿਆਂ ਲਈ ਢੁਕਵਾਂ ਨਾ ਹੋਣ।

ਇਹ ਅਸੀਂ _ਮੈਟਾਪ੍ਰਾਂਪਟਸ_ ਨਾਲ ਕਰ ਸਕਦੇ ਹਾਂ। ਮੈਟਾਪ੍ਰਾਂਪਟਸ ਉਹ ਟੈਕਸਟ ਪ੍ਰਾਂਪਟ ਹੁੰਦੇ ਹਨ ਜੋ ਜਨਰੇਟਿਵ AI ਮਾਡਲ ਦੇ ਨਤੀਜੇ ਨੂੰ ਨਿਯੰਤਰਿਤ ਕਰਨ ਲਈ ਵਰਤੇ ਜਾਂਦੇ ਹਨ। ਉਦਾਹਰਨ ਵਜੋਂ, ਅਸੀਂ ਮੈਟਾਪ੍ਰ

**ਅਸਵੀਕਾਰੋਪਣ**:  
ਇਹ ਦਸਤਾਵੇਜ਼ AI ਅਨੁਵਾਦ ਸੇਵਾ [Co-op Translator](https://github.com/Azure/co-op-translator) ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਅਨੁਵਾਦਿਤ ਕੀਤਾ ਗਿਆ ਹੈ। ਜਦੋਂ ਕਿ ਅਸੀਂ ਸਹੀਤਾ ਲਈ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ, ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਰੱਖੋ ਕਿ ਸਵੈਚਾਲਿਤ ਅਨੁਵਾਦਾਂ ਵਿੱਚ ਗਲਤੀਆਂ ਜਾਂ ਅਸਮਰਥਤਾਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ। ਮੂਲ ਦਸਤਾਵੇਜ਼ ਆਪਣੀ ਮੂਲ ਭਾਸ਼ਾ ਵਿੱਚ ਪ੍ਰਮਾਣਿਕ ਸਰੋਤ ਮੰਨਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ। ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਲਈ, ਪੇਸ਼ੇਵਰ ਮਨੁੱਖੀ ਅਨੁਵਾਦ ਦੀ ਸਿਫਾਰਸ਼ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਇਸ ਅਨੁਵਾਦ ਦੀ ਵਰਤੋਂ ਤੋਂ ਉਤਪੰਨ ਕਿਸੇ ਵੀ ਗਲਤਫਹਮੀ ਜਾਂ ਗਲਤ ਵਿਆਖਿਆ ਲਈ ਅਸੀਂ ਜ਼ਿੰਮੇਵਾਰ ਨਹੀਂ ਹਾਂ।