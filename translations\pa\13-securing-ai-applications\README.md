<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "f3cac698e9eea47dd563633bd82daf8c",
  "translation_date": "2025-07-09T15:22:26+00:00",
  "source_file": "13-securing-ai-applications/README.md",
  "language_code": "pa"
}
-->
# ਤੁਹਾਡੇ ਜਨਰੇਟਿਵ AI ਐਪਲੀਕੇਸ਼ਨਾਂ ਦੀ ਸੁਰੱਖਿਆ

[![ਤੁਹਾਡੇ ਜਨਰੇਟਿਵ AI ਐਪਲੀਕੇਸ਼ਨਾਂ ਦੀ ਸੁਰੱਖਿਆ](../../../translated_images/13-lesson-banner.14103e36b4bbf17398b64ed2b0531f6f2c6549e7f7342f797c40bcae5a11862e.pa.png)](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst)

## ਪਰਿਚਯ

ਇਸ ਪਾਠ ਵਿੱਚ ਇਹ ਗੱਲਾਂ ਕਵਰ ਕੀਤੀਆਂ ਜਾਣਗੀਆਂ:

- AI ਸਿਸਟਮਾਂ ਦੇ ਸੰਦਰਭ ਵਿੱਚ ਸੁਰੱਖਿਆ।
- AI ਸਿਸਟਮਾਂ ਨੂੰ ਹੋਣ ਵਾਲੇ ਆਮ ਖਤਰੇ ਅਤੇ ਧਮਕੀਆਂ।
- AI ਸਿਸਟਮਾਂ ਦੀ ਸੁਰੱਖਿਆ ਲਈ ਤਰੀਕੇ ਅਤੇ ਵਿਚਾਰ।

## ਸਿੱਖਣ ਦੇ ਲਕੜੇ

ਇਸ ਪਾਠ ਨੂੰ ਪੂਰਾ ਕਰਨ ਤੋਂ ਬਾਅਦ, ਤੁਹਾਨੂੰ ਇਹ ਸਮਝ ਆ ਜਾਵੇਗੀ:

- AI ਸਿਸਟਮਾਂ ਨੂੰ ਹੋਣ ਵਾਲੀਆਂ ਧਮਕੀਆਂ ਅਤੇ ਖਤਰੇ।
- AI ਸਿਸਟਮਾਂ ਦੀ ਸੁਰੱਖਿਆ ਲਈ ਆਮ ਤਰੀਕੇ ਅਤੇ ਅਮਲ।
- ਸੁਰੱਖਿਆ ਟੈਸਟਿੰਗ ਲਾਗੂ ਕਰਨ ਨਾਲ ਅਣਚਾਹੇ ਨਤੀਜਿਆਂ ਅਤੇ ਉਪਭੋਗਤਾ ਭਰੋਸੇ ਦੀ ਘਟਤੀ ਨੂੰ ਕਿਵੇਂ ਰੋਕਿਆ ਜਾ ਸਕਦਾ ਹੈ।

## ਜਨਰੇਟਿਵ AI ਦੇ ਸੰਦਰਭ ਵਿੱਚ ਸੁਰੱਖਿਆ ਦਾ ਕੀ ਮਤਲਬ ਹੈ?

ਜਿਵੇਂ ਕਿ ਕ੍ਰਿਤ੍ਰਿਮ ਬੁੱਧੀ (AI) ਅਤੇ ਮਸ਼ੀਨ ਲਰਨਿੰਗ (ML) ਤਕਨਾਲੋਜੀਆਂ ਸਾਡੇ ਜੀਵਨ ਨੂੰ ਵੱਧ ਤੋਂ ਵੱਧ ਪ੍ਰਭਾਵਿਤ ਕਰ ਰਹੀਆਂ ਹਨ, ਇਹ ਜਰੂਰੀ ਹੈ ਕਿ ਸਿਰਫ ਗਾਹਕਾਂ ਦੇ ਡੇਟਾ ਹੀ ਨਹੀਂ, ਸਗੋਂ AI ਸਿਸਟਮਾਂ ਦੀ ਵੀ ਸੁਰੱਖਿਆ ਕੀਤੀ ਜਾਵੇ। AI/ML ਨੂੰ ਅਜਿਹੇ ਉਦਯੋਗਾਂ ਵਿੱਚ ਉੱਚ-ਮੁੱਲ ਵਾਲੇ ਫੈਸਲੇ ਕਰਨ ਵਿੱਚ ਵਰਤਿਆ ਜਾ ਰਿਹਾ ਹੈ ਜਿੱਥੇ ਗਲਤ ਫੈਸਲਾ ਗੰਭੀਰ ਨਤੀਜੇ ਲਿਆ ਸਕਦਾ ਹੈ।

ਇੱਥੇ ਕੁਝ ਮੁੱਖ ਗੱਲਾਂ ਹਨ ਜੋ ਧਿਆਨ ਵਿੱਚ ਰੱਖਣੀਆਂ ਚਾਹੀਦੀਆਂ ਹਨ:

- **AI/ML ਦਾ ਪ੍ਰਭਾਵ**: AI/ML ਦਾ ਰੋਜ਼ਾਨਾ ਜੀਵਨ 'ਤੇ ਵੱਡਾ ਪ੍ਰਭਾਵ ਹੈ ਅਤੇ ਇਸ ਲਈ ਇਹਨਾਂ ਦੀ ਸੁਰੱਖਿਆ ਬਹੁਤ ਜਰੂਰੀ ਹੋ ਗਈ ਹੈ।
- **ਸੁਰੱਖਿਆ ਚੁਣੌਤੀਆਂ**: AI/ML ਦੇ ਇਸ ਪ੍ਰਭਾਵ ਨੂੰ ਧਿਆਨ ਵਿੱਚ ਰੱਖਦੇ ਹੋਏ, AI-ਆਧਾਰਿਤ ਉਤਪਾਦਾਂ ਨੂੰ ਸੁਚੱਜੇ ਹਮਲਿਆਂ ਤੋਂ ਬਚਾਉਣਾ ਜਰੂਰੀ ਹੈ, ਚਾਹੇ ਉਹ ਟਰੋਲਜ਼ ਹੋਣ ਜਾਂ ਸੰਗਠਿਤ ਗਰੁੱਪ।
- **ਰਣਨੀਤਿਕ ਸਮੱਸਿਆਵਾਂ**: ਟੈਕਨਾਲੋਜੀ ਉਦਯੋਗ ਨੂੰ ਲੰਬੇ ਸਮੇਂ ਲਈ ਗਾਹਕਾਂ ਦੀ ਸੁਰੱਖਿਆ ਅਤੇ ਡੇਟਾ ਸੁਰੱਖਿਆ ਯਕੀਨੀ ਬਣਾਉਣ ਲਈ ਰਣਨੀਤਿਕ ਚੁਣੌਤੀਆਂ ਦਾ ਸਾਹਮਣਾ ਕਰਨਾ ਪਵੇਗਾ।

ਇਸ ਤੋਂ ਇਲਾਵਾ, ਮਸ਼ੀਨ ਲਰਨਿੰਗ ਮਾਡਲ ਆਮ ਤੌਰ 'ਤੇ ਮਾਲਿਸ਼ੀਅਸ ਇਨਪੁੱਟ ਅਤੇ ਸਧਾਰਣ ਅਸਧਾਰਣ ਡੇਟਾ ਵਿੱਚ ਫਰਕ ਨਹੀਂ ਕਰ ਸਕਦੇ। ਟ੍ਰੇਨਿੰਗ ਡੇਟਾ ਦਾ ਇੱਕ ਵੱਡਾ ਹਿੱਸਾ ਅਣ-ਸੰਭਾਲਿਆ, ਅਣ-ਮੋਡਰੇਟਡ, ਜਨਤਕ ਡੇਟਾਸੈੱਟਾਂ ਤੋਂ ਆਉਂਦਾ ਹੈ, ਜਿੱਥੇ ਤੀਜੀ ਪੱਖੀ ਯੋਗਦਾਨਕਾਰੀਆਂ ਦਾ ਵੀ ਹਿੱਸਾ ਹੁੰਦਾ ਹੈ। ਹਮਲਾਵਰਾਂ ਨੂੰ ਡੇਟਾਸੈੱਟਾਂ ਨੂੰ ਹੈਕ ਕਰਨ ਦੀ ਲੋੜ ਨਹੀਂ ਹੁੰਦੀ ਜਦੋਂ ਉਹਨਾਂ ਨੂੰ ਯੋਗਦਾਨ ਦੇਣ ਦੀ ਆਜ਼ਾਦੀ ਹੁੰਦੀ ਹੈ। ਸਮੇਂ ਦੇ ਨਾਲ, ਘੱਟ ਭਰੋਸੇਯੋਗ ਮਾਲਿਸ਼ੀਅਸ ਡੇਟਾ ਉੱਚ ਭਰੋਸੇਯੋਗ ਡੇਟਾ ਬਣ ਜਾਂਦਾ ਹੈ, ਜੇ ਡੇਟਾ ਦੀ ਬਣਤਰ/ਫਾਰਮੈਟ ਸਹੀ ਰਹਿੰਦੀ ਹੈ।

ਇਸ ਲਈ ਇਹ ਬਹੁਤ ਜਰੂਰੀ ਹੈ ਕਿ ਤੁਸੀਂ ਆਪਣੇ ਮਾਡਲਾਂ ਵੱਲੋਂ ਫੈਸਲੇ ਕਰਨ ਲਈ ਵਰਤੇ ਜਾਣ ਵਾਲੇ ਡੇਟਾ ਸਟੋਰਾਂ ਦੀ ਸਚਾਈ ਅਤੇ ਸੁਰੱਖਿਆ ਯਕੀਨੀ ਬਣਾਓ।

## AI ਦੀਆਂ ਧਮਕੀਆਂ ਅਤੇ ਖਤਰਿਆਂ ਨੂੰ ਸਮਝਣਾ

AI ਅਤੇ ਸੰਬੰਧਿਤ ਸਿਸਟਮਾਂ ਦੇ ਸੰਦਰਭ ਵਿੱਚ, ਡੇਟਾ ਪੋਇਜ਼ਨਿੰਗ ਅੱਜ ਦਾ ਸਭ ਤੋਂ ਵੱਡਾ ਸੁਰੱਖਿਆ ਖਤਰਾ ਹੈ। ਡੇਟਾ ਪੋਇਜ਼ਨਿੰਗ ਉਹ ਹੈ ਜਦੋਂ ਕੋਈ ਵਿਅਕਤੀ ਜਾਣ-ਬੁਝ ਕੇ AI ਨੂੰ ਟ੍ਰੇਨ ਕਰਨ ਵਾਲੀ ਜਾਣਕਾਰੀ ਨੂੰ ਬਦਲਦਾ ਹੈ, ਜਿਸ ਨਾਲ AI ਗਲਤ ਨਤੀਜੇ ਦਿੰਦਾ ਹੈ। ਇਹ ਇਸ ਲਈ ਹੁੰਦਾ ਹੈ ਕਿਉਂਕਿ ਸਟੈਂਡਰਡ ਡਿਟੈਕਸ਼ਨ ਅਤੇ ਰੋਕਥਾਮ ਦੇ ਤਰੀਕੇ ਮੌਜੂਦ ਨਹੀਂ ਹਨ ਅਤੇ ਅਸੀਂ ਅਣ-ਭਰੋਸੇਯੋਗ ਜਾਂ ਅਣ-ਸੰਭਾਲੇ ਜਨਤਕ ਡੇਟਾਸੈੱਟਾਂ 'ਤੇ ਨਿਰਭਰ ਕਰਦੇ ਹਾਂ। ਡੇਟਾ ਦੀ ਸਚਾਈ ਬਣਾਈ ਰੱਖਣ ਅਤੇ ਖਰਾਬ ਟ੍ਰੇਨਿੰਗ ਪ੍ਰਕਿਰਿਆ ਨੂੰ ਰੋਕਣ ਲਈ, ਤੁਹਾਡੇ ਡੇਟਾ ਦੇ ਮੂਲ ਅਤੇ ਵੰਸ਼ਾਵਲੀ ਨੂੰ ਟਰੈਕ ਕਰਨਾ ਬਹੁਤ ਜਰੂਰੀ ਹੈ। ਨਹੀਂ ਤਾਂ, "ਗਰਬੇਜ ਇਨ, ਗਰਬੇਜ ਆਉਟ" ਦਾ ਮਤਲਬ ਸਹੀ ਬੈਠਦਾ ਹੈ, ਜਿਸ ਨਾਲ ਮਾਡਲ ਦੀ ਕਾਰਗੁਜ਼ਾਰੀ ਖਰਾਬ ਹੋ ਜਾਂਦੀ ਹੈ।

ਇੱਥੇ ਕੁਝ ਉਦਾਹਰਣ ਹਨ ਕਿ ਡੇਟਾ ਪੋਇਜ਼ਨਿੰਗ ਤੁਹਾਡੇ ਮਾਡਲਾਂ ਨੂੰ ਕਿਵੇਂ ਪ੍ਰਭਾਵਿਤ ਕਰ ਸਕਦੀ ਹੈ:

1. **ਲੇਬਲ ਫਲਿੱਪਿੰਗ**: ਇੱਕ ਬਾਈਨਰੀ ਕਲਾਸੀਫਿਕੇਸ਼ਨ ਟਾਸਕ ਵਿੱਚ, ਵੈਰੀ ਵਿਰੋਧੀ ਜਾਣ-ਬੁਝ ਕੇ ਟ੍ਰੇਨਿੰਗ ਡੇਟਾ ਦੇ ਛੋਟੇ ਹਿੱਸੇ ਦੇ ਲੇਬਲ ਬਦਲ ਦਿੰਦਾ ਹੈ। ਉਦਾਹਰਣ ਵਜੋਂ, ਸਧਾਰਣ ਨਮੂਨੇ ਨੂੰ ਮਾਲਿਸ਼ੀਅਸ ਲੇਬਲ ਦਿੱਤਾ ਜਾਂਦਾ ਹੈ, ਜਿਸ ਨਾਲ ਮਾਡਲ ਗਲਤ ਸਬੰਧ ਸਿੱਖਦਾ ਹੈ।\
   **ਉਦਾਹਰਣ**: ਇੱਕ ਸਪੈਮ ਫਿਲਟਰ ਸਹੀ ਈਮੇਲਾਂ ਨੂੰ ਸਪੈਮ ਵਜੋਂ ਗਲਤ ਕਲਾਸੀਫਾਈ ਕਰਦਾ ਹੈ ਕਿਉਂਕਿ ਲੇਬਲ ਬਦਲੇ ਗਏ ਹਨ।
2. **ਫੀਚਰ ਪੋਇਜ਼ਨਿੰਗ**: ਹਮਲਾਵਰ ਟ੍ਰੇਨਿੰਗ ਡੇਟਾ ਵਿੱਚ ਫੀਚਰਾਂ ਨੂੰ ਹੌਲੀ-ਹੌਲੀ ਬਦਲ ਕੇ ਮਾਡਲ ਨੂੰ ਭ੍ਰਮਿਤ ਕਰਦਾ ਹੈ।\
   **ਉਦਾਹਰਣ**: ਉਤਪਾਦ ਦੇ ਵੇਰਵੇ ਵਿੱਚ ਅਣਜਾਣੇ ਕੀਵਰਡ ਸ਼ਾਮਲ ਕਰਕੇ ਸਿਫਾਰਸ਼ੀ ਪ੍ਰਣਾਲੀਆਂ ਨੂੰ ਪ੍ਰਭਾਵਿਤ ਕਰਨਾ।
3. **ਡੇਟਾ ਇੰਜੈਕਸ਼ਨ**: ਮਾਡਲ ਦੇ ਵਿਹਾਰ ਨੂੰ ਪ੍ਰਭਾਵਿਤ ਕਰਨ ਲਈ ਟ੍ਰੇਨਿੰਗ ਸੈੱਟ ਵਿੱਚ ਮਾਲਿਸ਼ੀਅਸ ਡੇਟਾ ਸ਼ਾਮਲ ਕਰਨਾ।\
   **ਉਦਾਹਰਣ**: ਨਕਲੀ ਉਪਭੋਗਤਾ ਸਮੀਖਿਆਵਾਂ ਸ਼ਾਮਲ ਕਰਕੇ ਭਾਵਨਾ ਵਿਸ਼ਲੇਸ਼ਣ ਦੇ ਨਤੀਜਿਆਂ ਨੂੰ ਬਦਲਣਾ।
4. **ਬੈਕਡੋਰ ਹਮਲੇ**: ਵੈਰੀ ਵਿਰੋਧੀ ਟ੍ਰੇਨਿੰਗ ਡੇਟਾ ਵਿੱਚ ਇੱਕ ਛੁਪਿਆ ਹੋਇਆ ਪੈਟਰਨ (ਬੈਕਡੋਰ) ਸ਼ਾਮਲ ਕਰਦਾ ਹੈ। ਮਾਡਲ ਇਸ ਪੈਟਰਨ ਨੂੰ ਪਛਾਣਦਾ ਹੈ ਅਤੇ ਜਦੋਂ ਇਹ ਟ੍ਰਿਗਰ ਹੁੰਦਾ ਹੈ ਤਾਂ ਮਾਲਿਸ਼ੀਅਸ ਵਿਹਾਰ ਕਰਦਾ ਹੈ।\
   **ਉਦਾਹਰਣ**: ਇੱਕ ਚਿਹਰਾ ਪਛਾਣਣ ਵਾਲੀ ਪ੍ਰਣਾਲੀ ਜਿਸ ਨੂੰ ਬੈਕਡੋਰ ਵਾਲੀਆਂ ਤਸਵੀਰਾਂ ਨਾਲ ਟ੍ਰੇਨ ਕੀਤਾ ਗਿਆ ਹੈ ਅਤੇ ਜੋ ਕਿਸੇ ਵਿਸ਼ੇਸ਼ ਵਿਅਕਤੀ ਨੂੰ ਗਲਤ ਪਛਾਣਦੀ ਹੈ।

MITRE Corporation ਨੇ [ATLAS (Adversarial Threat Landscape for Artificial-Intelligence Systems)](https://atlas.mitre.org/?WT.mc_id=academic-105485-koreyst) ਬਣਾਇਆ ਹੈ, ਜੋ AI ਸਿਸਟਮਾਂ 'ਤੇ ਹਮਲਾਵਰਾਂ ਵੱਲੋਂ ਵਰਤੇ ਜਾਣ ਵਾਲੇ ਤਰੀਕਿਆਂ ਅਤੇ ਤਕਨੀਕਾਂ ਦਾ ਗਿਆਨਕੋਸ਼ ਹੈ।

> AI-ਸਮਰੱਥ ਸਿਸਟਮਾਂ ਵਿੱਚ ਕਈ ਨਵੀਆਂ ਕਮਜ਼ੋਰੀਆਂ ਆ ਰਹੀਆਂ ਹਨ, ਕਿਉਂਕਿ AI ਦੇ ਸ਼ਾਮਲ ਹੋਣ ਨਾਲ ਮੌਜੂਦਾ ਸਿਸਟਮਾਂ ਦਾ ਹਮਲਾ ਸਤਹ ਵਧ ਗਿਆ ਹੈ ਜੋ ਪਰੰਪਰਾਗਤ ਸਾਈਬਰ ਹਮਲਿਆਂ ਤੋਂ ਵੱਧ ਹੈ। ਅਸੀਂ ATLAS ਤਿਆਰ ਕੀਤਾ ਹੈ ਤਾਂ ਜੋ ਇਹਨਾਂ ਵਿਲੱਖਣ ਅਤੇ ਵਿਕਸਤ ਹੁੰਦੀਆਂ ਕਮਜ਼ੋਰੀਆਂ ਬਾਰੇ ਜਾਗਰੂਕਤਾ ਵਧਾਈ ਜਾ ਸਕੇ, ਕਿਉਂਕਿ ਵਿਸ਼ਵ ਭਰ ਦੀ ਕਮਿਊਨਿਟੀ ਵੱਖ-ਵੱਖ ਸਿਸਟਮਾਂ ਵਿੱਚ AI ਨੂੰ ਸ਼ਾਮਲ ਕਰ ਰਹੀ ਹੈ। ATLAS ਨੂੰ MITRE ATT&CK® ਫਰੇਮਵਰਕ ਦੇ ਅਧਾਰ 'ਤੇ ਮਾਡਲ ਕੀਤਾ ਗਿਆ ਹੈ ਅਤੇ ਇਸ ਦੀਆਂ ਤਕਨੀਕਾਂ, ਤਰੀਕੇ ਅਤੇ ਪ੍ਰਕਿਰਿਆਵਾਂ ATT&CK ਨਾਲ ਪੂਰੀਆਂ ਹੁੰਦੀਆਂ ਹਨ।

ਜਿਵੇਂ ਕਿ MITRE ATT&CK® ਫਰੇਮਵਰਕ, ਜੋ ਪਰੰਪਰਾਗਤ ਸਾਈਬਰਸੁਰੱਖਿਆ ਵਿੱਚ ਉੱਚ-ਪੱਧਰੀ ਧਮਕੀ ਨਕਲ ਸਥਿਤੀਆਂ ਦੀ ਯੋਜਨਾ ਬਣਾਉਣ ਲਈ ਵਰਤਿਆ ਜਾਂਦਾ ਹੈ, ATLAS ਵੀ ਇੱਕ ਆਸਾਨੀ ਨਾਲ ਖੋਜਯੋਗ TTPs ਦਾ ਸੈੱਟ ਪ੍ਰਦਾਨ ਕਰਦਾ ਹੈ ਜੋ ਨਵੇਂ ਹਮਲਿਆਂ ਤੋਂ ਬਚਾਅ ਲਈ ਸਮਝ ਅਤੇ ਤਿਆਰੀ ਵਿੱਚ ਮਦਦ ਕਰਦਾ ਹੈ।

ਇਸ ਤੋਂ ਇਲਾਵਾ, Open Web Application Security Project (OWASP) ਨੇ LLMs ਵਰਤਣ ਵਾਲੀਆਂ ਐਪਲੀਕੇਸ਼ਨਾਂ ਵਿੱਚ ਮਿਲਣ ਵਾਲੀਆਂ ਸਭ ਤੋਂ ਮਹੱਤਵਪੂਰਨ ਕਮਜ਼ੋਰੀਆਂ ਦੀ "[ਟੌਪ 10 ਸੂਚੀ](https://llmtop10.com/?WT.mc_id=academic-105485-koreyst)" ਬਣਾਈ ਹੈ। ਇਸ ਸੂਚੀ ਵਿੱਚ ਡੇਟਾ ਪੋਇਜ਼ਨਿੰਗ ਵਰਗੀਆਂ ਧਮਕੀਆਂ ਦੇ ਨਾਲ-ਨਾਲ ਹੋਰ ਖਤਰੇ ਵੀ ਦਰਸਾਏ ਗਏ ਹਨ, ਜਿਵੇਂ:

- **Prompt Injection**: ਇੱਕ ਤਕਨੀਕ ਜਿਸ ਵਿੱਚ ਹਮਲਾਵਰ Large Language Model (LLM) ਨੂੰ ਧੋਖੇਬਾਜ਼ੀ ਨਾਲ ਤਿਆਰ ਕੀਤੇ ਇਨਪੁੱਟਾਂ ਰਾਹੀਂ ਮੈਨਿਪੁਲੇਟ ਕਰਦੇ ਹਨ, ਜਿਸ ਨਾਲ ਇਹ ਆਪਣੀ ਮੂਲ ਵਿਹਾਰ ਤੋਂ ਬਾਹਰ ਚੱਲਦਾ ਹੈ।
- **ਸਪਲਾਈ ਚੇਨ ਕਮਜ਼ੋਰੀਆਂ**: LLM ਵੱਲੋਂ ਵਰਤੇ ਜਾਣ ਵਾਲੇ ਐਪਲੀਕੇਸ਼ਨਾਂ ਦੇ ਹਿੱਸੇ ਅਤੇ ਸਾਫਟਵੇਅਰ, ਜਿਵੇਂ ਕਿ Python ਮੋਡੀਊਲ ਜਾਂ ਬਾਹਰੀ ਡੇਟਾਸੈੱਟ, ਖੁਦ ਕਮਜ਼ੋਰ ਹੋ ਸਕਦੇ ਹਨ, ਜਿਸ ਨਾਲ ਅਣਚਾਹੇ ਨਤੀਜੇ, ਪੱਖਪਾਤ ਅਤੇ ਬੁਨਿਆਦੀ ਢਾਂਚੇ ਵਿੱਚ ਕਮਜ਼ੋਰੀਆਂ ਆ ਸਕਦੀਆਂ ਹਨ।
- **ਅਤਿ ਨਿਰਭਰਤਾ**: LLM ਗਲਤੀਆਂ ਕਰਨ ਵਾਲੇ ਹੁੰਦੇ ਹਨ ਅਤੇ ਕਈ ਵਾਰ ਹਲੂਸੀਨੇਸ਼ਨ ਕਰਦੇ ਹਨ, ਜਿਸ ਨਾਲ ਗਲਤ ਜਾਂ ਅਸੁਰੱਖਿਅਤ ਨਤੀਜੇ ਮਿਲਦੇ ਹਨ। ਕਈ ਦਸਤਾਵੇਜ਼ੀ ਸਥਿਤੀਆਂ ਵਿੱਚ ਲੋਕਾਂ ਨੇ ਨਤੀਜਿਆਂ ਨੂੰ ਸੱਚ ਮੰਨ ਕੇ ਅਣਚਾਹੇ ਨਤੀਜੇ ਭੁਗਤੇ ਹਨ।

Microsoft Cloud Advocate Rod Trent ਨੇ ਇੱਕ ਮੁਫ਼ਤ ਈਬੁੱਕ, [Must Learn AI Security](https://github.com/rod-trent/OpenAISecurity/tree/main/Must_Learn/Book_Version?WT.mc_id=academic-105485-koreyst), ਲਿਖੀ ਹੈ ਜੋ ਇਹਨਾਂ ਅਤੇ ਹੋਰ ਨਵੇਂ AI ਖਤਰਿਆਂ ਵਿੱਚ ਡੂੰਘਾਈ ਨਾਲ ਜਾਣਕਾਰੀ ਦਿੰਦੀ ਹੈ ਅਤੇ ਇਨ੍ਹਾਂ ਸਥਿਤੀਆਂ ਨਾਲ ਨਜਿੱਠਣ ਲਈ ਵਿਆਪਕ ਮਾਰਗਦਰਸ਼ਨ ਪ੍ਰਦਾਨ ਕਰਦੀ ਹੈ।

## AI ਸਿਸਟਮਾਂ ਅਤੇ LLMs ਲਈ ਸੁਰੱਖਿਆ ਟੈਸਟਿੰਗ

ਕ੍ਰਿਤ੍ਰਿਮ ਬੁੱਧੀ (AI) ਵੱਖ-ਵੱਖ ਖੇਤਰਾਂ ਅਤੇ ਉਦਯੋਗਾਂ ਵਿੱਚ ਬਦਲਾਅ ਲਿਆ ਰਹੀ ਹੈ, ਸਮਾਜ ਲਈ ਨਵੀਆਂ ਸੰਭਾਵਨਾਵਾਂ ਅਤੇ ਲਾਭ ਪੈਦਾ ਕਰ ਰਹੀ ਹੈ। ਪਰ AI ਨਾਲ ਕੁਝ ਵੱਡੀਆਂ ਚੁਣੌਤੀਆਂ ਅਤੇ ਖਤਰੇ ਵੀ ਜੁੜੇ ਹਨ, ਜਿਵੇਂ ਡੇਟਾ ਪ੍ਰਾਈਵੇਸੀ, ਪੱਖਪਾਤ, ਸਮਝਾਉਣ ਦੀ ਘਾਟ ਅਤੇ ਸੰਭਾਵਿਤ ਗਲਤ ਵਰਤੋਂ। ਇਸ ਲਈ ਇਹ ਜਰੂਰੀ ਹੈ ਕਿ AI ਸਿਸਟਮ ਸੁਰੱਖਿਅਤ ਅਤੇ ਜ਼ਿੰਮੇਵਾਰ ਹੋਣ, ਜਿਸਦਾ ਮਤਲਬ ਹੈ ਕਿ ਉਹ ਨੈਤਿਕ ਅਤੇ ਕਾਨੂੰਨੀ ਮਿਆਰਾਂ ਦਾ ਪਾਲਣ ਕਰਨ ਅਤੇ ਉਪਭੋਗਤਾਵਾਂ ਅਤੇ ਹਿੱਸੇਦਾਰਾਂ ਵੱਲੋਂ ਭਰੋਸੇਯੋਗ ਹੋਣ।

ਸੁਰੱਖਿਆ ਟੈਸਟਿੰਗ ਇੱਕ ਪ੍ਰਕਿਰਿਆ ਹੈ ਜਿਸ ਵਿੱਚ AI ਸਿਸਟਮ ਜਾਂ LLM ਦੀਆਂ ਕਮਜ਼ੋਰੀਆਂ ਦੀ ਪਛਾਣ ਅਤੇ ਉਨ੍ਹਾਂ ਦਾ ਫਾਇਦਾ ਉਠਾ ਕੇ ਸੁਰੱਖਿਆ ਦਾ ਮੁਲਾਂਕਣ ਕੀਤਾ ਜਾਂਦਾ ਹੈ। ਇਹ ਟੈਸਟਿੰਗ ਵਿਕਾਸਕਾਰਾਂ, ਉਪਭੋਗਤਾਵਾਂ ਜਾਂ ਤੀਜੀ ਪੱਖੀ ਆਡੀਟਰਾਂ ਵੱਲੋਂ ਕੀਤੀ ਜਾ ਸਕਦੀ ਹੈ, ਟੈਸਟਿੰਗ ਦੇ ਮਕਸਦ ਅਤੇ ਦਾਇਰੇ ਦੇ ਅਨੁਸਾਰ। AI ਸਿਸਟਮਾਂ ਅਤੇ LLMs ਲਈ ਕੁਝ ਆਮ ਸੁਰੱਖਿਆ ਟੈਸਟਿੰਗ ਤਰੀਕੇ ਹਨ:

- **ਡੇਟਾ ਸੈਨਿਟਾਈਜ਼ੇਸ਼ਨ**: ਇਹ ਪ੍ਰਕਿਰਿਆ ਹੈ ਜਿਸ ਵਿੱਚ ਟ੍ਰੇਨਿੰਗ ਡੇਟਾ ਜਾਂ AI ਸਿਸਟਮ ਜਾਂ LLM ਦੇ ਇਨਪੁੱਟ ਤੋਂ ਸੰਵੇਦਨਸ਼ੀਲ ਜਾਂ ਨਿੱਜੀ ਜਾਣਕਾਰੀ ਨੂੰ ਹਟਾਇਆ ਜਾਂ ਅਣਜਾਣਾ ਕੀਤਾ ਜਾਂਦਾ ਹੈ। ਡੇਟਾ ਸੈਨਿਟਾਈਜ਼ੇਸ਼ਨ ਡੇਟਾ ਲੀਕ ਅਤੇ ਮਾਲਿਸ਼ੀਅਸ ਮੈਨਿਪੁਲੇਸ਼ਨ ਨੂੰ ਰੋਕਣ ਵਿੱਚ ਮਦਦ ਕਰਦਾ ਹੈ।
- **ਵੈਰੀ ਵਿਰੋਧੀ ਟੈਸਟਿੰਗ**: ਇਹ ਪ੍ਰਕਿਰਿਆ ਹੈ ਜਿਸ ਵਿੱਚ AI ਸਿਸਟਮ ਜਾਂ LLM ਦੇ ਇਨਪੁੱਟ ਜਾਂ ਆਉਟਪੁੱਟ 'ਤੇ ਵੈਰੀ ਵਿਰੋਧੀ ਉਦਾਹਰਣ ਲਾਗੂ ਕਰਕੇ ਇਸ ਦੀ ਮਜ਼ਬੂਤੀ ਅਤੇ ਹਮਲਿਆਂ ਵਿਰੁੱਧ ਰੋਧਕਤਾ ਦਾ ਮੁਲਾਂਕਣ ਕੀਤਾ ਜਾਂਦਾ ਹੈ। ਇਸ ਨਾਲ ਕਮਜ਼ੋਰੀਆਂ ਦੀ ਪਛਾਣ ਅਤੇ ਰੋਕਥਾਮ ਹੋ ਸਕਦੀ ਹੈ।
- **ਮਾਡਲ ਵੈਰੀਫਿਕੇਸ਼ਨ**: ਇਹ ਪ੍ਰਕਿਰਿਆ ਹੈ ਜਿਸ ਵਿੱਚ AI ਸਿਸਟਮ ਜਾਂ LLM ਦੇ ਮਾਡਲ ਪੈਰਾਮੀਟਰ ਜਾਂ ਆਰਕੀਟੈਕਚਰ ਦੀ ਸਹੀਤਾ ਅਤੇ ਪੂਰਨਤਾ ਦੀ ਜਾਂਚ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਇਸ ਨਾਲ ਮਾਡਲ ਚੋਰੀ ਨੂੰ ਰੋਕਿਆ ਜਾ ਸਕਦਾ ਹੈ।
- **ਆਉਟਪੁੱਟ ਵੈਰੀਫਿਕੇਸ਼ਨ**: ਇਹ ਪ੍ਰਕਿਰਿਆ ਹੈ ਜਿਸ ਵਿੱਚ AI ਸਿਸਟਮ ਜਾਂ LLM ਦੇ ਆਉਟਪੁੱਟ ਦੀ ਗੁਣਵੱਤਾ ਅਤੇ ਭਰੋਸੇਯੋਗਤਾ ਦੀ ਜਾਂਚ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਇਸ ਨਾਲ ਮਾਲਿਸ਼ੀਅਸ ਮੈਨਿਪੁਲੇਸ਼ਨ ਦੀ ਪਛਾਣ ਅਤੇ ਸਹੀ ਕਰਵਾਈ ਹੋ ਸਕਦੀ ਹੈ।

OpenAI, ਜੋ AI ਸਿਸਟਮਾਂ ਵਿੱਚ ਅਗਵਾਈ ਕਰਦਾ ਹੈ, ਨੇ ਆਪਣੇ ਰੈੱਡ ਟੀਮਿੰਗ ਨੈੱਟਵਰਕ ਇਨਿਸ਼ੀਏਟਿਵ ਦੇ ਹਿੱਸੇ ਵਜੋਂ ਕਈ _ਸੁਰੱਖਿਆ ਮੁਲਾਂਕਣ_ ਸੈੱਟਅੱਪ ਕੀਤੇ ਹਨ, ਜੋ AI ਸਿਸਟਮਾਂ ਦੇ ਆਉਟਪੁੱਟ ਦੀ ਜਾਂਚ ਕਰਕੇ AI ਸੁਰੱਖਿਆ ਵਿੱਚ ਯੋਗਦਾਨ ਪਾਉਣ ਦਾ ਉਦੇਸ਼ ਰੱਖਦੇ ਹਨ।

> ਮੁਲਾਂਕਣ ਸਧਾਰਣ Q&A ਟੈਸਟਾਂ ਤੋਂ ਲੈ ਕੇ ਜਟਿਲ ਸਿਮੂਲੇਸ਼ਨਾਂ ਤੱਕ ਹੋ ਸਕਦੇ ਹਨ। ਕੁਝ ਉਦਾਹਰਣਾਂ ਵਜੋਂ, ਇੱਥੇ OpenAI ਵੱਲੋਂ AI ਵਿਹਾਰਾਂ ਦੀ ਕਈ ਪੱਖਾਂ ਤੋਂ ਮੁਲਾਂਕਣ ਲਈ ਤਿਆਰ ਕੀਤੇ ਗਏ ਕੁਝ ਨਮੂਨੇ ਹਨ:

#### ਮਨਾਉਣਾ

- [MakeMeSay](https://github.com/openai/evals/tree/main/evals/elsuite/make_me_say/readme.md?WT.mc_id=academic-105485-koreyst): ਇੱਕ AI ਸਿਸਟਮ ਦੂਜੇ AI ਸਿਸਟਮ ਨੂੰ ਕਿਸੇ ਗੁਪਤ ਸ਼ਬਦ ਨੂੰ ਕਹਿਣ ਲਈ ਕਿੰਨਾ ਚੰਗਾ ਠੱਗ ਸਕਦਾ ਹੈ?
- [MakeMePay](https://github.com/openai/evals/tree
> AI ਰੈੱਡ ਟੀਮਿੰਗ ਦੀ ਪ੍ਰੈਕਟਿਸ ਹੁਣ ਇੱਕ ਵੱਡੇ ਅਰਥ ਵਿੱਚ ਵਿਕਸਿਤ ਹੋ ਚੁੱਕੀ ਹੈ: ਇਹ ਸਿਰਫ ਸੁਰੱਖਿਆ ਦੀਆਂ ਕਮਜ਼ੋਰੀਆਂ ਦੀ ਜਾਂਚ ਕਰਨ ਤੱਕ ਸੀਮਿਤ ਨਹੀਂ ਰਹਿੰਦੀ, ਬਲਕਿ ਹੋਰ ਸਿਸਟਮ ਫੇਲ੍ਹ ਹੋਣ ਦੀ ਜਾਂਚ ਵੀ ਸ਼ਾਮਲ ਹੈ, ਜਿਵੇਂ ਕਿ ਸੰਭਾਵਿਤ ਨੁਕਸਾਨਦਾਇਕ ਸਮੱਗਰੀ ਦਾ ਉਤਪਾਦਨ। AI ਸਿਸਟਮ ਨਵੇਂ ਖਤਰੇ ਲੈ ਕੇ ਆਉਂਦੇ ਹਨ, ਅਤੇ ਰੈੱਡ ਟੀਮਿੰਗ ਉਹਨਾਂ ਨਵੇਂ ਖਤਰਿਆਂ ਨੂੰ ਸਮਝਣ ਲਈ ਬੁਨਿਆਦੀ ਹੈ, ਜਿਵੇਂ ਕਿ ਪ੍ਰਾਂਪਟ ਇੰਜੈਕਸ਼ਨ ਅਤੇ ਬਿਨਾਂ ਆਧਾਰ ਵਾਲੀ ਸਮੱਗਰੀ ਬਣਾਉਣਾ। - [Microsoft AI Red Team building future of safer AI](https://www.microsoft.com/security/blog/2023/08/07/microsoft-ai-red-team-building-future-of-safer-ai/?WT.mc_id=academic-105485-koreyst)
[![Guidance and resources for red teaming](../../../translated_images/13-AI-red-team.642ed54689d7e8a4d83bdf0635768c4fd8aa41ea539d8e3ffe17514aec4b4824.pa.png)]()

ਹੇਠਾਂ ਕੁਝ ਮੁੱਖ ਜਾਣਕਾਰੀਆਂ ਦਿੱਤੀਆਂ ਗਈਆਂ ਹਨ ਜਿਨ੍ਹਾਂ ਨੇ Microsoft ਦੇ AI Red Team ਪ੍ਰੋਗਰਾਮ ਨੂੰ ਆਕਾਰ ਦਿੱਤਾ ਹੈ।

1. **AI Red Teaming ਦਾ ਵਿਸ਼ਾਲ ਖੇਤਰ:**
   ਹੁਣ AI red teaming ਸੁਰੱਖਿਆ ਅਤੇ Responsible AI (RAI) ਨਤੀਜਿਆਂ ਦੋਹਾਂ ਨੂੰ ਸ਼ਾਮਲ ਕਰਦਾ ਹੈ। ਰਵਾਇਤੀ ਤੌਰ 'ਤੇ, red teaming ਸਿਰਫ ਸੁਰੱਖਿਆ ਪੱਖਾਂ 'ਤੇ ਧਿਆਨ ਦਿੰਦਾ ਸੀ, ਮਾਡਲ ਨੂੰ ਇੱਕ ਵੈਕਟਰ ਵਜੋਂ ਦੇਖਦਾ ਸੀ (ਜਿਵੇਂ ਕਿ ਮੂਲ ਮਾਡਲ ਚੋਰੀ ਕਰਨਾ)। ਪਰ AI ਸਿਸਟਮ ਨਵੇਂ ਸੁਰੱਖਿਆ ਖਤਰੇ ਲਿਆਉਂਦੇ ਹਨ (ਜਿਵੇਂ ਕਿ prompt injection, poisoning), ਜਿਸ ਲਈ ਖਾਸ ਧਿਆਨ ਦੀ ਲੋੜ ਹੁੰਦੀ ਹੈ। ਸੁਰੱਖਿਆ ਤੋਂ ਇਲਾਵਾ, AI red teaming ਨਿਆਂ ਦੇ ਮਸਲਿਆਂ (ਜਿਵੇਂ ਕਿ stereotyping) ਅਤੇ ਨੁਕਸਾਨਦਾਇਕ ਸਮੱਗਰੀ (ਜਿਵੇਂ ਕਿ ਹਿੰਸਾ ਦੀ ਮਹਿਮਾਂਡਾ) ਦੀ ਵੀ ਜਾਂਚ ਕਰਦਾ ਹੈ। ਇਹ ਮੁੱਦੇ ਜਲਦੀ ਪਛਾਣ ਕੇ ਰੱਖਿਆ ਵਿੱਚ ਪਹਿਲ ਦਿੱਤੀ ਜਾ ਸਕਦੀ ਹੈ।
2. **ਦੁਸ਼ਮਣ ਅਤੇ ਸਧਾਰਣ ਗਲਤੀਆਂ:**
   AI red teaming ਗਲਤੀਆਂ ਨੂੰ ਦੁਸ਼ਮਣ ਅਤੇ ਸਧਾਰਣ ਦੋਹਾਂ ਨਜ਼ਰੀਆਂ ਤੋਂ ਵੇਖਦਾ ਹੈ। ਉਦਾਹਰਨ ਵਜੋਂ, ਜਦੋਂ ਅਸੀਂ ਨਵੇਂ Bing ਦੀ red teaming ਕਰਦੇ ਹਾਂ, ਤਾਂ ਅਸੀਂ ਸਿਰਫ ਇਹ ਨਹੀਂ ਵੇਖਦੇ ਕਿ ਦੁਸ਼ਮਣ ਕਿਵੇਂ ਸਿਸਟਮ ਨੂੰ ਨੁਕਸਾਨ ਪਹੁੰਚਾ ਸਕਦੇ ਹਨ, ਬਲਕਿ ਇਹ ਵੀ ਵੇਖਦੇ ਹਾਂ ਕਿ ਆਮ ਯੂਜ਼ਰ ਕਿਵੇਂ ਸਮੱਸਿਆਵਾਂ ਜਾਂ ਨੁਕਸਾਨਦਾਇਕ ਸਮੱਗਰੀ ਦਾ ਸਾਹਮਣਾ ਕਰ ਸਕਦੇ ਹਨ। ਰਵਾਇਤੀ ਸੁਰੱਖਿਆ red teaming ਜਿੱਥੇ ਮੁੱਖ ਤੌਰ 'ਤੇ ਦੁਸ਼ਮਣਾਂ 'ਤੇ ਧਿਆਨ ਦਿੰਦੀ ਹੈ, ਉੱਥੇ AI red teaming ਵੱਖ-ਵੱਖ ਕਿਸਮ ਦੇ ਯੂਜ਼ਰਾਂ ਅਤੇ ਸੰਭਾਵਿਤ ਗਲਤੀਆਂ ਨੂੰ ਵੀ ਧਿਆਨ ਵਿੱਚ ਰੱਖਦੀ ਹੈ।
3. **AI ਸਿਸਟਮਾਂ ਦੀ ਗਤੀਸ਼ੀਲਤਾ:**
   AI ਐਪਲੀਕੇਸ਼ਨ ਲਗਾਤਾਰ ਵਿਕਸਤ ਹੁੰਦੇ ਰਹਿੰਦੇ ਹਨ। ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲ ਐਪਲੀਕੇਸ਼ਨਾਂ ਵਿੱਚ, ਡਿਵੈਲਪਰ ਬਦਲਦੇ ਹੋਏ ਲੋੜਾਂ ਦੇ ਅਨੁਸਾਰ ਅਪਡੇਟ ਕਰਦੇ ਹਨ। ਲਗਾਤਾਰ red teaming ਨਾਲ ਖਤਰੇ ਦੀ ਨਿਗਰਾਨੀ ਅਤੇ ਬਦਲਾਅ ਨੂੰ ਯਕੀਨੀ ਬਣਾਇਆ ਜਾਂਦਾ ਹੈ।

AI red teaming ਸਾਰੀਆਂ ਚੀਜ਼ਾਂ ਨੂੰ ਕਵਰ ਨਹੀਂ ਕਰਦਾ ਅਤੇ ਇਸਨੂੰ ਹੋਰ ਨਿਯੰਤਰਣਾਂ ਜਿਵੇਂ ਕਿ [role-based access control (RBAC)](https://learn.microsoft.com/azure/ai-services/openai/how-to/role-based-access-control?WT.mc_id=academic-105485-koreyst) ਅਤੇ ਵਿਆਪਕ ਡਾਟਾ ਪ੍ਰਬੰਧਨ ਹੱਲਾਂ ਦੇ ਨਾਲ ਮਿਲਾ ਕੇ ਵਰਤਣਾ ਚਾਹੀਦਾ ਹੈ। ਇਹ ਇੱਕ ਸੁਰੱਖਿਆ ਰਣਨੀਤੀ ਨੂੰ ਸਹਾਇਤਾ ਦੇਣ ਲਈ ਹੈ ਜੋ ਸੁਰੱਖਿਅਤ ਅਤੇ ਜ਼ਿੰਮੇਵਾਰ AI ਹੱਲਾਂ ਨੂੰ ਲਾਗੂ ਕਰਦੀ ਹੈ, ਜੋ ਗੋਪਨੀਯਤਾ ਅਤੇ ਸੁਰੱਖਿਆ ਦਾ ਧਿਆਨ ਰੱਖਦੇ ਹੋਏ ਪੱਖਪਾਤ, ਨੁਕਸਾਨਦਾਇਕ ਸਮੱਗਰੀ ਅਤੇ ਗਲਤ ਜਾਣਕਾਰੀ ਨੂੰ ਘਟਾਉਣ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰਦੀ ਹੈ, ਜੋ ਯੂਜ਼ਰਾਂ ਦਾ ਭਰੋਸਾ ਘਟਾ ਸਕਦੀ ਹੈ।

ਇੱਥੇ ਕੁਝ ਵਾਧੂ ਪੜ੍ਹਾਈ ਲਈ ਲਿੰਕ ਦਿੱਤੇ ਗਏ ਹਨ ਜੋ ਤੁਹਾਨੂੰ ਸਮਝਣ ਵਿੱਚ ਮਦਦ ਕਰ ਸਕਦੇ ਹਨ ਕਿ red teaming ਕਿਵੇਂ ਤੁਹਾਡੇ AI ਸਿਸਟਮਾਂ ਵਿੱਚ ਖਤਰਿਆਂ ਦੀ ਪਛਾਣ ਅਤੇ ਘਟਾਅ ਵਿੱਚ ਸਹਾਇਕ ਹੋ ਸਕਦੀ ਹੈ:

- [ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲਾਂ (LLMs) ਅਤੇ ਉਨ੍ਹਾਂ ਦੀਆਂ ਐਪਲੀਕੇਸ਼ਨਾਂ ਲਈ red teaming ਦੀ ਯੋਜਨਾ](https://learn.microsoft.com/azure/ai-services/openai/concepts/red-teaming?WT.mc_id=academic-105485-koreyst)
- [OpenAI Red Teaming Network ਕੀ ਹੈ?](https://openai.com/blog/red-teaming-network?WT.mc_id=academic-105485-koreyst)
- [AI Red Teaming - ਸੁਰੱਖਿਅਤ ਅਤੇ ਜ਼ਿੰਮੇਵਾਰ AI ਹੱਲਾਂ ਬਣਾਉਣ ਲਈ ਇੱਕ ਮੁੱਖ ਅਭਿਆਸ](https://rodtrent.substack.com/p/ai-red-teaming?WT.mc_id=academic-105485-koreyst)
- MITRE ਦਾ [ATLAS (Adversarial Threat Landscape for Artificial-Intelligence Systems)](https://atlas.mitre.org/?WT.mc_id=academic-105485-koreyst), ਜੋ AI ਸਿਸਟਮਾਂ 'ਤੇ ਅਸਲੀ ਦੁਨੀਆ ਦੇ ਹਮਲਿਆਂ ਵਿੱਚ ਦੁਸ਼ਮਣਾਂ ਵੱਲੋਂ ਵਰਤੇ ਜਾਂਦੇ ਤਰੀਕੇ ਅਤੇ ਤਕਨੀਕਾਂ ਦਾ ਗਿਆਨਕੋਸ਼ ਹੈ।

## ਗਿਆਨ ਦੀ ਜਾਂਚ

ਡਾਟਾ ਦੀ ਅਖੰਡਤਾ ਬਣਾਈ ਰੱਖਣ ਅਤੇ ਗਲਤ ਵਰਤੋਂ ਨੂੰ ਰੋਕਣ ਲਈ ਕਿਹੜਾ ਤਰੀਕਾ ਵਧੀਆ ਹੋ ਸਕਦਾ ਹੈ?

1. ਡਾਟਾ ਐਕਸੈਸ ਅਤੇ ਡਾਟਾ ਪ੍ਰਬੰਧਨ ਲਈ ਮਜ਼ਬੂਤ role-based ਨਿਯੰਤਰਣ ਰੱਖੋ  
1. ਡਾਟਾ ਦੀ ਗਲਤ ਪ੍ਰਤੀਨਿਧਤਾ ਜਾਂ ਗਲਤ ਵਰਤੋਂ ਨੂੰ ਰੋਕਣ ਲਈ ਡਾਟਾ ਲੇਬਲਿੰਗ ਲਾਗੂ ਕਰੋ ਅਤੇ ਆਡਿਟ ਕਰੋ  
1. ਯਕੀਨੀ ਬਣਾਓ ਕਿ ਤੁਹਾਡਾ AI ਢਾਂਚਾ ਸਮੱਗਰੀ ਫਿਲਟਰਿੰਗ ਨੂੰ ਸਹਾਰਾ ਦਿੰਦਾ ਹੈ  

ਜਵਾਬ: 1, ਜਦੋਂ ਕਿ ਇਹ ਤਿੰਨੋਂ ਸੁਝਾਅ ਵਧੀਆ ਹਨ, ਪਰ ਯੂਜ਼ਰਾਂ ਨੂੰ ਸਹੀ ਡਾਟਾ ਐਕਸੈਸ ਅਧਿਕਾਰ ਦੇਣਾ LLMs ਵੱਲੋਂ ਵਰਤੇ ਜਾਣ ਵਾਲੇ ਡਾਟਾ ਦੀ ਚਾਲਾਕੀ ਅਤੇ ਗਲਤ ਪ੍ਰਤੀਨਿਧਤਾ ਨੂੰ ਰੋਕਣ ਵਿੱਚ ਬਹੁਤ ਮਦਦਗਾਰ ਸਾਬਤ ਹੁੰਦਾ ਹੈ।

## 🚀 ਚੈਲੈਂਜ

AI ਦੇ ਯੁੱਗ ਵਿੱਚ [ਸੰਵੇਦਨਸ਼ੀਲ ਜਾਣਕਾਰੀ ਨੂੰ ਕਿਵੇਂ ਸੁਰੱਖਿਅਤ ਅਤੇ ਸ਼ਾਸਿਤ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ](https://learn.microsoft.com/training/paths/purview-protect-govern-ai/?WT.mc_id=academic-105485-koreyst) ਬਾਰੇ ਹੋਰ ਪੜ੍ਹੋ।

## ਸ਼ਾਬਾਸ਼, ਆਪਣੀ ਸਿੱਖਿਆ ਜਾਰੀ ਰੱਖੋ

ਇਸ ਪਾਠ ਨੂੰ ਪੂਰਾ ਕਰਨ ਤੋਂ ਬਾਅਦ, ਸਾਡੇ [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) ਨੂੰ ਵੇਖੋ ਤਾਂ ਜੋ ਤੁਸੀਂ ਆਪਣਾ Generative AI ਗਿਆਨ ਹੋਰ ਵਧਾ ਸਕੋ!

ਹੁਣ ਲੈਸਨ 14 ਵੱਲ ਜਾਓ ਜਿੱਥੇ ਅਸੀਂ [Generative AI Application Lifecycle](../14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst) ਬਾਰੇ ਜਾਣਕਾਰੀ ਲਵਾਂਗੇ!

**ਅਸਵੀਕਾਰੋਪਣ**:  
ਇਹ ਦਸਤਾਵੇਜ਼ AI ਅਨੁਵਾਦ ਸੇਵਾ [Co-op Translator](https://github.com/Azure/co-op-translator) ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਅਨੁਵਾਦਿਤ ਕੀਤਾ ਗਿਆ ਹੈ। ਜਦੋਂ ਕਿ ਅਸੀਂ ਸਹੀਅਤ ਲਈ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ, ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਰੱਖੋ ਕਿ ਸਵੈਚਾਲਿਤ ਅਨੁਵਾਦਾਂ ਵਿੱਚ ਗਲਤੀਆਂ ਜਾਂ ਅਸਮਰਥਤਾਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ। ਮੂਲ ਦਸਤਾਵੇਜ਼ ਆਪਣੀ ਮੂਲ ਭਾਸ਼ਾ ਵਿੱਚ ਪ੍ਰਮਾਣਿਕ ਸਰੋਤ ਮੰਨਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ। ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਲਈ, ਪੇਸ਼ੇਵਰ ਮਨੁੱਖੀ ਅਨੁਵਾਦ ਦੀ ਸਿਫਾਰਸ਼ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਇਸ ਅਨੁਵਾਦ ਦੀ ਵਰਤੋਂ ਤੋਂ ਉਤਪੰਨ ਕਿਸੇ ਵੀ ਗਲਤਫਹਿਮੀ ਜਾਂ ਗਲਤ ਵਿਆਖਿਆ ਲਈ ਅਸੀਂ ਜ਼ਿੰਮੇਵਾਰ ਨਹੀਂ ਹਾਂ।