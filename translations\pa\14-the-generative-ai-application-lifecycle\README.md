<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "27a5347a5022d5ef0a72ab029b03526a",
  "translation_date": "2025-07-09T15:51:36+00:00",
  "source_file": "14-the-generative-ai-application-lifecycle/README.md",
  "language_code": "pa"
}
-->
# ਜਨਰੇਟਿਵ AI ਐਪਲੀਕੇਸ਼ਨ ਲਾਈਫਸਾਈਕਲ

ਸਾਰੇ AI ਐਪਲੀਕੇਸ਼ਨਾਂ ਲਈ ਇੱਕ ਮਹੱਤਵਪੂਰਨ ਸਵਾਲ ਹੁੰਦਾ ਹੈ ਕਿ AI ਫੀਚਰਾਂ ਦੀ ਪ੍ਰਸੰਗਿਕਤਾ ਕੀ ਹੈ, ਕਿਉਂਕਿ AI ਇੱਕ ਤੇਜ਼ੀ ਨਾਲ ਵਿਕਸਿਤ ਹੋਣ ਵਾਲਾ ਖੇਤਰ ਹੈ। ਇਹ ਯਕੀਨੀ ਬਣਾਉਣ ਲਈ ਕਿ ਤੁਹਾਡੀ ਐਪਲੀਕੇਸ਼ਨ ਪ੍ਰਸੰਗਿਕ, ਭਰੋਸੇਯੋਗ ਅਤੇ ਮਜ਼ਬੂਤ ਰਹੇ, ਤੁਹਾਨੂੰ ਇਸਦੀ ਲਗਾਤਾਰ ਨਿਗਰਾਨੀ, ਮੁਲਾਂਕਣ ਅਤੇ ਸੁਧਾਰ ਕਰਨਾ ਪੈਂਦਾ ਹੈ। ਇੱਥੇ ਜਨਰੇਟਿਵ AI ਲਾਈਫਸਾਈਕਲ ਦੀ ਭੂਮਿਕਾ ਆਉਂਦੀ ਹੈ।

ਜਨਰੇਟਿਵ AI ਲਾਈਫਸਾਈਕਲ ਇੱਕ ਫਰੇਮਵਰਕ ਹੈ ਜੋ ਤੁਹਾਨੂੰ ਜਨਰੇਟਿਵ AI ਐਪਲੀਕੇਸ਼ਨ ਨੂੰ ਵਿਕਸਿਤ ਕਰਨ, ਤਾਇਨਾਤ ਕਰਨ ਅਤੇ ਸੰਭਾਲਣ ਦੇ ਚਰਣਾਂ ਵਿੱਚ ਮਦਦ ਕਰਦਾ ਹੈ। ਇਹ ਤੁਹਾਡੇ ਲਕੜਾਂ ਨੂੰ ਪਰਿਭਾਸ਼ਿਤ ਕਰਨ, ਪ੍ਰਦਰਸ਼ਨ ਨੂੰ ਮਾਪਣ, ਚੁਣੌਤੀਆਂ ਦੀ ਪਹਚਾਣ ਕਰਨ ਅਤੇ ਹੱਲ ਲਾਗੂ ਕਰਨ ਵਿੱਚ ਸਹਾਇਤਾ ਕਰਦਾ ਹੈ। ਇਹ ਤੁਹਾਡੇ ਐਪਲੀਕੇਸ਼ਨ ਨੂੰ ਤੁਹਾਡੇ ਖੇਤਰ ਅਤੇ ਸਟੇਕਹੋਲਡਰਾਂ ਦੇ ਨੈਤਿਕ ਅਤੇ ਕਾਨੂੰਨੀ ਮਿਆਰਾਂ ਨਾਲ ਸੰਗਤ ਕਰਨ ਵਿੱਚ ਵੀ ਮਦਦ ਕਰਦਾ ਹੈ। ਜਨਰੇਟਿਵ AI ਲਾਈਫਸਾਈਕਲ ਦੀ ਪਾਲਣਾ ਕਰਕੇ, ਤੁਸੀਂ ਯਕੀਨੀ ਬਣਾ ਸਕਦੇ ਹੋ ਕਿ ਤੁਹਾਡੀ ਐਪਲੀਕੇਸ਼ਨ ਹਮੇਸ਼ਾ ਮੁੱਲ ਪ੍ਰਦਾਨ ਕਰ ਰਹੀ ਹੈ ਅਤੇ ਤੁਹਾਡੇ ਉਪਭੋਗਤਾਵਾਂ ਨੂੰ ਸੰਤੁਸ਼ਟ ਕਰ ਰਹੀ ਹੈ।

## ਪਰਿਚਯ

ਇਸ ਅਧਿਆਇ ਵਿੱਚ, ਤੁਸੀਂ:

- MLOps ਤੋਂ LLMOps ਵੱਲ ਪੈਰਾਡਾਈਮ ਸ਼ਿਫਟ ਨੂੰ ਸਮਝੋਗੇ
- LLM ਲਾਈਫਸਾਈਕਲ
- ਲਾਈਫਸਾਈਕਲ ਟੂਲਿੰਗ
- ਲਾਈਫਸਾਈਕਲ ਮੈਟ੍ਰੀਫਿਕੇਸ਼ਨ ਅਤੇ ਮੁਲਾਂਕਣ

## MLOps ਤੋਂ LLMOps ਵੱਲ ਪੈਰਾਡਾਈਮ ਸ਼ਿਫਟ ਨੂੰ ਸਮਝੋ

LLMs ਕ੍ਰਿਤ੍ਰਿਮ ਬੁੱਧੀ ਦੇ ਹਥਿਆਰ ਵਿੱਚ ਇੱਕ ਨਵਾਂ ਸਾਧਨ ਹਨ, ਜੋ ਐਪਲੀਕੇਸ਼ਨਾਂ ਲਈ ਵਿਸ਼ਲੇਸ਼ਣ ਅਤੇ ਜਨਰੇਸ਼ਨ ਕਾਰਜਾਂ ਵਿੱਚ ਬਹੁਤ ਸ਼ਕਤੀਸ਼ਾਲੀ ਹਨ, ਪਰ ਇਸ ਸ਼ਕਤੀ ਦੇ ਕੁਝ ਨਤੀਜੇ ਹਨ ਜੋ AI ਅਤੇ ਕਲਾਸਿਕ ਮਸ਼ੀਨ ਲਰਨਿੰਗ ਕਾਰਜਾਂ ਨੂੰ ਸਧਾਰਨ ਬਣਾਉਣ ਵਿੱਚ ਪ੍ਰਭਾਵ ਪਾਉਂਦੇ ਹਨ।

ਇਸ ਲਈ, ਸਾਨੂੰ ਇਸ ਸਾਧਨ ਨੂੰ ਗਤੀਸ਼ੀਲ ਢੰਗ ਨਾਲ ਅਨੁਕੂਲਿਤ ਕਰਨ ਲਈ ਇੱਕ ਨਵਾਂ ਪੈਰਾਡਾਈਮ ਚਾਹੀਦਾ ਹੈ, ਜਿਸ ਵਿੱਚ ਸਹੀ ਪ੍ਰੇਰਣਾਵਾਂ ਹੋਣ। ਅਸੀਂ ਪੁਰਾਣੀਆਂ AI ਐਪਸ ਨੂੰ "ML Apps" ਅਤੇ ਨਵੀਆਂ AI ਐਪਸ ਨੂੰ "GenAI Apps" ਜਾਂ ਸਿਰਫ "AI Apps" ਵਜੋਂ ਵਰਗੀਕ੍ਰਿਤ ਕਰ ਸਕਦੇ ਹਾਂ, ਜੋ ਉਸ ਸਮੇਂ ਦੀ ਪ੍ਰਮੁੱਖ ਤਕਨਾਲੋਜੀ ਅਤੇ ਤਰੀਕਿਆਂ ਨੂੰ ਦਰਸਾਉਂਦਾ ਹੈ। ਇਹ ਸਾਡੇ ਕਥਾ-ਵਿਚਾਰ ਨੂੰ ਕਈ ਤਰੀਕਿਆਂ ਨਾਲ ਬਦਲਦਾ ਹੈ, ਹੇਠਾਂ ਦਿੱਤੇ ਤੁਲਨਾਤਮਕ ਅੰਸ਼ ਨੂੰ ਦੇਖੋ।

![LLMOps vs. MLOps comparison](../../../translated_images/01-llmops-shift.29bc933cb3bb0080a562e1655c0c719b71a72c3be6252d5c564b7f598987e602.pa.png)

ਧਿਆਨ ਦਿਓ ਕਿ LLMOps ਵਿੱਚ, ਅਸੀਂ ਐਪ ਡਿਵੈਲਪਰਾਂ 'ਤੇ ਜ਼ਿਆਦਾ ਧਿਆਨ ਕੇਂਦਰਿਤ ਕਰਦੇ ਹਾਂ, ਇੰਟਿਗ੍ਰੇਸ਼ਨਾਂ ਨੂੰ ਇੱਕ ਮੁੱਖ ਬਿੰਦੂ ਵਜੋਂ ਵਰਤਦੇ ਹਾਂ, "Models-as-a-Service" ਦੀ ਵਰਤੋਂ ਕਰਦੇ ਹਾਂ ਅਤੇ ਮੈਟ੍ਰਿਕਸ ਲਈ ਹੇਠਾਂ ਦਿੱਤੇ ਬਿੰਦੂਆਂ 'ਤੇ ਸੋਚਦੇ ਹਾਂ।

- ਗੁਣਵੱਤਾ: ਜਵਾਬ ਦੀ ਗੁਣਵੱਤਾ
- ਨੁਕਸਾਨ: ਜ਼ਿੰਮੇਵਾਰ AI
- ਇਮਾਨਦਾਰੀ: ਜਵਾਬ ਦੀ ਸਹੀਤਾ (ਕੀ ਇਹ ਸਹੀ ਹੈ?)
- ਲਾਗਤ: ਹੱਲ ਦਾ ਬਜਟ
- ਲੇਟੈਂਸੀ: ਟੋਕਨ ਜਵਾਬ ਲਈ ਔਸਤ ਸਮਾਂ

## LLM ਲਾਈਫਸਾਈਕਲ

ਸਭ ਤੋਂ ਪਹਿਲਾਂ, ਲਾਈਫਸਾਈਕਲ ਅਤੇ ਤਬਦੀਲੀਆਂ ਨੂੰ ਸਮਝਣ ਲਈ, ਅਗਲੇ ਇਨਫੋਗ੍ਰਾਫਿਕ ਨੂੰ ਦੇਖੋ।

![LLMOps infographic](../../../translated_images/02-llmops.70a942ead05a7645db740f68727d90160cb438ab71f0fb20548bc7fe5cad83ff.pa.png)

ਤੁਸੀਂ ਦੇਖ ਸਕਦੇ ਹੋ ਕਿ ਇਹ MLOps ਦੇ ਆਮ ਲਾਈਫਸਾਈਕਲ ਤੋਂ ਵੱਖਰਾ ਹੈ। LLMs ਲਈ ਕਈ ਨਵੀਆਂ ਲੋੜਾਂ ਹਨ, ਜਿਵੇਂ ਕਿ ਪ੍ਰਾਂਪਟਿੰਗ, ਗੁਣਵੱਤਾ ਸੁਧਾਰਨ ਲਈ ਵੱਖ-ਵੱਖ ਤਕਨੀਕਾਂ (Fine-Tuning, RAG, Meta-Prompts), ਜ਼ਿੰਮੇਵਾਰ AI ਨਾਲ ਵੱਖ-ਵੱਖ ਮੁਲਾਂਕਣ ਅਤੇ ਜ਼ਿੰਮੇਵਾਰੀ, ਅਤੇ ਨਵੇਂ ਮੁਲਾਂਕਣ ਮੈਟ੍ਰਿਕਸ (ਗੁਣਵੱਤਾ, ਨੁਕਸਾਨ, ਇਮਾਨਦਾਰੀ, ਲਾਗਤ ਅਤੇ ਲੇਟੈਂਸੀ)।

ਉਦਾਹਰਨ ਵਜੋਂ, ਦੇਖੋ ਕਿ ਅਸੀਂ ਕਿਵੇਂ ਵਿਚਾਰ ਕਰਦੇ ਹਾਂ। ਵੱਖ-ਵੱਖ LLMs ਨਾਲ ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਸੰਭਾਵਨਾਵਾਂ ਦੀ ਜਾਂਚ ਕਰਦੇ ਹਾਂ ਤਾਂ ਜੋ ਇਹ ਟੈਸਟ ਕੀਤਾ ਜਾ ਸਕੇ ਕਿ ਉਹਨਾਂ ਦੀ ਧਾਰਣਾ ਸਹੀ ਹੋ ਸਕਦੀ ਹੈ।

ਧਿਆਨ ਦਿਓ ਕਿ ਇਹ ਲਕੀਰਵਾਰ ਨਹੀਂ, ਬਲਕਿ ਇੰਟਿਗ੍ਰੇਟਿਡ ਲੂਪ, ਦੁਹਰਾਏ ਜਾਣ ਵਾਲੇ ਅਤੇ ਇੱਕ ਵਿਆਪਕ ਚੱਕਰ ਵਾਲਾ ਹੈ।

ਅਸੀਂ ਇਹ ਕਦਮ ਕਿਵੇਂ ਖੋਜ ਸਕਦੇ ਹਾਂ? ਆਓ ਵੇਰਵੇ ਵਿੱਚ ਜਾਈਏ ਕਿ ਅਸੀਂ ਲਾਈਫਸਾਈਕਲ ਕਿਵੇਂ ਬਣਾਉਂਦੇ ਹਾਂ।

![LLMOps Workflow](../../../translated_images/03-llm-stage-flows.3a1e1c401235a6cfa886ed6ba04aa52a096a545e1bc44fa54d7d5983a7201892.pa.png)

ਇਹ ਕੁਝ ਜਟਿਲ ਲੱਗ ਸਕਦਾ ਹੈ, ਆਓ ਪਹਿਲਾਂ ਤਿੰਨ ਵੱਡੇ ਕਦਮਾਂ 'ਤੇ ਧਿਆਨ ਦਈਏ।

1. ਵਿਚਾਰ/ਖੋਜ: ਖੋਜ, ਇੱਥੇ ਅਸੀਂ ਆਪਣੇ ਕਾਰੋਬਾਰੀ ਜ਼ਰੂਰਤਾਂ ਅਨੁਸਾਰ ਖੋਜ ਕਰ ਸਕਦੇ ਹਾਂ। ਪ੍ਰੋਟੋਟਾਈਪਿੰਗ, ਇੱਕ [PromptFlow](https://microsoft.github.io/promptflow/index.html?WT.mc_id=academic-105485-koreyst) ਬਣਾਉਣਾ ਅਤੇ ਟੈਸਟ ਕਰਨਾ ਕਿ ਕੀ ਇਹ ਸਾਡੀ ਧਾਰਣਾ ਲਈ ਕਾਫ਼ੀ ਪ੍ਰਭਾਵਸ਼ਾਲੀ ਹੈ।
1. ਬਣਾਉਣਾ/ਵਧਾਉਣਾ: ਲਾਗੂ ਕਰਨਾ, ਹੁਣ ਅਸੀਂ ਵੱਡੇ ਡੇਟਾਸੈੱਟਾਂ ਲਈ ਮੁਲਾਂਕਣ ਕਰਨਾ ਸ਼ੁਰੂ ਕਰਦੇ ਹਾਂ, ਤਕਨੀਕਾਂ ਜਿਵੇਂ Fine-tuning ਅਤੇ RAG ਲਾਗੂ ਕਰਦੇ ਹਾਂ, ਤਾਂ ਜੋ ਸਾਡੇ ਹੱਲ ਦੀ ਮਜ਼ਬੂਤੀ ਦੀ ਜਾਂਚ ਕਰੀ ਜਾ ਸਕੇ। ਜੇ ਇਹ ਕੰਮ ਨਹੀਂ ਕਰਦਾ, ਤਾਂ ਇਸਨੂੰ ਦੁਬਾਰਾ ਲਾਗੂ ਕਰਨਾ, ਸਾਡੇ ਫਲੋ ਵਿੱਚ ਨਵੇਂ ਕਦਮ ਸ਼ਾਮਲ ਕਰਨਾ ਜਾਂ ਡੇਟਾ ਨੂੰ ਦੁਬਾਰਾ ਸੰਰਚਿਤ ਕਰਨਾ ਮਦਦਗਾਰ ਹੋ ਸਕਦਾ ਹੈ। ਸਾਡੇ ਫਲੋ ਅਤੇ ਸਕੇਲ ਦੀ ਜਾਂਚ ਕਰਨ ਤੋਂ ਬਾਅਦ, ਜੇ ਇਹ ਕੰਮ ਕਰਦਾ ਹੈ ਅਤੇ ਸਾਡੇ ਮੈਟ੍ਰਿਕਸ ਨੂੰ ਪੂਰਾ ਕਰਦਾ ਹੈ, ਤਾਂ ਇਹ ਅਗਲੇ ਕਦਮ ਲਈ ਤਿਆਰ ਹੈ।
1. ਓਪਰੇਸ਼ਨਲਾਈਜ਼ਿੰਗ: ਇੰਟਿਗ੍ਰੇਸ਼ਨ, ਹੁਣ ਸਾਡੇ ਸਿਸਟਮ ਵਿੱਚ ਮਾਨੀਟਰਿੰਗ ਅਤੇ ਅਲਰਟ ਸਿਸਟਮ ਸ਼ਾਮਲ ਕਰਨਾ, ਤਾਇਨਾਤੀ ਅਤੇ ਐਪਲੀਕੇਸ਼ਨ ਇੰਟਿਗ੍ਰੇਸ਼ਨ।

ਫਿਰ, ਸੁਰੱਖਿਆ, ਅਨੁਕੂਲਤਾ ਅਤੇ ਗਵਰਨੈਂਸ 'ਤੇ ਧਿਆਨ ਕੇਂਦਰਿਤ ਕਰਦਿਆਂ ਪ੍ਰਬੰਧਨ ਦਾ ਵਿਆਪਕ ਚੱਕਰ ਹੁੰਦਾ ਹੈ।

ਵਧਾਈਆਂ, ਹੁਣ ਤੁਹਾਡੀ AI ਐਪ ਤਿਆਰ ਹੈ ਅਤੇ ਓਪਰੇਸ਼ਨਲ ਹੈ। ਹੱਥ-ਅਨੁਭਵ ਲਈ, [Contoso Chat Demo](https://nitya.github.io/contoso-chat/?WT.mc_id=academic-105485-koreys) ਨੂੰ ਦੇਖੋ।

ਹੁਣ, ਅਸੀਂ ਕਿਹੜੇ ਟੂਲ ਵਰਤ ਸਕਦੇ ਹਾਂ?

## ਲਾਈਫਸਾਈਕਲ ਟੂਲਿੰਗ

ਟੂਲਿੰਗ ਲਈ, Microsoft [Azure AI Platform](https://azure.microsoft.com/solutions/ai/?WT.mc_id=academic-105485-koreys) ਅਤੇ [PromptFlow](https://microsoft.github.io/promptflow/index.html?WT.mc_id=academic-105485-koreyst) ਪ੍ਰਦਾਨ ਕਰਦਾ ਹੈ ਜੋ ਤੁਹਾਡੇ ਚੱਕਰ ਨੂੰ ਆਸਾਨ ਬਣਾਉਂਦੇ ਹਨ ਅਤੇ ਲਾਗੂ ਕਰਨ ਲਈ ਤਿਆਰ ਕਰਦੇ ਹਨ।

[Azure AI Platform](https://azure.microsoft.com/solutions/ai/?WT.mc_id=academic-105485-koreys) ਤੁਹਾਨੂੰ [AI Studio](https://ai.azure.com/?WT.mc_id=academic-105485-koreys) ਦੀ ਵਰਤੋਂ ਕਰਨ ਦੀ ਆਗਿਆ ਦਿੰਦਾ ਹੈ। AI Studio ਇੱਕ ਵੈੱਬ ਪੋਰਟਲ ਹੈ ਜੋ ਤੁਹਾਨੂੰ ਮਾਡਲ, ਨਮੂਨੇ ਅਤੇ ਟੂਲ ਖੋਜਣ ਦੀ ਆਗਿਆ ਦਿੰਦਾ ਹੈ। ਇਹ ਤੁਹਾਡੇ ਸਰੋਤਾਂ, UI ਵਿਕਾਸ ਫਲੋਜ਼ ਅਤੇ SDK/CLI ਵਿਕਾਸ ਵਿਕਲਪਾਂ ਦਾ ਪ੍ਰਬੰਧਨ ਕਰਦਾ ਹੈ।

![Azure AI possibilities](../../../translated_images/04-azure-ai-platform.80203baf03a12fa8b166e194928f057074843d1955177baf0f5b53d50d7b6153.pa.png)

Azure AI ਤੁਹਾਨੂੰ ਕਈ ਸਰੋਤਾਂ ਦੀ ਵਰਤੋਂ ਕਰਨ ਦੀ ਆਗਿਆ ਦਿੰਦਾ ਹੈ, ਤਾਂ ਜੋ ਤੁਸੀਂ ਆਪਣੇ ਓਪਰੇਸ਼ਨਾਂ, ਸੇਵਾਵਾਂ, ਪ੍ਰੋਜੈਕਟਾਂ, ਵੈਕਟਰ ਖੋਜ ਅਤੇ ਡੇਟਾਬੇਸ ਦੀਆਂ ਜ਼ਰੂਰਤਾਂ ਦਾ ਪ੍ਰਬੰਧਨ ਕਰ ਸਕੋ।

![LLMOps with Azure AI](../../../translated_images/05-llm-azure-ai-prompt.a5ce85cdbb494bdf95420668e3464aae70d8b22275a744254e941dd5e73ae0d2.pa.png)

PromptFlow ਨਾਲ, ਪ੍ਰੂਫ-ਆਫ-ਕਾਂਸੈਪਟ (POC) ਤੋਂ ਲੈ ਕੇ ਵੱਡੇ ਪੱਧਰ ਦੀਆਂ ਐਪਲੀਕੇਸ਼ਨਾਂ ਤੱਕ ਬਣਾਓ:

- VS Code ਤੋਂ ਐਪ ਡਿਜ਼ਾਈਨ ਅਤੇ ਬਣਾਓ, ਵਿਜ਼ੂਅਲ ਅਤੇ ਫੰਕਸ਼ਨਲ ਟੂਲਾਂ ਨਾਲ
- ਆਪਣੀਆਂ ਐਪਾਂ ਦੀ ਗੁਣਵੱਤਾ ਵਾਲੀ AI ਲਈ ਟੈਸਟ ਅਤੇ ਫਾਈਨ-ਟਿਊਨ ਕਰੋ, ਆਸਾਨੀ ਨਾਲ।
- Azure AI Studio ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਕਲਾਉਡ ਨਾਲ ਇੰਟਿਗ੍ਰੇਟ ਅਤੇ ਦੁਹਰਾਓ, ਤੇਜ਼ ਇੰਟਿਗ੍ਰੇਸ਼ਨ ਲਈ ਪੁਸ਼ ਅਤੇ ਡਿਪਲੋਇ ਕਰੋ।

![LLMOps with PromptFlow](../../../translated_images/06-llm-promptflow.a183eba07a3a7fdf4aa74db92a318b8cbbf4a608671f6b166216358d3203d8d4.pa.png)

## ਵਧੀਆ! ਆਪਣੀ ਸਿੱਖਿਆ ਜਾਰੀ ਰੱਖੋ!

ਸ਼ਾਨਦਾਰ, ਹੁਣ ਸਿੱਖੋ ਕਿ ਅਸੀਂ ਐਪਲੀਕੇਸ਼ਨ ਨੂੰ ਕਿਵੇਂ ਸੰਰਚਿਤ ਕਰਦੇ ਹਾਂ ਤਾਂ ਜੋ ਇਹ ਸੰਕਲਪ [Contoso Chat App](https://nitya.github.io/contoso-chat/?WT.mc_id=academic-105485-koreyst) ਨਾਲ ਵਰਤਿਆ ਜਾ ਸਕੇ, ਅਤੇ ਦੇਖੋ ਕਿ ਕਿਵੇਂ Cloud Advocacy ਉਹਨਾਂ ਸੰਕਲਪਾਂ ਨੂੰ ਡੈਮੋ ਵਿੱਚ ਸ਼ਾਮਲ ਕਰਦਾ ਹੈ। ਹੋਰ ਸਮੱਗਰੀ ਲਈ, ਸਾਡਾ [Ignite breakout session!](https://www.youtube.com/watch?v=DdOylyrTOWg) ਵੇਖੋ।

ਹੁਣ, ਪਾਠ 15 ਨੂੰ ਦੇਖੋ, ਤਾਂ ਜੋ ਤੁਸੀਂ ਸਮਝ ਸਕੋ ਕਿ [Retrieval Augmented Generation ਅਤੇ Vector Databases](../15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst) ਜਨਰੇਟਿਵ AI 'ਤੇ ਕਿਵੇਂ ਪ੍ਰਭਾਵ ਪਾਉਂਦੇ ਹਨ ਅਤੇ ਕਿਵੇਂ ਹੋਰ ਮਨੋਰੰਜਕ ਐਪਲੀਕੇਸ਼ਨ ਬਣਾਈਆਂ ਜਾ ਸਕਦੀਆਂ ਹਨ!

**ਅਸਵੀਕਾਰੋਪਣ**:  
ਇਹ ਦਸਤਾਵੇਜ਼ AI ਅਨੁਵਾਦ ਸੇਵਾ [Co-op Translator](https://github.com/Azure/co-op-translator) ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਅਨੁਵਾਦਿਤ ਕੀਤਾ ਗਿਆ ਹੈ। ਜਦੋਂ ਕਿ ਅਸੀਂ ਸਹੀਤਾ ਲਈ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ, ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਰੱਖੋ ਕਿ ਸਵੈਚਾਲਿਤ ਅਨੁਵਾਦਾਂ ਵਿੱਚ ਗਲਤੀਆਂ ਜਾਂ ਅਸਮਰਥਤਾਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ। ਮੂਲ ਦਸਤਾਵੇਜ਼ ਆਪਣੀ ਮੂਲ ਭਾਸ਼ਾ ਵਿੱਚ ਪ੍ਰਮਾਣਿਕ ਸਰੋਤ ਮੰਨਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ। ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਲਈ, ਪੇਸ਼ੇਵਰ ਮਨੁੱਖੀ ਅਨੁਵਾਦ ਦੀ ਸਿਫਾਰਸ਼ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਅਸੀਂ ਇਸ ਅਨੁਵਾਦ ਦੀ ਵਰਤੋਂ ਤੋਂ ਉਤਪੰਨ ਕਿਸੇ ਵੀ ਗਲਤਫਹਿਮੀ ਜਾਂ ਗਲਤ ਵਿਆਖਿਆ ਲਈ ਜ਼ਿੰਮੇਵਾਰ ਨਹੀਂ ਹਾਂ।