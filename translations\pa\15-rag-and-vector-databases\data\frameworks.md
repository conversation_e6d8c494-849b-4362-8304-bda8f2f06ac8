<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:29:57+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "pa"
}
-->
# ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਫਰੇਮਵਰਕ

ਜਿਵੇਂ ਕਿ ਅਸੀਂ ਪਹਿਲਾਂ ਹੀ ਸਿੱਖਿਆ ਹੈ, ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਨੂੰ ਪ੍ਰਭਾਵਸ਼ਾਲੀ ਤਰੀਕੇ ਨਾਲ ਟ੍ਰੇਨ ਕਰਨ ਲਈ ਸਾਨੂੰ ਦੋ ਕੰਮ ਕਰਨੇ ਪੈਂਦੇ ਹਨ:

* ਟੈਂਸਰਾਂ 'ਤੇ ਕੰਮ ਕਰਨਾ, ਜਿਵੇਂ ਕਿ ਗੁਣਾ ਕਰਨਾ, ਜੋੜਨਾ, ਅਤੇ ਕੁਝ ਫੰਕਸ਼ਨਾਂ ਜਿਵੇਂ sigmoid ਜਾਂ softmax ਦੀ ਗਣਨਾ ਕਰਨੀ
* ਸਾਰੇ ਪ੍ਰਗਟਾਵਾਂ ਦੇ ਗ੍ਰੈਡੀਐਂਟ ਦੀ ਗਣਨਾ ਕਰਨੀ, ਤਾਂ ਜੋ gradient descent optimization ਕਰ ਸਕੀਏ

ਜਦੋਂ ਕਿ `numpy` ਲਾਇਬ੍ਰੇਰੀ ਪਹਿਲਾ ਹਿੱਸਾ ਕਰ ਸਕਦੀ ਹੈ, ਸਾਨੂੰ ਗ੍ਰੈਡੀਐਂਟ ਦੀ ਗਣਨਾ ਕਰਨ ਲਈ ਕੋਈ ਮਕੈਨਿਜ਼ਮ ਚਾਹੀਦਾ ਹੈ। ਸਾਡੇ ਫਰੇਮਵਰਕ ਵਿੱਚ ਜੋ ਅਸੀਂ ਪਿਛਲੇ ਭਾਗ ਵਿੱਚ ਵਿਕਸਿਤ ਕੀਤਾ ਸੀ, ਸਾਨੂੰ ਸਾਰੇ ਡੈਰੀਵੇਟਿਵ ਫੰਕਸ਼ਨਾਂ ਨੂੰ `backward` ਮੈਥਡ ਵਿੱਚ ਹੱਥੋਂ ਲਿਖਣਾ ਪੈਂਦਾ ਸੀ, ਜੋ backpropagation ਕਰਦਾ ਹੈ। ਆਦਰਸ਼ ਤੌਰ 'ਤੇ, ਇੱਕ ਫਰੇਮਵਰਕ ਸਾਨੂੰ *ਕਿਸੇ ਵੀ ਪ੍ਰਗਟਾਵੇ* ਦੇ ਗ੍ਰੈਡੀਐਂਟ ਦੀ ਗਣਨਾ ਕਰਨ ਦਾ ਮੌਕਾ ਦੇਣਾ ਚਾਹੀਦਾ ਹੈ ਜੋ ਅਸੀਂ ਪਰਿਭਾਸ਼ਿਤ ਕਰ ਸਕੀਏ।

ਇੱਕ ਹੋਰ ਜਰੂਰੀ ਗੱਲ ਇਹ ਹੈ ਕਿ ਗਣਨਾਵਾਂ ਨੂੰ GPU ਜਾਂ ਕਿਸੇ ਹੋਰ ਵਿਸ਼ੇਸ਼ ਕੰਪਿਊਟ ਯੂਨਿਟ, ਜਿਵੇਂ TPU, 'ਤੇ ਚਲਾਉਣ ਯੋਗ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ। ਡੀਪ ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਟ੍ਰੇਨਿੰਗ ਵਿੱਚ *ਬਹੁਤ* ਗਣਨਾਵਾਂ ਦੀ ਲੋੜ ਹੁੰਦੀ ਹੈ, ਅਤੇ ਇਹ ਗਣਨਾਵਾਂ ਨੂੰ GPUs 'ਤੇ ਪੈਰਲੇਲ ਕਰਨਾ ਬਹੁਤ ਜਰੂਰੀ ਹੈ।

> ✅ 'parallelize' ਦਾ ਮਤਲਬ ਹੈ ਗਣਨਾਵਾਂ ਨੂੰ ਕਈ ਡਿਵਾਈਸਾਂ ਵਿੱਚ ਵੰਡਣਾ।

ਹੁਣ ਤੱਕ, ਦੋ ਸਭ ਤੋਂ ਪ੍ਰਸਿੱਧ ਨਿਊਰਲ ਫਰੇਮਵਰਕ ਹਨ: TensorFlow ਅਤੇ PyTorch। ਦੋਹਾਂ CPU ਅਤੇ GPU 'ਤੇ ਟੈਂਸਰਾਂ ਨਾਲ ਕੰਮ ਕਰਨ ਲਈ ਨੀਵੀਂ ਸਤਰ ਦਾ API ਦਿੰਦੇ ਹਨ। ਇਸ ਨੀਵੀਂ ਸਤਰ ਦੇ ਉੱਪਰ, ਉੱਚ-ਸਤਰ ਦਾ API ਵੀ ਹੈ, ਜਿਸਨੂੰ ਕ੍ਰਮਵਾਰ Keras ਅਤੇ PyTorch Lightning ਕਿਹਾ ਜਾਂਦਾ ਹੈ।

Low-Level API | TensorFlow| PyTorch
--------------|-------------------------------------|--------------------------------
High-level API| Keras| PyTorch

**Low-level APIs** ਦੋਹਾਂ ਫਰੇਮਵਰਕਾਂ ਵਿੱਚ ਤੁਹਾਨੂੰ **ਕੰਪਿਊਟੇਸ਼ਨਲ ਗ੍ਰਾਫ** ਬਣਾਉਣ ਦੀ ਆਗਿਆ ਦਿੰਦੇ ਹਨ। ਇਹ ਗ੍ਰਾਫ ਦੱਸਦਾ ਹੈ ਕਿ ਦਿੱਤੇ ਗਏ ਇਨਪੁੱਟ ਪੈਰਾਮੀਟਰਾਂ ਨਾਲ ਆਉਟਪੁੱਟ (ਆਮ ਤੌਰ 'ਤੇ ਲਾਸ ਫੰਕਸ਼ਨ) ਕਿਵੇਂ ਗਣਨਾ ਕਰਨੀ ਹੈ, ਅਤੇ ਜੇ GPU ਉਪਲਬਧ ਹੋਵੇ ਤਾਂ ਇਸਨੂੰ ਗਣਨਾ ਲਈ ਭੇਜਿਆ ਜਾ ਸਕਦਾ ਹੈ। ਇਸ ਕੰਪਿਊਟੇਸ਼ਨਲ ਗ੍ਰਾਫ ਨੂੰ ਡਿਫਰੈਂਸ਼ੀਏਟ ਕਰਨ ਅਤੇ ਗ੍ਰੈਡੀਐਂਟ ਦੀ ਗਣਨਾ ਕਰਨ ਲਈ ਫੰਕਸ਼ਨ ਹਨ, ਜੋ ਮਾਡਲ ਪੈਰਾਮੀਟਰਾਂ ਨੂੰ optimize ਕਰਨ ਲਈ ਵਰਤੇ ਜਾ ਸਕਦੇ ਹਨ।

**High-level APIs** ਨਿਊਰਲ ਨੈੱਟਵਰਕਾਂ ਨੂੰ ਇੱਕ **ਲੇਅਰਾਂ ਦੀ ਲੜੀ** ਵਜੋਂ ਦੇਖਦੇ ਹਨ, ਅਤੇ ਜ਼ਿਆਦਾਤਰ ਨਿਊਰਲ ਨੈੱਟਵਰਕਾਂ ਨੂੰ ਬਹੁਤ ਆਸਾਨੀ ਨਾਲ ਬਣਾਉਣ ਵਿੱਚ ਮਦਦ ਕਰਦੇ ਹਨ। ਮਾਡਲ ਟ੍ਰੇਨ ਕਰਨ ਲਈ ਆਮ ਤੌਰ 'ਤੇ ਡੇਟਾ ਤਿਆਰ ਕਰਨਾ ਅਤੇ ਫਿਰ `fit` ਫੰਕਸ਼ਨ ਕਾਲ ਕਰਨਾ ਪੈਂਦਾ ਹੈ।

ਉੱਚ-ਸਤਰ ਦਾ API ਤੁਹਾਨੂੰ ਆਮ ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਬਹੁਤ ਤੇਜ਼ੀ ਨਾਲ ਬਣਾਉਣ ਦੀ ਆਗਿਆ ਦਿੰਦਾ ਹੈ ਬਿਨਾਂ ਬਹੁਤ ਸਾਰੀਆਂ ਵਿਸਥਾਰਾਂ ਦੀ ਚਿੰਤਾ ਕੀਤੇ। ਇਸੇ ਸਮੇਂ, ਨੀਵੀਂ ਸਤਰ ਦਾ API ਟ੍ਰੇਨਿੰਗ ਪ੍ਰਕਿਰਿਆ 'ਤੇ ਜ਼ਿਆਦਾ ਕੰਟਰੋਲ ਦਿੰਦਾ ਹੈ, ਇਸ ਲਈ ਇਹ ਖੋਜ ਵਿੱਚ ਬਹੁਤ ਵਰਤਿਆ ਜਾਂਦਾ ਹੈ, ਜਦੋਂ ਤੁਸੀਂ ਨਵੇਂ ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਆਰਕੀਟੈਕਚਰਾਂ ਨਾਲ ਕੰਮ ਕਰ ਰਹੇ ਹੋ।

ਇਹ ਸਮਝਣਾ ਵੀ ਜਰੂਰੀ ਹੈ ਕਿ ਤੁਸੀਂ ਦੋਹਾਂ APIs ਨੂੰ ਇਕੱਠੇ ਵੀ ਵਰਤ ਸਕਦੇ ਹੋ, ਜਿਵੇਂ ਕਿ ਤੁਸੀਂ ਨੀਵੀਂ ਸਤਰ ਦੇ API ਨਾਲ ਆਪਣਾ ਨੈੱਟਵਰਕ ਲੇਅਰ ਆਰਕੀਟੈਕਚਰ ਵਿਕਸਿਤ ਕਰ ਸਕਦੇ ਹੋ, ਅਤੇ ਫਿਰ ਇਸਨੂੰ ਉੱਚ-ਸਤਰ ਦੇ API ਨਾਲ ਬਣਾਏ ਅਤੇ ਟ੍ਰੇਨ ਕੀਤੇ ਵੱਡੇ ਨੈੱਟਵਰਕ ਵਿੱਚ ਵਰਤ ਸਕਦੇ ਹੋ। ਜਾਂ ਤੁਸੀਂ ਉੱਚ-ਸਤਰ ਦੇ API ਨਾਲ ਲੇਅਰਾਂ ਦੀ ਲੜੀ ਵਜੋਂ ਨੈੱਟਵਰਕ ਪਰਿਭਾਸ਼ਿਤ ਕਰ ਸਕਦੇ ਹੋ, ਅਤੇ ਫਿਰ ਆਪਣਾ ਨੀਵੀਂ ਸਤਰ ਦਾ ਟ੍ਰੇਨਿੰਗ ਲੂਪ ਵਰਤ ਕੇ optimization ਕਰ ਸਕਦੇ ਹੋ। ਦੋਹਾਂ APIs ਇੱਕੋ ਜਿਹੇ ਮੂਲ ਸਿਧਾਂਤਾਂ 'ਤੇ ਆਧਾਰਿਤ ਹਨ ਅਤੇ ਇਹ ਇਕੱਠੇ ਚੰਗੀ ਤਰ੍ਹਾਂ ਕੰਮ ਕਰਨ ਲਈ ਬਣਾਏ ਗਏ ਹਨ।

## ਸਿੱਖਿਆ

ਇਸ ਕੋਰਸ ਵਿੱਚ, ਅਸੀਂ ਜ਼ਿਆਦਾਤਰ ਸਮੱਗਰੀ ਦੋਹਾਂ PyTorch ਅਤੇ TensorFlow ਲਈ ਪ੍ਰਦਾਨ ਕਰਦੇ ਹਾਂ। ਤੁਸੀਂ ਆਪਣਾ ਮਨਪਸੰਦ ਫਰੇਮਵਰਕ ਚੁਣ ਸਕਦੇ ਹੋ ਅਤੇ ਸਿਰਫ਼ ਉਸਦੇ ਨੋਟਬੁੱਕਾਂ ਨੂੰ ਪੜ੍ਹ ਸਕਦੇ ਹੋ। ਜੇ ਤੁਹਾਨੂੰ ਪਤਾ ਨਹੀਂ ਕਿ ਕਿਹੜਾ ਫਰੇਮਵਰਕ ਚੁਣਨਾ ਹੈ, ਤਾਂ ਇੰਟਰਨੈੱਟ 'ਤੇ **PyTorch vs. TensorFlow** ਬਾਰੇ ਕੁਝ ਚਰਚਾ ਪੜ੍ਹੋ। ਤੁਸੀਂ ਦੋਹਾਂ ਫਰੇਮਵਰਕਾਂ ਨੂੰ ਵੇਖ ਕੇ ਵੀ ਬਿਹਤਰ ਸਮਝ ਪ੍ਰਾਪਤ ਕਰ ਸਕਦੇ ਹੋ।

ਜਿੱਥੇ ਸੰਭਵ ਹੋਵੇ, ਅਸੀਂ ਸੌਖਿਆ ਲਈ ਉੱਚ-ਸਤਰ ਦੇ APIs ਦੀ ਵਰਤੋਂ ਕਰਾਂਗੇ। ਪਰ ਅਸੀਂ ਮੰਨਦੇ ਹਾਂ ਕਿ ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਕਿਵੇਂ ਕੰਮ ਕਰਦੇ ਹਨ ਇਹ ਜ਼ਮੀਨ ਤੋਂ ਸਮਝਣਾ ਜਰੂਰੀ ਹੈ, ਇਸ ਲਈ ਸ਼ੁਰੂਆਤ ਵਿੱਚ ਅਸੀਂ ਨੀਵੀਂ ਸਤਰ ਦੇ API ਅਤੇ ਟੈਂਸਰਾਂ ਨਾਲ ਕੰਮ ਕਰਨਾ ਸ਼ੁਰੂ ਕਰਦੇ ਹਾਂ। ਪਰ ਜੇ ਤੁਸੀਂ ਤੇਜ਼ੀ ਨਾਲ ਅੱਗੇ ਵਧਣਾ ਚਾਹੁੰਦੇ ਹੋ ਅਤੇ ਇਹ ਵਿਸਥਾਰ ਸਿੱਖਣ ਵਿੱਚ ਜ਼ਿਆਦਾ ਸਮਾਂ ਨਹੀਂ ਲਗਾਉਣਾ ਚਾਹੁੰਦੇ, ਤਾਂ ਤੁਸੀਂ ਇਹ ਹਿੱਸੇ ਛੱਡ ਕੇ ਸਿੱਧਾ ਉੱਚ-ਸਤਰ ਦੇ API ਨੋਟਬੁੱਕਾਂ ਵਿੱਚ ਜਾ ਸਕਦੇ ਹੋ।

## ✍️ ਅਭਿਆਸ: ਫਰੇਮਵਰਕ

ਆਪਣੀ ਸਿੱਖਿਆ ਨੂੰ ਹੇਠਾਂ ਦਿੱਤੇ ਨੋਟਬੁੱਕਾਂ ਵਿੱਚ ਜਾਰੀ ਰੱਖੋ:

Low-Level API | TensorFlow+Keras Notebook | PyTorch
--------------|-------------------------------------|--------------------------------
High-level API| Keras | *PyTorch Lightning*

ਫਰੇਮਵਰਕਾਂ ਵਿੱਚ ਮਾਹਰ ਹੋਣ ਤੋਂ ਬਾਅਦ, ਆਓ overfitting ਦੇ ਮੂਲ ਵਿਚਾਰ ਨੂੰ ਦੁਹਰਾਈਏ।

# Overfitting

Overfitting ਮਸ਼ੀਨ ਲਰਨਿੰਗ ਵਿੱਚ ਬਹੁਤ ਜਰੂਰੀ ਧਾਰਣਾ ਹੈ, ਅਤੇ ਇਸਨੂੰ ਸਹੀ ਸਮਝਣਾ ਬਹੁਤ ਜਰੂਰੀ ਹੈ!

ਹੇਠਾਂ ਦਿੱਤੇ ਸਮੱਸਿਆ ਨੂੰ ਧਿਆਨ ਨਾਲ ਵੇਖੋ, ਜਿਸ ਵਿੱਚ 5 ਬਿੰਦੂਆਂ (ਜੋ ਗ੍ਰਾਫਾਂ ਵਿੱਚ `x` ਨਾਲ ਦਰਸਾਏ ਗਏ ਹਨ) ਦਾ ਅੰਦਾਜ਼ਾ ਲਗਾਇਆ ਜਾ ਰਿਹਾ ਹੈ:

!linear | overfit
-------------------------|--------------------------
**ਲਿਨੀਅਰ ਮਾਡਲ, 2 ਪੈਰਾਮੀਟਰ** | **ਗੈਰ-ਲਿਨੀਅਰ ਮਾਡਲ, 7 ਪੈਰਾਮੀਟਰ**
ਟ੍ਰੇਨਿੰਗ ਐਰਰ = 5.3 | ਟ੍ਰੇਨਿੰਗ ਐਰਰ = 0
ਵੈਲੀਡੇਸ਼ਨ ਐਰਰ = 5.1 | ਵੈਲੀਡੇਸ਼ਨ ਐਰਰ = 20

* ਖੱਬੇ ਪਾਸੇ, ਅਸੀਂ ਇੱਕ ਵਧੀਆ ਸਿੱਧੀ ਲਾਈਨ ਦਾ ਅੰਦਾਜ਼ਾ ਵੇਖਦੇ ਹਾਂ। ਕਿਉਂਕਿ ਪੈਰਾਮੀਟਰਾਂ ਦੀ ਗਿਣਤੀ ਠੀਕ ਹੈ, ਮਾਡਲ ਬਿੰਦੂਆਂ ਦੇ ਵੰਡ ਦੇ ਪਿੱਛੇ ਦਾ ਮਕਸਦ ਸਹੀ ਸਮਝਦਾ ਹੈ।
* ਸੱਜੇ ਪਾਸੇ, ਮਾਡਲ ਬਹੁਤ ਜ਼ਿਆਦਾ ਸ਼ਕਤੀਸ਼ਾਲੀ ਹੈ। ਕਿਉਂਕਿ ਸਾਡੇ ਕੋਲ ਸਿਰਫ 5 ਬਿੰਦੂ ਹਨ ਅਤੇ ਮਾਡਲ ਦੇ 7 ਪੈਰਾਮੀਟਰ ਹਨ, ਇਹ ਸਾਰੇ ਬਿੰਦੂਆਂ ਵਿੱਚੋਂ ਲੰਘ ਸਕਦਾ ਹੈ, ਜਿਸ ਨਾਲ ਟ੍ਰੇਨਿੰਗ ਐਰਰ 0 ਹੋ ਜਾਂਦਾ ਹੈ। ਪਰ ਇਸ ਨਾਲ ਮਾਡਲ ਡੇਟਾ ਦੇ ਸਹੀ ਪੈਟਰਨ ਨੂੰ ਸਮਝਣ ਤੋਂ ਰੁਕ ਜਾਂਦਾ ਹੈ, ਇਸ ਲਈ ਵੈਲੀਡੇਸ਼ਨ ਐਰਰ ਬਹੁਤ ਵੱਧ ਹੈ।

ਮਾਡਲ ਦੀ ਸ਼ਕਤੀ (ਪੈਰਾਮੀਟਰਾਂ ਦੀ ਗਿਣਤੀ) ਅਤੇ ਟ੍ਰੇਨਿੰਗ ਸੈਂਪਲਾਂ ਦੀ ਗਿਣਤੀ ਵਿਚਕਾਰ ਸਹੀ ਸੰਤੁਲਨ ਬਣਾਉਣਾ ਬਹੁਤ ਜਰੂਰੀ ਹੈ।

## Overfitting ਕਿਉਂ ਹੁੰਦਾ ਹੈ

  * ਟ੍ਰੇਨਿੰਗ ਡੇਟਾ ਕਾਫ਼ੀ ਨਹੀਂ ਹੁੰਦਾ
  * ਮਾਡਲ ਬਹੁਤ ਸ਼ਕਤੀਸ਼ਾਲੀ ਹੁੰਦਾ ਹੈ
  * ਇਨਪੁੱਟ ਡੇਟਾ ਵਿੱਚ ਬਹੁਤ ਜ਼ਿਆਦਾ ਸ਼ੋਰ ਹੁੰਦਾ ਹੈ

## Overfitting ਕਿਵੇਂ ਪਤਾ ਲਗਾਈਏ

ਜਿਵੇਂ ਕਿ ਉੱਪਰ ਦਿੱਤੇ ਗ੍ਰਾਫ ਤੋਂ ਪਤਾ ਲੱਗਦਾ ਹੈ, overfitting ਨੂੰ ਬਹੁਤ ਘੱਟ ਟ੍ਰੇਨਿੰਗ ਐਰਰ ਅਤੇ ਉੱਚ ਵੈਲੀਡੇਸ਼ਨ ਐਰਰ ਨਾਲ ਪਛਾਣਿਆ ਜਾ ਸਕਦਾ ਹੈ। ਆਮ ਤੌਰ 'ਤੇ ਟ੍ਰੇਨਿੰਗ ਦੌਰਾਨ ਅਸੀਂ ਦੋਹਾਂ ਟ੍ਰੇਨਿੰਗ ਅਤੇ ਵੈਲੀਡੇਸ਼ਨ ਐਰਰਾਂ ਨੂੰ ਘਟਦੇ ਵੇਖਦੇ ਹਾਂ, ਪਰ ਕਿਸੇ ਸਮੇਂ ਵੈਲੀਡੇਸ਼ਨ ਐਰਰ ਘਟਣਾ ਰੋਕ ਕੇ ਵਧਣਾ ਸ਼ੁਰੂ ਕਰ ਸਕਦਾ ਹੈ। ਇਹ overfitting ਦਾ ਸੰਕੇਤ ਹੁੰਦਾ ਹੈ, ਅਤੇ ਇਹ ਦਰਸਾਉਂਦਾ ਹੈ ਕਿ ਸਾਨੂੰ ਇਸ ਸਮੇਂ ਟ੍ਰੇਨਿੰਗ ਰੋਕ ਦੇਣੀ ਚਾਹੀਦੀ ਹੈ (ਜਾਂ ਘੱਟੋ-ਘੱਟ ਮਾਡਲ ਦਾ ਸਨੈਪਸ਼ਾਟ ਲੈਣਾ ਚਾਹੀਦਾ ਹੈ)।

overfitting

## Overfitting ਨੂੰ ਕਿਵੇਂ ਰੋਕੀਏ

ਜੇ ਤੁਸੀਂ ਵੇਖਦੇ ਹੋ ਕਿ overfitting ਹੋ ਰਿਹਾ ਹੈ, ਤਾਂ ਤੁਸੀਂ ਹੇਠਾਂ ਵਿੱਚੋਂ ਕੋਈ ਇੱਕ ਕਰ ਸਕਦੇ ਹੋ:

 * ਟ੍ਰੇਨਿੰਗ ਡੇਟਾ ਦੀ ਮਾਤਰਾ ਵਧਾਓ
 * ਮਾਡਲ ਦੀ ਜਟਿਲਤਾ ਘਟਾਓ
 * ਕੁਝ regularization ਤਕਨੀਕ ਵਰਤੋ, ਜਿਵੇਂ Dropout, ਜਿਸ ਬਾਰੇ ਅਸੀਂ ਬਾਅਦ ਵਿੱਚ ਵਿਚਾਰ ਕਰਾਂਗੇ।

## Overfitting ਅਤੇ Bias-Variance Tradeoff

Overfitting ਅਸਲ ਵਿੱਚ ਸਾਂਖਿਆਕੀ ਵਿੱਚ Bias-Variance Tradeoff ਨਾਮਕ ਇੱਕ ਵੱਡੇ ਸਮੱਸਿਆ ਦਾ ਹਿੱਸਾ ਹੈ। ਜੇ ਅਸੀਂ ਆਪਣੇ ਮਾਡਲ ਵਿੱਚ ਸੰਭਾਵਿਤ ਗਲਤੀਆਂ ਦੇ ਸਰੋਤਾਂ ਨੂੰ ਵੇਖੀਏ, ਤਾਂ ਸਾਨੂੰ ਦੋ ਕਿਸਮ ਦੀਆਂ ਗਲਤੀਆਂ ਮਿਲਦੀਆਂ ਹਨ:

* **Bias errors** ਸਾਡੇ algorithm ਵੱਲੋਂ ਟ੍ਰੇਨਿੰਗ ਡੇਟਾ ਦੇ ਸੰਬੰਧ ਨੂੰ ਸਹੀ ਤਰੀਕੇ ਨਾਲ ਕੈਪਚਰ ਨਾ ਕਰ ਸਕਣ ਕਾਰਨ ਹੁੰਦੀਆਂ ਹਨ। ਇਹ ਇਸ ਗੱਲ ਦਾ ਨਤੀਜਾ ਹੋ ਸਕਦਾ ਹੈ ਕਿ ਸਾਡਾ ਮਾਡਲ ਕਾਫ਼ੀ ਸ਼ਕਤੀਸ਼ਾਲੀ ਨਹੀਂ ਹੈ (**underfitting**).
* **Variance errors** ਉਹ ਹਨ ਜੋ ਮਾਡਲ ਇਨਪੁੱਟ ਡੇਟਾ ਵਿੱਚ ਸ਼ੋਰ ਨੂੰ ਅਸਲ ਸੰਬੰਧ ਦੀ ਥਾਂ ਅੰਦਾਜ਼ਾ ਲਗਾਉਂਦਾ ਹੈ (**overfitting**).

ਟ੍ਰੇਨਿੰਗ ਦੌਰਾਨ, bias error ਘਟਦਾ ਹੈ (ਜਿਵੇਂ ਸਾਡਾ ਮਾਡਲ ਡੇਟਾ ਨੂੰ ਅੰਦਾਜ਼ਾ ਲਗਾਉਣਾ ਸਿੱਖਦਾ ਹੈ), ਅਤੇ variance error ਵੱਧਦਾ ਹੈ। ਇਹ ਜਰੂਰੀ ਹੈ ਕਿ ਟ੍ਰੇਨਿੰਗ ਨੂੰ ਰੋਕਿਆ ਜਾਵੇ - ਚਾਹੇ ਹੱਥੋਂ (ਜਦੋਂ ਅਸੀਂ overfitting ਪਛਾਣੀਏ) ਜਾਂ ਆਪਣੇ ਆਪ (regularization ਲਾ ਕੇ) - ਤਾਂ ਜੋ overfitting ਤੋਂ ਬਚਿਆ ਜਾ ਸਕੇ।

## ਨਤੀਜਾ

ਇਸ ਪਾਠ ਵਿੱਚ, ਤੁਸੀਂ ਦੋ ਸਭ ਤੋਂ ਪ੍ਰਸਿੱਧ AI ਫਰੇਮਵਰਕਾਂ, TensorFlow ਅਤੇ PyTorch, ਦੇ ਵੱਖ-ਵੱਖ APIs ਵਿੱਚ ਫਰਕ ਬਾਰੇ ਸਿੱਖਿਆ। ਇਸਦੇ ਨਾਲ-ਨਾਲ, ਤੁਸੀਂ ਇੱਕ ਬਹੁਤ ਜਰੂਰੀ ਵਿਸ਼ਾ, overfitting, ਬਾਰੇ ਵੀ ਜਾਣਿਆ।

## 🚀 ਚੈਲੈਂਜ

ਸੰਬੰਧਿਤ ਨੋਟਬੁੱਕਾਂ ਵਿੱਚ, ਤੁਹਾਨੂੰ ਹੇਠਾਂ 'tasks' ਮਿਲਣਗੇ; ਨੋਟਬੁੱਕਾਂ ਨੂੰ ਪੜ੍ਹੋ ਅਤੇ ਟਾਸਕ ਪੂਰੇ ਕਰੋ।

## ਸਮੀਖਿਆ ਅਤੇ ਸਵੈ-ਅਧਿਐਨ

ਹੇਠਾਂ ਦਿੱਤੇ ਵਿਸ਼ਿਆਂ 'ਤੇ ਕੁਝ ਖੋਜ ਕਰੋ:

- TensorFlow
- PyTorch
- Overfitting

ਆਪਣੇ ਆਪ ਨੂੰ ਇਹ ਸਵਾਲ ਪੁੱਛੋ:

- TensorFlow ਅਤੇ PyTorch ਵਿੱਚ ਕੀ ਫਰਕ ਹੈ?
- Overfitting ਅਤੇ underfitting ਵਿੱਚ ਕੀ ਅੰਤਰ ਹੈ?

## ਅਸਾਈਨਮੈਂਟ

ਇਸ ਲੈਬ ਵਿੱਚ, ਤੁਹਾਨੂੰ PyTorch ਜਾਂ TensorFlow ਦੀ ਵਰਤੋਂ ਕਰਕੇ single- ਅਤੇ multi-layered fully-connected ਨੈੱਟਵਰਕਾਂ ਨਾਲ ਦੋ ਕਲਾਸੀਫਿਕੇਸ਼ਨ ਸਮੱਸਿਆਵਾਂ ਹੱਲ ਕਰਨ ਲਈ ਕਿਹਾ ਗਿਆ ਹੈ।

**ਅਸਵੀਕਾਰੋਪਣ**:  
ਇਹ ਦਸਤਾਵੇਜ਼ AI ਅਨੁਵਾਦ ਸੇਵਾ [Co-op Translator](https://github.com/Azure/co-op-translator) ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਅਨੁਵਾਦਿਤ ਕੀਤਾ ਗਿਆ ਹੈ। ਜਦੋਂ ਕਿ ਅਸੀਂ ਸਹੀਅਤ ਲਈ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ, ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਰੱਖੋ ਕਿ ਸਵੈਚਾਲਿਤ ਅਨੁਵਾਦਾਂ ਵਿੱਚ ਗਲਤੀਆਂ ਜਾਂ ਅਸਮਰਥਤਾਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ। ਮੂਲ ਦਸਤਾਵੇਜ਼ ਆਪਣੀ ਮੂਲ ਭਾਸ਼ਾ ਵਿੱਚ ਪ੍ਰਮਾਣਿਕ ਸਰੋਤ ਮੰਨਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ। ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਲਈ, ਪੇਸ਼ੇਵਰ ਮਨੁੱਖੀ ਅਨੁਵਾਦ ਦੀ ਸਿਫਾਰਸ਼ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਇਸ ਅਨੁਵਾਦ ਦੀ ਵਰਤੋਂ ਤੋਂ ਉਤਪੰਨ ਕਿਸੇ ਵੀ ਗਲਤਫਹਮੀ ਜਾਂ ਗਲਤ ਵਿਆਖਿਆ ਲਈ ਅਸੀਂ ਜ਼ਿੰਮੇਵਾਰ ਨਹੀਂ ਹਾਂ।