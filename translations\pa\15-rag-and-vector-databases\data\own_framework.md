<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:44:44+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "pa"
}
-->
# ਨਿਊਰਲ ਨੈੱਟਵਰਕਸ ਦਾ ਪਰਿਚਯ। ਮਲਟੀ-ਲੇਅਰਡ ਪਰਸੈਪਟਰਾਨ

ਪਿਛਲੇ ਭਾਗ ਵਿੱਚ, ਤੁਸੀਂ ਸਭ ਤੋਂ ਸਧਾਰਣ ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਮਾਡਲ ਬਾਰੇ ਸਿੱਖਿਆ - ਇੱਕ-ਪੜ੍ਹਾਈ ਵਾਲਾ ਪਰਸੈਪਟਰਾਨ, ਜੋ ਕਿ ਇੱਕ ਰੇਖੀ ਦੋ-ਕਲਾਸ ਵਰਗੀਕਰਨ ਮਾਡਲ ਹੈ।

ਇਸ ਭਾਗ ਵਿੱਚ ਅਸੀਂ ਇਸ ਮਾਡਲ ਨੂੰ ਇੱਕ ਹੋਰ ਲਚਕੀਲੇ ਫਰੇਮਵਰਕ ਵਿੱਚ ਵਧਾਵਾਂਗੇ, ਜੋ ਸਾਨੂੰ ਇਹ ਕਰਨ ਦੀ ਆਗਿਆ ਦੇਵੇਗਾ:

* ਦੋ-ਕਲਾਸ ਦੇ ਨਾਲ-ਨਾਲ **ਮਲਟੀ-ਕਲਾਸ ਵਰਗੀਕਰਨ** ਕਰਨਾ
* ਵਰਗੀਕਰਨ ਦੇ ਨਾਲ-ਨਾਲ **ਰੇਗ੍ਰੈਸ਼ਨ ਸਮੱਸਿਆਵਾਂ** ਦਾ ਹੱਲ ਕਰਨਾ
* ਉਹ ਕਲਾਸਾਂ ਵੱਖ ਕਰਨਾ ਜੋ ਰੇਖੀ ਤੌਰ 'ਤੇ ਵੱਖ ਨਹੀਂ ਕੀਤੀਆਂ ਜਾ ਸਕਦੀਆਂ

ਅਸੀਂ ਆਪਣਾ ਖੁਦ ਦਾ ਮੋਡੀਊਲਰ ਫਰੇਮਵਰਕ ਵੀ ਪਾਇਥਨ ਵਿੱਚ ਵਿਕਸਿਤ ਕਰਾਂਗੇ ਜੋ ਸਾਨੂੰ ਵੱਖ-ਵੱਖ ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਆਰਕੀਟੈਕਚਰ ਬਣਾਉਣ ਦੀ ਆਗਿਆ ਦੇਵੇਗਾ।

## ਮਸ਼ੀਨ ਲਰਨਿੰਗ ਦੀ ਫਾਰਮਲਾਈਜ਼ੇਸ਼ਨ

ਆਓ ਮਸ਼ੀਨ ਲਰਨਿੰਗ ਸਮੱਸਿਆ ਨੂੰ ਫਾਰਮਲ ਕਰਨਾ ਸ਼ੁਰੂ ਕਰੀਏ। ਮੰਨ ਲਓ ਸਾਡੇ ਕੋਲ ਟ੍ਰੇਨਿੰਗ ਡੇਟਾਸੈੱਟ **X** ਹੈ ਜਿਸਦੇ ਲੇਬਲ **Y** ਹਨ, ਅਤੇ ਸਾਨੂੰ ਇੱਕ ਮਾਡਲ *f* ਬਣਾਉਣਾ ਹੈ ਜੋ ਸਭ ਤੋਂ ਸਹੀ ਅਨੁਮਾਨ ਲਗਾਏ। ਅਨੁਮਾਨ ਦੀ ਗੁਣਵੱਤਾ ਨੂੰ **ਲਾਸ ਫੰਕਸ਼ਨ** ℒ ਨਾਲ ਮਾਪਿਆ ਜਾਂਦਾ ਹੈ। ਹੇਠਾਂ ਦਿੱਤੇ ਲਾਸ ਫੰਕਸ਼ਨ ਅਕਸਰ ਵਰਤੇ ਜਾਂਦੇ ਹਨ:

* ਜਦੋਂ ਸਾਨੂੰ ਇੱਕ ਨੰਬਰ ਦੀ ਭਵਿੱਖਬਾਣੀ ਕਰਨੀ ਹੋਵੇ (ਰੇਗ੍ਰੈਸ਼ਨ ਸਮੱਸਿਆ), ਤਾਂ ਅਸੀਂ **ਐਬਸੋਲਿਊਟ ਐਰਰ** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>| ਜਾਂ **ਸਕਵੇਅਰਡ ਐਰਰ** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup> ਵਰਤ ਸਕਦੇ ਹਾਂ
* ਵਰਗੀਕਰਨ ਲਈ, ਅਸੀਂ **0-1 ਲਾਸ** (ਜੋ ਮੂਲ ਰੂਪ ਵਿੱਚ ਮਾਡਲ ਦੀ **ਸਹੀਤਾ** ਦੇ ਬਰਾਬਰ ਹੈ) ਜਾਂ **ਲੋਜਿਸਟਿਕ ਲਾਸ** ਵਰਤਦੇ ਹਾਂ।

ਇੱਕ-ਪੜ੍ਹਾਈ ਵਾਲੇ ਪਰਸੈਪਟਰਾਨ ਲਈ, ਫੰਕਸ਼ਨ *f* ਨੂੰ ਇੱਕ ਰੇਖੀ ਫੰਕਸ਼ਨ *f(x)=wx+b* ਵਜੋਂ ਪਰਿਭਾਸ਼ਿਤ ਕੀਤਾ ਗਿਆ ਸੀ (ਇੱਥੇ *w* ਭਾਰ ਮੈਟ੍ਰਿਕਸ ਹੈ, *x* ਇਨਪੁੱਟ ਫੀਚਰਾਂ ਦਾ ਵੇਕਟਰ ਹੈ, ਅਤੇ *b* ਬਾਇਅਸ ਵੇਕਟਰ ਹੈ)। ਵੱਖ-ਵੱਖ ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਆਰਕੀਟੈਕਚਰ ਲਈ, ਇਹ ਫੰਕਸ਼ਨ ਹੋਰ ਜਟਿਲ ਰੂਪ ਲੈ ਸਕਦਾ ਹੈ।

> ਵਰਗੀਕਰਨ ਦੇ ਮਾਮਲੇ ਵਿੱਚ, ਅਕਸਰ ਚਾਹੀਦਾ ਹੈ ਕਿ ਨੈੱਟਵਰਕ ਆਉਟਪੁੱਟ ਵਜੋਂ ਸੰਬੰਧਿਤ ਕਲਾਸਾਂ ਦੀ ਸੰਭਾਵਨਾਵਾਂ ਮਿਲਣ। ਕਿਸੇ ਵੀ ਨੰਬਰ ਨੂੰ ਸੰਭਾਵਨਾ ਵਿੱਚ ਬਦਲਣ ਲਈ (ਜਿਵੇਂ ਆਉਟਪੁੱਟ ਨੂੰ ਨਾਰਮਲਾਈਜ਼ ਕਰਨ ਲਈ), ਅਸੀਂ ਅਕਸਰ **softmax** ਫੰਕਸ਼ਨ σ ਵਰਤਦੇ ਹਾਂ, ਅਤੇ ਫੰਕਸ਼ਨ *f* ਬਣ ਜਾਂਦਾ ਹੈ *f(x)=σ(wx+b)*

ਉਪਰ ਦਿੱਤੇ *f* ਦੀ ਪਰਿਭਾਸ਼ਾ ਵਿੱਚ, *w* ਅਤੇ *b* ਨੂੰ **ਪੈਰਾਮੀਟਰ** θ=⟨*w,b*⟩ ਕਿਹਾ ਜਾਂਦਾ ਹੈ। ਦਿੱਤੇ ਡੇਟਾਸੈੱਟ ⟨**X**,**Y**⟩ ਦੇ ਆਧਾਰ 'ਤੇ, ਅਸੀਂ ਪੈਰਾਮੀਟਰ θ ਦੇ ਫੰਕਸ਼ਨ ਵਜੋਂ ਪੂਰੇ ਡੇਟਾਸੈੱਟ 'ਤੇ ਕੁੱਲ ਗਲਤੀ ਗਣਨਾ ਕਰ ਸਕਦੇ ਹਾਂ।

> ✅ **ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਟ੍ਰੇਨਿੰਗ ਦਾ ਮਕਸਦ ਪੈਰਾਮੀਟਰ θ ਨੂੰ ਬਦਲ ਕੇ ਗਲਤੀ ਨੂੰ ਘਟਾਉਣਾ ਹੈ**

## ਗ੍ਰੇਡੀਐਂਟ ਡੀਸੈਂਟ ਓਪਟੀਮਾਈਜ਼ੇਸ਼ਨ

ਫੰਕਸ਼ਨ ਓਪਟੀਮਾਈਜ਼ੇਸ਼ਨ ਦੀ ਇੱਕ ਮਸ਼ਹੂਰ ਵਿਧੀ ਹੈ ਜਿਸਨੂੰ **ਗ੍ਰੇਡੀਐਂਟ ਡੀਸੈਂਟ** ਕਹਿੰਦੇ ਹਨ। ਵਿਚਾਰ ਇਹ ਹੈ ਕਿ ਅਸੀਂ ਲਾਸ ਫੰਕਸ਼ਨ ਦਾ ਪੈਰਾਮੀਟਰਾਂ ਦੇ ਸੰਦਰਭ ਵਿੱਚ ਡੈਰੀਵੇਟਿਵ (ਬਹੁ-ਆਯਾਮੀ ਮਾਮਲੇ ਵਿੱਚ ਇਸਨੂੰ **ਗ੍ਰੇਡੀਐਂਟ** ਕਹਿੰਦੇ ਹਨ) ਗਣਨਾ ਕਰ ਸਕਦੇ ਹਾਂ, ਅਤੇ ਪੈਰਾਮੀਟਰਾਂ ਨੂੰ ਇਸ ਤਰ੍ਹਾਂ ਬਦਲ ਸਕਦੇ ਹਾਂ ਕਿ ਗਲਤੀ ਘਟੇ। ਇਸਨੂੰ ਹੇਠਾਂ ਦਿੱਤੇ ਤਰੀਕੇ ਨਾਲ ਫਾਰਮਲ ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ:

* ਪੈਰਾਮੀਟਰਾਂ ਨੂੰ ਕੁਝ ਰੈਂਡਮ ਮੁੱਲਾਂ ਨਾਲ ਸ਼ੁਰੂ ਕਰੋ w<sup>(0)</sup>, b<sup>(0)</sup>
* ਹੇਠਾਂ ਦਿੱਤਾ ਕਦਮ ਕਈ ਵਾਰੀ ਦੁਹਰਾਓ:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

ਟ੍ਰੇਨਿੰਗ ਦੌਰਾਨ, ਓਪਟੀਮਾਈਜ਼ੇਸ਼ਨ ਕਦਮ ਪੂਰੇ ਡੇਟਾਸੈੱਟ ਨੂੰ ਧਿਆਨ ਵਿੱਚ ਰੱਖ ਕੇ ਗਣਨਾ ਕੀਤੇ ਜਾਣੇ ਚਾਹੀਦੇ ਹਨ (ਯਾਦ ਰੱਖੋ ਕਿ ਲਾਸ ਸਾਰੇ ਟ੍ਰੇਨਿੰਗ ਸੈਂਪਲਾਂ ਦੇ ਜੋੜ ਵਜੋਂ ਗਣਨਾ ਹੁੰਦੀ ਹੈ)। ਪਰ ਅਸਲ ਜ਼ਿੰਦਗੀ ਵਿੱਚ ਅਸੀਂ ਡੇਟਾ ਦੇ ਛੋਟੇ ਹਿੱਸੇ ਲੈਂਦੇ ਹਾਂ ਜਿਨ੍ਹਾਂ ਨੂੰ **ਮਿਨੀਬੈਚ** ਕਹਿੰਦੇ ਹਨ, ਅਤੇ ਡੇਟਾ ਦੇ ਇਸ ਹਿੱਸੇ ਦੇ ਆਧਾਰ 'ਤੇ ਗ੍ਰੇਡੀਐਂਟ ਗਣਨਾ ਕਰਦੇ ਹਾਂ। ਕਿਉਂਕਿ ਹਰ ਵਾਰੀ ਹਿੱਸਾ ਰੈਂਡਮ ਤੌਰ 'ਤੇ ਚੁਣਿਆ ਜਾਂਦਾ ਹੈ, ਇਸ ਤਰੀਕੇ ਨੂੰ **ਸਟੋਕਾਸਟਿਕ ਗ੍ਰੇਡੀਐਂਟ ਡੀਸੈਂਟ** (SGD) ਕਹਿੰਦੇ ਹਨ।

## ਮਲਟੀ-ਲੇਅਰਡ ਪਰਸੈਪਟਰਾਨ ਅਤੇ ਬੈਕਪ੍ਰੋਪਾਗੇਸ਼ਨ

ਜਿਵੇਂ ਅਸੀਂ ਉਪਰ ਵੇਖਿਆ, ਇੱਕ-ਪੜ੍ਹਾਈ ਵਾਲਾ ਨੈੱਟਵਰਕ ਰੇਖੀ ਤੌਰ 'ਤੇ ਵੱਖ ਹੋਣ ਵਾਲੀਆਂ ਕਲਾਸਾਂ ਨੂੰ ਵਰਗੀਕ੍ਰਿਤ ਕਰ ਸਕਦਾ ਹੈ। ਇੱਕ ਹੋਰ ਸੰਪੰਨ ਮਾਡਲ ਬਣਾਉਣ ਲਈ, ਅਸੀਂ ਨੈੱਟਵਰਕ ਦੀਆਂ ਕਈ ਪਰਤਾਂ ਨੂੰ ਜੋੜ ਸਕਦੇ ਹਾਂ। ਗਣਿਤਕ ਰੂਪ ਵਿੱਚ ਇਸਦਾ ਮਤਲਬ ਹੈ ਕਿ ਫੰਕਸ਼ਨ *f* ਹੋਰ ਜਟਿਲ ਰੂਪ ਲਵੇਗਾ ਅਤੇ ਕਈ ਕਦਮਾਂ ਵਿੱਚ ਗਣਨਾ ਕੀਤਾ ਜਾਵੇਗਾ:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

ਇੱਥੇ, α ਇੱਕ **ਗੈਰ-ਰੇਖੀ ਐਕਟੀਵੇਸ਼ਨ ਫੰਕਸ਼ਨ** ਹੈ, σ softmax ਫੰਕਸ਼ਨ ਹੈ, ਅਤੇ ਪੈਰਾਮੀਟਰ θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*> ਹਨ।

ਗ੍ਰੇਡੀਐਂਟ ਡੀਸੈਂਟ ਅਲਗੋਰਿਦਮ ਉਹੀ ਰਹੇਗਾ, ਪਰ ਗ੍ਰੇਡੀਐਂਟ ਗਣਨਾ ਕਰਨਾ ਥੋੜ੍ਹਾ ਮੁਸ਼ਕਲ ਹੋ ਜਾਵੇਗਾ। ਚੇਨ ਡਿਫਰੈਂਸ਼ੀਏਸ਼ਨ ਨਿਯਮ ਦੇ ਅਨੁਸਾਰ, ਅਸੀਂ ਡੈਰੀਵੇਟਿਵ ਗਣਨਾ ਕਰ ਸਕਦੇ ਹਾਂ:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ ਚੇਨ ਡਿਫਰੈਂਸ਼ੀਏਸ਼ਨ ਨਿਯਮ ਲਾਸ ਫੰਕਸ਼ਨ ਦੇ ਪੈਰਾਮੀਟਰਾਂ ਦੇ ਸੰਦਰਭ ਵਿੱਚ ਡੈਰੀਵੇਟਿਵ ਗਣਨਾ ਕਰਨ ਲਈ ਵਰਤਿਆ ਜਾਂਦਾ ਹੈ।

ਧਿਆਨ ਦਿਓ ਕਿ ਸਾਰੇ ਇਨ੍ਹਾਂ ਪ੍ਰਗਟਾਵਾਂ ਦਾ ਖੱਬਾ-ਸਭ ਤੋਂ ਹਿੱਸਾ ਇੱਕੋ ਜਿਹਾ ਹੈ, ਇਸ ਲਈ ਅਸੀਂ ਲਾਸ ਫੰਕਸ਼ਨ ਤੋਂ ਸ਼ੁਰੂ ਕਰਕੇ "ਪਿੱਛੇ" ਵੱਲ ਕੈਲਕੁਲੇਸ਼ਨ ਕਰ ਸਕਦੇ ਹਾਂ। ਇਸ ਤਰ੍ਹਾਂ ਮਲਟੀ-ਲੇਅਰਡ ਪਰਸੈਪਟਰਾਨ ਦੀ ਟ੍ਰੇਨਿੰਗ ਦੀ ਵਿਧੀ ਨੂੰ **ਬੈਕਪ੍ਰੋਪਾਗੇਸ਼ਨ** ਜਾਂ 'ਬੈਕਪ੍ਰੋਪ' ਕਹਿੰਦੇ ਹਨ।

> TODO: image citation

> ✅ ਅਸੀਂ ਆਪਣੇ ਨੋਟਬੁੱਕ ਉਦਾਹਰਨ ਵਿੱਚ ਬੈਕਪ੍ਰੋਪ ਨੂੰ ਬਹੁਤ ਵਿਸਥਾਰ ਨਾਲ ਕਵਰ ਕਰਾਂਗੇ।  

## ਨਤੀਜਾ

ਇਸ ਪਾਠ ਵਿੱਚ, ਅਸੀਂ ਆਪਣੀ ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਲਾਇਬ੍ਰੇਰੀ ਬਣਾਈ, ਅਤੇ ਇਸਨੂੰ ਇੱਕ ਸਧਾਰਣ ਦੋ-ਆਯਾਮੀ ਵਰਗੀਕਰਨ ਕੰਮ ਲਈ ਵਰਤਿਆ।

## 🚀 ਚੈਲੈਂਜ

ਸੰਬੰਧਿਤ ਨੋਟਬੁੱਕ ਵਿੱਚ, ਤੁਸੀਂ ਮਲਟੀ-ਲੇਅਰਡ ਪਰਸੈਪਟਰਾਨ ਬਣਾਉਣ ਅਤੇ ਟ੍ਰੇਨ ਕਰਨ ਲਈ ਆਪਣਾ ਫਰੇਮਵਰਕ ਲਾਗੂ ਕਰੋਗੇ। ਤੁਸੀਂ ਵੇਖੋਗੇ ਕਿ ਆਧੁਨਿਕ ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਕਿਵੇਂ ਕੰਮ ਕਰਦੇ ਹਨ।

ਆਪਣੇ ਫਰੇਮਵਰਕ ਨੋਟਬੁੱਕ ਵੱਲ ਵਧੋ ਅਤੇ ਇਸਨੂੰ ਪੂਰਾ ਕਰੋ।

## ਸਮੀਖਿਆ ਅਤੇ ਸਵੈ-ਅਧਿਐਨ

ਬੈਕਪ੍ਰੋਪਾਗੇਸ਼ਨ ਇੱਕ ਆਮ ਅਲਗੋਰਿਦਮ ਹੈ ਜੋ AI ਅਤੇ ML ਵਿੱਚ ਵਰਤਿਆ ਜਾਂਦਾ ਹੈ, ਇਸਨੂੰ ਹੋਰ ਵਿਸਥਾਰ ਨਾਲ ਪੜ੍ਹਨਾ ਲਾਭਦਾਇਕ ਹੈ।

## ਅਸਾਈਨਮੈਂਟ

ਇਸ ਲੈਬ ਵਿੱਚ, ਤੁਹਾਨੂੰ ਇਸ ਪਾਠ ਵਿੱਚ ਬਣਾਏ ਫਰੇਮਵਰਕ ਦੀ ਵਰਤੋਂ ਕਰਕੇ MNIST ਹੱਥ ਨਾਲ ਲਿਖੇ ਅੰਕਾਂ ਦੀ ਵਰਗੀਕਰਨ ਸਮੱਸਿਆ ਹੱਲ ਕਰਨੀ ਹੈ।

* ਹਦਾਇਤਾਂ  
* ਨੋਟਬੁੱਕ

**ਅਸਵੀਕਾਰੋਪਣ**:  
ਇਹ ਦਸਤਾਵੇਜ਼ AI ਅਨੁਵਾਦ ਸੇਵਾ [Co-op Translator](https://github.com/Azure/co-op-translator) ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਅਨੁਵਾਦਿਤ ਕੀਤਾ ਗਿਆ ਹੈ। ਜਦੋਂ ਕਿ ਅਸੀਂ ਸਹੀਤਾ ਲਈ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ, ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਰੱਖੋ ਕਿ ਸਵੈਚਾਲਿਤ ਅਨੁਵਾਦਾਂ ਵਿੱਚ ਗਲਤੀਆਂ ਜਾਂ ਅਸਮਰਥਤਾਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ। ਮੂਲ ਦਸਤਾਵੇਜ਼ ਆਪਣੀ ਮੂਲ ਭਾਸ਼ਾ ਵਿੱਚ ਪ੍ਰਮਾਣਿਕ ਸਰੋਤ ਮੰਨਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ। ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਲਈ, ਪੇਸ਼ੇਵਰ ਮਨੁੱਖੀ ਅਨੁਵਾਦ ਦੀ ਸਿਫਾਰਸ਼ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਅਸੀਂ ਇਸ ਅਨੁਵਾਦ ਦੀ ਵਰਤੋਂ ਤੋਂ ਉਤਪੰਨ ਕਿਸੇ ਵੀ ਗਲਤਫਹਮੀ ਜਾਂ ਗਲਤ ਵਿਆਖਿਆ ਲਈ ਜ਼ਿੰਮੇਵਾਰ ਨਹੀਂ ਹਾਂ।