<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T16:56:36+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "pa"
}
-->
# ਨਿਊਰਲ ਨੈੱਟਵਰਕਸ ਦਾ ਪਰਿਚਯ: ਪਰਸੈਪਟਰਾਨ

ਆਧੁਨਿਕ ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਵਰਗਾ ਕੁਝ ਬਣਾਉਣ ਦੀ ਪਹਿਲੀ ਕੋਸ਼ਿਸ਼ 1957 ਵਿੱਚ ਕੋਰਨੇਲ ਏਰੋਨੌਟਿਕਲ ਲੈਬੋਰੇਟਰੀ ਦੇ ਫ੍ਰੈਂਕ ਰੋਜ਼ਨਬਲੈਟ ਨੇ ਕੀਤੀ ਸੀ। ਇਹ ਇੱਕ ਹਾਰਡਵੇਅਰ ਇੰਪਲੀਮੈਂਟੇਸ਼ਨ ਸੀ ਜਿਸਨੂੰ "Mark-1" ਕਿਹਾ ਜਾਂਦਾ ਸੀ, ਜੋ ਤਿਕੋਣ, ਵਰਗ ਅਤੇ ਗੋਲਾਕਾਰ ਵਰਗੀਆਂ ਪ੍ਰਾਚੀਨ ਜਿਆਮਿਤੀਕ ਆਕਾਰਾਂ ਨੂੰ ਪਛਾਣਨ ਲਈ ਬਣਾਇਆ ਗਿਆ ਸੀ।

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='Frank Rosenblatt'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> ਵਿਖੇ ਪਾਈਆਂ ਗਈਆਂ ਤਸਵੀਰਾਂ

ਇੱਕ ਇਨਪੁੱਟ ਚਿੱਤਰ ਨੂੰ 20x20 ਫੋਟੋਸੈੱਲ ਐਰੇ ਨਾਲ ਦਰਸਾਇਆ ਗਿਆ ਸੀ, ਇਸ ਲਈ ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਕੋਲ 400 ਇਨਪੁੱਟ ਅਤੇ ਇੱਕ ਬਾਈਨਰੀ ਆਉਟਪੁੱਟ ਸੀ। ਇੱਕ ਸਧਾਰਣ ਨੈੱਟਵਰਕ ਵਿੱਚ ਇੱਕ ਨਿਊਰਾਨ ਹੁੰਦਾ ਸੀ, ਜਿਸਨੂੰ **threshold logic unit** ਵੀ ਕਿਹਾ ਜਾਂਦਾ ਹੈ। ਨਿਊਰਲ ਨੈੱਟਵਰਕ ਦੇ ਵਜ਼ਨ ਪੋਟੈਂਸ਼ੀਓਮੀਟਰ ਵਾਂਗ ਕੰਮ ਕਰਦੇ ਸਨ, ਜਿਨ੍ਹਾਂ ਨੂੰ ਟ੍ਰੇਨਿੰਗ ਦੌਰਾਨ ਹੱਥੋਂ ਸੈਟ ਕਰਨਾ ਪੈਂਦਾ ਸੀ।

> ✅ ਪੋਟੈਂਸ਼ੀਓਮੀਟਰ ਇੱਕ ਐਸਾ ਯੰਤਰ ਹੈ ਜੋ ਵਰਤੋਂਕਾਰ ਨੂੰ ਸਰਕਿਟ ਦੀ ਰੋਧਕਤਾ ਨੂੰ ਸਮਾਇਕ ਕਰਨ ਦੀ ਆਗਿਆ ਦਿੰਦਾ ਹੈ।

> ਉਸ ਸਮੇਂ The New York Times ਨੇ ਪਰਸੈਪਟਰਾਨ ਬਾਰੇ ਲਿਖਿਆ ਸੀ: *ਇੱਕ ਇਲੈਕਟ੍ਰਾਨਿਕ ਕੰਪਿਊਟਰ ਦਾ ਅੰਸ਼ ਜੋ [ਨੈਵੀ] ਨੂੰ ਉਮੀਦ ਹੈ ਕਿ ਚੱਲ ਸਕੇਗਾ, ਗੱਲ ਕਰ ਸਕੇਗਾ, ਦੇਖ ਸਕੇਗਾ, ਲਿਖ ਸਕੇਗਾ, ਆਪਣੇ ਆਪ ਨੂੰ ਦੁਹਰਾਵੇਗਾ ਅਤੇ ਆਪਣੀ ਮੌਜੂਦਗੀ ਦਾ ਜਾਗਰੂਕ ਹੋਵੇਗਾ।*

## ਪਰਸੈਪਟਰਾਨ ਮਾਡਲ

ਮੰਨ ਲਓ ਸਾਡੇ ਮਾਡਲ ਵਿੱਚ N ਫੀਚਰ ਹਨ, ਇਸ ਸਥਿਤੀ ਵਿੱਚ ਇਨਪੁੱਟ ਵੇਕਟਰ ਦਾ ਆਕਾਰ N ਹੋਵੇਗਾ। ਪਰਸੈਪਟਰਾਨ ਇੱਕ **ਬਾਈਨਰੀ ਵਰਗੀਕਰਨ** ਮਾਡਲ ਹੈ, ਜਿਸਦਾ ਮਤਲਬ ਹੈ ਕਿ ਇਹ ਇਨਪੁੱਟ ਡੇਟਾ ਦੀਆਂ ਦੋ ਵਰਗਾਂ ਵਿੱਚ ਫਰਕ ਕਰ ਸਕਦਾ ਹੈ। ਅਸੀਂ ਮੰਨਦੇ ਹਾਂ ਕਿ ਹਰ ਇਨਪੁੱਟ ਵੇਕਟਰ x ਲਈ ਸਾਡੇ ਪਰਸੈਪਟਰਾਨ ਦਾ ਆਉਟਪੁੱਟ +1 ਜਾਂ -1 ਹੋਵੇਗਾ, ਵਰਗ ਦੇ ਅਨੁਸਾਰ। ਆਉਟਪੁੱਟ ਹੇਠਾਂ ਦਿੱਤੇ ਫਾਰਮੂਲੇ ਨਾਲ ਗਣਨਾ ਕੀਤਾ ਜਾਵੇਗਾ:

y(x) = f(w<sup>T</sup>x)

ਜਿੱਥੇ f ਇੱਕ ਸਟੈਪ ਐਕਟੀਵੇਸ਼ਨ ਫੰਕਸ਼ਨ ਹੈ

## ਪਰਸੈਪਟਰਾਨ ਦੀ ਟ੍ਰੇਨਿੰਗ

ਪਰਸੈਪਟਰਾਨ ਨੂੰ ਟ੍ਰੇਨ ਕਰਨ ਲਈ ਸਾਨੂੰ ਇੱਕ ਵਜ਼ਨ ਵੇਕਟਰ w ਲੱਭਣਾ ਪੈਂਦਾ ਹੈ ਜੋ ਜ਼ਿਆਦਾਤਰ ਮੁੱਲਾਂ ਨੂੰ ਸਹੀ ਤਰੀਕੇ ਨਾਲ ਵਰਗੀਕ੍ਰਿਤ ਕਰਦਾ ਹੈ, ਯਾਨੀ ਸਭ ਤੋਂ ਘੱਟ **ਗਲਤੀ** ਲਿਆਉਂਦਾ ਹੈ। ਇਹ ਗਲਤੀ **perceptron criterion** ਦੁਆਰਾ ਹੇਠਾਂ ਦਿੱਤੇ ਤਰੀਕੇ ਨਾਲ ਪਰਿਭਾਸ਼ਿਤ ਕੀਤੀ ਜਾਂਦੀ ਹੈ:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

ਜਿੱਥੇ:

* ਜੋ ਟ੍ਰੇਨਿੰਗ ਡੇਟਾ ਪੌਇੰਟ i ਗਲਤ ਵਰਗੀਕਰਨ ਦਾ ਕਾਰਨ ਬਣਦੇ ਹਨ, ਉਨ੍ਹਾਂ ਦਾ ਜੋੜ ਲਿਆ ਜਾਂਦਾ ਹੈ
* x<sub>i</sub> ਇਨਪੁੱਟ ਡੇਟਾ ਹੈ, ਅਤੇ t<sub>i</sub> ਨਕਾਰਾਤਮਕ ਅਤੇ ਸਕਾਰਾਤਮਕ ਉਦਾਹਰਣਾਂ ਲਈ ਕ੍ਰਮਵਾਰ -1 ਜਾਂ +1 ਹੁੰਦਾ ਹੈ।

ਇਹ ਮਾਪਦੰਡ ਵਜ਼ਨਾਂ w ਦੇ ਫੰਕਸ਼ਨ ਵਜੋਂ ਮੰਨਿਆ ਜਾਂਦਾ ਹੈ, ਅਤੇ ਸਾਨੂੰ ਇਸ ਨੂੰ ਘਟਾਉਣਾ ਹੁੰਦਾ ਹੈ। ਅਕਸਰ, ਇੱਕ ਤਰੀਕਾ ਜਿਸਨੂੰ **gradient descent** ਕਿਹਾ ਜਾਂਦਾ ਹੈ ਵਰਤਿਆ ਜਾਂਦਾ ਹੈ, ਜਿਸ ਵਿੱਚ ਅਸੀਂ ਕੁਝ ਸ਼ੁਰੂਆਤੀ ਵਜ਼ਨ w<sup>(0)</sup> ਨਾਲ ਸ਼ੁਰੂ ਕਰਦੇ ਹਾਂ, ਅਤੇ ਹਰ ਕਦਮ 'ਤੇ ਵਜ਼ਨਾਂ ਨੂੰ ਹੇਠਾਂ ਦਿੱਤੇ ਫਾਰਮੂਲੇ ਅਨੁਸਾਰ ਅਪਡੇਟ ਕਰਦੇ ਹਾਂ:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

ਇੱਥੇ η ਨੂੰ **learning rate** ਕਿਹਾ ਜਾਂਦਾ ਹੈ, ਅਤੇ ∇E(w) E ਦਾ **ਗ੍ਰੇਡੀਐਂਟ** ਦਰਸਾਉਂਦਾ ਹੈ। ਗ੍ਰੇਡੀਐਂਟ ਦੀ ਗਣਨਾ ਕਰਨ ਤੋਂ ਬਾਅਦ, ਸਾਨੂੰ ਮਿਲਦਾ ਹੈ

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

ਪਾਇਥਨ ਵਿੱਚ ਇਹ ਅਲਗੋਰਿਦਮ ਇਸ ਤਰ੍ਹਾਂ ਦਿਖਾਈ ਦਿੰਦਾ ਹੈ:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## ਨਤੀਜਾ

ਇਸ ਪਾਠ ਵਿੱਚ, ਤੁਸੀਂ ਪਰਸੈਪਟਰਾਨ ਬਾਰੇ ਸਿੱਖਿਆ, ਜੋ ਕਿ ਇੱਕ ਬਾਈਨਰੀ ਵਰਗੀਕਰਨ ਮਾਡਲ ਹੈ, ਅਤੇ ਇਸਨੂੰ ਵਜ਼ਨ ਵੇਕਟਰ ਦੀ ਵਰਤੋਂ ਨਾਲ ਕਿਵੇਂ ਟ੍ਰੇਨ ਕਰਨਾ ਹੈ।

## 🚀 ਚੈਲੈਂਜ

ਜੇ ਤੁਸੀਂ ਆਪਣਾ ਪਰਸੈਪਟਰਾਨ ਬਣਾਉਣ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰਨਾ ਚਾਹੁੰਦੇ ਹੋ, ਤਾਂ Microsoft Learn 'ਤੇ ਇਹ ਲੈਬ ਕਰੋ ਜੋ Azure ML ਡਿਜ਼ਾਈਨਰ ਦੀ ਵਰਤੋਂ ਕਰਦਾ ਹੈ।

## ਸਮੀਖਿਆ ਅਤੇ ਸਵੈ ਅਧਿਐਨ

ਵੇਖਣ ਲਈ ਕਿ ਅਸੀਂ ਪਰਸੈਪਟਰਾਨ ਨੂੰ ਖਿਡੌਣਾ ਸਮੱਸਿਆ ਅਤੇ ਅਸਲੀ ਜ਼ਿੰਦਗੀ ਦੀਆਂ ਸਮੱਸਿਆਵਾਂ ਨੂੰ ਹੱਲ ਕਰਨ ਲਈ ਕਿਵੇਂ ਵਰਤ ਸਕਦੇ ਹਾਂ, ਅਤੇ ਸਿੱਖਣਾ ਜਾਰੀ ਰੱਖਣ ਲਈ - Perceptron ਨੋਟਬੁੱਕ 'ਤੇ ਜਾਓ।

ਇੱਥੇ ਪਰਸੈਪਟਰਾਨ ਬਾਰੇ ਇੱਕ ਦਿਲਚਸਪ ਲੇਖ ਵੀ ਹੈ।

## ਅਸਾਈਨਮੈਂਟ

ਇਸ ਪਾਠ ਵਿੱਚ, ਅਸੀਂ ਬਾਈਨਰੀ ਵਰਗੀਕਰਨ ਟਾਸਕ ਲਈ ਪਰਸੈਪਟਰਾਨ ਨੂੰ ਲਾਗੂ ਕੀਤਾ ਹੈ, ਅਤੇ ਇਸਨੂੰ ਦੋ ਹੱਥ ਨਾਲ ਲਿਖੇ ਅੰਕਾਂ ਵਿੱਚ ਵਰਗੀਕਰਨ ਲਈ ਵਰਤਿਆ ਹੈ। ਇਸ ਲੈਬ ਵਿੱਚ, ਤੁਹਾਨੂੰ ਅੰਕ ਵਰਗੀਕਰਨ ਦੀ ਸਮੱਸਿਆ ਨੂੰ ਪੂਰੀ ਤਰ੍ਹਾਂ ਹੱਲ ਕਰਨ ਲਈ ਕਿਹਾ ਗਿਆ ਹੈ, ਯਾਨੀ ਇਹ ਨਿਰਧਾਰਿਤ ਕਰਨਾ ਕਿ ਦਿੱਤੀ ਗਈ ਚਿੱਤਰ ਨਾਲ ਸਭ ਤੋਂ ਸੰਭਾਵਿਤ ਅੰਕ ਕਿਹੜਾ ਹੈ।

* ਹਦਾਇਤਾਂ  
* ਨੋਟਬੁੱਕ

**ਅਸਵੀਕਾਰੋਪਣ**:  
ਇਹ ਦਸਤਾਵੇਜ਼ AI ਅਨੁਵਾਦ ਸੇਵਾ [Co-op Translator](https://github.com/Azure/co-op-translator) ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਅਨੁਵਾਦਿਤ ਕੀਤਾ ਗਿਆ ਹੈ। ਜਦੋਂ ਕਿ ਅਸੀਂ ਸਹੀਤਾ ਲਈ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ, ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਰੱਖੋ ਕਿ ਸਵੈਚਾਲਿਤ ਅਨੁਵਾਦਾਂ ਵਿੱਚ ਗਲਤੀਆਂ ਜਾਂ ਅਸਮਰਥਤਾਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ। ਮੂਲ ਦਸਤਾਵੇਜ਼ ਆਪਣੀ ਮੂਲ ਭਾਸ਼ਾ ਵਿੱਚ ਪ੍ਰਮਾਣਿਕ ਸਰੋਤ ਮੰਨਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ। ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਲਈ, ਪੇਸ਼ੇਵਰ ਮਨੁੱਖੀ ਅਨੁਵਾਦ ਦੀ ਸਿਫਾਰਸ਼ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਇਸ ਅਨੁਵਾਦ ਦੀ ਵਰਤੋਂ ਤੋਂ ਉਤਪੰਨ ਕਿਸੇ ਵੀ ਗਲਤਫਹਮੀ ਜਾਂ ਗਲਤ ਵਿਆਖਿਆ ਲਈ ਅਸੀਂ ਜ਼ਿੰਮੇਵਾਰ ਨਹੀਂ ਹਾਂ।