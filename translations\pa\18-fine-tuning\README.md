<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "68664f7e754a892ae1d8d5e2b7bd2081",
  "translation_date": "2025-07-09T17:40:42+00:00",
  "source_file": "18-fine-tuning/README.md",
  "language_code": "pa"
}
-->
[![Open Source Models](../../../translated_images/18-lesson-banner.f30176815b1a5074fce9cceba317720586caa99e24001231a92fd04eeb54a121.pa.png)](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst)

# ਆਪਣੇ LLM ਨੂੰ ਫਾਈਨ-ਟਿਊਨ ਕਰਨਾ

ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲਾਂ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਜਨਰੇਟਿਵ AI ਐਪਲੀਕੇਸ਼ਨਾਂ ਬਣਾਉਣ ਨਾਲ ਨਵੇਂ ਚੁਣੌਤੀਆਂ ਸਾਹਮਣੇ ਆਉਂਦੀਆਂ ਹਨ। ਇੱਕ ਮੁੱਖ ਮੁੱਦਾ ਇਹ ਯਕੀਨੀ ਬਣਾਉਣਾ ਹੈ ਕਿ ਮਾਡਲ ਵੱਲੋਂ ਦਿੱਤੇ ਗਏ ਜਵਾਬ ਦੀ ਗੁਣਵੱਤਾ (ਸਹੀਤਾ ਅਤੇ ਸਬੰਧਿਤਤਾ) ਉਚਿਤ ਹੋਵੇ, ਖਾਸ ਕਰਕੇ ਜਦੋਂ ਉਪਭੋਗਤਾ ਕੋਈ ਬੇਨਤੀ ਕਰਦਾ ਹੈ। ਪਿਛਲੇ ਪਾਠਾਂ ਵਿੱਚ, ਅਸੀਂ ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ ਅਤੇ ਰੀਟਰੀਵਲ-ਆਗਮੈਂਟਿਡ ਜਨਰੇਸ਼ਨ ਵਰਗੀਆਂ ਤਕਨੀਕਾਂ ਬਾਰੇ ਗੱਲ ਕੀਤੀ ਸੀ, ਜੋ ਮੌਜੂਦਾ ਮਾਡਲ ਨੂੰ ਪ੍ਰਾਂਪਟ ਇਨਪੁੱਟ ਨੂੰ ਬਦਲ ਕੇ ਸਮੱਸਿਆ ਦਾ ਹੱਲ ਲੱਭਣ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰਦੀਆਂ ਹਨ।

ਅੱਜ ਦੇ ਪਾਠ ਵਿੱਚ, ਅਸੀਂ ਤੀਜੀ ਤਕਨੀਕ, **ਫਾਈਨ-ਟਿਊਨਿੰਗ**, ਬਾਰੇ ਗੱਲ ਕਰਾਂਗੇ, ਜੋ ਇਸ ਚੁਣੌਤੀ ਨੂੰ ਮਾਡਲ ਨੂੰ ਵਾਧੂ ਡੇਟਾ ਨਾਲ ਮੁੜ ਸਿਖਾ ਕੇ ਹੱਲ ਕਰਨ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰਦੀ ਹੈ। ਆਓ ਵੇਰਵੇ ਵਿੱਚ ਜਾਣਦੇ ਹਾਂ।

## ਸਿੱਖਣ ਦੇ ਲਕੜੇ

ਇਹ ਪਾਠ ਪ੍ਰੀ-ਟ੍ਰੇਨਡ ਭਾਸ਼ਾ ਮਾਡਲਾਂ ਲਈ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਦੇ ਸੰਕਲਪ ਨੂੰ ਜਾਣੂ ਕਰਵਾਉਂਦਾ ਹੈ, ਇਸ ਤਰੀਕੇ ਦੇ ਫਾਇਦੇ ਅਤੇ ਚੁਣੌਤੀਆਂ ਦੀ ਜਾਂਚ ਕਰਦਾ ਹੈ, ਅਤੇ ਤੁਹਾਡੇ ਜਨਰੇਟਿਵ AI ਮਾਡਲਾਂ ਦੀ ਕਾਰਗੁਜ਼ਾਰੀ ਸੁਧਾਰਨ ਲਈ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਕਦੋਂ ਅਤੇ ਕਿਵੇਂ ਵਰਤਣ ਬਾਰੇ ਮਦਦ ਦਿੰਦਾ ਹੈ।

ਇਸ ਪਾਠ ਦੇ ਅੰਤ ਤੱਕ, ਤੁਸੀਂ ਹੇਠ ਲਿਖੇ ਸਵਾਲਾਂ ਦੇ ਜਵਾਬ ਦੇ ਸਕੋਗੇ:

- ਭਾਸ਼ਾ ਮਾਡਲਾਂ ਲਈ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਕੀ ਹੈ?
- ਫਾਈਨ-ਟਿਊਨਿੰਗ ਕਦੋਂ ਅਤੇ ਕਿਉਂ ਲਾਭਦਾਇਕ ਹੁੰਦੀ ਹੈ?
- ਮੈਂ ਪ੍ਰੀ-ਟ੍ਰੇਨਡ ਮਾਡਲ ਨੂੰ ਕਿਵੇਂ ਫਾਈਨ-ਟਿਊਨ ਕਰ ਸਕਦਾ ਹਾਂ?
- ਫਾਈਨ-ਟਿਊਨਿੰਗ ਦੀਆਂ ਸੀਮਾਵਾਂ ਕੀ ਹਨ?

ਤਿਆਰ ਹੋ? ਚਲੋ ਸ਼ੁਰੂ ਕਰੀਏ।

## ਚਿੱਤਰਾਂ ਨਾਲ ਸਮਝਾਉਣ ਵਾਲਾ ਗਾਈਡ

ਕੀ ਤੁਸੀਂ ਪੂਰੇ ਵਿਸ਼ੇ ਦਾ ਇੱਕ ਵੱਡਾ ਨਜ਼ਾਰਾ ਲੈਣਾ ਚਾਹੁੰਦੇ ਹੋ ਇਸ ਤੋਂ ਪਹਿਲਾਂ ਕਿ ਅਸੀਂ ਡੂੰਘਾਈ ਵਿੱਚ ਜਾਈਏ? ਇਸ ਚਿੱਤਰਾਂ ਵਾਲੇ ਗਾਈਡ ਨੂੰ ਦੇਖੋ ਜੋ ਇਸ ਪਾਠ ਦੀ ਸਿੱਖਣ ਯਾਤਰਾ ਨੂੰ ਦਰਸਾਉਂਦਾ ਹੈ - ਫਾਈਨ-ਟਿਊਨਿੰਗ ਦੇ ਮੁੱਖ ਸੰਕਲਪ ਅਤੇ ਪ੍ਰੇਰਣਾ ਤੋਂ ਲੈ ਕੇ ਇਸ ਪ੍ਰਕਿਰਿਆ ਅਤੇ ਸਭ ਤੋਂ ਵਧੀਆ ਅਮਲਾਂ ਨੂੰ ਸਮਝਣ ਤੱਕ। ਇਹ ਇੱਕ ਦਿਲਚਸਪ ਵਿਸ਼ਾ ਹੈ, ਇਸ ਲਈ ਆਪਣੇ ਆਪ ਸਿੱਖਣ ਦੀ ਯਾਤਰਾ ਲਈ ਹੋਰ ਸਹਾਇਕ ਲਿੰਕਾਂ ਲਈ [Resources](./RESOURCES.md?WT.mc_id=academic-105485-koreyst) ਪੰਨਾ ਜ਼ਰੂਰ ਵੇਖੋ!

![Illustrated Guide to Fine Tuning Language Models](../../../translated_images/18-fine-tuning-sketchnote.11b21f9ec8a703467a120cb79a28b5ac1effc8d8d9d5b31bbbac6b8640432e14.pa.png)

## ਭਾਸ਼ਾ ਮਾਡਲਾਂ ਲਈ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਕੀ ਹੈ?

ਪਰਿਭਾਸ਼ਾ ਅਨੁਸਾਰ, ਵੱਡੇ ਭਾਸ਼ਾ ਮਾਡਲ ਵੱਖ-ਵੱਖ ਸਰੋਤਾਂ ਤੋਂ ਇਕੱਠੇ ਕੀਤੇ ਗਏ ਵੱਡੇ ਪਾਠ ਡੇਟਾ 'ਤੇ _ਪ੍ਰੀ-ਟ੍ਰੇਨਡ_ ਹੁੰਦੇ ਹਨ, ਜਿਸ ਵਿੱਚ ਇੰਟਰਨੈੱਟ ਵੀ ਸ਼ਾਮਲ ਹੈ। ਜਿਵੇਂ ਕਿ ਅਸੀਂ ਪਿਛਲੇ ਪਾਠਾਂ ਵਿੱਚ ਸਿੱਖਿਆ, ਮਾਡਲ ਦੇ ਜਵਾਬਾਂ ਦੀ ਗੁਣਵੱਤਾ ਸੁਧਾਰਨ ਲਈ ਸਾਨੂੰ _ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ_ ਅਤੇ _ਰੀਟਰੀਵਲ-ਆਗਮੈਂਟਿਡ ਜਨਰੇਸ਼ਨ_ ਵਰਗੀਆਂ ਤਕਨੀਕਾਂ ਦੀ ਲੋੜ ਹੁੰਦੀ ਹੈ।

ਇੱਕ ਪ੍ਰਸਿੱਧ ਪ੍ਰਾਂਪਟ-ਇੰਜੀਨੀਅਰਿੰਗ ਤਕਨੀਕ ਮਾਡਲ ਨੂੰ ਜਵਾਬ ਵਿੱਚ ਕੀ ਉਮੀਦ ਕੀਤੀ ਜਾਂਦੀ ਹੈ, ਇਸ ਬਾਰੇ ਵਧੇਰੇ ਦਿਸ਼ਾ-ਨਿਰਦੇਸ਼ ਦੇਣ ਦੀ ਹੁੰਦੀ ਹੈ, ਜਿਵੇਂ ਕਿ _ਹਦਾਇਤਾਂ_ (ਸਪਸ਼ਟ ਦਿਸ਼ਾ-ਨਿਰਦੇਸ਼) ਦੇ ਕੇ ਜਾਂ _ਕੁਝ ਉਦਾਹਰਣਾਂ_ ਦੇ ਕੇ (ਅਸਪਸ਼ਟ ਦਿਸ਼ਾ-ਨਿਰਦੇਸ਼)। ਇਸਨੂੰ _ਫਿਊ-ਸ਼ਾਟ ਲਰਨਿੰਗ_ ਕਿਹਾ ਜਾਂਦਾ ਹੈ ਪਰ ਇਸਦੇ ਦੋ ਸੀਮਿਤਤਾਵਾਂ ਹਨ:

- ਮਾਡਲ ਦੇ ਟੋਕਨ ਸੀਮਾਵਾਂ ਕਾਰਨ ਤੁਸੀਂ ਜਿੰਨੀ ਉਦਾਹਰਣਾਂ ਦੇ ਸਕਦੇ ਹੋ, ਉਹ ਸੀਮਿਤ ਰਹਿੰਦੀ ਹੈ, ਜਿਸ ਨਾਲ ਪ੍ਰਭਾਵਸ਼ੀਲਤਾ ਘੱਟ ਹੁੰਦੀ ਹੈ।
- ਮਾਡਲ ਦੇ ਟੋਕਨ ਖਰਚੇ ਕਾਰਨ ਹਰ ਪ੍ਰਾਂਪਟ ਵਿੱਚ ਉਦਾਹਰਣਾਂ ਸ਼ਾਮਲ ਕਰਨਾ ਮਹਿੰਗਾ ਪੈ ਸਕਦਾ ਹੈ, ਜਿਸ ਨਾਲ ਲਚਕੀਲਾਪਣ ਘੱਟ ਹੁੰਦਾ ਹੈ।

ਫਾਈਨ-ਟਿਊਨਿੰਗ ਮਸ਼ੀਨ ਲਰਨਿੰਗ ਸਿਸਟਮਾਂ ਵਿੱਚ ਇੱਕ ਆਮ ਅਮਲ ਹੈ, ਜਿਸ ਵਿੱਚ ਅਸੀਂ ਇੱਕ ਪ੍ਰੀ-ਟ੍ਰੇਨਡ ਮਾਡਲ ਨੂੰ ਨਵੇਂ ਡੇਟਾ ਨਾਲ ਮੁੜ ਸਿਖਾ ਕੇ ਕਿਸੇ ਖਾਸ ਕੰਮ 'ਤੇ ਇਸਦੀ ਕਾਰਗੁਜ਼ਾਰੀ ਸੁਧਾਰਦੇ ਹਾਂ। ਭਾਸ਼ਾ ਮਾਡਲਾਂ ਦੇ ਸੰਦਰਭ ਵਿੱਚ, ਅਸੀਂ ਪ੍ਰੀ-ਟ੍ਰੇਨਡ ਮਾਡਲ ਨੂੰ ਕਿਸੇ ਖਾਸ ਕੰਮ ਜਾਂ ਐਪਲੀਕੇਸ਼ਨ ਖੇਤਰ ਲਈ ਚੁਣੀ ਹੋਈ ਉਦਾਹਰਣਾਂ ਦੇ ਸੈੱਟ ਨਾਲ ਫਾਈਨ-ਟਿਊਨ ਕਰ ਸਕਦੇ ਹਾਂ, ਜਿਸ ਨਾਲ ਇੱਕ **ਕਸਟਮ ਮਾਡਲ** ਬਣਦਾ ਹੈ ਜੋ ਉਸ ਖੇਤਰ ਲਈ ਹੋਰ ਸਹੀ ਅਤੇ ਸਬੰਧਿਤ ਹੋ ਸਕਦਾ ਹੈ। ਫਾਈਨ-ਟਿਊਨਿੰਗ ਦਾ ਇੱਕ ਹੋਰ ਫਾਇਦਾ ਇਹ ਹੈ ਕਿ ਇਹ ਫਿਊ-ਸ਼ਾਟ ਲਰਨਿੰਗ ਲਈ ਲੋੜੀਂਦੇ ਉਦਾਹਰਣਾਂ ਦੀ ਗਿਣਤੀ ਘਟਾ ਸਕਦਾ ਹੈ - ਜਿਸ ਨਾਲ ਟੋਕਨ ਦੀ ਵਰਤੋਂ ਅਤੇ ਖਰਚੇ ਘੱਟ ਹੁੰਦੇ ਹਨ।

## ਫਾਈਨ-ਟਿਊਨਿੰਗ ਕਦੋਂ ਅਤੇ ਕਿਉਂ ਕਰਨੀ ਚਾਹੀਦੀ ਹੈ?

ਇਸ ਸੰਦਰਭ ਵਿੱਚ, ਜਦੋਂ ਅਸੀਂ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਦੀ ਗੱਲ ਕਰਦੇ ਹਾਂ, ਤਾਂ ਅਸੀਂ **ਸੁਪਰਵਾਈਜ਼ਡ** ਫਾਈਨ-ਟਿਊਨਿੰਗ ਦੀ ਗੱਲ ਕਰ ਰਹੇ ਹਾਂ, ਜਿਸ ਵਿੱਚ ਮੁੜ ਸਿਖਲਾਈ ਉਸ ਨਵੇਂ ਡੇਟਾ ਨਾਲ ਕੀਤੀ ਜਾਂਦੀ ਹੈ ਜੋ ਮੂਲ ਟ੍ਰੇਨਿੰਗ ਡੇਟਾਸੈੱਟ ਦਾ ਹਿੱਸਾ ਨਹੀਂ ਸੀ। ਇਹ ਅਣਸੁਪਰਵਾਈਜ਼ਡ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਤੋਂ ਵੱਖਰਾ ਹੈ, ਜਿਸ ਵਿੱਚ ਮਾਡਲ ਨੂੰ ਮੂਲ ਡੇਟਾ 'ਤੇ ਵੱਖ-ਵੱਖ ਹਾਈਪਰਪੈਰਾਮੀਟਰਾਂ ਨਾਲ ਮੁੜ ਸਿਖਾਇਆ ਜਾਂਦਾ ਹੈ।

ਇੱਕ ਗੱਲ ਜੋ ਯਾਦ ਰੱਖਣੀ ਚਾਹੀਦੀ ਹੈ, ਉਹ ਇਹ ਹੈ ਕਿ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਇੱਕ ਉੱਚ ਪੱਧਰੀ ਤਕਨੀਕ ਹੈ ਜਿਸ ਲਈ ਕੁਝ ਖਾਸ ਮਾਹਿਰਤਾ ਦੀ ਲੋੜ ਹੁੰਦੀ ਹੈ ਤਾਂ ਜੋ ਚਾਹੀਦੇ ਨਤੀਜੇ ਮਿਲ ਸਕਣ। ਜੇ ਇਹ ਗਲਤ ਤਰੀਕੇ ਨਾਲ ਕੀਤੀ ਗਈ, ਤਾਂ ਇਹ ਉਮੀਦ ਕੀਤੀਆਂ ਸੁਧਾਰਾਂ ਨਹੀਂ ਦੇ ਸਕਦੀ ਅਤੇ ਤੁਹਾਡੇ ਨਿਸ਼ਾਨਾ ਖੇਤਰ ਲਈ ਮਾਡਲ ਦੀ ਕਾਰਗੁਜ਼ਾਰੀ ਨੂੰ ਵੀ ਖਰਾਬ ਕਰ ਸਕਦੀ ਹੈ।

ਇਸ ਲਈ, "ਕਿਵੇਂ" ਫਾਈਨ-ਟਿਊਨ ਕਰਨਾ ਹੈ ਸਿੱਖਣ ਤੋਂ ਪਹਿਲਾਂ, ਤੁਹਾਨੂੰ ਇਹ ਜਾਣਨਾ ਜਰੂਰੀ ਹੈ ਕਿ "ਕਿਉਂ" ਇਹ ਰਸਤਾ ਅਪਣਾਉਣਾ ਚਾਹੀਦਾ ਹੈ ਅਤੇ "ਕਦੋਂ" ਫਾਈਨ-ਟਿਊਨਿੰਗ ਦੀ ਪ੍ਰਕਿਰਿਆ ਸ਼ੁਰੂ ਕਰਨੀ ਚਾਹੀਦੀ ਹੈ। ਆਪਣੇ ਆਪ ਨੂੰ ਇਹ ਸਵਾਲ ਪੁੱਛੋ:

- **ਵਰਤੋਂ ਦਾ ਮਾਮਲਾ**: ਤੁਹਾਡਾ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਲਈ ਕੀ _ਵਰਤੋਂ ਦਾ ਮਾਮਲਾ_ ਹੈ? ਮੌਜੂਦਾ ਪ੍ਰੀ-ਟ੍ਰੇਨਡ ਮਾਡਲ ਦੇ ਕਿਸ ਪੱਖ ਨੂੰ ਤੁਸੀਂ ਸੁਧਾਰਨਾ ਚਾਹੁੰਦੇ ਹੋ?
- **ਵਿਕਲਪ**: ਕੀ ਤੁਸੀਂ _ਹੋਰ ਤਕਨੀਕਾਂ_ ਦੀ ਕੋਸ਼ਿਸ਼ ਕੀਤੀ ਹੈ ਜੋ ਚਾਹੀਦੇ ਨਤੀਜੇ ਪ੍ਰਾਪਤ ਕਰਨ ਵਿੱਚ ਮਦਦ ਕਰ ਸਕਦੀਆਂ ਹਨ? ਉਨ੍ਹਾਂ ਨੂੰ ਇੱਕ ਬੇਸਲਾਈਨ ਬਣਾਉਣ ਲਈ ਵਰਤੋ।
  - ਪ੍ਰਾਂਪਟ ਇੰਜੀਨੀਅਰਿੰਗ: ਸੰਬੰਧਿਤ ਪ੍ਰਾਂਪਟ ਜਵਾਬਾਂ ਦੇ ਉਦਾਹਰਣਾਂ ਨਾਲ ਫਿਊ-ਸ਼ਾਟ ਪ੍ਰਾਂਪਟਿੰਗ ਵਰਗੀਆਂ ਤਕਨੀਕਾਂ ਦੀ ਕੋਸ਼ਿਸ਼ ਕਰੋ। ਜਵਾਬਾਂ ਦੀ ਗੁਣਵੱਤਾ ਦਾ ਮੁਲਾਂਕਣ ਕਰੋ।
  - ਰੀਟਰੀਵਲ ਆਗਮੈਂਟਿਡ ਜਨਰੇਸ਼ਨ: ਆਪਣੇ ਡੇਟਾ ਦੀ ਖੋਜ ਕਰਕੇ ਪ੍ਰਾਂਪਟਾਂ ਨੂੰ ਕੁਐਰੀ ਨਤੀਜਿਆਂ ਨਾਲ ਵਧਾਓ। ਜਵਾਬਾਂ ਦੀ ਗੁਣਵੱਤਾ ਦਾ ਮੁਲਾਂਕਣ ਕਰੋ।
- **ਖਰਚੇ**: ਕੀ ਤੁਸੀਂ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਦੇ ਖਰਚੇ ਪਛਾਣੇ ਹਨ?
  - ਟਿਊਨਬਿਲਟੀ - ਕੀ ਪ੍ਰੀ-ਟ੍ਰੇਨਡ ਮਾਡਲ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਲਈ ਉਪਲਬਧ ਹੈ?
  - ਕੋਸ਼ਿਸ਼ - ਟ੍ਰੇਨਿੰਗ ਡੇਟਾ ਤਿਆਰ ਕਰਨ, ਮਾਡਲ ਦਾ ਮੁਲਾਂਕਣ ਅਤੇ ਸੁਧਾਰ ਕਰਨ ਲਈ।
  - ਕੰਪਿਊਟ - ਫਾਈਨ-ਟਿਊਨਿੰਗ ਜੌਬ ਚਲਾਉਣ ਅਤੇ ਫਾਈਨ-ਟਿਊਨਡ ਮਾਡਲ ਨੂੰ ਡਿਪਲੋਇ ਕਰਨ ਲਈ।
  - ਡੇਟਾ - ਫਾਈਨ-ਟਿਊਨਿੰਗ ਪ੍ਰਭਾਵ ਲਈ ਕਾਫੀ ਗੁਣਵੱਤਾ ਵਾਲੇ ਉਦਾਹਰਣਾਂ ਤੱਕ ਪਹੁੰਚ।
- **ਫਾਇਦੇ**: ਕੀ ਤੁਸੀਂ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਦੇ ਫਾਇਦੇ ਪੁਸ਼ਟੀ ਕੀਤੇ ਹਨ?
  - ਗੁਣਵੱਤਾ - ਕੀ ਫਾਈਨ-ਟਿਊਨਡ ਮਾਡਲ ਬੇਸਲਾਈਨ ਤੋਂ ਵਧੀਆ ਹੈ?
  - ਖਰਚਾ - ਕੀ ਇਹ ਪ੍ਰਾਂਪਟਾਂ ਨੂੰ ਸਧਾਰਨ ਕਰਕੇ ਟੋਕਨ ਦੀ ਵਰਤੋਂ ਘਟਾਉਂਦਾ ਹੈ?
  - ਵਿਸਥਾਰਯੋਗਤਾ - ਕੀ ਤੁਸੀਂ ਬੇਸ ਮਾਡਲ ਨੂੰ ਨਵੇਂ ਖੇਤਰਾਂ ਲਈ ਦੁਬਾਰਾ ਵਰਤ ਸਕਦੇ ਹੋ?

ਇਨ੍ਹਾਂ ਸਵਾਲਾਂ ਦੇ ਜਵਾਬ ਦੇ ਕੇ, ਤੁਸੀਂ ਫੈਸਲਾ ਕਰ ਸਕੋਗੇ ਕਿ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਤੁਹਾਡੇ ਵਰਤੋਂ ਦੇ ਮਾਮਲੇ ਲਈ ਸਹੀ ਤਰੀਕਾ ਹੈ ਜਾਂ ਨਹੀਂ। ਆਦਰਸ਼ ਤੌਰ 'ਤੇ, ਇਹ ਤਰੀਕਾ ਸਿਰਫ਼ ਤਦ ਹੀ ਵੈਧ ਹੈ ਜਦੋਂ ਫਾਇਦੇ ਖਰਚਿਆਂ ਤੋਂ ਵੱਧ ਹੋਣ। ਜਦੋਂ ਤੁਸੀਂ ਅੱਗੇ ਵਧਣ ਦਾ ਫੈਸਲਾ ਕਰ ਲੈਂਦੇ ਹੋ, ਤਾਂ ਸੋਚੋ ਕਿ ਤੁਸੀਂ ਪ੍ਰੀ-ਟ੍ਰੇਨਡ ਮਾਡਲ ਨੂੰ _ਕਿਵੇਂ_ ਫਾਈਨ-ਟਿਊਨ ਕਰ ਸਕਦੇ ਹੋ।

ਫੈਸਲਾ ਕਰਨ ਦੀ ਪ੍ਰਕਿਰਿਆ ਬਾਰੇ ਹੋਰ ਜਾਣਕਾਰੀ ਚਾਹੁੰਦੇ ਹੋ? [To fine-tune or not to fine-tune](https://www.youtube.com/watch?v=0Jo-z-MFxJs) ਵੇਖੋ।

## ਅਸੀਂ ਪ੍ਰੀ-ਟ੍ਰੇਨਡ ਮਾਡਲ ਨੂੰ ਕਿਵੇਂ ਫਾਈਨ-ਟਿਊਨ ਕਰ ਸਕਦੇ ਹਾਂ?

ਪ੍ਰੀ-ਟ੍ਰੇਨਡ ਮਾਡਲ ਨੂੰ ਫਾਈਨ-ਟਿਊਨ ਕਰਨ ਲਈ ਤੁਹਾਡੇ ਕੋਲ ਇਹ ਚੀਜ਼ਾਂ ਹੋਣੀਆਂ ਚਾਹੀਦੀਆਂ ਹਨ:

- ਫਾਈਨ-ਟਿਊਨ ਕਰਨ ਲਈ ਇੱਕ ਪ੍ਰੀ-ਟ੍ਰੇਨਡ ਮਾਡਲ
- ਫਾਈਨ-ਟਿਊਨਿੰਗ ਲਈ ਵਰਤਣ ਵਾਲਾ ਡੇਟਾਸੈੱਟ
- ਫਾਈਨ-ਟਿਊਨਿੰਗ ਜੌਬ ਚਲਾਉਣ ਲਈ ਟ੍ਰੇਨਿੰਗ ਵਾਤਾਵਰਣ
- ਫਾਈਨ-ਟਿਊਨਡ ਮਾਡਲ ਨੂੰ ਡਿਪਲੋਇ ਕਰਨ ਲਈ ਹੋਸਟਿੰਗ ਵਾਤਾਵਰਣ

## ਫਾਈਨ-ਟਿਊਨਿੰਗ ਕਾਰਜ ਵਿੱਚ

ਹੇਠਾਂ ਦਿੱਤੇ ਸਰੋਤ ਤੁਹਾਨੂੰ ਚੁਣੇ ਹੋਏ ਮਾਡਲ ਅਤੇ ਚੁਣੀ ਹੋਈ ਡੇਟਾਸੈੱਟ ਨਾਲ ਇੱਕ ਅਸਲੀ ਉਦਾਹਰਣ ਰਾਹੀਂ ਕਦਮ-ਦਰ-ਕਦਮ ਟਿਊਟੋਰਿਯਲ ਪ੍ਰਦਾਨ ਕਰਦੇ ਹਨ। ਇਹ ਟਿਊਟੋਰਿਯਲ ਕਰਨ ਲਈ, ਤੁਹਾਨੂੰ ਉਸ ਖਾਸ ਪ੍ਰਦਾਤਾ ਤੇ ਖਾਤਾ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ, ਨਾਲ ਹੀ ਸੰਬੰਧਿਤ ਮਾਡਲ ਅਤੇ ਡੇਟਾਸੈੱਟਾਂ ਤੱਕ ਪਹੁੰਚ।

| ਪ੍ਰਦਾਤਾ       | ਟਿਊਟੋਰਿਯਲ                                                                                                                                                                    | ਵੇਰਵਾ                                                                                                                                                                                                                                                                                                                                                                                                                             |
| ------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| OpenAI        | [How to fine-tune chat models](https://github.com/openai/openai-cookbook/blob/main/examples/How_to_finetune_chat_models.ipynb?WT.mc_id=academic-105485-koreyst)                 | ਇੱਕ ਖਾਸ ਖੇਤਰ ("recipe assistant") ਲਈ `gpt-35-turbo` ਨੂੰ ਫਾਈਨ-ਟਿਊਨ ਕਰਨਾ ਸਿੱਖੋ, ਜਿਸ ਵਿੱਚ ਟ੍ਰੇਨਿੰਗ ਡੇਟਾ ਤਿਆਰ ਕਰਨਾ, ਫਾਈਨ-ਟਿਊਨਿੰਗ ਜੌਬ ਚਲਾਉਣਾ ਅਤੇ ਫਾਈਨ-ਟਿਊਨਡ ਮਾਡਲ ਨੂੰ ਇੰਫਰੈਂਸ ਲਈ ਵਰਤਣਾ ਸ਼ਾਮਲ ਹੈ।                                                                                                                                                                                                                              |
| Azure OpenAI  | [GPT 3.5 Turbo fine-tuning tutorial](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python-new%2Ccommand-line?WT.mc_id=academic-105485-koreyst)  | Azure 'ਤੇ `gpt-35-turbo-0613` ਮਾਡਲ ਨੂੰ ਫਾਈਨ-ਟਿਊਨ ਕਰਨਾ ਸਿੱਖੋ, ਜਿਸ ਵਿੱਚ ਟ੍ਰੇਨਿੰਗ ਡੇਟਾ ਬਣਾਉਣਾ ਅਤੇ ਅਪਲੋਡ ਕਰਨਾ, ਫਾਈਨ-ਟਿਊਨਿੰਗ ਜੌਬ ਚਲਾਉਣਾ, ਨਵੇਂ ਮਾਡਲ ਨੂੰ ਡਿਪਲੋਇ ਅਤੇ ਵਰਤਣਾ ਸ਼ਾਮਲ ਹੈ।                                                                                                                                                                                                                                            |
| Hugging Face  | [Fine-tuning LLMs with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                              | ਇਹ ਬਲੌਗ ਪੋਸਟ ਤੁਹਾਨੂੰ ਇੱਕ _ਓਪਨ LLM_ (ਜਿਵੇਂ `CodeLlama 7B`) ਨੂੰ [transformers](https://huggingface.co/docs/transformers/index?WT.mc_id=academic-105485-koreyst) ਲਾਇਬ੍ਰੇਰੀ ਅਤੇ [Transformer Reinforcement Learning (TRL)](https://huggingface.co/docs/trl/index?WT.mc_id=academic-105485-koreyst) ਨਾਲ ਫਾਈਨ-ਟਿਊਨ ਕਰਨਾ ਸਿਖਾਉਂਦੀ ਹੈ, ਜੋ Hugging Face 'ਤੇ ਖੁੱਲ੍ਹੇ [ਡੇਟਾਸੈੱਟ](https://huggingface.co/docs/datasets/index?WT.mc_id=academic-105485-koreyst) ਵਰਤਦੀ ਹੈ। |
|               |                                                                                                                                                                               |                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| 🤗 AutoTrain  | [Fine-tuning LLMs with AutoTrain](https://github.com/huggingface/autotrain-advanced/?WT.mc_id=academic-105485-koreyst)                                                        | AutoTrain (ਜਾਂ AutoTrain Advanced) ਇੱਕ ਪਾਇਥਨ ਲਾਇਬ੍ਰੇਰੀ ਹੈ ਜੋ Hugging Face ਵੱਲੋਂ ਵਿਕਸਿਤ ਕੀਤੀ ਗਈ ਹੈ ਅਤੇ ਇਹ ਕਈ ਵੱਖ-ਵੱਖ ਕੰਮਾਂ ਲਈ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਦੀ ਸਹੂਲਤ ਦਿੰਦੀ ਹੈ, ਜਿਸ ਵਿੱਚ LLM ਫਾਈਨ-ਟਿਊਨਿੰਗ ਵੀ ਸ਼ਾਮਲ ਹੈ। AutoTrain ਇੱਕ ਨੋ-ਕੋਡ ਹੱਲ ਹੈ ਅਤੇ ਫਾਈਨ-ਟਿਊਨਿੰਗ ਤੁਹਾਡੇ ਆਪਣੇ ਕਲਾਉਡ, Hugging Face Spaces ਜਾਂ ਲੋਕਲ ਤੌਰ 'ਤੇ ਕੀਤੀ ਜਾ ਸਕਦੀ ਹੈ। ਇਹ ਵੈੱਬ-ਅਧਾਰਿਤ GUI, CLI ਅਤੇ yaml ਕਨਫਿਗ ਫਾਈਲਾਂ ਰਾਹੀਂ ਟ੍ਰੇਨਿੰਗ ਦਾ ਸਮਰਥਨ ਕਰਦਾ ਹੈ। |
|               |                                                                                                                                                                               |                                                                                                                                                                                                                                                                                                                                                                                                                                   |

## ਅਸਾਈਨਮੈਂਟ

ਉਪਰ ਦਿੱਤੇ ਟਿਊਟੋਰਿਯਲਾਂ ਵਿੱਚੋਂ ਕਿਸੇ ਇੱਕ ਨੂੰ ਚੁਣੋ ਅਤੇ ਉਸਨੂੰ ਪੂਰੀ ਤਰ੍ਹਾਂ ਸਮਝੋ। _ਅਸੀਂ ਇਸ

**ਅਸਵੀਕਾਰੋਪਣ**:  
ਇਹ ਦਸਤਾਵੇਜ਼ AI ਅਨੁਵਾਦ ਸੇਵਾ [Co-op Translator](https://github.com/Azure/co-op-translator) ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਅਨੁਵਾਦਿਤ ਕੀਤਾ ਗਿਆ ਹੈ। ਜਦੋਂ ਕਿ ਅਸੀਂ ਸਹੀਅਤ ਲਈ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ, ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਰੱਖੋ ਕਿ ਸਵੈਚਾਲਿਤ ਅਨੁਵਾਦਾਂ ਵਿੱਚ ਗਲਤੀਆਂ ਜਾਂ ਅਸਮਰਥਤਾਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ। ਮੂਲ ਦਸਤਾਵੇਜ਼ ਆਪਣੀ ਮੂਲ ਭਾਸ਼ਾ ਵਿੱਚ ਪ੍ਰਮਾਣਿਕ ਸਰੋਤ ਮੰਨਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ। ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਲਈ, ਪੇਸ਼ੇਵਰ ਮਨੁੱਖੀ ਅਨੁਵਾਦ ਦੀ ਸਿਫਾਰਸ਼ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਇਸ ਅਨੁਵਾਦ ਦੀ ਵਰਤੋਂ ਤੋਂ ਉਤਪੰਨ ਕਿਸੇ ਵੀ ਗਲਤਫਹਮੀ ਜਾਂ ਗਲਤ ਵਿਆਖਿਆ ਲਈ ਅਸੀਂ ਜ਼ਿੰਮੇਵਾਰ ਨਹੀਂ ਹਾਂ।