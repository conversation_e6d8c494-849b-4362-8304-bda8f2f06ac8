<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:59:47+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "pa"
}
-->
# ਸਵੈ-ਮਾਰਗਦਰਸ਼ਿਤ ਸਿੱਖਿਆ ਲਈ ਸਰੋਤ

ਇਸ ਪਾਠ ਨੂੰ OpenAI ਅਤੇ Azure OpenAI ਦੇ ਕਈ ਮੁੱਖ ਸਰੋਤਾਂ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਬਣਾਇਆ ਗਿਆ ਸੀ, ਜੋ ਟਰਮੀਨੋਲੋਜੀ ਅਤੇ ਟਿਊਟੋਰਿਯਲ ਲਈ ਸੰਦਰਭ ਵਜੋਂ ਕੰਮ ਕਰਦੇ ਹਨ। ਇੱਥੇ ਤੁਹਾਡੇ ਆਪਣੇ ਸਵੈ-ਮਾਰਗਦਰਸ਼ਿਤ ਸਿੱਖਣ ਦੇ ਯਾਤਰਾ ਲਈ ਇੱਕ ਅਧੂਰਾ ਸੂਚੀ ਦਿੱਤੀ ਗਈ ਹੈ।

## 1. ਪ੍ਰਮੁੱਖ ਸਰੋਤ

| ਸਿਰਲੇਖ/ਲਿੰਕ                                                                                                                                                                                                                   | ਵਰਣਨ                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning ਕੁਝ ਉਦਾਹਰਣਾਂ 'ਤੇ ਸਿੱਖਣ ਨਾਲੋਂ ਬਿਹਤਰ ਹੁੰਦਾ ਹੈ ਕਿਉਂਕਿ ਇਹ ਪ੍ਰਾਂਪਟ ਵਿੱਚ ਆ ਸਕਣ ਵਾਲੇ ਉਦਾਹਰਣਾਂ ਨਾਲੋਂ ਕਈ ਵੱਧ ਉਦਾਹਰਣਾਂ 'ਤੇ ਟ੍ਰੇਨਿੰਗ ਕਰਦਾ ਹੈ, ਜਿਸ ਨਾਲ ਖਰਚ ਬਚਦਾ ਹੈ, ਜਵਾਬ ਦੀ ਗੁਣਵੱਤਾ ਸੁਧਰਦੀ ਹੈ ਅਤੇ ਘੱਟ ਲੇਟੈਂਸੀ ਵਾਲੀਆਂ ਬੇਨਤੀਆਂ ਸੰਭਵ ਹੁੰਦੀਆਂ ਹਨ। **OpenAI ਤੋਂ fine-tuning ਦਾ ਇੱਕ ਝਲਕ ਪ੍ਰਾਪਤ ਕਰੋ।**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | ਸਮਝੋ ਕਿ **fine-tuning ਕੀ ਹੈ (ਧਾਰਣਾ)**, ਇਸ ਨੂੰ ਕਿਉਂ ਦੇਖਣਾ ਚਾਹੀਦਾ ਹੈ (ਮੁੱਦਾ), ਕਿਹੜਾ ਡਾਟਾ ਵਰਤਣਾ ਹੈ (ਟ੍ਰੇਨਿੰਗ) ਅਤੇ ਗੁਣਵੱਤਾ ਨੂੰ ਕਿਵੇਂ ਮਾਪਣਾ ਹੈ।                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service ਤੁਹਾਨੂੰ fine-tuning ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਆਪਣੇ ਨਿੱਜੀ ਡਾਟਾਸੈੱਟ ਲਈ ਮਾਡਲ ਨੂੰ ਅਨੁਕੂਲਿਤ ਕਰਨ ਦੀ ਆਗਿਆ ਦਿੰਦਾ ਹੈ। ਸਿੱਖੋ ਕਿ **fine-tuning ਕਿਵੇਂ ਕਰਨੀ ਹੈ (ਪ੍ਰਕਿਰਿਆ)** ਅਤੇ Azure AI Studio, Python SDK ਜਾਂ REST API ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਮਾਡਲ ਚੁਣੋ।                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLMs ਖਾਸ ਡੋਮੇਨ, ਕੰਮ ਜਾਂ ਡਾਟਾਸੈੱਟ 'ਤੇ ਚੰਗਾ ਪ੍ਰਦਰਸ਼ਨ ਨਹੀਂ ਕਰ ਸਕਦੇ, ਜਾਂ ਗਲਤ ਜਾਂ ਭ੍ਰਮਿਤ ਕਰਨ ਵਾਲੇ ਨਤੀਜੇ ਦੇ ਸਕਦੇ ਹਨ। **ਤੁਹਾਨੂੰ ਕਦੋਂ fine-tuning ਬਾਰੇ ਸੋਚਣਾ ਚਾਹੀਦਾ ਹੈ** ਇਸ ਸਮੱਸਿਆ ਦਾ ਸੰਭਾਵਿਤ ਹੱਲ ਵਜੋਂ?                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | ਲਗਾਤਾਰ fine-tuning ਇੱਕ ਦੁਹਰਾਈ ਜਾਣ ਵਾਲੀ ਪ੍ਰਕਿਰਿਆ ਹੈ ਜਿਸ ਵਿੱਚ ਪਹਿਲਾਂ ਹੀ fine-tuned ਮਾਡਲ ਨੂੰ ਬੇਸ ਮਾਡਲ ਵਜੋਂ ਚੁਣਿਆ ਜਾਂਦਾ ਹੈ ਅਤੇ ਨਵੇਂ ਟ੍ਰੇਨਿੰਗ ਉਦਾਹਰਣਾਂ 'ਤੇ ਇਸ ਨੂੰ ਹੋਰ fine-tune ਕੀਤਾ ਜਾਂਦਾ ਹੈ।                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | ਆਪਣੇ ਮਾਡਲ ਨੂੰ **function calling ਉਦਾਹਰਣਾਂ ਨਾਲ fine-tune ਕਰਨਾ** ਮਾਡਲ ਦੇ ਨਤੀਜਿਆਂ ਨੂੰ ਹੋਰ ਸਹੀ ਅਤੇ ਸਥਿਰ ਬਣਾ ਸਕਦਾ ਹੈ - ਇਸ ਨਾਲ ਮਿਲਦੇ-ਜੁਲਦੇ ਫਾਰਮੈਟ ਵਾਲੇ ਜਵਾਬ ਅਤੇ ਖਰਚ ਵਿੱਚ ਬਚਤ ਹੁੰਦੀ ਹੈ।                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | ਇਸ ਸਾਰਣੀ ਨੂੰ ਵੇਖੋ ਤਾਂ ਜੋ ਸਮਝ ਸਕੋ ਕਿ Azure OpenAI ਵਿੱਚ **ਕਿਹੜੇ ਮਾਡਲ fine-tune ਕੀਤੇ ਜਾ ਸਕਦੇ ਹਨ**, ਅਤੇ ਇਹ ਕਿਹੜੇ ਖੇਤਰਾਂ ਵਿੱਚ ਉਪਲਬਧ ਹਨ। ਜੇ ਲੋੜ ਹੋਵੇ ਤਾਂ ਉਨ੍ਹਾਂ ਦੀ ਟੋਕਨ ਸੀਮਾ ਅਤੇ ਟ੍ਰੇਨਿੰਗ ਡਾਟਾ ਦੀ ਮਿਆਦ ਵੀ ਵੇਖੋ।                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | ਇਹ 30 ਮਿੰਟ ਦਾ **ਅਕਤੂਬਰ 2023** ਦਾ AI Show ਐਪੀਸੋਡ ਲਾਭ, ਨੁਕਸਾਨ ਅਤੇ ਪ੍ਰਯੋਗਿਕ ਜਾਣਕਾਰੀਆਂ ਬਾਰੇ ਗੱਲ ਕਰਦਾ ਹੈ ਜੋ ਤੁਹਾਨੂੰ ਇਹ ਫੈਸਲਾ ਕਰਨ ਵਿੱਚ ਮਦਦ ਕਰਦੀਆਂ ਹਨ।                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | ਇਹ **AI Playbook** ਸਰੋਤ ਤੁਹਾਨੂੰ ਡਾਟਾ ਦੀਆਂ ਲੋੜਾਂ, ਫਾਰਮੈਟਿੰਗ, ਹਾਈਪਰਪੈਰਾਮੀਟਰ fine-tuning ਅਤੇ ਚੁਣੌਤੀਆਂ/ਸੀਮਾਵਾਂ ਬਾਰੇ ਜਾਣਕਾਰੀ ਦਿੰਦਾ ਹੈ ਜੋ ਤੁਹਾਨੂੰ ਜਾਣਣੀ ਚਾਹੀਦੀ ਹੈ।                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | ਇੱਕ ਨਮੂਨਾ fine-tuning ਡਾਟਾਸੈੱਟ ਬਣਾਉਣਾ, fine-tuning ਲਈ ਤਿਆਰੀ ਕਰਨੀ, fine-tuning ਜੌਬ ਬਣਾਉਣਾ ਅਤੇ Azure 'ਤੇ fine-tuned ਮਾਡਲ ਨੂੰ ਤਾਇਨਾਤ ਕਰਨਾ ਸਿੱਖੋ।                                                                                                                                                                                    |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio ਤੁਹਾਨੂੰ ਆਪਣੇ ਨਿੱਜੀ ਡਾਟਾਸੈੱਟ ਲਈ ਵੱਡੇ ਭਾਸ਼ਾਈ ਮਾਡਲਾਂ ਨੂੰ ਅਨੁਕੂਲਿਤ ਕਰਨ ਦੀ ਆਗਿਆ ਦਿੰਦਾ ਹੈ _ਇੱਕ UI-ਅਧਾਰਿਤ ਵਰਕਫਲੋਅ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਜੋ ਘੱਟ ਕੋਡਿੰਗ ਵਾਲੇ ਵਿਕਾਸਕਾਰਾਂ ਲਈ موزੂਨ ਹੈ_। ਇਸ ਉਦਾਹਰਣ ਨੂੰ ਵੇਖੋ।                                                                                                                                                               |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | ਇਹ ਲੇਖ ਵੇਖਾਉਂਦਾ ਹੈ ਕਿ ਕਿਵੇਂ Hugging Face ਮਾਡਲ ਨੂੰ Hugging Face transformers ਲਾਇਬ੍ਰੇਰੀ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਇੱਕ ਸਿੰਗਲ GPU 'ਤੇ Azure DataBricks ਅਤੇ Hugging Face Trainer ਲਾਇਬ੍ਰੇਰੀਜ਼ ਨਾਲ fine-tune ਕੀਤਾ ਜਾ ਸਕਦਾ ਹੈ।                                                                                                                                                |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Azure Machine Learning ਵਿੱਚ ਮਾਡਲ ਕੈਟਾਲੌਗ ਵਿੱਚ ਕਈ ਖੁੱਲ੍ਹੇ ਸਰੋਤ ਮਾਡਲ ਹਨ ਜਿਨ੍ਹਾਂ ਨੂੰ ਤੁਸੀਂ ਆਪਣੇ ਖਾਸ ਕੰਮ ਲਈ fine-tune ਕਰ ਸਕਦੇ ਹੋ। ਇਸ ਮੋਡੀਊਲ ਨੂੰ [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) ਤੋਂ ਕੋਸ਼ਿਸ਼ ਕਰੋ। |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Microsoft Azure 'ਤੇ W&B ਦੀ ਵਰਤੋਂ ਕਰਕੇ GPT-3.5 ਜਾਂ GPT-4 ਮਾਡਲਾਂ ਨੂੰ fine-tune ਕਰਨ ਨਾਲ ਮਾਡਲ ਦੇ ਪ੍ਰਦਰਸ਼ਨ ਦੀ ਵਿਸਥਾਰਿਤ ਟ੍ਰੈਕਿੰਗ ਅਤੇ ਵਿਸ਼ਲੇਸ਼ਣ ਸੰਭਵ ਹੁੰਦੀ ਹੈ। ਇਹ ਗਾਈਡ OpenAI Fine-Tuning ਗਾਈਡ ਤੋਂ ਧਾਰਣਾਵਾਂ ਨੂੰ ਵਧਾਉਂਦੀ ਹੈ ਅਤੇ Azure OpenAI ਲਈ ਖਾਸ ਕਦਮ ਅਤੇ ਵਿਸ਼ੇਸ਼ਤਾਵਾਂ ਦਿੰਦੀ ਹੈ।                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. ਦੂਜੇ ਦਰਜੇ ਦੇ ਸਰੋਤ

ਇਹ ਭਾਗ ਹੋਰ ਸਰੋਤਾਂ ਨੂੰ ਕੈਪਚਰ ਕਰਦਾ ਹੈ ਜੋ ਖੋਜਣ ਯੋਗ ਹਨ, ਪਰ ਜਿਨ੍ਹਾਂ ਨੂੰ ਅਸੀਂ ਇਸ ਪਾਠ ਵਿੱਚ ਸਮਾਂ ਨਾ ਹੋਣ ਕਰਕੇ ਕਵਰ ਨਹੀਂ ਕੀਤਾ। ਇਹ ਭਵਿੱਖ ਵਿੱਚ ਕਿਸੇ ਹੋਰ ਪਾਠ ਵਿੱਚ ਜਾਂ ਦੂਜੇ ਅਸਾਈਨਮੈਂਟ ਦੇ ਵਿਕਲਪ ਵਜੋਂ ਕਵਰ ਕੀਤੇ ਜਾ ਸਕਦੇ ਹਨ। ਇਸ ਵੇਲੇ, ਇਨ੍ਹਾਂ ਨੂੰ ਵਰਤ ਕੇ ਇਸ ਵਿਸ਼ੇ 'ਤੇ ਆਪਣੀ ਮਾਹਿਰਤਾ ਅਤੇ ਗਿਆਨ ਬਣਾਓ।

| ਸਿਰਲੇਖ/ਲਿੰਕ                                                                                                                                                                                                            | ਵਰਣਨ                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Data preparation and analysis for chat model fine-tuning](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | ਇਹ ਨੋਟਬੁੱਕ ਚੈਟ ਮਾਡਲ ਦੀ fine-tuning ਲਈ ਵਰਤੇ ਜਾਣ ਵਾਲੇ ਚੈਟ ਡਾਟਾਸੈੱਟ ਨੂੰ ਪ੍ਰੀ-ਪ੍ਰੋਸੈਸ ਅਤੇ ਵਿਸ਼ਲੇਸ਼ਣ ਕਰਨ ਦਾ ਸੰਦ ਹੈ। ਇਹ ਫਾਰਮੈਟ ਦੀਆਂ ਗਲਤੀਆਂ ਦੀ ਜਾਂਚ ਕਰਦਾ ਹੈ, ਬੁਨਿਆਦੀ ਅੰਕੜੇ ਦਿੰਦਾ ਹੈ ਅਤੇ fine-tuning ਖਰਚਾਂ ਲਈ ਟੋਕਨ ਗਿਣਤੀ ਦਾ ਅੰਦਾਜ਼ਾ ਲਗਾਉਂਦਾ ਹੈ। ਵੇਖੋ: [Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)।                                                                                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning for Retrieval Augmented Generation (RAG) with Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | ਇਸ ਨੋਟਬੁੱਕ ਦਾ ਮਕਸਦ OpenAI ਮਾਡਲਾਂ ਨੂੰ Retrieval Augmented Generation (RAG) ਲਈ fine-tune ਕਰਨ ਦਾ ਵਿਸਥਾਰਿਤ ਉਦਾਹਰਣ ਦੇਣਾ ਹੈ। ਅਸੀਂ Qdrant ਅਤੇ Few-Shot Learning ਨੂੰ ਵੀ ਜੋੜ ਕੇ ਮਾਡਲ ਦੇ ਪ੍ਰਦਰਸ਼ਨ ਨੂੰ ਵਧਾਵਾਂਗੇ ਅਤੇ ਗਲਤੀਆਂ ਨੂੰ ਘਟਾਵਾਂਗੇ।                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT with Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) AI ਵਿਕਾਸਕਾਰ ਪਲੇਟਫਾਰਮ ਹੈ, ਜੋ ਮਾਡਲਾਂ ਦੀ ਟ੍ਰੇਨਿੰਗ, fine-tuning ਅਤੇ ਫਾਊਂਡੇਸ਼ਨ ਮਾਡਲਾਂ ਦੀ ਵਰਤੋਂ ਲਈ ਸੰਦ ਪ੍ਰਦਾਨ ਕਰਦਾ ਹੈ। ਪਹਿਲਾਂ ਉਹਨਾਂ ਦੀ [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) ਗਾਈਡ ਪੜ੍ਹੋ, ਫਿਰ Cookbook ਅਭਿਆਸ ਕਰੋ।                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - Small Language Models ਲਈ fine-tuning                                                   | ਮਿਲੋ [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst) ਨਾਲ, Microsoft ਦਾ ਨਵਾਂ ਛੋਟਾ ਮਾਡਲ ਜੋ ਬਹੁਤ ਤਾਕਤਵਰ ਅਤੇ ਛੋਟਾ ਹੈ। ਇਹ ਟਿਊਟੋਰਿਯਲ ਤੁਹਾਨੂੰ Phi-2 ਨੂੰ fine-tune ਕਰਨ ਲਈ ਮਦਦ ਕਰੇਗਾ, ਵਿਲੱਖਣ ਡਾਟਾਸੈੱਟ ਬਣਾਉਣ ਅਤੇ QLoRA ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਮਾਡਲ ਨੂੰ fine-tune ਕਰਨ ਦਾ ਤਰੀਕਾ ਦਿਖਾਉਂਦਾ ਹੈ।                                                                                                                                                                       |
| **Hugging Face Tutorial** [How to Fine-Tune LLMs in 2024 with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | ਇਹ ਬਲੌਗ ਪੋਸਟ ਤੁਹਾਨੂੰ ਦਿਖਾਉਂਦੀ ਹੈ ਕਿ 2024 ਵਿੱਚ Hugging Face TRL, Transformers ਅਤੇ ਡਾਟਾਸੈੱਟ ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਖੁੱਲ੍ਹੇ LLMs ਨੂੰ ਕਿਵੇਂ fine-tune ਕਰਨਾ ਹੈ। ਤੁਸੀਂ ਇੱਕ ਉਪਯੋਗ ਮਾਮਲਾ ਪਰਿਭਾਸ਼ਿਤ ਕਰਦੇ ਹੋ, ਵਿਕਾਸ ਵਾਤਾਵਰਣ ਸੈੱਟ ਕਰਦੇ ਹੋ, ਡਾਟਾਸੈੱਟ ਤਿਆਰ ਕਰਦੇ ਹੋ, ਮਾਡਲ fine-tune ਕਰਦੇ ਹੋ, ਟੈਸਟ ਅਤੇ ਮੁਲਾਂਕਣ ਕਰਦੇ ਹੋ, ਫਿਰ ਇਸਨੂੰ ਪ੍ਰੋਡਕਸ਼ਨ ਵਿੱਚ ਤਾਇਨਾਤ ਕਰਦੇ ਹੋ।                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | [ਅਧੁਨਿਕ ਮਸ਼ੀਨ ਲਰਨਿੰਗ ਮਾਡਲਾਂ](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst) ਦੀ ਤੇਜ਼ ਅਤੇ ਆਸਾਨ ਟ੍ਰੇਨਿੰਗ ਅਤੇ ਤਾਇਨਾਤੀ ਲਈ। ਰਿਪੋ ਵਿੱਚ Colab-ਮਿਤ੍ਰ ਟਿਊਟੋਰਿਯਲ ਅਤੇ YouTube ਵੀਡੀਓ ਮਦਦ ਹੈ, fine-tuning ਲਈ। **ਹਾਲ ਹੀ ਵਿੱਚ [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) ਅੱਪਡੇਟ ਨੂੰ ਦਰਸਾਉਂਦਾ ਹੈ।** [AutoTrain ਦਸਤਾਵੇਜ਼](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) ਪੜ੍ਹੋ। |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**ਅਸਵੀਕਾਰੋਪੱਤਰ**:  
ਇਹ ਦਸਤਾਵੇਜ਼ AI ਅਨੁਵਾਦ ਸੇਵਾ [Co-op Translator](https://github.com/Azure/co-op-translator) ਦੀ ਵਰਤੋਂ ਕਰਕੇ ਅਨੁਵਾਦਿਤ ਕੀਤਾ ਗਿਆ ਹੈ। ਜਦੋਂ ਕਿ ਅਸੀਂ ਸਹੀਤਾ ਲਈ ਕੋਸ਼ਿਸ਼ ਕਰਦੇ ਹਾਂ, ਕਿਰਪਾ ਕਰਕੇ ਧਿਆਨ ਰੱਖੋ ਕਿ ਸਵੈਚਾਲਿਤ ਅਨੁਵਾਦਾਂ ਵਿੱਚ ਗਲਤੀਆਂ ਜਾਂ ਅਸਮਰਥਤਾਵਾਂ ਹੋ ਸਕਦੀਆਂ ਹਨ। ਮੂਲ ਦਸਤਾਵੇਜ਼ ਆਪਣੀ ਮੂਲ ਭਾਸ਼ਾ ਵਿੱਚ ਪ੍ਰਮਾਣਿਕ ਸਰੋਤ ਮੰਨਿਆ ਜਾਣਾ ਚਾਹੀਦਾ ਹੈ। ਮਹੱਤਵਪੂਰਨ ਜਾਣਕਾਰੀ ਲਈ, ਪੇਸ਼ੇਵਰ ਮਨੁੱਖੀ ਅਨੁਵਾਦ ਦੀ ਸਿਫਾਰਸ਼ ਕੀਤੀ ਜਾਂਦੀ ਹੈ। ਅਸੀਂ ਇਸ ਅਨੁਵਾਦ ਦੀ ਵਰਤੋਂ ਤੋਂ ਉਤਪੰਨ ਕਿਸੇ ਵੀ ਗਲਤਫਹਿਮੀ ਜਾਂ ਗਲਤ ਵਿਆਖਿਆ ਲਈ ਜ਼ਿੰਮੇਵਾਰ ਨਹੀਂ ਹਾਂ।