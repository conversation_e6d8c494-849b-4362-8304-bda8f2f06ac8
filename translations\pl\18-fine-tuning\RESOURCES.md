<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:01:22+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "pl"
}
-->
# Zasoby do samodzielnej nauki

Lekcja została opracowana na podstawie wielu kluczowych zasobów OpenAI i Azure OpenAI, które posłużyły jako odniesienie do terminologii i samouczków. Poniżej znajduje się niepełna lista, która może posłużyć do samodzielnej nauki.

## 1. Główne zasoby

| Tytuł/Link                                                                                                                                                                                                                 | Opis                                                                                                                                                                                                                                                                                                                                                                                        |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                     | Fine-tuning to ulepszenie uczenia na podstawie kilku przykładów (few-shot learning) poprzez trenowanie na znacznie większej liczbie przykładów niż mieści się w promptcie, co pozwala obniżyć koszty, poprawić jakość odpowiedzi i umożliwia szybsze zapytania. **Zapoznaj się z przeglądem fine-tuningu od OpenAI.**                                                                                 |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                     | Dowiedz się, **czym jest fine-tuning (koncepcja)**, dlaczego warto się nim zainteresować (motywacja), jakie dane wykorzystać (trening) oraz jak mierzyć jakość                                                                                                                                                                                                                              |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service pozwala dostosować modele do własnych zbiorów danych za pomocą fine-tuningu. Dowiedz się, **jak przeprowadzić fine-tuning (proces)** wybranych modeli korzystając z Azure AI Studio, Python SDK lub REST API.                                                                                                                                                          |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                  | Modele LLM mogą nie radzić sobie dobrze w specyficznych dziedzinach, zadaniach lub na określonych zbiorach danych, a także mogą generować niedokładne lub mylące odpowiedzi. **Kiedy warto rozważyć fine-tuning** jako potencjalne rozwiązanie?                                                                                                                                             |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Ciągły fine-tuning to iteracyjny proces wyboru już wytrenowanego modelu jako modelu bazowego i **dalszego jego dostrajania** na nowych zestawach przykładów treningowych.                                                                                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fine-tuning modelu **z przykładami wywołań funkcji** może poprawić jakość i spójność odpowiedzi modelu – uzyskując odpowiedzi o podobnym formacie oraz oszczędzając koszty                                                                                                                                                                                                                     |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Sprawdź tę tabelę, aby dowiedzieć się, **które modele można dostrajać** w Azure OpenAI oraz w jakich regionach są dostępne. Znajdziesz tam także limity tokenów i daty wygaśnięcia danych treningowych, jeśli są potrzebne.                                                                                                                                                                      |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Ten 30-minutowy odcinek AI Show z października 2023 omawia korzyści, wady i praktyczne wskazówki, które pomogą Ci podjąć decyzję o fine-tuningu.                                                                                                                                                                                                                                            |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                   | Ten zasób z AI Playbook przeprowadzi Cię przez wymagania dotyczące danych, formatowanie, dostrajanie hiperparametrów oraz wyzwania i ograniczenia, o których warto wiedzieć.                                                                                                                                                                                                                   |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Naucz się tworzyć przykładowy zestaw danych do fine-tuningu, przygotować się do fine-tuningu, utworzyć zadanie fine-tuningu i wdrożyć dostrojony model na platformie Azure.                                                                                                                                                                                                                   |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio pozwala dostosować duże modele językowe do własnych zbiorów danych _za pomocą interfejsu użytkownika, odpowiedniego dla deweloperów low-code_. Zobacz ten przykład.                                                                                                                                                                                                          |
| **Tutorial**: [Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Ten artykuł opisuje, jak dostroić model Hugging Face za pomocą biblioteki transformers na pojedynczym GPU, korzystając z Azure DataBricks i bibliotek Hugging Face Trainer.                                                                                                                                                                                                                     |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)             | Katalog modeli w Azure Machine Learning oferuje wiele modeli open source, które możesz dostroić do swojego konkretnego zadania. Wypróbuj ten moduł z [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)                                                                 |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fine-tuning modeli GPT-3.5 lub GPT-4 na Microsoft Azure z użyciem W&B pozwala na szczegółowe śledzenie i analizę wydajności modelu. Ten przewodnik rozszerza koncepcje z przewodnika OpenAI Fine-Tuning o konkretne kroki i funkcje dla Azure OpenAI.                                                                                                                                          |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                                                                                             |

## 2. Dodatkowe zasoby

Ta sekcja zawiera dodatkowe materiały warte poznania, których nie udało się omówić podczas tej lekcji. Mogą zostać poruszone w przyszłych lekcjach lub jako opcjonalne zadania dodatkowe. Na razie wykorzystaj je do rozwijania własnej wiedzy i umiejętności w tym temacie.

| Tytuł/Link                                                                                                                                                                                                                  | Opis                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Przygotowanie i analiza danych do fine-tuningu modelu chat](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                          | Ten notatnik służy do wstępnego przetwarzania i analizy zbioru danych chat używanego do fine-tuningu modelu konwersacyjnego. Sprawdza błędy formatowania, dostarcza podstawowe statystyki i szacuje liczbę tokenów potrzebnych do obliczenia kosztów fine-tuningu. Zobacz: [Metoda fine-tuningu dla gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                      |
| **OpenAI Cookbook**: [Fine-tuning dla Retrieval Augmented Generation (RAG) z Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)             | Celem tego notatnika jest przeprowadzenie kompleksowego przykładu, jak dostroić modele OpenAI do Retrieval Augmented Generation (RAG). Będziemy także integrować Qdrant i Few-Shot Learning, aby zwiększyć wydajność modelu i zmniejszyć liczbę błędnych odpowiedzi.                                                                                                                                                                                                                                         |
| **OpenAI Cookbook**: [Fine-tuning GPT z Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                                     | Weights & Biases (W&B) to platforma dla deweloperów AI, oferująca narzędzia do trenowania modeli, fine-tuningu i wykorzystania modeli bazowych. Najpierw przeczytaj ich przewodnik [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst), a następnie wypróbuj ćwiczenie z Cookbook.                                                                                                                                                                      |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning dla małych modeli językowych                                                        | Poznaj [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), nowy, mały model Microsoftu, zaskakująco potężny, a jednocześnie kompaktowy. Ten tutorial przeprowadzi Cię przez fine-tuning Phi-2, pokazując, jak zbudować unikalny zestaw danych i dostroić model za pomocą QLoRA.                                                                                                                                                      |
| **Hugging Face Tutorial** [Jak dostroić LLM w 2024 z Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                                               | Ten wpis na blogu pokazuje, jak dostroić otwarte modele LLM za pomocą Hugging Face TRL, Transformers i datasets w 2024 roku. Definiujesz przypadek użycia, konfigurujesz środowisko, przygotowujesz zestaw danych, dostrajasz model, testujesz i oceniasz go, a następnie wdrażasz do produkcji.                                                                                                                                                                                               |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                                  | Umożliwia szybsze i łatwiejsze trenowanie oraz wdrażanie [nowoczesnych modeli uczenia maszynowego](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Repozytorium zawiera tutoriale przyjazne dla Colab oraz przewodniki wideo na YouTube dotyczące fine-tuningu. **Uwzględnia najnowszą aktualizację [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst).** Przeczytaj [dokumentację AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Zastrzeżenie**:  
Niniejszy dokument został przetłumaczony za pomocą usługi tłumaczenia AI [Co-op Translator](https://github.com/Azure/co-op-translator). Mimo że dążymy do dokładności, prosimy mieć na uwadze, że automatyczne tłumaczenia mogą zawierać błędy lub nieścisłości. Oryginalny dokument w języku źródłowym powinien być uznawany za źródło autorytatywne. W przypadku informacji o kluczowym znaczeniu zalecane jest skorzystanie z profesjonalnego tłumaczenia wykonanego przez człowieka. Nie ponosimy odpowiedzialności za jakiekolwiek nieporozumienia lub błędne interpretacje wynikające z korzystania z tego tłumaczenia.