<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:31:42+00:00",
  "source_file": "README.md",
  "language_code": "pl"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.pl.png)

### 21 lekc<PERSON> u<PERSON><PERSON>ch wszystkiego, co mus<PERSON><PERSON> w<PERSON>, aby zac<PERSON><PERSON><PERSON> tworzyć aplikacje Generative AI

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Wsparcie wielojęzyczne

#### Obsługiwane przez GitHub Action (Automatyczne i zawsze aktualne)

[Francuski](../fr/README.md) | [Hiszpański](../es/README.md) | [Niemiecki](../de/README.md) | [Rosyjski](../ru/README.md) | [Arabski](../ar/README.md) | [Perski (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chiński (uproszczony)](../zh/README.md) | [Chiński (tradycyjny, Makau)](../mo/README.md) | [Chiński (tradycyjny, Hongkong)](../hk/README.md) | [Chiński (tradycyjny, Tajwan)](../tw/README.md) | [Japoński](../ja/README.md) | [Koreański](../ko/README.md) | [Hindi](../hi/README.md) | [Bengalski](../bn/README.md) | [Marathi](../mr/README.md) | [Nepalski](../ne/README.md) | [Pendżabski (Gurmukhi)](../pa/README.md) | [Portugalski (Portugalia)](../pt/README.md) | [Portugalski (Brazylia)](../br/README.md) | [Włoski](../it/README.md) | [Polski](./README.md) | [Turecki](../tr/README.md) | [Grecki](../el/README.md) | [Tajski](../th/README.md) | [Szwedzki](../sv/README.md) | [Duński](../da/README.md) | [Norweski](../no/README.md) | [Fiński](../fi/README.md) | [Niderlandzki](../nl/README.md) | [Hebrajski](../he/README.md) | [Wietnamski](../vi/README.md) | [Indonezyjski](../id/README.md) | [Malajski](../ms/README.md) | [Tagalog (Filipiński)](../tl/README.md) | [Suahili](../sw/README.md) | [Węgierski](../hu/README.md) | [Czeski](../cs/README.md) | [Słowacki](../sk/README.md) | [Rumuński](../ro/README.md) | [Bułgarski](../bg/README.md) | [Serbski (cyrylica)](../sr/README.md) | [Chorwacki](../hr/README.md) | [Słoweński](../sl/README.md) | [Ukraiński](../uk/README.md) | [Birmański (Myanmar)](../my/README.md)

# Generative AI dla początkujących (Wersja 3) - Kurs

Poznaj podstawy tworzenia aplikacji Generative AI dzięki naszemu kompleksowemu kursowi składającemu się z 21 lekcji, przygotowanemu przez Microsoft Cloud Advocates.

## 🌱 Zacznijmy

Kurs składa się z 21 lekcji. Każda lekcja omawia inny temat, więc zacznij od dowolnej!

Lekcje są oznaczone jako "Learn" – wyjaśniające koncepcje Generative AI lub "Build" – wyjaśniające koncepcję wraz z przykładami kodu w **Python** i **TypeScript**, jeśli to możliwe.

Dla programistów .NET polecamy [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

Każda lekcja zawiera także sekcję "Keep Learning" z dodatkowymi materiałami do nauki.

## Czego potrzebujesz
### Aby uruchomić kod z tego kursu, możesz skorzystać z: 
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **Lekcje:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **Lekcje:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **Lekcje:** "oai-assignment" 
   
- Podstawowa znajomość Pythona lub TypeScript będzie pomocna - \*Dla zupełnie początkujących polecamy te kursy [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) i [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst)
- Konto GitHub, aby [sforkować całe to repozytorium](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) na swoje konto GitHub

Stworzyliśmy lekcję **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)**, która pomoże Ci skonfigurować środowisko programistyczne.

Nie zapomnij [dodać repozytorium do ulubionych (🌟)](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst), aby łatwiej je znaleźć później.

## 🧠 Gotowy do wdrożenia?

Jeśli szukasz bardziej zaawansowanych przykładów kodu, sprawdź naszą [kolekcję przykładów kodu Generative AI](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) w **Python** i **TypeScript**.

## 🗣️ Poznaj innych uczących się, uzyskaj wsparcie

Dołącz do naszego [oficjalnego serwera Discord Azure AI Foundry](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst), aby poznać innych uczestników kursu i uzyskać pomoc.

Zadaj pytania lub podziel się opinią o produkcie na naszym [forum deweloperów Azure AI Foundry](https://aka.ms/azureaifoundry/forum) na GitHub.

## 🚀 Budujesz startup?

Zarejestruj się w [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst), aby otrzymać **darmowe kredyty OpenAI** oraz do **150 000 USD na kredyty Azure, umożliwiające dostęp do modeli OpenAI przez Azure OpenAI Services**.

## 🙏 Chcesz pomóc?

Masz sugestie lub znalazłeś błędy ortograficzne albo w kodzie? [Zgłoś problem](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) lub [stwórz pull request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Każda lekcja zawiera:

- Krótkie wideo wprowadzające do tematu
- Pisemną lekcję w pliku README
- Przykłady kodu w Python i TypeScript wspierające Azure OpenAI i OpenAI API
- Linki do dodatkowych materiałów do dalszej nauki

## 🗃️ Lekcje

| #   | **Link do lekcji**                                                                                                                          | **Opis**                                                                                      | **Wideo**                                                                   | **Dodatkowa nauka**                                                            |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Learn:** Jak skonfigurować środowisko programistyczne                                      | Wideo wkrótce                                                                | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Wprowadzenie do Generative AI i LLM](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                                  | **Learn:** Zrozumienie czym jest Generative AI i jak działają duże modele językowe (LLM)      | [Wideo](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Eksploracja i porównanie różnych LLM](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)                 | **Learn:** Jak wybrać odpowiedni model do swojego zastosowania                               | [Wideo](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Odpowiedzialne korzystanie z Generative AI](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                   | **Learn:** Jak odpowiedzialnie tworzyć aplikacje Generative AI                               | [Wideo](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Podstawy inżynierii promptów](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)                               | **Learn:** Praktyczne najlepsze praktyki inżynierii promptów                                | [Wideo](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Tworzenie zaawansowanych promptów](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                          | **Learn:** Jak stosować techniki inżynierii promptów, które poprawiają efekty Twoich promptów | [Wideo](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Tworzenie aplikacji do generowania tekstu](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Buduj:** Aplikację do generowania tekstu z wykorzystaniem Azure OpenAI / OpenAI API             | [Wideo](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Tworzenie aplikacji czatu](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                         | **Buduj:** Techniki efektywnego tworzenia i integracji aplikacji czatu                            | [Wideo](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Tworzenie aplikacji wyszukiwania z bazami wektorowymi](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)            | **Buduj:** Aplikację wyszukującą dane za pomocą Embeddings                                      | [Wideo](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Tworzenie aplikacji do generowania obrazów](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Buduj:** Aplikację do generowania obrazów                                                    | [Wideo](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Tworzenie aplikacji AI z niskim kodem](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Buduj:** Aplikację Generative AI wykorzystującą narzędzia Low Code                            | [Wideo](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Integracja zewnętrznych aplikacji za pomocą Function Calling](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **Buduj:** Czym jest function calling i jak można go wykorzystać w aplikacjach                  | [Wideo](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [Projektowanie UX dla aplikacji AI](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                             | **Ucz się:** Jak stosować zasady projektowania UX podczas tworzenia aplikacji Generative AI     | [Wideo](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Zabezpieczanie aplikacji Generative AI](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                               | **Ucz się:** Zagrożenia i ryzyka dla systemów AI oraz metody ich zabezpieczania                  | [Wideo](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Cykl życia aplikacji Generative AI](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)                   | **Ucz się:** Narzędzia i metryki do zarządzania cyklem życia LLM i LLMOps                        | [Wideo](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) i bazy wektorowe](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)                | **Buduj:** Aplikację wykorzystującą framework RAG do pobierania embeddings z baz wektorowych   | [Wideo](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Modele open source i Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                          | **Buduj:** Aplikację wykorzystującą modele open source dostępne na Hugging Face                 | [Wideo](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [Agenci AI](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                           | **Buduj:** Aplikację wykorzystującą framework AI Agent                                         | [Wideo](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [Fine-Tuning LLMs](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                                  | **Ucz się:** Co to jest, dlaczego i jak przeprowadzać fine-tuning LLM                           | [Wideo](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Tworzenie z SLMs](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                                         | **Ucz się:** Korzyści z tworzenia z wykorzystaniem Small Language Models                        | Wideo wkrótce | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Tworzenie z modelami Mistral](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                         | **Ucz się:** Cechy i różnice modeli z rodziny Mistral                                          | Wideo wkrótce | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Tworzenie z modelami Meta](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                               | **Ucz się:** Cechy i różnice modeli z rodziny Meta                                            | Wideo wkrótce | [Dowiedz się więcej](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Specjalne podziękowania

Specjalne podziękowania dla [**John Aziz**](https://www.linkedin.com/in/john0isaac/) za stworzenie wszystkich GitHub Actions i workflowów

[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) za kluczowe wkłady w każdą lekcję, które poprawiły doświadczenie uczących się i jakość kodu.

## 🎒 Inne kursy

Nasz zespół tworzy także inne kursy! Sprawdź:

- [**NOWY** Model Context Protocol dla początkujących](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI Agents dla początkujących](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Generative AI dla początkujących z użyciem .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Generative AI dla początkujących z użyciem JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML dla początkujących](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science dla początkujących](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI dla początkujących](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Cyberbezpieczeństwo dla początkujących](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Web Dev dla początkujących](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT dla początkujących](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR Development dla początkujących](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Opanowanie GitHub Copilot do programowania w parach z AI](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Opanowanie GitHub Copilot dla programistów C#/.NET](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Wybierz swoją własną przygodę z Copilotem](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Zastrzeżenie**:  
Niniejszy dokument został przetłumaczony za pomocą usługi tłumaczenia AI [Co-op Translator](https://github.com/Azure/co-op-translator). Mimo że dążymy do dokładności, prosimy mieć na uwadze, że automatyczne tłumaczenia mogą zawierać błędy lub nieścisłości. Oryginalny dokument w języku źródłowym powinien być uznawany za źródło autorytatywne. W przypadku informacji krytycznych zalecane jest skorzystanie z profesjonalnego tłumaczenia wykonanego przez człowieka. Nie ponosimy odpowiedzialności za jakiekolwiek nieporozumienia lub błędne interpretacje wynikające z korzystania z tego tłumaczenia.