<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:53:23+00:00",
  "source_file": "SECURITY.md",
  "language_code": "pl"
}
-->
## Bezpieczeństwo

Microsoft traktuje bezpieczeństwo naszych produktów i usług programowych bardzo poważnie, co obejmuje wszystkie repozytoria kodu źródłowego zarządzane przez nasze organizacje na GitHub, w tym [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) oraz [nasze organizacje na GitHub](https://opensource.microsoft.com/).

<PERSON><PERSON><PERSON>, że znalazłeś lukę bezpieczeństwa w którymkolwiek z repozytoriów należących do Microsoft, która spełnia [definicję luki bezpieczeństwa Microsoft](https://aka.ms/opensource/security/definition), prosimy o zgłoszenie jej zgodnie z opisem poniżej.

## Zgłaszanie problemów związanych z bezpieczeństwem

**Prosimy nie zgłaszać luk bezpieczeństwa poprzez publiczne zgłoszenia na GitHub.**

Zamiast tego prosimy o zgłaszanie ich do Microsoft Security Response Center (MSRC) pod adresem [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Jeśli wolisz zgłosić problem bez logowania, wyślij wiadomość e-mail na adres [<EMAIL>](mailto:<EMAIL>). Jeśli to możliwe, zaszyfruj swoją wiadomość za pomocą naszego klucza PGP; możesz go pobrać ze strony [Microsoft Security Response Center PGP Key](https://aka.ms/opensource/security/pgpkey).

Powinieneś otrzymać odpowiedź w ciągu 24 godzin. Jeśli z jakiegoś powodu nie otrzymasz odpowiedzi, prosimy o ponowny kontakt mailowy, aby upewnić się, że otrzymaliśmy Twoją wiadomość. Dodatkowe informacje znajdziesz na [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Prosimy o dołączenie poniższych informacji (w miarę możliwości), które pomogą nam lepiej zrozumieć charakter i zakres potencjalnego problemu:

  * Rodzaj problemu (np. przepełnienie bufora, SQL injection, cross-site scripting itp.)
  * Pełne ścieżki plików źródłowych związanych z wystąpieniem problemu
  * Lokalizacja dotkniętego kodu źródłowego (tag/branch/commit lub bezpośredni URL)
  * Specjalna konfiguracja wymagana do odtworzenia problemu
  * Instrukcje krok po kroku, jak odtworzyć problem
  * Kod proof-of-concept lub exploit (jeśli to możliwe)
  * Wpływ problemu, w tym sposób, w jaki atakujący mógłby go wykorzystać

Te informacje pomogą nam szybciej przeanalizować Twoje zgłoszenie.

Jeśli zgłaszasz problem w ramach programu bug bounty, bardziej szczegółowe raporty mogą przyczynić się do wyższej nagrody. Więcej informacji o naszych aktywnych programach znajdziesz na stronie [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty).

## Preferowane języki

Preferujemy, aby cała komunikacja odbywała się w języku angielskim.

## Polityka

Microsoft stosuje zasadę [Koordynowanego Ujawniania Luk Bezpieczeństwa](https://aka.ms/opensource/security/cvd).

**Zastrzeżenie**:  
Niniejszy dokument został przetłumaczony za pomocą usługi tłumaczenia AI [Co-op Translator](https://github.com/Azure/co-op-translator). Chociaż dokładamy starań, aby tłumaczenie było jak najbardziej precyzyjne, prosimy mieć na uwadze, że automatyczne tłumaczenia mogą zawierać błędy lub nieścisłości. Oryginalny dokument w języku źródłowym należy traktować jako źródło autorytatywne. W przypadku informacji o kluczowym znaczeniu zalecane jest skorzystanie z profesjonalnego tłumaczenia wykonanego przez człowieka. Nie ponosimy odpowiedzialności za jakiekolwiek nieporozumienia lub błędne interpretacje wynikające z korzystania z tego tłumaczenia.