<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:00:18+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "pt"
}
-->
# Recursos para Aprendizagem Autónoma

A lição foi construída utilizando vários recursos principais da OpenAI e Azure OpenAI como referência para a terminologia e tutoriais. Aqui está uma lista não exaustiva, para as suas próprias jornadas de aprendizagem autónoma.

## 1. Recursos Principais

| Título/Link                                                                                                                                                                                                                 | Descrição                                                                                                                                                                                                                                                                                                                                                                                     |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | O fine-tuning melhora o few-shot learning ao treinar com muitos mais exemplos do que os que cabem no prompt, poupando custos, melhorando a qualidade das respostas e permitindo pedidos com menor latência. **Obtenha uma visão geral do fine-tuning pela OpenAI.**                                                                                                                               |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Compreenda **o que é o fine-tuning (conceito)**, por que deve considerá-lo (problema motivador), que dados usar (treino) e como medir a qualidade                                                                                                                                                                               |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | O Azure OpenAI Service permite-lhe adaptar os nossos modelos aos seus próprios conjuntos de dados usando fine-tuning. Aprenda **como fazer fine-tuning (processo)** e selecionar modelos usando Azure AI Studio, Python SDK ou REST API.                                                                                                                                                      |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | Os LLMs podem não ter bom desempenho em domínios, tarefas ou conjuntos de dados específicos, ou podem produzir resultados imprecisos ou enganosos. **Quando deve considerar o fine-tuning** como solução possível para isto?                                                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | O fine-tuning contínuo é o processo iterativo de selecionar um modelo já fine-tuned como modelo base e **fazer fine-tuning adicional** com novos conjuntos de exemplos de treino.                                                                                                                                                   |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fazer fine-tuning do seu modelo **com exemplos de chamadas de função** pode melhorar a saída do modelo, obtendo respostas mais precisas e consistentes - com respostas formatadas de forma semelhante e poupança de custos                                                                                                          |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Consulte esta tabela para perceber **quais modelos podem ser fine-tuned** no Azure OpenAI, e em que regiões estão disponíveis. Verifique os limites de tokens e datas de expiração dos dados de treino, se necessário.                                                                                                              |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Este episódio de 30 minutos do AI Show, de **outubro de 2023**, discute benefícios, desvantagens e insights práticos que o ajudam a tomar esta decisão.                                                                                                                                                                           |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Este recurso do **AI Playbook** guia-o pelos requisitos de dados, formatação, ajuste de hiperparâmetros e desafios/limitações que deve conhecer.                                                                                                                                                                                |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Aprenda a criar um conjunto de dados de fine-tuning de exemplo, preparar o fine-tuning, criar um trabalho de fine-tuning e implementar o modelo fine-tuned no Azure.                                                                                                                                                              |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | O Azure AI Studio permite-lhe adaptar grandes modelos de linguagem aos seus próprios conjuntos de dados _usando um fluxo de trabalho baseado em UI adequado para programadores low-code_. Veja este exemplo.                                                                                                                     |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Este artigo descreve como fazer fine-tuning de um modelo Hugging Face com a biblioteca transformers da Hugging Face numa única GPU com Azure DataBricks + bibliotecas Hugging Face Trainer                                                                                                                                        |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | O catálogo de modelos do Azure Machine Learning oferece muitos modelos open source que pode fine-tunar para a sua tarefa específica. Experimente este módulo que faz parte do [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fazer fine-tuning dos modelos GPT-3.5 ou GPT-4 na Microsoft Azure usando W&B permite um acompanhamento detalhado e análise do desempenho do modelo. Este guia expande os conceitos do guia de Fine-Tuning da OpenAI com passos e funcionalidades específicas para Azure OpenAI.                                                                                                         |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                                                                                              |

## 2. Recursos Secundários

Esta secção reúne recursos adicionais que vale a pena explorar, mas que não tivemos tempo para abordar nesta lição. Podem ser abordados numa lição futura, ou como opção de tarefa secundária, numa data posterior. Por agora, use-os para construir a sua própria experiência e conhecimento sobre este tema.

| Título/Link                                                                                                                                                                                                            | Descrição                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Preparação e análise de dados para fine-tuning de modelos de chat](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                              | Este notebook serve como ferramenta para pré-processar e analisar o conjunto de dados de chat usado para fine-tuning de um modelo de chat. Verifica erros de formato, fornece estatísticas básicas e estima o número de tokens para calcular custos de fine-tuning. Veja: [Método de fine-tuning para gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning para Retrieval Augmented Generation (RAG) com Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | O objetivo deste notebook é apresentar um exemplo completo de como fazer fine-tuning de modelos OpenAI para Retrieval Augmented Generation (RAG). Também integraremos Qdrant e Few-Shot Learning para melhorar o desempenho do modelo e reduzir invenções.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT com Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) é a plataforma para desenvolvedores de IA, com ferramentas para treinar modelos, fazer fine-tuning e aproveitar modelos base. Leia primeiro o seu guia [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) e depois experimente o exercício do Cookbook.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning para Pequenos Modelos de Linguagem                                                   | Conheça o [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), o novo pequeno modelo da Microsoft, surpreendentemente poderoso e compacto. Este tutorial guia-o no fine-tuning do Phi-2, mostrando como construir um conjunto de dados único e fazer fine-tuning do modelo usando QLoRA.                                                                                                                                                                       |
| **Hugging Face Tutorial** [Como fazer Fine-Tuning de LLMs em 2024 com Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Este artigo explica como fazer fine-tuning de LLMs abertos usando Hugging Face TRL, Transformers e datasets em 2024. Define um caso de uso, configura o ambiente de desenvolvimento, prepara um conjunto de dados, faz fine-tuning do modelo, testa e avalia, e depois implementa em produção.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Facilita o treino e implementação mais rápidos e fáceis de [modelos de machine learning de última geração](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). O repositório tem tutoriais compatíveis com Colab e vídeos no YouTube para orientação no fine-tuning. **Reflete a recente atualização [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)**. Leia a [documentação do AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Aviso Legal**:  
Este documento foi traduzido utilizando o serviço de tradução automática [Co-op Translator](https://github.com/Azure/co-op-translator). Embora nos esforcemos pela precisão, por favor tenha em conta que traduções automáticas podem conter erros ou imprecisões. O documento original na sua língua nativa deve ser considerado a fonte autorizada. Para informações críticas, recomenda-se tradução profissional humana. Não nos responsabilizamos por quaisquer mal-entendidos ou interpretações incorretas decorrentes da utilização desta tradução.