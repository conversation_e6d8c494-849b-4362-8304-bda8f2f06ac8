<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:52:57+00:00",
  "source_file": "SECURITY.md",
  "language_code": "pt"
}
-->
## Segurança

A Microsoft leva a sério a segurança dos nossos produtos e serviços de software, incluindo todos os repositórios de código-fonte geridos através das nossas organizações no GitHub, que incluem [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) e [as nossas organizações no GitHub](https://opensource.microsoft.com/).

Se acredita ter encontrado uma vulnerabilidade de segurança em algum repositório propriedade da Microsoft que cumpra a [definição de vulnerabilidade de segurança da Microsoft](https://aka.ms/opensource/security/definition), por favor reporte-a conforme descrito abaixo.

## Reportar Problemas de Segurança

**Por favor, não reporte vulnerabilidades de segurança através de issues públicas no GitHub.**

Em vez disso, reporte-as ao Microsoft Security Response Center (MSRC) em [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Se preferir submeter sem iniciar sessão, envie um email para [<EMAIL>](mailto:<EMAIL>). Se possível, encripte a sua mensagem com a nossa chave PGP; por favor, faça o download na página da [Microsoft Security Response Center PGP Key](https://aka.ms/opensource/security/pgpkey).

Deverá receber uma resposta dentro de 24 horas. Se por algum motivo não receber, por favor faça um seguimento por email para garantir que recebemos a sua mensagem original. Informação adicional pode ser encontrada em [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Por favor, inclua a informação solicitada abaixo (tanto quanto possível) para nos ajudar a compreender melhor a natureza e o alcance do possível problema:

  * Tipo de problema (ex.: buffer overflow, SQL injection, cross-site scripting, etc.)
  * Caminhos completos dos ficheiros fonte relacionados com a manifestação do problema
  * A localização do código fonte afetado (tag/branch/commit ou URL direto)
  * Qualquer configuração especial necessária para reproduzir o problema
  * Instruções passo a passo para reproduzir o problema
  * Código de prova de conceito ou exploit (se possível)
  * Impacto do problema, incluindo como um atacante poderia explorar a vulnerabilidade

Esta informação ajudará a triagem do seu relatório de forma mais rápida.

Se estiver a reportar para um programa de recompensa por bugs, relatórios mais completos podem contribuir para uma recompensa maior. Por favor, visite a nossa página do [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) para mais detalhes sobre os nossos programas ativos.

## Línguas Preferidas

Preferimos que todas as comunicações sejam feitas em inglês.

## Política

A Microsoft segue o princípio de [Divulgação Coordenada de Vulnerabilidades](https://aka.ms/opensource/security/cvd).

**Aviso Legal**:  
Este documento foi traduzido utilizando o serviço de tradução automática [Co-op Translator](https://github.com/Azure/co-op-translator). Embora nos esforcemos pela precisão, por favor tenha em conta que traduções automáticas podem conter erros ou imprecisões. O documento original na sua língua nativa deve ser considerado a fonte autorizada. Para informações críticas, recomenda-se tradução profissional humana. Não nos responsabilizamos por quaisquer mal-entendidos ou interpretações erradas decorrentes da utilização desta tradução.