<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:08:34+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "ro"
}
-->
# Resurse pentru Învățare Autonomă

Lecția a fost construită folosind o serie de resurse principale de la OpenAI și Azure OpenAI ca referințe pentru terminologie și tutoriale. Iată o listă neexhaustivă, pentru propriile tale călătorii de învățare autonomă.

## 1. Resurse Primare

| Titlu/Link                                                                                                                                                                                                                   | Descriere                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning îmbunătățește învățarea cu puține exemple prin antrenarea pe un număr mult mai mare de exemple decât pot încăpea în prompt, economisindu-ți costuri, îmbunătățind calitatea răspunsurilor și permițând cereri cu latență mai mică. **Obține o privire de ansamblu asupra fine-tuning-ului de la OpenAI.**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Înțelege **ce este fine-tuning-ul (conceptul)**, de ce ar trebui să îl iei în considerare (problema motivantă), ce date să folosești (antrenament) și cum să măsori calitatea                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service îți permite să personalizezi modelele noastre pe seturile tale de date folosind fine-tuning. Află **cum să faci fine-tuning (procesul)** și să selectezi modele folosind Azure AI Studio, Python SDK sau REST API.                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM-urile pot să nu performeze bine pe domenii, sarcini sau seturi de date specifice, sau pot produce rezultate inexacte sau înșelătoare. **Când ar trebui să iei în considerare fine-tuning-ul** ca o posibilă soluție?                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Fine-tuning-ul continuu este procesul iterativ de a selecta un model deja fine-tuned ca model de bază și **a-l ajusta în continuare** pe seturi noi de exemple de antrenament.                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fine-tuning-ul modelului tău **cu exemple de apelare a funcțiilor** poate îmbunătăți rezultatele modelului prin obținerea unor răspunsuri mai precise și consistente - cu răspunsuri formatate similar și economii de costuri                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Consultă acest tabel pentru a înțelege **ce modele pot fi fine-tuned** în Azure OpenAI și în ce regiuni sunt disponibile. Verifică limitele de tokeni și datele de expirare ale datelor de antrenament, dacă este necesar.                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Acest episod de 30 de minute din **octombrie 2023** al AI Show discută beneficiile, dezavantajele și perspective practice care te ajută să iei această decizie.                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Această resursă din **AI Playbook** te ghidează prin cerințele de date, formatare, ajustarea hiperparametrilor și provocările/limitările pe care ar trebui să le cunoști.                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Învață să creezi un set de date de fine-tuning, să te pregătești pentru fine-tuning, să creezi un job de fine-tuning și să implementezi modelul fine-tuned pe Azure.                                                                                                                                                                                    |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio îți permite să personalizezi modele mari de limbaj pe seturile tale de date _folosind un flux de lucru bazat pe UI, potrivit pentru dezvoltatori low-code_. Vezi acest exemplu.                                                                                                                                                               |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Acest articol descrie cum să faci fine-tuning unui model Hugging Face cu biblioteca Hugging Face transformers pe un singur GPU folosind Azure DataBricks + bibliotecile Hugging Face Trainer                                                                                                                                                |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Catalogul de modele din Azure Machine Learning oferă multe modele open source pe care le poți fine-tune pentru sarcina ta specifică. Încearcă acest modul din [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fine-tuning-ul modelelor GPT-3.5 sau GPT-4 pe Microsoft Azure folosind W&B permite urmărirea și analiza detaliată a performanței modelului. Acest ghid extinde conceptele din ghidul OpenAI Fine-Tuning cu pași și funcționalități specifice pentru Azure OpenAI.                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. Resurse Secundare

Această secțiune cuprinde resurse suplimentare care merită explorate, dar pe care nu am avut timp să le acoperim în această lecție. Ele pot fi abordate într-o lecție viitoare sau ca opțiune secundară de temă, la o dată ulterioară. Pentru moment, folosește-le pentru a-ți construi propria expertiză și cunoștințe pe acest subiect.

| Titlu/Link                                                                                                                                                                                                            | Descriere                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Pregătirea și analiza datelor pentru fine-tuning-ul modelelor de chat](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | Acest notebook servește ca un instrument pentru preprocesarea și analiza setului de date de chat folosit pentru fine-tuning-ul unui model de chat. Verifică erorile de format, oferă statistici de bază și estimează numărul de tokeni pentru costurile fine-tuning-ului. Vezi: [Metoda de fine-tuning pentru gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning pentru Retrieval Augmented Generation (RAG) cu Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | Scopul acestui notebook este să parcurgă un exemplu complet despre cum să faci fine-tuning modelelor OpenAI pentru Retrieval Augmented Generation (RAG). Vom integra și Qdrant și Few-Shot Learning pentru a crește performanța modelului și a reduce fabricările.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT cu Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) este platforma pentru dezvoltatori AI, cu unelte pentru antrenarea modelelor, fine-tuning și utilizarea modelelor fundamentale. Citește mai întâi ghidul lor [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst), apoi încearcă exercițiul din Cookbook.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning pentru modele mici de limbaj                                                   | Descoperă [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), noul model mic de la Microsoft, surprinzător de puternic și compact. Acest tutorial te va ghida prin fine-tuning-ul lui Phi-2, demonstrând cum să construiești un set de date unic și să faci fine-tuning folosind QLoRA.                                                                                                                                                                       |
| **Hugging Face Tutorial** [Cum să faci fine-tuning LLM-uri în 2024 cu Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Acest articol de blog te ghidează cum să faci fine-tuning modelelor LLM open folosind Hugging Face TRL, Transformers și seturi de date în 2024. Definiți un caz de utilizare, configurezi un mediu de dezvoltare, pregătești un set de date, faci fine-tuning modelului, îl testezi și evaluezi, apoi îl implementezi în producție.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Oferă antrenamente și implementări mai rapide și mai ușoare ale [celor mai avansate modele de machine learning](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Repo conține tutoriale prietenoase cu Colab și ghid video pe YouTube pentru fine-tuning. **Reflectă actualizarea recentă [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)**. Citește [documentația AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Declinare de responsabilitate**:  
Acest document a fost tradus folosind serviciul de traducere AI [Co-op Translator](https://github.com/Azure/co-op-translator). Deși ne străduim pentru acuratețe, vă rugăm să rețineți că traducerile automate pot conține erori sau inexactități. Documentul original în limba sa nativă trebuie considerat sursa autorizată. Pentru informații critice, se recomandă traducerea profesională realizată de un specialist uman. Nu ne asumăm răspunderea pentru eventualele neînțelegeri sau interpretări greșite rezultate din utilizarea acestei traduceri.