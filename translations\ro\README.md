<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:44:45+00:00",
  "source_file": "README.md",
  "language_code": "ro"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.ro.png)

### 21 Lecții care te învață tot ce trebuie să știi pentru a începe să construiești aplicații Generative AI

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Suport Multilingv

#### Susținut prin GitHub Action (Automatizat & Întotdeauna Actualizat)

[Franceză](../fr/README.md) | [Spaniolă](../es/README.md) | [Germană](../de/README.md) | [Rusă](../ru/README.md) | [Arabă](../ar/README.md) | [Persană (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chineză (Simplificată)](../zh/README.md) | [Chineză (Tradițională, Macau)](../mo/README.md) | [Chineză (Tradițională, Hong Kong)](../hk/README.md) | [Chineză (Tradițională, Taiwan)](../tw/README.md) | [Japoneză](../ja/README.md) | [Coreeană](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepaleză](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portugheză (Portugalia)](../pt/README.md) | [Portugheză (Brazilia)](../br/README.md) | [Italiană](../it/README.md) | [Poloneză](../pl/README.md) | [Turcă](../tr/README.md) | [Greacă](../el/README.md) | [Thailandeză](../th/README.md) | [Suedeză](../sv/README.md) | [Daneză](../da/README.md) | [Norvegiană](../no/README.md) | [Finlandeză](../fi/README.md) | [Olandeză](../nl/README.md) | [Ebraică](../he/README.md) | [Vietnameză](../vi/README.md) | [Indoneziană](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipineză)](../tl/README.md) | [Swahili](../sw/README.md) | [Maghiară](../hu/README.md) | [Cehă](../cs/README.md) | [Slovacă](../sk/README.md) | [Română](./README.md) | [Bulgară](../bg/README.md) | [Sârbă (Chirilică)](../sr/README.md) | [Croată](../hr/README.md) | [Slovenă](../sl/README.md) | [Ucraineană](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Generative AI pentru Începători (Versiunea 3) - Un Curs

Învață elementele de bază pentru a construi aplicații Generative AI cu ajutorul cursului nostru cuprinzător de 21 de lecții, creat de Microsoft Cloud Advocates.

## 🌱 Începutul

Acest curs are 21 de lecții. Fiecare lecție abordează un subiect propriu, așa că poți începe de oriunde dorești!

Lecțiile sunt etichetate fie ca lecții „Learn” care explică un concept Generative AI, fie ca lecții „Build” care explică un concept și oferă exemple de cod în **Python** și **TypeScript**, atunci când este posibil.

Pentru dezvoltatorii .NET, consultați [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

Fiecare lecție include și o secțiune „Keep Learning” cu instrumente suplimentare de învățare.

## Ce ai nevoie
### Pentru a rula codul acestui curs, poți folosi oricare dintre:
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **Lecții:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **Lecții:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **Lecții:** "oai-assignment" 
   
- Cunoștințe de bază de Python sau TypeScript sunt utile - \*Pentru începători absoluți, consultați aceste cursuri [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) și [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst)
- Un cont GitHub pentru a [face fork la întregul repo](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) în propriul tău cont GitHub

Am creat o lecție **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** pentru a te ajuta să-ți configurezi mediul de dezvoltare.

Nu uita să [acordezi o stea (🌟) acestui repo](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst) pentru a-l găsi mai ușor mai târziu.

## 🧠 Gata să implementezi?

Dacă vrei exemple de cod mai avansate, consultă colecția noastră de [Exemple de Cod Generative AI](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) în **Python** și **TypeScript**.

## 🗣️ Întâlnește alți cursanți, primește suport

Alătură-te serverului nostru oficial [Azure AI Foundry Discord](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) pentru a cunoaște și a face networking cu alți cursanți care urmează acest curs și pentru a primi suport.

Pune întrebări sau oferă feedback despre produs în [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) pe Github.

## 🚀 Construiești un startup?

Înscrie-te la [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) pentru a primi **credite gratuite OpenAI** și până la **150.000 $ în credite Azure pentru a accesa modelele OpenAI prin Azure OpenAI Services**.

## 🙏 Vrei să ajuți?

Ai sugestii sau ai găsit greșeli de ortografie ori în cod? [Deschide un issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) sau [creează un pull request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Fiecare lecție include:

- O scurtă introducere video în subiect
- O lecție scrisă în README
- Exemple de cod Python și TypeScript care suportă Azure OpenAI și OpenAI API
- Linkuri către resurse suplimentare pentru a-ți continua învățarea

## 🗃️ Lecții

| #   | **Link Lecție**                                                                                                                              | **Descriere**                                                                                 | **Video**                                                                   | **Învățare suplimentară**                                                     |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Configurarea cursului](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                         | **Learn:** Cum să-ți configurezi mediul de dezvoltare                                        | Video în curând                                                             | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Introducere în Generative AI și LLM-uri](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                              | **Learn:** Înțelegerea ce este Generative AI și cum funcționează modelele mari de limbaj (LLM) | [Video](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Explorarea și compararea diferitelor LLM-uri](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)         | **Learn:** Cum să alegi modelul potrivit pentru cazul tău de utilizare                        | [Video](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Folosirea responsabilă a Generative AI](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                       | **Learn:** Cum să construiești aplicații Generative AI responsabil                            | [Video](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Înțelegerea fundamentelor Prompt Engineering](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)                 | **Learn:** Cele mai bune practici de Prompt Engineering, cu exerciții practice                | [Video](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Crearea de Prompts Avansate](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **Learn:** Cum să aplici tehnici de prompt engineering care îmbunătățesc rezultatele prompturilor tale | [Video](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Construirea aplicațiilor de generare text](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Construiește:** O aplicație de generare text folosind Azure OpenAI / OpenAI API                                | [Video](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Construirea aplicațiilor de chat](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Construiește:** Tehnici pentru construirea și integrarea eficientă a aplicațiilor de chat.               | [Video](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Construirea aplicațiilor de căutare cu baze de date vectoriale](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Construiește:** O aplicație de căutare care folosește Embeddings pentru a căuta date.                        | [Video](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Construirea aplicațiilor de generare imagini](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Construiește:** O aplicație de generare imagini                                                       | [Video](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Construirea aplicațiilor AI cu Low Code](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Construiește:** O aplicație Generative AI folosind instrumente Low Code                                     | [Video](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Integrarea aplicațiilor externe cu Function Calling](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **Construiește:** Ce este function calling și cazurile sale de utilizare în aplicații                          | [Video](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [Proiectarea UX pentru aplicații AI](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Învață:** Cum să aplici principiile de design UX în dezvoltarea aplicațiilor Generative AI         | [Video](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Asigurarea aplicațiilor tale Generative AI](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Învață:** Amenințările și riscurile pentru sistemele AI și metodele de securizare a acestora.             | [Video](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Ciclul de viață al aplicațiilor Generative AI](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **Învață:** Instrumentele și metricile pentru gestionarea ciclului de viață LLM și LLMOps                         | [Video](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) și baze de date vectoriale](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **Construiește:** O aplicație folosind un cadru RAG pentru a prelua embeddings din baze de date vectoriale  | [Video](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Modele open source și Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **Construiește:** O aplicație folosind modele open source disponibile pe Hugging Face                    | [Video](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [Agenți AI](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **Construiește:** O aplicație folosind un cadru AI Agent                                           | [Video](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [Fine-Tuning LLMs](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Învață:** Ce, de ce și cum se face fine-tuning-ul LLM-urilor                                            | [Video](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Construirea cu SLMs](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Învață:** Beneficiile construirii cu Small Language Models                                            | Video în curând | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Construirea cu modelele Mistral](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Învață:** Caracteristicile și diferențele modelelor din familia Mistral                                           | Video în curând | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Construirea cu modelele Meta](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Învață:** Caracteristicile și diferențele modelelor din familia Meta                                           | Video în curând | [Află mai multe](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Mulțumiri speciale

Mulțumiri speciale lui [**John Aziz**](https://www.linkedin.com/in/john0isaac/) pentru crearea tuturor GitHub Actions și workflow-urilor

[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) pentru contribuțiile esențiale aduse fiecărei lecții, îmbunătățind experiența cursantului și a codului.

## 🎒 Alte cursuri

Echipa noastră produce și alte cursuri! Aruncă o privire:

- [**NOU** Protocolul Model Context pentru Începători](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Agenți AI pentru Începători](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Generative AI pentru Începători folosind .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Generative AI pentru Începători folosind JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML pentru Începători](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science pentru Începători](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI pentru Începători](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Securitate cibernetică pentru Începători](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Dezvoltare Web pentru Începători](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT pentru Începători](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [Dezvoltare XR pentru Începători](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Stăpânirea GitHub Copilot pentru programare AI în echipă](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Stăpânirea GitHub Copilot pentru dezvoltatori C#/.NET](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Alege-ți propria aventură Copilot](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Declinare de responsabilitate**:  
Acest document a fost tradus folosind serviciul de traducere AI [Co-op Translator](https://github.com/Azure/co-op-translator). Deși ne străduim pentru acuratețe, vă rugăm să rețineți că traducerile automate pot conține erori sau inexactități. Documentul original în limba sa nativă trebuie considerat sursa autorizată. Pentru informații critice, se recomandă traducerea profesională realizată de un specialist uman. Nu ne asumăm răspunderea pentru eventualele neînțelegeri sau interpretări greșite rezultate din utilizarea acestei traduceri.