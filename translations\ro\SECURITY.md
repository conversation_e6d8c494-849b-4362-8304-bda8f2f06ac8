<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:56:03+00:00",
  "source_file": "SECURITY.md",
  "language_code": "ro"
}
-->
## Securitate

Microsoft tratează cu seriozitate securitatea produselor și serviciilor noastre software, inclusiv toate depozitele de cod sursă gestionate prin organizațiile noastre GitHub, care includ [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) și [organizațiile noastre GitHub](https://opensource.microsoft.com/).

<PERSON><PERSON><PERSON> crezi că ai descoperit o vulnerabilitate de securitate într-un depozit deținut de Microsoft care corespunde [definiției Microsoft pentru o vulnerabilitate de securitate](https://aka.ms/opensource/security/definition), te rugăm să ne raportezi acest lucru conform instrucțiunilor de mai jos.

## Raportarea problemelor de securitate

**Te rugăm să nu raportezi vulnerabilitățile de securitate prin probleme publice pe GitHub.**

În schimb, te rugăm să le raportezi Centrului de Răspuns la Securitate Microsoft (MSRC) la [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Dacă preferi să trimiți raportul fără să te autentifici, poți trimite un email la [<EMAIL>](mailto:<EMAIL>). Dacă este posibil, criptează mesajul folosind cheia noastră PGP; o poți descărca de pe pagina [Microsoft Security Response Center PGP Key](https://aka.ms/opensource/security/pgpkey).

Ar trebui să primești un răspuns în 24 de ore. Dacă, din orice motiv, nu primești un răspuns, te rugăm să ne contactezi din nou prin email pentru a te asigura că am primit mesajul tău inițial. Informații suplimentare pot fi găsite la [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Te rugăm să incluzi informațiile solicitate mai jos (cât mai multe poți furniza) pentru a ne ajuta să înțelegem mai bine natura și amploarea problemei posibile:

  * Tipul problemei (de ex. depășire de buffer, injecție SQL, cross-site scripting etc.)
  * Căile complete ale fișierelor sursă legate de manifestarea problemei
  * Locația codului sursă afectat (tag/branch/commit sau URL direct)
  * Orice configurație specială necesară pentru a reproduce problema
  * Instrucțiuni pas cu pas pentru reproducerea problemei
  * Cod proof-of-concept sau exploit (dacă este posibil)
  * Impactul problemei, inclusiv modul în care un atacator ar putea exploata problema

Aceste informații ne vor ajuta să evaluăm raportul tău mai rapid.

Dacă raportezi pentru un bug bounty, rapoartele mai complete pot contribui la o recompensă mai mare. Te rugăm să vizitezi pagina noastră [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) pentru mai multe detalii despre programele noastre active.

## Limbi preferate

Preferăm ca toate comunicările să fie în limba engleză.

## Politică

Microsoft urmează principiul [Dezvăluirii Vulnerabilităților Coordonate](https://aka.ms/opensource/security/cvd).

**Declinare de responsabilitate**:  
Acest document a fost tradus folosind serviciul de traducere AI [Co-op Translator](https://github.com/Azure/co-op-translator). Deși ne străduim pentru acuratețe, vă rugăm să rețineți că traducerile automate pot conține erori sau inexactități. Documentul original în limba sa nativă trebuie considerat sursa autorizată. Pentru informații critice, se recomandă traducerea profesională realizată de un specialist uman. Nu ne asumăm răspunderea pentru eventualele neînțelegeri sau interpretări greșite rezultate din utilizarea acestei traduceri.