<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "e2f686f2eb794941761252ac5e8e090b",
  "translation_date": "2025-07-09T08:09:30+00:00",
  "source_file": "02-exploring-and-comparing-different-llms/README.md",
  "language_code": "ru"
}
-->
# Изучение и сравнение различных LLM

[![Изучение и сравнение различных LLM](../../../translated_images/02-lesson-banner.ef94c84979f97f60f07e27d905e708cbcbdf78707120553ccab27d91c947805b.ru.png)](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)

> _Нажмите на изображение выше, чтобы посмотреть видео этого урока_

В предыдущем уроке мы узнали, как генеративный ИИ меняет технологический ландшафт, как работают большие языковые модели (LLM) и как бизнес — например, наш стартап — может применять их для своих задач и развиваться! В этой главе мы сравним и противопоставим разные типы больших языковых моделей, чтобы понять их преимущества и недостатки.

Следующий шаг в пути нашего стартапа — изучить текущий ландшафт LLM и понять, какие из них подходят для нашего сценария.

## Введение

В этом уроке мы рассмотрим:

- Разные типы LLM, представленные сегодня.
- Тестирование, итерации и сравнение моделей для вашего сценария в Azure.
- Как развернуть LLM.

## Цели обучения

После прохождения этого урока вы сможете:

- Выбрать подходящую модель для вашего сценария.
- Понять, как тестировать, улучшать и оптимизировать работу модели.
- Узнать, как компании развертывают модели.

## Понимание разных типов LLM

LLM можно классифицировать по архитектуре, обучающим данным и области применения. Понимание этих различий поможет нашему стартапу выбрать правильную модель для конкретной задачи, а также понять, как тестировать, улучшать и оптимизировать её работу.

Существует множество типов LLM, и выбор зависит от того, для чего вы планируете их использовать, какие у вас данные, сколько вы готовы платить и других факторов.

В зависимости от того, хотите ли вы использовать модели для работы с текстом, аудио, видео, изображениями и так далее, вы можете выбрать разные типы моделей.

- **Аудио и распознавание речи**. Для этих целей отлично подходят модели типа Whisper — они универсальны и ориентированы на распознавание речи. Обучены на разнообразных аудиоданных и способны распознавать речь на нескольких языках. Подробнее о [моделях типа Whisper здесь](https://platform.openai.com/docs/models/whisper?WT.mc_id=academic-105485-koreyst).

- **Генерация изображений**. Для генерации изображений хорошо известны DALL-E и Midjourney. DALL-E доступен через Azure OpenAI. [Подробнее о DALL-E здесь](https://platform.openai.com/docs/models/dall-e?WT.mc_id=academic-105485-koreyst) и в главе 9 этого курса.

- **Генерация текста**. Большинство моделей обучены именно для генерации текста, и у вас есть широкий выбор — от GPT-3.5 до GPT-4. Они различаются по стоимости, при этом GPT-4 — самая дорогая. Рекомендуется ознакомиться с [Azure OpenAI playground](https://oai.azure.com/portal/playground?WT.mc_id=academic-105485-koreyst), чтобы оценить, какие модели лучше подходят по возможностям и цене.

- **Мультимодальность**. Если вам нужно работать с разными типами данных на входе и выходе, обратите внимание на модели вроде [gpt-4 turbo с поддержкой зрения или gpt-4o](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#gpt-4-and-gpt-4-turbo-models?WT.mc_id=academic-105485-koreyst) — последние релизы OpenAI, которые объединяют обработку естественного языка с визуальным восприятием, позволяя взаимодействовать через мультимодальные интерфейсы.

Выбор модели даёт базовые возможности, но этого может быть недостаточно. Часто у компании есть собственные данные, которые нужно как-то «рассказать» LLM. Существуют разные подходы к этому, о которых мы расскажем далее.

### Foundation Models и LLM

Термин Foundation Model был [введён исследователями из Стэнфорда](https://arxiv.org/abs/2108.07258?WT.mc_id=academic-105485-koreyst) и обозначает ИИ-модель, которая соответствует следующим критериям:

- **Обучается с использованием обучения без учителя или самоконтролируемого обучения**, то есть на неразмеченных мультимодальных данных, без необходимости ручной разметки.
- **Очень крупная модель**, основанная на глубоких нейронных сетях с миллиардами параметров.
- **Предназначена служить «основой» для других моделей**, то есть может использоваться как стартовая точка для создания специализированных моделей путём дообучения.

![Foundation Models versus LLMs](../../../translated_images/FoundationModel.e4859dbb7a825c94b284f17eae1c186aabc21d4d8644331f5b007d809cf8d0f2.ru.png)

Источник изображения: [Essential Guide to Foundation Models and Large Language Models | by Babar M Bhatti | Medium](https://thebabar.medium.com/essential-guide-to-foundation-models-and-large-language-models-27dab58f7404)

Для наглядности возьмём ChatGPT. Для создания первой версии ChatGPT в качестве foundation model использовалась модель GPT-3.5. Это значит, что OpenAI использовала специализированные данные для чата, чтобы создать настроенную версию GPT-3.5, оптимизированную для диалоговых сценариев, таких как чат-боты.

![Foundation Model](../../../translated_images/Multimodal.2c389c6439e0fc51b0b7b226d95d7d900d372ae66902d71b8ce5ec4951b8efbe.ru.png)

Источник изображения: [2108.07258.pdf (arxiv.org)](https://arxiv.org/pdf/2108.07258.pdf?WT.mc_id=academic-105485-koreyst)

### Открытые и проприетарные модели

Ещё один способ классификации LLM — по типу лицензии: открытые (open source) и проприетарные.

Открытые модели доступны всем и могут использоваться кем угодно. Их часто выпускают компании или исследовательские сообщества. Такие модели можно изучать, модифицировать и адаптировать под разные задачи. Однако они не всегда оптимизированы для промышленного использования, могут уступать по производительности проприетарным моделям, а также финансирование и поддержка таких проектов могут быть ограничены. Примеры популярных открытых моделей: [Alpaca](https://crfm.stanford.edu/2023/03/13/alpaca.html?WT.mc_id=academic-105485-koreyst), [Bloom](https://huggingface.co/bigscience/bloom) и [LLaMA](https://llama.meta.com).

Проприетарные модели принадлежат компаниям и не доступны для публичного использования. Они часто оптимизированы для промышленного применения, но не позволяют пользователям изучать, изменять или настраивать их под свои задачи. Кроме того, такие модели обычно требуют подписки или оплаты. Пользователи не контролируют данные, на которых обучалась модель, поэтому должны доверять владельцу модели в вопросах конфиденциальности и ответственного использования ИИ. Примеры популярных проприетарных моделей: [OpenAI models](https://platform.openai.com/docs/models/overview?WT.mc_id=academic-105485-koreyst), [Google Bard](https://sapling.ai/llm/bard?WT.mc_id=academic-105485-koreyst) и [Claude 2](https://www.anthropic.com/index/claude-2?WT.mc_id=academic-105485-koreyst).

### Встраивания, генерация изображений и генерация текста и кода

LLM также можно классифицировать по типу создаваемого ими результата.

Встраивания (embeddings) — это модели, которые преобразуют текст в числовое представление, называемое embedding. Это облегчает машинам понимание связей между словами или предложениями и может использоваться как вход для других моделей, например, для классификации или кластеризации, которые лучше работают с числовыми данными. Встраивания часто применяются в трансферном обучении, когда модель обучается на одной задаче с большим объёмом данных, а затем её веса (встраивания) используются для других задач. Пример — [OpenAI embeddings](https://platform.openai.com/docs/models/embeddings?WT.mc_id=academic-105485-koreyst).

![Embedding](../../../translated_images/Embedding.c3708fe988ccf76073d348483dbb7569f622211104f073e22e43106075c04800.ru.png)

Модели генерации изображений создают изображения. Их используют для редактирования, синтеза и преобразования изображений. Обычно они обучаются на больших наборах данных, таких как [LAION-5B](https://laion.ai/blog/laion-5b/?WT.mc_id=academic-105485-koreyst), и могут создавать новые изображения или редактировать существующие с помощью техник инпейнтинга, суперразрешения и колоризации. Примеры — [DALL-E-3](https://openai.com/dall-e-3?WT.mc_id=academic-105485-koreyst) и [Stable Diffusion](https://github.com/Stability-AI/StableDiffusion?WT.mc_id=academic-105485-koreyst).

![Image generation](../../../translated_images/Image.349c080266a763fd255b840a921cd8fc526ed78dc58708fa569ff1873d302345.ru.png)

Модели генерации текста и кода создают текст или программный код. Их применяют для суммирования, перевода и ответов на вопросы. Текстовые модели обучаются на больших текстовых корпусах, таких как [BookCorpus](https://www.cv-foundation.org/openaccess/content_iccv_2015/html/Zhu_Aligning_Books_and_ICCV_2015_paper.html?WT.mc_id=academic-105485-koreyst), и могут создавать новый текст или отвечать на вопросы. Модели генерации кода, например [CodeParrot](https://huggingface.co/codeparrot?WT.mc_id=academic-105485-koreyst), обучаются на больших наборах кода, например GitHub, и могут создавать новый код или исправлять ошибки.

![Text and code generation](../../../translated_images/Text.a8c0cf139e5cc2a0cd3edaba8d675103774e6ddcb3c9fc5a98bb17c9a450e31d.ru.png)

### Encoder-Decoder и только Decoder

Чтобы объяснить разные архитектуры LLM, приведём аналогию.

Представьте, что ваш руководитель поручил вам составить викторину для студентов. У вас есть два коллеги: один отвечает за создание контента, другой — за его проверку.

Создатель контента похож на модель только с Decoder: он смотрит на тему и то, что уже написано, и на основе этого пишет курс. Такие модели хорошо создают интересный и информативный текст, но не очень хорошо понимают тему и цели обучения. Пример моделей только с Decoder — семейство GPT, например GPT-3.

Проверяющий похож на модель только с Encoder: он смотрит на написанный курс и ответы, замечает взаимосвязи и понимает контекст, но не умеет создавать контент. Пример модели только с Encoder — BERT.

Если представить, что есть кто-то, кто может и создавать, и проверять викторину, это будет модель Encoder-Decoder. Примеры — BART и T5.

### Сервис и модель

Теперь поговорим о разнице между сервисом и моделью. Сервис — это продукт, предоставляемый облачным провайдером, часто представляющий собой комбинацию моделей, данных и других компонентов. Модель — это ядро сервиса, обычно foundation model, например LLM.

Сервисы оптимизированы для промышленного использования и обычно проще в применении благодаря графическому интерфейсу. Однако они не всегда бесплатны и могут требовать подписки или оплаты, взамен предоставляя доступ к оборудованию и ресурсам провайдера, оптимизируя расходы и обеспечивая масштабируемость. Пример сервиса — [Azure OpenAI Service](https://learn.microsoft.com/azure/ai-services/openai/overview?WT.mc_id=academic-105485-koreyst), который предлагает оплату по факту использования. Кроме того, Azure OpenAI Service обеспечивает корпоративный уровень безопасности и рамки ответственного использования ИИ поверх возможностей моделей.

Модели — это просто нейронные сети с параметрами и весами. Компании могут запускать их локально, но для этого нужно купить оборудование, построить инфраструктуру для масштабирования и приобрести лицензию или использовать открытую модель. Например, модель LLaMA доступна для использования, но требует вычислительных ресурсов.

## Как тестировать и улучшать модели для оценки производительности в Azure

После того как команда изучила текущий ландшафт LLM и выбрала несколько подходящих кандидатов для своих задач, следующий шаг — протестировать их на своих данных и нагрузках. Это итеративный процесс, основанный на экспериментах и измерениях.
Большинство моделей, упомянутых в предыдущих разделах (модели OpenAI, открытые модели, такие как Llama2, и трансформеры Hugging Face), доступны в [Каталоге моделей](https://learn.microsoft.com/azure/ai-studio/how-to/model-catalog-overview?WT.mc_id=academic-105485-koreyst) в [Azure AI Studio](https://ai.azure.com/?WT.mc_id=academic-105485-koreyst).

[Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/what-is-ai-studio?WT.mc_id=academic-105485-koreyst) — это облачная платформа, созданная для разработчиков, чтобы создавать приложения с генеративным ИИ и управлять всем циклом разработки — от экспериментов до оценки — объединяя все сервисы Azure AI в едином центре с удобным графическим интерфейсом. Каталог моделей в Azure AI Studio позволяет пользователю:

- Найти интересующую базовую модель в каталоге — как проприетарную, так и открытую, с возможностью фильтрации по задаче, лицензии или названию. Для удобства поиска модели организованы в коллекции, например, коллекция Azure OpenAI, коллекция Hugging Face и другие.

![Model catalog](../../../translated_images/AzureAIStudioModelCatalog.3cf8a499aa8ba0314f2c73d4048b3225d324165f547525f5b7cfa5f6c9c68941.ru.png)

- Ознакомиться с карточкой модели, включающей подробное описание предполагаемого использования и данных для обучения, примеры кода и результаты оценки на внутренней библиотеке тестов.

![Model card](../../../translated_images/ModelCard.598051692c6e400d681a713ba7717e8b6e5e65f08d12131556fcec0f1789459b.ru.png)

- Сравнить показатели производительности моделей и наборов данных, доступных в индустрии, чтобы определить, какая из них лучше подходит для конкретного бизнес-сценария, через панель [Model Benchmarks](https://learn.microsoft.com/azure/ai-studio/how-to/model-benchmarks?WT.mc_id=academic-105485-koreyst).

![Model benchmarks](../../../translated_images/ModelBenchmarks.254cb20fbd06c03a4ca53994585c5ea4300a88bcec8eff0450f2866ee2ac5ff3.ru.png)

- Дообучить модель на собственных данных, чтобы повысить её эффективность для конкретной задачи, используя возможности экспериментов и отслеживания в Azure AI Studio.

![Model fine-tuning](../../../translated_images/FineTuning.aac48f07142e36fddc6571b1f43ea2e003325c9c6d8e3fc9d8834b771e308dbf.ru.png)

- Развернуть исходную предобученную модель или её дообученную версию на удалённом endpoint для реального времени — управляемом вычислении — или безсерверном API с оплатой по факту использования — [pay-as-you-go](https://learn.microsoft.com/azure/ai-studio/how-to/model-catalog-overview#model-deployment-managed-compute-and-serverless-api-pay-as-you-go?WT.mc_id=academic-105485-koreyst) — чтобы приложения могли её использовать.

![Model deployment](../../../translated_images/ModelDeploy.890da48cbd0bccdb4abfc9257f3d884831e5d41b723e7d1ceeac9d60c3c4f984.ru.png)


> [!NOTE]
> Не все модели в каталоге доступны для дообучения и/или развертывания с оплатой по факту использования. Подробности о возможностях и ограничениях модели смотрите в её карточке.

## Улучшение результатов LLM

Мы с нашей стартап-командой исследовали разные типы LLM и облачную платформу (Azure Machine Learning), которая позволяет сравнивать модели, оценивать их на тестовых данных, улучшать производительность и развёртывать на inference endpoint.

Но когда стоит рассматривать дообучение модели вместо использования предобученной? Есть ли другие способы повысить качество модели для конкретных задач?

Существует несколько подходов, которые бизнес может использовать, чтобы получить нужные результаты от LLM. При развертывании LLM в продакшене можно выбирать разные типы моделей с разной степенью обучения, уровнем сложности, затрат и качества. Вот несколько вариантов:

- **Промпт-инжиниринг с контекстом**. Идея в том, чтобы предоставить достаточно контекста в запросе, чтобы получить нужные ответы.

- **Retrieval Augmented Generation (RAG)**. Ваши данные могут храниться в базе данных или на веб-ресурсе, и чтобы включить эти данные или их часть в запрос, можно извлечь релевантную информацию и добавить её в промпт пользователя.

- **Дообученная модель**. Здесь модель дополнительно обучается на ваших данных, что делает её более точной и адаптированной под ваши задачи, но это может быть дорогостоящим.

![LLMs deployment](../../../translated_images/Deploy.18b2d27412ec8c02871386cbe91097c7f2190a8c6e2be88f66392b411609a48c.ru.png)

Источник изображения: [Four Ways that Enterprises Deploy LLMs | Fiddler AI Blog](https://www.fiddler.ai/blog/four-ways-that-enterprises-deploy-llms?WT.mc_id=academic-105485-koreyst)

### Промпт-инжиниринг с контекстом

Предобученные LLM отлично справляются с общими задачами обработки естественного языка, даже если им дать короткий запрос, например, предложение для дополнения или вопрос — так называемое «zero-shot» обучение.

Однако чем больше пользователь формулирует запрос с подробным описанием и примерами — Контекстом — тем точнее и ближе к ожиданиям будет ответ. В этом случае говорят о «one-shot» обучении, если в промпте один пример, и «few-shot» обучении, если примеров несколько. Промпт-инжиниринг с контекстом — самый экономичный способ начать работу.

### Retrieval Augmented Generation (RAG)

У LLM есть ограничение: они могут использовать только те данные, на которых обучались, чтобы сгенерировать ответ. Это значит, что они не знают о событиях, произошедших после обучения, и не имеют доступа к непубличной информации (например, данным компании).
Это ограничение можно обойти с помощью RAG — техники, которая дополняет промпт внешними данными в виде фрагментов документов, учитывая ограничения по длине промпта. Для этого используются векторные базы данных (например, [Azure Vector Search](https://learn.microsoft.com/azure/search/vector-search-overview?WT.mc_id=academic-105485-koreyst)), которые извлекают полезные фрагменты из различных источников и добавляют их в контекст промпта.

Этот метод особенно полезен, когда у бизнеса недостаточно данных, времени или ресурсов для дообучения LLM, но есть желание улучшить качество работы на конкретной задаче и снизить риски генерации выдуманной или вредоносной информации.

### Дообученная модель

Дообучение — это процесс, использующий перенос обучения, чтобы «адаптировать» модель под конкретную задачу или проблему. В отличие от few-shot обучения и RAG, здесь создаётся новая модель с обновлёнными весами и смещениями. Для этого нужен набор обучающих примеров, состоящий из входных данных (промпт) и соответствующего им выхода (завершение).
Этот подход предпочтителен, если:

- **Используются дообученные модели**. Бизнес хочет применять дообученные менее мощные модели (например, embedding-модели) вместо высокопроизводительных, что позволяет сэкономить и ускорить решение.

- **Важна задержка отклика**. Для конкретного сценария важна низкая задержка, и нельзя использовать слишком длинные промпты или большое количество примеров, которые не помещаются в ограничение длины промпта.

- **Необходимо быть в курсе обновлений**. У бизнеса есть много качественных данных с метками и ресурсы для их постоянного обновления.

### Обученная модель

Обучение LLM с нуля — безусловно самый сложный и ресурсоёмкий подход, требующий огромных объёмов данных, квалифицированных специалистов и мощных вычислительных ресурсов. Этот вариант стоит рассматривать только если у бизнеса есть узкоспециализированный кейс и большой объём данных, ориентированных на конкретную область.

## Проверка знаний

Какой подход может быть хорошим для улучшения результатов генерации LLM?

1. Промпт-инжиниринг с контекстом  
2. RAG  
3. Дообученная модель

Ответ: 3, если у вас есть время, ресурсы и качественные данные, дообучение — лучший способ оставаться актуальным. Однако если вы хотите улучшить результаты, но времени мало, стоит сначала рассмотреть RAG.

## 🚀 Задание

Подробнее изучите, как можно [использовать RAG](https://learn.microsoft.com/azure/search/retrieval-augmented-generation-overview?WT.mc_id=academic-105485-koreyst) для вашего бизнеса.

## Отличная работа, продолжайте обучение

После прохождения этого урока ознакомьтесь с нашей [коллекцией по генеративному ИИ](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst), чтобы продолжить развивать свои знания в области генеративного ИИ!

Перейдите к уроку 3, где мы рассмотрим, как [ответственно работать с генеративным ИИ](../03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)!

**Отказ от ответственности**:  
Этот документ был переведен с помощью сервиса автоматического перевода [Co-op Translator](https://github.com/Azure/co-op-translator). Несмотря на наши усилия по обеспечению точности, просим учитывать, что автоматический перевод может содержать ошибки или неточности. Оригинальный документ на его исходном языке следует считать авторитетным источником. Для получения критически важной информации рекомендуется обращаться к профессиональному переводу, выполненному человеком. Мы не несем ответственности за любые недоразумения или неправильные толкования, возникшие в результате использования данного перевода.