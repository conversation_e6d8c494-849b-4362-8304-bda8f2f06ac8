<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "7f8f4c11f8c1cb6e1794442dead414ea",
  "translation_date": "2025-07-09T08:47:05+00:00",
  "source_file": "03-using-generative-ai-responsibly/README.md",
  "language_code": "ru"
}
-->
# Ответственное использование генеративного ИИ

[![Using Generative AI Responsibly](../../../translated_images/03-lesson-banner.1ed56067a452d97709d51f6cc8b6953918b2287132f4909ade2008c936cd4af9.ru.png)](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)

> _Нажмите на изображение выше, чтобы посмотреть видео этого урока_

Легко увлечься ИИ, особенно генеративным ИИ, но важно задуматься о том, как использовать его ответственно. Нужно учитывать, как обеспечить справедливость, безопасность и другие аспекты результата. В этой главе мы рассмотрим контекст, на что обращать внимание и как предпринимать активные шаги для улучшения использования ИИ.

## Введение

В этом уроке мы рассмотрим:

- Почему при создании приложений с генеративным ИИ стоит ставить во главу угла ответственное использование ИИ.
- Основные принципы ответственного ИИ и их связь с генеративным ИИ.
- Как применять эти принципы на практике с помощью стратегий и инструментов.

## Цели обучения

После прохождения этого урока вы узнаете:

- Почему ответственное использование ИИ важно при создании генеративных ИИ-приложений.
- Когда и как применять основные принципы ответственного ИИ при разработке генеративных ИИ-продуктов.
- Какие инструменты и стратегии помогут воплотить концепцию ответственного ИИ в жизнь.

## Принципы ответственного ИИ

Интерес к генеративному ИИ сейчас как никогда высок. Это привлекло множество новых разработчиков, внимание и финансирование. Для тех, кто хочет создавать продукты и компании на базе генеративного ИИ, это очень позитивно, но важно двигаться ответственно.

В рамках этого курса мы сосредоточимся на создании нашего стартапа и образовательного продукта по ИИ. Мы будем использовать принципы ответственного ИИ: справедливость, инклюзивность, надежность/безопасность, защиту и конфиденциальность, прозрачность и подотчетность. На их основе мы рассмотрим, как они связаны с использованием генеративного ИИ в наших продуктах.

## Почему стоит ставить ответственное ИИ в приоритет

При создании продукта человекоориентированный подход, учитывающий интересы пользователя, приводит к лучшим результатам.

Уникальность генеративного ИИ в его способности создавать полезные ответы, информацию, рекомендации и контент для пользователей. Это можно делать без множества ручных действий, что даёт впечатляющие результаты. Но без правильного планирования и стратегий это может привести к вредным последствиям для пользователей, продукта и общества в целом.

Рассмотрим некоторые (но не все) из таких потенциально вредных результатов:

### Галлюцинации

Галлюцинации — это термин, описывающий случаи, когда LLM генерирует либо бессмысленный контент, либо фактически неверную информацию, если сверяться с другими источниками.

Например, представим, что мы создаём функцию для нашего стартапа, позволяющую студентам задавать исторические вопросы модели. Студент спрашивает: `Кто был единственным выжившим на Титанике?`

Модель выдаёт ответ, похожий на этот:

![Prompt saying "Who was the sole survivor of the Titanic"](../../../03-using-generative-ai-responsibly/images/ChatGPT-titanic-survivor-prompt.webp)

> _(Источник: [Flying bisons](https://flyingbisons.com?WT.mc_id=academic-105485-koreyst))_

Это очень уверенный и подробный ответ. К сожалению, он неверен. Даже минимальное исследование показывает, что выживших на Титанике было несколько. Для студента, который только начинает изучать эту тему, такой ответ может показаться убедительным и восприниматься как факт. Это может привести к тому, что ИИ-система станет ненадёжной и негативно скажется на репутации нашего стартапа.

С каждой новой версией LLM мы видим улучшения в снижении количества галлюцинаций. Но даже с этими улучшениями разработчики и пользователи должны помнить о таких ограничениях.

### Вредоносный контент

Ранее мы говорили о случаях, когда LLM выдаёт неверные или бессмысленные ответы. Ещё один риск — когда модель генерирует вредоносный контент.

Вредоносный контент можно определить как:

- Инструкции или призывы к самоповреждению или нанесению вреда определённым группам.
- Ненавистнический или уничижительный контент.
- Помощь в планировании атак или насильственных действий.
- Инструкции по поиску нелегального контента или совершению преступлений.
- Демонстрация сексуально откровенного контента.

Для нашего стартапа важно иметь инструменты и стратегии, которые предотвратят появление такого контента для студентов.

### Отсутствие справедливости

Справедливость — это «обеспечение отсутствия предвзятости и дискриминации в ИИ-системах, а также равное и честное отношение ко всем». В мире генеративного ИИ важно не допускать, чтобы исключающие взгляды на маргинализированные группы закреплялись в ответах модели.

Подобные результаты не только разрушают позитивный опыт пользователей, но и наносят вред обществу. Разработчики должны всегда учитывать широкий и разнообразный круг пользователей при создании решений с генеративным ИИ.

## Как использовать генеративный ИИ ответственно

Теперь, когда мы поняли важность ответственного генеративного ИИ, рассмотрим 4 шага, которые помогут создавать ИИ-решения ответственно:

![Mitigate Cycle](../../../translated_images/mitigate-cycle.babcd5a5658e1775d5f2cb47f2ff305cca090400a72d98d0f9e57e9db5637c72.ru.png)

### Оценка потенциального вреда

В тестировании ПО мы проверяем ожидаемые действия пользователя в приложении. Аналогично, тестирование разнообразных запросов, которые пользователи скорее всего будут использовать, помогает оценить потенциальный вред.

Поскольку наш стартап создаёт образовательный продукт, полезно подготовить список запросов, связанных с образованием. Это могут быть вопросы по предметам, исторические факты и темы студенческой жизни.

### Смягчение потенциального вреда

Теперь нужно найти способы предотвратить или ограничить возможный вред от модели и её ответов. Рассмотрим это на 4 уровнях:

![Mitigation Layers](../../../translated_images/mitigation-layers.377215120b9a1159a8c3982c6bbcf41b6adf8c8fa04ce35cbaeeb13b4979cdfc.ru.png)

- **Модель**. Выбор подходящей модели для конкретного случая. Большие и сложные модели, такие как GPT-4, могут нести больший риск вредоносного контента при использовании в узкоспециализированных задачах. Тонкая настройка на ваших данных снижает этот риск.

- **Система безопасности**. Это набор инструментов и настроек на платформе, обслуживающей модель, которые помогают уменьшить вред. Например, система фильтрации контента в Azure OpenAI. Такие системы также должны обнаруживать попытки обхода ограничений и нежелательную активность, например запросы от ботов.

- **Метазапросы**. Метазапросы и ограничение контекста позволяют направлять или ограничивать модель на основе определённых правил и информации. Это может быть использование системных входных данных для задания границ модели. Также можно обеспечивать более релевантные ответы в рамках заданной области.

Можно применять техники, такие как Retrieval Augmented Generation (RAG), чтобы модель брала информацию только из выбранных надёжных источников. В этом курсе есть урок по [созданию поисковых приложений](../08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst).

- **Пользовательский опыт**. Последний уровень — взаимодействие пользователя с моделью через интерфейс нашего приложения. Здесь можно ограничить типы запросов, которые пользователь может отправлять, а также контролировать отображаемый текст и изображения. При запуске ИИ-приложения важно быть прозрачными в отношении возможностей и ограничений генеративного ИИ.

У нас есть отдельный урок по [дизайну UX для ИИ-приложений](../12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst).

- **Оценка модели**. Работа с LLM сложна, так как мы не всегда контролируем данные, на которых обучалась модель. Тем не менее, важно регулярно оценивать производительность и результаты модели. Нужно измерять точность, сходство, обоснованность и релевантность ответов. Это помогает обеспечить прозрачность и доверие со стороны заинтересованных лиц и пользователей.

### Эксплуатация ответственного генеративного ИИ

Создание операционной практики вокруг ваших ИИ-приложений — последний этап. Это включает сотрудничество с другими отделами стартапа, такими как юридический и отдел безопасности, чтобы обеспечить соответствие всем нормативным требованиям. Перед запуском нужно разработать планы по доставке, обработке инцидентов и откату, чтобы предотвратить возможный вред пользователям.

## Инструменты

Хотя разработка решений с ответственным ИИ может показаться сложной задачей, это стоит усилий. По мере развития генеративного ИИ появляются всё более эффективные инструменты для интеграции ответственности в рабочие процессы разработчиков. Например, [Azure AI Content Safety](https://learn.microsoft.com/azure/ai-services/content-safety/overview?WT.mc_id=academic-105485-koreyst) помогает обнаруживать вредоносный контент и изображения через API.

## Проверка знаний

О чём нужно помнить, чтобы обеспечить ответственное использование ИИ?

1. Чтобы ответ был правильным.
1. Чтобы ИИ не использовался в преступных целях.
1. Чтобы ИИ был свободен от предвзятости и дискриминации.

Ответ: правильны пункты 2 и 3. Ответственный ИИ помогает учитывать, как смягчить вредные эффекты, предвзятость и многое другое.

## 🚀 Задание

Изучите [Azure AI Content Safety](https://learn.microsoft.com/azure/ai-services/content-safety/overview?WT.mc_id=academic-105485-koreyst) и подумайте, что из этого вы можете применить в своей работе.

## Отличная работа, продолжайте обучение

После этого урока ознакомьтесь с нашей [коллекцией по генеративному ИИ](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst), чтобы продолжить развивать свои знания!

Переходите к уроку 4, где мы рассмотрим [Основы создания запросов (Prompt Engineering)](../04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)!

**Отказ от ответственности**:  
Этот документ был переведен с помощью сервиса автоматического перевода [Co-op Translator](https://github.com/Azure/co-op-translator). Несмотря на наши усилия по обеспечению точности, просим учитывать, что автоматический перевод может содержать ошибки или неточности. Оригинальный документ на его исходном языке следует считать авторитетным источником. Для получения критически важной информации рекомендуется обращаться к профессиональному переводу, выполненному человеком. Мы не несем ответственности за любые недоразумения или неправильные толкования, возникшие в результате использования данного перевода.