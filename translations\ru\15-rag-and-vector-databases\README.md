<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "e2861bbca91c0567ef32bc77fe054f9e",
  "translation_date": "2025-07-09T16:02:41+00:00",
  "source_file": "15-rag-and-vector-databases/README.md",
  "language_code": "ru"
}
-->
# Retrieval Augmented Generation (RAG) и векторные базы данных

[![Retrieval Augmented Generation (RAG) and Vector Databases](../../../translated_images/15-lesson-banner.ac49e59506175d4fc6ce521561dab2f9ccc6187410236376cfaed13cde371b90.ru.png)](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst)

В уроке по поисковым приложениям мы кратко рассмотрели, как интегрировать собственные данные в большие языковые модели (LLM). В этом уроке мы подробнее разберём концепцию привязки данных в вашем LLM-приложении, механику процесса и методы хранения данных, включая как эмбеддинги, так и текст.

> **Видео скоро будет доступно**

## Введение

В этом уроке мы рассмотрим:

- Введение в RAG: что это такое и зачем используется в искусственном интеллекте (ИИ).

- Понимание того, что такое векторные базы данных, и создание такой базы для нашего приложения.

- Практический пример интеграции RAG в приложение.

## Цели обучения

После прохождения этого урока вы сможете:

- Объяснить значение RAG в поиске и обработке данных.

- Настроить приложение с RAG и привязать ваши данные к LLM.

- Эффективно интегрировать RAG и векторные базы данных в LLM-приложения.

## Наш сценарий: улучшение LLM с помощью собственных данных

Для этого урока мы хотим добавить собственные заметки в образовательный стартап, чтобы чатбот мог получать больше информации по разным предметам. Используя эти заметки, учащиеся смогут лучше учиться и понимать различные темы, что облегчит подготовку к экзаменам. Для создания нашего сценария мы будем использовать:

- `Azure OpenAI:` LLM, который мы применим для создания чатбота

- `Урок по нейронным сетям для начинающих в AI:` это будут данные, на которых мы будем основывать наш LLM

- `Azure AI Search` и `Azure Cosmos DB:` векторная база данных для хранения данных и создания поискового индекса

Пользователи смогут создавать тренировочные викторины из своих заметок, карточки для повторения и краткие обзоры. Для начала давайте разберёмся, что такое RAG и как он работает:

## Retrieval Augmented Generation (RAG)

Чатбот на базе LLM обрабатывает запросы пользователей для генерации ответов. Он создан для интерактивного общения и может обсуждать широкий круг тем. Однако его ответы ограничены предоставленным контекстом и исходными обучающими данными. Например, у GPT-4 дата отсечения знаний — сентябрь 2021 года, то есть он не знает о событиях после этого времени. Кроме того, данные, использованные для обучения LLM, не включают конфиденциальную информацию, такую как личные заметки или руководство по продукту компании.

### Как работают RAG (Retrieval Augmented Generation)

![drawing showing how RAGs work](../../../translated_images/how-rag-works.f5d0ff63942bd3a638e7efee7a6fce7f0787f6d7a1fca4e43f2a7a4d03cde3e0.ru.png)

Предположим, вы хотите запустить чатбота, который создаёт викторины из ваших заметок — для этого потребуется подключение к базе знаний. Здесь на помощь приходит RAG. RAG работает следующим образом:

- **База знаний:** Перед поиском документы необходимо загрузить и обработать, обычно разбивая большие документы на более мелкие части, преобразовывая их в текстовые эмбеддинги и сохраняя в базе данных.

- **Запрос пользователя:** пользователь задаёт вопрос

- **Поиск:** когда пользователь задаёт вопрос, модель эмбеддинга извлекает релевантную информацию из базы знаний, чтобы добавить контекст в запрос.

- **Дополненная генерация:** LLM улучшает свой ответ на основе полученных данных. Это позволяет формировать ответ не только на основе предобученных данных, но и с учётом релевантной информации из дополнительного контекста. Извлечённые данные используются для расширения ответов LLM. Затем LLM возвращает ответ на вопрос пользователя.

![drawing showing how RAGs architecture](../../../translated_images/encoder-decode.f2658c25d0eadee2377bb28cf3aee8b67aa9249bf64d3d57bb9be077c4bc4e1a.ru.png)

Архитектура RAG реализована с помощью трансформеров, состоящих из двух частей: энкодера и декодера. Например, когда пользователь задаёт вопрос, входной текст «кодируется» в векторы, отражающие смысл слов, а затем эти векторы «декодируются» в наш индекс документов и генерируют новый текст на основе запроса пользователя. LLM использует модель энкодер-декодер для генерации ответа.

Согласно статье [Retrieval-Augmented Generation for Knowledge intensive NLP Tasks](https://arxiv.org/pdf/2005.11401.pdf?WT.mc_id=academic-105485-koreyst), существуют два подхода к реализации RAG:

- **_RAG-Sequence_** — использование извлечённых документов для предсказания наилучшего ответа на запрос пользователя

- **RAG-Token** — использование документов для генерации следующего токена, затем повторное извлечение для ответа на запрос пользователя

### Зачем использовать RAG?

- **Богатство информации:** обеспечивает актуальность и свежесть текстовых ответов. Это улучшает работу с задачами в конкретных областях за счёт доступа к внутренней базе знаний.

- Снижает вероятность выдумок, используя **проверяемые данные** из базы знаний для контекста запросов пользователей.

- **Экономичность:** такой подход дешевле, чем дообучение LLM.

## Создание базы знаний

Наше приложение основано на личных данных, а именно — уроке по нейронным сетям из курса AI For Beginners.

### Векторные базы данных

Векторная база данных, в отличие от традиционных, — это специализированная база для хранения, управления и поиска векторных эмбеддингов. Она хранит числовые представления документов. Преобразование данных в числовые эмбеддинги облегчает понимание и обработку информации нашей AI-системой.

Мы храним эмбеддинги в векторных базах, так как LLM ограничены по количеству токенов, которые они могут принять на вход. Поскольку нельзя передать все эмбеддинги целиком, их разбивают на части, и при запросе пользователя возвращаются наиболее релевантные эмбеддинги вместе с запросом. Разбиение на части также снижает затраты на количество токенов, передаваемых в LLM.

Популярные векторные базы данных включают Azure Cosmos DB, Clarifyai, Pinecone, Chromadb, ScaNN, Qdrant и DeepLake. Вы можете создать модель Azure Cosmos DB с помощью Azure CLI следующей командой:

```bash
az login
az group create -n <resource-group-name> -l <location>
az cosmosdb create -n <cosmos-db-name> -r <resource-group-name>
az cosmosdb list-keys -n <cosmos-db-name> -g <resource-group-name>
```

### От текста к эмбеддингам

Перед сохранением данных их нужно преобразовать в векторные эмбеддинги. Если вы работаете с большими документами или длинными текстами, их можно разбить на части в зависимости от ожидаемых запросов. Разбиение может происходить на уровне предложений или абзацев. Поскольку смысл части зависит от окружающих слов, можно добавить дополнительный контекст, например, заголовок документа или текст до и после части. Разбиение данных можно выполнить так:

```python
def split_text(text, max_length, min_length):
    words = text.split()
    chunks = []
    current_chunk = []

    for word in words:
        current_chunk.append(word)
        if len(' '.join(current_chunk)) < max_length and len(' '.join(current_chunk)) > min_length:
            chunks.append(' '.join(current_chunk))
            current_chunk = []

    # If the last chunk didn't reach the minimum length, add it anyway
    if current_chunk:
        chunks.append(' '.join(current_chunk))

    return chunks
```

После разбиения мы можем создать эмбеддинги текста с помощью различных моделей. Среди них: word2vec, ada-002 от OpenAI, Azure Computer Vision и многие другие. Выбор модели зависит от используемых языков, типа контента (текст/изображения/аудио), размера входных данных и длины выходного эмбеддинга.

Пример эмбеддинга слова с использованием модели OpenAI `text-embedding-ada-002`:
![an embedding of the word cat](../../../translated_images/cat.74cbd7946bc9ca380a8894c4de0c706a4f85b16296ffabbf52d6175df6bf841e.ru.png)

## Поиск и векторный поиск

Когда пользователь задаёт вопрос, система преобразует его в вектор с помощью энкодера запроса, затем ищет в нашем индексе документов релевантные векторы, связанные с запросом. После этого входной вектор и векторы документов преобразуются обратно в текст и передаются в LLM.

### Поиск

Поиск происходит, когда система быстро находит документы в индексе, соответствующие критериям запроса. Цель — получить документы, которые помогут предоставить контекст и привязать LLM к вашим данным.

Существует несколько способов поиска в базе данных:

- **Поиск по ключевым словам** — для текстовых запросов

- **Семантический поиск** — использует смысл слов

- **Векторный поиск** — преобразует документы из текста в векторные представления с помощью моделей эмбеддинга. Поиск происходит по документам, векторные представления которых наиболее близки к запросу пользователя.

- **Гибридный** — сочетание поиска по ключевым словам и векторного поиска.

Проблема возникает, если в базе нет похожего ответа на запрос — система вернёт лучший доступный результат. Для улучшения можно настроить максимальное расстояние релевантности или использовать гибридный поиск, сочетающий ключевые слова и векторный поиск. В этом уроке мы используем гибридный поиск. Данные будут храниться в dataframe с колонками для частей текста и эмбеддингов.

### Векторное сходство

Поисковая система ищет в базе эмбеддинги, которые находятся близко друг к другу — ближайших соседей, так как это похожие тексты. Когда пользователь задаёт вопрос, он сначала преобразуется в эмбеддинг, затем сопоставляется с похожими эмбеддингами. Обычно для измерения сходства векторов используется косинусное сходство — основанное на угле между двумя векторами.

Также можно использовать другие метрики: евклидово расстояние — прямая линия между концами векторов, и скалярное произведение — сумма произведений соответствующих элементов двух векторов.

### Поисковый индекс

Для поиска необходимо создать поисковый индекс для базы знаний. Индекс хранит эмбеддинги и позволяет быстро находить наиболее похожие части даже в большой базе. Индекс можно создать локально с помощью:

```python
from sklearn.neighbors import NearestNeighbors

embeddings = flattened_df['embeddings'].to_list()

# Create the search index
nbrs = NearestNeighbors(n_neighbors=5, algorithm='ball_tree').fit(embeddings)

# To query the index, you can use the kneighbors method
distances, indices = nbrs.kneighbors(embeddings)
```

### Перераспределение результатов (Re-ranking)

После запроса к базе может потребоваться отсортировать результаты по релевантности. Модель reranking использует машинное обучение для улучшения релевантности, упорядочивая результаты от наиболее подходящих. В Azure AI Search перераспределение происходит автоматически с помощью семантического reranker. Пример работы reranking с ближайшими соседями:

```python
# Find the most similar documents
distances, indices = nbrs.kneighbors([query_vector])

index = []
# Print the most similar documents
for i in range(3):
    index = indices[0][i]
    for index in indices[0]:
        print(flattened_df['chunks'].iloc[index])
        print(flattened_df['path'].iloc[index])
        print(flattened_df['distances'].iloc[index])
    else:
        print(f"Index {index} not found in DataFrame")
```

## Итоговая интеграция

Последний шаг — добавить наш LLM, чтобы получать ответы, основанные на наших данных. Реализация может выглядеть так:

```python
user_input = "what is a perceptron?"

def chatbot(user_input):
    # Convert the question to a query vector
    query_vector = create_embeddings(user_input)

    # Find the most similar documents
    distances, indices = nbrs.kneighbors([query_vector])

    # add documents to query  to provide context
    history = []
    for index in indices[0]:
        history.append(flattened_df['chunks'].iloc[index])

    # combine the history and the user input
    history.append(user_input)

    # create a message object
    messages=[
        {"role": "system", "content": "You are an AI assistant that helps with AI questions."},
        {"role": "user", "content": history[-1]}
    ]

    # use chat completion to generate a response
    response = openai.chat.completions.create(
        model="gpt-4",
        temperature=0.7,
        max_tokens=800,
        messages=messages
    )

    return response.choices[0].message

chatbot(user_input)
```

## Оценка нашего приложения

### Метрики оценки

- Качество ответов: звучат ли они естественно, плавно и похоже на человеческие

- Привязка к данным: проверка, что ответ основан на предоставленных документах

- Релевантность: насколько ответ соответствует и связан с заданным вопросом

- Плавность — грамматическая корректность ответа

## Сценарии использования RAG и векторных баз данных

Существует множество случаев, где вызовы функций могут улучшить ваше приложение, например:

- Вопросы и ответы: привязка данных компании к чату, который сотрудники могут использовать для получения информации.

- Рекомендательные системы: создание системы, которая подбирает наиболее похожие объекты, например фильмы, рестораны и многое другое.

- Чатботы: хранение истории чата и персонализация общения на основе данных пользователя.

- Поиск изображений на основе векторных эмбеддингов, полезный для распознавания изображений и обнаружения аномалий.

## Итоги

Мы рассмотрели основные аспекты RAG: добавление данных в приложение, запрос пользователя и получение ответа. Для упрощения создания RAG можно использовать фреймворки, такие как Semantic Kernel, Langchain или Autogen.

## Задание

Для продолжения изучения Retrieval Augmented Generation (RAG) вы можете:

- Создать фронтенд для приложения с использованием выбранного вами фреймворка

- Использовать фреймворк LangChain или Semantic Kernel и воссоздать ваше приложение

Поздравляем с завершением урока 👏.

## Обучение не заканчивается здесь — продолжайте путь

После прохождения этого урока ознакомьтесь с нашей [коллекцией по генеративному ИИ](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst), чтобы продолжить развивать свои знания в области генеративного ИИ!

**Отказ от ответственности**:  
Этот документ был переведен с помощью сервиса автоматического перевода [Co-op Translator](https://github.com/Azure/co-op-translator). Несмотря на наши усилия по обеспечению точности, просим учитывать, что автоматический перевод может содержать ошибки или неточности. Оригинальный документ на его исходном языке следует считать авторитетным источником. Для получения критически важной информации рекомендуется обращаться к профессиональному переводу, выполненному человеком. Мы не несем ответственности за любые недоразумения или неправильные толкования, возникшие в результате использования данного перевода.