<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:40:41+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "ru"
}
-->
# Введение в нейронные сети. Многослойный перцептрон

В предыдущем разделе вы познакомились с самой простой моделью нейронной сети — однослойным перцептроном, линейной моделью для двухклассовой классификации.

В этом разделе мы расширим эту модель до более гибкой структуры, которая позволит нам:

* выполнять **многоклассовую классификацию** помимо двухклассовой
* решать **задачи регрессии** помимо классификации
* разделять классы, которые не являются линейно разделимыми

Также мы разработаем собственный модульный фреймворк на Python, который позволит строить различные архитектуры нейронных сетей.

## Формализация задачи машинного обучения

Начнем с формализации задачи машинного обучения. Пусть у нас есть обучающая выборка **X** с метками **Y**, и нам нужно построить модель *f*, которая будет делать максимально точные предсказания. Качество предсказаний измеряется с помощью **функции потерь** ℒ. Часто используются следующие функции потерь:

* Для задачи регрессии, когда нужно предсказать число, можно использовать **абсолютную ошибку** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>| или **квадратичную ошибку** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup>
* Для классификации применяют **0-1 loss** (что по сути соответствует **точности** модели) или **логистическую функцию потерь**.

Для однослойного перцептрона функция *f* задавалась линейной функцией *f(x)=wx+b* (здесь *w* — матрица весов, *x* — вектор входных признаков, а *b* — вектор смещений). Для разных архитектур нейронных сетей эта функция может принимать более сложный вид.

> В случае классификации часто хочется получить вероятности соответствующих классов на выходе сети. Чтобы преобразовать произвольные числа в вероятности (например, нормализовать выход), часто используют функцию **softmax** σ, и функция *f* становится *f(x)=σ(wx+b)*

В определении *f* выше, *w* и *b* называются **параметрами** θ=⟨*w,b*⟩. Имея набор данных ⟨**X**,**Y**⟩, мы можем вычислить общую ошибку на всей выборке как функцию от параметров θ.

> ✅ **Цель обучения нейронной сети — минимизировать ошибку, изменяя параметры θ**

## Оптимизация методом градиентного спуска

Существует известный метод оптимизации функций — **градиентный спуск**. Идея в том, что мы можем вычислить производную (в многомерном случае — **градиент**) функции потерь по параметрам и изменять параметры так, чтобы ошибка уменьшалась. Это можно формализовать следующим образом:

* Инициализировать параметры случайными значениями w<sup>(0)</sup>, b<sup>(0)</sup>
* Многократно повторять следующий шаг:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup> - η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup> - η∂ℒ/∂b

Во время обучения шаги оптимизации должны рассчитываться с учетом всей выборки (помните, что функция потерь считается суммой по всем обучающим примерам). Однако на практике берут небольшие части выборки — **мини-батчи**, и вычисляют градиенты на их основе. Поскольку каждый раз берется случайный поднабор данных, такой метод называется **стохастическим градиентным спуском** (SGD).

## Многослойные перцептроны и обратное распространение ошибки

Однослойная сеть, как мы видели выше, способна классифицировать линейно разделимые классы. Чтобы построить более сложную модель, можно объединить несколько слоев сети. Математически это означает, что функция *f* примет более сложный вид и будет вычисляться в несколько этапов:
* z<sub>1</sub> = w<sub>1</sub>x + b<sub>1</sub>
* z<sub>2</sub> = w<sub>2</sub>α(z<sub>1</sub>) + b<sub>2</sub>
* f = σ(z<sub>2</sub>)

Здесь α — **нелинейная функция активации**, σ — функция softmax, а параметры θ = ⟨*w<sub>1</sub>, b<sub>1</sub>, w<sub>2</sub>, b<sub>2</sub>*⟩.

Алгоритм градиентного спуска остается тем же, но вычислять градиенты становится сложнее. Используя правило цепочки дифференцирования, можно вычислить производные так:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ Для вычисления производных функции потерь по параметрам используется правило цепочки дифференцирования.

Обратите внимание, что самая левая часть всех этих выражений совпадает, поэтому мы можем эффективно вычислять производные, начиная с функции потерь и двигаясь «назад» по вычислительному графу. Поэтому метод обучения многослойного перцептрона называется **обратным распространением ошибки** или просто **backpropagation**.

> TODO: ссылка на изображение

> ✅ Мы подробно рассмотрим backpropagation в нашем примере в ноутбуке.

## Заключение

В этом уроке мы создали собственную библиотеку для нейронных сетей и использовали её для простой задачи двумерной классификации.

## 🚀 Задание

В сопровождающем ноутбуке вы реализуете собственный фреймворк для построения и обучения многослойных перцептронов. Вы сможете подробно увидеть, как работают современные нейронные сети.

Перейдите к ноутбуку OwnFramework и выполните его.

## Обзор и самостоятельное изучение

Обратное распространение ошибки — распространённый алгоритм в ИИ и машинном обучении, который стоит изучить подробнее.

## Домашнее задание

В этой лабораторной работе вам нужно использовать созданный в уроке фреймворк для решения задачи классификации рукописных цифр MNIST.

* Инструкции
* Ноутбук

**Отказ от ответственности**:  
Этот документ был переведен с помощью сервиса автоматического перевода [Co-op Translator](https://github.com/Azure/co-op-translator). Несмотря на наши усилия по обеспечению точности, просим учитывать, что автоматический перевод может содержать ошибки или неточности. Оригинальный документ на его исходном языке следует считать авторитетным источником. Для получения критически важной информации рекомендуется обращаться к профессиональному переводу, выполненному человеком. Мы не несем ответственности за любые недоразумения или неправильные толкования, возникшие в результате использования данного перевода.