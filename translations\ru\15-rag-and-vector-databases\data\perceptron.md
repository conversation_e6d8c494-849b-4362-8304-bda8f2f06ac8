<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T16:53:50+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "ru"
}
-->
# Введение в нейронные сети: Персептрон

Одной из первых попыток создать нечто похожее на современную нейронную сеть была работа Фрэнка Розенблатта из Cornell Aeronautical Laboratory в 1957 году. Это была аппаратная реализация под названием "Mark-1", предназначенная для распознавания простых геометрических фигур, таких как треугольники, квадраты и круги.

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='Frank Rosenblatt'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> Изображения из Википедии

Входное изображение представлялось в виде массива из 20x20 фотодатчиков, таким образом нейронная сеть имела 400 входов и один бинарный выход. Простая сеть содержала один нейрон, также называемый **логическим элементом с порогом**. Веса нейронной сети работали как потенциометры, которые требовали ручной настройки во время фазы обучения.

> ✅ Потенциометр — это устройство, позволяющее пользователю регулировать сопротивление в цепи.

> Газета The New York Times писала о персептроне в то время: *зачаток электронного компьютера, который [ВМС] ожидают сможет ходить, говорить, видеть, писать, воспроизводить себя и осознавать своё существование.*

## Модель персептрона

Предположим, что в нашей модели есть N признаков, тогда входной вектор будет размером N. Персептрон — это модель **бинарной классификации**, то есть он может различать два класса входных данных. Мы будем считать, что для каждого входного вектора x выход нашего персептрона будет либо +1, либо -1, в зависимости от класса. Выход вычисляется по формуле:

y(x) = f(w<sup>T</sup>x)

где f — это ступенчатая функция активации

## Обучение персептрона

Для обучения персептрона нам нужно найти вектор весов w, который правильно классифицирует большинство значений, то есть минимизирует **ошибку**. Эта ошибка определяется с помощью **критерия персептрона** следующим образом:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

где:

* сумма берётся по тем обучающим примерам i, которые классифицированы неправильно
* x<sub>i</sub> — входные данные, а t<sub>i</sub> — либо -1, либо +1 для отрицательных и положительных примеров соответственно.

Этот критерий рассматривается как функция от весов w, и нам нужно её минимизировать. Часто используется метод, называемый **градиентным спуском**, при котором мы начинаем с некоторого начального вектора весов w<sup>(0)</sup>, а затем на каждом шаге обновляем веса по формуле:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

Здесь η — так называемая **скорость обучения**, а ∇E(w) обозначает **градиент** функции E. После вычисления градиента получаем

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

Алгоритм на Python выглядит так:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## Заключение

В этом уроке вы узнали о персептроне — модели бинарной классификации, и о том, как обучать её с помощью вектора весов.

## 🚀 Задача

Если хотите попробовать создать свой собственный персептрон, попробуйте этот лабораторный курс на Microsoft Learn, который использует Azure ML designer


## Обзор и самостоятельное изучение

Чтобы увидеть, как можно использовать персептрон для решения учебных задач и реальных проблем, а также продолжить обучение — перейдите к тетрадке Perceptron.

Вот также интересная статья о персептронах.

## Домашнее задание

В этом уроке мы реализовали персептрон для задачи бинарной классификации и использовали его для распознавания двух рукописных цифр. В этом лабораторном задании вам предлагается полностью решить задачу классификации цифр, то есть определить, какая цифра наиболее вероятно соответствует заданному изображению.

* Инструкции
* Тетрадка

**Отказ от ответственности**:  
Этот документ был переведен с помощью сервиса автоматического перевода [Co-op Translator](https://github.com/Azure/co-op-translator). Несмотря на наши усилия по обеспечению точности, просим учитывать, что автоматический перевод может содержать ошибки или неточности. Оригинальный документ на его исходном языке следует считать авторитетным источником. Для получения критически важной информации рекомендуется обращаться к профессиональному переводу, выполненному человеком. Мы не несем ответственности за любые недоразумения или неправильные толкования, возникшие в результате использования данного перевода.