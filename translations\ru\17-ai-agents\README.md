<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "11f03c81f190d9cbafd0f977dcbede6c",
  "translation_date": "2025-07-09T17:18:30+00:00",
  "source_file": "17-ai-agents/README.md",
  "language_code": "ru"
}
-->
[![Open Source Models](../../../translated_images/17-lesson-banner.a5b918fb0920e4e6d8d391a100f5cb1d5929f4c2752c937d40392905dec82592.ru.png)](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst)

## Введение

AI Agents представляют собой захватывающее направление в области генеративного ИИ, позволяя большим языковым моделям (LLM) эволюционировать из помощников в агентов, способных выполнять действия. Фреймворки AI Agent дают разработчикам возможность создавать приложения, которые предоставляют LLM доступ к инструментам и управлению состоянием. Эти фреймворки также повышают прозрачность, позволяя пользователям и разработчикам отслеживать действия, запланированные LLM, что улучшает управление пользовательским опытом.

В этом уроке мы рассмотрим следующие темы:

- Понимание, что такое AI Agent — что именно представляет собой AI Agent?
- Изучение четырёх различных фреймворков AI Agent — в чём их особенности?
- Применение AI Agents в разных сценариях — когда стоит использовать AI Agents?

## Цели обучения

После прохождения этого урока вы сможете:

- Объяснить, что такое AI Agents и как их можно использовать.
- Понять различия между некоторыми популярными фреймворками AI Agent и их особенности.
- Понять, как работают AI Agents, чтобы создавать приложения с их использованием.

## Что такое AI Agents?

AI Agents — это очень интересная область в мире генеративного ИИ. Вместе с этим интересом иногда возникает путаница в терминах и их применении. Чтобы упростить и охватить большинство инструментов, которые называют AI Agents, мы будем использовать следующее определение:

AI Agents позволяют большим языковым моделям (LLM) выполнять задачи, предоставляя им доступ к **состоянию** и **инструментам**.

![Agent Model](../../../translated_images/what-agent.21f2893bdfd01e6a7fd09b0416c2b15594d97f44bbb2ab5a1ff8bf643d2fcb3d.ru.png)

Давайте определим эти термины:

**Большие языковые модели** — это модели, упоминаемые в этом курсе, такие как GPT-3.5, GPT-4, Llama-2 и другие.

**Состояние** — это контекст, в котором работает LLM. Модель использует контекст своих прошлых действий и текущий контекст, чтобы направлять принятие решений для последующих действий. Фреймворки AI Agent облегчают разработчикам поддержание этого контекста.

**Инструменты** — чтобы выполнить задачу, которую запросил пользователь и которую запланировал LLM, модель должна иметь доступ к инструментам. Примерами таких инструментов могут быть база данных, API, внешнее приложение или даже другая LLM!

Эти определения помогут вам лучше понять, как всё реализовано. Давайте рассмотрим несколько различных фреймворков AI Agent:

## LangChain Agents

[LangChain Agents](https://python.langchain.com/docs/how_to/#agents?WT.mc_id=academic-105485-koreyst) — это реализация приведённых выше определений.

Для управления **состоянием** используется встроенная функция `AgentExecutor`. Она принимает определённого `agent` и доступные для него `tools`.

`AgentExecutor` также сохраняет историю чата, чтобы обеспечить контекст беседы.

![Langchain Agents](../../../translated_images/langchain-agents.edcc55b5d5c437169a2037211284154561183c58bcec6d4ac2f8a79046fac9af.ru.png)

LangChain предлагает [каталог инструментов](https://integrations.langchain.com/tools?WT.mc_id=academic-105485-koreyst), которые можно импортировать в ваше приложение, чтобы LLM получил к ним доступ. Эти инструменты создаются сообществом и командой LangChain.

Вы можете определить эти инструменты и передать их в `AgentExecutor`.

Прозрачность — ещё один важный аспект при работе с AI Agents. Разработчикам важно понимать, какой инструмент использует LLM и почему. Для этого команда LangChain разработала LangSmith.

## AutoGen

Следующий фреймворк AI Agent, который мы рассмотрим, — [AutoGen](https://microsoft.github.io/autogen/?WT.mc_id=academic-105485-koreyst). Основное внимание в AutoGen уделяется диалогам. Агенты одновременно **способны вести диалог** и **настраиваемы**.

**Способны вести диалог** — LLM могут начинать и продолжать разговор с другой LLM для выполнения задачи. Это реализуется созданием `AssistantAgents` с определённым системным сообщением.

```python

autogen.AssistantAgent( name="Coder", llm_config=llm_config, ) pm = autogen.AssistantAgent( name="Product_manager", system_message="Creative in software product ideas.", llm_config=llm_config, )

```

**Настраиваемы** — агенты могут быть определены не только как LLM, но и как пользователь или инструмент. Как разработчик, вы можете определить `UserProxyAgent`, который отвечает за взаимодействие с пользователем для получения обратной связи при выполнении задачи. Эта обратная связь может либо продолжить выполнение задачи, либо остановить её.

```python
user_proxy = UserProxyAgent(name="user_proxy")
```

### Состояние и инструменты

Для изменения и управления состоянием агент-ассистент генерирует Python-код для выполнения задачи.

Вот пример этого процесса:

![AutoGen](../../../translated_images/autogen.dee9a25a45fde584fedd84b812a6e31de5a6464687cdb66bb4f2cb7521391856.ru.png)

#### LLM определяется с помощью системного сообщения

```python
system_message="For weather related tasks, only use the functions you have been provided with. Reply TERMINATE when the task is done."
```

Это системное сообщение направляет конкретную LLM, какие функции релевантны для её задачи. Помните, что в AutoGen можно иметь несколько AssistantAgents с разными системными сообщениями.

#### Диалог инициируется пользователем

```python
user_proxy.initiate_chat( chatbot, message="I am planning a trip to NYC next week, can you help me pick out what to wear? ", )

```

Это сообщение от user_proxy (человека) запускает процесс, в ходе которого агент исследует возможные функции для выполнения.

#### Выполнение функции

```bash
chatbot (to user_proxy):

***** Suggested tool Call: get_weather ***** Arguments: {"location":"New York City, NY","time_periond:"7","temperature_unit":"Celsius"} ******************************************************** --------------------------------------------------------------------------------

>>>>>>>> EXECUTING FUNCTION get_weather... user_proxy (to chatbot): ***** Response from calling function "get_weather" ***** 112.22727272727272 EUR ****************************************************************

```

После обработки начального сообщения агент предложит инструмент для вызова. В данном случае это функция `get_weather`. В зависимости от вашей настройки, функция может быть выполнена автоматически и прочитана агентом или выполнена по запросу пользователя.

Вы можете найти список [примеров кода AutoGen](https://microsoft.github.io/autogen/docs/Examples/?WT.mc_id=academic-105485-koreyst), чтобы подробнее изучить, как начать разработку.

## Taskweaver

Следующий фреймворк, который мы рассмотрим, — [Taskweaver](https://microsoft.github.io/TaskWeaver/?WT.mc_id=academic-105485-koreyst). Его называют «code-first» агентом, потому что он работает не только со строками (`strings`), но и с DataFrames в Python. Это особенно полезно для задач анализа данных и генерации, например, создания графиков, диаграмм или генерации случайных чисел.

### Состояние и инструменты

Для управления состоянием разговора TaskWeaver использует концепцию `Planner`. `Planner` — это LLM, который принимает запросы пользователей и планирует задачи, необходимые для выполнения этого запроса.

Для выполнения задач `Planner` имеет доступ к набору инструментов, называемых `Plugins`. Это могут быть классы Python или общий интерпретатор кода. Эти плагины хранятся в виде эмбеддингов, чтобы LLM мог лучше находить нужный плагин.

![Taskweaver](../../../translated_images/taskweaver.da8559999267715a95b7677cf9b7d7dd8420aee6f3c484ced1833f081988dcd5.ru.png)

Вот пример плагина для обнаружения аномалий:

```python
class AnomalyDetectionPlugin(Plugin): def __call__(self, df: pd.DataFrame, time_col_name: str, value_col_name: str):
```

Код проверяется перед выполнением. Ещё одна функция для управления контекстом в Taskweaver — это `experience`. Experience позволяет сохранять контекст разговора в долгосрочной перспективе в YAML-файле. Это можно настроить так, чтобы LLM со временем улучшался в выполнении определённых задач, учитывая предыдущие разговоры.

## JARVIS

Последний фреймворк, который мы рассмотрим, — [JARVIS](https://github.com/microsoft/JARVIS?tab=readme-ov-file?WT.mc_id=academic-105485-koreyst). Особенность JARVIS в том, что он использует LLM для управления `состоянием` разговора, а `инструментами` выступают другие AI-модели. Каждая из этих моделей специализирована на выполнении определённых задач, таких как обнаружение объектов, транскрипция или создание подписей к изображениям.

![JARVIS](../../../translated_images/jarvis.762ddbadbd1a3a3364d4ca3db1a7a9c0d2180060c0f8da6f7bd5b5ea2a115aa7.ru.png)

LLM, будучи универсальной моделью, получает запрос от пользователя, определяет конкретную задачу и необходимые аргументы/данные для её выполнения.

```python
[{"task": "object-detection", "id": 0, "dep": [-1], "args": {"image": "e1.jpg" }}]
```

Затем LLM форматирует запрос в виде, понятном специализированной AI-модели, например, в JSON. После того как AI-модель возвращает свой результат, LLM получает ответ.

Если для выполнения задачи требуется несколько моделей, LLM также интерпретирует ответы от этих моделей и объединяет их, чтобы сформировать ответ для пользователя.

Пример ниже показывает, как это работает, когда пользователь запрашивает описание и подсчёт объектов на изображении:

## Задание

Чтобы продолжить изучение AI Agents, вы можете создать с помощью AutoGen:

- Приложение, имитирующее деловое совещание с разными отделами образовательного стартапа.
- Создать системные сообщения, которые помогут LLM понять разные роли и приоритеты, а также позволят пользователю представить новую идею продукта.
- Затем LLM должен сгенерировать уточняющие вопросы от каждого отдела, чтобы доработать и улучшить презентацию и идею продукта.

## Обучение не заканчивается здесь — продолжайте путь

После завершения этого урока ознакомьтесь с нашей [коллекцией по генеративному ИИ](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst), чтобы продолжать развивать свои знания в области генеративного ИИ!

**Отказ от ответственности**:  
Этот документ был переведен с помощью сервиса автоматического перевода [Co-op Translator](https://github.com/Azure/co-op-translator). Несмотря на наши усилия по обеспечению точности, просим учитывать, что автоматический перевод может содержать ошибки или неточности. Оригинальный документ на его исходном языке следует считать авторитетным источником. Для получения критически важной информации рекомендуется обращаться к профессиональному переводу, выполненному человеком. Мы не несем ответственности за любые недоразумения или неправильные толкования, возникшие в результате использования данного перевода.