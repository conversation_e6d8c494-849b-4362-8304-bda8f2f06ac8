<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "68664f7e754a892ae1d8d5e2b7bd2081",
  "translation_date": "2025-07-09T17:35:15+00:00",
  "source_file": "18-fine-tuning/README.md",
  "language_code": "ru"
}
-->
[![Open Source Models](../../../translated_images/18-lesson-banner.f30176815b1a5074fce9cceba317720586caa99e24001231a92fd04eeb54a121.ru.png)](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst)

# Тонкая настройка вашего LLM

Использование больших языковых моделей для создания генеративных AI-приложений сопряжено с новыми вызовами. Ключевая задача — обеспечить качество ответов (точность и релевантность) в содержимом, сгенерированном моделью на запрос пользователя. В предыдущих уроках мы обсуждали такие техники, как prompt engineering и retrieval-augmented generation, которые пытаются решить эту проблему путём _изменения входного запроса_ для существующей модели.

В сегодняшнем уроке мы рассмотрим третий подход — **тонкую настройку**, которая пытается решить задачу путём _дополнительного обучения самой модели_ на новых данных. Давайте разберёмся подробнее.

## Цели обучения

В этом уроке вы познакомитесь с понятием тонкой настройки предобученных языковых моделей, узнаете о преимуществах и сложностях этого подхода, а также получите рекомендации, когда и как использовать тонкую настройку для улучшения работы ваших генеративных AI-моделей.

К концу урока вы сможете ответить на следующие вопросы:

- Что такое тонкая настройка языковых моделей?
- Когда и зачем она нужна?
- Как можно тонко настроить предобученную модель?
- Какие ограничения есть у тонкой настройки?

Готовы? Поехали.

## Иллюстрированное руководство

Хотите получить общее представление о том, что мы будем изучать, прежде чем углубляться? Ознакомьтесь с этим иллюстрированным руководством, которое описывает путь обучения в этом уроке — от изучения основных концепций и мотивации тонкой настройки до понимания процесса и лучших практик выполнения задачи тонкой настройки. Это увлекательная тема для изучения, не забудьте заглянуть на страницу [Resources](./RESOURCES.md?WT.mc_id=academic-105485-koreyst) для дополнительных ссылок, которые помогут вам в самостоятельном обучении!

![Иллюстрированное руководство по тонкой настройке языковых моделей](../../../translated_images/18-fine-tuning-sketchnote.11b21f9ec8a703467a120cb79a28b5ac1effc8d8d9d5b31bbbac6b8640432e14.ru.png)

## Что такое тонкая настройка языковых моделей?

По определению, большие языковые модели _предобучены_ на огромных объёмах текстов из различных источников, включая интернет. Как мы узнали в предыдущих уроках, для улучшения качества ответов модели на вопросы пользователя ("промпты") нужны такие техники, как _prompt engineering_ и _retrieval-augmented generation_.

Популярный метод prompt engineering — дать модели больше указаний о том, что ожидается в ответе, либо через _инструкции_ (явные указания), либо через _несколько примеров_ (неявные указания). Это называется _few-shot learning_, но у этого подхода есть два ограничения:

- Ограничения по количеству токенов модели могут ограничить число примеров и снизить эффективность.
- Стоимость токенов может сделать добавление примеров к каждому запросу дорогим и ограничить гибкость.

Тонкая настройка — распространённая практика в машинном обучении, когда берут предобученную модель и дообучают её на новых данных для улучшения результатов в конкретной задаче. В контексте языковых моделей мы можем тонко настроить предобученную модель _на специально подобранном наборе примеров для конкретной задачи или области применения_, чтобы создать **кастомную модель**, которая будет точнее и релевантнее для этой задачи или области. Дополнительным плюсом тонкой настройки является уменьшение количества примеров, необходимых для few-shot обучения — что снижает использование токенов и связанные с этим затраты.

## Когда и зачем стоит тонко настраивать модели?

В _этом_ контексте под тонкой настройкой понимается **контролируемая** тонкая настройка, когда дообучение происходит за счёт **добавления новых данных**, которые не входили в исходный тренировочный набор. Это отличается от неконтролируемой тонкой настройки, когда модель дообучается на исходных данных, но с другими гиперпараметрами.

Главное помнить, что тонкая настройка — это продвинутый метод, требующий определённого уровня экспертизы для достижения желаемых результатов. Если сделать это неправильно, улучшений может не быть, а производительность модели для вашей целевой области может даже ухудшиться.

Поэтому, прежде чем изучать "как" тонко настраивать языковые модели, нужно понять "зачем" это делать и "когда" начинать процесс тонкой настройки. Начните с ответов на следующие вопросы:

- **Сценарий использования**: Какова ваша _цель_ тонкой настройки? Какой аспект текущей предобученной модели вы хотите улучшить?
- **Альтернативы**: Пробовали ли вы _другие методы_ для достижения желаемого результата? Используйте их как базу для сравнения.
  - Prompt engineering: попробуйте few-shot prompting с примерами релевантных ответов. Оцените качество ответов.
  - Retrieval Augmented Generation: попробуйте дополнить запросы результатами поиска по вашим данным. Оцените качество ответов.
- **Затраты**: Определили ли вы затраты на тонкую настройку?
  - Возможность настройки — доступна ли предобученная модель для тонкой настройки?
  - Усилия — подготовка данных, оценка и доработка модели.
  - Вычислительные ресурсы — для запуска задач тонкой настройки и развертывания модели.
  - Данные — наличие достаточного количества качественных примеров для заметного эффекта.
- **Преимущества**: Подтвердили ли вы выгоды от тонкой настройки?
  - Качество — превзошла ли тонко настроенная модель базовую?
  - Стоимость — снизилось ли использование токенов за счёт упрощения запросов?
  - Расширяемость — можно ли использовать базовую модель для новых областей?

Ответив на эти вопросы, вы сможете решить, подходит ли тонкая настройка для вашего случая. Идеально, если преимущества перевешивают затраты. Если решение принято, пора подумать о том, _как_ тонко настраивать предобученную модель.

Хотите больше информации о процессе принятия решения? Посмотрите [To fine-tune or not to fine-tune](https://www.youtube.com/watch?v=0Jo-z-MFxJs)

## Как можно тонко настроить предобученную модель?

Для тонкой настройки предобученной модели вам понадобятся:

- предобученная модель для дообучения
- набор данных для тонкой настройки
- среда для запуска задачи тонкой настройки
- среда для развертывания тонко настроенной модели

## Тонкая настройка на практике

Ниже приведены ресурсы с пошаговыми руководствами, которые помогут вам пройти реальный пример с выбранной моделью и подобранным набором данных. Для работы с этими уроками вам понадобится аккаунт у соответствующего провайдера, а также доступ к нужной модели и данным.

| Провайдер    | Руководство                                                                                                                                                                  | Описание                                                                                                                                                                                                                                                                                                                                                                                                                         |
| ------------ | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| OpenAI       | [How to fine-tune chat models](https://github.com/openai/openai-cookbook/blob/main/examples/How_to_finetune_chat_models.ipynb?WT.mc_id=academic-105485-koreyst)             | Научитесь тонко настраивать `gpt-35-turbo` для конкретной области ("помощник по рецептам") — подготовка данных, запуск задачи тонкой настройки и использование модели для вывода.                                                                                                                                                                                                                                            |
| Azure OpenAI | [GPT 3.5 Turbo fine-tuning tutorial](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python-new%2Ccommand-line?WT.mc_id=academic-105485-koreyst) | Узнайте, как тонко настроить модель `gpt-35-turbo-0613` **в Azure**: создание и загрузка данных, запуск задачи тонкой настройки, развертывание и использование новой модели.                                                                                                                                                                                                                                                   |
| Hugging Face | [Fine-tuning LLMs with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                          | В этом блоге показано, как тонко настраивать _открытую LLM_ (например, `CodeLlama 7B`) с помощью библиотеки [transformers](https://huggingface.co/docs/transformers/index?WT.mc_id=academic-105485-koreyst) и [Transformer Reinforcement Learning (TRL)](https://huggingface.co/docs/trl/index?WT.mc_id=academic-105485-koreyst) с использованием открытых [наборов данных](https://huggingface.co/docs/datasets/index?WT.mc_id=academic-105485-koreyst) Hugging Face. |
|              |                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| 🤗 AutoTrain | [Fine-tuning LLMs with AutoTrain](https://github.com/huggingface/autotrain-advanced/?WT.mc_id=academic-105485-koreyst)                                                    | AutoTrain (или AutoTrain Advanced) — это python-библиотека от Hugging Face, позволяющая тонко настраивать модели для различных задач, включая LLM. AutoTrain — решение без кода, тонкая настройка может выполняться в вашем облаке, на Hugging Face Spaces или локально. Поддерживает веб-интерфейс, CLI и обучение через yaml-конфигурации.                                                                                         |
|              |                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                                                                                                                                 |

## Задание

Выберите одно из приведённых выше руководств и пройдите его. _Мы можем создать версию этих уроков в Jupyter Notebooks в этом репозитории для справки. Пожалуйста, используйте оригинальные источники, чтобы получить самые свежие версии_.

## Отличная работа! Продолжайте обучение.

После завершения этого урока ознакомьтесь с нашей коллекцией [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst), чтобы продолжить развивать свои знания в области генеративного ИИ!

Поздравляем!! Вы завершили последний урок из серии v2 этого курса! Не останавливайтесь на достигнутом — продолжайте учиться и создавать. \*\*Загляните на страницу [RESOURCES](RESOURCES.md?WT.mc_id=academic-105485-koreyst) для списка дополнительных материалов по этой теме.

Наша серия уроков v1 также обновлена с новыми заданиями и концепциями. Потратьте минуту, чтобы освежить знания — и, пожалуйста, [делитесь своими вопросами и отзывами](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst), чтобы помочь нам улучшить эти уроки для сообщества.

**Отказ от ответственности**:  
Этот документ был переведен с помощью сервиса автоматического перевода [Co-op Translator](https://github.com/Azure/co-op-translator). Несмотря на наши усилия по обеспечению точности, просим учитывать, что автоматический перевод может содержать ошибки или неточности. Оригинальный документ на его исходном языке следует считать авторитетным источником. Для получения критически важной информации рекомендуется обращаться к профессиональному переводу, выполненному человеком. Мы не несем ответственности за любые недоразумения или неправильные толкования, возникшие в результате использования данного перевода.