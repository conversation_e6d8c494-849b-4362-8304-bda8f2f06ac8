<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:54:09+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "ru"
}
-->
# Ресурсы для самостоятельного обучения

Урок был создан с использованием ряда основных ресурсов от OpenAI и Azure OpenAI в качестве справочников по терминологии и учебным материалам. Ниже приведён неполный список для вашего самостоятельного изучения.

## 1. Основные ресурсы

| Название/Ссылка                                                                                                                                                                                                             | Описание                                                                                                                                                                                                                                                                                                                                                                                     |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Тонкая настройка улучшает обучение с несколькими примерами, позволяя обучаться на гораздо большем количестве данных, чем помещается в подсказку, что экономит ваши средства, повышает качество ответов и снижает задержки. **Обзор тонкой настройки от OpenAI.**                                                                                                                               |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Понимание **что такое тонкая настройка (концепция)**, зачем её использовать (мотивация), какие данные применять (обучение) и как оценивать качество.                                                                                                                                                                                                                                        |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Сервис Azure OpenAI позволяет адаптировать модели под ваши данные с помощью тонкой настройки. Узнайте **как выполнять тонкую настройку (процесс)**, выбирая модели через Azure AI Studio, Python SDK или REST API.                                                                                                                                                                            |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | Большие языковые модели могут плохо справляться с узкоспециализированными областями, задачами или наборами данных, а также выдавать неточные или вводящие в заблуждение ответы. **Когда стоит рассматривать тонкую настройку** как возможное решение?                                                                                                                                       |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Непрерывная тонкая настройка — это итеративный процесс, при котором уже тонко настроенная модель используется как базовая, и её дополнительно обучают на новых наборах примеров.                                                                                                                                                                                                             |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Тонкая настройка модели **с примерами вызова функций** может улучшить качество и согласованность ответов — с ответами в едином формате и экономией средств.                                                                                                                                                                                                                                   |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Ознакомьтесь с таблицей, чтобы понять, **какие модели можно тонко настраивать** в Azure OpenAI и в каких регионах они доступны. При необходимости проверьте ограничения по токенам и сроки действия обучающих данных.                                                                                                                                                                          |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Этот 30-минутный эпизод AI Show (октябрь 2023) обсуждает плюсы, минусы и практические советы, которые помогут принять решение о тонкой настройке.                                                                                                                                                                                                                                           |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Этот ресурс из AI Playbook проведёт вас через требования к данным, форматирование, настройку гиперпараметров и основные сложности/ограничения, которые стоит учитывать.                                                                                                                                                                                                                       |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Научитесь создавать пример набора данных для тонкой настройки, готовиться к обучению, запускать задачу тонкой настройки и развёртывать настроенную модель в Azure.                                                                                                                                                                                                                             |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio позволяет адаптировать большие языковые модели под ваши данные _с помощью интерфейса, подходящего для разработчиков с минимальным кодированием_. Посмотрите этот пример.                                                                                                                                                                                                   |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | В этой статье описывается, как тонко настраивать модель Hugging Face с помощью библиотеки transformers на одном GPU с использованием Azure DataBricks и Hugging Face Trainer.                                                                                                                                                                                                                 |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Каталог моделей в Azure Machine Learning предлагает множество открытых моделей, которые можно тонко настроить под конкретные задачи. Попробуйте этот модуль из [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst).                                                                 |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Тонкая настройка моделей GPT-3.5 или GPT-4 на Microsoft Azure с помощью W&B позволяет детально отслеживать и анализировать производительность модели. Это руководство расширяет концепции из OpenAI Fine-Tuning, добавляя конкретные шаги и функции для Azure OpenAI.                                                                                                                        |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                             |

## 2. Дополнительные ресурсы

В этом разделе собраны дополнительные материалы, которые стоит изучить, но на которые не хватило времени в рамках этого урока. Возможно, они будут рассмотрены в будущих уроках или предложены в качестве дополнительных заданий. Пока используйте их для расширения своих знаний и навыков по теме.

| Название/Ссылка                                                                                                                                                                                                            | Описание                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Подготовка и анализ данных для тонкой настройки чат-модели](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                          | Этот ноутбук служит инструментом для предобработки и анализа набора данных для тонкой настройки чат-модели. Он проверяет ошибки формата, предоставляет базовую статистику и оценивает количество токенов для расчёта стоимости обучения. См. также: [Метод тонкой настройки для gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [Тонкая настройка для Retrieval Augmented Generation (RAG) с Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)       | Цель этого ноутбука — подробно показать, как тонко настраивать модели OpenAI для Retrieval Augmented Generation (RAG). Также рассматривается интеграция с Qdrant и обучение с несколькими примерами для повышения производительности модели и снижения количества выдуманных ответов.                                                                                                                                                                                                                              |
| **OpenAI Cookbook**: [Тонкая настройка GPT с Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                               | Weights & Biases (W&B) — платформа для разработчиков ИИ с инструментами для обучения моделей, тонкой настройки и использования базовых моделей. Сначала ознакомьтесь с их руководством по [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst), затем попробуйте упражнение из Cookbook.                                                                                                                                                                      |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) — тонкая настройка для малых языковых моделей                                                     | Познакомьтесь с [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), новой компактной, но мощной малой моделью от Microsoft. Этот туториал проведёт вас через процесс тонкой настройки Phi-2, показывая, как создать уникальный набор данных и настроить модель с помощью QLoRA.                                                                                                                                                     |
| **Hugging Face Tutorial** [Как тонко настраивать LLM в 2024 с Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                                     | В этом блоге подробно описывается, как в 2024 году тонко настраивать открытые большие языковые модели с помощью Hugging Face TRL, Transformers и datasets. Вы определяете кейс использования, настраиваете среду разработки, готовите набор данных, обучаете модель, тестируете и оцениваете её, а затем разворачиваете в продакшн.                                                                                                                                                                      |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                                | Обеспечивает более быстрое и простое обучение и развёртывание [современных моделей машинного обучения](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Репозиторий содержит учебные материалы, удобные для Colab, с видеоинструкциями на YouTube по тонкой настройке. **Отражает недавнее обновление [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)**. Читайте [документацию AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst). |
|                                                                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |

**Отказ от ответственности**:  
Этот документ был переведен с помощью сервиса автоматического перевода [Co-op Translator](https://github.com/Azure/co-op-translator). Несмотря на наши усилия по обеспечению точности, просим учитывать, что автоматический перевод может содержать ошибки или неточности. Оригинальный документ на его исходном языке следует считать авторитетным источником. Для получения критически важной информации рекомендуется обращаться к профессиональному переводу, выполненному человеком. Мы не несем ответственности за любые недоразумения или неправильные толкования, возникшие в результате использования данного перевода.