<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:18:10+00:00",
  "source_file": "README.md",
  "language_code": "ru"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.ru.png)

### 21 урок, которые научат всему, что нужно знать для создания приложений на основе генеративного ИИ

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Поддержка нескольких языков

#### Поддерживается через GitHub Action (автоматически и всегда актуально)

[Французский](../fr/README.md) | [Испанский](../es/README.md) | [Немецкий](../de/README.md) | [Русский](./README.md) | [Арабский](../ar/README.md) | [Персидский (фарси)](../fa/README.md) | [Урду](../ur/README.md) | [Китайский (упрощённый)](../zh/README.md) | [Китайский (традиционный, Макао)](../mo/README.md) | [Китайский (традиционный, Гонконг)](../hk/README.md) | [Китайский (традиционный, Тайвань)](../tw/README.md) | [Японский](../ja/README.md) | [Корейский](../ko/README.md) | [Хинди](../hi/README.md) | [Бенгальский](../bn/README.md) | [Маратхи](../mr/README.md) | [Непальский](../ne/README.md) | [Пенджаби (гурмукхи)](../pa/README.md) | [Португальский (Португалия)](../pt/README.md) | [Португальский (Бразилия)](../br/README.md) | [Итальянский](../it/README.md) | [Польский](../pl/README.md) | [Турецкий](../tr/README.md) | [Греческий](../el/README.md) | [Тайский](../th/README.md) | [Шведский](../sv/README.md) | [Датский](../da/README.md) | [Норвежский](../no/README.md) | [Финский](../fi/README.md) | [Нидерландский](../nl/README.md) | [Иврит](../he/README.md) | [Вьетнамский](../vi/README.md) | [Индонезийский](../id/README.md) | [Малайский](../ms/README.md) | [Тагальский (филиппинский)](../tl/README.md) | [Суахили](../sw/README.md) | [Венгерский](../hu/README.md) | [Чешский](../cs/README.md) | [Словацкий](../sk/README.md) | [Румынский](../ro/README.md) | [Болгарский](../bg/README.md) | [Сербский (кириллица)](../sr/README.md) | [Хорватский](../hr/README.md) | [Словенский](../sl/README.md) | [Украинский](../uk/README.md) | [Бирманский (Мьянма)](../my/README.md)

# Generative AI for Beginners (версия 3) — курс

Изучите основы создания приложений на базе генеративного ИИ с помощью нашего комплексного курса из 21 урока от Microsoft Cloud Advocates.

## 🌱 Начинаем

В курсе 21 урок. Каждый урок посвящён отдельной теме, так что начинайте с любого!

Уроки делятся на «Learn» — объясняющие концепции генеративного ИИ, и «Build» — где помимо объяснений есть примеры кода на **Python** и **TypeScript**, когда это возможно.

Для разработчиков на .NET рекомендуем [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

В каждом уроке есть раздел «Keep Learning» с дополнительными материалами для углублённого изучения.

## Что понадобится
### Для запуска кода из курса можно использовать: 
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) — **уроки:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) — **уроки:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) — **уроки:** "oai-assignment" 
   
- Базовые знания Python или TypeScript будут полезны — \*Для абсолютных новичков рекомендуем эти курсы по [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) и [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst)
- Аккаунт GitHub, чтобы [форкнуть весь этот репозиторий](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) к себе

Мы подготовили урок **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)**, который поможет настроить вашу среду разработки.

Не забудьте [поставить звезду (🌟) этому репозиторию](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst), чтобы потом легче было его найти.

## 🧠 Готовы к развертыванию?

Если хотите более продвинутые примеры кода, посмотрите нашу [коллекцию примеров кода для генеративного ИИ](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) на **Python** и **TypeScript**.

## 🗣️ Встречайтесь с другими учащимися, получайте поддержку

Присоединяйтесь к нашему [официальному Discord-серверу Azure AI Foundry](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst), чтобы познакомиться и пообщаться с другими участниками курса и получить помощь.

Задавайте вопросы или делитесь отзывами о продукте на нашем [форуме разработчиков Azure AI Foundry](https://aka.ms/azureaifoundry/forum) на GitHub.

## 🚀 Создаёте стартап?

Зарегистрируйтесь в [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst), чтобы получить **бесплатные кредиты OpenAI** и до **150 000 долларов на Azure кредиты для доступа к моделям OpenAI через Azure OpenAI Services**.

## 🙏 Хотите помочь?

Есть предложения или нашли ошибки в тексте или коде? [Откройте issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) или [создайте pull request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 В каждом уроке есть:

- Краткое видео-введение в тему
- Текстовый урок в README
- Примеры кода на Python и TypeScript с поддержкой Azure OpenAI и OpenAI API
- Ссылки на дополнительные ресурсы для продолжения обучения

## 🗃️ Уроки

| #   | **Ссылка на урок**                                                                                                                          | **Описание**                                                                                   | **Видео**                                                                   | **Дополнительное обучение**                                                    |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Learn:** Как настроить среду разработки                                                    | Видео скоро появится                                                          | [Узнать больше](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Introduction to Generative AI and LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                              | **Learn:** Что такое генеративный ИИ и как работают большие языковые модели (LLM)             | [Видео](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Узнать больше](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Exploring and comparing different LLMs](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** Как выбрать подходящую модель для вашей задачи                                     | [Видео](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Узнать больше](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Using Generative AI Responsibly](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                           | **Learn:** Как ответственно создавать приложения на базе генеративного ИИ                     | [Видео](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Узнать больше](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Understanding Prompt Engineering Fundamentals](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** Практические основы prompt engineering                                            | [Видео](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Узнать больше](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Creating Advanced Prompts](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **Learn:** Как применять техники prompt engineering для улучшения результатов ваших запросов | [Видео](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Узнать больше](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Создание приложений для генерации текста](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Создать:** Приложение для генерации текста с использованием Azure OpenAI / OpenAI API             | [Видео](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Создание чат-приложений](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                           | **Создать:** Методы эффективного создания и интеграции чат-приложений                              | [Видео](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Создание поисковых приложений с векторными базами данных](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)         | **Создать:** Поисковое приложение, использующее Embeddings для поиска данных                       | [Видео](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Создание приложений для генерации изображений](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                     | **Создать:** Приложение для генерации изображений                                                | [Видео](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Создание AI-приложений с низким кодом](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Создать:** Генеративное AI-приложение с использованием Low Code инструментов                   | [Видео](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Интеграция внешних приложений с помощью Function Calling](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst)     | **Создать:** Что такое function calling и как его использовать в приложениях                     | [Видео](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [Проектирование UX для AI-приложений](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                           | **Изучить:** Как применять принципы UX-дизайна при разработке генеративных AI-приложений          | [Видео](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Обеспечение безопасности генеративных AI-приложений](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                   | **Изучить:** Угрозы и риски для AI-систем и методы их защиты                                     | [Видео](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Жизненный цикл генеративных AI-приложений](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)             | **Изучить:** Инструменты и метрики для управления жизненным циклом LLM и LLMOps                   | [Видео](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) и векторные базы данных](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)          | **Создать:** Приложение с использованием RAG Framework для извлечения embeddings из векторных баз | [Видео](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Открытые модели и Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                              | **Создать:** Приложение с использованием открытых моделей, доступных на Hugging Face             | [Видео](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI-агенты](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                           | **Создать:** Приложение с использованием AI Agent Framework                                     | [Видео](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [Тонкая настройка LLM](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Изучить:** Что такое тонкая настройка LLM, зачем она нужна и как её выполнять                   | [Видео](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Создание с использованием SLM](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                             | **Изучить:** Преимущества создания с использованием Small Language Models                        | Видео скоро будет доступно | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Создание с использованием моделей Mistral](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                             | **Изучить:** Особенности и отличия моделей семейства Mistral                                    | Видео скоро будет доступно | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Создание с использованием моделей Meta](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                   | **Изучить:** Особенности и отличия моделей семейства Meta                                      | Видео скоро будет доступно | [Подробнее](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Особая благодарность

Особая благодарность [**John Aziz**](https://www.linkedin.com/in/john0isaac/) за создание всех GitHub Actions и рабочих процессов

[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) за ключевой вклад в каждое занятие, улучшая опыт обучения и качество кода.

## 🎒 Другие курсы

Наша команда выпускает и другие курсы! Ознакомьтесь с ними:

- [**НОВЫЙ** Протокол контекста модели для начинающих](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI-агенты для начинающих](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Генеративный AI для начинающих на .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Генеративный AI для начинающих на JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML для начинающих](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science для начинающих](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI для начинающих](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Кибербезопасность для начинающих](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Веб-разработка для начинающих](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT для начинающих](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR-разработка для начинающих](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Освоение GitHub Copilot для парного программирования с AI](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Освоение GitHub Copilot для разработчиков C#/.NET](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Выбери своё приключение с Copilot](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Отказ от ответственности**:  
Этот документ был переведен с помощью сервиса автоматического перевода [Co-op Translator](https://github.com/Azure/co-op-translator). Несмотря на наши усилия по обеспечению точности, просим учитывать, что автоматические переводы могут содержать ошибки или неточности. Оригинальный документ на его исходном языке следует считать авторитетным источником. Для получения критически важной информации рекомендуется обращаться к профессиональному человеческому переводу. Мы не несем ответственности за любые недоразумения или неправильные толкования, возникшие в результате использования данного перевода.