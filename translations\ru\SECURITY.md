<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:50:38+00:00",
  "source_file": "SECURITY.md",
  "language_code": "ru"
}
-->
## Безопасность

Microsoft серьезно относится к безопасности наших программных продуктов и сервисов, включая все репозитории исходного кода, управляемые через наши организации на GitHub, такие как [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [<PERSON><PERSON>rin](https://github.com/xamarin) и [наши организации на GitHub](https://opensource.microsoft.com/).

Если вы считаете, что нашли уязвимость безопасности в любом репозитории, принадлежащем Microsoft, которая соответствует [определению уязвимости безопасности Microsoft](https://aka.ms/opensource/security/definition), пожалуйста, сообщите нам об этом, как описано ниже.

## Сообщение о проблемах безопасности

**Пожалуйста, не сообщайте об уязвимостях безопасности через публичные вопросы на GitHub.**

Вместо этого отправляйте их в Центр реагирования на инциденты безопасности Microsoft (MSRC) по адресу [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Если вы предпочитаете отправить сообщение без входа в систему, отправьте письмо на [<EMAIL>](mailto:<EMAIL>). По возможности зашифруйте сообщение с помощью нашего PGP-ключа; его можно скачать на странице [PGP-ключ Центра реагирования на инциденты безопасности Microsoft](https://aka.ms/opensource/security/pgpkey).

Вы должны получить ответ в течение 24 часов. Если по какой-то причине ответа не последует, пожалуйста, свяжитесь с нами повторно по электронной почте, чтобы убедиться, что мы получили ваше первоначальное сообщение. Дополнительную информацию можно найти на [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Пожалуйста, включите в сообщение следующую информацию (по возможности максимально полную), чтобы помочь нам лучше понять суть и масштаб возможной проблемы:

  * Тип проблемы (например, переполнение буфера, SQL-инъекция, межсайтовый скриптинг и т.д.)
  * Полные пути к исходным файлам, связанным с проявлением проблемы
  * Местоположение затронутого исходного кода (тег/ветка/коммит или прямая ссылка)
  * Особые настройки, необходимые для воспроизведения проблемы
  * Пошаговые инструкции для воспроизведения проблемы
  * Доказательство концепции или код эксплойта (если возможно)
  * Влияние проблемы, включая способы, которыми злоумышленник может её использовать

Эта информация поможет нам быстрее обработать ваше сообщение.

Если вы сообщаете об уязвимости в рамках программы Bug Bounty, более полные отчеты могут способствовать получению более высокой награды. Пожалуйста, посетите нашу страницу [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) для получения подробной информации о действующих программах.

## Предпочтительные языки

Мы предпочитаем, чтобы все сообщения были на английском языке.

## Политика

Microsoft придерживается принципа [Координированного раскрытия уязвимостей](https://aka.ms/opensource/security/cvd).

**Отказ от ответственности**:  
Этот документ был переведен с помощью сервиса автоматического перевода [Co-op Translator](https://github.com/Azure/co-op-translator). Несмотря на наши усилия по обеспечению точности, просим учитывать, что автоматические переводы могут содержать ошибки или неточности. Оригинальный документ на его исходном языке следует считать авторитетным источником. Для получения критически важной информации рекомендуется обращаться к профессиональному человеческому переводу. Мы не несем ответственности за любые недоразумения или неправильные толкования, возникшие в результате использования данного перевода.