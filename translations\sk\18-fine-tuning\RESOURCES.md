<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:08:12+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "sk"
}
-->
# Zdroje pre samostatné štúdium

Táto lekcia bola vytvorená na základe viacerých kľúčových zdrojov od OpenAI a Azure OpenAI, ktoré slúžili ako referencie pre terminológiu a návody. Tu je neúplný zoznam, ktorý vám pomôže na vašej ceste samostatného učenia.

## 1. <PERSON>lav<PERSON><PERSON> zdroje

| Názov/Odkaz                                                                                                                                                                                                                 | Popis                                                                                                                                                                                                                                                                                                                                                                                        |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning zlepšuje učenie na základe niekoľkých príkladov tým, že trénuje na oveľa väčšom množstve dát, než sa zmestí do promptu, čím šetrí náklady, zvyšuje kvalitu odpovedí a umožňuje rýchlejšie spracovanie požiadaviek. **Získajte prehľad o fine-tuningu od OpenAI.**                                                                                                                  |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Pochopte **čo je fine-tuning (koncept)**, prečo by ste ho mali zvážiť (motivujúci problém), aké dáta použiť (tréning) a ako merať kvalitu výsledkov.                                                                                                                                                                                                                                      |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service vám umožňuje prispôsobiť modely na základe vašich vlastných dát pomocou fine-tuningu. Naučte sa **ako vykonať fine-tuning (proces)** vybraných modelov cez Azure AI Studio, Python SDK alebo REST API.                                                                                                                                                                   |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | Veľké jazykové modely (LLM) nemusia dobre fungovať na špecifických doménach, úlohách alebo dátach, alebo môžu produkovať nepresné či zavádzajúce výstupy. **Kedy by ste mali zvážiť fine-tuning** ako riešenie?                                                                                                                                                                               |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Kontinuálny fine-tuning je iteratívny proces, pri ktorom sa už existujúci fine-tuned model používa ako základ a **ďalej sa doladí** na nových tréningových dátach.                                                                                                                                                                                                                          |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fine-tuning modelu **s príkladmi volania funkcií** môže zlepšiť výstupy modelu tým, že zabezpečí presnejšie a konzistentnejšie odpovede – s podobným formátovaním a zároveň úsporou nákladov.                                                                                                                                                                                               |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Pozrite si túto tabuľku, aby ste pochopili, **ktoré modely je možné fine-tunovať** v Azure OpenAI a v ktorých regiónoch sú dostupné. Ak je potrebné, skontrolujte limity tokenov a dátum vypršania platnosti tréningových dát.                                                                                                                                                               |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Tento 30-minútový diel AI Show z októbra 2023 rozoberá výhody, nevýhody a praktické poznatky, ktoré vám pomôžu rozhodnúť sa, či fine-tuning použiť.                                                                                                                                                                                                                                         |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Tento zdroj z **AI Playbook** vás prevedie požiadavkami na dáta, formátovaním, doladením hyperparametrov a výzvami či obmedzeniami, ktoré by ste mali poznať.                                                                                                                                                                                                                                  |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Naučte sa vytvoriť ukážkový dataset pre fine-tuning, pripraviť sa na fine-tuning, spustiť fine-tuning úlohu a nasadiť doladený model na Azure.                                                                                                                                                                                                                                               |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio vám umožňuje prispôsobiť veľké jazykové modely na základe vašich dát _pomocou UI workflow vhodného pre low-code vývojárov_. Pozrite si tento príklad.                                                                                                                                                                                                                         |
| **Tutorial**: [Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Tento článok popisuje, ako doladiť model Hugging Face pomocou knižnice transformers na jednom GPU s využitím Azure DataBricks a Hugging Face Trainer knižníc.                                                                                                                                                                                                                                  |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Katalóg modelov v Azure Machine Learning ponúka množstvo open source modelov, ktoré môžete doladiť pre svoju konkrétnu úlohu. Vyskúšajte tento modul z [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst).                                                                 |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fine-tuning modelov GPT-3.5 alebo GPT-4 na Microsoft Azure pomocou W&B umožňuje detailné sledovanie a analýzu výkonu modelu. Tento návod rozširuje koncepty z OpenAI Fine-Tuning príručky o špecifické kroky a funkcie pre Azure OpenAI.                                                                                                                                                     |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                             |

## 2. Vedľajšie zdroje

Táto sekcia obsahuje ďalšie zdroje, ktoré stoja za preskúmanie, no v tejto lekcii sme ich nestihli pokryť. Môžu byť zahrnuté v budúcich lekciách alebo ako doplnkové úlohy. Zatiaľ ich využite na rozšírenie svojich znalostí a odbornosti v tejto oblasti.

| Názov/Odkaz                                                                                                                                                                                                            | Popis                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Príprava a analýza dát pre fine-tuning chat modelu](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                            | Tento notebook slúži ako nástroj na predspracovanie a analýzu datasetu pre fine-tuning chat modelu. Kontroluje formátové chyby, poskytuje základné štatistiky a odhaduje počet tokenov pre výpočet nákladov na fine-tuning. Pozrite si: [Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                                            |
| **OpenAI Cookbook**: [Fine-tuning pre Retrieval Augmented Generation (RAG) s Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)       | Cieľom tohto notebooku je ukázať komplexný príklad, ako doladiť OpenAI modely pre Retrieval Augmented Generation (RAG). Zároveň integrujeme Qdrant a Few-Shot Learning na zvýšenie výkonu modelu a zníženie nepresností.                                                                                                                                                                                                                                         |
| **OpenAI Cookbook**: [Fine-tuning GPT s Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                               | Weights & Biases (W&B) je platforma pre AI vývojárov s nástrojmi na trénovanie modelov, fine-tuning a využívanie základných modelov. Najprv si prečítajte ich [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) príručku a potom vyskúšajte cvičenie z Cookbooku.                                                                                                                                                                      |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning pre malé jazykové modely                                                      | Spoznajte [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), nový malý model od Microsoftu, ktorý je prekvapivo výkonný a zároveň kompaktný. Tento tutoriál vás prevedie fine-tuningom Phi-2, ukáže, ako vytvoriť jedinečný dataset a doladiť model pomocou QLoRA.                                                                                                                                                                  |
| **Hugging Face Tutorial** [Ako doladiť LLM v roku 2024 s Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                                     | Tento blogový príspevok vás prevedie procesom doladenia otvorených LLM pomocou Hugging Face TRL, Transformers a datasetov v roku 2024. Definujete si prípad použitia, nastavíte vývojové prostredie, pripravíte dataset, doladíte model, otestujete a vyhodnotíte ho a nakoniec nasadíte do produkcie.                                                                                                                                                              |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Prináša rýchlejšie a jednoduchšie trénovanie a nasadzovanie [najmodernejších modelov strojového učenia](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Repo obsahuje tutoriály priateľské k Colab s video návodmi na YouTube pre fine-tuning. **Zohľadňuje nedávnu [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) aktualizáciu**. Prečítajte si [dokumentáciu AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst). |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |

**Vyhlásenie o zodpovednosti**:  
Tento dokument bol preložený pomocou AI prekladateľskej služby [Co-op Translator](https://github.com/Azure/co-op-translator). Aj keď sa snažíme o presnosť, prosím, majte na pamäti, že automatizované preklady môžu obsahovať chyby alebo nepresnosti. Originálny dokument v jeho pôvodnom jazyku by mal byť považovaný za autoritatívny zdroj. Pre kritické informácie sa odporúča profesionálny ľudský preklad. Nie sme zodpovední za akékoľvek nedorozumenia alebo nesprávne interpretácie vyplývajúce z použitia tohto prekladu.