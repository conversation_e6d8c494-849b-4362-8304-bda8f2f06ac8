<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:44:02+00:00",
  "source_file": "README.md",
  "language_code": "sk"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.sk.png)

### 21 lekci<PERSON>, ktoré vás naučia všetko, čo potrebujete vedieť na začatie tvorby aplikácií s generatívnou AI

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Podpora viacerých jazykov

#### Podporované cez GitHub Action (automatizované a vždy aktuálne)

[French](../fr/README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](../ja/README.md) | [Korean](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](../el/README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](../he/README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](./README.md) | [Romanian](../ro/README.md) | [Bulgarian](../bg/README.md) | [Serbian (Cyrillic)](../sr/README.md) | [Croatian](../hr/README.md) | [Slovenian](../sl/README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Generatívna AI pre začiatočníkov (verzia 3) – Kurz

Naučte sa základy tvorby aplikácií s generatívnou AI v našom komplexnom kurze s 21 lekciami od Microsoft Cloud Advocates.

## 🌱 Začíname

Tento kurz obsahuje 21 lekcií. Každá lekcia sa venuje samostatnej téme, takže začnite kdekoľvek chcete!

Lekcie sú označené ako „Learn“ (Nauč sa) – vysvetľujúce koncept generatívnej AI, alebo „Build“ (Stavaj) – ktoré vysvetľujú koncept a obsahujú príklady kódu v **Pythone** a **TypeScripte**, ak je to možné.

Pre .NET vývojárov odporúčame [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

Každá lekcia obsahuje aj sekciu „Keep Learning“ s ďalšími vzdelávacími zdrojmi.

## Čo potrebujete
### Na spustenie kódu z tohto kurzu môžete použiť:
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **Lekcie:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **Lekcie:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **Lekcie:** "oai-assignment" 
   
- Základné znalosti Pythonu alebo TypeScriptu sú užitočné – \*Pre úplných začiatočníkov odporúčame tieto [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) a [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) kurzy
- GitHub účet na [forknutie tohto celého repozitára](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) do vlastného GitHub účtu

Vytvorili sme lekciu **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)**, ktorá vám pomôže s nastavením vývojového prostredia.

Nezabudnite [pridať tento repozitár medzi obľúbené (🌟)](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst), aby ste ho neskôr ľahšie našli.

## 🧠 Pripravení na nasadenie?

Ak hľadáte pokročilejšie ukážky kódu, pozrite si našu [kolekciu ukážok kódu pre generatívnu AI](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) v **Pythone** a **TypeScripte**.

## 🗣️ Spojte sa s ostatnými študentmi, získajte podporu

Pridajte sa na náš [oficiálny Azure AI Foundry Discord server](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst), kde môžete spoznať a nadviazať kontakty s ostatnými študentmi tohto kurzu a získať podporu.

Pýtajte sa otázky alebo zdieľajte spätnú väzbu v našom [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) na GitHube.

## 🚀 Budujete startup?

Zaregistrujte sa do [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) a získajte **bezplatné kredity na OpenAI** a až do **150 000 $ na Azure kredity na prístup k OpenAI modelom cez Azure OpenAI Services**.

## 🙏 Chcete pomôcť?

Máte návrhy alebo ste našli chyby v pravopise či kóde? [Otvorte issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) alebo [vytvorte pull request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Každá lekcia obsahuje:

- Krátke video s úvodom do témy
- Písanú lekciu v README
- Ukážky kódu v Pythone a TypeScripte podporujúce Azure OpenAI a OpenAI API
- Odkazy na ďalšie zdroje pre pokračovanie vo vzdelávaní

## 🗃️ Lekcie

| #   | **Odkaz na lekciu**                                                                                                                          | **Popis**                                                                                      | **Video**                                                                   | **Ďalšie vzdelávanie**                                                         |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Learn:** Ako nastaviť vývojové prostredie                                                  | Video čoskoro                                                                 | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Úvod do generatívnej AI a LLM](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                                         | **Learn:** Pochopenie, čo je generatívna AI a ako fungujú veľké jazykové modely (LLM)          | [Video](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Preskúmanie a porovnanie rôznych LLM](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)                 | **Learn:** Ako vybrať správny model pre váš prípad použitia                                   | [Video](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Zodpovedné používanie generatívnej AI](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                       | **Learn:** Ako zodpovedne vytvárať aplikácie s generatívnou AI                                | [Video](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Základy prompt engineeringu](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)                                  | **Learn:** Praktické najlepšie postupy v prompt engineeringu                                 | [Video](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Tvorba pokročilých promptov](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **Learn:** Ako aplikovať techniky prompt engineeringu, ktoré zlepšia výsledky vašich promptov | [Video](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Tvorba aplikácií na generovanie textu](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Vytvorte:** aplikáciu na generovanie textu pomocou Azure OpenAI / OpenAI API                                | [Video](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Tvorba chatovacích aplikácií](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Vytvorte:** techniky na efektívnu tvorbu a integráciu chatovacích aplikácií               | [Video](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Tvorba vyhľadávacích aplikácií s vektorovými databázami](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Vytvorte:** vyhľadávaciu aplikáciu, ktorá používa Embeddings na vyhľadávanie dát                        | [Video](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Tvorba aplikácií na generovanie obrázkov](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Vytvorte:** aplikáciu na generovanie obrázkov                                                       | [Video](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Tvorba AI aplikácií s nízkym kódom](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Vytvorte:** generatívnu AI aplikáciu pomocou nástrojov Low Code                                     | [Video](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Integrácia externých aplikácií pomocou volania funkcií](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **Vytvorte:** čo je volanie funkcií a jeho využitie v aplikáciách                          | [Video](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [Návrh UX pre AI aplikácie](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Naučte sa:** ako aplikovať princípy UX dizajnu pri vývoji generatívnych AI aplikácií         | [Video](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Zabezpečenie vašich generatívnych AI aplikácií](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Naučte sa:** hrozby a riziká pre AI systémy a spôsoby, ako tieto systémy zabezpečiť             | [Video](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Životný cyklus generatívnej AI aplikácie](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **Naučte sa:** nástroje a metriky na riadenie životného cyklu LLM a LLMOps                         | [Video](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) a vektorové databázy](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **Vytvorte:** aplikáciu využívajúcu RAG Framework na získavanie embeddings z vektorových databáz  | [Video](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Open Source modely a Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **Vytvorte:** aplikáciu využívajúcu open source modely dostupné na Hugging Face                    | [Video](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI agenti](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **Vytvorte:** aplikáciu využívajúcu AI Agent Framework                                           | [Video](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [Ladenie LLM](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Naučte sa:** čo, prečo a ako ladíme LLM                                            | [Video](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Tvorba s SLM](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Naučte sa:** výhody tvorby s malými jazykovými modelmi                                            | Video čoskoro | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Tvorba s Mistral modelmi](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Naučte sa:** vlastnosti a rozdiely modelov rodiny Mistral                                           | Video čoskoro | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Tvorba s Meta modelmi](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Naučte sa:** vlastnosti a rozdiely modelov rodiny Meta                                           | Video čoskoro | [Viac informácií](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Špeciálne poďakovanie

Špeciálne poďakovanie patrí [**Johnovi Azizovi**](https://www.linkedin.com/in/john0isaac/) za vytvorenie všetkých GitHub Actions a workflowov

[**Bernhardovi Merkleovi**](https://www.linkedin.com/in/bernhard-merkle-738b73/) za kľúčové príspevky ku každej lekcii, ktoré zlepšili zážitok z učenia a kódovania.

## 🎒 Ďalšie kurzy

Náš tím pripravuje aj ďalšie kurzy! Pozrite si:

- [**NOVÝ** Model Context Protocol pre začiatočníkov](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI agenti pre začiatočníkov](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Generatívna AI pre začiatočníkov s .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Generatívna AI pre začiatočníkov s JavaScriptom](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [Strojové učenie pre začiatočníkov](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Dátová veda pre začiatočníkov](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI pre začiatočníkov](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Kybernetická bezpečnosť pre začiatočníkov](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Webový vývoj pre začiatočníkov](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT pre začiatočníkov](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [Vývoj XR pre začiatočníkov](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Ovládnutie GitHub Copilot pre AI párované programovanie](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Ovládnutie GitHub Copilot pre C#/.NET vývojárov](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Vyberte si vlastné dobrodružstvo s Copilotom](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Vyhlásenie o zodpovednosti**:  
Tento dokument bol preložený pomocou AI prekladateľskej služby [Co-op Translator](https://github.com/Azure/co-op-translator). Hoci sa snažíme o presnosť, prosím, majte na pamäti, že automatizované preklady môžu obsahovať chyby alebo nepresnosti. Originálny dokument v jeho pôvodnom jazyku by mal byť považovaný za autoritatívny zdroj. Pre kritické informácie sa odporúča profesionálny ľudský preklad. Nie sme zodpovední za akékoľvek nedorozumenia alebo nesprávne interpretácie vyplývajúce z použitia tohto prekladu.