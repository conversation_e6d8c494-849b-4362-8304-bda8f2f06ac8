<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:55:54+00:00",
  "source_file": "SECURITY.md",
  "language_code": "sk"
}
-->
## Bezpečnosť

Microsoft berie bezpečnosť našich softvérových produktov a služieb vážne, čo zahŕňa všetky repozitáre so zdrojovým kódom spravované cez naše GitHub organizácie, medzi ktoré patria [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) a [naše GitHub organizácie](https://opensource.microsoft.com/).

Ak máte podozrenie, že ste našli bezpečnostnú zraniteľnosť v niektorom z repozitárov vlastnených Microsoftom, ktorá spĺňa [definíciu bezpečnostnej zraniteľnosti podľa Microsoftu](https://aka.ms/opensource/security/definition), prosím, nahláste nám ju podľa nižšie uvedených pokynov.

## Nahlasovanie bezpečnostných problémov

**Prosím, nenahlasujte bezpečnostné zraniteľnosti cez verejné GitHub issues.**

Namiesto toho ich nahláste Microsoft Security Response Center (MSRC) na [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Ak uprednostňujete odoslanie bez prihlásenia, pošlite e-mail na [<EMAIL>](mailto:<EMAIL>). Ak je to možné, zašifrujte správu naším PGP kľúčom; stiahnuť ho môžete na stránke [Microsoft Security Response Center PGP Key](https://aka.ms/opensource/security/pgpkey).

Očakávajte odpoveď do 24 hodín. Ak z nejakého dôvodu nedostanete odpoveď, prosím, kontaktujte nás opätovne e-mailom, aby sme potvrdili prijatie vašej pôvodnej správy. Ďalšie informácie nájdete na [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Prosíme, aby ste zahrnuli čo najviac z nasledujúcich informácií, ktoré nám pomôžu lepšie pochopiť povahu a rozsah možného problému:

  * Typ problému (napr. buffer overflow, SQL injection, cross-site scripting a pod.)
  * Kompletné cesty k zdrojovým súborom súvisiacim s prejavom problému
  * Umiestnenie postihnutého zdrojového kódu (tag/branch/commit alebo priamy URL)
  * Akákoľvek špeciálna konfigurácia potrebná na reprodukciu problému
  * Podrobný postup na reprodukciu problému
  * Proof-of-concept alebo exploit kód (ak je to možné)
  * Dopad problému vrátane spôsobu, akým by ho mohol útočník zneužiť

Tieto informácie nám pomôžu rýchlejšie spracovať vašu správu.

Ak nahlasujete chybu v rámci bug bounty programu, podrobnejšie správy môžu viesť k vyššej odmeny. Viac informácií o našich aktívnych programoch nájdete na stránke [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty).

## Preferované jazyky

Uprednostňujeme, aby všetka komunikácia prebiehala v angličtine.

## Politika

Microsoft dodržiava princíp [Koordinovaného zverejňovania zraniteľností](https://aka.ms/opensource/security/cvd).

**Vyhlásenie o zodpovednosti**:  
Tento dokument bol preložený pomocou AI prekladateľskej služby [Co-op Translator](https://github.com/Azure/co-op-translator). Aj keď sa snažíme o presnosť, prosím, majte na pamäti, že automatizované preklady môžu obsahovať chyby alebo nepresnosti. Originálny dokument v jeho pôvodnom jazyku by mal byť považovaný za autoritatívny zdroj. Pre kritické informácie sa odporúča profesionálny ľudský preklad. Nie sme zodpovední za akékoľvek nedorozumenia alebo nesprávne interpretácie vyplývajúce z použitia tohto prekladu.