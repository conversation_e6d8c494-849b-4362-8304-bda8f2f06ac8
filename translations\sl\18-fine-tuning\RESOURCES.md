<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:10:11+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "sl"
}
-->
# Viri za samostojno učenje

Lekcija je bila zgrajena z uporabo več ključnih virov iz OpenAI in Azure OpenAI kot referenc za terminologijo in vodiče. Tukaj je nepopoln seznam za vaše samostojne učne poti.

## 1. Primarni viri

| Naslov/Povezava                                                                                                                                                                                                                   | Opis                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning izboljša učenje z nekaj primeri tako, da trenira na veliko več primerih, kot jih lahko vključite v poziv, kar vam prihrani stroške, izboljša kakovost odgovorov in omogoča hitrejše odzive. **Pridobite pregled fine-tuninga iz OpenAI.**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Spoznajte **kaj je fine-tuning (koncept)**, zakaj ga je vredno preučiti (motivacijski problem), katere podatke uporabiti (usposabljanje) in kako meriti kakovost.                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service vam omogoča prilagoditev modelov na vaše osebne podatkovne zbirke z uporabo fine-tuninga. Naučite se **kako izvajati fine-tuning (postopek)** in izbirati modele z Azure AI Studio, Python SDK ali REST API.                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM-ji morda ne delujejo dobro na določenih področjih, nalogah ali podatkovnih zbirkah, ali pa lahko proizvedejo netočne ali zavajajoče rezultate. **Kdaj je smiselno razmisliti o fine-tuningu** kot možni rešitvi?                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Kontinuirani fine-tuning je iterativni proces, kjer izberete že fine-tuniran model kot osnovo in ga **nadalje prilagajate** na novih nizih učnih primerov.                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fine-tuning vašega modela **z uporabo primerov klicev funkcij** lahko izboljša izhod modela z natančnejšimi in bolj doslednimi odgovori – s podobno oblikovanimi odzivi in prihranki pri stroških.                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Preverite to tabelo, da razumete **kateri modeli so na voljo za fine-tuning** v Azure OpenAI in v katerih regijah. Po potrebi preverite njihove omejitve števila tokenov in datume poteka učnih podatkov.                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Ta 30-minutna epizoda AI Show iz oktobra 2023 obravnava prednosti, slabosti in praktične vpoglede, ki vam pomagajo pri odločitvi.                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Ta **AI Playbook** vir vas vodi skozi zahteve glede podatkov, oblikovanje, nastavitve hiperparametrov fine-tuninga ter izzive in omejitve, ki jih morate poznati.                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Naučite se ustvariti vzorčno podatkovno zbirko za fine-tuning, pripraviti se na fine-tuning, ustvariti nalogo fine-tuninga in namestiti prilagojen model na Azure.                                                                                                                                                                                    |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio vam omogoča prilagoditev velikih jezikovnih modelov na vaše osebne podatkovne zbirke _z uporabo UI delovnega toka, primernega za razvijalce z malo kode_. Oglejte si ta primer.                                                                                                                                                               |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Ta članek opisuje, kako fine-tunirati Hugging Face model z uporabo knjižnice Hugging Face transformers na enem GPU z Azure DataBricks in knjižnicami Hugging Face Trainer.                                                                                                                                                |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Katalog modelov v Azure Machine Learning ponuja številne odprtokodne modele, ki jih lahko prilagodite za svojo specifično nalogo. Preizkusite ta modul iz [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst). |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fine-tuning modelov GPT-3.5 ali GPT-4 na Microsoft Azure z uporabo W&B omogoča podrobno sledenje in analizo zmogljivosti modela. Ta vodič razširja koncepte iz OpenAI Fine-Tuning vodiča z dodatnimi koraki in funkcijami za Azure OpenAI.                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. Sekundarni viri

Ta razdelek zajema dodatne vire, ki jih je vredno raziskati, vendar jih v tej lekciji nismo uspeli obravnavati. Morda bodo vključeni v prihodnjih lekcijah ali kot dodatna naloga. Za zdaj jih uporabite za gradnjo lastnega znanja in strokovnosti na to temo.

| Naslov/Povezava                                                                                                                                                                                                            | Opis                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Priprava in analiza podatkov za fine-tuning klepetalnega modela](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | Ta zvezek služi kot orodje za predobdelavo in analizo podatkovne zbirke za fine-tuning klepetalnega modela. Preverja napake v formatu, nudi osnovno statistiko in ocenjuje število tokenov za stroške fine-tuninga. Oglejte si: [Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning za Retrieval Augmented Generation (RAG) z Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | Namen tega zvezka je prikazati celovit primer, kako fine-tunirati OpenAI modele za Retrieval Augmented Generation (RAG). Prav tako bomo integrirali Qdrant in Few-Shot Learning za izboljšanje zmogljivosti modela in zmanjšanje napačnih informacij.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT z Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) je platforma za razvijalce AI z orodji za treniranje modelov, fine-tuning in uporabo osnovnih modelov. Najprej preberite njihov vodič [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst), nato preizkusite vajo iz Cookbook.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning za majhne jezikovne modele                                                   | Spoznajte [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), nov Microsoftov majhen model, ki je presenetljivo zmogljiv in kompakten. Ta vodič vas bo popeljal skozi fine-tuning Phi-2, prikazal, kako zgraditi edinstveno podatkovno zbirko in fine-tunirati model z uporabo QLoRA.                                                                                                                                                                       |
| **Hugging Face Tutorial** [Kako fine-tunirati LLM-je v letu 2024 z Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Ta blog vas vodi skozi postopek fine-tuninga odprtih LLM-jev z uporabo Hugging Face TRL, Transformers in podatkovnih zbirk v letu 2024. Določite primer uporabe, nastavite razvojno okolje, pripravite podatkovno zbirko, fine-tunirajte model, ga testno ocenite in nato uvedite v produkcijo.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Omogoča hitrejše in enostavnejše treniranje ter uvajanje [najnaprednejših modelov strojnega učenja](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Repo vsebuje Colab-prijazne vodiče z YouTube video navodili za fine-tuning. **Vključuje nedavno posodobitev [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)**. Preberite [AutoTrain dokumentacijo](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst). |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Omejitev odgovornosti**:  
Ta dokument je bil preveden z uporabo storitve za avtomatski prevod AI [Co-op Translator](https://github.com/Azure/co-op-translator). Čeprav si prizadevamo za natančnost, vas opozarjamo, da lahko avtomatski prevodi vsebujejo napake ali netočnosti. Izvirni dokument v njegovem izvirnem jeziku velja za avtoritativni vir. Za pomembne informacije priporočamo strokovni človeški prevod. Za morebitne nesporazume ali napačne interpretacije, ki izhajajo iz uporabe tega prevoda, ne odgovarjamo.