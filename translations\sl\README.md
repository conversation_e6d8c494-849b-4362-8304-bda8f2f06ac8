<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:47:41+00:00",
  "source_file": "README.md",
  "language_code": "sl"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.sl.png)

### 21 lekcij, ki te naučijo vsega, kar potrebuješ za začetek gradnje aplikacij Generativne umetne inteligence

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Podpora za več jezikov

#### Podprto preko GitHub Action (avtomatizirano in vedno posodobljeno)

[French](../fr/README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](../ja/README.md) | [Korean](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](../el/README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](../he/README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](../sk/README.md) | [Romanian](../ro/README.md) | [Bulgarian](../bg/README.md) | [Serbian (Cyrillic)](../sr/README.md) | [Croatian](../hr/README.md) | [Slovenian](./README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Generativna umetna inteligenca za začetnike (različica 3) - tečaj

Nauči se osnov gradnje aplikacij Generativne umetne inteligence z našim obsežnim tečajem, ki ga pripravljajo Microsoft Cloud Advocates.

## 🌱 Začetek

Ta tečaj vsebuje 21 lekcij. Vsaka lekcija pokriva svojo temo, zato začni kjerkoli želiš!

Lekcije so označene kot "Learn" lekcije, ki razlagajo koncept Generativne AI, ali "Build" lekcije, ki razložijo koncept in prikažejo primere kode v **Python** in **TypeScript**, kjer je to mogoče.

Za .NET razvijalce je na voljo [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

Vsaka lekcija vsebuje tudi razdelek "Keep Learning" z dodatnimi učnimi viri.

## Kaj potrebuješ
### Za zagon kode iz tega tečaja lahko uporabiš:
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **Lekcije:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **Lekcije:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **Lekcije:** "oai-assignment" 
   
- Osnovno znanje Pythona ali TypeScripta je koristno - \*Za popolne začetnike si oglej ta [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) in [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) tečaja
- GitHub račun za [fork celotnega repozitorija](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) na svoj GitHub račun

Pripravili smo lekcijo **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)**, ki ti pomaga pri nastavitvi razvojnega okolja.

Ne pozabi [ozvezditi (🌟) tega repozitorija](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst), da ga boš lažje našel kasneje.

## 🧠 Pripravljen za objavo?

Če iščeš bolj napredne primere kode, si oglej našo [zbirko primerov kode za Generativno AI](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) v **Pythonu** in **TypeScriptu**.

## 🗣️ Spoznaj druge udeležence, pridobi podporo

Pridruži se našemu [uradnemu Azure AI Foundry Discord strežniku](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst), kjer lahko spoznaš in povežeš z drugimi, ki obiskujejo ta tečaj, ter dobiš podporo.

Postavljaj vprašanja ali deli povratne informacije o izdelku v našem [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) na GitHubu.

## 🚀 Gradiš startup?

Prijavi se na [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) in prejmi **brezplačne OpenAI kredite** ter do **150.000 $ Azure kreditov za dostop do OpenAI modelov preko Azure OpenAI Services**.

## 🙏 Želiš pomagati?

Imaš predloge ali si našel napake v črkovanju ali kodi? [Odpri issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) ali [ustvari pull request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Vsaka lekcija vsebuje:

- Kratek video uvod v temo
- Pisno lekcijo v README datoteki
- Primere kode v Pythonu in TypeScriptu, ki podpirajo Azure OpenAI in OpenAI API
- Povezave do dodatnih virov za nadaljnje učenje

## 🗃️ Lekcije

| #   | **Povezava do lekcije**                                                                                                                      | **Opis**                                                                                      | **Video**                                                                   | **Dodatno učenje**                                                             |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Learn:** Kako nastaviti razvojno okolje                                                   | Video kmalu na voljo                                                        | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Introduction to Generative AI and LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                              | **Learn:** Razumevanje, kaj je Generativna AI in kako delujejo veliki jezikovni modeli (LLM) | [Video](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Exploring and comparing different LLMs](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** Kako izbrati pravi model za tvojo uporabo                                        | [Video](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Using Generative AI Responsibly](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                           | **Learn:** Kako odgovorno graditi aplikacije Generativne AI                                 | [Video](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Understanding Prompt Engineering Fundamentals](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** Praktične najboljše prakse za prompt engineering                                 | [Video](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Creating Advanced Prompts](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **Learn:** Kako uporabiti tehnike prompt engineering za izboljšanje rezultatov tvojih pozivov | [Video](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Gradnja aplikacij za generiranje besedil](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Izdelava:** Aplikacija za generiranje besedil z uporabo Azure OpenAI / OpenAI API                | [Video](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Gradnja klepetalnih aplikacij](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Izdelava:** Tehnike za učinkovito gradnjo in integracijo klepetalnih aplikacij                   | [Video](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Gradnja iskalnih aplikacij z vektorskimi bazami](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                  | **Izdelava:** Iskalna aplikacija, ki uporablja Embeddings za iskanje podatkov                      | [Video](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Gradnja aplikacij za generiranje slik](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                             | **Izdelava:** Aplikacija za generiranje slik                                                     | [Video](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Gradnja AI aplikacij z nizko kodo](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                           | **Izdelava:** Generativna AI aplikacija z uporabo orodij za nizko kodo                            | [Video](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Integracija zunanjih aplikacij s klicem funkcij](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst)              | **Izdelava:** Kaj je klic funkcij in kako se uporablja v aplikacijah                             | [Video](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [Oblikovanje UX za AI aplikacije](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                               | **Učenje:** Kako uporabiti načela UX oblikovanja pri razvoju generativnih AI aplikacij             | [Video](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Zavarovanje vaših generativnih AI aplikacij](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                           | **Učenje:** Grožnje in tveganja za AI sisteme ter načini za njihovo zaščito                        | [Video](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Življenjski cikel generativnih AI aplikacij](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)             | **Učenje:** Orodja in metrike za upravljanje življenjskega cikla LLM in LLMOps                    | [Video](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) in vektorske baze](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)                | **Izdelava:** Aplikacija, ki uporablja RAG okvir za pridobivanje embeddingov iz vektorskih baz   | [Video](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Modeli odprte kode in Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                          | **Izdelava:** Aplikacija, ki uporablja modele odprte kode, dostopne na Hugging Face              | [Video](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI agenti](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                           | **Izdelava:** Aplikacija, ki uporablja AI Agent Framework                                        | [Video](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [Fino nastavljanje LLM-jev](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                        | **Učenje:** Kaj, zakaj in kako fino nastavljati LLM-je                                           | [Video](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Gradnja z SLM-ji](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                                         | **Učenje:** Prednosti gradnje z malimi jezikovnimi modeli                                       | Video kmalu na voljo | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Gradnja z Mistral modeli](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                             | **Učenje:** Značilnosti in razlike modelov družine Mistral                                       | Video kmalu na voljo | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Gradnja z Meta modeli](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                                   | **Učenje:** Značilnosti in razlike modelov družine Meta                                         | Video kmalu na voljo | [Več informacij](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Posebna zahvala

Posebna zahvala [**Johnu Azizu**](https://www.linkedin.com/in/john0isaac/) za ustvarjanje vseh GitHub Actions in delovnih tokov

[**Bernhardu Merkleju**](https://www.linkedin.com/in/bernhard-merkle-738b73/) za ključne prispevke k vsaki lekciji, ki izboljšujejo izkušnjo učenja in kode.

## 🎒 Drugi tečaji

Naša ekipa pripravlja tudi druge tečaje! Oglejte si:

- [**NOVO** Model Context Protocol za začetnike](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI agenti za začetnike](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Generativna AI za začetnike z uporabo .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Generativna AI za začetnike z uporabo JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [Strojno učenje za začetnike](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Podatkovna znanost za začetnike](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI za začetnike](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Kibernetska varnost za začetnike](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Spletni razvoj za začetnike](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT za začetnike](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR razvoj za začetnike](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Obvladovanje GitHub Copilot za AI parno programiranje](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Obvladovanje GitHub Copilot za C#/.NET razvijalce](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Izberi svojo Copilot pustolovščino](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Omejitev odgovornosti**:  
Ta dokument je bil preveden z uporabo storitve za avtomatski prevod AI [Co-op Translator](https://github.com/Azure/co-op-translator). Čeprav si prizadevamo za natančnost, vas opozarjamo, da lahko avtomatski prevodi vsebujejo napake ali netočnosti. Izvirni dokument v njegovem izvirnem jeziku velja za avtoritativni vir. Za pomembne informacije priporočamo strokovni človeški prevod. Za morebitna nesporazume ali napačne interpretacije, ki izhajajo iz uporabe tega prevoda, ne odgovarjamo.