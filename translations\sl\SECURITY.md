<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:56:41+00:00",
  "source_file": "SECURITY.md",
  "language_code": "sl"
}
-->
## Varnost

Microsoft resno jemlje varnost naših programski izdelkov in storitev, kar vključuje vse repozitorije izvorne kode, ki jih upravljamo prek naših GitHub organizacij, med katerimi so [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) in [naše GitHub organizacije](https://opensource.microsoft.com/).

<PERSON>e menite, da ste odkrili varnostno ranljivost v katerem koli repozitoriju v lasti Microsofta, ki ustreza [Microsoftovi definiciji varnostne ranljivosti](https://aka.ms/opensource/security/definition), nam jo prosim prijavite, kot je opisano spodaj.

## Prijava varnostnih težav

**Prosimo, ne prijavljajte varnostnih ranljivosti preko javnih GitHub zadev.**

Namesto tega jih prijavite Microsoft Security Response Center (MSRC) na [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Če želite prijavo oddati brez prijave, pošljite e-pošto na [<EMAIL>](mailto:<EMAIL>). Če je mogoče, sporočilo šifrirajte z našim PGP ključem; prenesite ga na strani [Microsoft Security Response Center PGP Key](https://aka.ms/opensource/security/pgpkey).

Odgovor bi morali prejeti v 24 urah. Če iz kakršnega koli razloga ne, prosimo, da nas preko e-pošte opomnite, da zagotovite, da smo prejeli vaše prvotno sporočilo. Dodatne informacije so na voljo na [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Prosimo, vključite spodaj zahtevane informacije (kolikor jih lahko zagotovite), da nam pomagate bolje razumeti naravo in obseg morebitne težave:

  * Vrsta težave (npr. prelivanje medpomnilnika, SQL injekcija, cross-site scripting itd.)
  * Polne poti izvornih datotek, povezanih z manifestacijo težave
  * Lokacija prizadete izvorne kode (tag/branch/commit ali neposredni URL)
  * Morebitna posebna konfiguracija, potrebna za ponovitev težave
  * Navodila korak za korakom za ponovitev težave
  * Dokaz koncepta ali izkoriščevalna koda (če je mogoče)
  * Vpliv težave, vključno s tem, kako bi jo lahko napadalec izkoristil

Te informacije nam bodo pomagale hitreje obravnavati vašo prijavo.

Če prijavljate za bug bounty, lahko bolj popolne prijave prispevajo k višji nagradi. Za več podrobnosti o naših aktivnih programih obiščite stran [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty).

## Priporočeni jeziki

Vse komunikacije naj bodo v angleščini.

## Politika

Microsoft sledi načelu [Koordinirane razkritja ranljivosti](https://aka.ms/opensource/security/cvd).

**Omejitev odgovornosti**:  
Ta dokument je bil preveden z uporabo AI prevajalske storitve [Co-op Translator](https://github.com/Azure/co-op-translator). Čeprav si prizadevamo za natančnost, vas opozarjamo, da avtomatizirani prevodi lahko vsebujejo napake ali netočnosti. Izvirni dokument v njegovem izvirnem jeziku velja za avtoritativni vir. Za ključne informacije priporočamo strokovni človeški prevod. Za morebitna nesporazume ali napačne interpretacije, ki izhajajo iz uporabe tega prevoda, ne odgovarjamo.