<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:51:39+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "sr"
}
-->
# Увод у неуронске мреже. Вишеслојни перцептрон

У претходном делу сте научили о најједноставнијем моделу неуронске мреже - једнослојном перцептрону, линеарном моделу за класификацију у две класе.

У овом делу ћемо проширити овај модел у флексибилнији оквир који нам омогућава да:

* изводимо **класификацију више класа** поред класификације у две класе
* решавамо **проблеме регресије** поред класификације
* раздвајамо класе које нису линеарно раздвојиве

Такође ћемо развити свој модуларни оквир у Python-у који ће нам омогућити да конструишемо различите архитектуре неуронских мрежа.

## Формализација машинског учења

Хајде да почнемо са формализацијом проблема машинског учења. Претпоставимо да имамо скуп података за учење **X** са ознакама **Y**, и да треба да направимо модел *f* који ће правити што прецизније предикције. Квалитет предикција мери се помоћу **функције губитка** ℒ. Често коришћене функције губитка су:

* За проблем регресије, када треба предвидети број, можемо користити **апсолутну грешку** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>|, или **квадратну грешку** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup>
* За класификацију користимо **0-1 губитак** (што је у суштини исто као **тачност** модела), или **логистичку функцију губитка**.

За једнослојни перцептрон, функција *f* је дефинисана као линеарна функција *f(x)=wx+b* (овде је *w* матрица тежина, *x* вектор улазних карактеристика, а *b* вектор помака). За различите архитектуре неуронских мрежа, ова функција може имати сложенији облик.

> У случају класификације, често је пожељно да излаз мреже буду вероватноће одговарајућих класа. Да бисмо претворили произвољне бројеве у вероватноће (нпр. да нормализујемо излаз), често користимо **softmax** функцију σ, и функција *f* постаје *f(x)=σ(wx+b)*

У дефиницији *f* изнад, *w* и *b* се називају **параметри** θ=⟨*w,b*⟩. За дати скуп података ⟨**X**,**Y**⟩, можемо израчунати укупну грешку на целом скупу као функцију параметара θ.

> ✅ **Циљ тренинга неуронске мреже је да се минимизира грешка варирањем параметара θ**

## Оптимизација градијентним спуштањем

Постоји добро позната метода оптимизације функција која се зове **градијентно спуштање**. Идеја је да можемо израчунати извод (у вишедимензионалном случају се назива **градијент**) функције губитка у односу на параметре, и мењати параметре тако да грешка опада. Ово се формализује на следећи начин:

* Иницијализујемо параметре неким случајним вредностима w<sup>(0)</sup>, b<sup>(0)</sup>
* Понављамо следећи корак више пута:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

Током тренинга, кораци оптимизације се обично рачунају узимајући у обзир цео скуп података (подсетите се да се губитак рачуна као збир кроз све узорке за учење). Међутим, у пракси узимамо мале делове скупа података који се зову **минибатцхеви**, и рачунамо градијенте на основу подскупа података. Пошто се подскуп узима насумично сваки пут, ова метода се назива **стохастичко градијентно спуштање** (SGD).

## Вишеслојни перцептрони и бацкпропагација

Једнослојна мрежа, као што смо видели, може да класификује линеарно раздвојиве класе. Да бисмо направили богатији модел, можемо комбиновати више слојева мреже. Математички, то значи да функција *f* има сложенији облик и рачуна се у неколико корака:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

Овде је α **нелинеарна активациона функција**, σ је softmax функција, а параметри θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*>.

Алгоритам градијентног спуштања остаје исти, али је рачунање градијената сложеније. Уз правилo ланчаног извођења, можемо израчунати изводе као:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ Правило ланчаног извођења користи се за израчунавање изводa функције губитка у односу на параметре.

Имајте у виду да је леви део свих ових израза исти, па можемо ефикасно израчунати изводе крећући се од функције губитка и идући "унатраг" кроз рачунски граф. Због тога се метода тренинга вишеслојног перцептрона назива **бацкпропагација**, или скраћено 'backprop'.



> TODO: цитат слике

> ✅ Бацкпропагацију ћемо детаљније обрадити у нашем примеру у свесци.

## Закључак

У овој лекцији смо направили своју библиотеку за неуронске мреже и користили је за једноставан задатак класификације у две димензије.

## 🚀 Изазов

У пратећој свесци имплементираћете свој оквир за конструисање и тренирање вишеслојних перцептрона. Можете детаљно видети како модерне неуронске мреже функционишу.

Наставите на OwnFramework свеску и радите кроз њу.

## Преглед и самостално учење

Бацкпропагација је уобичајен алгоритам који се користи у вештачкој интелигенцији и машинском учењу, вреди га детаљније проучити.

## Задатак

У овом лабораторијском задатку треба да употребите оквир који сте направили у овој лекцији да решите класификацију рукописних цифара MNIST.

* Упутства
* Свеска

**Одрицање од одговорности**:  
Овај документ је преведен коришћењем AI услуге за превођење [Co-op Translator](https://github.com/Azure/co-op-translator). Иако се трудимо да превод буде тачан, молимо вас да имате у виду да аутоматски преводи могу садржати грешке или нетачности. Оригинални документ на његовом изворном језику треба сматрати ауторитетним извором. За критичне информације препоручује се професионални људски превод. Нисмо одговорни за било каква неспоразума или погрешна тумачења која произилазе из коришћења овог превода.