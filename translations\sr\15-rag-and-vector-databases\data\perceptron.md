<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T17:01:27+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "sr"
}
-->
# Увод у неуронске мреже: Перцептрон

Један од првих покушаја да се имплементира нешто слично модерној неуронској мрежи направио је Френк Розенблат са Корнел аеронаутичке лабораторије 1957. године. Ради се о хардверској имплементацији под називом "Mark-1", дизајнираној да препозна примитивне геометријске облике, као што су троуглови, квадрати и кругови.

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='Frank Rosenblatt'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> Слике са Википедије

Улазна слика је представљена као матрица од 20x20 фотосензора, тако да је неуронска мрежа имала 400 улаза и један бинарни излаз. Једноставна мрежа садржала је један неурон, који се такође називао **логичка јединица прага**. Тежине у неуронској мрежи понашале су се као потенциометри који су захтевали ручно подешавање током фазе учења.

> ✅ Потенциометар је уређај који омогућава кориснику да подеси отпор у колу.

> The New York Times је у то време писао о перцептрону: *ембрион електронског рачунара који [морнарица] очекује да ће моћи да хода, говори, види, пише, репродукује се и буде свестан свог постојања.*

## Модел перцептрона

Претпоставимо да у нашем моделу имамо N карактеристика, у ком случају би улазни вектор био вектор величине N. Перцептрон је модел **бинарне класификације**, односно може да разликује између две класе улазних података. Претпоставићемо да за сваки улазни вектор x излаз нашег перцептрона може бити или +1 или -1, у зависности од класе. Излаз ће се израчунати по формули:

y(x) = f(w<sup>T</sup>x)

где је f функција корак активације

## Тренирање перцептрона

Да бисмо тренирали перцептрон, потребно је да пронађемо вектор тежина w који највећи број вредности класификује исправно, односно да резултује најмањом **грешком**. Ова грешка је дефинисана **перцептрон критеријумом** на следећи начин:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

где:

* збир се узима преко оних тренинг података i који доводе до погрешне класификације
* x<sub>i</sub> је улазни податак, а t<sub>i</sub> је или -1 или +1 за негативне и позитивне примере редом.

Овај критеријум се посматра као функција тежина w, и потребно га је минимизовати. Често се користи метода која се зове **градијентни спуст**, у којој почињемо са неким почетним тежинама w<sup>(0)</sup>, а затим на сваком кораку ажурирамо тежине по формули:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

Овде је η такозвана **степен учења**, а ∇E(w) означава **градијент** функције E. Након израчунавања градијента добијамо

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

Алгоритам у Питону изгледа овако:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## Закључак

У овој лекцији сте научили о перцептрону, који је модел бинарне класификације, и како га тренирати коришћењем вектора тежина.

## 🚀 Изазов

Ако желите да покушате да направите свој перцептрон, испробајте овај лабораторијски задатак на Microsoft Learn који користи Azure ML дизајнер


## Преглед и самостално учење

Да бисте видели како можемо користити перцептрон за решавање једноставних као и стварних проблема, и да наставите са учењем - идите на Perceptron notebook.

Ево и један занимљив чланак о перцептронима.

## Задатак

У овој лекцији смо имплементирали перцептрон за задатак бинарне класификације, и користили га да класификујемо између две руком написане цифре. У овом лабораторијском задатку, од вас се тражи да решите проблем класификације цифара у потпуности, односно да одредите која цифра највероватније одговара датој слици.

* Упутства
* Notebook

**Одрицање од одговорности**:  
Овај документ је преведен коришћењем AI услуге за превођење [Co-op Translator](https://github.com/Azure/co-op-translator). Иако се трудимо да превод буде тачан, молимо вас да имате у виду да аутоматски преводи могу садржати грешке или нетачности. Оригинални документ на његовом изворном језику треба сматрати ауторитетним извором. За критичне информације препоручује се професионални људски превод. Нисмо одговорни за било каква неспоразума или погрешна тумачења која произилазе из коришћења овог превода.