<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:09:23+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "sr"
}
-->
# Ресурси за самостално учење

Овај час је направљен користећи низ кључних ресурса из OpenAI и Azure OpenAI као референце за терминологију и туторијале. Ево непотпуног списка за ваше самостално учење.

## 1. Примарни ресурси

| Наслов/Линк                                                                                                                                                                                                                 | Опис                                                                                                                                                                                                                                                                                                                                                                                         |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Фајн-тунинг побољшава few-shot учење тако што тренира на много више примера него што може да стане у упит, штедећи вам трошкове, побољшавајући квалитет одговора и омогућавајући захтеве са мањом латенцијом. **Прегледајте фајн-тунинг из OpenAI.**                                                                                                                                            |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Разумите **шта је фајн-тунинг (концепт)**, зашто би требало да га размотрите (мотивациони проблем), које податке користити (тренинг) и како мерити квалитет                                                                                                                                                                        |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service вам омогућава да прилагодите моделе својим личним скупом података користећи фајн-тунинг. Сазнајте **како да извршите фајн-тунинг (процес)** одабиром модела преко Azure AI Studio, Python SDK или REST API.                                                                                                            |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM модели можда неће добро радити на специфичним доменима, задацима или скупу података, или могу произвести нетачне или обмањујуће резултате. **Када треба размотрити фајн-тунинг** као могуће решење?                                                                                                                             |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Континуирани фајн-тунинг је итеративни процес у коме се већ фајн-тунирани модел користи као основа и **даље се фајн-тунира** на новим скупова примера за тренирање.                                                                                                                                                                  |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Фајн-тунинг вашег модела **са примерима позива функција** може побољшати излаз модела добијањем прецизнијих и конзистентнијих одговора - са слично форматираним реакцијама и уштедом трошкова                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Погледајте ову табелу да бисте разумели **које моделе је могуће фајн-тунирати** у Azure OpenAI и у којим регионима су доступни. Погледајте и њихове лимите за токене и рокове важења података за тренирање ако је потребно.                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Ова 30-минутна епизода AI Show из октобра 2023. године говори о предностима, манама и практичним увидима који ће вам помоћи да донесете одлуку.                                                                                                                                                                                    |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Овај **AI Playbook** ресурс вас води кроз захтеве за податке, форматирање, подешавање хиперпараметара и изазове/ограничења које треба да знате.                                                                                                                                                                                   |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Научите како да направите пример сета података за фајн-тунинг, припремите се за фајн-тунинг, креирате посао фајн-тунинга и имплементирате фајн-тунирани модел на Azure.                                                                                                                                                              |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio вам омогућава да прилагодите велике језичке моделе својим личним скупом података _користећи UI-оријентисан радни ток погодан за програмере са мало кода_. Погледајте овај пример.                                                                                                                                     |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Овај чланак описује како да фајн-тунирате Hugging Face модел користећи Hugging Face transformers библиотеку на једном GPU-у са Azure DataBricks и Hugging Face Trainer библиотекама.                                                                                                                                               |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Каталог модела у Azure Machine Learning нуди много open source модела које можете фајн-тунирати за свој специфичан задатак. Испробајте овај модул из [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst).                                                                |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Фајн-тунинг GPT-3.5 или GPT-4 модела на Microsoft Azure уз помоћ W&B омогућава детаљно праћење и анализу перформанси модела. Овај водич проширује концепте из OpenAI Fine-Tuning водича са специфичним корацима и функцијама за Azure OpenAI.                                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                             |

## 2. Секундарни ресурси

Овај одељак садржи додатне ресурсе које вреди истражити, али нисмо имали времена да их обрадимо у овом часу. Могу бити обрађени у неком будућем часу или као секундарна опција задатка. За сада их користите да изградите своје знање и експертизу о овој теми.

| Наслов/Линк                                                                                                                                                                                                            | Опис                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **OpenAI Cookbook**: [Data preparation and analysis for chat model fine-tuning](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | Овај notebook служи као алат за претходну обраду и анализу сета података за фајн-тунинг chat модела. Проверава грешке у формату, пружа основне статистике и процењује број токена за трошкове фајн-тунинга. Погледајте: [Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                         |
| **OpenAI Cookbook**: [Fine-Tuning for Retrieval Augmented Generation (RAG) with Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | Циљ овог notebook-а је да вас проведе кроз свеобухватан пример како фајн-тунирати OpenAI моделе за Retrieval Augmented Generation (RAG). Такође ћемо интегрисати Qdrant и Few-Shot Learning како бисмо побољшали перформансе модела и смањили измишљене одговоре.                                                                                                                                                                                                                  |
| **OpenAI Cookbook**: [Fine-tuning GPT with Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) је платформа за AI програмере са алатима за тренирање модела, фајн-тунинг и коришћење основних модела. Прво прочитајте њихов [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) водич, а затим испробајте вежбу из Cookbook-а.                                                                                                                                                                         |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - фајн-тунинг за мале језичке моделе                                                   | Упознајте [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), Microsoft-ов нови мали модел, изузетно моћан али компактног формата. Овај туторијал ће вас провести кроз фајн-тунинг Phi-2, показујући како да направите јединствени скуп података и фајн-тунирате модел користећи QLoRA.                                                                                                         |
| **Hugging Face Tutorial** [How to Fine-Tune LLMs in 2024 with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Овај блог пост вас води кроз процес фајн-тунинга отворених LLM модела користећи Hugging Face TRL, Transformers и скупове података у 2024. Дефинишете случај употребе, подешавате развојно окружење, припремате скуп података, фајн-тунирате модел, тестирајте и процењујете, а затим га имплементирате у продукцију.                                                                                                                                    |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Омогућава брже и лакше тренирање и имплементацију [најсавременијих модела машинског учења](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Репозиторијум садржи туторијале прилагођене Colab-у са YouTube видео упутствима за фајн-тунинг. **Одражава недавну [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) надоградњу**. Прочитајте [AutoTrain документацију](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |

**Одрицање од одговорности**:  
Овај документ је преведен коришћењем AI преводилачке услуге [Co-op Translator](https://github.com/Azure/co-op-translator). Иако се трудимо да превод буде тачан, молимо вас да имате у виду да аутоматски преводи могу садржати грешке или нетачности. Оригинални документ на његовом изворном језику треба сматрати ауторитетним извором. За критичне информације препоручује се професионални људски превод. Нисмо одговорни за било каква неспоразума или погрешна тумачења која произилазе из коришћења овог превода.