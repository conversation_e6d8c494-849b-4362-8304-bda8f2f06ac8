<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:46:14+00:00",
  "source_file": "README.md",
  "language_code": "sr"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.sr.png)

### 21 лекција које вас уче свему што треба да знате да бисте почели да правите апликације засноване на генеративној вештачкој интелигенцији

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Подршка за више језика

#### Подржано преко GitHub Action (аутоматски и увек ажурирано)

[French](../fr/README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](../ur/README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](../ja/README.md) | [Korean](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](../el/README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](../he/README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](../sk/README.md) | [Romanian](../ro/README.md) | [Bulgarian](../bg/README.md) | [Serbian (Cyrillic)](./README.md) | [Croatian](../hr/README.md) | [Slovenian](../sl/README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Генеративна вештачка интелигенција за почетнике (Верзија 3) - Курс

Научите основе прављења апликација заснованих на генеративној вештачкој интелигенцији кроз наш свеобухватни курс од 21 лекције који су припремили Microsoft Cloud Advocates.

## 🌱 Почетак

Овај курс има 21 лекцију. Свака лекција покрива своју тему, па почните где год желите!

Лекције су означене као „Learn“ лекције које објашњавају концепт генеративне вештачке интелигенције или „Build“ лекције које објашњавају концепт и дају примере кода у оба језика, **Python** и **TypeScript**, кад год је могуће.

За .NET програмере погледајте [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)!

Свака лекција такође садржи одељак „Keep Learning“ са додатним алатима за учење.

## Шта вам је потребно
### За покретање кода из овог курса можете користити:
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **Лекције:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **Лекције:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **Лекције:** "oai-assignment" 
   
- Основно познавање Python-а или TypeScript-а је корисно - \*За апсолутне почетнике препоручујемо ове [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) и [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) курсеве
- GitHub налог да бисте [форковали цео овај репозиторијум](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) на свој налог

Направили смо лекцију **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** која ће вам помоћи да подесите развојно окружење.

Не заборавите да [означите (🌟) овај репозиторијум](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst) како бисте га лакше пронашли касније.

## 🧠 Спремни за деплој?

Ако тражите напредније примере кода, погледајте нашу [збирку примера кода за генеративну вештачку интелигенцију](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) на оба језика, **Python** и **TypeScript**.

## 🗣️ Упознајте друге ученике, добијте подршку

Придружите се нашем [званичном Azure AI Foundry Discord серверу](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) да упознате и умрежите се са другим ученицима овог курса и добијете подршку.

Постављајте питања или делите повратне информације о производу у нашем [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) на GitHub-у.

## 🚀 Правите стартап?

Пријавите се на [Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) и добијте **бесплатне OpenAI кредите** и до **150.000$ у Azure кредитима за приступ OpenAI моделима преко Azure OpenAI Services**.

## 🙏 Желите да помогнете?

Имате предлоге или сте пронашли правописне или кодне грешке? [Отворите issue](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) или [направите pull request](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Свака лекција садржи:

- Кратак видео увод у тему
- Писану лекцију у README-у
- Примере кода у Python-у и TypeScript-у који подржавају Azure OpenAI и OpenAI API
- Линкове ка додатним ресурсима за наставак учења

## 🗃️ Лекције

| #   | **Линк ка лекцији**                                                                                                                          | **Опис**                                                                                      | **Видео**                                                                   | **Додатно учење**                                                              |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Learn:** Како подесити развојно окружење                                                  | Видео ускоро                                                                | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Introduction to Generative AI and LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                              | **Learn:** Разумевање шта је генеративна вештачка интелигенција и како функционишу велики језички модели (LLMs). | [Видео](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Exploring and comparing different LLMs](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** Како изабрати прави модел за ваш случај употребе                                 | [Видео](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Using Generative AI Responsibly](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                           | **Learn:** Како одговорно правити апликације засноване на генеративној вештачкој интелигенцији | [Видео](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Understanding Prompt Engineering Fundamentals](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** Практичне најбоље праксе у Prompt Engineering-у                                  | [Видео](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Creating Advanced Prompts](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **Learn:** Како применити технике prompt engineering-а које побољшавају резултате ваших упита | [Видео](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Learn More](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Прављење апликација за генерисање текста](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Прави:** апликацију за генерисање текста користећи Azure OpenAI / OpenAI API                                | [Видео](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Прављење апликација за ћаскање](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Прави:** технике за ефикасно прављење и интеграцију апликација за ћаскање.               | [Видео](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Прављење претраживачких апликација и векторских база података](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Прави:** претраживачку апликацију која користи Embeddings за претрагу података.                        | [Видео](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Прављење апликација за генерисање слика](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Прави:** апликацију за генерисање слика                                                       | [Видео](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Прављење AI апликација са мало кода](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Прави:** генеративну AI апликацију користећи Low Code алате                                     | [Видео](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Интеграција спољних апликација са Function Calling](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **Прави:** шта је function calling и њене примене у апликацијама                          | [Видео](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [Дизајн UX за AI апликације](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Учите:** како применити принципе UX дизајна приликом развоја генеративних AI апликација         | [Видео](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Заштита ваших генеративних AI апликација](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Учите:** претње и ризике за AI системе и начине заштите тих система.             | [Видео](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Животни циклус генеративних AI апликација](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **Учите:** алате и метрике за управљање LLM животним циклусом и LLMOps                         | [Видео](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) и векторске базе података](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **Прави:** апликацију која користи RAG Framework за преузимање embeddings из векторских база података  | [Видео](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Open Source модели и Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **Прави:** апликацију користећи open source моделе доступне на Hugging Face                    | [Видео](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI агенти](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **Прави:** апликацију користећи AI Agent Framework                                           | [Видео](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [Фино подешавање LLM-ова](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Учите:** шта, зашто и како се врши фино подешавање LLM-ова                                            | [Видео](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [Прављење са SLM-овима](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Учите:** предности коришћења Small Language Models                                            | Видео ускоро | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Прављење са Mistral моделима](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Учите:** карактеристике и разлике Mistral породице модела                                           | Видео ускоро | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Прављење са Meta моделима](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Учите:** карактеристике и разлике Meta породице модела                                           | Видео ускоро | [Сазнајте више](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Посебне захвалности

Посебне захвалности [**John Aziz**](https://www.linkedin.com/in/john0isaac/) за креирање свих GitHub Actions и workflow-ова

[**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) за кључне доприносе свакој лекцији у циљу побољшања искуства учења и рада са кодом.

## 🎒 Остали курсеви

Наш тим прави и друге курсеве! Погледајте:

- [**НОВИ** Model Context Protocol за почетнике](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI агенти за почетнике](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Генеративни AI за почетнике користећи .NET](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [Генеративни AI за почетнике користећи JavaScript](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML за почетнике](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science за почетнике](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI за почетнике](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Cybersecurity за почетнике](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Web Dev за почетнике](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT за почетнике](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR развој за почетнике](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Мастеринг GitHub Copilot за AI парно програмирање](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [Мастеринг GitHub Copilot за C#/.NET програмере](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Изабери своју Copilot авантуру](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Одрицање од одговорности**:  
Овај документ је преведен коришћењем AI услуге за превођење [Co-op Translator](https://github.com/Azure/co-op-translator). Иако се трудимо да превод буде тачан, молимо вас да имате у виду да аутоматски преводи могу садржати грешке или нетачности. Оригинални документ на његовом изворном језику треба сматрати ауторитетним извором. За критичне информације препоручује се професионални људски превод. Нисмо одговорни за било каква неспоразума или погрешна тумачења која произилазе из коришћења овог превода.