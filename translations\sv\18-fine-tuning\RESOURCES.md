<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:03:05+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "sv"
}
-->
# Resurser för Självstyrt Lärande

Lektionerna är byggda med hjälp av flera kärnresurser från OpenAI och Azure OpenAI som referenser för terminologi och handledningar. Här är en icke uttömmande lista för din egen självstyrda läranderesa.

## 1. <PERSON><PERSON>ära Resurser

| Titel/Länk                                                                                                                                                                                                                 | Beskrivning                                                                                                                                                                                                                                                                                                                    |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning med OpenAI-modeller](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                   | Fine-tuning förbättrar few-shot learning genom att träna på många fler exempel än vad som får plats i prompten, vilket sparar kostnader, förbättrar svarskvaliteten och möjliggör förfrågningar med lägre latens. **Få en översikt av fine-tuning från OpenAI.**                                                                 |
| [Vad är Fine-Tuning med Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                     | Förstå **vad fine-tuning är (konceptet)**, varför du bör överväga det (motiverande problem), vilken data som ska användas (träning) och hur man mäter kvaliteten.                                                                                                                                                               |
| [Anpassa en modell med fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service låter dig anpassa våra modeller till dina egna dataset med hjälp av fine-tuning. Lär dig **hur du fine-tunar (processen)** och väljer modeller med Azure AI Studio, Python SDK eller REST API.                                                                                                               |
| [Rekommendationer för LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                  | LLM:er kan prestera dåligt på specifika domäner, uppgifter eller dataset, eller ge felaktiga eller missvisande svar. **När bör du överväga fine-tuning** som en möjlig lösning på detta?                                                                                                                                       |
| [Kontinuerlig Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)           | Kontinuerlig fine-tuning är en iterativ process där man väljer en redan fine-tunad modell som bas och **finjusterar den ytterligare** med nya träningsdata.                                                                                                                                                                   |
| [Fine-tuning och funktionsanrop](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fine-tuning av din modell **med exempel på funktionsanrop** kan förbättra modellens output genom att ge mer exakta och konsekventa svar – med liknande formaterade svar och kostnadsbesparingar.                                                                                                                               |
| [Fine-tuning Modeller: Azure OpenAI Riktlinjer](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                  | Kolla denna tabell för att förstå **vilka modeller som kan fine-tunas** i Azure OpenAI och i vilka regioner de finns tillgängliga. Kontrollera deras token-gränser och utgångsdatum för träningsdata vid behov.                                                                                                               |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Detta 30-minuters avsnitt från AI Show i oktober 2023 diskuterar fördelar, nackdelar och praktiska insikter som hjälper dig att fatta detta beslut.                                                                                                                                                                            |
| [Kom igång med LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                         | Denna **AI Playbook**-resurs guidar dig genom data-krav, formatering, hyperparameter-fine-tuning och utmaningar/begränsningar du bör känna till.                                                                                                                                                                              |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Lär dig skapa ett exempel på fine-tuning dataset, förbereda för fine-tuning, skapa ett fine-tuning-jobb och distribuera den fine-tunade modellen på Azure.                                                                                                                                                                       |
| **Tutorial**: [Fine-tune en Llama 2-modell i Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                    | Azure AI Studio låter dig anpassa stora språkmodeller till dina egna dataset _med ett UI-baserat arbetsflöde som passar lågkod-utvecklare_. Se detta exempel.                                                                                                                                                                   |
| **Tutorial**: [Fine-tune Hugging Face-modeller för en enda GPU på Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)             | Denna artikel beskriver hur man fine-tunar en Hugging Face-modell med Hugging Face transformers-biblioteket på en enda GPU med Azure DataBricks + Hugging Face Trainer-bibliotek.                                                                                                                                                 |
| **Utbildning:** [Fine-tune en foundation-modell med Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Modellkatalogen i Azure Machine Learning erbjuder många open source-modeller som du kan fine-tuna för din specifika uppgift. Prova denna modul som är [från AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                              | Fine-tuning av GPT-3.5 eller GPT-4-modeller på Microsoft Azure med W&B möjliggör detaljerad spårning och analys av modellprestanda. Denna guide bygger vidare på koncepten från OpenAI Fine-Tuning-guiden med specifika steg och funktioner för Azure OpenAI.                                                                 |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                               |

## 2. Sekundära Resurser

Denna sektion innehåller ytterligare resurser som är värda att utforska, men som vi inte hann täcka i denna lektion. De kan tas upp i en framtida lektion eller som ett sekundärt uppdragsalternativ vid ett senare tillfälle. Använd dem för att bygga din egen expertis och kunskap inom detta ämne.

| Titel/Länk                                                                                                                                                                                                                  | Beskrivning                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Datapreparering och analys för chatmodell-fine-tuning](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                              | Denna notebook fungerar som ett verktyg för att förbehandla och analysera chattdatasetet som används för fine-tuning av en chattmodell. Den kontrollerar formatfel, ger grundläggande statistik och uppskattar token-antal för kostnadsberäkning vid fine-tuning. Se: [Fine-tuning metod för gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning för Retrieval Augmented Generation (RAG) med Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)         | Syftet med denna notebook är att gå igenom ett omfattande exempel på hur man fine-tunar OpenAI-modeller för Retrieval Augmented Generation (RAG). Vi kommer även att integrera Qdrant och Few-Shot Learning för att förbättra modellens prestanda och minska felaktiga svar.                                                                                                                                                                                                                                        |
| **OpenAI Cookbook**: [Fine-tuning GPT med Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                                   | Weights & Biases (W&B) är en plattform för AI-utvecklare med verktyg för att träna modeller, fine-tuna modeller och använda foundation-modeller. Läs deras [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) guide först, och prova sedan Cookbook-övningen.                                                                                                                                                                                      |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning för små språkmodeller                                                           | Möt [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), Microsofts nya lilla modell, förvånansvärt kraftfull men kompakt. Denna tutorial guidar dig genom fine-tuning av Phi-2, visar hur man bygger ett unikt dataset och fine-tunar modellen med QLoRA.                                                                                                                                                                         |
| **Hugging Face Tutorial** [Hur man fine-tunar LLMs 2024 med Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                                     | Detta blogginlägg visar hur man fine-tunar öppna LLM:er med Hugging Face TRL, Transformers och datasets under 2024. Du definierar ett användningsfall, sätter upp en utvecklingsmiljö, förbereder ett dataset, fine-tunar modellen, testar och utvärderar den, och distribuerar sedan till produktion.                                                                                                                                                                                               |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                                | Ger snabbare och enklare träning och distribution av [state-of-the-art maskininlärningsmodeller](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Repo innehåller Colab-vänliga tutorials med YouTube-videoguider för fine-tuning. **Reflekterar senaste [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) uppdateringen**. Läs [AutoTrain-dokumentationen](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |

**Ansvarsfriskrivning**:  
Detta dokument har översatts med hjälp av AI-översättningstjänsten [Co-op Translator](https://github.com/Azure/co-op-translator). Även om vi strävar efter noggrannhet, vänligen observera att automatiska översättningar kan innehålla fel eller brister. Det ursprungliga dokumentet på dess modersmål bör betraktas som den auktoritativa källan. För kritisk information rekommenderas professionell mänsklig översättning. Vi ansvarar inte för några missförstånd eller feltolkningar som uppstår till följd av användningen av denna översättning.