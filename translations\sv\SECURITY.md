<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:54:00+00:00",
  "source_file": "SECURITY.md",
  "language_code": "sv"
}
-->
## Säkerhet

Microsoft tar säkerheten för våra mjukvaruprodukter och tjänster på största allvar, vilket inkluderar alla källkodsförråd som hanteras genom våra GitHub-organisationer, som inkluderar [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) och [våra GitHub-organisationer](https://opensource.microsoft.com/).

Om du tror att du har hittat en säkerhetsbrist i något Microsoft-ägt förråd som uppfyller [Microsofts definition av en säkerhetsbrist](https://aka.ms/opensource/security/definition), vänligen rapportera det till oss enligt beskrivningen nedan.

## Rapportera säkerhetsproblem

**Rapportera inte säkerhetsbrister via offentliga GitHub-ärenden.**

Rapportera dem istället till Microsoft Security Response Center (MSRC) på [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Om du föredrar att skicka in utan att logga in, skicka e-post till [<EMAIL>](mailto:<EMAIL>). Om möjligt, kryptera ditt meddelande med vår PGP-nyckel; ladda ner den från sidan för [Microsoft Security Response Center PGP Key](https://aka.ms/opensource/security/pgpkey).

Du bör få svar inom 24 timmar. Om du av någon anledning inte får det, följ upp via e-post för att säkerställa att vi mottagit ditt ursprungliga meddelande. Mer information finns på [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Vänligen inkludera den begärda informationen nedan (så mycket du kan) för att hjälpa oss att bättre förstå problemets natur och omfattning:

  * Typ av problem (t.ex. buffer overflow, SQL-injektion, cross-site scripting, etc.)
  * Fullständiga sökvägar till källkodsfiler relaterade till problemet
  * Platsen för den påverkade källkoden (tagg/branch/commit eller direkt URL)
  * Eventuell särskild konfiguration som krävs för att återskapa problemet
  * Steg-för-steg-instruktioner för att återskapa problemet
  * Proof-of-concept eller exploit-kod (om möjligt)
  * Problemets påverkan, inklusive hur en angripare kan utnyttja det

Denna information hjälper oss att snabbare hantera din rapport.

Om du rapporterar för en bug bounty kan mer fullständiga rapporter bidra till högre belöning. Besök vår [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty)-sida för mer information om våra aktiva program.

## Föredragna språk

Vi föredrar att all kommunikation sker på engelska.

## Policy

Microsoft följer principen för [Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd).

**Ansvarsfriskrivning**:  
Detta dokument har översatts med hjälp av AI-översättningstjänsten [Co-op Translator](https://github.com/Azure/co-op-translator). Även om vi strävar efter noggrannhet, vänligen observera att automatiska översättningar kan innehålla fel eller brister. Det ursprungliga dokumentet på dess modersmål bör betraktas som den auktoritativa källan. För kritisk information rekommenderas professionell mänsklig översättning. Vi ansvarar inte för några missförstånd eller feltolkningar som uppstår till följd av användningen av denna översättning.