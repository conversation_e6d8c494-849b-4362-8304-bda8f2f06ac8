<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "1a7fd0f95f9eb673b79da47c0814f4d4",
  "translation_date": "2025-07-09T13:31:27+00:00",
  "source_file": "09-building-image-applications/README.md",
  "language_code": "sw"
}
-->
# Kujenga Programu za Uundaji Picha

[![Kujenga Programu za Uundaji Picha](../../../translated_images/09-lesson-banner.906e408c741f44112ff5da17492a30d3872abb52b8530d6506c2631e86e704d0.sw.png)](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)

LLM siyo tu kwa ajili ya kuunda maandishi. Pia inawezekana kuunda picha kutoka kwa maelezo ya maandishi. Kuwa na picha kama njia ya mawasiliano kunaweza kuwa na manufaa makubwa katika maeneo mbalimbali kama MedTech, usanifu, utal<PERSON>, maendeleo ya michezo na mengineyo. Katika sura hii, tutaangalia mifano miwili maarufu ya uundaji picha, DALL-E na Midjourney.

## Utangulizi

Katika somo hili, tutashughulikia:

- Uundaji picha na kwa nini ni muhimu.
- DALL-E na Midjourney, ni nini, na jinsi zinavyofanya kazi.
- Jinsi unavyoweza kujenga programu ya uundaji picha.

## Malengo ya Kujifunza

Baada ya kumaliza somo hili, utaweza:

- Kujenga programu ya uundaji picha.
- Kuweka mipaka kwa programu yako kwa kutumia meta prompts.
- Kufanya kazi na DALL-E na Midjourney.

## Kwa nini kujenga programu ya uundaji picha?

Programu za uundaji picha ni njia nzuri ya kuchunguza uwezo wa AI ya Kizazi. Zinaweza kutumika kwa, kwa mfano:

- **Uhariri na muundo wa picha**. Unaweza kuunda picha kwa matumizi mbalimbali, kama uhariri wa picha na muundo wa picha.

- **Kutumika katika sekta mbalimbali**. Pia zinaweza kutumika kuunda picha kwa sekta mbalimbali kama Medtech, Utalii, Maendeleo ya michezo na mengineyo.

## Hali ya Mfano: Edu4All

Kama sehemu ya somo hili, tutaendelea kufanya kazi na startup yetu, Edu4All. Wanafunzi wataunda picha kwa ajili ya tathmini zao, ni picha gani hasa ni kwa wanafunzi kuamua, lakini zinaweza kuwa michoro ya hadithi zao au kuunda mhusika mpya kwa hadithi yao au kuwasaidia kuona mawazo na dhana zao.

Hapa ni mfano wa picha wanafunzi wa Edu4All wanaweza kuunda ikiwa wanajifunza darasani kuhusu miji ya kihistoria:

![Edu4All startup, class on monuments, Eiffel Tower](../../../translated_images/startup.94d6b79cc4bb3f5afbf6e2ddfcf309aa5d1e256b5f30cc41d252024eaa9cc5dc.sw.png)

wakitumia maelekezo kama

> "Mbwa kando ya Mnara wa Eiffel asubuhi mapema akielea jua"

## DALL-E na Midjourney ni nini?

[DALL-E](https://openai.com/dall-e-2?WT.mc_id=academic-105485-koreyst) na [Midjourney](https://www.midjourney.com/?WT.mc_id=academic-105485-koreyst) ni mifano miwili maarufu ya uundaji picha, inakuwezesha kutumia maelekezo kuunda picha.

### DALL-E

Tuanze na DALL-E, ambayo ni mfano wa AI ya Kizazi inayounda picha kutoka kwa maelezo ya maandishi.

> [DALL-E ni mchanganyiko wa mifano miwili, CLIP na diffused attention](https://towardsdatascience.com/openais-dall-e-and-clip-101-a-brief-introduction-3a4367280d4e?WT.mc_id=academic-105485-koreyst).

- **CLIP**, ni mfano unaounda embeddings, ambazo ni uwakilishi wa nambari wa data, kutoka kwa picha na maandishi.

- **Diffused attention**, ni mfano unaounda picha kutoka kwa embeddings. DALL-E imefundishwa kwa seti ya picha na maandishi na inaweza kutumika kuunda picha kutoka kwa maelezo ya maandishi. Kwa mfano, DALL-E inaweza kutumika kuunda picha ya paka akiwa na kofia, au mbwa akiwa na mohawk.

### Midjourney

Midjourney hufanya kazi kwa njia inayofanana na DALL-E, inaunda picha kutoka kwa maelekezo ya maandishi. Midjourney pia inaweza kutumika kuunda picha kwa kutumia maelekezo kama “paka akiwa na kofia”, au “mbwa akiwa na mohawk”.

![Image generated by Midjourney, mechanical pigeon](https://upload.wikimedia.org/wikipedia/commons/thumb/8/8c/Rupert_Breheny_mechanical_dove_eca144e7-476d-4976-821d-a49c408e4f36.png/440px-Rupert_Breheny_mechanical_dove_eca144e7-476d-4976-821d-a49c408e4f36.png?WT.mc_id=academic-105485-koreyst)  
_Picha imetolewa na Wikipedia, picha iliyoundwa na Midjourney_

## DALL-E na Midjourney hufanya kazi vipi

Kwanza, [DALL-E](https://arxiv.org/pdf/2102.12092.pdf?WT.mc_id=academic-105485-koreyst). DALL-E ni mfano wa AI ya Kizazi unaotegemea usanifu wa transformer na _autoregressive transformer_.

_Autoregressive transformer_ inaeleza jinsi mfano unavyounda picha kutoka kwa maelezo ya maandishi, huunda pixel moja kwa moja, kisha hutumia pixel zilizoundwa kuunda pixel inayofuata. Hupitia tabaka mbalimbali katika mtandao wa neva, hadi picha itakapokamilika.

Kwa mchakato huu, DALL-E hudhibiti sifa, vitu, tabia, na zaidi katika picha inayoundwa. Hata hivyo, DALL-E 2 na 3 zina udhibiti zaidi juu ya picha inayoundwa.

## Kujenga programu yako ya kwanza ya uundaji picha

Basi, inachukua nini kujenga programu ya uundaji picha? Unahitaji maktaba zifuatazo:

- **python-dotenv**, inashauriwa sana kutumia maktaba hii kuweka siri zako katika faili la _.env_ mbali na msimbo.
- **openai**, maktaba hii ndiyo utakayotumia kuwasiliana na API ya OpenAI.
- **pillow**, kwa ajili ya kufanya kazi na picha katika Python.
- **requests**, kusaidia kutuma maombi ya HTTP.

1. Tengeneza faili _.env_ yenye maudhui yafuatayo:

   ```text
   AZURE_OPENAI_ENDPOINT=<your endpoint>
   AZURE_OPENAI_API_KEY=<your key>
   ```

   Tafuta taarifa hii katika Azure Portal kwa rasilimali yako katika sehemu ya "Keys and Endpoint".

1. Kusanya maktaba zilizotajwa katika faili liitwalo _requirements.txt_ kama ifuatavyo:

   ```text
   python-dotenv
   openai
   pillow
   requests
   ```

1. Kisha, tengeneza mazingira ya virtual na usakinishe maktaba:

   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

   Kwa Windows, tumia amri zifuatazo kuunda na kuanzisha mazingira ya virtual:

   ```bash
   python3 -m venv venv
   venv\Scripts\activate.bat
   ```

1. Ongeza msimbo ufuatao katika faili liitwalo _app.py_:

   ```python
   import openai
   import os
   import requests
   from PIL import Image
   import dotenv

   # import dotenv
   dotenv.load_dotenv()

   # Get endpoint and key from environment variables
   openai.api_base = os.environ['AZURE_OPENAI_ENDPOINT']
   openai.api_key = os.environ['AZURE_OPENAI_API_KEY']

   # Assign the API version (DALL-E is currently supported for the 2023-06-01-preview API version only)
   openai.api_version = '2023-06-01-preview'
   openai.api_type = 'azure'


   try:
       # Create an image by using the image generation API
       generation_response = openai.Image.create(
           prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
           size='1024x1024',
           n=2,
           temperature=0,
       )
       # Set the directory for the stored image
       image_dir = os.path.join(os.curdir, 'images')

       # If the directory doesn't exist, create it
       if not os.path.isdir(image_dir):
           os.mkdir(image_dir)

       # Initialize the image path (note the filetype should be png)
       image_path = os.path.join(image_dir, 'generated-image.png')

       # Retrieve the generated image
       image_url = generation_response["data"][0]["url"]  # extract image URL from response
       generated_image = requests.get(image_url).content  # download the image
       with open(image_path, "wb") as image_file:
           image_file.write(generated_image)

       # Display the image in the default image viewer
       image = Image.open(image_path)
       image.show()

   # catch exceptions
   except openai.InvalidRequestError as err:
       print(err)

   ```

Hebu tuelezee msimbo huu:

- Kwanza, tunaingiza maktaba tunazohitaji, ikiwa ni pamoja na maktaba ya OpenAI, dotenv, requests, na Pillow.

  ```python
  import openai
  import os
  import requests
  from PIL import Image
  import dotenv
  ```

- Kisha, tunapakia mabadiliko ya mazingira kutoka kwa faili _.env_.

  ```python
  # import dotenv
  dotenv.load_dotenv()
  ```

- Baadaye, tunaweka endpoint, ufunguo wa API ya OpenAI, toleo na aina.

  ```python
  # Get endpoint and key from environment variables
  openai.api_base = os.environ['AZURE_OPENAI_ENDPOINT']
  openai.api_key = os.environ['AZURE_OPENAI_API_KEY']

  # add version and type, Azure specific
  openai.api_version = '2023-06-01-preview'
  openai.api_type = 'azure'
  ```

- Kisha, tunaunda picha:

  ```python
  # Create an image by using the image generation API
  generation_response = openai.Image.create(
      prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
      size='1024x1024',
      n=2,
      temperature=0,
  )
  ```

  Msimbo huu unajibu na kitu cha JSON chenye URL ya picha iliyoundwa. Tunaweza kutumia URL hii kupakua picha na kuihifadhi kwenye faili.

- Mwishowe, tunafungua picha na kutumia mtazamaji wa picha wa kawaida kuionyesha:

  ```python
  image = Image.open(image_path)
  image.show()
  ```

### Maelezo zaidi kuhusu uundaji picha

Tazama msimbo unaounda picha kwa undani zaidi:

```python
generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2,
        temperature=0,
    )
```

- **prompt**, ni maelekezo ya maandishi yanayotumika kuunda picha. Katika kesi hii, tunatumia maelekezo "Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils".
- **size**, ni ukubwa wa picha inayoundwa. Hapa, tunaunda picha yenye ukubwa wa 1024x1024 pixels.
- **n**, ni idadi ya picha zinazoundwa. Hapa, tunaunda picha mbili.
- **temperature**, ni kipimo kinachodhibiti nasibu ya matokeo ya mfano wa AI ya Kizazi. Joto ni thamani kati ya 0 na 1 ambapo 0 ina maana matokeo ni thabiti na 1 ina maana matokeo ni ya nasibu. Thamani ya kawaida ni 0.7.

Kuna mambo zaidi unayoweza kufanya na picha ambayo tutayajadili katika sehemu inayofuata.

## Uwezo Zaidi wa Uundaji Picha

Umeona hadi sasa jinsi tulivyoweza kuunda picha kwa mistari michache ya Python. Hata hivyo, kuna mambo zaidi unayoweza kufanya na picha.

Unaweza pia kufanya yafuatayo:

- **Fanya marekebisho**. Kwa kutoa picha iliyopo, mask na maelekezo, unaweza kubadilisha picha. Kwa mfano, unaweza kuongeza kitu sehemu ya picha. Fikiria picha yetu ya sungura, unaweza kuongeza kofia kwa sungura. Unavyofanya hivyo ni kwa kutoa picha, mask (kuonyesha sehemu ya eneo la mabadiliko) na maelekezo ya maandishi kueleza kinachotakiwa kufanywa.

  ```python
  response = openai.Image.create_edit(
    image=open("base_image.png", "rb"),
    mask=open("mask.png", "rb"),
    prompt="An image of a rabbit with a hat on its head.",
    n=1,
    size="1024x1024"
  )
  image_url = response['data'][0]['url']
  ```

  Picha ya msingi itakuwa na sungura tu lakini picha ya mwisho itakuwa na kofia juu ya sungura.

- **Tengeneza mabadiliko**. Wazo ni kuchukua picha iliyopo na kuomba mabadiliko ya picha hiyo. Kutengeneza mabadiliko, unatoa picha na maelekezo ya maandishi na msimbo kama ifuatavyo:

  ```python
  response = openai.Image.create_variation(
    image=open("bunny-lollipop.png", "rb"),
    n=1,
    size="1024x1024"
  )
  image_url = response['data'][0]['url']
  ```

  > Kumbuka, hii inaungwa mkono tu na OpenAI

## Joto (Temperature)

Joto ni kipimo kinachodhibiti nasibu ya matokeo ya mfano wa AI ya Kizazi. Joto ni thamani kati ya 0 na 1 ambapo 0 ina maana matokeo ni thabiti na 1 ina maana matokeo ni ya nasibu. Thamani ya kawaida ni 0.7.

Tazama mfano wa jinsi joto linavyofanya kazi, kwa kuendesha maelekezo haya mara mbili:

> Maelekezo: "Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils"

![Bunny on a horse holding a lollipop, version 1](../../../translated_images/v1-generated-image.a295cfcffa3c13c2432eb1e41de7e49a78c814000fb1b462234be24b6e0db7ea.sw.png)

Sasa tuchukue maelekezo hayo tena kuona kwamba hatutapata picha ile ile mara mbili:

![Generated image of bunny on horse](../../../translated_images/v2-generated-image.33f55a3714efe61dc19622c869ba6cd7d6e6de562e26e95b5810486187aace39.sw.png)

Kama unavyoona, picha zinafanana, lakini si sawa kabisa. Hebu jaribu kubadilisha thamani ya joto kuwa 0.1 na tazama kinachotokea:

```python
 generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2
    )
```

### Kubadilisha joto

Basi jaribu kufanya majibu kuwa thabiti zaidi. Tunaweza kuona kutoka kwa picha mbili tulizounda kwamba katika picha ya kwanza, kuna sungura na katika picha ya pili, kuna farasi, hivyo picha zinatofautiana sana.

Kwa hiyo, badilisha msimbo wetu na kuweka joto kuwa 0, kama ifuatavyo:

```python
generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2,
        temperature=0
    )
```

Sasa unapotekeleza msimbo huu, unapata picha hizi mbili:

- ![Temperature 0, v1](../../../translated_images/v1-temp-generated-image.a4346e1d2360a056d855ee3dfcedcce91211747967cb882e7d2eff2076f90e4a.sw.png)
- ![Temperature 0 , v2](../../../translated_images/v2-temp-generated-image.871d0c920dbfb0f1cb5d9d80bffd52da9b41f83b386320d9a9998635630ec83d.sw.png)

Hapa unaweza kuona wazi jinsi picha zinavyofanana zaidi.

## Jinsi ya kuweka mipaka kwa programu yako kwa kutumia metaprompts

Kwa demo yetu, tayari tunaweza kuunda picha kwa wateja wetu. Hata hivyo, tunahitaji kuweka mipaka kwa programu yetu.

Kwa mfano, hatutaki kuunda picha zisizofaa kazini, au zisizofaa kwa watoto.

Tunaweza kufanya hivi kwa kutumia _metaprompts_. Metaprompts ni maelekezo ya maandishi yanayotumika kudhibiti matokeo ya mfano wa AI ya Kizazi. Kwa mfano, tunaweza kutumia metaprompts kudhibiti matokeo, na kuhakikisha picha zinazoundwa ni salama kazini, au zinazofaa kwa watoto.

### Hufanya kazi vipi?

Sasa, metaprompts hufanya kazi vipi?

Metaprompts ni maelekezo ya maandishi yanayotumika kudhibiti matokeo ya mfano wa AI ya Kizazi, huwekwa kabla ya maelekezo ya maandishi, na hutumika kudhibiti matokeo ya mfano na huingizwa katika programu kudhibiti matokeo ya mfano. Hufunga maelekezo ya prompt na meta prompt katika maelekezo moja ya maandishi.

Mfano mmoja wa meta prompt ungekuwa kama ifuatavyo:

```text
You are an assistant designer that creates images for children.

The image needs to be safe for work and appropriate for children.

The image needs to be in color.

The image needs to be in landscape orientation.

The image needs to be in a 16:9 aspect ratio.

Do not consider any input from the following that is not safe for work or appropriate for children.

(Input)

```

Sasa, tazama jinsi tunavyoweza kutumia metaprompts katika demo yetu.

```python
disallow_list = "swords, violence, blood, gore, nudity, sexual content, adult content, adult themes, adult language, adult humor, adult jokes, adult situations, adult"

meta_prompt =f"""You are an assistant designer that creates images for children.

The image needs to be safe for work and appropriate for children.

The image needs to be in color.

The image needs to be in landscape orientation.

The image needs to be in a 16:9 aspect ratio.

Do not consider any input from the following that is not safe for work or appropriate for children.
{disallow_list}
"""

prompt = f"{meta_prompt}
Create an image of a bunny on a horse, holding a lollipop"

# TODO add request to generate image
```

Kutoka kwa maelekezo hapo juu, unaweza kuona jinsi picha zote zinazoundwa zinazingatia metaprompt.

## Kazi ya Nyumbani - tuwezeshe wanafunzi

Tulianzisha Edu4All mwanzoni mwa somo hili. Sasa ni wakati wa kuwawezesha wanafunzi kuunda picha kwa ajili ya tathmini zao.

Wanafunzi wataunda picha za tathmini zao zenye miji ya kihistoria, ni kwa wanafunzi kuamua ni miji gani hasa. Wanafunzi wanahimizwa kutumia ubunifu wao katika kazi hii kuweka miji hiyo katika muktadha tofauti.

## Suluhisho

Hapa kuna suluhisho moja linalowezekana:

```python
import openai
import os
import requests
from PIL import Image
import dotenv

# import dotenv
dotenv.load_dotenv()

# Get endpoint and key from environment variables
openai.api_base = "<replace with endpoint>"
openai.api_key = "<replace with api key>"

# Assign the API version (DALL-E is currently supported for the 2023-06-01-preview API version only)
openai.api_version = '2023-06-01-preview'
openai.api_type = 'azure'

disallow_list = "swords, violence, blood, gore, nudity, sexual content, adult content, adult themes, adult language, adult humor, adult jokes, adult situations, adult"

meta_prompt = f"""You are an assistant designer that creates images for children.

The image needs to be safe for work and appropriate for children.

The image needs to be in color.

The image needs to be in landscape orientation.

The image needs to be in a 16:9 aspect ratio.

Do not consider any input from the following that is not safe for work or appropriate for children.
{disallow_list}"""

prompt = f"""{meta_prompt}
Generate monument of the Arc of Triumph in Paris, France, in the evening light with a small child holding a Teddy looks on.
""""

try:
    # Create an image by using the image generation API
    generation_response = openai.Image.create(
        prompt=prompt,    # Enter your prompt text here
        size='1024x1024',
        n=2,
        temperature=0,
    )
    # Set the directory for the stored image
    image_dir = os.path.join(os.curdir, 'images')

    # If the directory doesn't exist, create it
    if not os.path.isdir(image_dir):
        os.mkdir(image_dir)

    # Initialize the image path (note the filetype should be png)
    image_path = os.path.join(image_dir, 'generated-image.png')

    # Retrieve the generated image
    image_url = generation_response["data"][0]["url"]  # extract image URL from response
    generated_image = requests.get(image_url).content  # download the image
    with open(image_path, "wb") as image_file:
        image_file.write(generated_image)

    # Display the image in the default image viewer
    image = Image.open(image_path)
    image.show()

# catch exceptions
except openai.InvalidRequestError as err:
    print(err)
```

## Kazi Nzuri! Endelea Kujifunza

Baada ya kumaliza somo hili, tembelea [Mkusanyiko wetu wa Kujifunza AI ya Kizazi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) ili kuendelea kuongeza ujuzi wako wa AI ya Kizazi!

Nenda kwenye Somo la 10 ambapo tutaangalia jinsi ya [kujenga programu za AI kwa kutumia low-code](../10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)

**Kiarifu cha Kutotegemea**:  
Hati hii imetafsiriwa kwa kutumia huduma ya tafsiri ya AI [Co-op Translator](https://github.com/Azure/co-op-translator). Ingawa tunajitahidi kwa usahihi, tafadhali fahamu kuwa tafsiri za kiotomatiki zinaweza kuwa na makosa au upungufu wa usahihi. Hati ya asili katika lugha yake ya asili inapaswa kuchukuliwa kama chanzo cha mamlaka. Kwa taarifa muhimu, tafsiri ya kitaalamu inayofanywa na binadamu inapendekezwa. Hatubebei dhamana kwa kutoelewana au tafsiri potofu zinazotokana na matumizi ya tafsiri hii.