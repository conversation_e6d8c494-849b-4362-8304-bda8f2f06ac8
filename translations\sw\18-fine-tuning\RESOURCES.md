<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:06:59+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "sw"
}
-->
# <PERSON><PERSON><PERSON><PERSON> Kwa Kujifunza Binafsi

Somo hili limejengwa kwa kutumia rasilimali kuu kadhaa kutoka OpenAI na Azure OpenAI kama marejeleo ya istilahi na mafunzo. Hapa kuna orodha isiyo kamili, kwa ajili ya safari zako za kujifunza binafsi.

## 1. <PERSON><PERSON><PERSON><PERSON>

| Kichwa/Kiungo                                                                                                                                                                                                                   | Maelezo                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning huongeza ufanisi wa kujifunza kwa mifano michache kwa kufundisha kwa mifano mingi zaidi kuliko inavyoweza kuingizwa kwenye prompt, ikikuokoa gharama, kuboresha ubora wa majibu, na kuwezesha maombi yenye ucheleweshaji mdogo. **Pata muhtasari wa fine-tuning kutoka OpenAI.**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Elewa **nini maana ya fine-tuning (dhana)**, kwa nini unapaswa kuangalia hili (tatizo linalochochea), data gani ya kutumia (mafunzo) na jinsi ya kupima ubora                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Huduma ya Azure OpenAI inakuwezesha kubinafsisha mifano yetu kwa kutumia seti zako binafsi za data kwa njia ya fine-tuning. Jifunze **jinsi ya kufanya fine-tuning (mchakato)** kwa kuchagua mifano kupitia Azure AI Studio, Python SDK au REST API.                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLMs zinaweza zisifanye kazi vizuri katika maeneo maalum, kazi fulani, au seti za data, au zinaweza kutoa matokeo yasiyo sahihi au yanayochanganya. **Lini unapaswa kuzingatia fine-tuning** kama suluhisho linalowezekana?                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Fine-tuning endelevu ni mchakato wa kurudia wa kuchagua mfano ulioshafinyangwa kama msingi na **kuufinyanga zaidi** kwa seti mpya za mifano ya mafunzo.                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Kufinyanga mfano wako **kwa kutumia mifano ya kupiga simu za kazi** kunaweza kuboresha matokeo kwa kupata majibu sahihi na thabiti - kwa majibu yenye muundo sawa na kuokoa gharama                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Angalia jedwali hili kuelewa **mifano gani inaweza kufinyangwa** katika Azure OpenAI, na katika maeneo gani zinapatikana. Angalia vizingiti vya tokeni na tarehe za kumalizika kwa data za mafunzo ikiwa unahitaji.                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Kipindi hiki cha dakika 30 cha **Oktoba 2023** cha AI Show kinajadili faida, hasara na maarifa ya vitendo yanayokusaidia kufanya uamuzi huu.                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Rasilimali hii ya **AI Playbook** inakuongoza kupitia mahitaji ya data, uundaji wa muundo, fine-tuning ya hyperparameters na changamoto/vizingiti unavyopaswa kujua.                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Jifunze kuunda sampuli ya seti ya data ya fine-tuning, kujiandaa kwa fine-tuning, kuanzisha kazi ya fine-tuning, na kupeleka mfano uliobinafsishwa kwenye Azure.                                                                                                                                                                                    |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio inakuwezesha kubinafsisha mifano mikubwa ya lugha kwa kutumia seti zako binafsi za data _kwa kutumia mtiririko wa kazi unaotegemea UI unaofaa kwa waendelezaji wa low-code_. Tazama mfano huu.                                                                                                                                                               |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Makala hii inaelezea jinsi ya kufinyanga mfano wa Hugging Face kwa kutumia maktaba ya Hugging Face transformers kwenye GPU moja kwa kutumia Azure DataBricks + maktaba ya Hugging Face Trainer                                                                                                                                                |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Katalogi ya mifano katika Azure Machine Learning inatoa mifano mingi ya chanzo huria ambayo unaweza kufinyanga kwa kazi yako maalum. Jaribu moduli hii ni [kutoka kwenye AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Kufinyanga mifano ya GPT-3.5 au GPT-4 kwenye Microsoft Azure kwa kutumia W&B kunaruhusu ufuatiliaji wa kina na uchambuzi wa utendaji wa mfano. Mwongozo huu unaongeza dhana kutoka kwenye Mwongozo wa OpenAI Fine-Tuning kwa hatua maalum na vipengele vya Azure OpenAI.                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. Rasilimali Zaidi

Sehemu hii inakusanya rasilimali za ziada zinazostahili kuchunguzwa, lakini hatukuwa na muda wa kuzifunika katika somo hili. Huenda zikafunikwa katika somo lijalo, au kama chaguo la kazi ya ziada, baadaye. Kwa sasa, zitumie kujenga utaalamu na maarifa yako kuhusu mada hii.

| Kichwa/Kiungo                                                                                                                                                                                                            | Maelezo                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Maandalizi na uchambuzi wa data kwa fine-tuning ya mfano wa mazungumzo](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | Daftari hili ni chombo cha kuandaa na kuchambua seti ya data ya mazungumzo inayotumika kwa fine-tuning ya mfano wa mazungumzo. Linakagua makosa ya muundo, hutoa takwimu za msingi, na kukadiria idadi ya tokeni kwa gharama za fine-tuning. Angalia: [Njia ya fine-tuning kwa gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning kwa Retrieval Augmented Generation (RAG) na Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | Lengo la daftari hili ni kuonyesha mfano kamili wa jinsi ya kufinyanga mifano ya OpenAI kwa Retrieval Augmented Generation (RAG). Pia tutajumuisha Qdrant na Few-Shot Learning ili kuongeza utendaji wa mfano na kupunguza uongo.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT na Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) ni jukwaa la waendelezaji wa AI, lenye zana za kufundisha mifano, kufinyanga mifano, na kutumia mifano ya msingi. Soma mwongozo wao wa [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) kwanza, kisha jaribu zoezi la Cookbook.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning kwa Mifano Midogo ya Lugha                                                   | Kutana na [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), mfano mdogo mpya wa Microsoft, wenye nguvu kubwa lakini mdogo. Mafunzo haya yatakuongoza jinsi ya kufinyanga Phi-2, kuonyesha jinsi ya kuunda seti ya data ya kipekee na kufinyanga mfano kwa kutumia QLoRA.                                                                                                                                                                       |
| **Hugging Face Tutorial** [Jinsi ya Kufinyanga LLMs mwaka 2024 na Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Chapisho hili la blogu linakuongoza jinsi ya kufinyanga LLMs za wazi kwa kutumia Hugging Face TRL, Transformers na seti za data mwaka 2024. Unaeleza matumizi, kuanzisha mazingira ya maendeleo, kuandaa seti ya data, kufinyanga mfano, kupima na kutathmini, kisha kupeleka kwenye uzalishaji.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Inaleta mafunzo na upeleka haraka na rahisi wa [mifano ya hali ya juu ya mashine ya kujifunza](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Repo ina mafunzo yanayofaa kwa Colab na mwongozo wa video za YouTube, kwa fine-tuning. **Inaonyesha sasisho la hivi karibuni la [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)**. Soma [nyaraka za AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Kiarifu cha Kutotegemea**:  
Hati hii imetafsiriwa kwa kutumia huduma ya tafsiri ya AI [Co-op Translator](https://github.com/Azure/co-op-translator). Ingawa tunajitahidi kwa usahihi, tafadhali fahamu kwamba tafsiri za kiotomatiki zinaweza kuwa na makosa au upungufu wa usahihi. Hati ya asili katika lugha yake ya asili inapaswa kuchukuliwa kama chanzo cha mamlaka. Kwa taarifa muhimu, tafsiri ya kitaalamu inayofanywa na binadamu inapendekezwa. Hatubebei dhamana kwa kutoelewana au tafsiri potofu zinazotokana na matumizi ya tafsiri hii.