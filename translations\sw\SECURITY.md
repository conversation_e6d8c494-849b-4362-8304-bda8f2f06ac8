<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:55:26+00:00",
  "source_file": "SECURITY.md",
  "language_code": "sw"
}
-->
## Usalama

Microsoft inachukulia usalama wa bidhaa na huduma zetu za programu kwa umakini mkubwa, ikiwa ni pamoja na hifadhidata zote za msimbo wa chanzo zinazosimamiwa kupitia mashirika yetu ya GitHub, ambayo ni pamoja na [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin), na [mashirika yetu ya GitHub](https://opensource.microsoft.com/).

Ka<PERSON> unaamini umebaini udhaifu wa usalama katika hifadhidata yoyote inayomilikiwa na Microsoft ambayo inakidhi [ufafanuzi wa Microsoft wa udhaifu wa usalama](https://aka.ms/opensource/security/definition), tafadhali ripoti kwetu kama ilivyoelezwa hapa chini.

## Kuripoti Masuala ya Usalama

**Tafadhali usiripoti udhaifu wa usalama kupitia masuala ya umma ya GitHub.**

Badala yake, ripoti masuala hayo kwa Microsoft Security Response Center (MSRC) kupitia [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Kama unapendelea kuwasilisha bila kuingia kwenye akaunti, tuma barua pepe kwa [<EMAIL>](mailto:<EMAIL>). Ikiwezekana, funga ujumbe wako kwa kutumia ufunguo wetu wa PGP; tafadhali pakua kutoka kwenye [ukurasa wa Microsoft Security Response Center PGP Key](https://aka.ms/opensource/security/pgpkey).

Unapaswa kupokea majibu ndani ya masaa 24. Ikiwa kwa sababu yoyote hautapokea, tafadhali fuatilia kupitia barua pepe ili kuhakikisha tumepokea ujumbe wako wa awali. Taarifa zaidi zinaweza kupatikana kwenye [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Tafadhali jumuisha taarifa ulizozitajwa hapa chini (kadri uwezavyo) ili kutusaidia kuelewa vyema asili na wigo wa tatizo linalowezekana:

  * Aina ya tatizo (mfano: buffer overflow, SQL injection, cross-site scripting, n.k.)
  * Njia kamili za faili za chanzo zinazohusiana na kuibuka kwa tatizo
  * Mahali pa msimbo wa chanzo ulioathirika (tag/branch/commit au URL moja kwa moja)
  * Mipangilio maalum inayohitajika kuonyesha tatizo tena
  * Maelekezo hatua kwa hatua ya kuonyesha tatizo tena
  * Mshauri wa dhana au msimbo wa udhaifu (ikiwa inawezekana)
  * Athari za tatizo, ikiwa ni pamoja na jinsi mshambuliaji anavyoweza kutumia tatizo hilo

Taarifa hizi zitatusaidia kushughulikia ripoti yako kwa haraka zaidi.

Kama unaripoti kwa ajili ya zawadi ya mdudu, ripoti kamili zaidi zinaweza kuchangia tuzo kubwa zaidi. Tafadhali tembelea ukurasa wetu wa [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) kwa maelezo zaidi kuhusu programu zetu zinazofanya kazi.

## Lugha Zinazopendekezwa

Tunapendelea mawasiliano yote yafanyike kwa Kiingereza.

## Sera

Microsoft inafuata kanuni ya [Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd).

**Kiarifu cha Kutotegemea**:  
Hati hii imetafsiriwa kwa kutumia huduma ya tafsiri ya AI [Co-op Translator](https://github.com/Azure/co-op-translator). Ingawa tunajitahidi kwa usahihi, tafadhali fahamu kwamba tafsiri za kiotomatiki zinaweza kuwa na makosa au upungufu wa usahihi. Hati ya asili katika lugha yake ya asili inapaswa kuchukuliwa kama chanzo cha mamlaka. Kwa taarifa muhimu, tafsiri ya kitaalamu inayofanywa na binadamu inapendekezwa. Hatubebei dhamana kwa kutoelewana au tafsiri potofu zinazotokana na matumizi ya tafsiri hii.