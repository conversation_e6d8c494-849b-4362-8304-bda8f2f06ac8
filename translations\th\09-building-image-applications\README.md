<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "1a7fd0f95f9eb673b79da47c0814f4d4",
  "translation_date": "2025-07-09T13:26:14+00:00",
  "source_file": "09-building-image-applications/README.md",
  "language_code": "th"
}
-->
# การสร้างแอปพลิเคชันสร้างภาพ

[![การสร้างแอปพลิเคชันสร้างภาพ](../../../translated_images/09-lesson-banner.906e408c741f44112ff5da17492a30d3872abb52b8530d6506c2631e86e704d0.th.png)](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)

ความสามารถของ LLMs ไม่ได้จำกัดแค่การสร้างข้อความเท่านั้น แต่ยังสามารถสร้างภาพจากคำบรรยายข้อความได้อีกด้วย การมีภาพเป็นรูปแบบข้อมูลหนึ่งสามารถมีประโยชน์อย่างมากในหลายๆ ด้าน เช่น MedTech, สถาปัตยกรรม, การท่องเที่ยว, การพัฒนาเกม และอื่นๆ ในบทนี้ เราจะมาดูสองโมเดลสร้างภาพที่ได้รับความนิยมมากที่สุด คือ DALL-E และ Midjourney

## บทนำ

ในบทเรียนนี้ เราจะพูดถึง:

- การสร้างภาพและเหตุผลที่มันมีประโยชน์
- DALL-E และ Midjourney คืออะไร และทำงานอย่างไร
- วิธีการสร้างแอปพลิเคชันสร้างภาพ

## เป้าหมายการเรียนรู้

หลังจากเรียนบทนี้จบ คุณจะสามารถ:

- สร้างแอปพลิเคชันสร้างภาพได้
- กำหนดขอบเขตของแอปพลิเคชันด้วย meta prompts
- ใช้งาน DALL-E และ Midjourney ได้

## ทำไมต้องสร้างแอปพลิเคชันสร้างภาพ?

แอปพลิเคชันสร้างภาพเป็นวิธีที่ดีในการสำรวจความสามารถของ Generative AI ตัวอย่างการใช้งาน เช่น

- **แก้ไขและสังเคราะห์ภาพ** คุณสามารถสร้างภาพสำหรับกรณีการใช้งานต่างๆ เช่น การแก้ไขภาพและการสังเคราะห์ภาพ

- **ประยุกต์ใช้ในหลายอุตสาหกรรม** สามารถใช้สร้างภาพสำหรับอุตสาหกรรมต่างๆ เช่น Medtech, การท่องเที่ยว, การพัฒนาเกม และอื่นๆ

## กรณีศึกษา: Edu4All

ในบทเรียนนี้ เราจะทำงานร่วมกับสตาร์ทอัพ Edu4All ต่อไป นักเรียนจะสร้างภาพสำหรับการประเมินผลของพวกเขา ภาพที่สร้างขึ้นจะขึ้นอยู่กับนักเรียนเอง อาจเป็นภาพประกอบนิทานของตัวเอง สร้างตัวละครใหม่สำหรับเรื่องราว หรือช่วยให้พวกเขาเห็นภาพความคิดและแนวคิดของตน

นี่คือตัวอย่างภาพที่นักเรียน Edu4All อาจสร้างขึ้นหากพวกเขาทำงานในชั้นเรียนเกี่ยวกับอนุสาวรีย์:

![Edu4All startup, class on monuments, Eiffel Tower](../../../translated_images/startup.94d6b79cc4bb3f5afbf6e2ddfcf309aa5d1e256b5f30cc41d252024eaa9cc5dc.th.png)

โดยใช้ prompt เช่น

> "Dog next to Eiffel Tower in early morning sunlight"

## DALL-E และ Midjourney คืออะไร?

[DALL-E](https://openai.com/dall-e-2?WT.mc_id=academic-105485-koreyst) และ [Midjourney](https://www.midjourney.com/?WT.mc_id=academic-105485-koreyst) เป็นโมเดลสร้างภาพที่ได้รับความนิยมสูงสุดสองตัว ซึ่งช่วยให้คุณใช้ prompt เพื่อสร้างภาพได้

### DALL-E

เริ่มจาก DALL-E ซึ่งเป็นโมเดล Generative AI ที่สร้างภาพจากคำบรรยายข้อความ

> [DALL-E เป็นการผสมผสานของสองโมเดล คือ CLIP และ diffused attention](https://towardsdatascience.com/openais-dall-e-and-clip-101-a-brief-introduction-3a4367280d4e?WT.mc_id=academic-105485-koreyst)

- **CLIP** คือโมเดลที่สร้าง embeddings ซึ่งเป็นตัวแทนเชิงตัวเลขของข้อมูลจากภาพและข้อความ

- **Diffused attention** คือโมเดลที่สร้างภาพจาก embeddings DALL-E ถูกฝึกด้วยชุดข้อมูลภาพและข้อความ และสามารถใช้สร้างภาพจากคำบรรยายข้อความได้ เช่น สร้างภาพแมวใส่หมวก หรือสุนัขที่มีทรงผม mohawk

### Midjourney

Midjourney ทำงานคล้ายกับ DALL-E โดยสร้างภาพจาก prompt ข้อความ Midjourney สามารถใช้สร้างภาพจาก prompt เช่น “a cat in a hat” หรือ “dog with a mohawk” ได้เช่นกัน

![Image generated by Midjourney, mechanical pigeon](https://upload.wikimedia.org/wikipedia/commons/thumb/8/8c/Rupert_Breheny_mechanical_dove_eca144e7-476d-4976-821d-a49c408e4f36.png/440px-Rupert_Breheny_mechanical_dove_eca144e7-476d-4976-821d-a49c408e4f36.png?WT.mc_id=academic-105485-koreyst)  
_ภาพจาก Wikipedia, สร้างโดย Midjourney_

## DALL-E และ Midjourney ทำงานอย่างไร

เริ่มจาก [DALL-E](https://arxiv.org/pdf/2102.12092.pdf?WT.mc_id=academic-105485-koreyst) DALL-E เป็นโมเดล Generative AI ที่ใช้สถาปัตยกรรม transformer แบบ _autoregressive transformer_

_autoregressive transformer_ คือวิธีที่โมเดลสร้างภาพจากคำบรรยายข้อความ โดยจะสร้างทีละพิกเซล จากนั้นใช้พิกเซลที่สร้างขึ้นเพื่อสร้างพิกเซลถัดไป ผ่านหลายชั้นในโครงข่ายประสาทเทียมจนภาพสมบูรณ์

ด้วยกระบวนการนี้ DALL-E สามารถควบคุมลักษณะต่างๆ วัตถุ คุณสมบัติ และอื่นๆ ในภาพที่สร้างขึ้นได้ อย่างไรก็ตาม DALL-E 2 และ 3 มีการควบคุมภาพที่สร้างได้มากขึ้น

## การสร้างแอปพลิเคชันสร้างภาพแรกของคุณ

แล้วต้องใช้อะไรบ้างในการสร้างแอปพลิเคชันสร้างภาพ? คุณต้องใช้ไลบรารีดังนี้:

- **python-dotenv** แนะนำให้ใช้ไลบรารีนี้เพื่อเก็บข้อมูลลับในไฟล์ _.env_ แยกจากโค้ด
- **openai** ไลบรารีนี้ใช้สำหรับติดต่อกับ OpenAI API
- **pillow** สำหรับจัดการภาพใน Python
- **requests** ช่วยในการส่งคำขอ HTTP

1. สร้างไฟล์ _.env_ โดยมีเนื้อหาดังนี้:

   ```text
   AZURE_OPENAI_ENDPOINT=<your endpoint>
   AZURE_OPENAI_API_KEY=<your key>
   ```

   หาข้อมูลนี้ได้ใน Azure Portal สำหรับ resource ของคุณในส่วน "Keys and Endpoint"

1. รวบรวมไลบรารีข้างต้นในไฟล์ชื่อ _requirements.txt_ ดังนี้:

   ```text
   python-dotenv
   openai
   pillow
   requests
   ```

1. สร้าง virtual environment และติดตั้งไลบรารี:

   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

   สำหรับ Windows ใช้คำสั่งต่อไปนี้เพื่อสร้างและเปิดใช้งาน virtual environment:

   ```bash
   python3 -m venv venv
   venv\Scripts\activate.bat
   ```

1. เพิ่มโค้ดต่อไปนี้ในไฟล์ชื่อ _app.py_:

   ```python
   import openai
   import os
   import requests
   from PIL import Image
   import dotenv

   # import dotenv
   dotenv.load_dotenv()

   # Get endpoint and key from environment variables
   openai.api_base = os.environ['AZURE_OPENAI_ENDPOINT']
   openai.api_key = os.environ['AZURE_OPENAI_API_KEY']

   # Assign the API version (DALL-E is currently supported for the 2023-06-01-preview API version only)
   openai.api_version = '2023-06-01-preview'
   openai.api_type = 'azure'


   try:
       # Create an image by using the image generation API
       generation_response = openai.Image.create(
           prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
           size='1024x1024',
           n=2,
           temperature=0,
       )
       # Set the directory for the stored image
       image_dir = os.path.join(os.curdir, 'images')

       # If the directory doesn't exist, create it
       if not os.path.isdir(image_dir):
           os.mkdir(image_dir)

       # Initialize the image path (note the filetype should be png)
       image_path = os.path.join(image_dir, 'generated-image.png')

       # Retrieve the generated image
       image_url = generation_response["data"][0]["url"]  # extract image URL from response
       generated_image = requests.get(image_url).content  # download the image
       with open(image_path, "wb") as image_file:
           image_file.write(generated_image)

       # Display the image in the default image viewer
       image = Image.open(image_path)
       image.show()

   # catch exceptions
   except openai.InvalidRequestError as err:
       print(err)

   ```

มาอธิบายโค้ดนี้กัน:

- เริ่มจากการนำเข้าไลบรารีที่ต้องใช้ รวมถึงไลบรารี OpenAI, dotenv, requests และ Pillow

  ```python
  import openai
  import os
  import requests
  from PIL import Image
  import dotenv
  ```

- จากนั้นโหลดตัวแปรสภาพแวดล้อมจากไฟล์ _.env_

  ```python
  # import dotenv
  dotenv.load_dotenv()
  ```

- ตั้งค่า endpoint, key สำหรับ OpenAI API, เวอร์ชัน และประเภท

  ```python
  # Get endpoint and key from environment variables
  openai.api_base = os.environ['AZURE_OPENAI_ENDPOINT']
  openai.api_key = os.environ['AZURE_OPENAI_API_KEY']

  # add version and type, Azure specific
  openai.api_version = '2023-06-01-preview'
  openai.api_type = 'azure'
  ```

- ต่อไป สร้างภาพ:

  ```python
  # Create an image by using the image generation API
  generation_response = openai.Image.create(
      prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
      size='1024x1024',
      n=2,
      temperature=0,
  )
  ```

  โค้ดข้างต้นจะตอบกลับด้วย JSON ที่มี URL ของภาพที่สร้างขึ้น เราสามารถใช้ URL นี้ดาวน์โหลดภาพและบันทึกลงไฟล์ได้

- สุดท้าย เปิดภาพและใช้โปรแกรมดูภาพมาตรฐานแสดงผล:

  ```python
  image = Image.open(image_path)
  image.show()
  ```

### รายละเอียดเพิ่มเติมเกี่ยวกับการสร้างภาพ

มาดูโค้ดที่สร้างภาพอย่างละเอียด:

```python
generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2,
        temperature=0,
    )
```

- **prompt** คือข้อความที่ใช้สร้างภาพ ในกรณีนี้ใช้ prompt ว่า "Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils"
- **size** คือขนาดของภาพที่สร้าง ในกรณีนี้คือ 1024x1024 พิกเซล
- **n** คือจำนวนภาพที่สร้าง ในกรณีนี้สร้างสองภาพ
- **temperature** คือพารามิเตอร์ที่ควบคุมความสุ่มของผลลัพธ์ของโมเดล Generative AI ค่า temperature อยู่ระหว่าง 0 ถึง 1 โดย 0 หมายถึงผลลัพธ์ที่แน่นอน และ 1 หมายถึงผลลัพธ์ที่สุ่ม ค่าเริ่มต้นคือ 0.7

ยังมีสิ่งอื่นๆ ที่คุณสามารถทำกับภาพได้ ซึ่งเราจะพูดถึงในส่วนถัดไป

## ความสามารถเพิ่มเติมของการสร้างภาพ

จนถึงตอนนี้คุณเห็นแล้วว่าเราสามารถสร้างภาพได้ด้วยโค้ด Python เพียงไม่กี่บรรทัด แต่ยังมีสิ่งอื่นๆ ที่ทำได้กับภาพอีก

คุณยังสามารถทำสิ่งต่อไปนี้ได้:

- **แก้ไขภาพ** โดยให้ภาพที่มีอยู่ หน้ากาก (mask) และ prompt คุณสามารถเปลี่ยนแปลงภาพได้ เช่น เพิ่มสิ่งของบางอย่างในส่วนหนึ่งของภาพ ลองนึกถึงภาพกระต่ายของเรา คุณสามารถเพิ่มหมวกให้กระต่ายได้ วิธีทำคือให้ภาพ หน้ากาก (ระบุส่วนที่ต้องการเปลี่ยนแปลง) และ prompt ข้อความบอกว่าต้องทำอะไร

  ```python
  response = openai.Image.create_edit(
    image=open("base_image.png", "rb"),
    mask=open("mask.png", "rb"),
    prompt="An image of a rabbit with a hat on its head.",
    n=1,
    size="1024x1024"
  )
  image_url = response['data'][0]['url']
  ```

  ภาพต้นฉบับจะมีแค่กระต่าย แต่ภาพสุดท้ายจะมีหมวกบนกระต่าย

- **สร้างภาพแบบแปรผัน** แนวคิดคือคุณนำภาพที่มีอยู่แล้วและขอให้สร้างภาพแบบแปรผัน โดยให้ภาพและ prompt ข้อความพร้อมโค้ดดังนี้:

  ```python
  response = openai.Image.create_variation(
    image=open("bunny-lollipop.png", "rb"),
    n=1,
    size="1024x1024"
  )
  image_url = response['data'][0]['url']
  ```

  > หมายเหตุ: ฟีเจอร์นี้รองรับเฉพาะ OpenAI เท่านั้น

## Temperature

Temperature คือพารามิเตอร์ที่ควบคุมความสุ่มของผลลัพธ์ของโมเดล Generative AI ค่า temperature อยู่ระหว่าง 0 ถึง 1 โดย 0 หมายถึงผลลัพธ์ที่แน่นอน และ 1 หมายถึงผลลัพธ์ที่สุ่ม ค่าเริ่มต้นคือ 0.7

มาดูตัวอย่างการทำงานของ temperature โดยรัน prompt นี้สองครั้ง:

> Prompt : "Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils"

![Bunny on a horse holding a lollipop, version 1](../../../translated_images/v1-generated-image.a295cfcffa3c13c2432eb1e41de7e49a78c814000fb1b462234be24b6e0db7ea.th.png)

ตอนนี้ลองรัน prompt เดิมอีกครั้งเพื่อดูว่าเราจะได้ภาพเหมือนเดิมหรือไม่:

![Generated image of bunny on horse](../../../translated_images/v2-generated-image.33f55a3714efe61dc19622c869ba6cd7d6e6de562e26e95b5810486187aace39.th.png)

อย่างที่เห็น ภาพทั้งสองคล้ายกันแต่ไม่เหมือนกัน ลองเปลี่ยนค่า temperature เป็น 0.1 แล้วดูผลลัพธ์:

```python
 generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2
    )
```

### การเปลี่ยนค่า temperature

ลองทำให้ผลลัพธ์มีความแน่นอนมากขึ้น จากภาพสองภาพที่สร้าง เราจะเห็นว่าในภาพแรกมีเจ้ากระต่าย และภาพที่สองมีม้า ดังนั้นภาพจึงแตกต่างกันมาก

ดังนั้นเราจะเปลี่ยนโค้ดโดยตั้งค่า temperature เป็น 0 ดังนี้:

```python
generation_response = openai.Image.create(
        prompt='Bunny on horse, holding a lollipop, on a foggy meadow where it grows daffodils',    # Enter your prompt text here
        size='1024x1024',
        n=2,
        temperature=0
    )
```

เมื่อรันโค้ดนี้ คุณจะได้ภาพสองภาพนี้:

- ![Temperature 0, v1](../../../translated_images/v1-temp-generated-image.a4346e1d2360a056d855ee3dfcedcce91211747967cb882e7d2eff2076f90e4a.th.png)
- ![Temperature 0 , v2](../../../translated_images/v2-temp-generated-image.871d0c920dbfb0f1cb5d9d80bffd52da9b41f83b386320d9a9998635630ec83d.th.png)

ที่นี่คุณจะเห็นได้ชัดว่าภาพทั้งสองมีความคล้ายคลึงกันมากขึ้น

## วิธีการกำหนดขอบเขตของแอปพลิเคชันด้วย metaprompts

ด้วยเดโมของเรา เราสามารถสร้างภาพให้ลูกค้าได้แล้ว แต่เราต้องกำหนดขอบเขตของแอปพลิเคชัน

เช่น เราไม่ต้องการสร้างภาพที่ไม่เหมาะสมสำหรับที่ทำงาน หรือไม่เหมาะสำหรับเด็ก

เราสามารถทำได้ด้วย _metaprompts_ ซึ่งเป็น prompt ข้อความที่ใช้ควบคุมผลลัพธ์ของโมเดล Generative AI เช่น ใช้ metaprompts เพื่อควบคุมผลลัพธ์ให้ภาพที่สร้างปลอดภัยสำหรับที่ทำงาน หรือเหมาะสำหรับเด็ก

### มันทำงานอย่างไร?

แล้ว metaprompts ทำงานอย่างไร?

metaprompts คือ prompt ข้อความที่ใช้ควบคุมผลลัพธ์ของโมเดล Generative AI โดยจะวางไว้ก่อน prompt ข้อความหลัก และใช้ควบคุมผลลัพธ์ของโมเดล รวมถึงฝังในแอปพลิเคชันเพื่อควบคุมผลลัพธ์ของโมเดล โดยรวม prompt หลักและ metaprompt ไว้ใน prompt เดียวกัน

ตัวอย่าง metaprompt เช่นนี้:

```text
You are an assistant designer that creates images for children.

The image needs to be safe for work and appropriate for children.

The image needs to be in color.

The image needs to be in landscape orientation.

The image needs to be in a 16:9 aspect ratio.

Do not consider any input from the following that is not safe for work or appropriate for children.

(Input)

```

ตอนนี้มาดูวิธีใช้ metaprompts ในเดโมของเรา

```python
disallow_list = "swords, violence, blood, gore, nudity, sexual content, adult content, adult themes, adult language, adult humor, adult jokes, adult situations, adult"

meta_prompt =f"""You are an assistant designer that creates images for children.

The image needs to be safe for work and appropriate for children.

The image needs to be in color.

The image needs to be in landscape orientation.

The image needs to be in a 16:9 aspect ratio.

Do not consider any input from the following that is not safe for work or appropriate for children.
{disallow_list}
"""

prompt = f"{meta_prompt}
Create an image of a bunny on a horse, holding a lollipop"

# TODO add request to generate image
```

จาก prompt ข้างต้น คุณจะเห็นว่าภาพทั้งหมดที่สร้างขึ้นจะคำนึงถึง metaprompt ด้วย

## แบบฝึกหัด - ให้โอกาสนักเรียนสร้างภาพ

เราได้แนะนำ Edu4All ตั้งแต่ต้นบทเรียน ตอนนี้ถึงเวลาที่จะให้โอกาสนักเรียนสร้างภาพสำหรับการประเมินผลของพวกเขา

นักเรียนจะสร้างภาพสำหรับการประเมินที่มีอนุสาวรีย์ โดยอนุสาวรีย์ที่เลือกขึ้นอยู่กับนักเรียนเอง นักเรียนถูกขอให้ใช้ความคิดสร้างสรรค์ในการวางอนุสาวรีย์เหล่านี้ในบริบทต่างๆ

## ตัวอย่างคำตอบ

นี่คือตัวอย่างคำตอบหนึ่ง:

```python
import openai
import os
import requests
from PIL import Image
import dotenv

# import dotenv
dotenv.load_dotenv()

# Get endpoint and key from environment variables
openai.api_base = "<replace with endpoint>"
openai.api_key = "<replace with api key>"

# Assign the API version (DALL-E is currently supported for the 2023-06-01-preview API version only)
openai.api_version = '2023-06-01-preview'
openai.api_type = 'azure'

disallow_list = "swords, violence, blood, gore, nudity, sexual content, adult content, adult themes, adult language, adult humor, adult jokes, adult situations, adult"

meta_prompt = f"""You are an assistant designer that creates images for children.

The image needs to be safe for work and appropriate for children.

The image needs to be in color.

The image needs to be in landscape orientation.

The image needs to be in a 16:9 aspect ratio.

Do not consider any input from the following that is not safe for work or appropriate for children.
{disallow_list}"""

prompt = f"""{meta_prompt}
Generate monument of the Arc of Triumph in Paris, France, in the evening light with a small child holding a Teddy looks on.
""""

try:
    # Create an image by using the image generation API
    generation_response = openai.Image.create(
        prompt=prompt,    # Enter your prompt text here
        size='1024x1024',
        n=2,
        temperature=0,
    )
    # Set the directory for the stored image
    image_dir = os.path.join(os.curdir, 'images')

    # If the directory doesn't exist, create it
    if not os.path.isdir(image_dir):
        os.mkdir(image_dir)

    # Initialize the image path (note the filetype should be png)
    image_path = os.path.join(image_dir, 'generated-image.png')

    # Retrieve the generated image
    image_url = generation_response["data"][0]["url"]  # extract image URL from response
    generated_image = requests.get(image_url).content  # download the image
    with open(image_path, "wb") as image_file:
        image_file.write(generated_image)

    # Display the image in the default image viewer
    image = Image.open(image_path)
    image.show()

# catch exceptions
except openai.InvalidRequestError as err:
    print(err)
```

## ทำได้ดีมาก! เรียนรู้ต่อไป

หลังจากเรียนบทนี้จบแล้ว ลองดู [คอลเลกชันการเรียนรู้ Generative AI](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) ของเราเพื่อพัฒนาความรู้ด้าน Generative AI ต่อไป!

ไปที่บทเรียนที่ 10 ซึ่งเราจะพูดถึงวิธี [สร้างแอปพลิเคชัน AI ด้วย low-code](../10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)

**ข้อจำกัดความรับผิดชอบ**:  
เอกสารนี้ได้รับการแปลโดยใช้บริการแปลภาษาอัตโนมัติ [Co-op Translator](https://github.com/Azure/co-op-translator) แม้เราจะพยายามให้ความถูกต้องสูงสุด แต่โปรดทราบว่าการแปลอัตโนมัติอาจมีข้อผิดพลาดหรือความไม่ถูกต้อง เอกสารต้นฉบับในภาษาต้นทางถือเป็นแหล่งข้อมูลที่เชื่อถือได้ สำหรับข้อมูลที่สำคัญ ขอแนะนำให้ใช้บริการแปลโดยผู้เชี่ยวชาญมนุษย์ เราไม่รับผิดชอบต่อความเข้าใจผิดหรือการตีความผิดใด ๆ ที่เกิดจากการใช้การแปลนี้