<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:46:53+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "th"
}
-->
# บทนำสู่โครงข่ายประสาทเทียม: Multi-Layered Perceptron

ในบทก่อนหน้านี้ คุณได้เรียนรู้เกี่ยวกับโมเดลโครงข่ายประสาทเทียมที่ง่ายที่สุด คือ one-layered perceptron ซึ่งเป็นโมเดลจำแนกประเภทแบบสองคลาสเชิงเส้น

ในบทนี้เราจะขยายโมเดลนี้ให้เป็นกรอบงานที่ยืดหยุ่นมากขึ้น ซึ่งจะช่วยให้เราสามารถ:

* ทำ **การจำแนกหลายคลาส** นอกเหนือจากสองคลาส
* แก้ปัญหา **การถดถอย** นอกเหนือจากการจำแนกประเภท
* แยกคลาสที่ไม่สามารถแยกด้วยเส้นตรงได้

เราจะพัฒนากรอบงานแบบโมดูลาร์ใน Python ที่ช่วยให้เราสร้างสถาปัตยกรรมโครงข่ายประสาทเทียมที่หลากหลายได้

## การนิยามปัญหาการเรียนรู้ของเครื่อง

เริ่มต้นด้วยการนิยามปัญหาการเรียนรู้ของเครื่อง สมมติว่าเรามีชุดข้อมูลฝึกอบรม **X** พร้อมป้ายกำกับ **Y** และเราต้องการสร้างโมเดล *f* ที่ทำนายได้แม่นยำที่สุด คุณภาพของการทำนายจะถูกวัดด้วย **ฟังก์ชันความสูญเสีย** ℒ ฟังก์ชันความสูญเสียที่ใช้บ่อยมีดังนี้:

* สำหรับปัญหาการถดถอย เมื่อเราต้องทำนายตัวเลข เราสามารถใช้ **ความผิดพลาดสัมบูรณ์** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>| หรือ **ความผิดพลาดกำลังสอง** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup>
* สำหรับการจำแนกประเภท เราใช้ **0-1 loss** (ซึ่งโดยพื้นฐานเหมือนกับ **ความแม่นยำ** ของโมเดล) หรือ **logistic loss**

สำหรับ one-level perceptron ฟังก์ชัน *f* ถูกกำหนดเป็นฟังก์ชันเชิงเส้น *f(x)=wx+b* (โดยที่ *w* คือเมทริกซ์น้ำหนัก, *x* คือเวกเตอร์คุณลักษณะนำเข้า และ *b* คือเวกเตอร์ไบแอส) สำหรับสถาปัตยกรรมโครงข่ายประสาทเทียมที่แตกต่างกัน ฟังก์ชันนี้อาจมีรูปแบบที่ซับซ้อนมากขึ้น

> ในกรณีของการจำแนกประเภท มักต้องการให้ผลลัพธ์ของโครงข่ายเป็นความน่าจะเป็นของแต่ละคลาส เพื่อแปลงตัวเลขใดๆ ให้เป็นความน่าจะเป็น (เช่น การทำ normalization) เรามักใช้ฟังก์ชัน **softmax** σ และฟังก์ชัน *f* จะกลายเป็น *f(x)=σ(wx+b)*

ในการนิยาม *f* ข้างต้น *w* และ *b* เรียกว่า **พารามิเตอร์** θ=⟨*w,b*⟩ เมื่อมีชุดข้อมูล ⟨**X**,**Y**⟩ เราสามารถคำนวณความผิดพลาดรวมของชุดข้อมูลทั้งหมดในรูปของฟังก์ชันของพารามิเตอร์ θ ได้

> ✅ **เป้าหมายของการฝึกโครงข่ายประสาทเทียมคือการลดความผิดพลาดโดยการปรับพารามิเตอร์ θ**

## การเพิ่มประสิทธิภาพด้วย Gradient Descent

มีวิธีการเพิ่มประสิทธิภาพฟังก์ชันที่รู้จักกันดีชื่อว่า **gradient descent** แนวคิดคือเราสามารถคำนวณอนุพันธ์ (ในกรณีหลายมิติเรียกว่า **gradient**) ของฟังก์ชันความสูญเสียตามพารามิเตอร์ และปรับพารามิเตอร์ในทิศทางที่ทำให้ความผิดพลาดลดลง ซึ่งสามารถนิยามได้ดังนี้:

* เริ่มต้นพารามิเตอร์ด้วยค่าที่สุ่ม w<sup>(0)</sup>, b<sup>(0)</sup>
* ทำซ้ำขั้นตอนต่อไปนี้หลายครั้ง:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

ในระหว่างการฝึก ขั้นตอนการเพิ่มประสิทธิภาพควรคำนวณโดยพิจารณาชุดข้อมูลทั้งหมด (จำไว้ว่าความสูญเสียคำนวณจากผลรวมของตัวอย่างทั้งหมด) อย่างไรก็ตาม ในชีวิตจริงเราจะใช้ข้อมูลเป็นส่วนย่อยๆ เรียกว่า **minibatches** และคำนวณ gradient จากข้อมูลย่อยเหล่านี้ เนื่องจากข้อมูลย่อยถูกสุ่มเลือกในแต่ละครั้ง วิธีนี้จึงเรียกว่า **stochastic gradient descent** (SGD)

## Multi-Layered Perceptrons และ Backpropagation

โครงข่ายชั้นเดียวตามที่เห็นข้างต้น สามารถจำแนกคลาสที่แยกด้วยเส้นตรงได้เท่านั้น เพื่อสร้างโมเดลที่ซับซ้อนขึ้น เราสามารถรวมหลายชั้นของโครงข่ายเข้าด้วยกัน ในทางคณิตศาสตร์หมายความว่าฟังก์ชัน *f* จะมีรูปแบบที่ซับซ้อนขึ้น และคำนวณเป็นหลายขั้นตอน:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

โดยที่ α คือ **ฟังก์ชันเปิดใช้งานแบบไม่เชิงเส้น**, σ คือฟังก์ชัน softmax และพารามิเตอร์ θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*>.

อัลกอริทึม gradient descent ยังคงเหมือนเดิม แต่การคำนวณ gradient จะซับซ้อนขึ้น โดยใช้กฎการอนุพันธ์แบบลูกโซ่ (chain differentiation rule) เราสามารถคำนวณอนุพันธ์ได้ดังนี้:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ กฎการอนุพันธ์แบบลูกโซ่ใช้ในการคำนวณอนุพันธ์ของฟังก์ชันความสูญเสียตามพารามิเตอร์

สังเกตว่าส่วนซ้ายสุดของนิพจน์ทั้งหมดเหมือนกัน ดังนั้นเราสามารถคำนวณอนุพันธ์ได้อย่างมีประสิทธิภาพโดยเริ่มจากฟังก์ชันความสูญเสียและย้อนกลับผ่านกราฟการคำนวณ วิธีการฝึกโครงข่ายประสาทเทียมหลายชั้นนี้จึงเรียกว่า **backpropagation** หรือ 'backprop'

> TODO: image citation

> ✅ เราจะอธิบาย backprop อย่างละเอียดมากขึ้นในตัวอย่างโน้ตบุ๊กของเรา

## สรุป

ในบทเรียนนี้ เราได้สร้างไลบรารีโครงข่ายประสาทเทียมของเราเอง และใช้มันสำหรับงานจำแนกประเภทสองมิติอย่างง่าย

## 🚀 ความท้าทาย

ในโน้ตบุ๊กประกอบ คุณจะได้สร้างกรอบงานของตัวเองสำหรับการสร้างและฝึก multi-layered perceptrons คุณจะได้เห็นรายละเอียดการทำงานของโครงข่ายประสาทเทียมสมัยใหม่

ไปที่โน้ตบุ๊ก OwnFramework และทำตามขั้นตอน

## ทบทวน & ศึกษาด้วยตนเอง

Backpropagation เป็นอัลกอริทึมที่ใช้กันทั่วไปใน AI และ ML ควรศึกษาให้ละเอียดมากขึ้น

## การบ้าน

ในห้องปฏิบัติการนี้ คุณจะใช้กรอบงานที่สร้างขึ้นในบทเรียนนี้เพื่อแก้ปัญหาการจำแนกตัวเลขเขียนด้วยมือ MNIST

* คำแนะนำ
* โน้ตบุ๊ก

**ข้อจำกัดความรับผิดชอบ**:  
เอกสารนี้ได้รับการแปลโดยใช้บริการแปลภาษาอัตโนมัติ [Co-op Translator](https://github.com/Azure/co-op-translator) แม้เราจะพยายามให้ความถูกต้องสูงสุด แต่โปรดทราบว่าการแปลอัตโนมัติอาจมีข้อผิดพลาดหรือความไม่ถูกต้อง เอกสารต้นฉบับในภาษาต้นทางถือเป็นแหล่งข้อมูลที่เชื่อถือได้ สำหรับข้อมูลที่สำคัญ ขอแนะนำให้ใช้บริการแปลโดยผู้เชี่ยวชาญมนุษย์ เราไม่รับผิดชอบต่อความเข้าใจผิดหรือการตีความผิดใด ๆ ที่เกิดจากการใช้การแปลนี้