<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T16:58:02+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "th"
}
-->
# บทนำสู่โครงข่ายประสาทเทียม: Perceptron

หนึ่งในความพยายามครั้งแรกในการสร้างสิ่งที่คล้ายกับโครงข่ายประสาทเทียมสมัยใหม่เกิดขึ้นโดย <PERSON> จาก Cornell Aeronautical Laboratory ในปี 1957 ซึ่งเป็นการสร้างฮาร์ดแวร์ที่เรียกว่า "Mark-1" ออกแบบมาเพื่อจดจำรูปทรงเรขาคณิตพื้นฐาน เช่น สามเหลี่ยม สี่เหลี่ยม และวงกลม

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='<PERSON>'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> รูปภาพจาก Wikipedia

ภาพอินพุตถูกแทนด้วยอาร์เรย์ของโฟโตเซลล์ขนาด 20x20 ดังนั้นโครงข่ายประสาทจึงมีอินพุต 400 ตัวและเอาต์พุตแบบไบนารีหนึ่งตัว เครือข่ายง่ายๆ ประกอบด้วยนิวรอนหนึ่งตัว เรียกอีกชื่อว่า **threshold logic unit** น้ำหนักของโครงข่ายประสาททำหน้าที่เหมือนตัวปรับความต้านทานที่ต้องปรับด้วยมือในช่วงการฝึกสอน

> ✅ ตัวปรับความต้านทาน (potentiometer) คืออุปกรณ์ที่ช่วยให้ผู้ใช้ปรับความต้านทานของวงจรได้

> The New York Times เคยเขียนเกี่ยวกับ perceptron ในเวลานั้นว่า: *ตัวอ่อนของคอมพิวเตอร์อิเล็กทรอนิกส์ที่ [กองทัพเรือ] คาดหวังว่าจะสามารถเดิน พูด เห็น เขียน สืบพันธุ์ และมีสติรู้ตัวได้*

## แบบจำลอง Perceptron

สมมติว่าเรามีคุณลักษณะ N ตัวในแบบจำลองของเรา ซึ่งอินพุตเวกเตอร์จะมีขนาด N perceptron เป็นแบบจำลอง **การจำแนกประเภทแบบไบนารี** หมายความว่าสามารถแยกแยะข้อมูลอินพุตออกเป็นสองคลาสได้ เราจะสมมติว่าเอาต์พุตของ perceptron สำหรับอินพุตเวกเตอร์ x จะเป็น +1 หรือ -1 ขึ้นอยู่กับคลาส เอาต์พุตจะถูกคำนวณโดยใช้สูตร:

y(x) = f(w<sup>T</sup>x)

โดยที่ f คือฟังก์ชันการเปิดใช้งานแบบขั้นบันได

## การฝึกสอน Perceptron

เพื่อฝึกสอน perceptron เราต้องหาน้ำหนักเวกเตอร์ w ที่สามารถจำแนกข้อมูลส่วนใหญ่ได้ถูกต้อง หรือก็คือทำให้เกิด **ข้อผิดพลาด** น้อยที่สุด ข้อผิดพลาดนี้ถูกกำหนดโดย **perceptron criterion** ดังนี้:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

โดยที่:

* ผลรวมจะถูกคำนวณเฉพาะข้อมูลฝึกสอน i ที่ถูกจำแนกผิด
* x<sub>i</sub> คือข้อมูลอินพุต และ t<sub>i</sub> คือ -1 หรือ +1 สำหรับตัวอย่างลบและบวกตามลำดับ

เกณฑ์นี้ถือเป็นฟังก์ชันของน้ำหนัก w และเราต้องการหาค่าที่ทำให้มันน้อยที่สุด โดยปกติจะใช้วิธีที่เรียกว่า **gradient descent** ซึ่งเริ่มจากน้ำหนักเริ่มต้น w<sup>(0)</sup> แล้วในแต่ละขั้นตอนจะอัปเดตน้ำหนักตามสูตร:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

โดยที่ η คือ **อัตราการเรียนรู้** และ ∇E(w) คือ **เกรเดียนต์** ของ E หลังจากคำนวณเกรเดียนต์แล้ว เราจะได้ว่า

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

อัลกอริทึมในภาษา Python มีลักษณะดังนี้:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## สรุป

ในบทเรียนนี้ คุณได้เรียนรู้เกี่ยวกับ perceptron ซึ่งเป็นแบบจำลองการจำแนกประเภทแบบไบนารี และวิธีการฝึกสอนโดยใช้เวกเตอร์น้ำหนัก

## 🚀 ความท้าทาย

ถ้าคุณอยากลองสร้าง perceptron ของตัวเอง ลองทำแลบนี้บน Microsoft Learn ซึ่งใช้ Azure ML designer

## ทบทวน & ศึกษาด้วยตนเอง

เพื่อดูว่าเราสามารถใช้ perceptron แก้ปัญหาแบบง่ายๆ และปัญหาในชีวิตจริงได้อย่างไร และเพื่อเรียนรู้ต่อ - ไปที่ Perceptron notebook

นี่คือบทความที่น่าสนใจเกี่ยวกับ perceptron ด้วย

## การบ้าน

ในบทเรียนนี้ เราได้สร้าง perceptron สำหรับงานจำแนกประเภทแบบไบนารี และใช้มันจำแนกระหว่างตัวเลขเขียนด้วยมือสองตัว ในแลบนี้ คุณจะต้องแก้ปัญหาการจำแนกตัวเลขทั้งหมด คือการระบุว่าตัวเลขใดมีความน่าจะเป็นมากที่สุดที่จะตรงกับภาพที่กำหนด

* คำแนะนำ
* Notebook

**ข้อจำกัดความรับผิดชอบ**:  
เอกสารนี้ได้รับการแปลโดยใช้บริการแปลภาษาอัตโนมัติ [Co-op Translator](https://github.com/Azure/co-op-translator) แม้เราจะพยายามให้ความถูกต้องสูงสุด แต่โปรดทราบว่าการแปลอัตโนมัติอาจมีข้อผิดพลาดหรือความไม่ถูกต้อง เอกสารต้นฉบับในภาษาต้นทางถือเป็นแหล่งข้อมูลที่เชื่อถือได้ สำหรับข้อมูลที่สำคัญ ขอแนะนำให้ใช้บริการแปลโดยผู้เชี่ยวชาญมนุษย์ เราไม่รับผิดชอบต่อความเข้าใจผิดหรือการตีความผิดใด ๆ ที่เกิดจากการใช้การแปลนี้