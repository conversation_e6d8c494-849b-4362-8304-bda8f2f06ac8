<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:02:34+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "th"
}
-->
# แหล่งข้อมูลสำหรับการเรียนรู้ด้วยตนเอง

บทเรียนนี้สร้างขึ้นโดยใช้แหล่งข้อมูลหลักจาก OpenAI และ Azure OpenAI เป็นข้อมูลอ้างอิงสำหรับคำศัพท์และบทแนะนำ นี่คือรายชื่อที่ไม่ครบถ้วนสำหรับการเรียนรู้ด้วยตนเองของคุณ

## 1. แหล่งข้อมูลหลัก

| ชื่อเรื่อง/ลิงก์                                                                                                                                                                                                                   | คำอธิบาย                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | การปรับแต่งแบบละเอียดช่วยพัฒนาการเรียนรู้แบบ few-shot โดยการฝึกกับตัวอย่างจำนวนมากกว่าที่ใส่ใน prompt ได้ ช่วยประหยัดค่าใช้จ่าย ปรับปรุงคุณภาพการตอบกลับ และลดความหน่วงของคำขอ **ดูภาพรวมของการปรับแต่งแบบละเอียดจาก OpenAI**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | ทำความเข้าใจ **ว่าการปรับแต่งแบบละเอียดคืออะไร (แนวคิด)** ทำไมคุณควรสนใจ (ปัญหาที่กระตุ้น) ข้อมูลที่ใช้ (การฝึก) และการวัดคุณภาพ                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | บริการ Azure OpenAI ช่วยให้คุณปรับแต่งโมเดลให้เหมาะกับชุดข้อมูลส่วนตัวของคุณโดยใช้การปรับแต่งแบบละเอียด เรียนรู้ **วิธีการปรับแต่ง (กระบวนการ)** เลือกโมเดลโดยใช้ Azure AI Studio, Python SDK หรือ REST API                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLM อาจทำงานได้ไม่ดีในโดเมน งาน หรือชุดข้อมูลเฉพาะ หรืออาจสร้างผลลัพธ์ที่ไม่ถูกต้องหรือทำให้เข้าใจผิด **เมื่อใดควรพิจารณาการปรับแต่งแบบละเอียด** เป็นทางเลือกแก้ไขปัญหานี้                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | การปรับแต่งแบบละเอียดอย่างต่อเนื่องคือกระบวนการทำซ้ำโดยเลือกโมเดลที่ผ่านการปรับแต่งแล้วเป็นโมเดลฐาน และ **ปรับแต่งเพิ่มเติม** ด้วยชุดตัวอย่างฝึกใหม่                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | การปรับแต่งโมเดลของคุณ **ด้วยตัวอย่างการเรียกฟังก์ชัน** สามารถช่วยปรับปรุงผลลัพธ์ของโมเดลให้แม่นยำและสม่ำเสมอมากขึ้น - ด้วยการตอบกลับที่มีรูปแบบคล้ายกันและช่วยประหยัดค่าใช้จ่าย                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | ดูตารางนี้เพื่อเข้าใจ **ว่าโมเดลใดบ้างที่สามารถปรับแต่งได้** ใน Azure OpenAI และในภูมิภาคใดบ้างที่มีให้บริการ ตรวจสอบขีดจำกัดโทเค็นและวันหมดอายุของข้อมูลฝึกหากจำเป็น                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | ตอนนี้ความยาว 30 นาที **ตุลาคม 2023** ของ AI Show พูดถึงข้อดี ข้อเสีย และข้อมูลเชิงลึกที่ช่วยให้คุณตัดสินใจเรื่องนี้ได้                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | แหล่งข้อมูล **AI Playbook** นี้จะพาคุณผ่านความต้องการข้อมูล การจัดรูปแบบ การปรับแต่งไฮเปอร์พารามิเตอร์ และความท้าทาย/ข้อจำกัดที่ควรรู้                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | เรียนรู้วิธีสร้างชุดข้อมูลตัวอย่างสำหรับการปรับแต่งแบบละเอียด เตรียมตัวสำหรับการปรับแต่ง สร้างงานปรับแต่ง และนำโมเดลที่ปรับแต่งแล้วไปใช้งานบน Azure                                                                                                                                                                                    |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio ช่วยให้คุณปรับแต่งโมเดลภาษาขนาดใหญ่ให้เหมาะกับชุดข้อมูลส่วนตัว _โดยใช้กระบวนการผ่าน UI ที่เหมาะกับนักพัฒนาที่ไม่เน้นโค้ดมาก_ ดูตัวอย่างนี้ได้                                                                                                                                                               |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | บทความนี้อธิบายวิธีปรับแต่งโมเดล Hugging Face ด้วยไลบรารี transformers บน GPU เดียว โดยใช้ Azure DataBricks และไลบรารี Hugging Face Trainer                                                                                                                                                |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | แคตตาล็อกโมเดลใน Azure Machine Learning มีโมเดลโอเพนซอร์สมากมายที่คุณสามารถปรับแต่งให้เหมาะกับงานเฉพาะของคุณ ลองทำโมดูลนี้ซึ่งเป็นส่วนหนึ่งของ [เส้นทางการเรียนรู้ AzureML Generative AI](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | การปรับแต่ง GPT-3.5 หรือ GPT-4 บน Microsoft Azure ด้วย W&B ช่วยให้ติดตามและวิเคราะห์ประสิทธิภาพของโมเดลได้อย่างละเอียด คู่มือนี้ขยายแนวคิดจากคู่มือ OpenAI Fine-Tuning ด้วยขั้นตอนและฟีเจอร์เฉพาะสำหรับ Azure OpenAI                                                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. แหล่งข้อมูลรอง

ส่วนนี้รวบรวมแหล่งข้อมูลเพิ่มเติมที่น่าสนใจแต่เราไม่มีเวลาครอบคลุมในบทเรียนนี้ อาจจะถูกนำมาใช้ในบทเรียนอนาคต หรือเป็นตัวเลือกงานมอบหมายรองในภายหลัง ตอนนี้ใช้แหล่งข้อมูลเหล่านี้เพื่อสร้างความเชี่ยวชาญและความรู้ของคุณเองในหัวข้อนี้

| ชื่อเรื่อง/ลิงก์                                                                                                                                                                                                            | คำอธิบาย                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [การเตรียมข้อมูลและวิเคราะห์สำหรับการปรับแต่งโมเดลแชท](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | โน้ตบุ๊กนี้เป็นเครื่องมือสำหรับเตรียมข้อมูลและวิเคราะห์ชุดข้อมูลแชทที่ใช้สำหรับการปรับแต่งโมเดลแชท ตรวจสอบข้อผิดพลาดของรูปแบบ ให้สถิติพื้นฐาน และประมาณจำนวนโทเค็นเพื่อคำนวณค่าใช้จ่ายในการปรับแต่ง ดู: [วิธีการปรับแต่งสำหรับ gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                                                                                   |
| **OpenAI Cookbook**: [การปรับแต่งสำหรับ Retrieval Augmented Generation (RAG) ด้วย Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | โน้ตบุ๊กนี้มีตัวอย่างครบถ้วนของการปรับแต่งโมเดล OpenAI สำหรับ Retrieval Augmented Generation (RAG) โดยจะรวมการใช้งาน Qdrant และ Few-Shot Learning เพื่อเพิ่มประสิทธิภาพโมเดลและลดการสร้างข้อมูลเท็จ                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [การปรับแต่ง GPT ด้วย Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) เป็นแพลตฟอร์มนักพัฒนา AI ที่มีเครื่องมือสำหรับฝึกโมเดล ปรับแต่งโมเดล และใช้โมเดลพื้นฐาน อ่านคู่มือ [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) ก่อน แล้วลองทำแบบฝึกหัดใน Cookbook                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - การปรับแต่งสำหรับโมเดลภาษาเล็ก                                                   | พบกับ [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst) โมเดลขนาดเล็กใหม่ของ Microsoft ที่ทรงพลังและกะทัดรัด บทแนะนำนี้จะพาคุณผ่านการปรับแต่ง Phi-2 แสดงวิธีสร้างชุดข้อมูลเฉพาะและปรับแต่งโมเดลโดยใช้ QLoRA                                                                                                                                                                       |
| **Hugging Face Tutorial** [วิธีปรับแต่ง LLMs ในปี 2024 ด้วย Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | บล็อกโพสต์นี้แนะนำวิธีปรับแต่ง LLMs แบบเปิดโดยใช้ Hugging Face TRL, Transformers และ datasets ในปี 2024 คุณจะกำหนดกรณีใช้งาน ตั้งค่าสภาพแวดล้อมพัฒนา เตรียมชุดข้อมูล ปรับแต่งโมเดล ทดสอบ-ประเมินผล และนำไปใช้งานจริง                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | ช่วยให้การฝึกและนำโมเดลแมชชีนเลิร์นนิงระดับสูงไปใช้งานเร็วและง่ายขึ้น [state-of-the-art machine learning models](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst) รีโปนี้มีบทแนะนำที่ใช้งานง่ายบน Colab พร้อมวิดีโอแนะนำบน YouTube สำหรับการปรับแต่ง **สะท้อนการอัปเดต [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) ล่าสุด** อ่านเอกสาร [AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**ข้อจำกัดความรับผิดชอบ**:  
เอกสารนี้ได้รับการแปลโดยใช้บริการแปลภาษาอัตโนมัติ [Co-op Translator](https://github.com/Azure/co-op-translator) แม้เราจะพยายามให้ความถูกต้องสูงสุด แต่โปรดทราบว่าการแปลอัตโนมัติอาจมีข้อผิดพลาดหรือความไม่ถูกต้อง เอกสารต้นฉบับในภาษาต้นทางถือเป็นแหล่งข้อมูลที่เชื่อถือได้ สำหรับข้อมูลที่สำคัญ ขอแนะนำให้ใช้บริการแปลโดยผู้เชี่ยวชาญมนุษย์ เราไม่รับผิดชอบต่อความเข้าใจผิดหรือการตีความผิดใด ๆ ที่เกิดจากการใช้การแปลนี้