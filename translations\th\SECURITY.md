<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:53:51+00:00",
  "source_file": "SECURITY.md",
  "language_code": "th"
}
-->
## ความปลอดภัย

Microsoft ให้ความสำคัญกับความปลอดภัยของผลิตภัณฑ์ซอฟต์แวร์และบริการของเราอย่างจริงจัง ซึ่งรวมถึงที่เก็บซอร์สโค้ดทั้งหมดที่จัดการผ่านองค์กร GitHub ของเรา เช่น [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) และ [องค์กร GitHub ของเรา](https://opensource.microsoft.com/)

หากคุณเชื่อว่าพบช่องโหว่ด้านความปลอดภัยในที่เก็บข้อมูลใด ๆ ที่เป็นของ Microsoft ซึ่งตรงตาม [คำนิยามช่องโหว่ด้านความปลอดภัยของ Microsoft](https://aka.ms/opensource/security/definition) กรุณาแจ้งให้เราทราบตามคำแนะนำด้านล่าง

## การรายงานปัญหาด้านความปลอดภัย

**กรุณาอย่ารายงานช่องโหว่ด้านความปลอดภัยผ่านทาง GitHub issues สาธารณะ**

ให้รายงานไปยังศูนย์ตอบสนองความปลอดภัยของ Microsoft (MSRC) ที่ [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report)

หากคุณต้องการส่งรายงานโดยไม่ต้องเข้าสู่ระบบ ให้ส่งอีเมลไปที่ [<EMAIL>](mailto:<EMAIL>) หากเป็นไปได้ กรุณาเข้ารหัสข้อความของคุณด้วยกุญแจ PGP ของเรา ดาวน์โหลดได้จาก [หน้า Microsoft Security Response Center PGP Key](https://aka.ms/opensource/security/pgpkey)

คุณควรได้รับการตอบกลับภายใน 24 ชั่วโมง หากด้วยเหตุผลใดก็ตามที่ไม่ได้รับการตอบกลับ กรุณาติดตามผลทางอีเมลเพื่อยืนยันว่าเราได้รับข้อความของคุณแล้ว ข้อมูลเพิ่มเติมสามารถดูได้ที่ [microsoft.com/msrc](https://aka.ms/opensource/security/msrc)

กรุณาระบุข้อมูลที่ร้องขอด้านล่าง (ให้มากที่สุดเท่าที่จะทำได้) เพื่อช่วยให้เราเข้าใจลักษณะและขอบเขตของปัญหาที่อาจเกิดขึ้นได้ดียิ่งขึ้น:

  * ประเภทของปัญหา (เช่น buffer overflow, SQL injection, cross-site scripting ฯลฯ)
  * เส้นทางเต็มของไฟล์ซอร์สที่เกี่ยวข้องกับการแสดงออกของปัญหา
  * ตำแหน่งของซอร์สโค้ดที่ได้รับผลกระทบ (tag/branch/commit หรือ URL โดยตรง)
  * การตั้งค่าพิเศษใด ๆ ที่จำเป็นเพื่อทำซ้ำปัญหา
  * คำแนะนำทีละขั้นตอนเพื่อทำซ้ำปัญหา
  * โค้ดตัวอย่างหรือโค้ดโจมตี (ถ้าเป็นไปได้)
  * ผลกระทบของปัญหา รวมถึงวิธีที่ผู้โจมตีอาจใช้ประโยชน์จากปัญหา

ข้อมูลเหล่านี้จะช่วยให้เราจัดลำดับความสำคัญของรายงานของคุณได้รวดเร็วยิ่งขึ้น

หากคุณรายงานเพื่อรับรางวัลบั๊กบาวน์ตี้ รายงานที่สมบูรณ์ยิ่งขึ้นจะช่วยเพิ่มโอกาสได้รับรางวัลที่สูงขึ้น กรุณาเยี่ยมชมหน้า [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) เพื่อดูรายละเอียดเพิ่มเติมเกี่ยวกับโปรแกรมที่เปิดใช้งานอยู่ของเรา

## ภาษาที่ต้องการ

เราต้องการให้การสื่อสารทั้งหมดเป็นภาษาอังกฤษ

## นโยบาย

Microsoft ปฏิบัติตามหลักการ [Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd)

**ข้อจำกัดความรับผิดชอบ**:  
เอกสารนี้ได้รับการแปลโดยใช้บริการแปลภาษาอัตโนมัติ [Co-op Translator](https://github.com/Azure/co-op-translator) แม้เราจะพยายามให้ความถูกต้องสูงสุด แต่โปรดทราบว่าการแปลอัตโนมัติอาจมีข้อผิดพลาดหรือความไม่ถูกต้อง เอกสารต้นฉบับในภาษาต้นทางถือเป็นแหล่งข้อมูลที่เชื่อถือได้ สำหรับข้อมูลที่สำคัญ ขอแนะนำให้ใช้บริการแปลโดยผู้เชี่ยวชาญมนุษย์ เราไม่รับผิดชอบต่อความเข้าใจผิดหรือการตีความผิดใด ๆ ที่เกิดจากการใช้การแปลนี้