<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:06:35+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "tl"
}
-->
# Mga Mapagkukunan Para sa Sariling Pag-aaral

Ang aralin ay ginawa gamit ang ilang pangunahing mapagkukunan mula sa OpenAI at Azure OpenAI bilang mga sanggunian para sa terminolohiya at mga tutorial. Narito ang isang hindi kumpletong listahan, para sa iyong sariling paglalakbay sa sariling pag-aaral.

## 1. Pangunahing Mga Mapagkukunan

| Pamagat/Link                                                                                                                                                                                                                   | Paglalarawan                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Pinapabuti ng fine-tuning ang few-shot learning sa pamamagitan ng pagsasanay gamit ang mas maraming halimbawa kaysa sa kayang ilagay sa prompt, na nakakatipid sa gastos, nagpapahusay ng kalidad ng tugon, at nagpapahintulot ng mas mababang latency sa mga kahilingan. **Alamin ang pangkalahatang ideya ng fine-tuning mula sa OpenAI.**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Unawain **kung ano ang fine-tuning (konsepto)**, bakit ito dapat tingnan (motivating problem), anong data ang gagamitin (pagsasanay) at kung paano sukatin ang kalidad                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Pinapayagan ka ng Azure OpenAI Service na iangkop ang aming mga modelo sa iyong personal na mga dataset gamit ang fine-tuning. Alamin **kung paano mag-fine-tune (proseso)** ng mga piling modelo gamit ang Azure AI Studio, Python SDK o REST API.                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | Maaaring hindi mag-perform nang maayos ang mga LLM sa mga partikular na domain, gawain, o dataset, o maaaring magbigay ng hindi tumpak o nakalilitong mga output. **Kailan dapat mong isaalang-alang ang fine-tuning** bilang posibleng solusyon dito?                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Ang continuous fine-tuning ay ang paulit-ulit na proseso ng pagpili ng isang modelo na na-fine-tune na bilang base model at **patuloy na pag-fine-tune nito** gamit ang mga bagong set ng mga halimbawa sa pagsasanay.                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Ang pag-fine-tune ng iyong modelo **gamit ang mga halimbawa ng function calling** ay maaaring mapabuti ang output ng modelo sa pamamagitan ng pagkakaroon ng mas tumpak at pare-parehong mga tugon - na may magkakatulad na format ng mga sagot at nakakatipid sa gastos                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Tingnan ang talahanayan na ito upang maunawaan **kung aling mga modelo ang maaaring i-fine-tune** sa Azure OpenAI, at saang mga rehiyon ito available. Tingnan din ang kanilang mga token limit at petsa ng pag-expire ng training data kung kinakailangan.                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Ang 30-minutong episode ng AI Show noong **Oktubre 2023** na ito ay tinalakay ang mga benepisyo, kahinaan, at mga praktikal na pananaw na makakatulong sa iyo sa paggawa ng desisyong ito.                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Ang resource na ito mula sa **AI Playbook** ay ginagabayan ka sa mga pangangailangan sa data, pag-format, hyperparameter fine-tuning, at mga hamon/limitasyon na dapat mong malaman.                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Matutunan kung paano gumawa ng sample na fine-tuning dataset, maghanda para sa fine-tuning, gumawa ng fine-tuning job, at i-deploy ang fine-tuned na modelo sa Azure.                                                                                                                                                                                    |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Pinapayagan ka ng Azure AI Studio na iangkop ang malalaking language model sa iyong personal na mga dataset _gamit ang UI-based workflow na angkop para sa mga low-code developer_. Tingnan ang halimbawang ito.                                                                                                                                                               |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Inilalarawan ng artikulong ito kung paano mag-fine-tune ng Hugging Face model gamit ang Hugging Face transformers library sa isang GPU gamit ang Azure DataBricks + Hugging Face Trainer libraries                                                                                                                                                |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Nag-aalok ang model catalog sa Azure Machine Learning ng maraming open source na mga modelo na maaari mong i-fine-tune para sa iyong partikular na gawain. Subukan ang module na ito mula sa [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Ang pag-fine-tune ng GPT-3.5 o GPT-4 models sa Microsoft Azure gamit ang W&B ay nagbibigay-daan sa detalyadong pagsubaybay at pagsusuri ng performance ng modelo. Pinalalawak ng gabay na ito ang mga konsepto mula sa OpenAI Fine-Tuning guide na may mga partikular na hakbang at tampok para sa Azure OpenAI.                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. Pangalawang Mga Mapagkukunan

Ang seksyong ito ay naglalaman ng mga karagdagang mapagkukunan na sulit tuklasin, ngunit hindi namin nagkaroon ng oras na talakayin sa araling ito. Maaaring talakayin ito sa mga susunod na aralin, o bilang opsyon sa pangalawang takdang-aralin sa hinaharap. Sa ngayon, gamitin ang mga ito upang palawakin ang iyong kaalaman at kasanayan tungkol sa paksang ito.

| Pamagat/Link                                                                                                                                                                                                            | Paglalarawan                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Paghahanda at pagsusuri ng data para sa fine-tuning ng chat model](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | Ang notebook na ito ay nagsisilbing kasangkapan para i-preprocess at suriin ang chat dataset na ginamit para sa fine-tuning ng chat model. Sinusuri nito ang mga error sa format, nagbibigay ng mga pangunahing estadistika, at tinatantiya ang bilang ng token para sa gastos sa fine-tuning. Tingnan: [Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                                                                   |
| **OpenAI Cookbook**: [Fine-Tuning para sa Retrieval Augmented Generation (RAG) gamit ang Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | Layunin ng notebook na ito na ipakita ang isang komprehensibong halimbawa kung paano mag-fine-tune ng OpenAI models para sa Retrieval Augmented Generation (RAG). Isasama rin natin ang Qdrant at Few-Shot Learning upang mapabuti ang performance ng modelo at mabawasan ang mga maling sagot.                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Fine-tuning GPT gamit ang Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Ang Weights & Biases (W&B) ay isang platform para sa mga AI developer, na may mga kasangkapan para sa pagsasanay ng mga modelo, fine-tuning, at paggamit ng mga foundation model. Basahin muna ang kanilang [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) na gabay, pagkatapos subukan ang exercise sa Cookbook.                                                                                                                                                                                                                  |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning para sa Small Language Models                                                   | Kilalanin ang [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), ang bagong maliit na modelo ng Microsoft na makapangyarihan ngunit compact. Gagabayan ka ng tutorial na ito sa pag-fine-tune ng Phi-2, na nagpapakita kung paano bumuo ng natatanging dataset at mag-fine-tune ng modelo gamit ang QLoRA.                                                                                                                                                                       |
| **Hugging Face Tutorial** [Paano Mag-Fine-Tune ng LLMs sa 2024 gamit ang Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | Tinatalakay ng blog post na ito kung paano mag-fine-tune ng open LLMs gamit ang Hugging Face TRL, Transformers, at datasets sa 2024. Dito mo itinatakda ang use case, inihahanda ang dev environment, pinaghahandaan ang dataset, fine-tune ang modelo, sinusubukan at sinusuri ito, at saka ito ide-deploy sa production.                                                                                                                                                                                                                                                                |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Nagdadala ng mas mabilis at mas madaling pagsasanay at deployment ng [pinakabagong mga modelo ng machine learning](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). May mga tutorial na friendly sa Colab at gabay sa YouTube para sa fine-tuning. **Nagpapakita ng bagong [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) na update**. Basahin ang [AutoTrain documentation](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Paalala**:  
Ang dokumentong ito ay isinalin gamit ang AI translation service na [Co-op Translator](https://github.com/Azure/co-op-translator). Bagamat nagsusumikap kami para sa katumpakan, pakatandaan na ang mga awtomatikong pagsasalin ay maaaring maglaman ng mga pagkakamali o di-tumpak na impormasyon. Ang orihinal na dokumento sa kanyang sariling wika ang dapat ituring na pangunahing sanggunian. Para sa mahahalagang impormasyon, inirerekomenda ang propesyonal na pagsasalin ng tao. Hindi kami mananagot sa anumang hindi pagkakaunawaan o maling interpretasyon na maaaring magmula sa paggamit ng pagsasaling ito.