<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:55:18+00:00",
  "source_file": "SECURITY.md",
  "language_code": "tl"
}
-->
## Seguridad

Seryoso ang Microsoft sa seguridad ng aming mga software na produkto at serbisyo, kabilang na ang lahat ng source code repositories na pinamamahalaan sa pamamagitan ng aming mga GitHub organizations, tulad ng [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin), at [ang aming mga GitHub organizations](https://opensource.microsoft.com/).

<PERSON> nanin<PERSON>wala kang nakakita ka ng security vulnerability sa anumang repository na pag-aari ng Microsoft na tumutugma sa [depinisyon ng Microsoft ng security vulnerability](https://aka.ms/opensource/security/definition), mangyaring i-report ito sa amin ayon sa mga tagubiling nakasaad sa ibaba.

## Pag-uulat ng mga Isyu sa Seguridad

**Huwag mag-report ng security vulnerabilities sa pamamagitan ng pampublikong GitHub issues.**

Sa halip, i-report ito sa Microsoft Security Response Center (MSRC) sa [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Kung mas gusto mong mag-submit nang hindi nagla-login, magpadala ng email sa [<EMAIL>](mailto:<EMAIL>). Kung maaari, i-encrypt ang iyong mensahe gamit ang aming PGP key; maaari mo itong i-download mula sa [Microsoft Security Response Center PGP Key page](https://aka.ms/opensource/security/pgpkey).

Dapat kang makatanggap ng tugon sa loob ng 24 na oras. Kung sa anumang dahilan ay hindi mo ito natanggap, mangyaring mag-follow up sa pamamagitan ng email upang matiyak na natanggap namin ang iyong orihinal na mensahe. Karagdagang impormasyon ay makikita sa [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Mangyaring isama ang mga hinihiling na impormasyon sa ibaba (hangga't maaari) upang matulungan kaming mas maintindihan ang kalikasan at lawak ng posibleng isyu:

  * Uri ng isyu (hal. buffer overflow, SQL injection, cross-site scripting, atbp.)
  * Buong path ng source file(s) na may kaugnayan sa paglitaw ng isyu
  * Lokasyon ng apektadong source code (tag/branch/commit o direktang URL)
  * Anumang espesyal na configuration na kinakailangan upang maulit ang isyu
  * Hakbang-hakbang na mga tagubilin upang maulit ang isyu
  * Proof-of-concept o exploit code (kung maaari)
  * Epekto ng isyu, kabilang kung paano maaaring samantalahin ng isang attacker ang isyu

Makakatulong ang impormasyong ito upang mas mabilis naming ma-triage ang iyong report.

Kung nag-uulat ka para sa bug bounty, ang mas kumpletong mga report ay maaaring magdulot ng mas mataas na gantimpala. Mangyaring bisitahin ang aming [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) na pahina para sa karagdagang detalye tungkol sa aming mga aktibong programa.

## Mga Preferred na Wika

Mas gusto namin na lahat ng komunikasyon ay nasa Ingles.

## Patakaran

Sinusunod ng Microsoft ang prinsipyo ng [Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd).

**Paalala**:  
Ang dokumentong ito ay isinalin gamit ang AI translation service na [Co-op Translator](https://github.com/Azure/co-op-translator). Bagamat nagsusumikap kami para sa katumpakan, pakatandaan na ang mga awtomatikong pagsasalin ay maaaring maglaman ng mga pagkakamali o di-tumpak na impormasyon. Ang orihinal na dokumento sa orihinal nitong wika ang dapat ituring na pangunahing sanggunian. Para sa mahahalagang impormasyon, inirerekomenda ang propesyonal na pagsasalin ng tao. Hindi kami mananagot sa anumang hindi pagkakaunawaan o maling interpretasyon na maaaring magmula sa paggamit ng pagsasaling ito.