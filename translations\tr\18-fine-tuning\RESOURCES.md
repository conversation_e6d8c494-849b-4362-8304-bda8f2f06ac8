<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:01:45+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "tr"
}
-->
# <PERSON><PERSON>ğrenme İçin <PERSON>, terminoloji ve eğitimler için referans olarak OpenAI ve Azure OpenAI’den birçok temel kaynağın kullanılmasıyla oluşturuldu. İşte kendi kendinize öğrenme yolculuklarınız için kapsamlı olmayan bir liste.

## 1. <PERSON><PERSON><PERSON><PERSON>

| Başlık/Bağlantı                                                                                                                                                                                                              | Açıklama                                                                                                                                                                                                                                                                                                                                                                                     |
| :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [OpenAI Modelleri ile İnce Ayar](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                        | İnce ayar, prompt’a sığabilecekten çok daha fazla örnek üzerinde eğitim yaparak few-shot öğrenmeyi geliştirir, maliyetlerinizi düşürür, yanıt kalitesini artırır ve daha düşük gecikmeli istekler sağlar. **OpenAI’den ince ayar hakkında genel bir bakış edinin.**                                                                                                                           |
| [Azure OpenAI ile İnce Ayar Nedir?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                            | **İnce ayarın ne olduğu (kavram)**, neden ince ayara bakmanız gerektiği (motivasyonel problem), hangi verilerin kullanılacağı (eğitim) ve kalite ölçümü hakkında bilgi edinin.                                                                                                                                                                                                             |
| [İnce Ayar ile Model Özelleştirme](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)      | Azure OpenAI Hizmeti, modellerimizi kişisel veri setlerinize göre ince ayar yapmanızı sağlar. Azure AI Studio, Python SDK veya REST API kullanarak **ince ayar yapma (süreç)** ve model seçmeyi öğrenin.                                                                                                                                                                                    |
| [LLM İnce Ayar Önerileri](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                                 | LLM’ler belirli alanlarda, görevlerde veya veri setlerinde iyi performans göstermeyebilir veya yanlış ya da yanıltıcı çıktılar üretebilir. **İnce ayarı ne zaman bir çözüm olarak düşünmelisiniz?**                                                                                                                                                                                        |
| [Sürekli İnce Ayar](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)                    | Sürekli ince ayar, önceden ince ayar yapılmış bir modeli temel model olarak seçip, yeni eğitim örnekleri üzerinde **daha fazla ince ayar yapma** sürecidir.                                                                                                                                                                                                                               |
| [İnce Ayar ve Fonksiyon Çağrısı](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                          | Modelinizi **fonksiyon çağrısı örnekleriyle ince ayar yapmak**, benzer formatta yanıtlar ve maliyet tasarrufu ile daha doğru ve tutarlı çıktılar elde ederek model performansını artırabilir.                                                                                                                                                                                             |
| [İnce Ayar Modelleri: Azure OpenAI Rehberi](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                         | Azure OpenAI’da **hangi modellerin ince ayar yapılabileceğini** ve bu modellerin hangi bölgelerde mevcut olduğunu anlamak için bu tabloya bakın. Gerekirse token limitlerini ve eğitim verisi son kullanma tarihlerini kontrol edin.                                                                                                                                                        |
| [İnce Ayar Yapmalı mı Yapmamalı mı? İşte Bütün Mesele Bu](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Bu 30 dakikalık **Ekim 2023** tarihli AI Show bölümü, ince ayarın avantajları, dezavantajları ve karar vermenize yardımcı olacak pratik bilgiler sunuyor.                                                                                                                                                                                                                                  |
| [LLM İnce Ayarına Başlarken](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                              | Bu **AI Playbook** kaynağı, veri gereksinimleri, formatlama, hiperparametre ince ayarı ve bilmeniz gereken zorluklar/sınırlamalar hakkında sizi adım adım yönlendirir.                                                                                                                                                                                                                       |
| **Eğitim:** [Azure OpenAI GPT3.5 Turbo İnce Ayar](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                      | Örnek bir ince ayar veri seti oluşturmayı, ince ayara hazırlanmayı, ince ayar işi başlatmayı ve ince ayarlı modeli Azure üzerinde dağıtmayı öğrenin.                                                                                                                                                                                                                                         |
| **Eğitim:** [Azure AI Studio’da Llama 2 Modelini İnce Ayar Yapma](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                               | Azure AI Studio, büyük dil modellerini kişisel veri setlerinize göre _düşük kodlu geliştiriciler için uygun UI tabanlı bir iş akışı kullanarak_ özelleştirmenizi sağlar. Bu örneğe göz atın.                                                                                                                                                                                                |
| **Eğitim:** [Azure’da Tek GPU ile Hugging Face Modellerini İnce Ayar Yapma](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)           | Bu makale, Hugging Face transformers kütüphanesi kullanarak Azure DataBricks + Hugging Face Trainer kütüphaneleri ile tek GPU üzerinde bir Hugging Face modelinin nasıl ince ayar yapılacağını anlatır.                                                                                                                                                                                    |
| **Eğitim:** [Azure Machine Learning ile Temel Modeli İnce Ayar Yapma](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)                 | Azure Machine Learning model kataloğu, belirli göreviniz için ince ayar yapabileceğiniz birçok açık kaynak modeli sunar. Bu modülü [AzureML Üretken AI Öğrenme Yolu](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) içinden deneyin.                                                                 |
| **Eğitim:** [Azure OpenAI İnce Ayar](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                  | Microsoft Azure üzerinde W&B kullanarak GPT-3.5 veya GPT-4 modellerini ince ayar yapmak, model performansının detaylı takibini ve analizini sağlar. Bu rehber, OpenAI İnce Ayar kılavuzundaki kavramları Azure OpenAI için özel adımlar ve özelliklerle genişletir.                                                                                                                        |
|                                                                                                                                                                                                                            |                                                                                                                                                                                                                                                                                                                                                                                             |

## 2. İkincil Kaynaklar

Bu bölüm, keşfetmeye değer ancak bu derste ele almaya vakit bulamadığımız ek kaynakları içerir. Gelecekteki bir derste veya ikincil bir ödev seçeneği olarak ele alınabilirler. Şimdilik, bu kaynakları kullanarak bu konu hakkında kendi uzmanlığınızı ve bilginizi geliştirin.

| Başlık/Bağlantı                                                                                                                                                                                                                 | Açıklama                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **OpenAI Cookbook**: [Chat Model İnce Ayarı için Veri Hazırlama ve Analizi](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                                    | Bu not defteri, bir chat modelini ince ayar yapmak için kullanılan chat veri setini ön işleme ve analiz etme aracı olarak hizmet eder. Format hatalarını kontrol eder, temel istatistikler sağlar ve ince ayar maliyetleri için token sayısını tahmin eder. Bakınız: [gpt-3.5-turbo için ince ayar yöntemi](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                         |
| **OpenAI Cookbook**: [Qdrant ile Retrieval Augmented Generation (RAG) için İnce Ayar](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)               | Bu not defterinin amacı, OpenAI modellerini Retrieval Augmented Generation (RAG) için nasıl ince ayar yapacağınızı kapsamlı bir örnekle göstermek. Ayrıca model performansını artırmak ve uydurmaları azaltmak için Qdrant ve Few-Shot Learning entegrasyonunu da içerecek.                                                                                                                                                                                                                                         |
| **OpenAI Cookbook**: [Weights & Biases ile GPT İnce Ayarı](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                                        | Weights & Biases (W&B), modelleri eğitmek, ince ayar yapmak ve temel modellerden yararlanmak için AI geliştirici platformudur. Önce onların [OpenAI İnce Ayar](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) rehberini okuyun, ardından Cookbook alıştırmasını deneyin.                                                                                                                                                                                        |
| **Topluluk Eğitimi** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - Küçük Dil Modelleri için ince ayar                                                                 | Microsoft’un yeni küçük modeli [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst) ile tanışın; şaşırtıcı derecede güçlü ve kompakt. Bu eğitim, Phi-2’yi ince ayar yapmayı, benzersiz bir veri seti oluşturmayı ve QLoRA kullanarak modeli ince ayar yapmayı gösterir.                                                                                                                                                                  |
| **Hugging Face Eğitimi** [2024’te Hugging Face ile LLM’leri Nasıl İnce Ayar Yapılır](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                              | Bu blog yazısı, 2024’te Hugging Face TRL, Transformers ve veri setleri kullanarak açık LLM’lerin nasıl ince ayar yapılacağını anlatır. Bir kullanım durumu tanımlarsınız, geliştirme ortamı kurarsınız, veri seti hazırlarsınız, modeli ince ayar yapar, test ve değerlendirme yapar, ardından üretime alırsınız.                                                                                                                                                                                        |
| **Hugging Face:** [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)                                                                                                   | [En son teknoloji makine öğrenimi modellerinin](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst) daha hızlı ve kolay eğitim ve dağıtımlarını sağlar. Repo, YouTube video rehberli Colab dostu eğitimler içerir. **Son [yerel-öncelikli](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) güncellemeyi yansıtır.** [AutoTrain dokümantasyonunu](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) okuyun. |
|                                                                                                                                                                                                                               |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Feragatname**:  
Bu belge, AI çeviri servisi [Co-op Translator](https://github.com/Azure/co-op-translator) kullanılarak çevrilmiştir. Doğruluk için çaba göstersek de, otomatik çevirilerin hatalar veya yanlışlıklar içerebileceğini lütfen unutmayın. Orijinal belge, kendi dilinde yetkili kaynak olarak kabul edilmelidir. Kritik bilgiler için profesyonel insan çevirisi önerilir. Bu çevirinin kullanımı sonucu ortaya çıkabilecek yanlış anlamalar veya yorum hatalarından sorumlu değiliz.