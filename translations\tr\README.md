<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:32:24+00:00",
  "source_file": "README.md",
  "language_code": "tr"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.tr.png)

### Generatif AI uygulamaları geliştirmeye başlamak için bilmeniz gereken her şeyi öğreten 21 Ders

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 Çok Dilli Destek

#### GitHub Action ile desteklenir (Otomatik ve Her Zaman Güncel)

[Fransızca](../fr/README.md) | [İspanyolca](../es/README.md) | [Almanca](../de/README.md) | [Rusça](../ru/README.md) | [Arapça](../ar/README.md) | [Farsça (Persian)](../fa/README.md) | [Urduca](../ur/README.md) | [Çince (Basitleştirilmiş)](../zh/README.md) | [Çince (Geleneksel, Makao)](../mo/README.md) | [Çince (Geleneksel, Hong Kong)](../hk/README.md) | [Çince (Geleneksel, Tayvan)](../tw/README.md) | [Japonca](../ja/README.md) | [Korece](../ko/README.md) | [Hintçe](../hi/README.md) | [Bengalce](../bn/README.md) | [Marathi](../mr/README.md) | [Nepalce](../ne/README.md) | [Pencapça (Gurmukhi)](../pa/README.md) | [Portekizce (Portekiz)](../pt/README.md) | [Portekizce (Brezilya)](../br/README.md) | [İtalyanca](../it/README.md) | [Lehçe](../pl/README.md) | [Türkçe](./README.md) | [Yunanca](../el/README.md) | [Tayca](../th/README.md) | [İsveççe](../sv/README.md) | [Danca](../da/README.md) | [Norveççe](../no/README.md) | [Fince](../fi/README.md) | [Flemenkçe](../nl/README.md) | [İbranice](../he/README.md) | [Vietnamca](../vi/README.md) | [Endonezce](../id/README.md) | [Malayca](../ms/README.md) | [Tagalogca (Filipince)](../tl/README.md) | [Svahili](../sw/README.md) | [Macarca](../hu/README.md) | [Çekçe](../cs/README.md) | [Slovakça](../sk/README.md) | [Rumence](../ro/README.md) | [Bulgarca](../bg/README.md) | [Sırpça (Kiril)](../sr/README.md) | [Hırvatça](../hr/README.md) | [Slovence](../sl/README.md) | [Ukraynaca](../uk/README.md) | [Birmanca (Myanmar)](../my/README.md)

# Başlangıç Seviyesi için Generatif AI (Sürüm 3) - Bir Kurs

Microsoft Cloud Advocates tarafından hazırlanan 21 derslik kapsamlı kursumuzla Generatif AI uygulamaları geliştirme temellerini öğrenin.

## 🌱 Başlarken

Bu kursta 21 ders bulunmaktadır. Her ders kendi konusunu kapsar, istediğiniz yerden başlayabilirsiniz!

Dersler ya Generatif AI kavramını açıklayan "Öğren" dersleri ya da mümkün olduğunda hem **Python** hem de **TypeScript** ile kavram ve kod örnekleri sunan "Yap" dersleri olarak etiketlenmiştir.

.NET geliştiriciler için [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst) sayfasına göz atabilirsiniz!

Her ders ayrıca ek öğrenme araçları içeren "Öğrenmeye Devam Et" bölümü içerir.

## İhtiyacınız Olanlar
### Bu kursun kodlarını çalıştırmak için şunlardan birini kullanabilirsiniz:
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **Dersler:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **Dersler:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **Dersler:** "oai-assignment" 
   
- Python veya TypeScript hakkında temel bilgi faydalıdır - \*Tamamen yeni başlayanlar için bu [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) ve [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) kurslarına göz atabilirsiniz
- Bu depoyu kendi GitHub hesabınıza [forklamak](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) için bir GitHub hesabı

Geliştirme ortamınızı kurmanıza yardımcı olmak için bir **[Kurs Kurulumu](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** dersi hazırladık.

Daha sonra kolayca bulabilmek için bu depoya [yıldız (🌟) vermeyi](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst) unutmayın.

## 🧠 Yayına Hazır mısınız?

Daha gelişmiş kod örnekleri arıyorsanız, hem **Python** hem de **TypeScript** için hazırladığımız [Generatif AI Kod Örnekleri koleksiyonumuza](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) göz atabilirsiniz.

## 🗣️ Diğer Öğrenenlerle Tanışın, Destek Alın

Bu kursu alan diğer öğrenenlerle tanışmak ve destek almak için [resmi Azure AI Foundry Discord sunucumuza](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) katılın.

Sorularınızı sorun veya ürün geri bildirimi paylaşmak için [Azure AI Foundry Geliştirici Forumu](https://aka.ms/azureaifoundry/forum) GitHub sayfasını kullanabilirsiniz.

## 🚀 Bir Startup mı Kuruyorsunuz?

[Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) programına kaydolun ve **ücretsiz OpenAI kredileri** ile Azure OpenAI Hizmetleri üzerinden OpenAI modellerine erişmek için **Azure kredilerinde 150.000 $’a kadar destek** kazanın.

## 🙏 Yardımcı Olmak İster misiniz?

Önerileriniz mi var ya da yazım ya da kod hataları mı buldunuz? [Bir issue açın](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) veya [Pull request oluşturun](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 Her ders şunları içerir:

- Konuya kısa bir video tanıtımı
- README dosyasında yazılı ders
- Azure OpenAI ve OpenAI API destekli Python ve TypeScript kod örnekleri
- Öğrenmeye devam etmek için ek kaynak bağlantıları

## 🗃️ Dersler

| #   | **Ders Bağlantısı**                                                                                                                          | **Açıklama**                                                                                   | **Video**                                                                   | **Ek Öğrenme**                                                                 |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------------ |
| 00  | [Kurs Kurulumu](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Öğren:** Geliştirme Ortamınızı Nasıl Kurarsınız                                            | Video Yakında                                                                 | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Generatif AI ve LLM’lere Giriş](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                                        | **Öğren:** Generatif AI’nin ne olduğu ve Büyük Dil Modellerinin (LLM) nasıl çalıştığını anlamak | [Video](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Farklı LLM’leri Keşfetmek ve Karşılaştırmak](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)           | **Öğren:** Kullanım durumunuza uygun modeli nasıl seçersiniz                                  | [Video](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Generatif AI’yi Sorumlu Kullanmak](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                           | **Öğren:** Generatif AI Uygulamalarını sorumlu şekilde nasıl geliştirirsiniz                   | [Video](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Prompt Mühendisliği Temellerini Anlamak](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)                     | **Öğren:** Uygulamalı Prompt Mühendisliği En İyi Uygulamaları                                 | [Video](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Gelişmiş Promtlar Oluşturmak](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                              | **Öğren:** Promtlarınızın sonuçlarını iyileştiren prompt mühendisliği tekniklerini nasıl uygularsınız | [Video](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [Metin Üretim Uygulamaları Geliştirme](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **Oluştur:** Azure OpenAI / OpenAI API kullanarak bir metin üretim uygulaması                      | [Video](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [Sohbet Uygulamaları Geliştirme](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **Oluştur:** Sohbet uygulamalarını verimli şekilde geliştirme ve entegre etme teknikleri          | [Video](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [Arama Uygulamaları ve Vektör Veritabanları Geliştirme](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Oluştur:** Verileri aramak için Embeddings kullanan bir arama uygulaması                        | [Video](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [Görüntü Üretim Uygulamaları Geliştirme](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **Oluştur:** Bir görüntü üretim uygulaması                                                       | [Video](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [Düşük Kodlu AI Uygulamaları Geliştirme](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **Oluştur:** Düşük Kod araçları kullanarak bir Üretken AI uygulaması                             | [Video](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [Fonksiyon Çağrısı ile Harici Uygulamaların Entegrasyonu](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **Oluştur:** Fonksiyon çağrısı nedir ve uygulamalarda kullanım alanları                           | [Video](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [AI Uygulamaları için UX Tasarımı](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Öğren:** Üretken AI uygulamaları geliştirirken UX tasarım prensiplerini nasıl uygulayacağınız   | [Video](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [Üretken AI Uygulamalarınızı Güvence Altına Alma](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **Öğren:** AI sistemlerine yönelik tehditler ve riskler ile bu sistemleri güvence altına alma yöntemleri | [Video](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Üretken AI Uygulama Yaşam Döngüsü](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **Öğren:** LLM Yaşam Döngüsünü ve LLMOps’u yönetmek için araçlar ve metrikler                    | [Video](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) ve Vektör Veritabanları](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **Oluştur:** Vektör Veritabanlarından embedding’leri almak için RAG Çerçevesi kullanan bir uygulama | [Video](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [Açık Kaynak Modeller ve Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **Oluştur:** Hugging Face’de bulunan açık kaynak modelleri kullanan bir uygulama                 | [Video](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI Ajanları](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **Oluştur:** Bir AI Ajan Çerçevesi kullanan bir uygulama                                        | [Video](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [LLM’lerin İnce Ayarı](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Öğren:** LLM’lerin ince ayarının ne olduğu, neden yapıldığı ve nasıl yapıldığı                 | [Video](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [SLM’lerle Geliştirme](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Öğren:** Küçük Dil Modelleri ile geliştirmenin faydaları                                      | Video Yakında | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Mistral Modelleri ile Geliştirme](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Öğren:** Mistral Ailesi Modellerinin özellikleri ve farkları                                  | Video Yakında | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Meta Modelleri ile Geliştirme](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **Öğren:** Meta Ailesi Modellerinin özellikleri ve farkları                                    | Video Yakında | [Daha Fazla Bilgi](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 Özel Teşekkürler

Tüm GitHub Actions ve iş akışlarını oluşturan [**John Aziz**](https://www.linkedin.com/in/john0isaac/)’a özel teşekkürler

Her dersin öğrenme ve kod deneyimini geliştirmek için önemli katkılar sağlayan [**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/)’ye teşekkür ederiz.

## 🎒 Diğer Kurslar

Ekibimiz başka kurslar da üretiyor! Göz atın:

- [**YENİ** Başlangıç için Model Context Protocol](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [Başlangıç için AI Ajanları](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [.NET ile Başlangıç için Üretken AI](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [JavaScript ile Başlangıç için Üretken AI](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [Başlangıç için Makine Öğrenimi](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Başlangıç için Veri Bilimi](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [Başlangıç için AI](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Başlangıç için Siber Güvenlik](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Başlangıç için Web Geliştirme](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [Başlangıç için IoT](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [Başlangıç için XR Geliştirme](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI Eşliğinde Programlama için GitHub Copilot’u Ustalaştırma](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [C#/.NET Geliştiricileri için GitHub Copilot’u Ustalaştırma](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [Kendi Copilot Maceranı Seç](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**Feragatname**:  
Bu belge, AI çeviri servisi [Co-op Translator](https://github.com/Azure/co-op-translator) kullanılarak çevrilmiştir. Doğruluk için çaba göstersek de, otomatik çevirilerin hatalar veya yanlışlıklar içerebileceğini lütfen unutmayın. Orijinal belge, kendi dilinde yetkili kaynak olarak kabul edilmelidir. Kritik bilgiler için profesyonel insan çevirisi önerilir. Bu çevirinin kullanımı sonucu ortaya çıkabilecek yanlış anlamalar veya yorum hatalarından sorumlu değiliz.