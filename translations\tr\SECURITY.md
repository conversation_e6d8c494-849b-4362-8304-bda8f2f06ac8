<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:53:32+00:00",
  "source_file": "SECURITY.md",
  "language_code": "tr"
}
-->
## Güvenlik

Microsoft, yazılım ürünlerimizin ve hizmetlerimizin güvenliğini ciddiye almaktadır. Bu, [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [Xamarin](https://github.com/xamarin) ve [GitHub organizasyonlarımız](https://opensource.microsoft.com/) aracılığıyla yönetilen tüm kaynak kod depolarını da kapsamaktadır.

Eğer Microsoft’a ait herhangi bir depoda [Microsoft’un güvenlik açığı tanımına](https://aka.ms/opensource/security/definition) uyan bir güvenlik açığı bulduğunuzu düşünüyorsanız, lütfen aşağıda belirtildiği şekilde bize bildirin.

## Güvenlik Sorunlarının Bildirilmesi

**Lütfen güvenlik açıklarını kamuya açık GitHub sorunları üzerinden bildirmeyin.**

Bunun yerine, lütfen Microsoft Güvenlik Yanıt Merkezi’ne (MSRC) [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report) adresinden bildirin.

Giriş yapmadan göndermeyi tercih ederseniz, [<EMAIL>](mailto:<EMAIL>) adresine e-posta gönderin. Mümkünse, mesajınızı PGP anahtarımızla şifreleyin; anahtarı [Microsoft Güvenlik Yanıt Merkezi PGP Anahtarı sayfasından](https://aka.ms/opensource/security/pgpkey) indirebilirsiniz.

24 saat içinde yanıt almanız beklenir. Eğer herhangi bir nedenle yanıt alamazsanız, orijinal mesajınızın ulaştığından emin olmak için lütfen e-posta ile takipte bulunun. Ek bilgiler [microsoft.com/msrc](https://aka.ms/opensource/security/msrc) adresinde mevcuttur.

Lütfen aşağıda istenen bilgileri (mümkün olduğunca eksiksiz) ekleyin; bu, sorunun doğası ve kapsamını daha iyi anlamamıza yardımcı olacaktır:

  * Sorun türü (örneğin, buffer overflow, SQL injection, cross-site scripting vb.)
  * Sorunun ortaya çıktığı kaynak dosyanın tam yolu
  * Etkilenen kaynak kodun konumu (tag/branch/commit veya doğrudan URL)
  * Sorunu yeniden oluşturmak için gereken özel yapılandırmalar
  * Sorunu yeniden oluşturmak için adım adım talimatlar
  * Kanıt niteliğinde konsept veya exploit kodu (varsa)
  * Sorunun etkisi ve bir saldırganın bu sorunu nasıl kullanabileceği

Bu bilgiler, raporunuzu daha hızlı değerlendirmemize yardımcı olacaktır.

Eğer bir bug bounty kapsamında raporluyorsanız, daha eksiksiz raporlar daha yüksek ödüller kazanmanıza katkı sağlar. Aktif programlarımız hakkında daha fazla bilgi için lütfen [Microsoft Bug Bounty Programı](https://aka.ms/opensource/security/bounty) sayfamızı ziyaret edin.

## Tercih Edilen Diller

Tüm iletişimlerin İngilizce olmasını tercih ediyoruz.

## Politika

Microsoft, [Koordine Edilmiş Güvenlik Açığı Bildirimi](https://aka.ms/opensource/security/cvd) ilkesini takip etmektedir.

**Feragatname**:  
Bu belge, AI çeviri servisi [Co-op Translator](https://github.com/Azure/co-op-translator) kullanılarak çevrilmiştir. Doğruluk için çaba göstersek de, otomatik çevirilerin hatalar veya yanlışlıklar içerebileceğini lütfen unutmayınız. Orijinal belge, kendi dilinde yetkili kaynak olarak kabul edilmelidir. Kritik bilgiler için profesyonel insan çevirisi önerilir. Bu çevirinin kullanımı sonucu ortaya çıkabilecek yanlış anlamalar veya yorum hatalarından sorumlu değiliz.