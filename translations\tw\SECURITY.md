<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:51:40+00:00",
  "source_file": "SECURITY.md",
  "language_code": "tw"
}
-->
## 安全性

Microsoft 非常重視我們軟體產品與服務的安全性，這包括所有透過我們的 GitHub 組織管理的原始碼庫，這些組織包括 [Microsoft](https://github.com/microsoft)、[Azure](https://github.com/Azure)、[DotNet](https://github.com/dotnet)、[AspNet](https://github.com/aspnet)、[Xamarin](https://github.com/xamarin)，以及 [我們的 GitHub 組織](https://opensource.microsoft.com/)。

如果您認為在任何 Microsoft 擁有的原始碼庫中發現了符合 [Microsoft 對安全漏洞定義](https://aka.ms/opensource/security/definition) 的安全漏洞，請依照以下說明向我們回報。

## 回報安全問題

**請勿透過公開的 GitHub issue 回報安全漏洞。**

請改為向 Microsoft 安全回應中心 (MSRC) 回報，網址為 [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report)。

如果您不想登入提交，請寄信至 [<EMAIL>](mailto:<EMAIL>)。若可能，請使用我們的 PGP 金鑰加密您的訊息；您可以從 [Microsoft 安全回應中心 PGP 金鑰頁面](https://aka.ms/opensource/security/pgpkey) 下載。

您應該會在 24 小時內收到回覆。如果因故未收到，請透過電子郵件追蹤，確保我們已收到您的原始訊息。更多資訊請參考 [microsoft.com/msrc](https://aka.ms/opensource/security/msrc)。

請盡可能提供以下資訊，以協助我們更好地了解問題的性質與範圍：

  * 問題類型（例如緩衝區溢位、SQL 注入、跨站腳本攻擊等）
  * 與問題現象相關的原始碼檔案完整路徑
  * 受影響原始碼的位置（標籤/分支/提交或直接 URL）
  * 重現問題所需的特殊設定
  * 重現問題的逐步說明
  * 概念驗證或利用程式碼（若有）
  * 問題的影響範圍，包括攻擊者可能如何利用此漏洞

這些資訊將幫助我們更快地處理您的回報。

若您是為了漏洞賞金計畫回報，提供更完整的報告有助於獲得更高的獎金。請造訪我們的 [Microsoft 漏洞賞金計畫](https://aka.ms/opensource/security/bounty) 頁面，了解更多關於現行計畫的詳情。

## 優先語言

我們偏好所有溝通以英文進行。

## 政策

Microsoft 遵循 [協調漏洞揭露](https://aka.ms/opensource/security/cvd) 原則。

**免責聲明**：  
本文件係使用 AI 翻譯服務 [Co-op Translator](https://github.com/Azure/co-op-translator) 進行翻譯。雖然我們致力於確保翻譯的準確性，但請注意，自動翻譯可能包含錯誤或不準確之處。原始文件的母語版本應視為權威來源。對於重要資訊，建議採用專業人工翻譯。我們不對因使用本翻譯而產生的任何誤解或誤釋負責。