<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:39:15+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "uk"
}
-->
# Фреймворки для нейронних мереж

Як ми вже дізналися, щоб ефективно навчати нейронні мережі, потрібно зробити дві речі:

* Працювати з тензорами, наприклад, множити, додавати та обчислювати деякі функції, такі як sigmoid або softmax
* Обчислювати градієнти всіх виразів для виконання оптимізації методом градієнтного спуску

Хоча бібліотека `numpy` може виконувати першу частину, нам потрібен механізм для обчислення градієнтів. У нашому фреймворку, який ми розробили в попередньому розділі, нам доводилося вручну програмувати всі функції похідних у методі `backward`, який виконує зворотне поширення помилки. Ідеально, якщо фреймворк дає можливість обчислювати градієнти *будь-якого виразу*, який ми можемо визначити.

Ще одна важлива річ — це можливість виконувати обчислення на GPU або інших спеціалізованих обчислювальних пристроях, таких як TPU. Навчання глибоких нейронних мереж вимагає *дуже багато* обчислень, і можливість паралелізувати ці обчислення на GPU є надзвичайно важливою.

> ✅ Термін «паралелізувати» означає розподіляти обчислення між кількома пристроями.

Наразі двома найпопулярнішими фреймворками для нейронних мереж є TensorFlow і PyTorch. Обидва надають низькорівневий API для роботи з тензорами як на CPU, так і на GPU. Поверх низькорівневого API існує також високорівневий API, який називається відповідно Keras і PyTorch Lightning.

Низькорівневий API | TensorFlow | PyTorch  
-------------------|------------|---------  
Високорівневий API | Keras      | PyTorch

**Низькорівневі API** в обох фреймворках дозволяють будувати так звані **обчислювальні графи**. Цей граф визначає, як обчислити вихід (зазвичай функцію втрат) для заданих вхідних параметрів, і може бути виконаний на GPU, якщо він доступний. Існують функції для диференціювання цього обчислювального графа та обчислення градієнтів, які потім можна використовувати для оптимізації параметрів моделі.

**Високорівневі API** розглядають нейронні мережі як **послідовність шарів** і значно спрощують побудову більшості нейронних мереж. Навчання моделі зазвичай вимагає підготовки даних і виклику функції `fit` для запуску процесу.

Високорівневий API дозволяє дуже швидко створювати типові нейронні мережі, не турбуючись про багато деталей. Водночас низькорівневий API дає набагато більше контролю над процесом навчання, тому його часто використовують у дослідженнях, коли працюють з новими архітектурами нейронних мереж.

Також важливо розуміти, що можна використовувати обидва API разом, наприклад, розробити власну архітектуру шару мережі за допомогою низькорівневого API, а потім використовувати її в більшій мережі, побудованій і навченій за допомогою високорівневого API. Або можна визначити мережу за допомогою високорівневого API як послідовність шарів, а потім використовувати власний низькорівневий цикл навчання для оптимізації. Обидва API базуються на одних і тих же основних концепціях і спроектовані для спільної роботи.

## Навчання

У цьому курсі більшість матеріалів доступні як для PyTorch, так і для TensorFlow. Ви можете обрати улюблений фреймворк і проходити лише відповідні ноутбуки. Якщо ви не впевнені, який фреймворк обрати, почитайте обговорення в інтернеті на тему **PyTorch vs. TensorFlow**. Також можна ознайомитися з обома фреймворками, щоб краще зрозуміти їх.

Де можливо, ми використовуватимемо високорівневі API для простоти. Проте ми вважаємо важливим розуміти, як працюють нейронні мережі з самого початку, тому на початку ми працюємо з низькорівневим API і тензорами. Якщо ж ви хочете швидко почати і не витрачати багато часу на вивчення цих деталей, можете пропустити цей етап і перейти одразу до ноутбуків з високорівневим API.

## ✍️ Вправи: Фреймворки

Продовжуйте навчання у наступних ноутбуках:

Низькорівневий API | TensorFlow+Keras Notebook | PyTorch  
-------------------|----------------------------|---------  
Високорівневий API | Keras                      | *PyTorch Lightning*

Після освоєння фреймворків давайте згадаємо поняття перенавчання.

# Перенавчання

Перенавчання — це надзвичайно важлива концепція в машинному навчанні, і дуже важливо правильно її розуміти!

Розглянемо задачу апроксимації 5 точок (позначених `x` на графіках нижче):

!linear | overfit  
-------------------------|--------------------------  
**Лінійна модель, 2 параметри** | **Нелінійна модель, 7 параметрів**  
Помилка на тренуванні = 5.3 | Помилка на тренуванні = 0  
Помилка на валідації = 5.1 | Помилка на валідації = 20

* Зліва ми бачимо хорошу пряму апроксимацію. Оскільки кількість параметрів адекватна, модель правильно відображає розподіл точок.
* Справа модель надто потужна. Оскільки у нас лише 5 точок, а модель має 7 параметрів, вона може налаштуватися так, щоб пройти через усі точки, зробивши помилку на тренуванні рівною 0. Проте це заважає моделі зрозуміти правильний патерн у даних, тому помилка на валідації дуже висока.

Дуже важливо знайти правильний баланс між складністю моделі (кількістю параметрів) та кількістю тренувальних зразків.

## Чому виникає перенавчання

  * Недостатньо тренувальних даних
  * Надто потужна модель
  * Занадто багато шуму у вхідних даних

## Як виявити перенавчання

Як видно з графіка вище, перенавчання можна виявити за дуже низькою помилкою на тренуванні і високою помилкою на валідації. Зазвичай під час навчання ми бачимо, що помилки на тренуванні і валідації спочатку зменшуються, а потім у певний момент помилка на валідації перестає знижуватися і починає зростати. Це буде сигналом перенавчання і показником того, що, ймовірно, варто припинити навчання на цьому етапі (або принаймні зробити знімок моделі).

перенавчання

## Як запобігти перенавчанню

Якщо ви бачите, що виникає перенавчання, можна зробити одне з наступного:

 * Збільшити обсяг тренувальних даних
 * Зменшити складність моделі
 * Використати техніку регуляризації, наприклад Dropout, яку ми розглянемо пізніше.

## Перенавчання і компроміс між зміщенням і дисперсією

Перенавчання — це випадок більш загальної проблеми в статистиці, яка називається компромісом між зміщенням і дисперсією (Bias-Variance Tradeoff). Якщо розглянути можливі джерела помилок у моделі, можна виділити два типи помилок:

* **Помилки зміщення (Bias errors)** виникають через те, що наш алгоритм не може правильно відобразити зв’язок у тренувальних даних. Це може бути наслідком того, що модель недостатньо потужна (**недонавчання**).
* **Помилки дисперсії (Variance errors)** виникають через те, що модель апроксимує шум у вхідних даних замість значущих закономірностей (**перенавчання**).

Під час навчання помилка зміщення зменшується (оскільки модель навчається апроксимувати дані), а помилка дисперсії зростає. Важливо припинити навчання — або вручну (коли виявляємо перенавчання), або автоматично (впроваджуючи регуляризацію) — щоб запобігти перенавчанню.

## Висновок

У цьому уроці ви дізналися про відмінності між різними API двох найпопулярніших AI-фреймворків — TensorFlow і PyTorch. Крім того, ви ознайомилися з дуже важливою темою — перенавчанням.

## 🚀 Виклик

У супровідних ноутбуках ви знайдете «завдання» внизу; пройдіть ноутбуки і виконайте ці завдання.

## Огляд і самостійне вивчення

Проведіть дослідження за наступними темами:

- TensorFlow  
- PyTorch  
- Перенавчання

Задайте собі такі питання:

- У чому різниця між TensorFlow і PyTorch?  
- У чому різниця між перенавчанням і недонавчанням?

## Завдання

У цій лабораторній роботі вам потрібно розв’язати дві задачі класифікації, використовуючи одно- та багатошарові повнозв’язні мережі на базі PyTorch або TensorFlow.

**Відмова від відповідальності**:  
Цей документ було перекладено за допомогою сервісу автоматичного перекладу [Co-op Translator](https://github.com/Azure/co-op-translator). Хоча ми прагнемо до точності, будь ласка, майте на увазі, що автоматичні переклади можуть містити помилки або неточності. Оригінальний документ рідною мовою слід вважати авторитетним джерелом. Для критично важливої інформації рекомендується звертатися до професійного людського перекладу. Ми не несемо відповідальності за будь-які непорозуміння або неправильні тлумачення, що виникли внаслідок використання цього перекладу.