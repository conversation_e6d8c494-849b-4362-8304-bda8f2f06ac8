<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:52:48+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "uk"
}
-->
# Вступ до нейронних мереж. Багатошаровий перцептрон

У попередньому розділі ви ознайомилися з найпростішою моделлю нейронної мережі — одношаровим перцептроном, лінійною моделлю для двокласової класифікації.

У цьому розділі ми розширимо цю модель до більш гнучкої структури, що дозволить нам:

* виконувати **багатокласову класифікацію** на додаток до двокласової
* розв’язувати **задачі регресії** поряд із класифікацією
* розділяти класи, які не є лінійно роздільними

Також ми розробимо власний модульний фреймворк на Python, який дозволить будувати різні архітектури нейронних мереж.

## Формалізація задачі машинного навчання

Почнемо з формалізації задачі машинного навчання. Припустимо, у нас є навчальний набір даних **X** з мітками **Y**, і нам потрібно побудувати модель *f*, яка робитиме максимально точні передбачення. Якість передбачень вимірюється за допомогою **функції втрат** ℒ. Часто використовують такі функції втрат:

* Для задачі регресії, коли потрібно передбачити число, можна застосувати **абсолютну помилку** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>| або **квадратичну помилку** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup>
* Для класифікації використовують **0-1 втрати** (що фактично відповідає **точності** моделі) або **логістичні втрати**.

Для одношарового перцептрона функція *f* визначалась як лінійна функція *f(x)=wx+b* (тут *w* — матриця ваг, *x* — вектор вхідних ознак, а *b* — вектор зсуву). Для різних архітектур нейронних мереж ця функція може мати складніший вигляд.

> У випадку класифікації часто бажано отримати ймовірності відповідних класів на виході мережі. Щоб перетворити довільні числа у ймовірності (наприклад, нормалізувати вихід), часто використовують функцію **softmax** σ, і функція *f* набуває вигляду *f(x)=σ(wx+b)*

У наведеному визначенні *f* параметри *w* і *b* називають **параметрами** θ=⟨*w,b*⟩. Маючи набір даних ⟨**X**,**Y**⟩, ми можемо обчислити загальну помилку на всьому наборі як функцію від параметрів θ.

> ✅ **Мета навчання нейронної мережі — мінімізувати помилку, змінюючи параметри θ**

## Оптимізація за допомогою градієнтного спуску

Існує відомий метод оптимізації функцій, який називається **градієнтним спуском**. Ідея полягає в тому, що ми можемо обчислити похідну (у багатовимірному випадку — **градієнт**) функції втрат за параметрами, і змінювати параметри так, щоб помилка зменшувалась. Це можна формалізувати так:

* Ініціалізувати параметри випадковими значеннями w<sup>(0)</sup>, b<sup>(0)</sup>
* Повторювати наступний крок багато разів:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

Під час навчання кроки оптимізації зазвичай обчислюють, враховуючи весь набір даних (пам’ятайте, що функція втрат обчислюється як сума по всіх навчальних прикладах). Однак на практиці беруть невеликі частини набору, які називають **мінібатчами**, і обчислюють градієнти на підмножині даних. Оскільки підмножина вибирається випадково щоразу, цей метод називають **стохастичним градієнтним спуском** (SGD).

## Багатошарові перцептрони та зворотне поширення помилки

Одношарова мережа, як ми бачили вище, здатна класифікувати лінійно роздільні класи. Щоб побудувати більш складну модель, можна поєднати кілька шарів мережі. Математично це означає, що функція *f* набуде складнішої форми і буде обчислюватися у кілька кроків:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

Тут α — це **нелінійна функція активації**, σ — функція softmax, а параметри θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*>.

Алгоритм градієнтного спуску залишається тим самим, але обчислювати градієнти стає складніше. Використовуючи правило диференціювання складеної функції (правило ланцюга), можна обчислити похідні так:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ Для обчислення похідних функції втрат за параметрами використовується правило ланцюга.

Зверніть увагу, що ліві частини всіх цих виразів однакові, тому ми можемо ефективно обчислювати похідні, починаючи від функції втрат і рухаючись "назад" по обчислювальному графу. Саме тому метод навчання багатошарового перцептрона називають **зворотним поширенням помилки** або просто 'backprop'.

> TODO: посилання на зображення

> ✅ Ми детальніше розглянемо backprop у нашому прикладі в ноутбуці.

## Висновок

У цьому уроці ми створили власну бібліотеку нейронних мереж і використали її для простої двовимірної задачі класифікації.

## 🚀 Виклик

У супровідному ноутбуці ви реалізуєте власний фреймворк для побудови та навчання багатошарових перцептронів. Ви зможете детально побачити, як працюють сучасні нейронні мережі.

Перейдіть до ноутбука OwnFramework і опрацюйте його.

## Огляд та самостійне вивчення

Зворотне поширення помилки — це поширений алгоритм у штучному інтелекті та машинному навчанні, вартий детального вивчення.

## Завдання

У цій лабораторній роботі вам потрібно використати фреймворк, створений у цьому уроці, для розв’язання задачі класифікації рукописних цифр MNIST.

* Інструкції
* Ноутбук

**Відмова від відповідальності**:  
Цей документ було перекладено за допомогою сервісу автоматичного перекладу [Co-op Translator](https://github.com/Azure/co-op-translator). Хоча ми прагнемо до точності, будь ласка, майте на увазі, що автоматичні переклади можуть містити помилки або неточності. Оригінальний документ рідною мовою слід вважати авторитетним джерелом. Для критично важливої інформації рекомендується звертатися до професійного людського перекладу. Ми не несемо відповідальності за будь-які непорозуміння або неправильні тлумачення, що виникли внаслідок використання цього перекладу.