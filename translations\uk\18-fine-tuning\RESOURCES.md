<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:11:07+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "uk"
}
-->
# Ресурси для самостійного навчання

Урок був створений на основі низки ключових ресурсів від OpenAI та Azure OpenAI, які використовувалися як довідкові матеріали для термінології та навчальних посібників. Ось неповний список для вашого самостійного вивчення.

## 1. Основні ресурси

| Назва/Посилання                                                                                                                                                                                                             | Опис                                                                                                                                                                                                                                                                                                                                                                                        |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Тонке налаштування покращує навчання з кількох прикладів, тренуючись на значно більшій кількості прикладів, ніж поміщається у запит, що дозволяє зекономити кошти, покращити якість відповідей і знизити затримки. **Ознайомтеся з оглядом тонкого налаштування від OpenAI.**                                                                                                               |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Зрозумійте **що таке тонке налаштування (концепція)**, чому варто його розглядати (мотиваційна проблема), які дані використовувати (тренування) та як оцінювати якість.                                                                                                                                                                                                                     |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Сервіс Azure OpenAI дозволяє налаштовувати моделі під ваші особисті набори даних за допомогою тонкого налаштування. Дізнайтеся **як виконувати тонке налаштування (процес)**, обирати моделі через Azure AI Studio, Python SDK або REST API.                                                                                                                                                   |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | Великі мовні моделі (LLM) можуть працювати не дуже добре у певних доменах, завданнях або на конкретних наборах даних, або видавати неточні чи оманливі результати. **Коли варто розглядати тонке налаштування** як можливе рішення?                                                                                                                                                            |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Безперервне тонке налаштування — це ітеративний процес вибору вже тонко налаштованої моделі як базової та **подальшого її налаштування** на нових наборах тренувальних прикладів.                                                                                                                                                                                                             |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Тонке налаштування моделі **з прикладами виклику функцій** може покращити результати, забезпечуючи більш точні та послідовні відповіді — з подібним форматом відповідей і економією коштів.                                                                                                                                                                                                |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Ознайомтеся з цією таблицею, щоб зрозуміти, **які моделі можна тонко налаштовувати** в Azure OpenAI, та в яких регіонах вони доступні. За потреби перевірте обмеження на кількість токенів і терміни дії тренувальних даних.                                                                                                                                                                  |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Цей 30-хвилинний епізод AI Show **жовтень 2023** обговорює переваги, недоліки та практичні поради, які допоможуть вам прийняти рішення.                                                                                                                                                                                                                                                     |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Цей ресурс **AI Playbook** проведе вас через вимоги до даних, форматування, налаштування гіперпараметрів та виклики/обмеження, які варто враховувати.                                                                                                                                                                                                                                        |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Навчіться створювати приклад набору даних для тонкого налаштування, готуватися до налаштування, запускати завдання тонкого налаштування та розгортати тонко налаштовану модель в Azure.                                                                                                                                                                                                     |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio дозволяє налаштовувати великі мовні моделі під ваші особисті набори даних _за допомогою інтерфейсу, зручного для розробників з мінімальним кодуванням_. Перегляньте цей приклад.                                                                                                                                                                                             |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | У цій статті описано, як тонко налаштувати модель Hugging Face з бібліотекою Hugging Face transformers на одному GPU за допомогою Azure DataBricks та бібліотек Hugging Face Trainer.                                                                                                                                                                                                        |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Каталог моделей в Azure Machine Learning пропонує багато відкритих моделей, які можна тонко налаштувати для вашого конкретного завдання. Спробуйте цей модуль з [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst).                                                                 |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Тонке налаштування моделей GPT-3.5 або GPT-4 на Microsoft Azure з використанням W&B дозволяє детально відстежувати та аналізувати продуктивність моделі. Цей посібник розширює концепції з OpenAI Fine-Tuning, додаючи конкретні кроки та функції для Azure OpenAI.                                                                                                                           |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                             |

## 2. Додаткові ресурси

Цей розділ містить додаткові ресурси, які варто вивчити, але на які не вистачило часу в цьому уроці. Вони можуть бути розглянуті в майбутніх уроках або як додаткове завдання. Наразі використовуйте їх для поглиблення своїх знань і навичок з цієї теми.

| Назва/Посилання                                                                                                                                                                                                            | Опис                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Підготовка та аналіз даних для тонкого налаштування чат-моделі](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | Цей ноутбук служить інструментом для попередньої обробки та аналізу набору даних для тонкого налаштування чат-моделі. Він перевіряє форматування, надає базову статистику та оцінює кількість токенів для розрахунку вартості тонкого налаштування. Див. також: [Fine-tuning method for gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                     |
| **OpenAI Cookbook**: [Тонке налаштування для Retrieval Augmented Generation (RAG) з Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | Мета цього ноутбука — покроково показати, як тонко налаштувати моделі OpenAI для Retrieval Augmented Generation (RAG). Також буде інтегровано Qdrant і Few-Shot Learning для підвищення продуктивності моделі та зменшення вигадок.                                                                                                                                                                                                                                  |
| **OpenAI Cookbook**: [Тонке налаштування GPT з Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) — це платформа для розробників ШІ з інструментами для тренування моделей, тонкого налаштування та використання базових моделей. Спочатку ознайомтеся з їхнім посібником [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst), а потім спробуйте вправу з Cookbook.                                                                                                                                                     |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - тонке налаштування для малих мовних моделей                                                   | Познайомтеся з [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), новою компактною, але потужною малою моделлю від Microsoft. Цей посібник проведе вас через процес тонкого налаштування Phi-2, показуючи, як створити унікальний набір даних і налаштувати модель за допомогою QLoRA.                                                                                                                                    |
| **Hugging Face Tutorial** [Як тонко налаштувати LLM у 2024 році з Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | У цьому блозі описано, як тонко налаштувати відкриті великі мовні моделі за допомогою Hugging Face TRL, Transformers і datasets у 2024 році. Ви визначаєте кейс використання, налаштовуєте середовище розробки, готуєте набір даних, тонко налаштовуєте модель, тестуєте та оцінюєте її, а потім розгортаєте у продакшн.                                                                                                                                                          |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                            | Забезпечує швидше та простіше тренування і розгортання [сучасних моделей машинного навчання](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Репозиторій містить навчальні посібники, дружні до Colab, з відеоінструкціями на YouTube для тонкого налаштування. **Відображає нещодавнє оновлення [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst).** Ознайомтеся з [документацією AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst). |
|                                                                                                                                                                                                                           |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**Відмова від відповідальності**:  
Цей документ було перекладено за допомогою сервісу автоматичного перекладу [Co-op Translator](https://github.com/Azure/co-op-translator). Хоча ми прагнемо до точності, будь ласка, майте на увазі, що автоматичні переклади можуть містити помилки або неточності. Оригінальний документ рідною мовою слід вважати авторитетним джерелом. Для критично важливої інформації рекомендується звертатися до професійного людського перекладу. Ми не несемо відповідальності за будь-які непорозуміння або неправильні тлумачення, що виникли внаслідок використання цього перекладу.