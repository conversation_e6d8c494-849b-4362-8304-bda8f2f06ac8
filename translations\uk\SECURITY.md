<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:57:04+00:00",
  "source_file": "SECURITY.md",
  "language_code": "uk"
}
-->
## Безпека

Microsoft серйозно ставиться до безпеки наших програмних продуктів і сервісів, включно з усіма репозиторіями вихідного коду, які керуються через наші організації на GitHub, серед яких [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [X<PERSON>rin](https://github.com/xamarin) та [наші організації на GitHub](https://opensource.microsoft.com/).

Якщо ви вважаєте, що знайшли вразливість безпеки в будь-якому репозиторії, що належить Microsoft, яка відповідає [визначенню вразливості безпеки Microsoft](https://aka.ms/opensource/security/definition), будь ласка, повідомте нам про це, як описано нижче.

## Повідомлення про проблеми безпеки

**Будь ласка, не повідомляйте про вразливості безпеки через публічні питання на GitHub.**

Натомість, будь ласка, повідомляйте про них до Microsoft Security Response Center (MSRC) за адресою [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Якщо ви віддаєте перевагу надсиланню без входу в систему, надішліть листа на [<EMAIL>](mailto:<EMAIL>). Якщо можливо, зашифруйте ваше повідомлення за допомогою нашого PGP-ключа; будь ласка, завантажте його зі сторінки [Microsoft Security Response Center PGP Key](https://aka.ms/opensource/security/pgpkey).

Ви повинні отримати відповідь протягом 24 годин. Якщо з якоїсь причини цього не станеться, будь ласка, надішліть повторне повідомлення електронною поштою, щоб переконатися, що ми отримали ваше первинне звернення. Додаткову інформацію можна знайти на [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Будь ласка, включіть у повідомлення якомога більше з наведених нижче даних, щоб допомогти нам краще зрозуміти природу та масштаб потенційної проблеми:

  * Тип проблеми (наприклад, переповнення буфера, SQL-ін’єкція, міжсайтовий скриптинг тощо)
  * Повні шляхи до файлів вихідного коду, пов’язаних із проявом проблеми
  * Розташування ураженого коду (тег/гілка/коміт або пряме посилання)
  * Будь-які спеціальні налаштування, необхідні для відтворення проблеми
  * Покрокові інструкції для відтворення проблеми
  * Доказ концепції або код експлойту (якщо можливо)
  * Вплив проблеми, включно з тим, як зловмисник може її використати

Ця інформація допоможе нам швидше опрацювати ваше повідомлення.

Якщо ви повідомляєте про баг у рамках програми Bug Bounty, більш повні звіти можуть сприяти отриманню вищої винагороди. Будь ласка, відвідайте нашу сторінку [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) для отримання детальнішої інформації про активні програми.

## Бажані мови

Ми віддаємо перевагу, щоб усі повідомлення були англійською мовою.

## Політика

Microsoft дотримується принципу [Координованого розкриття вразливостей](https://aka.ms/opensource/security/cvd).

**Відмова від відповідальності**:  
Цей документ було перекладено за допомогою сервісу автоматичного перекладу [Co-op Translator](https://github.com/Azure/co-op-translator). Хоча ми прагнемо до точності, будь ласка, майте на увазі, що автоматичні переклади можуть містити помилки або неточності. Оригінальний документ рідною мовою слід вважати авторитетним джерелом. Для критично важливої інформації рекомендується звертатися до професійного людського перекладу. Ми не несемо відповідальності за будь-які непорозуміння або неправильні тлумачення, що виникли внаслідок використання цього перекладу.