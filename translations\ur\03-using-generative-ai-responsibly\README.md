<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "7f8f4c11f8c1cb6e1794442dead414ea",
  "translation_date": "2025-07-09T08:48:13+00:00",
  "source_file": "03-using-generative-ai-responsibly/README.md",
  "language_code": "ur"
}
-->
# ذمہ داری کے ساتھ Generative AI کا استعمال

[![Using Generative AI Responsibly](../../../translated_images/03-lesson-banner.1ed56067a452d97709d51f6cc8b6953918b2287132f4909ade2008c936cd4af9.ur.png)](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)

> _اس سبق کی ویڈیو دیکھنے کے لیے اوپر تصویر پر کلک کریں_

AI اور خاص طور پر Generative AI سے متاثر ہونا آسان ہے، لیکن آپ کو یہ سوچنا ہوگا کہ آپ اسے ذمہ داری کے ساتھ کیسے استعمال کریں گے۔ آپ کو یہ غور کرنا ہوگا کہ آؤٹ پٹ منصفانہ، نقصان دہ نہ ہو اور دیگر پہلوؤں کو کیسے یقینی بنایا جائے۔ یہ باب آپ کو مذکورہ سیاق و سباق، غور کرنے والی باتوں، اور AI کے استعمال کو بہتر بنانے کے لیے عملی اقدامات کے بارے میں معلومات فراہم کرنے کا مقصد رکھتا ہے۔

## تعارف

اس سبق میں شامل ہیں:

- Generative AI ایپلیکیشنز بناتے وقت Responsible AI کو ترجیح دینے کی وجوہات۔
- Responsible AI کے بنیادی اصول اور وہ Generative AI سے کیسے جُڑے ہیں۔
- ان Responsible AI اصولوں کو حکمت عملی اور ٹولز کے ذریعے عملی جامہ پہنانا۔

## سیکھنے کے مقاصد

اس سبق کو مکمل کرنے کے بعد آپ جانیں گے:

- Generative AI ایپلیکیشنز بناتے وقت Responsible AI کی اہمیت۔
- کب اور کیسے Responsible AI کے بنیادی اصولوں کو اپنانا چاہیے۔
- کون سے ٹولز اور حکمت عملیاں دستیاب ہیں تاکہ Responsible AI کے تصور کو عملی شکل دی جا سکے۔

## Responsible AI کے اصول

Generative AI کا جوش کبھی اتنا زیادہ نہیں رہا۔ اس جوش نے بہت سے نئے ڈویلپرز، توجہ، اور فنڈنگ اس میدان میں لائی ہے۔ یہ ان لوگوں کے لیے بہت مثبت ہے جو Generative AI استعمال کرتے ہوئے مصنوعات اور کمپنیاں بنانا چاہتے ہیں، لیکن یہ بھی ضروری ہے کہ ہم ذمہ داری کے ساتھ آگے بڑھیں۔

اس کورس کے دوران، ہم اپنی اسٹارٹ اپ اور AI تعلیمی پروڈکٹ بنانے پر توجہ مرکوز کر رہے ہیں۔ ہم Responsible AI کے اصول استعمال کریں گے: انصاف، شمولیت، اعتبار/حفاظت، سیکیورٹی اور پرائیویسی، شفافیت اور جوابدہی۔ ان اصولوں کے ذریعے ہم دیکھیں گے کہ یہ ہمارے مصنوعات میں Generative AI کے استعمال سے کیسے جُڑے ہیں۔

## آپ کو Responsible AI کو ترجیح کیوں دینی چاہیے

جب آپ کوئی پروڈکٹ بنا رہے ہوں، تو صارف کے بہترین مفاد کو ذہن میں رکھتے ہوئے انسان مرکزیت کا طریقہ اپنانا بہترین نتائج دیتا ہے۔

Generative AI کی خاص بات یہ ہے کہ یہ صارفین کے لیے مددگار جوابات، معلومات، رہنمائی، اور مواد تخلیق کرنے کی طاقت رکھتا ہے۔ یہ بہت کم دستی مراحل کے بغیر کیا جا سکتا ہے، جو بہت متاثر کن نتائج دے سکتا ہے۔ لیکن بغیر مناسب منصوبہ بندی اور حکمت عملی کے، یہ بدقسمتی سے آپ کے صارفین، آپ کی پروڈکٹ، اور معاشرے کے لیے نقصان دہ نتائج بھی لا سکتا ہے۔

آئیے کچھ (لیکن تمام نہیں) ممکنہ نقصان دہ نتائج پر نظر ڈالیں:

### ہیلوسینیشنز (خیالی جوابات)

ہیلوسینیشنز اس وقت کہلاتے ہیں جب کوئی LLM ایسا مواد پیدا کرتا ہے جو بالکل بے معنی ہو یا جس کے بارے میں ہمیں دوسرے ذرائع سے معلوم ہو کہ وہ غلط ہے۔

مثال کے طور پر، فرض کریں ہم نے اپنی اسٹارٹ اپ کے لیے ایک فیچر بنایا ہے جو طلباء کو تاریخی سوالات پوچھنے کی اجازت دیتا ہے۔ ایک طالب علم سوال کرتا ہے: `Titanic کا واحد زندہ بچ جانے والا کون تھا؟`

ماڈل اس طرح کا جواب دیتا ہے:

![Prompt saying "Who was the sole survivor of the Titanic"](../../../03-using-generative-ai-responsibly/images/ChatGPT-titanic-survivor-prompt.webp)

> _(ماخذ: [Flying bisons](https://flyingbisons.com?WT.mc_id=academic-105485-koreyst))_

یہ ایک بہت پراعتماد اور مکمل جواب ہے۔ بدقسمتی سے، یہ غلط ہے۔ معمولی تحقیق سے معلوم ہوتا ہے کہ Titanic حادثے میں ایک سے زیادہ زندہ بچ جانے والے تھے۔ ایک طالب علم جو اس موضوع پر تحقیق شروع کر رہا ہے، اس جواب کو بغیر سوال کیے حقیقت سمجھ سکتا ہے۔ اس کے نتائج AI سسٹم کی غیر معتبر حیثیت اور ہماری اسٹارٹ اپ کی ساکھ کو نقصان پہنچا سکتے ہیں۔

ہر LLM کی نئی نسل کے ساتھ، ہم نے ہیلوسینیشنز کو کم کرنے میں بہتری دیکھی ہے۔ اس بہتری کے باوجود، ہمیں بطور ایپلیکیشن بنانے والے اور صارفین ان حدود سے آگاہ رہنا چاہیے۔

### نقصان دہ مواد

پچھلے حصے میں ہم نے دیکھا کہ جب LLM غلط یا بے معنی جوابات دیتا ہے۔ ایک اور خطرہ یہ ہے کہ ماڈل نقصان دہ مواد فراہم کرے۔

نقصان دہ مواد کی تعریف ہو سکتی ہے:

- خود کو نقصان پہنچانے یا مخصوص گروہوں کو نقصان پہنچانے کی ہدایات یا ترغیب دینا۔
- نفرت انگیز یا توہین آمیز مواد۔
- کسی قسم کے حملے یا پرتشدد کارروائیوں کی منصوبہ بندی میں رہنمائی۔
- غیر قانونی مواد تلاش کرنے یا غیر قانونی کارروائیاں کرنے کی ہدایات۔
- جنسی طور پر واضح مواد دکھانا۔

ہماری اسٹارٹ اپ کے لیے ضروری ہے کہ ہمارے پاس ایسے ٹولز اور حکمت عملیاں ہوں جو طلباء کو اس قسم کا مواد دیکھنے سے روک سکیں۔

### انصاف کی کمی

انصاف کا مطلب ہے “یہ یقینی بنانا کہ AI سسٹم تعصب اور امتیاز سے پاک ہو اور سب کے ساتھ منصفانہ اور برابر سلوک کرے۔” Generative AI کی دنیا میں، ہم چاہتے ہیں کہ ماڈل کی آؤٹ پٹ ایسے گروہوں کے خلاف تعصبات کو مضبوط نہ کرے جو معاشرتی طور پر حاشیے پر ہوں۔

ایسی آؤٹ پٹس نہ صرف ہمارے صارفین کے لیے مثبت تجربات بنانے میں رکاوٹ بنتی ہیں بلکہ معاشرتی نقصان بھی پہنچاتی ہیں۔ بطور ایپلیکیشن بنانے والے، ہمیں ہمیشہ ایک وسیع اور متنوع صارفین کی بنیاد کو ذہن میں رکھ کر حل تیار کرنے چاہئیں۔

## Generative AI کو ذمہ داری کے ساتھ کیسے استعمال کریں

اب جب کہ ہم نے Responsible Generative AI کی اہمیت کو سمجھ لیا ہے، آئیے چار اقدامات دیکھتے ہیں جن کے ذریعے ہم اپنی AI حل ذمہ داری کے ساتھ بنا سکتے ہیں:

![Mitigate Cycle](../../../translated_images/mitigate-cycle.babcd5a5658e1775d5f2cb47f2ff305cca090400a72d98d0f9e57e9db5637c72.ur.png)

### ممکنہ نقصانات کی پیمائش کریں

سافٹ ویئر ٹیسٹنگ میں، ہم ایپلیکیشن پر صارف کے متوقع عمل کی جانچ کرتے ہیں۔ اسی طرح، صارفین کے ممکنہ مختلف پرامپٹس کی جانچ کرنا جو وہ زیادہ تر استعمال کریں گے، ممکنہ نقصان کی پیمائش کا اچھا طریقہ ہے۔

چونکہ ہماری اسٹارٹ اپ تعلیمی پروڈکٹ بنا رہی ہے، اس لیے تعلیمی موضوعات، تاریخی حقائق، اور طالب علموں کی زندگی سے متعلق پرامپٹس کی فہرست تیار کرنا مفید ہوگا۔

### ممکنہ نقصانات کو کم کریں

اب وقت ہے کہ ایسے طریقے تلاش کریں جن سے ماڈل اور اس کے جوابات کی وجہ سے ممکنہ نقصان کو روکا یا محدود کیا جا سکے۔ ہم اسے چار مختلف سطحوں پر دیکھ سکتے ہیں:

![Mitigation Layers](../../../translated_images/mitigation-layers.377215120b9a1159a8c3982c6bbcf41b6adf8c8fa04ce35cbaeeb13b4979cdfc.ur.png)

- **ماڈل**۔ درست استعمال کے لیے صحیح ماڈل کا انتخاب۔ بڑے اور پیچیدہ ماڈلز جیسے GPT-4 چھوٹے اور مخصوص استعمال کے کیسز میں نقصان دہ مواد کا زیادہ خطرہ پیدا کر سکتے ہیں۔ اپنے تربیتی ڈیٹا سے فائن ٹیوننگ کرنا بھی نقصان دہ مواد کے خطرے کو کم کرتا ہے۔

- **حفاظتی نظام**۔ حفاظتی نظام وہ ٹولز اور کنفیگریشنز کا مجموعہ ہے جو ماڈل کی میزبانی کرنے والے پلیٹ فارم پر نقصان کو کم کرنے میں مدد دیتے ہیں۔ مثال کے طور پر Azure OpenAI سروس پر مواد فلٹرنگ سسٹم۔ یہ سسٹمز جیل بریک حملوں اور غیر مطلوبہ سرگرمیوں جیسے بوٹس کی درخواستوں کا پتہ بھی لگاتے ہیں۔

- **میٹا پرامپٹ**۔ میٹا پرامپٹس اور گراؤنڈنگ وہ طریقے ہیں جن سے ہم ماڈل کو مخصوص رویوں اور معلومات کی بنیاد پر محدود یا ہدایت دے سکتے ہیں۔ یہ سسٹم ان پٹس کے ذریعے ماڈل کی حدود متعین کرنے یا آؤٹ پٹ کو سسٹم کے دائرہ کار یا موضوع سے متعلق بنانے میں مدد دیتے ہیں۔

یہ تکنیکیں Retrieval Augmented Generation (RAG) جیسی بھی ہو سکتی ہیں، جس سے ماڈل صرف منتخب شدہ معتبر ذرائع سے معلومات حاصل کرتا ہے۔ اس کورس میں بعد میں [سرچ ایپلیکیشنز بنانے](../08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst) کا سبق بھی موجود ہے۔

- **صارف کا تجربہ**۔ آخری سطح وہ ہے جہاں صارف براہ راست ہماری ایپلیکیشن کے ذریعے ماڈل کے ساتھ تعامل کرتا ہے۔ اس طرح ہم UI/UX کو اس طرح ڈیزائن کر سکتے ہیں کہ صارف کو ماڈل کو بھیجے جانے والے ان پٹ کی اقسام اور صارف کو دکھائے جانے والے متن یا تصاویر پر محدودیت ہو۔ AI ایپلیکیشن کو تعینات کرتے وقت، ہمیں یہ بھی شفاف ہونا چاہیے کہ ہماری Generative AI ایپلیکیشن کیا کر سکتی ہے اور کیا نہیں۔

ہمارے پاس AI ایپلیکیشنز کے لیے [UX ڈیزائن کرنے](../12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst) کا ایک مکمل سبق بھی ہے۔

- **ماڈل کا جائزہ لیں**۔ LLMs کے ساتھ کام کرنا چیلنجنگ ہو سکتا ہے کیونکہ ہمارے پاس ہمیشہ ماڈل کی تربیت کے لیے استعمال ہونے والے ڈیٹا پر کنٹرول نہیں ہوتا۔ اس کے باوجود، ہمیں ہمیشہ ماڈل کی کارکردگی اور آؤٹ پٹ کا جائزہ لینا چاہیے۔ ماڈل کی درستگی، مماثلت، گراؤنڈڈنس، اور آؤٹ پٹ کی مطابقت کو ماپنا ضروری ہے۔ یہ شفافیت اور اعتماد فراہم کرنے میں مدد دیتا ہے۔

### ذمہ دار Generative AI حل چلائیں

اپنی AI ایپلیکیشنز کے گرد ایک عملی طریقہ کار بنانا آخری مرحلہ ہے۔ اس میں ہماری اسٹارٹ اپ کے دیگر شعبوں جیسے قانونی اور سیکیورٹی کے ساتھ شراکت داری شامل ہے تاکہ ہم تمام ضابطہ کاری پالیسیوں کی پابندی کریں۔ لانچ سے پہلے، ہمیں ترسیل، حادثات کے انتظام، اور رول بیک کے منصوبے بھی بنانے چاہئیں تاکہ صارفین کو نقصان سے بچایا جا سکے۔

## ٹولز

اگرچہ Responsible AI حل تیار کرنا بظاہر بہت کام لگتا ہے، لیکن یہ محنت قابل قدر ہے۔ جیسے جیسے Generative AI کا میدان بڑھ رہا ہے، ایسے ٹولز بھی تیار ہو رہے ہیں جو ڈویلپرز کو ذمہ داری کو اپنے کام کے بہاؤ میں مؤثر طریقے سے شامل کرنے میں مدد دیتے ہیں۔ مثال کے طور پر، [Azure AI Content Safety](https://learn.microsoft.com/azure/ai-services/content-safety/overview?WT.mc_id=academic-105485-koreyst) API کے ذریعے نقصان دہ مواد اور تصاویر کا پتہ لگا سکتا ہے۔

## علم کی جانچ

ذمہ دار AI کے استعمال کو یقینی بنانے کے لیے آپ کو کن باتوں کا خیال رکھنا چاہیے؟

1. جواب درست ہو۔
1. نقصان دہ استعمال، یعنی AI کو مجرمانہ مقاصد کے لیے استعمال نہ کیا جائے۔
1. یہ یقینی بنانا کہ AI تعصب اور امتیاز سے پاک ہو۔

جواب: 2 اور 3 درست ہیں۔ Responsible AI آپ کو نقصان دہ اثرات، تعصبات اور دیگر مسائل کو کم کرنے کے بارے میں سوچنے میں مدد دیتا ہے۔

## 🚀 چیلنج

[Azure AI Content Safety](https://learn.microsoft.com/azure/ai-services/content-safety/overview?WT.mc_id=academic-105485-koreyst) کے بارے میں پڑھیں اور دیکھیں کہ آپ اپنی ضروریات کے لیے کیا اپنا سکتے ہیں۔

## شاباش، اپنی تعلیم جاری رکھیں

اس سبق کو مکمل کرنے کے بعد، ہمارے [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) کو دیکھیں تاکہ آپ اپنی Generative AI کی معلومات کو مزید بڑھا سکیں!

سبق 4 پر جائیں جہاں ہم [Prompt Engineering Fundamentals](../04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst) کا جائزہ لیں گے!

**دستخطی نوٹ**:  
یہ دستاویز AI ترجمہ سروس [Co-op Translator](https://github.com/Azure/co-op-translator) کے ذریعے ترجمہ کی گئی ہے۔ اگرچہ ہم درستگی کے لیے کوشاں ہیں، براہ کرم آگاہ رہیں کہ خودکار ترجمے میں غلطیاں یا عدم درستیاں ہو سکتی ہیں۔ اصل دستاویز اپنی مادری زبان میں معتبر ماخذ سمجھی جانی چاہیے۔ اہم معلومات کے لیے پیشہ ور انسانی ترجمہ کی سفارش کی جاتی ہے۔ اس ترجمے کے استعمال سے پیدا ہونے والی کسی بھی غلط فہمی یا غلط تشریح کی ذمہ داری ہم پر عائد نہیں ہوتی۔