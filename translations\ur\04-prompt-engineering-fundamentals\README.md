<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "a45c318dc6ebc2604f35b8b829f93af2",
  "translation_date": "2025-07-09T09:27:24+00:00",
  "source_file": "04-prompt-engineering-fundamentals/README.md",
  "language_code": "ur"
}
-->
# پرامپٹ انجینئرنگ کے بنیادی اصول

[![Prompt Engineering Fundamentals](../../../translated_images/04-lesson-banner.a2c90deba7fedacda69f35b41636a8951ec91c2e33f5420b1254534ac85bc18e.ur.png)](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)

## تعارف  
یہ ماڈیول جنریٹو AI ماڈلز میں مؤثر پرامپٹس بنانے کے لیے بنیادی تصورات اور تکنیکوں کا احاطہ کرتا ہے۔ آپ کا LLM کو پرامپٹ لکھنے کا طریقہ بھی اہمیت رکھتا ہے۔ ایک سوچ سمجھ کر تیار کیا گیا پرامپٹ بہتر معیار کا جواب حاصل کر سکتا ہے۔ لیکن _پرامپٹ_ اور _پرامپٹ انجینئرنگ_ جیسے اصطلاحات کا مطلب کیا ہے؟ اور میں LLM کو بھیجے جانے والے پرامپٹ _ان پٹ_ کو کیسے بہتر بنا سکتا ہوں؟ یہ وہ سوالات ہیں جن کے جواب ہم اس باب اور اگلے میں تلاش کریں گے۔

_جنریٹو AI_ صارف کی درخواستوں کے جواب میں نئی مواد (مثلاً متن، تصاویر، آڈیو، کوڈ وغیرہ) تخلیق کرنے کی صلاحیت رکھتا ہے۔ یہ کام _بڑے زبان کے ماڈلز_ جیسے OpenAI کے GPT ("Generative Pre-trained Transformer") سیریز کے ذریعے انجام دیتا ہے جو قدرتی زبان اور کوڈ کے استعمال کے لیے تربیت یافتہ ہوتے ہیں۔

اب صارفین ان ماڈلز کے ساتھ چیٹ جیسے معروف طریقوں سے بات چیت کر سکتے ہیں، بغیر کسی تکنیکی مہارت یا تربیت کے۔ یہ ماڈلز _پرامپٹ پر مبنی_ ہوتے ہیں — صارف ایک متن (پرامپٹ) بھیجتا ہے اور AI کا جواب (تکمیل) حاصل کرتا ہے۔ پھر وہ "AI سے بات چیت" کر کے، کئی بار پرامپٹ کو بہتر بناتے ہوئے، اس وقت تک جواب کو بہتر بنا سکتے ہیں جب تک وہ اپنی توقعات پر پورا نہ اترے۔

"پرامپٹس" اب جنریٹو AI ایپس کے لیے بنیادی _پروگرامنگ انٹرفیس_ بن چکے ہیں، جو ماڈلز کو بتاتے ہیں کہ کیا کرنا ہے اور واپس آنے والے جوابات کے معیار کو متاثر کرتے ہیں۔ "پرامپٹ انجینئرنگ" ایک تیزی سے بڑھتا ہوا شعبہ ہے جو پرامپٹس کے _ڈیزائن اور اصلاح_ پر توجہ دیتا ہے تاکہ بڑے پیمانے پر مستقل اور معیاری جوابات فراہم کیے جا سکیں۔

## سیکھنے کے مقاصد

اس سبق میں، ہم جانیں گے کہ پرامپٹ انجینئرنگ کیا ہے، یہ کیوں اہم ہے، اور ہم کس طرح دیے گئے ماڈل اور ایپلیکیشن کے مقصد کے لیے زیادہ مؤثر پرامپٹس تیار کر سکتے ہیں۔ ہم پرامپٹ انجینئرنگ کے بنیادی تصورات اور بہترین طریقے سمجھیں گے — اور ایک انٹرایکٹو Jupyter Notebooks "sandbox" ماحول کے بارے میں جانیں گے جہاں ہم ان تصورات کو حقیقی مثالوں پر لاگو ہوتے دیکھ سکتے ہیں۔

اس سبق کے آخر تک ہم قابل ہوں گے:

1. پرامپٹ انجینئرنگ کیا ہے اور یہ کیوں اہم ہے، بیان کریں۔
2. پرامپٹ کے اجزاء کی وضاحت کریں اور یہ کیسے استعمال ہوتے ہیں۔
3. پرامپٹ انجینئرنگ کے بہترین طریقے اور تکنیکیں سیکھیں۔
4. سیکھی ہوئی تکنیکوں کو حقیقی مثالوں پر OpenAI endpoint کے ذریعے لاگو کریں۔

## اہم اصطلاحات

پرامپٹ انجینئرنگ: AI ماڈلز کو مطلوبہ نتائج پیدا کرنے کی رہنمائی کے لیے ان پٹ کو ڈیزائن اور بہتر بنانے کا عمل۔  
ٹوکینائزیشن: متن کو چھوٹے حصوں (ٹوکینز) میں تبدیل کرنے کا عمل، جنہیں ماڈل سمجھ سکتا ہے اور پراسیس کر سکتا ہے۔  
انسٹرکشن-ٹیونڈ LLMs: بڑے زبان کے ماڈلز جو مخصوص ہدایات کے ساتھ بہتر ردعمل کے لیے فائن ٹیون کیے گئے ہیں۔

## سیکھنے کا سینڈباکس

پرامپٹ انجینئرنگ فی الحال زیادہ فن ہے بجائے سائنس کے۔ اس میں مہارت حاصل کرنے کا بہترین طریقہ _زیادہ مشق_ کرنا اور آزمائش و خطا کا طریقہ اپنانا ہے جو ایپلیکیشن کے شعبے کی مہارت کو تجویز کردہ تکنیکوں اور ماڈل کی مخصوص اصلاحات کے ساتھ ملاتا ہے۔

اس سبق کے ساتھ دیا گیا Jupyter Notebook ایک _سینڈباکس_ ماحول فراہم کرتا ہے جہاں آپ جو کچھ سیکھتے ہیں اسے آزما سکتے ہیں — چاہے راستے میں یا آخر میں کوڈ چیلنج کے طور پر۔ مشقیں کرنے کے لیے آپ کو چاہیے:

1. **Azure OpenAI API کی کلید** — تعینات شدہ LLM کے لیے سروس اینڈپوائنٹ۔  
2. **Python رن ٹائم** — جس میں نوٹ بک چلائی جا سکے۔  
3. **مقامی ماحول کے متغیرات** — _ابھی [SETUP](./../00-course-setup/SETUP.md?WT.mc_id=academic-105485-koreyst) کے مراحل مکمل کریں تاکہ تیار ہو جائیں_۔

نوٹ بک کے ساتھ _ابتدائی_ مشقیں آتی ہیں — لیکن آپ کو ترغیب دی جاتی ہے کہ اپنی _Markdown_ (تفصیل) اور _Code_ (پرامپٹ درخواستیں) شامل کریں تاکہ مزید مثالیں یا خیالات آزما سکیں — اور پرامپٹ ڈیزائن کے لیے اپنی سمجھ بوجھ بڑھا سکیں۔

## تصویری رہنمائی

کیا آپ اس سبق کا مجموعی خاکہ جاننا چاہتے ہیں اس میں غوطہ لگانے سے پہلے؟ اس تصویری رہنمائی کو دیکھیں، جو آپ کو اہم موضوعات اور ہر ایک کے لیے کلیدی نکات کا اندازہ دیتی ہے جن پر آپ غور کر سکتے ہیں۔ سبق کا روڈ میپ آپ کو بنیادی تصورات اور چیلنجز کو سمجھنے سے لے کر متعلقہ پرامپٹ انجینئرنگ تکنیکوں اور بہترین طریقوں کے ذریعے ان کا حل کرنے تک لے جاتا ہے۔ نوٹ کریں کہ اس رہنمائی میں "ایڈوانسڈ تکنیکس" کا حصہ اس نصاب کے _اگلے_ باب میں شامل مواد کی طرف اشارہ کرتا ہے۔

![Illustrated Guide to Prompt Engineering](../../../translated_images/04-prompt-engineering-sketchnote.d5f33336957a1e4f623b826195c2146ef4cc49974b72fa373de6929b474e8b70.ur.png)

## ہمارا اسٹارٹ اپ

اب، آئیے بات کرتے ہیں کہ _یہ موضوع_ ہمارے اسٹارٹ اپ کے مشن سے کیسے جُڑا ہے جو [تعلیم میں AI کی جدت لانے](https://educationblog.microsoft.com/2023/06/collaborating-to-bring-ai-innovation-to-education?WT.mc_id=academic-105485-koreyst) کے لیے ہے۔ ہم _ذاتی نوعیت کی تعلیم_ کی AI سے چلنے والی ایپلیکیشنز بنانا چاہتے ہیں — تو آئیے سوچیں کہ ہمارے ایپلیکیشن کے مختلف صارفین پرامپٹس کو کیسے "ڈیزائن" کر سکتے ہیں:

- **ایڈمنسٹریٹرز** AI سے کہہ سکتے ہیں کہ _نصاب کے ڈیٹا کا تجزیہ کرے تاکہ کورنگ میں خالی جگہوں کی نشاندہی ہو سکے۔_ AI نتائج کا خلاصہ کر سکتا ہے یا کوڈ کے ذریعے انہیں بصری شکل دے سکتا ہے۔  
- **اساتذہ** AI سے کہہ سکتے ہیں کہ _مخصوص سامعین اور موضوع کے لیے ایک سبق کا منصوبہ تیار کرے۔_ AI ذاتی نوعیت کا منصوبہ مخصوص فارمیٹ میں بنا سکتا ہے۔  
- **طلباء** AI سے کہہ سکتے ہیں کہ _مشکل مضمون میں ان کی رہنمائی کرے۔_ AI اب طلباء کو ان کی سطح کے مطابق اسباق، اشارے اور مثالیں دے سکتا ہے۔

یہ تو محض ابتدا ہے۔ [Prompts For Education](https://github.com/microsoft/prompts-for-edu/tree/main?WT.mc_id=academic-105485-koreyst) دیکھیں — ایک اوپن سورس پرامپٹس لائبریری جو تعلیمی ماہرین نے تیار کی ہے — تاکہ امکانات کا وسیع تر اندازہ ہو سکے! _کوشش کریں کہ ان پرامپٹس کو سینڈباکس میں یا OpenAI Playground میں چلائیں اور دیکھیں کیا ہوتا ہے!_

<!--  
LESSON TEMPLATE:  
This unit should cover core concept #1.  
Reinforce the concept with examples and references.

CONCEPT #1:  
Prompt Engineering.  
Define it and explain why it is needed.  
-->

## پرامپٹ انجینئرنگ کیا ہے؟

ہم نے اس سبق کا آغاز **پرامپٹ انجینئرنگ** کی تعریف سے کیا کہ یہ ایک ایسا عمل ہے جس میں متن کے ان پٹ (پرامپٹس) کو _ڈیزائن اور بہتر_ کیا جاتا ہے تاکہ دیے گئے ایپلیکیشن کے مقصد اور ماڈل کے لیے مستقل اور معیاری جوابات (تکمیل) فراہم کیے جا سکیں۔ ہم اسے دو مرحلوں کے عمل کے طور پر سوچ سکتے ہیں:

- دیے گئے ماڈل اور مقصد کے لیے ابتدائی پرامپٹ _ڈیزائن_ کرنا  
- جواب کے معیار کو بہتر بنانے کے لیے پرامپٹ کو بار بار _بہتر_ کرنا  

یہ لازمی طور پر ایک آزمائش و خطا کا عمل ہے جس کے لیے صارف کی سمجھ بوجھ اور محنت درکار ہوتی ہے تاکہ بہترین نتائج حاصل ہوں۔ تو یہ کیوں اہم ہے؟ اس سوال کا جواب دینے کے لیے ہمیں پہلے تین تصورات سمجھنے ہوں گے:

- _ٹوکینائزیشن_ = ماڈل پرامپٹ کو کیسے "دیکھتا" ہے  
- _بیس LLMs_ = بنیادی ماڈل پرامپٹ کو کیسے "پروسیس" کرتا ہے  
- _انسٹرکشن-ٹیونڈ LLMs_ = ماڈل اب "کاموں" کو کیسے سمجھ سکتا ہے  

### ٹوکینائزیشن

ایک LLM پرامپٹس کو _ٹوکینز کے سلسلے_ کے طور پر دیکھتا ہے جہاں مختلف ماڈلز (یا ماڈل کے ورژنز) ایک ہی پرامپٹ کو مختلف طریقوں سے ٹوکینائز کر سکتے ہیں۔ چونکہ LLMs کو ٹوکینز پر تربیت دی جاتی ہے (خام متن پر نہیں)، اس لیے پرامپٹ کے ٹوکینائز ہونے کا طریقہ پیدا ہونے والے جواب کے معیار پر براہ راست اثر ڈالتا ہے۔

ٹوکینائزیشن کا اندازہ لگانے کے لیے، [OpenAI Tokenizer](https://platform.openai.com/tokenizer?WT.mc_id=academic-105485-koreyst) جیسے ٹولز آزمائیں۔ اپنا پرامپٹ کاپی کریں — اور دیکھیں کہ یہ ٹوکینز میں کیسے تبدیل ہوتا ہے، خاص طور پر خالی جگہوں اور رموزِ اوقاف کے ہینڈلنگ پر دھیان دیں۔ نوٹ کریں کہ یہ مثال ایک پرانے LLM (GPT-3) کی ہے — اس لیے نئے ماڈل کے ساتھ آزمانے پر مختلف نتیجہ آ سکتا ہے۔

![Tokenization](../../../translated_images/04-tokenizer-example.e71f0a0f70356c5c7d80b21e8753a28c18a7f6d4aaa1c4b08e65d17625e85642.ur.png)

### تصور: بنیادی ماڈلز

ایک بار پرامپٹ ٹوکینائز ہو جائے، تو ["Base LLM"](https://blog.gopenai.com/an-introduction-to-base-and-instruction-tuned-large-language-models-8de102c785a6?WT.mc_id=academic-105485-koreyst) (یا بنیادی ماڈل) کا بنیادی کام اس سلسلے میں اگلے ٹوکین کی پیش گوئی کرنا ہوتا ہے۔ چونکہ LLMs کو وسیع متن کے ڈیٹا سیٹس پر تربیت دی گئی ہے، انہیں ٹوکینز کے درمیان شماریاتی تعلقات کا اچھا اندازہ ہوتا ہے اور وہ اعتماد کے ساتھ پیش گوئی کر سکتے ہیں۔ یاد رکھیں کہ وہ پرامپٹ یا ٹوکین کے الفاظ کا _معنی_ نہیں سمجھتے؛ وہ صرف ایک پیٹرن دیکھتے ہیں جسے وہ اپنی اگلی پیش گوئی سے "مکمل" کر سکتے ہیں۔ وہ اس سلسلے کی پیش گوئی جاری رکھ سکتے ہیں جب تک صارف مداخلت نہ کرے یا کوئی پہلے سے طے شدہ شرط پوری نہ ہو۔

کیا آپ دیکھنا چاہتے ہیں کہ پرامپٹ پر مبنی تکمیل کیسے کام کرتی ہے؟ اوپر دیا گیا پرامپٹ Azure OpenAI Studio کے [_Chat Playground_](https://oai.azure.com/playground?WT.mc_id=academic-105485-koreyst) میں ڈیفالٹ سیٹنگز کے ساتھ داخل کریں۔ نظام پرامپٹس کو معلومات کی درخواست کے طور پر سمجھتا ہے — لہٰذا آپ کو ایسا جواب ملنا چاہیے جو اس سیاق و سباق کو پورا کرے۔

لیکن اگر صارف کچھ خاص دیکھنا چاہتا ہو جو کسی معیار یا کام کے مقصد پر پورا اترتا ہو؟ تب _انسٹرکشن-ٹیونڈ_ LLMs کا کردار آتا ہے۔

![Base LLM Chat Completion](../../../translated_images/04-playground-chat-base.65b76fcfde0caa6738e41d20f1a6123f9078219e6f91a88ee5ea8014f0469bdf.ur.png)

### تصور: انسٹرکشن-ٹیونڈ LLMs

ایک [انسٹرکشن-ٹیونڈ LLM](https://blog.gopenai.com/an-introduction-to-base-and-instruction-tuned-large-language-models-8de102c785a6?WT.mc_id=academic-105485-koreyst) بنیادی ماڈل سے شروع ہوتا ہے اور اسے مثالوں یا ان پٹ/آؤٹ پٹ جوڑوں (مثلاً، کثیر مرحلہ "پیغامات") کے ساتھ فائن ٹیون کرتا ہے جن میں واضح ہدایات ہو سکتی ہیں — اور AI کا جواب ان ہدایات کی پیروی کرنے کی کوشش کرتا ہے۔

یہ Reinforcement Learning with Human Feedback (RLHF) جیسی تکنیکوں کا استعمال کرتا ہے جو ماڈل کو _ہدایات پر عمل کرنے_ اور _فیڈبیک سے سیکھنے_ کی تربیت دیتی ہیں تاکہ وہ ایسے جوابات دے جو عملی استعمال کے لیے زیادہ موزوں اور صارف کے مقاصد کے مطابق ہوں۔

آئیے اسے آزمائیں — اوپر والے پرامپٹ پر واپس جائیں، لیکن اب _سسٹم میسج_ کو تبدیل کر کے درج ذیل ہدایت بطور سیاق و سباق فراہم کریں:

> _آپ کو دی گئی معلومات کو دوسرے درجے کے طالب علم کے لیے خلاصہ کریں۔ نتیجہ ایک پیراگراف میں 3-5 بلٹ پوائنٹس تک محدود رکھیں۔_

دیکھیں کہ نتیجہ اب مطلوبہ مقصد اور فارمیٹ کے مطابق ڈھل گیا ہے؟ ایک استاد اب اس جواب کو اپنی کلاس کے سلائیڈز میں براہ راست استعمال کر سکتا ہے۔

![Instruction Tuned LLM Chat Completion](../../../translated_images/04-playground-chat-instructions.b30bbfbdf92f2d051639c9bc23f74a0e2482f8dc7f0dafc6cc6fda81b2b00534.ur.png)

## ہمیں پرامپٹ انجینئرنگ کی ضرورت کیوں ہے؟

اب جب کہ ہم جانتے ہیں کہ LLMs پرامپٹس کو کیسے پراسیس کرتے ہیں، آئیے بات کرتے ہیں کہ _ہمیں پرامپٹ انجینئرنگ کی ضرورت کیوں ہے۔_ اس کا جواب اس حقیقت میں مضمر ہے کہ موجودہ LLMs کئی چیلنجز پیش کرتے ہیں جو _قابل اعتماد اور مستقل تکمیل_ حاصل کرنا مشکل بنا دیتے ہیں اگر پرامپٹ کی تعمیر اور اصلاح پر محنت نہ کی جائے۔ مثلاً:

1. **ماڈل کے جوابات تصادفی ہوتے ہیں۔** _ایک ہی پرامپٹ_ مختلف ماڈلز یا ماڈل کے ورژنز کے ساتھ مختلف جوابات دے سکتا ہے۔ اور یہ ایک ہی ماڈل کے ساتھ مختلف اوقات میں بھی مختلف نتائج دے سکتا ہے۔ _پرامپٹ انجینئرنگ تکنیکیں ان تغیرات کو کم کرنے میں مدد دیتی ہیں بہتر گارڈ ریلز فراہم کر کے۔_

2. **ماڈلز جوابات گھڑ سکتے ہیں۔** ماڈلز کو _بڑے مگر محدود_ ڈیٹا سیٹس پر تربیت دی جاتی ہے، جس کا مطلب ہے کہ انہیں تربیت کے دائرہ کار سے باہر کے تصورات کا علم نہیں ہوتا۔ نتیجتاً، وہ ایسے جوابات دے سکتے ہیں جو غلط، خیالی، یا معروف حقائق کے خلاف ہوں۔ _پرامپٹ انجینئرنگ تکنیکیں صارفین کو ایسی گھڑتوں کی نشاندہی اور ان پر قابو پانے میں مدد دیتی ہیں، مثلاً AI سے حوالہ جات یا منطق طلب کر کے۔_

3. **ماڈلز کی صلاحیتیں مختلف ہوں گی۔** نئے ماڈلز یا ماڈل کی نسلیں زیادہ صلاحیتیں رکھتی ہیں لیکن ان کے ساتھ منفرد خامیاں اور لاگت و پیچیدگی کے مسائل بھی آتے ہیں۔ _پرامپٹ انجینئرنگ ہمیں بہترین طریقے اور ورک فلو تیار کرنے میں مدد دیتی ہے جو فرق کو چھپاتے ہیں اور ماڈل کی مخصوص ضروریات کے مطابق بڑے پیمانے پر اور آسانی سے ڈھل جاتے ہیں۔_

آئیے اسے OpenAI یا Azure OpenAI Playground میں عملی طور پر دیکھیں:

- مختلف LLM تعیناتیوں (مثلاً OpenAI، Azure OpenAI، Hugging Face) کے ساتھ ایک ہی پرامپٹ استعمال کریں — کیا آپ نے فرق محسوس کیا؟  
- ایک ہی LLM تعیناتی (مثلاً Azure OpenAI playground) کے ساتھ بار بار ایک ہی پرامپٹ استعمال کریں — یہ فرق کیسے مختلف تھے؟  

### گھڑتوں کی مثال

اس کورس میں، ہم اصطلاح **"fabrication"** استعمال کرتے ہیں تاکہ اس واقعے کی نشاندہی کی جا سکے جہاں LLMs اپنی تربیت یا دیگر حدود کی وجہ سے بعض اوقات حقائق کے خلاف معلومات پیدا کرتے ہیں۔ آپ نے اسے مقبول مضامین یا تحقیقی مقالوں میں _"hallucinations"_ کے طور پر بھی سنا ہوگا۔ تاہم، ہم سختی سے سفارش کرتے ہیں کہ _"fabrication"_ کا استعمال کریں تاکہ ہم غلطی سے اس رویے کو انسانی خصوصیت نہ دے دیں، جو ایک مشین کی پیداوار ہے۔ یہ [Responsible AI guidelines](https://www.microsoft.com/ai/responsible-ai?WT.mc_id=academic-105485-koreyst) کی اصطلاحات کے حوالے سے بھی حمایت کرتا ہے، اور ایسے الفاظ کو ہٹاتا ہے جو بعض سیاق و سباق میں توہین آمیز یا غیر شمولیتی سمجھے جا سکتے ہیں۔

کیا آپ جاننا چاہتے ہیں کہ گھڑتیں کیسے کام کرتی ہیں؟ ایک ایسا پرامپٹ سوچیں جو AI کو غیر موجود موضوع کے لیے مواد تیار کرنے کی ہدایت دیتا ہے (تاکہ یہ تربیتی ڈیٹا سیٹ میں نہ ملے)۔ مثال کے
# 2076 کی مریخی جنگ پر سبق کا منصوبہ

## تعارف
2076 کی مریخی جنگ ایک اہم تاریخی واقعہ ہے جس نے انسانیت اور مریخ کے باشندوں کے تعلقات کو بدل کر رکھ دیا۔ اس سبق میں ہم اس جنگ کے اسباب، اہم واقعات، اور اس کے نتائج کا جائزہ لیں گے۔

## سبق کے مقاصد
- طلباء کو 2076 کی مریخی جنگ کی وجوہات اور پس منظر سے آگاہ کرنا  
- جنگ کے دوران پیش آنے والے اہم واقعات کی تفہیم  
- جنگ کے اثرات اور اس کے بعد کے حالات پر غور و فکر کرنا  

## سبق کا خاکہ

### 1. پس منظر  
- مریخ کی آبادکاری اور انسانی مداخلت  
- وسائل کی کمی اور کشیدگی کی ابتدا  

### 2. جنگ کے اسباب  
- سیاسی اور معاشی تنازعات  
- ثقافتی اختلافات اور غلط فہمیاں  

### 3. اہم واقعات  
- جنگ کا آغاز اور پہلی جھڑپیں  
- کلیدی معرکے اور ان کے نتائج  
- جنگ میں استعمال ہونے والی ٹیکنالوجی  

### 4. جنگ کے نتائج  
- انسانی اور مریخی معاشروں پر اثرات  
- امن معاہدے اور مستقبل کے امکانات  

## سرگرمیاں  
- گروپ ڈسکشن: جنگ کے اسباب پر تبادلہ خیال  
- نقشہ سازی: جنگ کے اہم مقامات کی نشاندہی  
- تخلیقی تحریر: جنگ کے بعد کے منظرنامے پر مضمون  

## جائزہ  
سبق کے آخر میں ایک مختصر کوئز لیا جائے گا تاکہ طلباء کی سمجھ بوجھ کا اندازہ لگایا جا سکے۔

## اضافی وسائل  
- متعلقہ دستاویزی فلمیں اور مضامین  
- آن لائن آرکائیوز اور تحقیقی مواد  

---

**نوٹ:** اس سبق کا مقصد طلباء کو تاریخی واقعات کی گہرائی میں لے جانا اور ان کے تجزیاتی سوچ کو فروغ دینا ہے۔
ایک ویب سرچ نے مجھے دکھایا کہ مریخی جنگوں پر خیالی کہانیاں (مثلاً ٹیلی ویژن سیریز یا کتابیں) موجود ہیں — لیکن 2076 میں کوئی نہیں۔ عام فہم بھی ہمیں بتاتی ہے کہ 2076 _مستقبل_ میں ہے اور اس لیے اسے کسی حقیقی واقعے سے منسوب نہیں کیا جا سکتا۔

تو جب ہم یہ پرامپٹ مختلف LLM فراہم کنندگان کے ساتھ چلائیں تو کیا ہوتا ہے؟

> **Response 1**: OpenAI Playground (GPT-35)

![Response 1](../../../translated_images/04-fabrication-oai.5818c4e0b2a2678c40e0793bf873ef4a425350dd0063a183fb8ae02cae63aa0c.ur.png)

> **Response 2**: Azure OpenAI Playground (GPT-35)

![Response 2](../../../translated_images/04-fabrication-aoai.b14268e9ecf25caf613b7d424c16e2a0dc5b578f8f960c0c04d4fb3a68e6cf61.ur.png)

> **Response 3**: : Hugging Face Chat Playground (LLama-2)

![Response 3](../../../translated_images/04-fabrication-huggingchat.faf82a0a512789565e410568bce1ac911075b943dec59b1ef4080b61723b5bf4.ur.png)

جیسا کہ توقع تھی، ہر ماڈل (یا ماڈل کے ورژن) تھوڑا مختلف جواب دیتا ہے، جس کی وجہ اس کے تصادفی رویے اور ماڈل کی صلاحیتوں میں فرق ہے۔ مثال کے طور پر، ایک ماڈل آٹھویں جماعت کے طلبہ کو مخاطب کرتا ہے جبکہ دوسرا ہائی اسکول کے طالب علم کو فرض کرتا ہے۔ لیکن تینوں ماڈلز نے ایسے جوابات دیے جو ایک غیر مطلع صارف کو قائل کر سکتے ہیں کہ یہ واقعہ حقیقی تھا۔

پرامپٹ انجینئرنگ کی تکنیکیں جیسے _metaprompting_ اور _temperature configuration_ ماڈل کی تخلیقات کو کچھ حد تک کم کر سکتی ہیں۔ نئی پرامپٹ انجینئرنگ _architectures_ بھی نئے اوزار اور تکنیکیں بغیر رکاوٹ کے پرامپٹ کے بہاؤ میں شامل کرتی ہیں، تاکہ ان اثرات کو کم یا روک سکیں۔

## کیس اسٹڈی: GitHub Copilot

آئیے اس سیکشن کو ختم کرتے ہیں اور دیکھتے ہیں کہ پرامپٹ انجینئرنگ حقیقی دنیا کے حل میں کیسے استعمال ہوتی ہے، ایک کیس اسٹڈی کے ذریعے: [GitHub Copilot](https://github.com/features/copilot?WT.mc_id=academic-105485-koreyst)۔

GitHub Copilot آپ کا "AI جوڑی پروگرامر" ہے — یہ ٹیکسٹ پرامپٹس کو کوڈ مکمل کرنے میں تبدیل کرتا ہے اور آپ کے ترقیاتی ماحول (مثلاً Visual Studio Code) میں مربوط ہوتا ہے تاکہ صارف کو آسان تجربہ فراہم کرے۔ نیچے دی گئی بلاگز کی سیریز میں دستاویزی طور پر بتایا گیا ہے کہ ابتدائی ورژن OpenAI Codex ماڈل پر مبنی تھا — انجینئرز نے جلد ہی ماڈل کو بہتر بنانے اور پرامپٹ انجینئرنگ کی تکنیکوں کو ترقی دینے کی ضرورت محسوس کی تاکہ کوڈ کی کوالٹی بہتر ہو۔ جولائی میں، انہوں نے [ایک بہتر AI ماڈل متعارف کرایا جو Codex سے آگے جاتا ہے](https://github.blog/2023-07-28-smarter-more-efficient-coding-github-copilot-goes-beyond-codex-with-improved-ai-model/?WT.mc_id=academic-105485-koreyst) تاکہ تجاویز اور بھی تیز ہوں۔

ان کے سیکھنے کے سفر کو سمجھنے کے لیے بلاگز کو ترتیب سے پڑھیں۔

- **مئی 2023** | [GitHub Copilot آپ کے کوڈ کو سمجھنے میں بہتر ہو رہا ہے](https://github.blog/2023-05-17-how-github-copilot-is-getting-better-at-understanding-your-code/?WT.mc_id=academic-105485-koreyst)
- **مئی 2023** | [GitHub کے اندر: GitHub Copilot کے پیچھے LLMs کے ساتھ کام کرنا](https://github.blog/2023-05-17-inside-github-working-with-the-llms-behind-github-copilot/?WT.mc_id=academic-105485-koreyst)
- **جون 2023** | [GitHub Copilot کے لیے بہتر پرامپٹس کیسے لکھیں](https://github.blog/2023-06-20-how-to-write-better-prompts-for-github-copilot/?WT.mc_id=academic-105485-koreyst)
- **جولائی 2023** | [.. GitHub Copilot بہتر AI ماڈل کے ساتھ Codex سے آگے جاتا ہے](https://github.blog/2023-07-28-smarter-more-efficient-coding-github-copilot-goes-beyond-codex-with-improved-ai-model/?WT.mc_id=academic-105485-koreyst)
- **جولائی 2023** | [پرامپٹ انجینئرنگ اور LLMs کے لیے ڈویلپر کا رہنما](https://github.blog/2023-07-17-prompt-engineering-guide-generative-ai-llms/?WT.mc_id=academic-105485-koreyst)
- **ستمبر 2023** | [انٹرپرائز LLM ایپ کیسے بنائیں: GitHub Copilot سے سبق](https://github.blog/2023-09-06-how-to-build-an-enterprise-llm-application-lessons-from-github-copilot/?WT.mc_id=academic-105485-koreyst)

آپ ان کے [انجینئرنگ بلاگ](https://github.blog/category/engineering/?WT.mc_id=academic-105485-koreyst) میں بھی مزید پوسٹس دیکھ سکتے ہیں، جیسے [یہاں](https://github.blog/2023-09-27-how-i-used-github-copilot-chat-to-build-a-reactjs-gallery-prototype/?WT.mc_id=academic-105485-koreyst) جو دکھاتی ہیں کہ یہ ماڈلز اور تکنیکیں حقیقی دنیا کی ایپلیکیشنز چلانے کے لیے کیسے _لاگو_ کی جاتی ہیں۔

---

<!--
LESSON TEMPLATE:
یہ یونٹ بنیادی تصور #2 کو کور کرے گا۔
مثالوں اور حوالہ جات کے ساتھ تصور کو مضبوط کریں۔

CONCEPT #2:
پرامپٹ ڈیزائن۔
مثالوں کے ساتھ وضاحت۔
-->

## پرامپٹ کی تشکیل

ہم نے دیکھا کہ پرامپٹ انجینئرنگ کیوں اہم ہے — اب سمجھتے ہیں کہ پرامپٹس کیسے _تشکیل_ دیے جاتے ہیں تاکہ ہم مختلف تکنیکوں کا جائزہ لے سکیں اور مؤثر پرامپٹ ڈیزائن کر سکیں۔

### بنیادی پرامپٹ

آئیے بنیادی پرامپٹ سے شروع کرتے ہیں: ایک ٹیکسٹ ان پٹ جو ماڈل کو بغیر کسی اضافی سیاق و سباق کے بھیجی جاتی ہے۔ مثال کے طور پر، جب ہم امریکی قومی ترانہ کے پہلے چند الفاظ OpenAI [Completion API](https://platform.openai.com/docs/api-reference/completions?WT.mc_id=academic-105485-koreyst) کو بھیجتے ہیں تو یہ فوراً اگلی چند لائنیں مکمل کر دیتا ہے، جو بنیادی پیش گوئی کے رویے کی وضاحت کرتا ہے۔

| پرامپٹ (ان پٹ)       | مکمل جواب (آؤٹ پٹ)                                                                                                                        |
| :----------------- | :----------------------------------------------------------------------------------------------------------------------------------------- |
| Oh say can you see | ایسا لگتا ہے کہ آپ "The Star-Spangled Banner" کے بول شروع کر رہے ہیں، جو امریکہ کا قومی ترانہ ہے۔ مکمل بول یہ ہیں ... |

### پیچیدہ پرامپٹ

اب اس بنیادی پرامپٹ میں سیاق و سباق اور ہدایات شامل کرتے ہیں۔ [Chat Completion API](https://learn.microsoft.com/azure/ai-services/openai/how-to/chatgpt?WT.mc_id=academic-105485-koreyst) ہمیں ایک پیچیدہ پرامپٹ بنانے دیتا ہے جو _پیغامات_ کے مجموعے پر مشتمل ہوتا ہے:

- ان پٹ/آؤٹ پٹ جوڑے جو _صارف_ کی ان پٹ اور _اسسٹنٹ_ کے جواب کی عکاسی کرتے ہیں۔
- سسٹم پیغام جو اسسٹنٹ کے رویے یا شخصیت کے لیے سیاق و سباق طے کرتا ہے۔

اب درخواست نیچے دیے گئے فارم میں ہوتی ہے، جہاں _ٹوکینائزیشن_ مؤثر طریقے سے سیاق و سباق اور گفتگو سے متعلق معلومات کو پکڑتی ہے۔ سسٹم کے سیاق و سباق کو بدلنا اتنا ہی مؤثر ہو سکتا ہے جتنا کہ صارف کی ان پٹ کی فراہمی، جو مکمل جوابات کے معیار پر اثر انداز ہوتی ہے۔

```python
response = openai.chat.completions.create(
    model="gpt-3.5-turbo",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Who won the world series in 2020?"},
        {"role": "assistant", "content": "The Los Angeles Dodgers won the World Series in 2020."},
        {"role": "user", "content": "Where was it played?"}
    ]
)
```

### ہدایت پرامپٹ

اوپر دیے گئے مثالوں میں، صارف کا پرامپٹ ایک سادہ ٹیکسٹ سوال تھا جسے معلومات کی درخواست کے طور پر سمجھا جا سکتا ہے۔ _ہدایت_ والے پرامپٹس میں، ہم اس ٹیکسٹ کو استعمال کر کے AI کو بہتر رہنمائی فراہم کرتے ہوئے کام کی تفصیل دے سکتے ہیں۔ مثال کے طور پر:

| پرامپٹ (ان پٹ)                                                                                                                                                                                                                         | مکمل جواب (آؤٹ پٹ)                                                                                                        | ہدایت کی قسم    |
| :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------- | :------------------ |
| Write a description of the Civil War                                                                                                                                                                                                   | _ایک سادہ پیراگراف واپس کیا_                                                                                              | سادہ              |
| Write a description of the Civil War. Provide key dates and events and describe their significance                                                                                                                                     | _ایک پیراگراف کے بعد اہم واقعات کی تاریخوں کی فہرست اور وضاحتیں واپس کیں_                                             | پیچیدہ             |
| Write a description of the Civil War in 1 paragraph. Provide 3 bullet points with key dates and their significance. Provide 3 more bullet points with key historical figures and their contributions. Return the output as a JSON file | _مزید تفصیلی معلومات ایک ٹیکسٹ باکس میں واپس کرتا ہے، جو JSON فارمیٹ میں ہوتی ہے اور آپ اسے کاپی پیسٹ کر کے فائل میں محفوظ اور تصدیق کر سکتے ہیں_ | پیچیدہ۔ فارمیٹ شدہ۔ |

## بنیادی مواد

اوپر دیے گئے مثالوں میں، پرامپٹ ابھی بھی کافی کھلا تھا، جس سے LLM کو یہ فیصلہ کرنے کی آزادی تھی کہ اس کے پری ٹرینڈ ڈیٹا سیٹ کا کون سا حصہ متعلقہ ہے۔ _بنیادی مواد_ ڈیزائن پیٹرن میں، ان پٹ ٹیکسٹ کو دو حصوں میں تقسیم کیا جاتا ہے:

- ایک ہدایت (عمل)
- متعلقہ مواد (جو عمل کو متاثر کرتا ہے)

یہاں ایک مثال ہے جہاں ہدایت ہے "اسے 2 جملوں میں خلاصہ کریں"۔

| پرامپٹ (ان پٹ)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      | مکمل جواب (آؤٹ پٹ)                                                                                                                                                                                                                                                                             |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| Jupiter is the fifth planet from the Sun and the largest in the Solar System. It is a gas giant with a mass one-thousandth that of the Sun, but two-and-a-half times that of all the other planets in the Solar System combined. Jupiter is one of the brightest objects visible to the naked eye in the night sky, and has been known to ancient civilizations since before recorded history. It is named after the Roman god Jupiter.[19] When viewed from Earth, Jupiter can be bright enough for its reflected light to cast visible shadows,[20] and is on average the third-brightest natural object in the night sky after the Moon and Venus. <br/> **Summarize this in 2 short sentences** | مشتری، جو سورج سے پانچواں سیارہ ہے، نظام شمسی کا سب سے بڑا سیارہ ہے اور رات کے آسمان میں سب سے روشن اجسام میں سے ایک ہے۔ اسے رومی دیوتا مشتری کے نام سے جانا جاتا ہے، اور یہ ایک گیس دیو ہے جس کا وزن نظام شمسی کے تمام دیگر سیاروں کے مجموعے کے دو اور آدھے گنا ہے۔ |

بنیادی مواد کے حصے کو مختلف طریقوں سے استعمال کیا جا سکتا ہے تاکہ ہدایات کو زیادہ مؤثر بنایا جا سکے:

- **مثالیں** — ماڈل کو واضح ہدایت دینے کے بجائے، اسے مطلوبہ آؤٹ پٹ کی مثالیں دیں اور ماڈل خود پیٹرن سمجھ لے۔
- **اشارے** — ہدایت کے بعد ایک "اشارہ" دیں جو مکمل جواب کو متحرک کرے، ماڈل کو زیادہ متعلقہ جوابات کی طرف رہنمائی کرے۔
- **ٹیمپلیٹس** — یہ دہرائے جانے والے 'نسخے' ہوتے ہیں جن میں جگہ دار (variables) ہوتے ہیں جنہیں مخصوص ڈیٹا کے ساتھ حسب ضرورت بنایا جا سکتا ہے۔

آئیے ان کو عملی طور پر دیکھتے ہیں۔

### مثالوں کا استعمال

یہ ایک طریقہ ہے جس میں آپ بنیادی مواد کو ماڈل کو مطلوبہ آؤٹ پٹ کی چند مثالیں "کھلاتے" ہیں تاکہ وہ پیٹرن سمجھ سکے۔ فراہم کردہ مثالوں کی تعداد کی بنیاد پر، ہم zero-shot prompting، one-shot prompting، few-shot prompting وغیرہ کر سکتے ہیں۔

اب پرامپٹ تین اجزاء پر مشتمل ہوتا ہے:

- کام کی وضاحت
- مطلوبہ آؤٹ پٹ کی چند مثالیں
- ایک نئی مثال کی شروعات (جو ضمنی طور پر کام کی وضاحت بن جاتی ہے)

| سیکھنے کی قسم | پرامپٹ (ان پٹ)                                                                                                                                        | مکمل جواب (آؤٹ پٹ)         |
| :------------ | :---------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------- |
| زیرو شاٹ     | "The Sun is Shining". Translate to Spanish                                                                                                            | "El Sol está brillando".    |
| ون شاٹ      | "The Sun is Shining" => ""El Sol está brillando". <br> "It's a Cold and Windy Day" =>                                                                 | "Es un día frío y ventoso". |
| فیو شاٹ      | The player ran the bases => Baseball <br/> The player hit an ace => Tennis <br/> The player hit a six => Cricket <br/> The player made a slam-dunk => | Basketball                  |
|               |                                                                                                                                                       |                             |

نوٹ کریں کہ زیرو شاٹ پرامپٹنگ میں ہمیں واضح ہدایت ("Translate to Spanish") دینی پڑی، لیکن ون شاٹ میں یہ ضمنی طور پر سمجھ آ جاتی ہے۔ فیو شاٹ مثال میں مزید مثالیں شامل کرنے سے ماڈلز بغیر اضافی ہدایات کے زیادہ درست اندازے لگا سکتے ہیں۔

### پرامپٹ اشارے

بنیادی مواد کے استعمال کی ایک اور تکنیک _اشارے_ فراہم کرنا ہے بجائے مثالوں کے۔ اس صورت میں، ہم ماڈل کو ایک چھوٹا سا آغاز دیتے ہیں جو مطلوبہ جواب کے انداز کی نمائندگی کرتا ہے۔ ماڈل پھر اس اشارے کو لے کر اسی انداز میں جواب جاری رکھتا ہے۔

| اشاروں کی تعداد | پرامپٹ (ان پٹ)                                                                                                                                                                                                                                                                                                                                                                                                                                               | مکمل جواب (آؤٹ پٹ)                                                                                                                                                                                                                                                                                       |
| :------------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 0              | Jupiter is the fifth planet from the Sun and the largest in the Solar System. It is a gas giant with a mass one-thousandth that of the Sun, but two-and-a-half times that of all the other planets in the Solar System combined. Jupiter is one of the brightest objects visible to the naked eye in the night sky, and has been known to ancient civilizations since before recorded history.

**Summarize This**                                       | مشتری ہمارا نظام شمسی کا سب سے بڑا سیارہ ہے اور سورج سے پانچواں نمبر پر ہے۔ یہ ایک گیس دیو ہے جس کا وزن سورج کے وزن کا 1/1000 حصہ ہے، لیکن یہ تمام دوسرے سیاروں کے مجموعی وزن سے زیادہ بھاری ہے۔ قدیم تہذیبوں کو مشتری کے بارے میں طویل عرصے سے معلوم ہے، اور یہ آسانی سے رات کے آسمان میں نظر آتا ہے۔ |
| 1              | مشتری سورج سے پانچواں سیارہ ہے اور نظام شمسی کا سب سے بڑا سیارہ ہے۔ یہ ایک گیس دیو ہے جس کا وزن سورج کے وزن کا ایک ہزارواں حصہ ہے، لیکن نظام شمسی کے تمام دوسرے سیاروں کے مجموعی وزن سے ڈیڑھ گنا زیادہ ہے۔ مشتری رات کے آسمان میں آنکھ سے دیکھنے والے سب سے روشن اجسام میں سے ایک ہے، اور قدیم تہذیبوں کو تاریخ سے پہلے سے معلوم ہے۔ <br/>**Summarize This** <br/> ہم نے جو سیکھا وہ یہ ہے کہ مشتری | سورج سے پانچواں سیارہ ہے اور نظام شمسی کا سب سے بڑا سیارہ ہے۔ یہ ایک گیس دیو ہے جس کا وزن سورج کے وزن کا ایک ہزارواں حصہ ہے، لیکن تمام دوسرے سیاروں کے مجموعی وزن سے ڈیڑھ گنا زیادہ ہے۔ یہ آسانی سے آنکھ سے دیکھا جا سکتا ہے اور قدیم زمانے سے جانا جاتا ہے۔                        |
| 2              | مشتری سورج سے پانچواں سیارہ ہے اور نظام شمسی کا سب سے بڑا سیارہ ہے۔ یہ ایک گیس دیو ہے جس کا وزن سورج کے وزن کا ایک ہزارواں حصہ ہے، لیکن نظام شمسی کے تمام دوسرے سیاروں کے مجموعی وزن سے ڈیڑھ گنا زیادہ ہے۔ مشتری رات کے آسمان میں آنکھ سے دیکھنے والے سب سے روشن اجسام میں سے ایک ہے، اور قدیم تہذیبوں کو تاریخ سے پہلے سے معلوم ہے۔ <br/>**Summarize This** <br/> ہم نے جو تین اہم حقائق سیکھے:         | 1. مشتری سورج سے پانچواں سیارہ ہے اور نظام شمسی کا سب سے بڑا سیارہ ہے۔ <br/> 2. یہ ایک گیس دیو ہے جس کا وزن سورج کے وزن کا ایک ہزارواں حصہ ہے...<br/> 3. مشتری قدیم زمانے سے آنکھ سے دیکھا جا سکتا ہے ...                                                                       |
|                |                                                                                                                                                                                                                                                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                           |

### Prompt Templates

ایک پرامپٹ ٹیمپلیٹ ایک _پیشگی طے شدہ نسخہ_ ہوتا ہے جسے ضرورت کے مطابق محفوظ کر کے دوبارہ استعمال کیا جا سکتا ہے، تاکہ بڑے پیمانے پر صارف کے تجربات کو زیادہ مستقل بنایا جا سکے۔ اس کی سادہ شکل میں، یہ پرامپٹ کی مثالوں کا مجموعہ ہوتا ہے جیسے [OpenAI کی یہ مثال](https://platform.openai.com/examples?WT.mc_id=academic-105485-koreyst) جو انٹرایکٹو پرامپٹ اجزاء (صارف اور نظام کے پیغامات) اور API سے چلنے والی درخواست کے فارمیٹ دونوں فراہم کرتا ہے - تاکہ دوبارہ استعمال کی سہولت ہو۔

اس کی زیادہ پیچیدہ شکل میں، جیسے [LangChain کی یہ مثال](https://python.langchain.com/docs/concepts/prompt_templates/?WT.mc_id=academic-105485-koreyst)، اس میں _پلیس ہولڈرز_ ہوتے ہیں جنہیں مختلف ذرائع (صارف کی ان پٹ، نظام کا سیاق و سباق، بیرونی ڈیٹا ذرائع وغیرہ) سے ڈیٹا کے ساتھ تبدیل کیا جا سکتا ہے تاکہ پرامپٹ کو متحرک طور پر تیار کیا جا سکے۔ اس سے ہمیں ایک لائبریری بنانے کی سہولت ملتی ہے جو پروگراماتی طور پر مستقل صارف تجربات فراہم کرنے کے لیے استعمال کی جا سکتی ہے۔

آخر میں، ٹیمپلیٹس کی اصل قدر _پرامپٹ لائبریریاں_ بنانے اور شائع کرنے کی صلاحیت میں ہے جو مخصوص عمودی ایپلیکیشن ڈومینز کے لیے ہوتی ہیں - جہاں پرامپٹ ٹیمپلیٹ اب _مخصوص سیاق و سباق یا مثالوں_ کے مطابق بہتر بنایا جاتا ہے تاکہ جوابات ہدف شدہ صارفین کے لیے زیادہ متعلقہ اور درست ہوں۔ [Prompts For Edu](https://github.com/microsoft/prompts-for-edu?WT.mc_id=academic-105485-koreyst) ریپوزیٹری اس طریقہ کار کی ایک عمدہ مثال ہے، جو تعلیمی شعبے کے لیے پرامپٹس کی لائبریری تیار کرتی ہے، جس میں اہم مقاصد جیسے سبق کی منصوبہ بندی، نصاب کی تیاری، طالب علم کی رہنمائی وغیرہ پر زور دیا گیا ہے۔

## Supporting Content

اگر ہم پرامپٹ کی تشکیل کو ایک ہدایت (ٹاسک) اور ایک ہدف (بنیادی مواد) کے طور پر دیکھیں، تو _ثانوی مواد_ اضافی سیاق و سباق کی طرح ہوتا ہے جو ہم آؤٹ پٹ کو کسی نہ کسی طرح متاثر کرنے کے لیے فراہم کرتے ہیں۔ یہ ٹیوننگ پیرامیٹرز، فارمیٹنگ ہدایات، موضوع کی درجہ بندی وغیرہ ہو سکتے ہیں جو ماڈل کو اس کے جواب کو مطلوبہ صارف کے مقاصد یا توقعات کے مطابق ڈھالنے میں مدد دیتے ہیں۔

مثال کے طور پر: اگر ہمارے پاس نصاب کے تمام دستیاب کورسز کے بارے میں وسیع میٹا ڈیٹا (نام، وضاحت، سطح، میٹا ڈیٹا ٹیگز، انسٹرکٹر وغیرہ) کے ساتھ ایک کورس کیٹلاگ ہو:

- ہم ہدایت دے سکتے ہیں کہ "فال 2023 کے لیے کورس کیٹلاگ کا خلاصہ بنائیں"
- ہم بنیادی مواد کے طور پر چند مطلوبہ آؤٹ پٹ کی مثالیں دے سکتے ہیں
- ہم ثانوی مواد کے طور پر دلچسپی کے 5 سب سے اہم "ٹیگز" کی نشاندہی کر سکتے ہیں۔

اب، ماڈل چند مثالوں کے ذریعے دکھائے گئے فارمیٹ میں خلاصہ فراہم کر سکتا ہے - لیکن اگر نتیجے میں متعدد ٹیگز ہوں، تو یہ ثانوی مواد میں دیے گئے 5 ٹیگز کو ترجیح دے سکتا ہے۔

---

<!--
LESSON TEMPLATE:
یہ یونٹ بنیادی تصور #1 کو کور کرے گا۔
تصور کو مثالوں اور حوالہ جات کے ساتھ مضبوط کریں۔

CONCEPT #3:
پرامپٹ انجینئرنگ کی تکنیکیں۔
پرامپٹ انجینئرنگ کی کچھ بنیادی تکنیکیں کیا ہیں؟
کچھ مشقوں کے ساتھ وضاحت کریں۔
-->

## Prompting Best Practices

اب جب کہ ہم جانتے ہیں کہ پرامپٹس کو کیسے _تشکیل_ دیا جا سکتا ہے، ہم سوچ سکتے ہیں کہ انہیں کیسے _ڈیزائن_ کیا جائے تاکہ بہترین طریقے اپنائے جا سکیں۔ ہم اسے دو حصوں میں سوچ سکتے ہیں - صحیح _ذہنیت_ رکھنا اور درست _تکنیکیں_ اپنانا۔

### Prompt Engineering Mindset

پرامپٹ انجینئرنگ ایک آزمائش اور غلطی کا عمل ہے، اس لیے تین وسیع رہنما اصول ذہن میں رکھیں:

1. **ڈومین کی سمجھ ضروری ہے۔** جواب کی درستگی اور مطابقت اس _ڈومین_ پر منحصر ہے جس میں وہ ایپلیکیشن یا صارف کام کر رہا ہو۔ اپنی سمجھ اور ڈومین کی مہارت استعمال کریں تاکہ **تکنیکوں کو مزید حسب ضرورت بنایا جا سکے**۔ مثلاً، اپنے نظام کے پرامپٹس میں _ڈومین مخصوص شخصیات_ متعین کریں، یا صارف کے پرامپٹس میں _ڈومین مخصوص ٹیمپلیٹس_ استعمال کریں۔ ثانوی مواد فراہم کریں جو ڈومین کے مخصوص سیاق و سباق کی عکاسی کرے، یا ماڈل کو واقف استعمال کے نمونوں کی طرف رہنمائی کے لیے _ڈومین مخصوص اشارے اور مثالیں_ استعمال کریں۔

2. **ماڈل کی سمجھ ضروری ہے۔** ہم جانتے ہیں کہ ماڈلز فطری طور پر تصادفی ہوتے ہیں۔ لیکن ماڈل کی تعمیل مختلف ہو سکتی ہے، جیسے کہ وہ کس تربیتی ڈیٹا سیٹ (پری ٹرینڈ نالج) کا استعمال کرتے ہیں، کون سی صلاحیتیں فراہم کرتے ہیں (مثلاً API یا SDK کے ذریعے)، اور کس قسم کے مواد کے لیے بہتر ہیں (مثلاً کوڈ، تصاویر، یا متن)۔ جس ماڈل کا آپ استعمال کر رہے ہیں اس کی طاقتوں اور حدود کو سمجھیں، اور اس علم کو استعمال کرتے ہوئے _کاموں کو ترجیح دیں_ یا _حسب ضرورت ٹیمپلیٹس_ بنائیں جو ماڈل کی صلاحیتوں کے لیے بہتر ہوں۔

3. **دہرانا اور تصدیق ضروری ہے۔** ماڈلز تیزی سے ترقی کر رہے ہیں، اور پرامپٹ انجینئرنگ کی تکنیکیں بھی۔ ایک ڈومین ماہر کے طور پر، آپ کے پاس اپنے مخصوص ایپلیکیشن کے لیے دیگر سیاق و سباق یا معیار ہو سکتے ہیں جو وسیع کمیونٹی پر لاگو نہ ہوں۔ پرامپٹ انجینئرنگ کے اوزار اور تکنیکیں استعمال کریں تاکہ پرامپٹ کی تشکیل کا آغاز کریں، پھر اپنے تجربے اور مہارت سے نتائج کو دہرائیں اور تصدیق کریں۔ اپنے مشاہدات کو ریکارڈ کریں اور ایک **علمی ذخیرہ** (مثلاً پرامپٹ لائبریریاں) بنائیں جو دوسروں کے لیے ایک نئی بنیاد کے طور پر کام کرے، تاکہ مستقبل میں تیزی سے بہتری کی جا سکے۔

## Best Practices

اب ہم عام بہترین طریقے دیکھتے ہیں جو [OpenAI](https://help.openai.com/en/articles/6654000-best-practices-for-prompt-engineering-with-openai-api?WT.mc_id=academic-105485-koreyst) اور [Azure OpenAI](https://learn.microsoft.com/azure/ai-services/openai/concepts/prompt-engineering#best-practices?WT.mc_id=academic-105485-koreyst) کے ماہرین تجویز کرتے ہیں۔

| کیا                              | کیوں                                                                                                                                                                                                                                               |
| :-------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| تازہ ترین ماڈلز کا جائزہ لیں۔       | نئے ماڈلز میں بہتر خصوصیات اور معیار ہو سکتے ہیں - لیکن ان کی لاگت بھی زیادہ ہو سکتی ہے۔ ان کے اثرات کا جائزہ لیں، پھر منتقلی کے فیصلے کریں۔                                                                                |
| ہدایات اور سیاق و سباق کو الگ کریں۔   | چیک کریں کہ آیا آپ کا ماڈل/پرووائیڈر _حد بندی کنندگان_ متعین کرتا ہے تاکہ ہدایات، بنیادی اور ثانوی مواد کو واضح طور پر الگ کیا جا سکے۔ یہ ماڈلز کو ٹوکنز کو زیادہ درست وزن دینے میں مدد دے سکتا ہے۔                                                         |
| واضح اور مخصوص رہیں۔             | مطلوبہ سیاق و سباق، نتیجہ، لمبائی، فارمیٹ، انداز وغیرہ کے بارے میں زیادہ تفصیل دیں۔ اس سے جوابات کا معیار اور مستقل مزاجی بہتر ہوگی۔ نسخے قابلِ استعمال ٹیمپلیٹس میں محفوظ کریں۔                                                          |
| وضاحتی ہوں، مثالیں دیں۔      | ماڈلز "دکھائیں اور بتائیں" کے طریقہ کار پر بہتر ردعمل دے سکتے ہیں۔ `زیرو شاٹ` طریقہ سے شروع کریں جہاں آپ صرف ہدایت دیتے ہیں (مثالیں نہیں) پھر `فیو شاٹ` کے ذریعے چند مثالیں دے کر بہتر کریں۔ تشبیہات استعمال کریں۔ |
| جوابات کی شروعات کے لیے اشارے دیں | ماڈل کو مطلوبہ نتیجے کی طرف راغب کرنے کے لیے کچھ ابتدائی الفاظ یا جملے دیں جنہیں وہ جواب کی شروعات کے لیے استعمال کر سکے۔                                                                                                               |
| دہرائیں                        | کبھی کبھار ماڈل کو بار بار ہدایت دینی پڑ سکتی ہے۔ ہدایات کو بنیادی مواد سے پہلے اور بعد میں دیں، ہدایت اور اشارہ دونوں استعمال کریں، وغیرہ۔ دہرائیں اور تصدیق کریں کہ کیا بہتر کام کرتا ہے۔                                                         |
| ترتیب اہم ہے                   | جس ترتیب میں آپ معلومات ماڈل کو دیتے ہیں، اس کا اثر آؤٹ پٹ پر پڑ سکتا ہے، خاص طور پر سیکھنے کی مثالوں میں، کیونکہ تازہ کاری کا رجحان ہوتا ہے۔ مختلف اختیارات آزمائیں تاکہ بہترین معلوم ہو۔                                                               |
| ماڈل کو “باہر نکلنے” کا راستہ دیں           | ماڈل کو ایک _فیل بیک_ جواب دیں جو وہ فراہم کر سکے اگر کسی وجہ سے ٹاسک مکمل نہ کر سکے۔ اس سے غلط یا فرضی جوابات کے امکانات کم ہو جاتے ہیں۔                                                         |
|                                   |                                                                                                                                                                                                                                                   |

کسی بھی بہترین طریقے کی طرح، یاد رکھیں کہ _آپ کا تجربہ مختلف ہو سکتا ہے_ ماڈل، ٹاسک اور ڈومین کے لحاظ سے۔ انہیں ایک نقطہ آغاز کے طور پر استعمال کریں، اور دہرائیں تاکہ آپ کے لیے بہترین معلوم ہو۔ نئے ماڈلز اور اوزار دستیاب ہوتے رہیں، اپنے پرامپٹ انجینئرنگ کے عمل کا مسلسل جائزہ لیتے رہیں، خاص طور پر عمل کی توسیع پذیری اور جواب کے معیار پر توجہ دیتے ہوئے۔

<!--
LESSON TEMPLATE:
اگر قابلِ اطلاق ہو تو اس یونٹ میں کوڈ چیلنج فراہم کریں۔

CHALLENGE:
ایک Jupyter Notebook کا لنک دیں جس میں صرف کوڈ کے تبصرے ہوں (کوڈ سیکشن خالی ہوں)۔

SOLUTION:
اس Notebook کی ایک کاپی کا لنک دیں جس میں پرامپٹس بھرے ہوئے ہوں اور چلائی گئی ہو، تاکہ ایک مثال دکھائی جا سکے۔
-->

## Assignment

مبارک ہو! آپ سبق کے آخر تک پہنچ گئے ہیں! اب وقت ہے کہ ان تصورات اور تکنیکوں کو حقیقی مثالوں کے ساتھ آزمایا جائے!

ہمارے اسائنمنٹ کے لیے، ہم ایک Jupyter Notebook استعمال کریں گے جس میں آپ مشقیں انٹرایکٹو طور پر مکمل کر سکتے ہیں۔ آپ اپنی مرضی سے Markdown اور Code سیلز بھی شامل کر کے خیالات اور تکنیکوں کو خود دریافت کر سکتے ہیں۔

### شروع کرنے کے لیے، ریپو کو فورک کریں، پھر

- (تجویز کردہ) GitHub Codespaces لانچ کریں
- (متبادل) ریپو کو اپنے مقامی آلے پر کلون کریں اور Docker Desktop کے ساتھ استعمال کریں
- (متبادل) Notebook کو اپنی پسندیدہ رن ٹائم ماحول میں کھولیں۔

### اگلا، اپنے ماحول کے متغیرات ترتیب دیں

- ریپو کی جڑ میں `.env.copy` فائل کو `.env` میں کاپی کریں اور `AZURE_OPENAI_API_KEY`, `AZURE_OPENAI_ENDPOINT` اور `AZURE_OPENAI_DEPLOYMENT` کی قدریں بھریں۔ پھر واپس [Learning Sandbox سیکشن](../../../04-prompt-engineering-fundamentals/04-prompt-engineering-fundamentals) پر آئیں تاکہ سیکھ سکیں کہ کیسے۔

### پھر، Jupyter Notebook کھولیں

- رن ٹائم کرنل منتخب کریں۔ اگر آپ آپشن 1 یا 2 استعمال کر رہے ہیں، تو بس ڈیولپمنٹ کنٹینر کی طرف سے فراہم کردہ Python 3.10.x کرنل منتخب کریں۔

آپ مشقیں چلانے کے لیے تیار ہیں۔ نوٹ کریں کہ یہاں کوئی _صحیح یا غلط_ جواب نہیں ہیں - صرف آزمائش اور غلطی کے ذریعے اختیارات کو دریافت کرنا اور ماڈل اور ایپلیکیشن ڈومین کے لیے کام کرنے والی چیزوں کی سمجھ بوجھ حاصل کرنا ہے۔

_اسی وجہ سے اس سبق میں کوئی کوڈ حل کے حصے نہیں ہیں۔ اس کے بجائے، Notebook میں "My Solution:" کے عنوان سے Markdown سیلز ہوں گے جو ایک مثال کے آؤٹ پٹ کو بطور حوالہ دکھائیں گے۔_

 <!--
LESSON TEMPLATE:
اس سیکشن کو خلاصہ اور خود رہنمائی کے وسائل کے ساتھ لپیٹیں۔
-->

## Knowledge check

مندرجہ ذیل میں سے کون سا پرامپٹ معقول بہترین طریقوں کے مطابق اچھا ہے؟

1. مجھے سرخ کار کی تصویر دکھائیں
2. مجھے سرخ کار کی تصویر دکھائیں جو Volvo کی ہے اور ماڈل XC90 ہے، جو ایک چٹان کے کنارے سورج غروب ہوتے ہوئے کھڑی ہے
3. مجھے سرخ کار کی تصویر دکھائیں جو Volvo کی ہے اور ماڈل XC90 ہے

جواب: 2، یہ بہترین پرامپٹ ہے کیونکہ یہ "کیا" کی تفصیل دیتا ہے اور خاص باتیں بتاتا ہے (صرف کوئی بھی کار نہیں بلکہ مخصوص برانڈ اور ماڈل) اور مجموعی منظر بھی بیان کرتا ہے۔ 3 دوسرا بہترین ہے کیونکہ اس میں بھی کافی تفصیل ہے۔

## 🚀 Challenge

دیکھیں کہ آپ "cue" تکنیک کو اس پرامپٹ کے ساتھ کیسے استعمال کر سکتے ہیں: جملہ مکمل کریں "مجھے سرخ کار کی تصویر دکھائیں جو Volvo کی ہے اور "۔ یہ کیا جواب دیتا ہے، اور آپ اسے کیسے بہتر بنائیں گے؟

## Great Work! Continue Your Learning

کیا آپ مختلف پرامپٹ انجینئرنگ تصورات کے بارے میں مزید جاننا چاہتے ہیں؟ [continued learning page](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) پر جائیں جہاں اس موضوع پر دیگر بہترین وسائل دستیاب ہیں۔

سبق 5 پر جائیں جہاں ہم [advanced prompting techniques](../05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst) کا جائزہ لیں گے!

**دستخطی نوٹ**:  
یہ دستاویز AI ترجمہ سروس [Co-op Translator](https://github.com/Azure/co-op-translator) کے ذریعے ترجمہ کی گئی ہے۔ اگرچہ ہم درستگی کے لیے کوشاں ہیں، براہ کرم آگاہ رہیں کہ خودکار ترجمے میں غلطیاں یا عدم درستیاں ہو سکتی ہیں۔ اصل دستاویز اپنی مادری زبان میں ہی معتبر ماخذ سمجھی جانی چاہیے۔ اہم معلومات کے لیے پیشہ ور انسانی ترجمہ کی سفارش کی جاتی ہے۔ اس ترجمے کے استعمال سے پیدا ہونے والی کسی بھی غلط فہمی یا غلط تشریح کی ذمہ داری ہم پر عائد نہیں ہوتی۔