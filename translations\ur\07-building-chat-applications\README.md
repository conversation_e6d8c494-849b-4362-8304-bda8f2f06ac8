<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "ea4bbe640847aafbbba14dae4625e9af",
  "translation_date": "2025-07-09T12:17:20+00:00",
  "source_file": "07-building-chat-applications/README.md",
  "language_code": "ur"
}
-->
# جنریٹو AI سے چلنے والی چیٹ ایپلیکیشنز کی تعمیر

[![جنریٹو AI سے چلنے والی چیٹ ایپلیکیشنز کی تعمیر](../../../translated_images/07-lesson-banner.a279b937f2843833fe28b4597f51bdef92d0ad03efee7ba52d0f166dea7574e5.ur.png)](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst)

> _(اس سبق کی ویڈیو دیکھنے کے لیے اوپر تصویر پر کلک کریں)_

اب جب کہ ہم نے دیکھا کہ ہم ٹیکسٹ جنریشن ایپس کیسے بنا سکتے ہیں، آئیے چیٹ ایپلیکیشنز پر نظر ڈالتے ہیں۔

چیٹ ایپلیکیشنز ہماری روزمرہ زندگی کا حصہ بن چکی ہیں، جو صرف غیر رسمی گفتگو کا ذریعہ نہیں بلکہ کسٹمر سروس، تکنیکی مدد، اور پیچیدہ مشاورتی نظاموں کا لازمی جزو ہیں۔ ممکن ہے کہ آپ نے حال ہی میں کسی چیٹ ایپلیکیشن سے مدد حاصل کی ہو۔ جیسے جیسے ہم جنریٹو AI جیسی جدید ٹیکنالوجیز کو ان پلیٹ فارمز میں شامل کرتے ہیں، پیچیدگی اور چیلنجز بھی بڑھتے ہیں۔

کچھ سوالات جن کے جواب ہمیں چاہیے:

- **ایپ کی تعمیر**۔ ہم مخصوص استعمال کے کیسز کے لیے ان AI سے چلنے والی ایپلیکیشنز کو مؤثر طریقے سے کیسے بنا کر بغیر رکاوٹ کے شامل کر سکتے ہیں؟
- **نگرانی**۔ تعیناتی کے بعد، ہم کیسے یقینی بنائیں کہ ایپلیکیشنز اعلیٰ معیار کی کارکردگی دکھا رہی ہیں، چاہے وہ فنکشنلٹی ہو یا [ذمہ دار AI کے چھ اصول](https://www.microsoft.com/ai/responsible-ai?WT.mc_id=academic-105485-koreyst) کی پابندی؟

آٹومیشن اور ہموار انسان-مشین تعاملات کے دور میں داخل ہوتے ہوئے، یہ سمجھنا ضروری ہے کہ جنریٹو AI چیٹ ایپلیکیشنز کی وسعت، گہرائی، اور لچک کو کیسے بدلتا ہے۔ یہ سبق ان پیچیدہ نظاموں کی حمایت کرنے والی آرکیٹیکچر کے پہلوؤں کا جائزہ لے گا، مخصوص شعبوں کے کاموں کے لیے انہیں بہتر بنانے کے طریقے دریافت کرے گا، اور ذمہ دار AI کی تعیناتی کے لیے متعلقہ میٹرکس اور غور و فکر کا تجزیہ کرے گا۔

## تعارف

یہ سبق درج ذیل موضوعات پر مشتمل ہے:

- چیٹ ایپلیکیشنز کو مؤثر طریقے سے بنانے اور شامل کرنے کی تکنیکیں۔
- ایپلیکیشنز میں تخصیص اور فائن ٹیوننگ کا اطلاق۔
- چیٹ ایپلیکیشنز کی مؤثر نگرانی کے لیے حکمت عملی اور غور و فکر۔

## سیکھنے کے مقاصد

اس سبق کے اختتام پر، آپ قابل ہوں گے کہ:

- موجودہ نظاموں میں چیٹ ایپلیکیشنز کی تعمیر اور انضمام کے لیے غور و فکر بیان کریں۔
- مخصوص استعمال کے کیسز کے لیے چیٹ ایپلیکیشنز کو حسب ضرورت بنائیں۔
- AI سے چلنے والی چیٹ ایپلیکیشنز کے معیار کو مؤثر طریقے سے مانیٹر اور برقرار رکھنے کے لیے اہم میٹرکس اور غور و فکر کی نشاندہی کریں۔
- یقینی بنائیں کہ چیٹ ایپلیکیشنز AI کو ذمہ داری سے استعمال کر رہی ہیں۔

## چیٹ ایپلیکیشنز میں جنریٹو AI کا انضمام

چیٹ ایپلیکیشنز کو جنریٹو AI کے ذریعے بہتر بنانا صرف انہیں زیادہ ذہین بنانے تک محدود نہیں؛ بلکہ یہ ان کی آرکیٹیکچر، کارکردگی، اور یوزر انٹرفیس کو بہتر بنا کر اعلیٰ معیار کا تجربہ فراہم کرنے کا معاملہ ہے۔ اس میں آرکیٹیکچرل بنیادوں، API انضمام، اور یوزر انٹرفیس کے پہلوؤں کی جانچ شامل ہے۔ یہ سیکشن آپ کو ان پیچیدہ موضوعات میں رہنمائی فراہم کرنے کا مقصد رکھتا ہے، چاہے آپ انہیں موجودہ نظاموں میں شامل کر رہے ہوں یا الگ پلیٹ فارم کے طور پر بنا رہے ہوں۔

اس سیکشن کے اختتام تک، آپ کے پاس چیٹ ایپلیکیشنز کو مؤثر طریقے سے بنانے اور شامل کرنے کی مہارت ہوگی۔

### چیٹ بوٹ یا چیٹ ایپلیکیشن؟

چیٹ ایپلیکیشنز بنانے میں غوطہ لگانے سے پہلے، آئیے 'چیٹ بوٹس' اور 'AI سے چلنے والی چیٹ ایپلیکیشنز' کا موازنہ کریں، جو مختلف کردار اور فنکشنز رکھتے ہیں۔ چیٹ بوٹ کا بنیادی مقصد مخصوص بات چیت کے کاموں کو خودکار بنانا ہے، جیسے اکثر پوچھے جانے والے سوالات کے جواب دینا یا پیکیج کی ٹریکنگ۔ یہ عام طور پر قواعد پر مبنی منطق یا پیچیدہ AI الگورتھمز کے تحت کام کرتا ہے۔ اس کے برعکس، AI سے چلنے والی چیٹ ایپلیکیشن ایک وسیع تر ماحول ہے جو مختلف قسم کی ڈیجیٹل بات چیت کو ممکن بناتی ہے، جیسے ٹیکسٹ، وائس، اور ویڈیو چیٹس انسانی صارفین کے درمیان۔ اس کی خاص بات جنریٹو AI ماڈل کا انضمام ہے جو باریک، انسانی طرز کی گفتگو کی نقل کرتا ہے، مختلف ان پٹ اور سیاق و سباق کی بنیاد پر جوابات تیار کرتا ہے۔ جنریٹو AI سے چلنے والی چیٹ ایپلیکیشن کھلے موضوعات پر بات چیت کر سکتی ہے، گفتگو کے بدلتے ہوئے سیاق و سباق کے مطابق خود کو ڈھال سکتی ہے، اور تخلیقی یا پیچیدہ مکالمے بھی پیدا کر سکتی ہے۔

ذیل کی جدول ان کے اہم فرق اور مماثلتوں کو واضح کرتی ہے تاکہ ہم ان کے منفرد کردار کو سمجھ سکیں۔

| چیٹ بوٹ                              | جنریٹو AI سے چلنے والی چیٹ ایپلیکیشن          |
| ----------------------------------- | --------------------------------------------- |
| کام پر مرکوز اور قواعد پر مبنی       | سیاق و سباق سے آگاہ                           |
| اکثر بڑے نظاموں میں شامل ہوتا ہے     | ایک یا متعدد چیٹ بوٹس کی میزبانی کر سکتا ہے  |
| پروگرام شدہ فنکشنز تک محدود          | جنریٹو AI ماڈلز کو شامل کرتا ہے               |
| مخصوص اور منظم بات چیت               | کھلے موضوعات پر بات چیت کرنے کے قابل          |

### SDKs اور APIs کے ذریعے پہلے سے بنائی گئی خصوصیات کا فائدہ اٹھانا

چیٹ ایپلیکیشن بنانے کے دوران، سب سے اچھا پہلا قدم یہ جانچنا ہے کہ پہلے سے کیا دستیاب ہے۔ SDKs اور APIs کا استعمال چیٹ ایپلیکیشنز بنانے کے لیے ایک فائدہ مند حکمت عملی ہے۔ اچھی دستاویزات والے SDKs اور APIs کو شامل کر کے، آپ اپنی ایپلیکیشن کو طویل مدتی کامیابی کے لیے بہتر پوزیشن میں رکھتے ہیں، جس سے اسکیل ایبلٹی اور دیکھ بھال کے مسائل حل ہوتے ہیں۔

- **ترقی کے عمل کو تیز کرتا ہے اور اضافی بوجھ کم کرتا ہے**: خود سے مہنگی خصوصیات بنانے کے بجائے پہلے سے موجود فنکشنز پر انحصار کرنے سے آپ اپنی ایپلیکیشن کے دوسرے اہم پہلوؤں جیسے کاروباری منطق پر توجہ مرکوز کر سکتے ہیں۔
- **بہتر کارکردگی**: جب آپ فنکشنز خود سے بناتے ہیں، تو آپ کو آخر میں یہ سوال کرنا پڑتا ہے کہ "یہ کس طرح اسکیل کرتا ہے؟ کیا یہ ایپلیکیشن اچانک صارفین کی بڑی تعداد کو سنبھال سکتی ہے؟" اچھی طرح سے برقرار رکھے گئے SDKs اور APIs میں اکثر ان مسائل کے حل شامل ہوتے ہیں۔
- **آسان دیکھ بھال**: اپ ڈیٹس اور بہتریاں آسانی سے منظم کی جا سکتی ہیں کیونکہ زیادہ تر APIs اور SDKs کو صرف لائبریری کی اپ ڈیٹ کی ضرورت ہوتی ہے جب نیا ورژن جاری ہوتا ہے۔
- **جدید ترین ٹیکنالوجی تک رسائی**: ایسے ماڈلز کا استعمال جو وسیع ڈیٹا سیٹس پر تربیت یافتہ اور فائن ٹیون کیے گئے ہیں، آپ کی ایپلیکیشن کو قدرتی زبان کی صلاحیتیں فراہم کرتا ہے۔

SDK یا API کی خصوصیات تک رسائی عام طور پر فراہم کردہ خدمات کے استعمال کی اجازت حاصل کرنے سے ہوتی ہے، جو اکثر ایک منفرد کلید یا توثیقی ٹوکن کے ذریعے ہوتی ہے۔ ہم OpenAI Python Library کا استعمال کرتے ہوئے دیکھیں گے کہ یہ کیسا لگتا ہے۔ آپ اس سبق کے لیے درج ذیل [OpenAI کا نوٹ بک](../../../07-building-chat-applications/python/oai-assignment.ipynb) یا [Azure OpenAI Services کا نوٹ بک](../../../07-building-chat-applications/python/aoai-assignment.ipynb) بھی آزما سکتے ہیں۔

```python
import os
from openai import OpenAI

API_KEY = os.getenv("OPENAI_API_KEY","")

client = OpenAI(
    api_key=API_KEY
    )

chat_completion = client.chat.completions.create(model="gpt-3.5-turbo", messages=[{"role": "user", "content": "Suggest two titles for an instructional lesson on chat applications for generative AI."}])
```

اوپر دیے گئے مثال میں GPT-3.5 Turbo ماڈل کو پرامپٹ مکمل کرنے کے لیے استعمال کیا گیا ہے، لیکن نوٹ کریں کہ API کلید پہلے سے سیٹ کی گئی ہے۔ اگر آپ کلید سیٹ نہ کریں تو آپ کو ایرر ملے گا۔

## صارف کا تجربہ (UX)

عام UX اصول چیٹ ایپلیکیشنز پر لاگو ہوتے ہیں، لیکن یہاں کچھ اضافی غور و فکر ہیں جو مشین لرننگ کے اجزاء کی وجہ سے خاص اہمیت اختیار کر لیتے ہیں۔

- **ابہام کو دور کرنے کا طریقہ**: جنریٹو AI ماڈلز کبھی کبھار مبہم جوابات دیتے ہیں۔ ایک ایسی خصوصیت جو صارفین کو وضاحت طلب کرنے کی اجازت دیتی ہو، اس مسئلے سے نمٹنے میں مددگار ثابت ہو سکتی ہے۔
- **سیاق و سباق کا برقرار رہنا**: جدید جنریٹو AI ماڈلز گفتگو کے دوران سیاق و سباق کو یاد رکھنے کی صلاحیت رکھتے ہیں، جو صارف کے تجربے کے لیے ضروری ہو سکتی ہے۔ صارفین کو سیاق و سباق کو کنٹرول اور منظم کرنے کی اجازت دینا تجربے کو بہتر بناتا ہے، لیکن حساس معلومات کے ذخیرہ ہونے کا خطرہ بھی پیدا کرتا ہے۔ اس معلومات کو کتنی دیر تک محفوظ رکھا جائے، جیسے کہ ایک ریٹینشن پالیسی متعارف کرانا، پرائیویسی اور سیاق و سباق کی ضرورت کے درمیان توازن قائم کر سکتا ہے۔
- **ذاتی نوعیت**: سیکھنے اور ڈھلنے کی صلاحیت کے ساتھ، AI ماڈلز صارف کے لیے ایک انفرادی تجربہ فراہم کرتے ہیں۔ صارف کے پروفائلز جیسی خصوصیات کے ذریعے تجربے کو حسب ضرورت بنانا نہ صرف صارف کو سمجھا ہوا محسوس کراتا ہے بلکہ مخصوص جوابات تلاش کرنے میں بھی مدد دیتا ہے، جس سے بات چیت زیادہ مؤثر اور تسلی بخش بنتی ہے۔

ذاتی نوعیت کی ایک مثال OpenAI کے ChatGPT میں "Custom instructions" کی سیٹنگ ہے۔ یہ آپ کو اپنے بارے میں ایسی معلومات فراہم کرنے کی اجازت دیتی ہے جو آپ کے پرامپٹس کے لیے اہم سیاق و سباق ہو سکتی ہیں۔ یہاں ایک کسٹم انسٹرکشن کی مثال ہے۔

![ChatGPT میں Custom Instructions کی سیٹنگ](../../../translated_images/custom-instructions.b96f59aa69356fcfed456414221919e8996f93c90c20d0d58d1bc0221e3c909f.ur.png)

یہ "پروفائل" ChatGPT کو لنکڈ لسٹس پر ایک سبق کا منصوبہ بنانے کی ہدایت دیتا ہے۔ نوٹ کریں کہ ChatGPT صارف کے تجربے کی بنیاد پر زیادہ تفصیلی سبق کا منصوبہ بنانے کو مدنظر رکھتا ہے۔

![ChatGPT میں لنکڈ لسٹس کے بارے میں سبق کا منصوبہ بنانے کے لیے پرامپٹ](../../../translated_images/lesson-plan-prompt.cc47c488cf1343df5d67aa796a1acabca32c380e5b782971e289f6ab8b21cf5a.ur.png)

### Microsoft کا Large Language Models کے لیے System Message Framework

[Microsoft نے رہنمائی فراہم کی ہے](https://learn.microsoft.com/azure/ai-services/openai/concepts/system-message#define-the-models-output-format?WT.mc_id=academic-105485-koreyst) کہ LLMs سے جوابات پیدا کرتے وقت مؤثر سسٹم میسجز کیسے لکھے جائیں، جو چار حصوں میں تقسیم ہے:

1. ماڈل کے لیے ہدف صارف، اس کی صلاحیتیں اور حدود کی وضاحت۔
2. ماڈل کے آؤٹ پٹ فارمیٹ کی تعریف۔
3. مخصوص مثالیں فراہم کرنا جو ماڈل کے مطلوبہ رویے کو ظاہر کریں۔
4. اضافی رویے کے لیے حفاظتی اقدامات فراہم کرنا۔

### رسائی

چاہے صارف کو بصری، سماعتی، موٹر، یا علمی معذوریاں ہوں، ایک اچھی ڈیزائن کی گئی چیٹ ایپلیکیشن سب کے لیے قابل استعمال ہونی چاہیے۔ درج ذیل فہرست مختلف صارف کی معذوریوں کے لیے رسائی کو بہتر بنانے والی خصوصیات کو بیان کرتی ہے۔

- **بصری معذوری کے لیے خصوصیات**: ہائی کانٹراسٹ تھیمز اور قابلِ توسیع متن، اسکرین ریڈر کی مطابقت۔
- **سماعتی معذوری کے لیے خصوصیات**: ٹیکسٹ ٹو اسپیچ اور اسپیچ ٹو ٹیکسٹ فنکشنز، آڈیو نوٹیفیکیشنز کے لیے بصری اشارے۔
- **موٹر معذوری کے لیے خصوصیات**: کی بورڈ نیویگیشن کی حمایت، وائس کمانڈز۔
- **علمی معذوری کے لیے خصوصیات**: آسان زبان کے اختیارات۔

## مخصوص شعبوں کے لیے زبان کے ماڈلز کی تخصیص اور فائن ٹیوننگ

تصور کریں ایک ایسی چیٹ ایپلیکیشن جو آپ کی کمپنی کی اصطلاحات کو سمجھتی ہو اور صارفین کے عام سوالات کا اندازہ لگا لیتی ہو۔ اس کے لیے چند طریقے قابل ذکر ہیں:

- **DSL ماڈلز کا استعمال**۔ DSL کا مطلب ہے domain specific language۔ آپ ایک ایسے DSL ماڈل کا استعمال کر سکتے ہیں جو مخصوص شعبے پر تربیت یافتہ ہو تاکہ اس کے تصورات اور حالات کو سمجھ سکے۔
- **فائن ٹیوننگ کا اطلاق**۔ فائن ٹیوننگ کا مطلب ہے اپنے ماڈل کو مخصوص ڈیٹا کے ساتھ مزید تربیت دینا۔

## تخصیص: DSL کا استعمال

ڈومین اسپیسفک لینگویج ماڈلز (DSL ماڈلز) کا استعمال صارف کی مشغولیت کو بڑھا سکتا ہے اور مخصوص، سیاق و سباق سے متعلق بات چیت فراہم کر کے بہتر بنا سکتا ہے۔ یہ ایک ایسا ماڈل ہے جو کسی خاص میدان، صنعت، یا موضوع سے متعلق متن کو سمجھنے اور پیدا کرنے کے لیے تربیت یافتہ یا فائن ٹیون کیا گیا ہو۔ DSL ماڈل استعمال کرنے کے اختیارات میں نیا ماڈل بنانا، یا SDKs اور APIs کے ذریعے پہلے سے موجود ماڈلز کا استعمال شامل ہو سکتا ہے۔ ایک اور آپشن فائن ٹیوننگ ہے، جس میں پہلے سے تربیت یافتہ ماڈل کو مخصوص شعبے کے لیے ڈھالا جاتا ہے۔

## تخصیص: فائن ٹیوننگ کا اطلاق

جب کوئی پہلے سے تربیت یافتہ ماڈل کسی مخصوص شعبے یا کام میں ناکافی ہو، تو فائن ٹیوننگ پر غور کیا جاتا ہے۔

مثال کے طور پر، طبی سوالات پیچیدہ ہوتے ہیں اور بہت سی معلومات کی ضرورت ہوتی ہے۔ جب کوئی طبی ماہر مریض کی تشخیص کرتا ہے تو وہ مختلف عوامل جیسے طرز زندگی یا پہلے سے موجود بیماریوں کی بنیاد پر کرتا ہے، اور حالیہ طبی جرنلز پر بھی انحصار کر سکتا ہے۔ ایسے نازک حالات میں، عام مقصد کی AI چیٹ ایپلیکیشن قابل اعتماد ذریعہ نہیں ہو سکتی۔

### منظر نامہ: ایک طبی ایپلیکیشن

ایک ایسی چیٹ ایپلیکیشن کا تصور کریں جو طبی ماہرین کی مدد کے لیے علاج کے رہنما اصول، دوائیوں کے تعاملات، یا حالیہ تحقیق کے نتائج فوری طور پر فراہم کرتی ہو۔

ایک عام مقصد کا ماڈل بنیادی طبی سوالات کے جواب دینے یا عمومی مشورہ دینے کے لیے مناسب ہو سکتا ہے، لیکن یہ درج ذیل میں مشکلات کا سامنا کر سکتا ہے:

- **انتہائی مخصوص یا پیچیدہ کیسز**۔ مثال کے طور پر، ایک نیورولوجسٹ ایپلیکیشن سے پوچھ سکتا ہے، "بچوں میں دوائیوں کے خلاف مزاحم مرگی کے انتظام کے لیے موجودہ بہترین طریقے کیا ہیں؟"
- **حالیہ پیش رفت کی کمی**۔ ایک عام ماڈل جدید نیورولوجی اور فارماکولوجی کی تازہ ترین پیش رفت کو شامل کرنے میں ناکام ہو سکتا ہے۔

ایسے معاملات میں، ماڈل کو مخصوص طبی ڈیٹا سیٹ کے ساتھ فائن ٹیون کرنا اس کی صلاحیت کو بہتر اور زیادہ قابل اعتماد بنا سکتا ہے۔ اس کے لیے ایک بڑا اور متعلقہ ڈیٹا سیٹ درکار ہوتا ہے جو شعبے کی مخصوص چیلنجز اور سوالات کی نمائندگی کرتا ہو۔

## اعلیٰ معیار کے AI سے چلنے والے چیٹ تجربے کے لیے غور و فکر

یہ سیکشن "اعلیٰ معیار" کی چیٹ ایپلیکیشنز کے معیار کو بیان کرتا ہے، جن میں قابل عمل میٹرکس کی گرفت اور AI ٹیکنالوجی کے ذمہ دارانہ استعمال کے لیے فریم ورک کی پابندی شامل ہے۔

### اہم میٹرکس

اعلیٰ معیار کی کارکردگی کو برقرار رکھنے کے لیے، ضروری ہے کہ کلیدی میٹرکس اور غور و فکر پر نظر رکھی جائے۔ یہ پیمائشیں نہ صرف ایپلیکیشن کی فعالیت کو یقینی بناتی ہیں بلکہ AI ماڈل اور صارف کے تجربے کے معیار کا بھی جائزہ لیتی ہیں۔ ذیل میں بنیادی، AI، اور صارف کے تجربے کے میٹرکس کی فہرست دی گئی ہے جن پر غور کرنا چاہیے۔

| میٹرک                         | تعریف                                                                                                               | چیٹ ڈویلپر کے لیے غور و فکر                                         |
| ----------------------------- | -------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------ |
| **اپ ٹائم**                   | وہ وقت جس میں ایپلیکیشن فعال اور صارفین کے لیے دستیاب ہوتی ہے۔                                                      | آپ ڈاؤن ٹائم کو کیسے کم کریں گے؟                                    |
| **ردعمل کا وقت**              | صارف کے سوال کا جواب دینے میں ایپلیکیشن کا لیا گیا وقت۔                                                              | آپ ردعمل کے وقت کو بہتر بنانے کے لیے سوالات کی پروسیسنگ کو کیسے بہتر بنائیں گے؟ |
| **درستگی (Precision)**        | درست مثبت پیش گوئیوں کا تناسب کل مثبت پیش گوئیوں میں۔                                                                | آپ اپنے ماڈل کی درستگی کو کیسے جانچیں گے؟                         |
| **یادداشت (Recall / Sensitivity)** | درست مثبت پیش گوئیوں کا تناسب اصل مثبت کیسز میں۔                                                                     | آپ یادداشت کو کیسے ناپیں گے اور بہتر بنائیں گے؟                  
| **انومالی ڈیٹیکشن**         | غیر معمولی پیٹرنز کی شناخت کے لیے ٹولز اور تکنیکیں جو متوقع رویے سے مطابقت نہیں رکھتیں۔                        | آپ انومالیز کا کیسے جواب دیں گے؟                                        |

### چیٹ ایپلیکیشنز میں ذمہ دار AI کے اصول نافذ کرنا

مائیکروسافٹ کے ذمہ دار AI کے نقطہ نظر نے چھ اصول متعین کیے ہیں جو AI کی ترقی اور استعمال کی رہنمائی کریں۔ نیچے اصول، ان کی تعریف، اور وہ چیزیں دی گئی ہیں جن پر چیٹ ڈویلپر کو غور کرنا چاہیے اور کیوں انہیں سنجیدگی سے لینا ضروری ہے۔

| اصول                  | مائیکروسافٹ کی تعریف                                  | چیٹ ڈویلپر کے لیے غور و فکر                                      | کیوں یہ اہم ہے                                                                     |
| ---------------------- | ----------------------------------------------------- | ---------------------------------------------------------------- | ---------------------------------------------------------------------------------- |
| انصاف                 | AI سسٹمز کو تمام لوگوں کے ساتھ منصفانہ سلوک کرنا چاہیے۔ | یقینی بنائیں کہ چیٹ ایپلیکیشن صارف کے ڈیٹا کی بنیاد پر امتیاز نہ کرے۔ | صارفین کے درمیان اعتماد اور شمولیت قائم کرنے کے لیے؛ قانونی مسائل سے بچاؤ کے لیے۔ |
| اعتبار اور حفاظت      | AI سسٹمز کو قابل اعتماد اور محفوظ طریقے سے کام کرنا چاہیے۔ | غلطیوں اور خطرات کو کم کرنے کے لیے ٹیسٹنگ اور فیل سیف نافذ کریں۔   | صارف کی اطمینان کو یقینی بناتا ہے اور ممکنہ نقصان سے بچاتا ہے۔                   |
| رازداری اور سیکیورٹی  | AI سسٹمز کو محفوظ اور رازداری کا احترام کرنے والا ہونا چاہیے۔ | مضبوط انکرپشن اور ڈیٹا پروٹیکشن کے اقدامات نافذ کریں۔             | حساس صارف ڈیٹا کی حفاظت اور رازداری کے قوانین کی تعمیل کے لیے۔                     |
| شمولیت                | AI سسٹمز کو ہر کسی کو بااختیار بنانا اور لوگوں کو شامل کرنا چاہیے۔ | مختلف صارفین کے لیے قابل رسائی اور آسان UI/UX ڈیزائن کریں۔         | اس بات کو یقینی بناتا ہے کہ زیادہ سے زیادہ لوگ ایپلیکیشن کو مؤثر طریقے سے استعمال کر سکیں۔ |
| شفافیت                | AI سسٹمز کو سمجھنے کے قابل ہونا چاہیے۔                  | AI کے جوابات کے لیے واضح دستاویزات اور وجوہات فراہم کریں۔          | اگر صارفین سمجھ سکیں کہ فیصلے کیسے کیے جاتے ہیں تو وہ سسٹم پر زیادہ اعتماد کرتے ہیں۔ |
| جوابدہی               | AI سسٹمز کے لیے لوگوں کو جوابدہ ہونا چاہیے۔              | AI کے فیصلوں کی جانچ اور بہتری کے لیے واضح عمل قائم کریں۔           | غلطیوں کی صورت میں مسلسل بہتری اور اصلاحی اقدامات ممکن بناتا ہے۔               |

## اسائنمنٹ

دیکھیں [assignment](../../../07-building-chat-applications/python) یہ آپ کو مختلف مشقوں کے ذریعے لے جائے گا، جیسے کہ آپ کے پہلے چیٹ پرامپٹس چلانا، متن کی درجہ بندی اور خلاصہ کرنا وغیرہ۔ نوٹ کریں کہ اسائنمنٹس مختلف پروگرامنگ زبانوں میں دستیاب ہیں!

## شاندار کام! سفر جاری رکھیں

اس سبق کو مکمل کرنے کے بعد، ہمارے [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) کو دیکھیں تاکہ آپ اپنی Generative AI کی معلومات کو مزید بڑھا سکیں!

سبق 8 پر جائیں تاکہ دیکھیں کہ آپ کیسے [سرچ ایپلیکیشنز بنا سکتے ہیں](../08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)!

**دستخطی دستبرداری**:  
یہ دستاویز AI ترجمہ سروس [Co-op Translator](https://github.com/Azure/co-op-translator) کے ذریعے ترجمہ کی گئی ہے۔ اگرچہ ہم درستگی کے لیے کوشاں ہیں، براہ کرم آگاہ رہیں کہ خودکار ترجمے میں غلطیاں یا عدم درستیاں ہو سکتی ہیں۔ اصل دستاویز اپنی مادری زبان میں ہی معتبر ماخذ سمجھی جانی چاہیے۔ اہم معلومات کے لیے پیشہ ور انسانی ترجمہ کی سفارش کی جاتی ہے۔ اس ترجمے کے استعمال سے پیدا ہونے والی کسی بھی غلط فہمی یا غلط تشریح کی ذمہ داری ہم پر عائد نہیں ہوتی۔