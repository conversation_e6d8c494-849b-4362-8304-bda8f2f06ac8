<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:26:14+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "ur"
}
-->
# نیورل نیٹ ورک فریم ورکس

جیسا کہ ہم نے پہلے ہی سیکھا ہے، نیورل نیٹ ورکس کو مؤثر طریقے سے ٹرین کرنے کے لیے ہمیں دو کام کرنے ہوتے ہیں:

* ٹینسرز پر آپریشن کرنا، مثلاً ضرب دینا، جمع کرنا، اور کچھ فنکشنز جیسے sigmoid یا softmax کا حساب لگانا  
* تمام اظہار کے گریڈینٹس کا حساب لگانا تاکہ gradient descent optimization کی جا سکے

اگرچہ `numpy` لائبریری پہلا کام کر سکتی ہے، ہمیں گریڈینٹس کا حساب لگانے کے لیے کوئی طریقہ چاہیے۔ ہمارے فریم ورک میں جو ہم نے پچھلے سیکشن میں تیار کیا تھا، ہمیں `backward` میتھڈ کے اندر تمام مشتق فنکشنز کو دستی طور پر پروگرام کرنا پڑتا تھا، جو backpropagation کرتا ہے۔ مثالی طور پر، ایک فریم ورک ہمیں *کسی بھی اظہار* کے گریڈینٹس کا حساب لگانے کا موقع دینا چاہیے جو ہم تعریف کر سکیں۔

ایک اور اہم بات یہ ہے کہ GPU یا کسی اور خاص کمپیوٹ یونٹس جیسے TPU پر کمپیوٹیشنز انجام دی جا سکیں۔ گہرے نیورل نیٹ ورک کی ٹریننگ میں *بہت زیادہ* کمپیوٹیشنز کی ضرورت ہوتی ہے، اور ان کمپیوٹیشنز کو GPUs پر متوازی طور پر چلانا بہت ضروری ہے۔

> ✅ لفظ 'parallelize' کا مطلب ہے کمپیوٹیشنز کو متعدد ڈیوائسز پر تقسیم کرنا۔

اس وقت، دو سب سے مقبول نیورل فریم ورکس ہیں: TensorFlow اور PyTorch۔ دونوں CPU اور GPU پر ٹینسرز کے ساتھ کام کرنے کے لیے ایک لو لیول API فراہم کرتے ہیں۔ لو لیول API کے اوپر، ایک ہائی لیول API بھی ہے، جسے بالترتیب Keras اور PyTorch Lightning کہا جاتا ہے۔

Low-Level API | TensorFlow | PyTorch  
--------------|-----------------------------|-----------------------------  
High-level API| Keras | PyTorch

**لو لیول APIs** دونوں فریم ورکس میں آپ کو **computational graphs** بنانے کی اجازت دیتے ہیں۔ یہ گراف بتاتا ہے کہ دیے گئے ان پٹ پیرامیٹرز کے ساتھ آؤٹ پٹ (عام طور پر loss function) کیسے حساب کیا جائے، اور اگر GPU دستیاب ہو تو اسے وہاں کمپیوٹ کے لیے بھیجا جا سکتا ہے۔ اس computational graph کو differentiate کرنے اور گریڈینٹس نکالنے کے فنکشنز بھی موجود ہیں، جنہیں ماڈل کے پیرامیٹرز کو optimize کرنے کے لیے استعمال کیا جا سکتا ہے۔

**ہائی لیول APIs** نیورل نیٹ ورکس کو ایک **layers کے سلسلے** کے طور پر دیکھتے ہیں، اور زیادہ تر نیورل نیٹ ورکس کی تعمیر کو بہت آسان بنا دیتے ہیں۔ ماڈل کی ٹریننگ عام طور پر ڈیٹا کی تیاری کے بعد `fit` فنکشن کال کرنے سے ہوتی ہے۔

ہائی لیول API آپ کو عام نیورل نیٹ ورکس بہت تیزی سے بنانے دیتا ہے بغیر بہت سی تفصیلات کی فکر کیے۔ اسی وقت، لو لیول API ٹریننگ کے عمل پر زیادہ کنٹرول دیتا ہے، اس لیے تحقیق میں جب آپ نئے نیورل نیٹ ورک آرکیٹیکچرز کے ساتھ کام کر رہے ہوں تو اسے زیادہ استعمال کیا جاتا ہے۔

یہ سمجھنا بھی ضروری ہے کہ آپ دونوں APIs کو ایک ساتھ استعمال کر سکتے ہیں، مثلاً آپ لو لیول API استعمال کرتے ہوئے اپنا نیٹ ورک لیئر آرکیٹیکچر تیار کر سکتے ہیں، اور پھر اسے ہائی لیول API کے ذریعے بنائے اور ٹرین کیے گئے بڑے نیٹ ورک میں استعمال کر سکتے ہیں۔ یا آپ ہائی لیول API کے ذریعے layers کے سلسلے کے طور پر نیٹ ورک ڈیفائن کر کے اپنی لو لیول ٹریننگ لوپ استعمال کر کے optimization کر سکتے ہیں۔ دونوں APIs ایک ہی بنیادی تصورات پر مبنی ہیں اور ایک ساتھ اچھی طرح کام کرنے کے لیے ڈیزائن کیے گئے ہیں۔

## سیکھنا

اس کورس میں، ہم زیادہ تر مواد PyTorch اور TensorFlow دونوں کے لیے فراہم کرتے ہیں۔ آپ اپنی پسند کا فریم ورک منتخب کر کے صرف متعلقہ نوٹ بکس سے گزر سکتے ہیں۔ اگر آپ کو یقین نہیں کہ کون سا فریم ورک منتخب کریں، تو انٹرنیٹ پر **PyTorch بمقابلہ TensorFlow** کے بارے میں کچھ مباحثے پڑھیں۔ آپ دونوں فریم ورکس کو دیکھ کر بہتر سمجھ بھی حاصل کر سکتے ہیں۔

جہاں ممکن ہو، ہم سادگی کے لیے ہائی لیول APIs استعمال کریں گے۔ تاہم، ہمارا ماننا ہے کہ نیورل نیٹ ورکس کو بنیادی سطح سے سمجھنا ضروری ہے، اس لیے ابتدا میں ہم لو لیول API اور ٹینسرز کے ساتھ کام شروع کرتے ہیں۔ اگر آپ جلدی شروع کرنا چاہتے ہیں اور تفصیلات سیکھنے میں زیادہ وقت نہیں لگانا چاہتے، تو آپ انہیں چھوڑ کر سیدھے ہائی لیول API نوٹ بکس پر جا سکتے ہیں۔

## ✍️ مشقیں: فریم ورکس

اپنی تعلیم درج ذیل نوٹ بکس میں جاری رکھیں:

Low-Level API | TensorFlow+Keras Notebook | PyTorch  
--------------|-----------------------------|-----------------------------  
High-level API| Keras | *PyTorch Lightning*

فریم ورکس پر عبور حاصل کرنے کے بعد، آئیں overfitting کے تصور کا جائزہ لیں۔

# اوورفٹنگ

اوورفٹنگ مشین لرننگ میں ایک انتہائی اہم تصور ہے، اور اسے صحیح سمجھنا بہت ضروری ہے!

مندرجہ ذیل مسئلہ پر غور کریں جس میں 5 نقاط (گراف میں `x` سے ظاہر کیے گئے) کی تقریب لگانی ہے:

!linear | overfit  
-------------------------|--------------------------  
**لکیری ماڈل، 2 پیرامیٹرز** | **غیر لکیری ماڈل، 7 پیرامیٹرز**  
ٹریننگ ایرر = 5.3 | ٹریننگ ایرر = 0  
ویلیڈیشن ایرر = 5.1 | ویلیڈیشن ایرر = 20

* بائیں طرف، ہم ایک اچھی سیدھی لائن کی تقریب دیکھتے ہیں۔ چونکہ پیرامیٹرز کی تعداد مناسب ہے، ماڈل نقاط کی تقسیم کا صحیح اندازہ لگا پاتا ہے۔  
* دائیں طرف، ماڈل بہت زیادہ طاقتور ہے۔ چونکہ ہمارے پاس صرف 5 نقاط ہیں اور ماڈل کے 7 پیرامیٹرز ہیں، یہ تمام نقاط سے گزرنے کے لیے خود کو ایڈجسٹ کر سکتا ہے، جس سے ٹریننگ ایرر صفر ہو جاتا ہے۔ تاہم، اس سے ماڈل ڈیٹا کے پیچھے صحیح پیٹرن کو سمجھنے سے قاصر رہتا ہے، اس لیے ویلیڈیشن ایرر بہت زیادہ ہوتا ہے۔

ماڈل کی پیچیدگی (پیرامیٹرز کی تعداد) اور ٹریننگ سیمپلز کی تعداد کے درمیان صحیح توازن قائم کرنا بہت ضروری ہے۔

## اوورفٹنگ کیوں ہوتی ہے

  * ٹریننگ ڈیٹا کی کمی  
  * ماڈل کا بہت زیادہ طاقتور ہونا  
  * ان پٹ ڈیٹا میں بہت زیادہ شور

## اوورفٹنگ کا پتہ کیسے لگائیں

جیسا کہ اوپر گراف میں دیکھا جا سکتا ہے، اوورفٹنگ کا پتہ بہت کم ٹریننگ ایرر اور زیادہ ویلیڈیشن ایرر سے چلتا ہے۔ عام طور پر ٹریننگ کے دوران ہم دونوں ٹریننگ اور ویلیڈیشن ایررز کو کم ہوتے دیکھتے ہیں، اور پھر کسی مقام پر ویلیڈیشن ایرر کم ہونا بند کر کے بڑھنا شروع ہو جاتا ہے۔ یہ اوورفٹنگ کی علامت ہوگی، اور اس بات کی نشاندہی کہ ہمیں شاید اس مقام پر ٹریننگ روک دینی چاہیے (یا کم از کم ماڈل کا snapshot بنا لینا چاہیے)۔

overfitting

## اوورفٹنگ کو کیسے روکا جائے

اگر آپ دیکھیں کہ اوورفٹنگ ہو رہی ہے، تو آپ درج ذیل میں سے کوئی ایک کر سکتے ہیں:

 * ٹریننگ ڈیٹا کی مقدار بڑھائیں  
 * ماڈل کی پیچیدگی کم کریں  
 * کوئی regularization تکنیک استعمال کریں، جیسے Dropout، جس پر ہم بعد میں غور کریں گے۔

## اوورفٹنگ اور Bias-Variance Tradeoff

اوورفٹنگ درحقیقت ایک عام شماریاتی مسئلہ Bias-Variance Tradeoff کی ایک صورت ہے۔ اگر ہم ماڈل میں ممکنہ غلطیوں کے ذرائع کو دیکھیں، تو دو قسم کی غلطیاں نظر آتی ہیں:

* **Bias errors** اس وجہ سے ہوتی ہیں کہ ہمارا الگورتھم ٹریننگ ڈیٹا کے تعلق کو صحیح طریقے سے نہیں سمجھ پاتا۔ یہ اس بات کی علامت ہو سکتی ہے کہ ہمارا ماڈل کافی طاقتور نہیں ہے (**underfitting**)۔  
* **Variance errors** اس وجہ سے ہوتی ہیں کہ ماڈل ان پٹ ڈیٹا کے شور کو حقیقی تعلق کی بجائے تقریباً سیکھ لیتا ہے (**overfitting**)۔

ٹریننگ کے دوران، bias error کم ہوتی ہے (جب ہمارا ماڈل ڈیٹا کی تقریب سیکھتا ہے)، اور variance error بڑھتی ہے۔ اوورفٹنگ کو روکنے کے لیے ٹریننگ کو روکنا ضروری ہے — یا دستی طور پر (جب اوورفٹنگ کا پتہ چل جائے) یا خودکار طریقے سے (regularization متعارف کروا کر)۔

## نتیجہ

اس سبق میں، آپ نے دو سب سے مقبول AI فریم ورکس، TensorFlow اور PyTorch، کے مختلف APIs کے فرق کے بارے میں سیکھا۔ اس کے علاوہ، آپ نے ایک بہت اہم موضوع، اوورفٹنگ، کے بارے میں بھی جانا۔

## 🚀 چیلنج

ساتھ والے نوٹ بکس میں، آپ کو نیچے 'tasks' ملیں گے؛ نوٹ بکس کو مکمل کریں اور ان ٹاسکس کو حل کریں۔

## جائزہ اور خود مطالعہ

مندرجہ ذیل موضوعات پر تحقیق کریں:

- TensorFlow  
- PyTorch  
- Overfitting

اپنے آپ سے یہ سوالات پوچھیں:

- TensorFlow اور PyTorch میں کیا فرق ہے؟  
- اوورفٹنگ اور انڈر فٹنگ میں کیا فرق ہے؟

## اسائنمنٹ

اس لیب میں، آپ سے کہا گیا ہے کہ PyTorch یا TensorFlow استعمال کرتے ہوئے single- اور multi-layered fully-connected نیٹ ورکس کے ذریعے دو classification مسائل حل کریں۔

**دستخطی نوٹ**:  
یہ دستاویز AI ترجمہ سروس [Co-op Translator](https://github.com/Azure/co-op-translator) کے ذریعے ترجمہ کی گئی ہے۔ اگرچہ ہم درستگی کے لیے کوشاں ہیں، براہ کرم آگاہ رہیں کہ خودکار ترجمے میں غلطیاں یا عدم درستیاں ہو سکتی ہیں۔ اصل دستاویز اپنی مادری زبان میں ہی معتبر ماخذ سمجھی جانی چاہیے۔ اہم معلومات کے لیے پیشہ ور انسانی ترجمہ کی سفارش کی جاتی ہے۔ اس ترجمے کے استعمال سے پیدا ہونے والی کسی بھی غلط فہمی یا غلط تشریح کی ذمہ داری ہم پر عائد نہیں ہوتی۔