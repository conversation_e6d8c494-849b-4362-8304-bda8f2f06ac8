<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:41:28+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "ur"
}
-->
# نیورل نیٹ ورکس کا تعارف۔ ملٹی-لیئرڈ پرسیپٹرون

پچھلے حصے میں، آپ نے سب سے سادہ نیورل نیٹ ورک ماڈل کے بارے میں سیکھا — ایک لیئر والا پرسیپٹرون، جو ایک خطی دو-کلاس درجہ بندی کا ماڈل ہے۔

اس حصے میں ہم اس ماڈل کو ایک زیادہ لچکدار فریم ورک میں تبدیل کریں گے، جو ہمیں اجازت دے گا کہ:

* دو کلاس کے علاوہ **کئی کلاسوں کی درجہ بندی** کریں
* درجہ بندی کے علاوہ **ریگریشن مسائل** حل کریں
* ایسی کلاسز کو الگ کریں جو خطی طور پر الگ نہیں کی جا سکتیں

ہم Python میں اپنا ایک ماڈیولر فریم ورک بھی تیار کریں گے جو ہمیں مختلف نیورل نیٹ ورک آرکیٹیکچرز بنانے کی سہولت دے گا۔

## مشین لرننگ کی رسمی تعریف

آئیے مشین لرننگ کے مسئلے کو رسمی شکل میں بیان کریں۔ فرض کریں ہمارے پاس ایک تربیتی ڈیٹا سیٹ **X** ہے جس کے لیبلز **Y** ہیں، اور ہمیں ایک ماڈل *f* بنانا ہے جو سب سے زیادہ درست پیش گوئیاں کرے۔ پیش گوئی کی کوالٹی کو **Loss function** ℒ سے ناپا جاتا ہے۔ درج ذیل نقصان کے افعال عام طور پر استعمال ہوتے ہیں:

* ریگریشن کے مسئلے کے لیے، جب ہمیں کوئی عدد پیش کرنا ہو، ہم **absolute error** ∑<sub>i</sub>|f(x<sup>(i)</sup>)-y<sup>(i)</sup>| یا **squared error** ∑<sub>i</sub>(f(x<sup>(i)</sup>)-y<sup>(i)</sup>)<sup>2</sup> استعمال کر سکتے ہیں
* درجہ بندی کے لیے، ہم **0-1 loss** (جو بنیادی طور پر ماڈل کی **accuracy** کے برابر ہے) یا **logistic loss** استعمال کرتے ہیں۔

ایک سطحی پرسیپٹرون کے لیے، فنکشن *f* کو ایک خطی فنکشن *f(x)=wx+b* کے طور پر بیان کیا گیا تھا (یہاں *w* وزن میٹرکس ہے، *x* ان پٹ فیچرز کا ویکٹر ہے، اور *b* بایس ویکٹر ہے)۔ مختلف نیورل نیٹ ورک آرکیٹیکچرز کے لیے یہ فنکشن زیادہ پیچیدہ شکل اختیار کر سکتا ہے۔

> درجہ بندی کے معاملے میں، اکثر نیٹ ورک کے آؤٹ پٹ کے طور پر متعلقہ کلاسز کے امکانات حاصل کرنا مطلوب ہوتا ہے۔ کسی بھی عدد کو امکانات میں تبدیل کرنے (مثلاً آؤٹ پٹ کو نارملائز کرنے) کے لیے، ہم اکثر **softmax** فنکشن σ استعمال کرتے ہیں، اور فنکشن *f* بن جاتا ہے *f(x)=σ(wx+b)*

اوپر *f* کی تعریف میں، *w* اور *b* کو **parameters** θ=⟨*w,b*⟩ کہا جاتا ہے۔ دیے گئے ڈیٹا سیٹ ⟨**X**,**Y**⟩ کے لیے، ہم پورے ڈیٹا سیٹ پر کل غلطی کو پیرامیٹرز θ کے فنکشن کے طور پر حساب کر سکتے ہیں۔

> ✅ **نیورل نیٹ ورک کی تربیت کا مقصد پیرامیٹرز θ کو تبدیل کر کے غلطی کو کم سے کم کرنا ہے**

## گریڈینٹ ڈیسینٹ آپٹیمائزیشن

ایک معروف فنکشن آپٹیمائزیشن طریقہ ہے جسے **gradient descent** کہتے ہیں۔ اس کا خیال یہ ہے کہ ہم نقصان کے فنکشن کا پیرامیٹرز کے لحاظ سے مشتق (کئی جہتی صورت میں اسے **gradient** کہتے ہیں) نکال سکتے ہیں، اور پیرامیٹرز کو اس طرح تبدیل کریں کہ غلطی کم ہو جائے۔ اسے یوں رسمی شکل دی جا سکتی ہے:

* پیرامیٹرز کو کچھ تصادفی قیمتوں سے شروع کریں w<sup>(0)</sup>, b<sup>(0)</sup>
* درج ذیل قدم کو کئی بار دہرائیں:
    - w<sup>(i+1)</sup> = w<sup>(i)</sup>-η∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup>-η∂ℒ/∂b

تربیت کے دوران، آپٹیمائزیشن کے مراحل پورے ڈیٹا سیٹ کو مدنظر رکھتے ہوئے حساب کیے جاتے ہیں (یاد رکھیں کہ نقصان تمام تربیتی نمونوں کے مجموعے کے طور پر ناپا جاتا ہے)۔ تاہم، حقیقی زندگی میں ہم ڈیٹا کے چھوٹے حصے لیتے ہیں جنہیں **minibatches** کہتے ہیں، اور ان کے ذریعے gradients نکالتے ہیں۔ چونکہ ہر بار یہ حصہ تصادفی طور پر لیا جاتا ہے، اس طریقہ کو **stochastic gradient descent** (SGD) کہتے ہیں۔

## ملٹی-لیئرڈ پرسیپٹرونز اور بیک پروپیگیشن

جیسا کہ اوپر دیکھا گیا، ایک سطحی نیٹ ورک صرف خطی طور پر الگ ہونے والی کلاسز کو درجہ بند کر سکتا ہے۔ ایک زیادہ جامع ماڈل بنانے کے لیے، ہم نیٹ ورک کی کئی تہیں جوڑ سکتے ہیں۔ ریاضیاتی طور پر اس کا مطلب ہے کہ فنکشن *f* زیادہ پیچیدہ شکل اختیار کرے گا، اور اسے کئی مراحل میں حساب کیا جائے گا:
* z<sub>1</sub>=w<sub>1</sub>x+b<sub>1</sub>
* z<sub>2</sub>=w<sub>2</sub>α(z<sub>1</sub>)+b<sub>2</sub>
* f = σ(z<sub>2</sub>)

یہاں، α ایک **غیر خطی activation فنکشن** ہے، σ softmax فنکشن ہے، اور پیرامیٹرز θ=<*w<sub>1</sub>,b<sub>1</sub>,w<sub>2</sub>,b<sub>2</sub>*> ہیں۔

گریڈینٹ ڈیسینٹ الگورتھم وہی رہے گا، لیکن gradients کا حساب لگانا زیادہ مشکل ہو جائے گا۔ چین قاعدہ تفریق کے تحت، ہم مشتقات یوں نکال سکتے ہیں:

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ چین قاعدہ تفریق نقصان کے فنکشن کے پیرامیٹرز کے لحاظ سے مشتقات نکالنے کے لیے استعمال ہوتا ہے۔

نوٹ کریں کہ ان تمام اظہارات کا بائیں طرف کا حصہ ایک جیسا ہے، اس لیے ہم مؤثر طریقے سے مشتقات کو نقصان کے فنکشن سے شروع کر کے "پیچھے" کمپیوٹیشنل گراف کے ذریعے نکال سکتے ہیں۔ اس لیے ملٹی-لیئرڈ پرسیپٹرون کی تربیت کے طریقہ کو **backpropagation** یا 'backprop' کہتے ہیں۔

> TODO: تصویر کا حوالہ

> ✅ ہم اپنے نوٹ بک کے مثال میں backprop کو بہت تفصیل سے دیکھیں گے۔

## نتیجہ

اس سبق میں، ہم نے اپنی نیورل نیٹ ورک لائبریری بنائی، اور اسے ایک سادہ دو-بعدی درجہ بندی کے کام کے لیے استعمال کیا۔

## 🚀 چیلنج

ساتھ والے نوٹ بک میں، آپ اپنا فریم ورک بنائیں گے جو ملٹی-لیئرڈ پرسیپٹرونز کی تعمیر اور تربیت کے لیے ہوگا۔ آپ تفصیل سے دیکھ سکیں گے کہ جدید نیورل نیٹ ورکس کیسے کام کرتے ہیں۔

OwnFramework نوٹ بک پر جائیں اور اسے مکمل کریں۔

## جائزہ اور خود مطالعہ

Backpropagation ایک عام الگورتھم ہے جو AI اور ML میں استعمال ہوتا ہے، اسے مزید تفصیل سے پڑھنا فائدہ مند ہے۔

## اسائنمنٹ

اس لیب میں، آپ سے کہا گیا ہے کہ آپ اس سبق میں بنائے گئے فریم ورک کو استعمال کرتے ہوئے MNIST ہاتھ سے لکھے گئے اعداد کی درجہ بندی کا مسئلہ حل کریں۔

* ہدایات
* نوٹ بک

**دستخطی نوٹ**:  
یہ دستاویز AI ترجمہ سروس [Co-op Translator](https://github.com/Azure/co-op-translator) کے ذریعے ترجمہ کی گئی ہے۔ اگرچہ ہم درستگی کے لیے کوشاں ہیں، براہ کرم اس بات سے آگاہ رہیں کہ خودکار ترجمے میں غلطیاں یا عدم درستیاں ہو سکتی ہیں۔ اصل دستاویز اپنی مادری زبان میں ہی معتبر ماخذ سمجھی جانی چاہیے۔ اہم معلومات کے لیے پیشہ ور انسانی ترجمہ کی سفارش کی جاتی ہے۔ اس ترجمے کے استعمال سے پیدا ہونے والی کسی بھی غلط فہمی یا غلط تشریح کی ذمہ داری ہم پر عائد نہیں ہوتی۔