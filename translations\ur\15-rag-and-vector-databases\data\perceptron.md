<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "59021c5f419d3feda19075910a74280a",
  "translation_date": "2025-07-09T16:54:25+00:00",
  "source_file": "15-rag-and-vector-databases/data/perceptron.md",
  "language_code": "ur"
}
-->
# نیورل نیٹ ورکس کا تعارف: پرسیپٹرون

جدید نیورل نیٹ ورک کی طرح کچھ بنانے کی پہلی کوششوں میں سے ایک 1957 میں فرینک روزن بلیٹ نے کورنل ایروناٹیکل لیبارٹری سے کی تھی۔ یہ ایک ہارڈویئر امپلیمنٹیشن تھی جسے "Mark-1" کہا جاتا تھا، جو ابتدائی جیومیٹرک اشکال جیسے مثلث، مربع اور دائرے کو پہچاننے کے لیے ڈیزائن کی گئی تھی۔

|      |      |
|--------------|-----------|
|<img src='images/Rosenblatt-wikipedia.jpg' alt='Frank Rosenblatt'/> | <img src='images/Mark_I_perceptron_wikipedia.jpg' alt='The Mark 1 Perceptron' />|

> تصاویر ویکیپیڈیا سے

ان پٹ تصویر کو 20x20 فوٹو سیل ارے کے ذریعے ظاہر کیا گیا تھا، اس لیے نیورل نیٹ ورک کے پاس 400 ان پٹس اور ایک بائنری آؤٹ پٹ تھا۔ ایک سادہ نیٹ ورک میں ایک نیورون ہوتا تھا، جسے **threshold logic unit** بھی کہا جاتا ہے۔ نیورل نیٹ ورک کے وزن پوٹینشیومیٹرز کی طرح کام کرتے تھے جنہیں تربیتی مرحلے کے دوران دستی طور پر ایڈجسٹ کرنا پڑتا تھا۔

> ✅ پوٹینشیومیٹر ایک ایسا آلہ ہے جو صارف کو سرکٹ کی مزاحمت کو ایڈجسٹ کرنے کی اجازت دیتا ہے۔

> نیو یارک ٹائمز نے اس وقت پرسیپٹرون کے بارے میں لکھا: *ایک الیکٹرانک کمپیوٹر کا ابتدائی نمونہ جس کے بارے میں [نیوی] توقع کرتی ہے کہ یہ چل سکے گا، بات کر سکے گا، دیکھ سکے گا، لکھ سکے گا، خود کو دوبارہ پیدا کر سکے گا اور اپنی موجودگی کا شعور رکھے گا۔*

## پرسیپٹرون ماڈل

فرض کریں کہ ہمارے ماڈل میں N خصوصیات ہیں، ایسی صورت میں ان پٹ ویکٹر کا سائز N ہوگا۔ پرسیپٹرون ایک **بائنری کلاسیفیکیشن** ماڈل ہے، یعنی یہ ان پٹ ڈیٹا کی دو کلاسوں میں تمیز کر سکتا ہے۔ ہم فرض کریں گے کہ ہر ان پٹ ویکٹر x کے لیے ہمارے پرسیپٹرون کا آؤٹ پٹ یا تو +1 ہوگا یا -1، کلاس کے مطابق۔ آؤٹ پٹ درج ذیل فارمولا سے حساب کیا جائے گا:

y(x) = f(w<sup>T</sup>x)

جہاں f ایک قدمی ایکٹیویشن فنکشن ہے

## پرسیپٹرون کی تربیت

پرسیپٹرون کو تربیت دینے کے لیے ہمیں ایک وزن ویکٹر w تلاش کرنا ہوتا ہے جو زیادہ تر اقدار کو درست طریقے سے درجہ بند کرے، یعنی سب سے کم **غلطی** پیدا کرے۔ یہ غلطی **perceptron criterion** کے ذریعے یوں بیان کی جاتی ہے:

E(w) = -∑w<sup>T</sup>x<sub>i</sub>t<sub>i</sub>

جہاں:

* یہ مجموعہ ان تربیتی ڈیٹا پوائنٹس i پر لیا جاتا ہے جن کی درجہ بندی غلط ہوئی ہو
* x<sub>i</sub> ان پٹ ڈیٹا ہے، اور t<sub>i</sub> منفی اور مثبت مثالوں کے لیے بالترتیب -1 یا +1 ہوتا ہے۔

یہ معیار وزن w کے فنکشن کے طور پر سمجھا جاتا ہے، اور ہمیں اسے کم سے کم کرنا ہوتا ہے۔ اکثر، ایک طریقہ جسے **gradient descent** کہا جاتا ہے استعمال کیا جاتا ہے، جس میں ہم ابتدائی وزن w<sup>(0)</sup> سے شروع کرتے ہیں، اور پھر ہر قدم پر وزن کو درج ذیل فارمولا کے مطابق اپ ڈیٹ کرتے ہیں:

w<sup>(t+1)</sup> = w<sup>(t)</sup> - η∇E(w)

یہاں η کو **learning rate** کہا جاتا ہے، اور ∇E(w) E کا **گریڈینٹ** ظاہر کرتا ہے۔ گریڈینٹ کا حساب لگانے کے بعد ہمیں ملتا ہے:

w<sup>(t+1)</sup> = w<sup>(t)</sup> + ∑ηx<sub>i</sub>t<sub>i</sub>

پائتھن میں الگورتھم کچھ یوں دکھائی دیتا ہے:

```python
def train(positive_examples, negative_examples, num_iterations = 100, eta = 1):

    weights = [0,0,0] # Initialize weights (almost randomly :)
        
    for i in range(num_iterations):
        pos = random.choice(positive_examples)
        neg = random.choice(negative_examples)

        z = np.dot(pos, weights) # compute perceptron output
        if z < 0: # positive example classified as negative
            weights = weights + eta*weights.shape

        z  = np.dot(neg, weights)
        if z >= 0: # negative example classified as positive
            weights = weights - eta*weights.shape

    return weights
```

## نتیجہ

اس سبق میں، آپ نے پرسیپٹرون کے بارے میں سیکھا، جو ایک بائنری کلاسیفیکیشن ماڈل ہے، اور اسے وزن ویکٹر کے ذریعے تربیت دینے کا طریقہ جانا۔

## 🚀 چیلنج

اگر آپ اپنا پرسیپٹرون بنانے کی کوشش کرنا چاہتے ہیں، تو Microsoft Learn پر یہ لیب آزمائیں جو Azure ML designer استعمال کرتی ہے۔

## جائزہ اور خود مطالعہ

یہ دیکھنے کے لیے کہ ہم پرسیپٹرون کو کھلونا مسئلہ اور حقیقی زندگی کے مسائل حل کرنے کے لیے کیسے استعمال کر سکتے ہیں، اور مزید سیکھنے کے لیے - Perceptron نوٹ بک پر جائیں۔

یہاں پرسیپٹرونز کے بارے میں ایک دلچسپ مضمون بھی موجود ہے۔

## اسائنمنٹ

اس سبق میں، ہم نے بائنری کلاسیفیکیشن کے کام کے لیے پرسیپٹرون کو نافذ کیا، اور اسے دو ہاتھ سے لکھے گئے اعداد کی درجہ بندی کے لیے استعمال کیا۔ اس لیب میں، آپ سے کہا گیا ہے کہ آپ عدد کی درجہ بندی کا مسئلہ مکمل طور پر حل کریں، یعنی یہ تعین کریں کہ دی گئی تصویر کے لیے کون سا عدد سب سے زیادہ ممکن ہے۔

* ہدایات
* نوٹ بک

**دستخطی نوٹ**:  
یہ دستاویز AI ترجمہ سروس [Co-op Translator](https://github.com/Azure/co-op-translator) کے ذریعے ترجمہ کی گئی ہے۔ اگرچہ ہم درستگی کے لیے کوشاں ہیں، براہ کرم اس بات سے آگاہ رہیں کہ خودکار ترجمے میں غلطیاں یا عدم درستیاں ہو سکتی ہیں۔ اصل دستاویز اپنی مادری زبان میں ہی معتبر ماخذ سمجھی جانی چاہیے۔ اہم معلومات کے لیے پیشہ ور انسانی ترجمہ کی سفارش کی جاتی ہے۔ اس ترجمے کے استعمال سے پیدا ہونے والی کسی بھی غلط فہمی یا غلط تشریح کی ذمہ داری ہم پر عائد نہیں ہوتی۔