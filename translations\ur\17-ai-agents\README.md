<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "11f03c81f190d9cbafd0f977dcbede6c",
  "translation_date": "2025-07-09T17:19:30+00:00",
  "source_file": "17-ai-agents/README.md",
  "language_code": "ur"
}
-->
[![Open Source Models](../../../translated_images/17-lesson-banner.a5b918fb0920e4e6d8d391a100f5cb1d5929f4c2752c937d40392905dec82592.ur.png)](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst)

## تعارف

AI Agents جنریٹو AI میں ایک دلچسپ پیش رفت کی نمائندگی کرتے ہیں، جو Large Language Models (LLMs) کو اسسٹنٹس سے ایسے ایجنٹس میں تبدیل کرنے کے قابل بناتے ہیں جو عمل انجام دے سکیں۔ AI Agent فریم ورکس ڈویلپرز کو ایسی ایپلیکیشنز بنانے کی سہولت دیتے ہیں جو LLMs کو ٹولز اور اسٹیٹ مینجمنٹ تک رسائی فراہم کرتی ہیں۔ یہ فریم ورکس نظر آنے کی صلاحیت کو بھی بہتر بناتے ہیں، جس سے صارفین اور ڈویلپرز LLMs کے منصوبہ بند اقدامات کی نگرانی کر سکتے ہیں، اور یوں تجربے کے انتظام کو بہتر بنایا جا سکتا ہے۔

اس سبق میں درج ذیل موضوعات شامل ہوں گے:

- AI Agent کیا ہے - AI Agent بالکل کیا ہوتا ہے؟
- چار مختلف AI Agent فریم ورکس کا جائزہ - انہیں منفرد کیا بناتا ہے؟
- ان AI Agents کو مختلف استعمال کے کیسز میں لاگو کرنا - ہمیں AI Agents کب استعمال کرنے چاہئیں؟

## سیکھنے کے مقاصد

اس سبق کے بعد، آپ قابل ہوں گے کہ:

- وضاحت کریں کہ AI Agents کیا ہیں اور انہیں کیسے استعمال کیا جا سکتا ہے۔
- کچھ مشہور AI Agent فریم ورکس کے درمیان فرق کو سمجھیں اور یہ کیسے مختلف ہیں۔
- سمجھیں کہ AI Agents کیسے کام کرتے ہیں تاکہ آپ ان کے ساتھ ایپلیکیشنز بنا سکیں۔

## AI Agents کیا ہیں؟

AI Agents جنریٹو AI کی دنیا میں ایک بہت دلچسپ میدان ہیں۔ اس دلچسپی کے ساتھ کبھی کبھار اصطلاحات اور ان کے استعمال میں الجھن بھی ہوتی ہے۔ چیزوں کو آسان اور زیادہ تر ایسے ٹولز کو شامل رکھنے کے لیے جو AI Agents کہلاتے ہیں، ہم یہ تعریف استعمال کریں گے:

AI Agents Large Language Models (LLMs) کو **اسٹیٹ** اور **ٹولز** تک رسائی دے کر کام انجام دینے کے قابل بناتے ہیں۔

![Agent Model](../../../translated_images/what-agent.21f2893bdfd01e6a7fd09b0416c2b15594d97f44bbb2ab5a1ff8bf643d2fcb3d.ur.png)

آئیے ان اصطلاحات کی وضاحت کرتے ہیں:

**Large Language Models** - یہ وہ ماڈلز ہیں جن کا حوالہ اس کورس میں دیا گیا ہے جیسے GPT-3.5، GPT-4، Llama-2 وغیرہ۔

**State** - اس سے مراد وہ سیاق و سباق ہے جس میں LLM کام کر رہا ہوتا ہے۔ LLM اپنے ماضی کے اقدامات اور موجودہ سیاق و سباق کو استعمال کرتا ہے تاکہ آئندہ کے اقدامات کے لیے فیصلہ سازی کی رہنمائی کرے۔ AI Agent فریم ورکس ڈویلپرز کو اس سیاق و سباق کو آسانی سے برقرار رکھنے کی سہولت دیتے ہیں۔

**Tools** - صارف کی درخواست کردہ اور LLM کے منصوبہ بند کام کو مکمل کرنے کے لیے، LLM کو ٹولز تک رسائی کی ضرورت ہوتی ہے۔ ٹولز کی مثالوں میں ڈیٹا بیس، API، بیرونی ایپلیکیشن یا حتیٰ کہ کوئی اور LLM شامل ہو سکتا ہے!

یہ تعریفیں آپ کو آگے بڑھنے کے لیے ایک مضبوط بنیاد فراہم کریں گی جب ہم دیکھیں گے کہ انہیں کیسے نافذ کیا جاتا ہے۔ آئیے چند مختلف AI Agent فریم ورکس کا جائزہ لیتے ہیں:

## LangChain Agents

[LangChain Agents](https://python.langchain.com/docs/how_to/#agents?WT.mc_id=academic-105485-koreyst) وہ نفاذ ہے جو ہم نے اوپر دی گئی تعریفوں کے مطابق کیا ہے۔

**state** کو منظم کرنے کے لیے، یہ ایک بلٹ ان فنکشن `AgentExecutor` استعمال کرتا ہے۔ یہ متعین `agent` اور دستیاب `tools` کو قبول کرتا ہے۔

`Agent Executor` چیٹ ہسٹری کو بھی محفوظ کرتا ہے تاکہ چیٹ کے سیاق و سباق کو فراہم کیا جا سکے۔

![Langchain Agents](../../../translated_images/langchain-agents.edcc55b5d5c437169a2037211284154561183c58bcec6d4ac2f8a79046fac9af.ur.png)

LangChain ایک [ٹولز کا کیٹلاگ](https://integrations.langchain.com/tools?WT.mc_id=academic-105485-koreyst) پیش کرتا ہے جسے آپ اپنی ایپلیکیشن میں درآمد کر سکتے ہیں تاکہ LLM ان تک رسائی حاصل کر سکے۔ یہ ٹولز کمیونٹی اور LangChain ٹیم کی جانب سے بنائے گئے ہیں۔

آپ پھر ان ٹولز کو متعین کر کے `Agent Executor` کو دے سکتے ہیں۔

AI Agents کی بات کرتے ہوئے نظر آنے کی صلاحیت بھی ایک اہم پہلو ہے۔ ایپلیکیشن ڈویلپرز کے لیے یہ سمجھنا ضروری ہے کہ LLM کون سا ٹول استعمال کر رہا ہے اور کیوں۔ اسی لیے LangChain کی ٹیم نے LangSmith تیار کیا ہے۔

## AutoGen

اگلا AI Agent فریم ورک جس پر ہم بات کریں گے وہ [AutoGen](https://microsoft.github.io/autogen/?WT.mc_id=academic-105485-koreyst) ہے۔ AutoGen کا مرکزی فوکس گفتگو ہے۔ ایجنٹس دونوں **بات چیت کرنے کے قابل** اور **حسب ضرورت** ہوتے ہیں۔

**Conversable -** LLMs ایک دوسرے LLM کے ساتھ بات چیت شروع اور جاری رکھ سکتے ہیں تاکہ کوئی کام مکمل کیا جا سکے۔ یہ `AssistantAgents` بنا کر اور انہیں مخصوص سسٹم میسج دے کر کیا جاتا ہے۔

```python

autogen.AssistantAgent( name="Coder", llm_config=llm_config, ) pm = autogen.AssistantAgent( name="Product_manager", system_message="Creative in software product ideas.", llm_config=llm_config, )

```

**Customizable** - ایجنٹس نہ صرف LLMs کے طور پر بلکہ صارف یا ٹول کے طور پر بھی متعین کیے جا سکتے ہیں۔ بطور ڈویلپر، آپ `UserProxyAgent` متعین کر سکتے ہیں جو صارف سے فیڈبیک لینے کا ذمہ دار ہوتا ہے تاکہ کام مکمل کیا جا سکے۔ یہ فیڈبیک کام کی انجام دہی جاری رکھنے یا روکنے کے لیے ہو سکتا ہے۔

```python
user_proxy = UserProxyAgent(name="user_proxy")
```

### State اور Tools

اسٹیٹ کو تبدیل اور منظم کرنے کے لیے، ایک اسسٹنٹ ایجنٹ Python کوڈ تیار کرتا ہے تاکہ کام مکمل کیا جا سکے۔

یہاں اس عمل کی ایک مثال ہے:

![AutoGen](../../../translated_images/autogen.dee9a25a45fde584fedd84b812a6e31de5a6464687cdb66bb4f2cb7521391856.ur.png)

#### LLM کو سسٹم میسج کے ساتھ متعین کرنا

```python
system_message="For weather related tasks, only use the functions you have been provided with. Reply TERMINATE when the task is done."
```

یہ سسٹم میسج اس مخصوص LLM کو ہدایت دیتا ہے کہ کون سے فنکشن اس کے کام کے لیے متعلقہ ہیں۔ یاد رکھیں، AutoGen کے ساتھ آپ مختلف سسٹم میسجز کے ساتھ متعدد AssistantAgents متعین کر سکتے ہیں۔

#### چیٹ صارف کی جانب سے شروع کی جاتی ہے

```python
user_proxy.initiate_chat( chatbot, message="I am planning a trip to NYC next week, can you help me pick out what to wear? ", )

```

یہ پیغام user_proxy (انسان) کی جانب سے ایجنٹ کے عمل کو شروع کرے گا تاکہ وہ ممکنہ فنکشنز کو دریافت کرے جو اسے چلانے چاہئیں۔

#### فنکشن کا اجرا

```bash
chatbot (to user_proxy):

***** Suggested tool Call: get_weather ***** Arguments: {"location":"New York City, NY","time_periond:"7","temperature_unit":"Celsius"} ******************************************************** --------------------------------------------------------------------------------

>>>>>>>> EXECUTING FUNCTION get_weather... user_proxy (to chatbot): ***** Response from calling function "get_weather" ***** 112.22727272727272 EUR ****************************************************************

```

جب ابتدائی چیٹ پراسیس ہو جائے، ایجنٹ تجویز کردہ ٹول کو کال کرنے کے لیے بھیجے گا۔ اس مثال میں، یہ `get_weather` نامی فنکشن ہے۔ آپ کی ترتیب کے مطابق، یہ فنکشن خودکار طور پر ایجنٹ کے ذریعے چلایا جا سکتا ہے یا صارف کی ان پٹ کی بنیاد پر۔

آپ مزید جاننے کے لیے [AutoGen کوڈ نمونے](https://microsoft.github.io/autogen/docs/Examples/?WT.mc_id=academic-105485-koreyst) دیکھ سکتے ہیں تاکہ شروع کرنے کا طریقہ سمجھ سکیں۔

## Taskweaver

اگلا ایجنٹ فریم ورک جس کا ہم جائزہ لیں گے وہ [Taskweaver](https://microsoft.github.io/TaskWeaver/?WT.mc_id=academic-105485-koreyst) ہے۔ اسے "code-first" ایجنٹ کہا جاتا ہے کیونکہ یہ صرف `strings` کے ساتھ کام کرنے کے بجائے Python میں DataFrames کے ساتھ کام کر سکتا ہے۔ یہ ڈیٹا تجزیہ اور جنریشن کے کاموں کے لیے بہت مفید ہے، جیسے گراف اور چارٹس بنانا یا تصادفی اعداد پیدا کرنا۔

### State اور Tools

گفتگو کی اسٹیٹ کو منظم کرنے کے لیے، TaskWeaver `Planner` کے تصور کا استعمال کرتا ہے۔ `Planner` ایک LLM ہے جو صارفین کی درخواست لیتا ہے اور ان کاموں کا نقشہ بناتا ہے جو اس درخواست کو پورا کرنے کے لیے مکمل کیے جانے ہیں۔

کام مکمل کرنے کے لیے `Planner` کو `Plugins` کے مجموعے تک رسائی دی جاتی ہے۔ یہ Python کلاسز یا عمومی کوڈ انٹرپریٹر ہو سکتے ہیں۔ یہ پلگ انز embeddings کی صورت میں محفوظ کیے جاتے ہیں تاکہ LLM بہتر طریقے سے درست پلگ ان تلاش کر سکے۔

![Taskweaver](../../../translated_images/taskweaver.da8559999267715a95b7677cf9b7d7dd8420aee6f3c484ced1833f081988dcd5.ur.png)

یہاں anomaly detection کو ہینڈل کرنے کے لیے ایک پلگ ان کی مثال ہے:

```python
class AnomalyDetectionPlugin(Plugin): def __call__(self, df: pd.DataFrame, time_col_name: str, value_col_name: str):
```

کوڈ کو چلانے سے پہلے تصدیق کیا جاتا ہے۔ Taskweaver میں سیاق و سباق کو منظم کرنے کی ایک اور خصوصیت `experience` ہے۔ Experience گفتگو کے سیاق و سباق کو طویل مدتی طور پر YAML فائل میں محفوظ کرنے کی اجازت دیتا ہے۔ اسے اس طرح ترتیب دیا جا سکتا ہے کہ LLM مخصوص کاموں پر وقت کے ساتھ بہتر ہو جائے بشرطیکہ اسے پچھلی گفتگوؤں سے آگاہ کیا جائے۔

## JARVIS

آخری ایجنٹ فریم ورک جس کا ہم جائزہ لیں گے وہ [JARVIS](https://github.com/microsoft/JARVIS?tab=readme-ov-file?WT.mc_id=academic-105485-koreyst) ہے۔ JARVIS کی خاص بات یہ ہے کہ یہ گفتگو کی `state` کو منظم کرنے کے لیے ایک LLM استعمال کرتا ہے اور `tools` دیگر AI ماڈلز ہوتے ہیں۔ ہر AI ماڈل مخصوص کام انجام دینے کے لیے ماہر ہوتا ہے جیسے object detection، transcription یا image captioning۔

![JARVIS](../../../translated_images/jarvis.762ddbadbd1a3a3364d4ca3db1a7a9c0d2180060c0f8da6f7bd5b5ea2a115aa7.ur.png)

LLM، جو ایک جنرل پرپز ماڈل ہے، صارف کی درخواست وصول کرتا ہے اور مخصوص کام اور اس کے لیے درکار دلائل/ڈیٹا کی شناخت کرتا ہے۔

```python
[{"task": "object-detection", "id": 0, "dep": [-1], "args": {"image": "e1.jpg" }}]
```

پھر LLM درخواست کو اس انداز میں فارمیٹ کرتا ہے جسے ماہر AI ماڈل سمجھ سکے، جیسے JSON۔ جب AI ماڈل اپنے پیش گوئی کے ساتھ واپس آتا ہے، تو LLM جواب وصول کرتا ہے۔

اگر کام مکمل کرنے کے لیے متعدد ماڈلز کی ضرورت ہو، تو یہ ان ماڈلز کے جوابات کی بھی تشریح کرے گا اور پھر انہیں یکجا کر کے صارف کو جواب فراہم کرے گا۔

ذیل کی مثال دکھاتی ہے کہ جب صارف تصویر میں موجود اشیاء کی تفصیل اور تعداد طلب کرتا ہے تو یہ کیسے کام کرے گا:

## اسائنمنٹ

AI Agents کی اپنی سیکھ کو جاری رکھنے کے لیے آپ AutoGen کے ساتھ درج ذیل بنا سکتے ہیں:

- ایک ایسی ایپلیکیشن جو ایک تعلیمی اسٹارٹ اپ کے مختلف شعبہ جات کے ساتھ کاروباری میٹنگ کی نقل کرے۔
- سسٹم میسجز بنائیں جو LLMs کو مختلف شخصیات اور ترجیحات کو سمجھنے میں رہنمائی دیں، اور صارف کو نیا پروڈکٹ آئیڈیا پیش کرنے کے قابل بنائیں۔
- پھر LLM ہر شعبہ سے فالو اپ سوالات تیار کرے تاکہ پیشکش اور پروڈکٹ آئیڈیا کو بہتر اور نکھارا جا سکے۔

## سیکھنا یہاں ختم نہیں ہوتا، سفر جاری رکھیں

اس سبق کو مکمل کرنے کے بعد، ہمارے [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) کو دیکھیں تاکہ آپ اپنی Generative AI کی معلومات کو مزید بڑھا سکیں!

**دستخطی دستبرداری**:  
یہ دستاویز AI ترجمہ سروس [Co-op Translator](https://github.com/Azure/co-op-translator) کے ذریعے ترجمہ کی گئی ہے۔ اگرچہ ہم درستگی کے لیے کوشاں ہیں، براہ کرم آگاہ رہیں کہ خودکار ترجمے میں غلطیاں یا عدم درستیاں ہو سکتی ہیں۔ اصل دستاویز اپنی مادری زبان میں ہی معتبر ماخذ سمجھی جانی چاہیے۔ اہم معلومات کے لیے پیشہ ور انسانی ترجمہ کی سفارش کی جاتی ہے۔ اس ترجمے کے استعمال سے پیدا ہونے والی کسی بھی غلط فہمی یا غلط تشریح کی ذمہ داری ہم پر عائد نہیں ہوتی۔