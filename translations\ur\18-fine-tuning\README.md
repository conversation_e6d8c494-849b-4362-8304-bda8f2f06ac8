<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "68664f7e754a892ae1d8d5e2b7bd2081",
  "translation_date": "2025-07-09T17:36:22+00:00",
  "source_file": "18-fine-tuning/README.md",
  "language_code": "ur"
}
-->
[![Open Source Models](../../../translated_images/18-lesson-banner.f30176815b1a5074fce9cceba317720586caa99e24001231a92fd04eeb54a121.ur.png)](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst)

# اپنے LLM کی Fine-Tuning

بڑے زبان کے ماڈلز کو استعمال کرتے ہوئے جنریٹو AI ایپلیکیشنز بنانے کے ساتھ نئے چیلنجز آتے ہیں۔ ایک اہم مسئلہ یہ ہے کہ ماڈل کی طرف سے صارف کی درخواست کے لیے تیار کردہ مواد میں جواب کی کوالٹی (درستی اور مطابقت) کو یقینی بنایا جائے۔ پچھلے اسباق میں، ہم نے پرامپٹ انجینئرنگ اور retrieval-augmented generation جیسی تکنیکوں پر بات کی جو مسئلہ کو موجودہ ماڈل کے پرامپٹ ان پٹ کو _ترمیم_ کر کے حل کرنے کی کوشش کرتی ہیں۔

آج کے سبق میں، ہم تیسری تکنیک، **fine-tuning** پر بات کریں گے، جو اس چیلنج کو _ماڈل کو اضافی ڈیٹا کے ساتھ دوبارہ تربیت دے کر_ حل کرنے کی کوشش کرتی ہے۔ آئیے تفصیلات میں جائیں۔

## سیکھنے کے مقاصد

یہ سبق pre-trained زبان کے ماڈلز کے لیے fine-tuning کے تصور کا تعارف کراتا ہے، اس طریقہ کار کے فوائد اور چیلنجز کو دریافت کرتا ہے، اور آپ کے جنریٹو AI ماڈلز کی کارکردگی بہتر بنانے کے لیے fine-tuning کب اور کیسے استعمال کی جائے، اس کی رہنمائی فراہم کرتا ہے۔

اس سبق کے آخر تک، آپ درج ذیل سوالات کے جواب دے سکیں گے:

- زبان کے ماڈلز کے لیے fine-tuning کیا ہے؟
- fine-tuning کب اور کیوں مفید ہے؟
- میں pre-trained ماڈل کو کیسے fine-tune کر سکتا ہوں؟
- fine-tuning کی کیا حدود ہیں؟

تیار ہیں؟ چلیں شروع کرتے ہیں۔

## تصویری رہنمائی

کیا آپ اس سبق میں شامل موضوعات کا مجموعی جائزہ لینا چاہتے ہیں اس سے پہلے کہ ہم تفصیل میں جائیں؟ اس تصویری رہنمائی کو دیکھیں جو اس سبق کے سیکھنے کے سفر کو بیان کرتی ہے — fine-tuning کے بنیادی تصورات اور محرکات سے لے کر fine-tuning کے عمل اور بہترین طریقوں کو سمجھنے تک۔ یہ ایک دلچسپ موضوع ہے، اس لیے اپنی خود سے سیکھنے کے سفر کی مدد کے لیے اضافی لنکس کے لیے [Resources](./RESOURCES.md?WT.mc_id=academic-105485-koreyst) صفحہ ضرور دیکھیں!

![Illustrated Guide to Fine Tuning Language Models](../../../translated_images/18-fine-tuning-sketchnote.11b21f9ec8a703467a120cb79a28b5ac1effc8d8d9d5b31bbbac6b8640432e14.ur.png)

## زبان کے ماڈلز کے لیے fine-tuning کیا ہے؟

تعریف کے مطابق، بڑے زبان کے ماڈلز کو انٹرنیٹ سمیت مختلف ذرائع سے حاصل کردہ بڑی مقدار میں متن پر _pre-trained_ کیا جاتا ہے۔ جیسا کہ ہم نے پچھلے اسباق میں سیکھا، ہمیں ماڈل کے صارف کے سوالات ("prompts") کے جوابات کی کوالٹی بہتر بنانے کے لیے _prompt engineering_ اور _retrieval-augmented generation_ جیسی تکنیکوں کی ضرورت ہوتی ہے۔

ایک مقبول prompt-engineering تکنیک میں ماڈل کو جواب میں کیا توقع کی جاتی ہے اس بارے میں زیادہ رہنمائی دینا شامل ہے، چاہے وہ _ہدایات_ (واضح رہنمائی) کے ذریعے ہو یا _چند مثالیں دینا_ (مبہم رہنمائی) کے ذریعے۔ اسے _few-shot learning_ کہا جاتا ہے لیکن اس کے دو محدودات ہیں:

- ماڈل کے token کی حد کی وجہ سے آپ جتنی مثالیں دے سکتے ہیں اس پر پابندی ہوتی ہے، جو مؤثریت کو محدود کرتی ہے۔
- ماڈل کے token کے اخراجات کی وجہ سے ہر پرامپٹ میں مثالیں شامل کرنا مہنگا ہو سکتا ہے، اور لچک کو محدود کرتا ہے۔

fine-tuning مشین لرننگ سسٹمز میں ایک عام عمل ہے جہاں ہم pre-trained ماڈل کو نئی ڈیٹا کے ساتھ دوبارہ تربیت دیتے ہیں تاکہ کسی مخصوص کام پر اس کی کارکردگی بہتر ہو۔ زبان کے ماڈلز کے سیاق و سباق میں، ہم pre-trained ماڈل کو _کسی مخصوص کام یا ایپلیکیشن ڈومین کے لیے منتخب کردہ مثالوں کے سیٹ کے ساتھ fine-tune کر سکتے ہیں_ تاکہ ایک **custom ماڈل** بنایا جا سکے جو اس مخصوص کام یا ڈومین کے لیے زیادہ درست اور متعلقہ ہو۔ fine-tuning کا ایک ضمنی فائدہ یہ بھی ہے کہ یہ few-shot learning کے لیے درکار مثالوں کی تعداد کو کم کر سکتا ہے — جس سے token کے استعمال اور متعلقہ اخراجات کم ہوتے ہیں۔

## ہم کب اور کیوں ماڈلز کو fine-tune کریں؟

_اس_ سیاق میں، جب ہم fine-tuning کی بات کرتے ہیں، تو ہمارا مطلب ہے **supervised** fine-tuning جہاں دوبارہ تربیت **نئی ڈیٹا شامل کر کے** کی جاتی ہے جو اصل تربیتی ڈیٹا سیٹ کا حصہ نہیں تھی۔ یہ unsupervised fine-tuning سے مختلف ہے جہاں ماڈل کو اصل ڈیٹا پر دوبارہ تربیت دی جاتی ہے، لیکن مختلف hyperparameters کے ساتھ۔

اہم بات یہ ہے کہ fine-tuning ایک جدید تکنیک ہے جس کے لیے مطلوبہ نتائج حاصل کرنے کے لیے خاص مہارت درکار ہوتی ہے۔ اگر غلط طریقے سے کی جائے تو یہ متوقع بہتری فراہم نہیں کر سکتی، بلکہ آپ کے ہدف والے ڈومین کے لیے ماڈل کی کارکردگی کو خراب بھی کر سکتی ہے۔

لہٰذا، اس سے پہلے کہ آپ "کیسے" زبان کے ماڈلز کو fine-tune کریں، آپ کو یہ جاننا ضروری ہے کہ "کیوں" آپ کو یہ راستہ اختیار کرنا چاہیے، اور "کب" fine-tuning کا عمل شروع کرنا چاہیے۔ اپنے آپ سے یہ سوالات پوچھیں:

- **Use Case**: آپ کا fine-tuning کا _استعمال کا کیس_ کیا ہے؟ آپ موجودہ pre-trained ماڈل کے کس پہلو کو بہتر بنانا چاہتے ہیں؟
- **Alternatives**: کیا آپ نے _دیگر تکنیکیں_ آزما کر مطلوبہ نتائج حاصل کرنے کی کوشش کی ہے؟ ان کو موازنہ کے لیے بنیاد کے طور پر استعمال کریں۔
  - Prompt engineering: متعلقہ پرامپٹ جوابات کی مثالوں کے ساتھ few-shot prompting جیسی تکنیکیں آزمائیں۔ جوابات کی کوالٹی کا جائزہ لیں۔
  - Retrieval Augmented Generation: اپنے ڈیٹا کی تلاش سے حاصل کردہ سوالات کے نتائج کے ساتھ پرامپٹس کو بڑھائیں۔ جوابات کی کوالٹی کا جائزہ لیں۔
- **Costs**: کیا آپ نے fine-tuning کے اخراجات کی نشاندہی کی ہے؟
  - Tunability - کیا pre-trained ماڈل fine-tuning کے لیے دستیاب ہے؟
  - Effort - تربیتی ڈیٹا کی تیاری، ماڈل کی جانچ اور بہتر بنانے کے لیے محنت۔
  - Compute - fine-tuning کے کام چلانے اور fine-tuned ماڈل کو تعینات کرنے کے لیے کمپیوٹنگ وسائل۔
  - Data - fine-tuning کے اثر کے لیے معیاری مثالوں تک رسائی۔
- **Benefits**: کیا آپ نے fine-tuning کے فوائد کی تصدیق کی ہے؟
  - Quality - کیا fine-tuned ماڈل نے بنیاد سے بہتر کارکردگی دکھائی؟
  - Cost - کیا اس سے پرامپٹس کو آسان بنا کر token کے استعمال میں کمی آئی؟
  - Extensibility - کیا آپ بنیادی ماڈل کو نئے ڈومینز کے لیے دوبارہ استعمال کر سکتے ہیں؟

ان سوالات کے جواب دے کر، آپ فیصلہ کر سکیں گے کہ fine-tuning آپ کے استعمال کے کیس کے لیے صحیح طریقہ ہے یا نہیں۔ مثالی طور پر، یہ طریقہ تب ہی درست ہے جب فوائد اخراجات سے زیادہ ہوں۔ جب آپ آگے بڑھنے کا فیصلہ کر لیں، تو اب سوچیں کہ آپ pre-trained ماڈل کو _کیسے_ fine-tune کر سکتے ہیں۔

فیصلہ سازی کے عمل پر مزید بصیرت چاہتے ہیں؟ دیکھیں [To fine-tune or not to fine-tune](https://www.youtube.com/watch?v=0Jo-z-MFxJs)

## ہم pre-trained ماڈل کو کیسے fine-tune کر سکتے ہیں؟

pre-trained ماڈل کو fine-tune کرنے کے لیے آپ کو چاہیے:

- fine-tune کرنے کے لیے ایک pre-trained ماڈل
- fine-tuning کے لیے استعمال کرنے کے لیے ایک ڈیٹا سیٹ
- fine-tuning کا کام چلانے کے لیے تربیتی ماحول
- fine-tuned ماڈل کو تعینات کرنے کے لیے ہوسٹنگ ماحول

## Fine-Tuning عمل میں

مندرجہ ذیل وسائل آپ کو منتخب کردہ ماڈل اور منتخب کردہ ڈیٹا سیٹ کے ساتھ ایک حقیقی مثال کے ذریعے قدم بہ قدم رہنمائی فراہم کرتے ہیں۔ ان ٹیوٹوریلز پر کام کرنے کے لیے، آپ کو متعلقہ فراہم کنندہ پر اکاؤنٹ، اور متعلقہ ماڈل اور ڈیٹا سیٹس تک رسائی کی ضرورت ہوگی۔

| Provider     | Tutorial                                                                                                                                                                       | Description                                                                                                                                                                                                                                                                                                                                                                                                                        |
| ------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| OpenAI       | [How to fine-tune chat models](https://github.com/openai/openai-cookbook/blob/main/examples/How_to_finetune_chat_models.ipynb?WT.mc_id=academic-105485-koreyst)                | ایک مخصوص ڈومین ("recipe assistant") کے لیے `gpt-35-turbo` کو fine-tune کرنا سیکھیں، جس میں تربیتی ڈیٹا تیار کرنا، fine-tuning کا کام چلانا، اور fine-tuned ماڈل کو inference کے لیے استعمال کرنا شامل ہے۔                                                                                                                                                                                                                          |
| Azure OpenAI | [GPT 3.5 Turbo fine-tuning tutorial](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python-new%2Ccommand-line?WT.mc_id=academic-105485-koreyst) | Azure پر `gpt-35-turbo-0613` ماڈل کو fine-tune کرنا سیکھیں، جس میں تربیتی ڈیٹا بنانا اور اپلوڈ کرنا، fine-tuning کا کام چلانا، اور نئے ماڈل کو تعینات اور استعمال کرنا شامل ہے۔                                                                                                                                                                                                                                                     |
| Hugging Face | [Fine-tuning LLMs with Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | یہ بلاگ پوسٹ آپ کو _open LLM_ (مثلاً `CodeLlama 7B`) کو [transformers](https://huggingface.co/docs/transformers/index?WT.mc_id=academic-105485-koreyst) لائبریری اور [Transformer Reinforcement Learning (TRL)](https://huggingface.co/docs/trl/index?WT.mc_id=academic-105485-koreyst) کے ساتھ fine-tune کرنا سکھاتی ہے، اور Hugging Face پر دستیاب [datasets](https://huggingface.co/docs/datasets/index?WT.mc_id=academic-105485-koreyst) کا استعمال کرتی ہے۔ |
|              |                                                                                                                                                                                |                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| 🤗 AutoTrain | [Fine-tuning LLMs with AutoTrain](https://github.com/huggingface/autotrain-advanced/?WT.mc_id=academic-105485-koreyst)                                                         | AutoTrain (یا AutoTrain Advanced) ایک پائتھن لائبریری ہے جو Hugging Face نے تیار کی ہے اور جو مختلف کاموں کے لیے، بشمول LLM fine-tuning، آسان fine-tuning کی سہولت دیتی ہے۔ AutoTrain ایک no-code حل ہے اور fine-tuning آپ کے اپنے کلاؤڈ، Hugging Face Spaces یا لوکل مشین پر کی جا سکتی ہے۔ یہ ویب بیسڈ GUI، CLI، اور yaml کنفیگریشن فائلز کے ذریعے تربیت کی حمایت کرتا ہے۔                                                                                   |
|              |                                                                                                                                                                                |                                                                                                                                                                                                                                                                                                                                                                                                                                    |

## اسائنمنٹ

مندرجہ بالا ٹیوٹوریلز میں سے کسی ایک کو منتخب کریں اور اس پر عمل کریں۔ _ہم ان ٹیوٹوریلز کا ایک ورژن Jupyter Notebooks میں اس ریپو میں صرف حوالہ کے لیے بنا سکتے ہیں۔ تازہ ترین ورژنز کے لیے براہِ راست اصل ذرائع استعمال کریں۔_

## شاباش! اپنی تعلیم جاری رکھیں۔

اس سبق کو مکمل کرنے کے بعد، ہمارے [Generative AI Learning collection](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) کو دیکھیں تاکہ آپ اپنی جنریٹو AI کی معلومات کو مزید بہتر بنا سکیں!

مبارک ہو!! آپ نے اس کورس کی v2 سیریز کا آخری سبق مکمل کر لیا ہے! سیکھنا اور بنانا بند نہ کریں۔ \*\*صرف اس موضوع کے لیے اضافی تجاویز کی فہرست کے لیے [RESOURCES](RESOURCES.md?WT.mc_id=academic-105485-koreyst) صفحہ دیکھیں۔

ہماری v1 سیریز کے اسباق کو بھی مزید اسائنمنٹس اور تصورات کے ساتھ اپ ڈیٹ کیا گیا ہے۔ لہٰذا اپنی معلومات کو تازہ کرنے کے لیے ایک منٹ نکالیں — اور براہ کرم [اپنے سوالات اور تاثرات شیئر کریں](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) تاکہ ہم کمیونٹی کے لیے ان اسباق کو بہتر بنا سکیں۔

**دستخطی نوٹ**:  
یہ دستاویز AI ترجمہ سروس [Co-op Translator](https://github.com/Azure/co-op-translator) کے ذریعے ترجمہ کی گئی ہے۔ اگرچہ ہم درستگی کے لیے کوشاں ہیں، براہ کرم آگاہ رہیں کہ خودکار ترجمے میں غلطیاں یا عدم درستیاں ہو سکتی ہیں۔ اصل دستاویز اپنی مادری زبان میں معتبر ماخذ سمجھی جانی چاہیے۔ اہم معلومات کے لیے پیشہ ور انسانی ترجمہ کی سفارش کی جاتی ہے۔ اس ترجمے کے استعمال سے پیدا ہونے والی کسی بھی غلط فہمی یا غلط تشریح کی ذمہ داری ہم پر عائد نہیں ہوتی۔