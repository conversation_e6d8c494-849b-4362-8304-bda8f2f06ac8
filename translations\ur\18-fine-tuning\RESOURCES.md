<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:55:16+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "ur"
}
-->
# خود رہنمائی کے لیے وسائل

یہ سبق OpenAI اور Azure OpenAI کے بنیادی وسائل کو اصطلاحات اور ٹیوٹوریلز کے حوالے کے طور پر استعمال کرتے ہوئے تیار کیا گیا ہے۔ یہاں ایک غیر مکمل فہرست دی گئی ہے، جو آپ کی خود رہنمائی کے لیے مددگار ثابت ہو سکتی ہے۔

## 1. بنیادی وسائل

| عنوان/لنک                                                                                                                                                                                                                   | وضاحت                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [OpenAI ماڈلز کے ساتھ Fine-tuning](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning چند مثالوں پر مبنی سیکھنے کو بہتر بناتا ہے، کیونکہ یہ پرامپٹ میں فٹ ہونے والی مثالوں سے کہیں زیادہ ڈیٹا پر تربیت دیتا ہے، جس سے آپ کے اخراجات کم ہوتے ہیں، جوابات کی کوالٹی بہتر ہوتی ہے، اور درخواستوں کی تاخیر کم ہوتی ہے۔ **OpenAI سے fine-tuning کا جائزہ حاصل کریں۔**                                                                                    |
| [Azure OpenAI کے ساتھ Fine-Tuning کیا ہے؟](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | سمجھیں کہ **fine-tuning کیا ہے (تصور)**، آپ کو اس پر غور کیوں کرنا چاہیے (مسئلے کی وضاحت)، کون سا ڈیٹا استعمال کرنا ہے (تربیت) اور معیار کی پیمائش کیسے کی جاتی ہے۔                                                                                                                                                                           |
| [ماڈل کو fine-tuning کے ذریعے حسب ضرورت بنائیں](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI سروس آپ کو fine-tuning کے ذریعے اپنے ذاتی ڈیٹا سیٹس کے مطابق ماڈلز کو ڈھالنے کی اجازت دیتی ہے۔ جانیں کہ **fine-tuning کا عمل کیسے ہوتا ہے**، Azure AI Studio، Python SDK یا REST API کے ذریعے ماڈلز کا انتخاب کیسے کریں۔                                                                                                                                |
| [LLM fine-tuning کے لیے سفارشات](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | LLMs مخصوص شعبوں، کاموں یا ڈیٹا سیٹس پر اچھی کارکردگی نہیں دکھا سکتے، یا غلط یا گمراہ کن نتائج دے سکتے ہیں۔ **کب آپ کو fine-tuning پر غور کرنا چاہیے** اس مسئلے کے حل کے طور پر؟                                                                                                                                  |
| [مسلسل Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | مسلسل fine-tuning ایک تکراری عمل ہے جس میں پہلے سے fine-tuned ماڈل کو بنیادی ماڈل کے طور پر منتخب کیا جاتا ہے اور اسے نئے تربیتی ڈیٹا پر **مزید fine-tune کیا جاتا ہے**۔                                                                                                                                                     |
| [Fine-tuning اور function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | اپنے ماڈل کو **function calling کی مثالوں کے ساتھ fine-tune کرنا** ماڈل کے نتائج کو زیادہ درست اور مستقل بنا سکتا ہے - اسی طرح کے فارمیٹ میں جوابات اور لاگت کی بچت کے ساتھ۔                                                                                                                                        |
| [Fine-tuning ماڈلز: Azure OpenAI رہنمائی](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | اس جدول کو دیکھیں تاکہ سمجھ سکیں کہ Azure OpenAI میں **کون سے ماڈلز fine-tune کیے جا سکتے ہیں**، اور یہ کن علاقوں میں دستیاب ہیں۔ اگر ضرورت ہو تو ان کے ٹوکن کی حد اور تربیتی ڈیٹا کی میعاد ختم ہونے کی تاریخ بھی دیکھیں۔                                                                                                                            |
| [Fine Tune کرنا ہے یا نہیں؟ یہی سوال ہے](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | AI Show کا یہ 30 منٹ کا **اکتوبر 2023** کا قسط فوائد، نقصانات اور عملی بصیرتوں پر بات کرتا ہے جو آپ کو یہ فیصلہ کرنے میں مدد دیتی ہیں۔                                                                                                                                                                                        |
| [LLM Fine-Tuning کے ساتھ شروعات](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | یہ **AI Playbook** وسیلہ آپ کو ڈیٹا کی ضروریات، فارمیٹنگ، ہائپر پیرامیٹر fine-tuning اور چیلنجز/حدود کے بارے میں رہنمائی فراہم کرتا ہے جو آپ کو جاننی چاہیے۔                                                                                                                                                                         |
| **ٹیوٹوریل**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | ایک نمونہ fine-tuning ڈیٹا سیٹ بنانے، fine-tuning کی تیاری کرنے، fine-tuning کا کام تخلیق کرنے، اور Azure پر fine-tuned ماڈل کو تعینات کرنے کا طریقہ سیکھیں۔                                                                                                                                                                                    |
| **ٹیوٹوریل**: [Azure AI Studio میں Llama 2 ماڈل کو fine-tune کریں](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio آپ کو بڑے زبان کے ماڈلز کو اپنے ذاتی ڈیٹا سیٹس کے مطابق _ایک UI پر مبنی ورک فلو کے ذریعے جو کم کوڈنگ والے ڈویلپرز کے لیے موزوں ہے_ ڈھالنے کی اجازت دیتا ہے۔ اس مثال کو دیکھیں۔                                                                                                                                                               |
| **ٹیوٹوریل**: [Azure پر ایک GPU کے لیے Hugging Face ماڈلز کو fine-tune کریں](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | یہ مضمون بتاتا ہے کہ Azure DataBricks اور Hugging Face Trainer لائبریریز کے ساتھ ایک GPU پر Hugging Face ماڈل کو Hugging Face transformers لائبریری کے ذریعے fine-tune کیسے کیا جاتا ہے۔                                                                                                                                                |
| **تربیت:** [Azure Machine Learning کے ساتھ foundation ماڈل کو fine-tune کریں](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Azure Machine Learning کے ماڈل کیٹلاگ میں بہت سے اوپن سورس ماڈلز دستیاب ہیں جنہیں آپ اپنے مخصوص کام کے لیے fine-tune کر سکتے ہیں۔ یہ ماڈیول AzureML Generative AI Learning Path کا حصہ ہے۔                                                                                                                                 |
| **ٹیوٹوریل:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Microsoft Azure پر W&B کے ذریعے GPT-3.5 یا GPT-4 ماڈلز کی fine-tuning ماڈل کی کارکردگی کی تفصیلی نگرانی اور تجزیہ کی اجازت دیتی ہے۔ یہ گائیڈ OpenAI Fine-Tuning گائیڈ کے تصورات کو Azure OpenAI کے مخصوص مراحل اور خصوصیات کے ساتھ بڑھاتا ہے۔                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. ثانوی وسائل

یہ سیکشن اضافی وسائل پر مشتمل ہے جنہیں دریافت کرنا فائدہ مند ہے، لیکن جنہیں ہم نے اس سبق میں شامل کرنے کا وقت نہیں پایا۔ ممکن ہے کہ انہیں مستقبل کے کسی سبق میں یا ثانوی اسائنمنٹ کے طور پر شامل کیا جائے۔ فی الحال، ان کا استعمال اپنی مہارت اور علم کو بڑھانے کے لیے کریں۔

| عنوان/لنک                                                                                                                                                                                                            | وضاحت                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [چیٹ ماڈل fine-tuning کے لیے ڈیٹا کی تیاری اور تجزیہ](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | یہ نوٹ بک چیٹ ماڈل کی fine-tuning کے لیے استعمال ہونے والے چیٹ ڈیٹا سیٹ کو پری پروسیس اور تجزیہ کرنے کا آلہ ہے۔ یہ فارمیٹ کی غلطیوں کی جانچ کرتا ہے، بنیادی اعدادوشمار فراہم کرتا ہے، اور fine-tuning کے اخراجات کے لیے ٹوکن کی تعداد کا اندازہ لگاتا ہے۔ دیکھیں: [gpt-3.5-turbo کے لیے fine-tuning کا طریقہ](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)۔                                                                                                                                                                   |
| **OpenAI Cookbook**: [Retrieval Augmented Generation (RAG) کے لیے Qdrant کے ساتھ Fine-Tuning](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | اس نوٹ بک کا مقصد OpenAI ماڈلز کو Retrieval Augmented Generation (RAG) کے لیے fine-tune کرنے کی مکمل مثال پیش کرنا ہے۔ ہم Qdrant اور Few-Shot Learning کو بھی شامل کریں گے تاکہ ماڈل کی کارکردگی بہتر ہو اور غلط بیانی کم ہو۔                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [Weights & Biases کے ساتھ GPT کی Fine-tuning](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) ایک AI ڈویلپر پلیٹ فارم ہے، جو ماڈلز کی تربیت، fine-tuning، اور foundation ماڈلز کے استعمال کے لیے ٹولز فراہم کرتا ہے۔ پہلے ان کی [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) گائیڈ پڑھیں، پھر Cookbook کی مشق کریں۔                                                                                                                                                                                                                  |
| **کمیونٹی ٹیوٹوریل** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - چھوٹے زبان کے ماڈلز کے لیے fine-tuning                                                   | ملو [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst) سے، مائیکروسافٹ کا نیا چھوٹا ماڈل، جو حیرت انگیز طور پر طاقتور اور کمپیکٹ ہے۔ یہ ٹیوٹوریل آپ کو Phi-2 کو fine-tune کرنے کی رہنمائی کرے گا، ایک منفرد ڈیٹا سیٹ بنانے اور QLoRA کے ذریعے ماڈل کو fine-tune کرنے کا طریقہ دکھائے گا۔                                                                                                                                                                       |
| **Hugging Face ٹیوٹوریل** [2024 میں Hugging Face کے ساتھ LLMs کو fine-tune کیسے کریں](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | یہ بلاگ پوسٹ آپ کو دکھاتی ہے کہ 2024 میں Hugging Face TRL، Transformers اور ڈیٹا سیٹس کے ذریعے اوپن LLMs کو کیسے fine-tune کیا جائے۔ آپ ایک استعمال کا کیس متعین کرتے ہیں، ڈویلپمنٹ ماحول تیار کرتے ہیں، ڈیٹا سیٹ تیار کرتے ہیں، ماڈل fine-tune کرتے ہیں، اس کی جانچ اور تشخیص کرتے ہیں، اور پھر اسے پروڈکشن میں تعینات کرتے ہیں۔                                                                                                                                                                                                                                                                |
| **Hugging Face:** [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)                                                                                            | یہ [جدید مشین لرننگ ماڈلز](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst) کی تیز اور آسان تربیت اور تعیناتی فراہم کرتا ہے۔ ریپو میں Colab کے لیے دوستانہ ٹیوٹوریلز اور یوٹیوب ویڈیو گائیڈنس شامل ہیں، fine-tuning کے لیے۔ **یہ حالیہ [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst) اپ ڈیٹ کی عکاسی کرتا ہے۔** [AutoTrain دستاویزات](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) پڑھیں۔ |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**دستخطی نوٹ**:  
یہ دستاویز AI ترجمہ سروس [Co-op Translator](https://github.com/Azure/co-op-translator) کے ذریعے ترجمہ کی گئی ہے۔ اگرچہ ہم درستگی کے لیے کوشاں ہیں، براہ کرم آگاہ رہیں کہ خودکار ترجموں میں غلطیاں یا عدم درستیاں ہو سکتی ہیں۔ اصل دستاویز اپنی مادری زبان میں معتبر ماخذ سمجھی جانی چاہیے۔ اہم معلومات کے لیے پیشہ ور انسانی ترجمہ کی سفارش کی جاتی ہے۔ اس ترجمے کے استعمال سے پیدا ہونے والی کسی بھی غلط فہمی یا غلط تشریح کی ذمہ داری ہم پر عائد نہیں ہوتی۔