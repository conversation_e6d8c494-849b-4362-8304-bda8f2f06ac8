<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "5740c9b7fcb6919da1ea4df93475f331",
  "translation_date": "2025-07-09T06:20:16+00:00",
  "source_file": "README.md",
  "language_code": "ur"
}
-->
![Generative AI For Beginners](../../translated_images/repo-thumbnailv4-fixed.11f1ce6a85d01461c33c11943bb61f2b6d6dcce3a3b25cd27e627031f41f8e00.ur.png)

### 21 اسباق جو آپ کو Generative AI ایپلیکیشنز بنانے کے لیے درکار تمام معلومات سکھاتے ہیں

[![GitHub license](https://img.shields.io/github/license/microsoft/Generative-AI-For-Beginners.svg)](https://github.com/microsoft/Generative-AI-For-Beginners/blob/master/LICENSE?WT.mc_id=academic-105485-koreyst)
[![GitHub contributors](https://img.shields.io/github/contributors/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/graphs/contributors/?WT.mc_id=academic-105485-koreyst)
[![GitHub issues](https://img.shields.io/github/issues/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/issues/?WT.mc_id=academic-105485-koreyst)
[![GitHub pull-requests](https://img.shields.io/github/issues-pr/microsoft/Generative-AI-For-Beginners.svg)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/pulls/?WT.mc_id=academic-105485-koreyst)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square)](http://makeapullrequest.com?WT.mc_id=academic-105485-koreyst)

[![GitHub watchers](https://img.shields.io/github/watchers/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Watch)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/watchers/?WT.mc_id=academic-105485-koreyst)
[![GitHub forks](https://img.shields.io/github/forks/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Fork)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/network/?WT.mc_id=academic-105485-koreyst)
[![GitHub stars](https://img.shields.io/github/stars/microsoft/Generative-AI-For-Beginners.svg?style=social&label=Star)](https://GitHub.com/microsoft/Generative-AI-For-Beginners/stargazers/?WT.mc_id=academic-105485-koreyst)

[![](https://dcbadge.limes.pink/api/server/ByRwuEEgH4)](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst)

### 🌐 کثیراللسانی معاونت

#### GitHub Action کے ذریعے سپورٹ شدہ (خودکار اور ہمیشہ تازہ ترین)

[French](../fr/README.md) | [Spanish](../es/README.md) | [German](../de/README.md) | [Russian](../ru/README.md) | [Arabic](../ar/README.md) | [Persian (Farsi)](../fa/README.md) | [Urdu](./README.md) | [Chinese (Simplified)](../zh/README.md) | [Chinese (Traditional, Macau)](../mo/README.md) | [Chinese (Traditional, Hong Kong)](../hk/README.md) | [Chinese (Traditional, Taiwan)](../tw/README.md) | [Japanese](../ja/README.md) | [Korean](../ko/README.md) | [Hindi](../hi/README.md) | [Bengali](../bn/README.md) | [Marathi](../mr/README.md) | [Nepali](../ne/README.md) | [Punjabi (Gurmukhi)](../pa/README.md) | [Portuguese (Portugal)](../pt/README.md) | [Portuguese (Brazil)](../br/README.md) | [Italian](../it/README.md) | [Polish](../pl/README.md) | [Turkish](../tr/README.md) | [Greek](../el/README.md) | [Thai](../th/README.md) | [Swedish](../sv/README.md) | [Danish](../da/README.md) | [Norwegian](../no/README.md) | [Finnish](../fi/README.md) | [Dutch](../nl/README.md) | [Hebrew](../he/README.md) | [Vietnamese](../vi/README.md) | [Indonesian](../id/README.md) | [Malay](../ms/README.md) | [Tagalog (Filipino)](../tl/README.md) | [Swahili](../sw/README.md) | [Hungarian](../hu/README.md) | [Czech](../cs/README.md) | [Slovak](../sk/README.md) | [Romanian](../ro/README.md) | [Bulgarian](../bg/README.md) | [Serbian (Cyrillic)](../sr/README.md) | [Croatian](../hr/README.md) | [Slovenian](../sl/README.md) | [Ukrainian](../uk/README.md) | [Burmese (Myanmar)](../my/README.md)

# Generative AI for Beginners (ورژن 3) - ایک کورس

Microsoft Cloud Advocates کی جانب سے 21 اسباق پر مشتمل جامع کورس کے ذریعے Generative AI ایپلیکیشنز بنانے کے بنیادی اصول سیکھیں۔

## 🌱 شروعات

یہ کورس 21 اسباق پر مشتمل ہے۔ ہر سبق اپنا موضوع رکھتا ہے، لہٰذا جہاں چاہیں وہاں سے شروع کریں!

اسباق کو "Learn" یا "Build" کے طور پر لیبل کیا گیا ہے۔ "Learn" اسباق میں Generative AI کے تصورات کی وضاحت کی جاتی ہے جبکہ "Build" اسباق میں تصور کے ساتھ ساتھ **Python** اور **TypeScript** میں کوڈ کی مثالیں بھی دی جاتی ہیں جہاں ممکن ہو۔

.NET ڈویلپرز کے لیے [Generative AI for Beginners (.NET Edition)](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst) دیکھیں!

ہر سبق میں "Keep Learning" سیکشن بھی شامل ہے جس میں اضافی تعلیمی مواد موجود ہے۔

## آپ کو کیا چاہیے
### اس کورس کا کوڈ چلانے کے لیے آپ درج ذیل میں سے کوئی بھی استعمال کر سکتے ہیں:
 - [Azure OpenAI Service](https://aka.ms/genai-beginners/azure-open-ai?WT.mc_id=academic-105485-koreyst) - **اسباق:** "aoai-assignment"
 - [GitHub Marketplace Model Catalog](https://aka.ms/genai-beginners/gh-models?WT.mc_id=academic-105485-koreyst) - **اسباق:** "githubmodels"
 - [OpenAI API](https://aka.ms/genai-beginners/open-ai?WT.mc_id=academic-105485-koreyst) - **اسباق:** "oai-assignment" 
   
- Python یا TypeScript کی بنیادی معلومات مددگار ثابت ہوگی - \*بالکل نئے سیکھنے والوں کے لیے یہ [Python](https://aka.ms/genai-beginners/python?WT.mc_id=academic-105485-koreyst) اور [TypeScript](https://aka.ms/genai-beginners/typescript?WT.mc_id=academic-105485-koreyst) کورسز دیکھیں
- اپنا GitHub اکاؤنٹ تاکہ آپ اس پورے ریپوزٹری کو [fork](https://aka.ms/genai-beginners/github?WT.mc_id=academic-105485-koreyst) کر سکیں

ہم نے آپ کی ڈیولپمنٹ ماحول کی سیٹ اپ میں مدد کے لیے ایک **[Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)** سبق تیار کیا ہے۔

بعد میں آسانی سے تلاش کرنے کے لیے اس ریپو کو [star (🌟)](https://docs.github.com/en/get-started/exploring-projects-on-github/saving-repositories-with-stars?WT.mc_id=academic-105485-koreyst) کرنا نہ بھولیں۔

## 🧠 تعیناتی کے لیے تیار؟

اگر آپ مزید جدید کوڈ نمونے تلاش کر رہے ہیں تو ہمارے [Generative AI Code Samples](https://aka.ms/genai-beg-code?WT.mc_id=academic-105485-koreyst) کا مجموعہ دیکھیں جو **Python** اور **TypeScript** دونوں میں دستیاب ہے۔

## 🗣️ دوسرے سیکھنے والوں سے ملیں، مدد حاصل کریں

ہمارے [official Azure AI Foundry Discord server](https://aka.ms/genai-discord?WT.mc_id=academic-105485-koreyst) میں شامل ہوں تاکہ اس کورس کو کرنے والے دوسرے سیکھنے والوں سے مل سکیں اور مدد حاصل کر سکیں۔

سوالات پوچھیں یا مصنوعات کے بارے میں رائے ہمارے [Azure AI Foundry Developer Forum](https://aka.ms/azureaifoundry/forum) پر GitHub پر شیئر کریں۔

## 🚀 اسٹارٹ اپ بنا رہے ہیں؟

[Microsoft for Startups Founders Hub](https://aka.ms/genai-foundershub?WT.mc_id=academic-105485-koreyst) کے لیے سائن اپ کریں تاکہ **مفت OpenAI کریڈٹس** اور Azure OpenAI Services کے ذریعے OpenAI ماڈلز تک رسائی کے لیے **Azure کریڈٹس میں $150,000 تک** حاصل کر سکیں۔

## 🙏 مدد کرنا چاہتے ہیں؟

کیا آپ کے پاس تجاویز ہیں یا آپ نے املا یا کوڈ کی غلطیاں دیکھی ہیں؟ [مسئلہ اٹھائیں](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst) یا [پُل ریکویسٹ بنائیں](https://github.com/microsoft/generative-ai-for-beginners/pulls?WT.mc_id=academic-105485-koreyst)

## 📂 ہر سبق میں شامل ہے:

- موضوع کا مختصر ویڈیو تعارف
- README میں تحریری سبق
- Python اور TypeScript کوڈ کی مثالیں جو Azure OpenAI اور OpenAI API کی حمایت کرتی ہیں
- اضافی وسائل کے لنکس تاکہ آپ اپنی تعلیم جاری رکھ سکیں

## 🗃️ اسباق

| #   | **سبق کا لنک**                                                                                                                              | **تفصیل**                                                                                 | **ویڈیو**                                                                   | **اضافی تعلیم**                                                             |
| --- | -------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | -------------------------------------------------------------------------- |
| 00  | [Course Setup](./00-course-setup/README.md?WT.mc_id=academic-105485-koreyst)                                                                 | **Learn:** اپنا ڈیولپمنٹ ماحول کیسے سیٹ اپ کریں                                           | ویڈیو جلد آ رہا ہے                                                          | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 01  | [Introduction to Generative AI and LLMs](./01-introduction-to-genai/README.md?WT.mc_id=academic-105485-koreyst)                              | **Learn:** Generative AI کیا ہے اور Large Language Models (LLMs) کیسے کام کرتے ہیں۔       | [ویڈیو](https://aka.ms/gen-ai-lesson-1-gh?WT.mc_id=academic-105485-koreyst) | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 02  | [Exploring and comparing different LLMs](./02-exploring-and-comparing-different-llms/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** اپنے استعمال کے لیے صحیح ماڈل کا انتخاب کیسے کریں                              | [ویڈیو](https://aka.ms/gen-ai-lesson2-gh?WT.mc_id=academic-105485-koreyst)  | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 03  | [Using Generative AI Responsibly](./03-using-generative-ai-responsibly/README.md?WT.mc_id=academic-105485-koreyst)                           | **Learn:** Generative AI ایپلیکیشنز ذمہ داری سے کیسے بنائیں                              | [ویڈیو](https://aka.ms/gen-ai-lesson3-gh?WT.mc_id=academic-105485-koreyst)  | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 04  | [Understanding Prompt Engineering Fundamentals](./04-prompt-engineering-fundamentals/README.md?WT.mc_id=academic-105485-koreyst)             | **Learn:** عملی طور پر Prompt Engineering کی بہترین مشقیں                                | [ویڈیو](https://aka.ms/gen-ai-lesson4-gh?WT.mc_id=academic-105485-koreyst)  | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 05  | [Creating Advanced Prompts](./05-advanced-prompts/README.md?WT.mc_id=academic-105485-koreyst)                                                | **Learn:** ایسے prompt engineering طریقے کیسے اپنائیں جو آپ کے prompts کے نتائج کو بہتر بنائیں۔ | [ویڈیو](https://aka.ms/gen-ai-lesson5-gh?WT.mc_id=academic-105485-koreyst)  | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 06  | [ٹیکسٹ جنریشن ایپلیکیشنز بنانا](./06-text-generation-apps/README.md?WT.mc_id=academic-105485-koreyst)                                | **بنائیں:** Azure OpenAI / OpenAI API استعمال کرتے ہوئے ایک ٹیکسٹ جنریشن ایپلیکیشن                                | [ویڈیو](https://aka.ms/gen-ai-lesson6-gh?WT.mc_id=academic-105485-koreyst)  | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 07  | [چیٹ ایپلیکیشنز بنانا](./07-building-chat-applications/README.md?WT.mc_id=academic-105485-koreyst)                                     | **بنائیں:** چیٹ ایپلیکیشنز کو مؤثر طریقے سے بنانے اور انضمام کرنے کی تکنیکیں               | [ویڈیو](https://aka.ms/gen-ai-lessons7-gh?WT.mc_id=academic-105485-koreyst) | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 08  | [سرچ ایپس اور ویکٹر ڈیٹا بیسز بنانا](./08-building-search-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **بنائیں:** ایک سرچ ایپلیکیشن جو ڈیٹا تلاش کرنے کے لیے Embeddings استعمال کرتی ہے                        | [ویڈیو](https://aka.ms/gen-ai-lesson8-gh?WT.mc_id=academic-105485-koreyst)  | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 09  | [امیج جنریشن ایپلیکیشنز بنانا](./09-building-image-applications/README.md?WT.mc_id=academic-105485-koreyst)                        | **بنائیں:** ایک امیج جنریشن ایپلیکیشن                                                       | [ویڈیو](https://aka.ms/gen-ai-lesson9-gh?WT.mc_id=academic-105485-koreyst)  | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 10  | [لو کوڈ AI ایپلیکیشنز بنانا](./10-building-low-code-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                       | **بنائیں:** لو کوڈ ٹولز استعمال کرتے ہوئے ایک Generative AI ایپلیکیشن                                     | [ویڈیو](https://aka.ms/gen-ai-lesson10-gh?WT.mc_id=academic-105485-koreyst) | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 11  | [فنکشن کالنگ کے ساتھ بیرونی ایپلیکیشنز کو مربوط کرنا](./11-integrating-with-function-calling/README.md?WT.mc_id=academic-105485-koreyst) | **بنائیں:** فنکشن کالنگ کیا ہے اور ایپلیکیشنز میں اس کے استعمال کے کیسز                          | [ویڈیو](https://aka.ms/gen-ai-lesson11-gh?WT.mc_id=academic-105485-koreyst) | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 12  | [AI ایپلیکیشنز کے لیے UX ڈیزائن کرنا](./12-designing-ux-for-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **سیکھیں:** Generative AI ایپلیکیشنز تیار کرتے وقت UX ڈیزائن کے اصول کیسے لاگو کیے جائیں         | [ویڈیو](https://aka.ms/gen-ai-lesson12-gh?WT.mc_id=academic-105485-koreyst) | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 13  | [اپنی Generative AI ایپلیکیشنز کو محفوظ بنانا](./13-securing-ai-applications/README.md?WT.mc_id=academic-105485-koreyst)                         | **سیکھیں:** AI سسٹمز کو لاحق خطرات اور ان سسٹمز کو محفوظ بنانے کے طریقے             | [ویڈیو](https://aka.ms/gen-ai-lesson13-gh?WT.mc_id=academic-105485-koreyst) | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 14  | [Generative AI ایپلیکیشن کے لائف سائیکل کا جائزہ](./14-the-generative-ai-application-lifecycle/README.md?WT.mc_id=academic-105485-koreyst)           | **سیکھیں:** LLM لائف سائیکل اور LLMOps کو منظم کرنے کے لیے ٹولز اور میٹرکس                         | [ویڈیو](https://aka.ms/gen-ai-lesson14-gh?WT.mc_id=academic-105485-koreyst) | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 15  | [Retrieval Augmented Generation (RAG) اور ویکٹر ڈیٹا بیسز](./15-rag-and-vector-databases/README.md?WT.mc_id=academic-105485-koreyst)        | **بنائیں:** RAG فریم ورک استعمال کرتے ہوئے ویکٹر ڈیٹا بیسز سے embeddings بازیافت کرنے والی ایپلیکیشن  | [ویڈیو](https://aka.ms/gen-ai-lesson15-gh?WT.mc_id=academic-105485-koreyst) | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 16  | [اوپن سورس ماڈلز اور Hugging Face](./16-open-source-models/README.md?WT.mc_id=academic-105485-koreyst)                                    | **بنائیں:** Hugging Face پر دستیاب اوپن سورس ماڈلز استعمال کرتے ہوئے ایک ایپلیکیشن                    | [ویڈیو](https://aka.ms/gen-ai-lesson16-gh?WT.mc_id=academic-105485-koreyst) | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 17  | [AI ایجنٹس](./17-ai-agents/README.md?WT.mc_id=academic-105485-koreyst)                                                                       | **بنائیں:** AI ایجنٹ فریم ورک استعمال کرتے ہوئے ایک ایپلیکیشن                                           | [ویڈیو](https://aka.ms/gen-ai-lesson17-gh?WT.mc_id=academic-105485-koreyst) | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 18  | [LLMs کی Fine-Tuning](./18-fine-tuning/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **سیکھیں:** LLMs کی fine-tuning کیا ہے، کیوں ضروری ہے اور کیسے کی جاتی ہے                                            | [ویڈیو](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst) | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 19  | [SLMs کے ساتھ بنانا](./19-slm/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **سیکھیں:** Small Language Models کے ساتھ بنانے کے فوائد                                            | ویڈیو جلد آ رہا ہے | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 20  | [Mistral ماڈلز کے ساتھ بنانا](./20-mistral/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **سیکھیں:** Mistral فیملی ماڈلز کی خصوصیات اور فرق                                           | ویڈیو جلد آ رہا ہے | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |
| 21  | [Meta ماڈلز کے ساتھ بنانا](./21-meta/README.md?WT.mc_id=academic-105485-koreyst)                                                              | **سیکھیں:** Meta فیملی ماڈلز کی خصوصیات اور فرق                                           | ویڈیو جلد آ رہا ہے | [مزید جانیں](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst) |

### 🌟 خصوصی شکریہ

تمام GitHub Actions اور ورک فلو بنانے کے لیے [**John Aziz**](https://www.linkedin.com/in/john0isaac/) کا خصوصی شکریہ

ہر سبق میں سیکھنے والے اور کوڈ کے تجربے کو بہتر بنانے کے لیے اہم تعاون کرنے پر [**Bernhard Merkle**](https://www.linkedin.com/in/bernhard-merkle-738b73/) کا شکریہ۔

## 🎒 دیگر کورسز

ہماری ٹیم دیگر کورسز بھی تیار کرتی ہے! دیکھیں:

- [**نیا** Model Context Protocol برائے مبتدی](https://github.com/microsoft/mcp-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI Agents برائے مبتدی](https://github.com/microsoft/ai-agents-for-beginners?WT.mc_id=academic-105485-koreyst)
- [.NET استعمال کرتے ہوئے Generative AI برائے مبتدی](https://github.com/microsoft/Generative-AI-for-beginners-dotnet?WT.mc_id=academic-105485-koreyst)
- [JavaScript استعمال کرتے ہوئے Generative AI برائے مبتدی](https://aka.ms/genai-js-course?WT.mc_id=academic-105485-koreyst)
- [ML برائے مبتدی](https://aka.ms/ml-beginners?WT.mc_id=academic-105485-koreyst)
- [Data Science برائے مبتدی](https://aka.ms/datascience-beginners?WT.mc_id=academic-105485-koreyst)
- [AI برائے مبتدی](https://aka.ms/ai-beginners?WT.mc_id=academic-105485-koreyst)
- [Cybersecurity برائے مبتدی](https://github.com/microsoft/Security-101??WT.mc_id=academic-96948-sayoung)
- [Web Dev برائے مبتدی](https://aka.ms/webdev-beginners?WT.mc_id=academic-105485-koreyst)
- [IoT برائے مبتدی](https://aka.ms/iot-beginners?WT.mc_id=academic-105485-koreyst)
- [XR Development برائے مبتدی](https://github.com/microsoft/xr-development-for-beginners?WT.mc_id=academic-105485-koreyst)
- [AI Paired Programming کے لیے GitHub Copilot میں مہارت حاصل کریں](https://aka.ms/GitHubCopilotAI?WT.mc_id=academic-105485-koreyst)
- [C#/.NET ڈویلپرز کے لیے GitHub Copilot میں مہارت حاصل کریں](https://github.com/microsoft/mastering-github-copilot-for-dotnet-csharp-developers?WT.mc_id=academic-105485-koreyst)
- [اپنی خود کی Copilot ایڈونچر منتخب کریں](https://github.com/microsoft/CopilotAdventures?WT.mc_id=academic-105485-koreyst)

**دستخطی نوٹ**:  
یہ دستاویز AI ترجمہ سروس [Co-op Translator](https://github.com/Azure/co-op-translator) کے ذریعے ترجمہ کی گئی ہے۔ اگرچہ ہم درستگی کے لیے کوشاں ہیں، براہ کرم آگاہ رہیں کہ خودکار ترجمے میں غلطیاں یا عدم درستیاں ہو سکتی ہیں۔ اصل دستاویز اپنی مادری زبان میں ہی معتبر ماخذ سمجھی جانی چاہیے۔ اہم معلومات کے لیے پیشہ ور انسانی ترجمہ کی سفارش کی جاتی ہے۔ اس ترجمے کے استعمال سے پیدا ہونے والی کسی بھی غلط فہمی یا غلط تشریح کی ذمہ داری ہم پر عائد نہیں ہوتی۔