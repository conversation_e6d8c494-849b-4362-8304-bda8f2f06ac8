<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:51:05+00:00",
  "source_file": "SECURITY.md",
  "language_code": "ur"
}
-->
## سیکیورٹی

مائیکروسافٹ اپنے سافٹ ویئر مصنوعات اور خدمات کی سیکیورٹی کو سنجیدگی سے لیتا ہے، جس میں ہمارے GitHub تنظیموں کے ذریعے منظم تمام سورس کوڈ ریپوزٹریز شامل ہیں، جن میں [Microsoft](https://github.com/microsoft)، [Azure](https://github.com/Azure)، [DotNet](https://github.com/dotnet)، [AspNet](https://github.com/aspnet)، [Xamarin](https://github.com/xamarin)، اور [ہمارے GitHub تنظیمیں](https://opensource.microsoft.com/) شامل ہیں۔

اگر آپ کو کسی مائیکروسافٹ کے زیر ملکیت ریپوزٹری میں کوئی سیکیورٹی کمزوری نظر آئے جو [Microsoft کی سیکیورٹی کمزوری کی تعریف](https://aka.ms/opensource/security/definition) پر پوری اترتی ہو، تو براہ کرم اسے نیچے دیے گئے طریقے سے رپورٹ کریں۔

## سیکیورٹی مسائل کی رپورٹنگ

**براہ کرم سیکیورٹی کمزوریوں کی رپورٹنگ پبلک GitHub ایشوز کے ذریعے نہ کریں۔**

اس کے بجائے، انہیں Microsoft Security Response Center (MSRC) پر رپورٹ کریں: [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report)۔

اگر آپ لاگ ان کیے بغیر رپورٹ کرنا چاہتے ہیں، تو ای میل بھیجیں [<EMAIL>](mailto:<EMAIL>) پر۔ اگر ممکن ہو تو، اپنا پیغام ہمارے PGP کلید کے ساتھ انکرپٹ کریں؛ براہ کرم اسے [Microsoft Security Response Center PGP Key صفحہ](https://aka.ms/opensource/security/pgpkey) سے ڈاؤن لوڈ کریں۔

آپ کو 24 گھنٹوں کے اندر جواب ملنا چاہیے۔ اگر کسی وجہ سے جواب نہ ملے، تو براہ کرم ای میل کے ذریعے فالو اپ کریں تاکہ ہم آپ کا اصل پیغام وصول کر سکیں۔ مزید معلومات کے لیے [microsoft.com/msrc](https://aka.ms/opensource/security/msrc) دیکھیں۔

براہ کرم نیچے دی گئی مطلوبہ معلومات شامل کریں (جتنا ممکن ہو فراہم کریں) تاکہ ہم مسئلے کی نوعیت اور دائرہ کار کو بہتر سمجھ سکیں:

  * مسئلے کی قسم (مثلاً buffer overflow، SQL injection، cross-site scripting، وغیرہ)
  * مسئلے کے ظہور سے متعلق سورس فائل(وں) کے مکمل راستے
  * متاثرہ سورس کوڈ کی جگہ (tag/branch/commit یا براہ راست URL)
  * مسئلہ دوبارہ پیدا کرنے کے لیے کوئی خاص کنفیگریشن
  * مسئلہ دوبارہ پیدا کرنے کے لیے مرحلہ وار ہدایات
  * ثبوتِ تصور یا exploit کوڈ (اگر ممکن ہو)
  * مسئلے کا اثر، بشمول یہ کہ حملہ آور اس مسئلے کا کس طرح فائدہ اٹھا سکتا ہے

یہ معلومات ہمیں آپ کی رپورٹ کو جلدی سے جانچنے میں مدد دے گی۔

اگر آپ بگ باؤنٹی کے لیے رپورٹ کر رہے ہیں، تو مکمل رپورٹس زیادہ باؤنٹی انعام میں مددگار ثابت ہو سکتی ہیں۔ ہمارے فعال پروگرامز کے بارے میں مزید تفصیلات کے لیے براہ کرم ہمارا [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) صفحہ ملاحظہ کریں۔

## ترجیحی زبانیں

ہم تمام مواصلات کے لیے انگریزی کو ترجیح دیتے ہیں۔

## پالیسی

مائیکروسافٹ [Coordinated Vulnerability Disclosure](https://aka.ms/opensource/security/cvd) کے اصول کی پیروی کرتا ہے۔

**دستخطی نوٹ**:  
یہ دستاویز AI ترجمہ سروس [Co-op Translator](https://github.com/Azure/co-op-translator) کے ذریعے ترجمہ کی گئی ہے۔ اگرچہ ہم درستگی کے لیے کوشاں ہیں، براہ کرم آگاہ رہیں کہ خودکار ترجمے میں غلطیاں یا عدم درستیاں ہو سکتی ہیں۔ اصل دستاویز اپنی مادری زبان میں ہی معتبر ماخذ سمجھی جانی چاہیے۔ اہم معلومات کے لیے پیشہ ور انسانی ترجمہ کی سفارش کی جاتی ہے۔ اس ترجمے کے استعمال سے پیدا ہونے والی کسی بھی غلط فہمی یا غلط تشریح کی ذمہ داری ہم پر عائد نہیں ہوتی۔