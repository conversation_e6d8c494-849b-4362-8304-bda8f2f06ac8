<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T18:05:28+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "vi"
}
-->
# T<PERSON>i <PERSON>iệc Học Tự Động

Bài học được xây dựng dựa trên một số tài nguyên cốt lõi từ OpenAI và Azure OpenAI làm tham khảo cho thuật ngữ và hướng dẫn. Dưới đây là danh sách không đầy đủ, dành cho hành trình học tự động của bạn.

## 1. T<PERSON><PERSON>

| Tiêu đề/Liên kết                                                                                                                                                                                                             | <PERSON><PERSON> tả                                                                                                                                                                                                                                                                                                                                                                                        |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | Fine-tuning cải thiện khả năng học ít ví dụ bằng cách huấn luyện trên nhiều ví dụ hơn so với những gì có thể đưa vào prompt, giúp tiết kiệm chi phí, nâng cao chất lượng phản hồi và giảm độ trễ khi gọi API. **Tìm hiểu tổng quan về fine-tuning từ OpenAI.**                                                                                                                               |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | Hiểu **fine-tuning là gì (khái niệm)**, tại sao bạn nên quan tâm (vấn đề cần giải quyết), dữ liệu nào nên dùng (huấn luyện) và cách đo lường chất lượng                                                                                                                                                                        |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI Service cho phép bạn tùy chỉnh các mô hình dựa trên bộ dữ liệu cá nhân bằng fine-tuning. Tìm hiểu **cách fine-tune (quy trình)** chọn mô hình qua Azure AI Studio, Python SDK hoặc REST API.                                                                                                                        |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | Các LLM có thể không hoạt động tốt trên các lĩnh vực, nhiệm vụ hoặc bộ dữ liệu cụ thể, hoặc có thể tạo ra kết quả không chính xác hoặc gây hiểu lầm. **Khi nào bạn nên cân nhắc fine-tuning** như một giải pháp khả thi?                                                                                                         |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | Fine-tuning liên tục là quá trình lặp đi lặp lại, chọn một mô hình đã được fine-tune làm mô hình cơ sở và **tiếp tục fine-tune** trên các bộ dữ liệu huấn luyện mới.                                                                                                                                                           |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | Fine-tuning mô hình của bạn **với các ví dụ gọi hàm** có thể cải thiện đầu ra bằng cách tạo ra các phản hồi chính xác và nhất quán hơn - với định dạng phản hồi tương tự và tiết kiệm chi phí                                                                                                                                |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | Tham khảo bảng này để hiểu **mô hình nào có thể fine-tune** trong Azure OpenAI, và các khu vực mà chúng có sẵn. Kiểm tra giới hạn token và ngày hết hạn dữ liệu huấn luyện nếu cần.                                                                                                                                              |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | Tập 30 phút **tháng 10 năm 2023** của AI Show thảo luận về lợi ích, hạn chế và những hiểu biết thực tế giúp bạn đưa ra quyết định này.                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | Tài nguyên **AI Playbook** này hướng dẫn bạn qua các yêu cầu về dữ liệu, định dạng, điều chỉnh siêu tham số và những thách thức/giới hạn bạn nên biết.                                                                                                                                                                         |
| **Tutorial**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | Học cách tạo bộ dữ liệu fine-tuning mẫu, chuẩn bị fine-tuning, tạo công việc fine-tuning và triển khai mô hình đã fine-tune trên Azure.                                                                                                                                                                                        |
| **Tutorial**: [Fine-tune a Llama 2 model in Azure AI Studio](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio cho phép bạn tùy chỉnh các mô hình ngôn ngữ lớn dựa trên bộ dữ liệu cá nhân _bằng quy trình làm việc dựa trên giao diện người dùng phù hợp với nhà phát triển ít code_. Xem ví dụ này.                                                                                                                        |
| **Tutorial**:[Fine-tune Hugging Face models for a single GPU on Azure](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | Bài viết này mô tả cách fine-tune mô hình Hugging Face với thư viện transformers trên một GPU đơn bằng Azure DataBricks và thư viện Hugging Face Trainer.                                                                                                                                                                      |
| **Training:** [Fine-tune a foundation model with Azure Machine Learning](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Danh mục mô hình trong Azure Machine Learning cung cấp nhiều mô hình mã nguồn mở bạn có thể fine-tune cho nhiệm vụ cụ thể. Thử module này trong [AzureML Generative AI Learning Path](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst) |
| **Tutorial:** [Azure OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | Fine-tuning các mô hình GPT-3.5 hoặc GPT-4 trên Microsoft Azure sử dụng W&B cho phép theo dõi và phân tích chi tiết hiệu suất mô hình. Hướng dẫn này mở rộng các khái niệm từ hướng dẫn Fine-Tuning của OpenAI với các bước và tính năng cụ thể cho Azure OpenAI.                                                                                                                     |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                                                                                             |

## 2. Tài Nguyên Phụ

Phần này tổng hợp các tài nguyên bổ sung đáng để khám phá, nhưng chúng tôi chưa có thời gian đề cập trong bài học này. Có thể sẽ được đưa vào bài học tương lai hoặc làm bài tập phụ vào thời điểm khác. Hiện tại, bạn có thể dùng chúng để xây dựng kiến thức và chuyên môn của riêng mình về chủ đề này.

| Tiêu đề/Liên kết                                                                                                                                                                                                            | Mô tả                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [Chuẩn bị và phân tích dữ liệu cho fine-tuning mô hình chat](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                          | Notebook này là công cụ để tiền xử lý và phân tích bộ dữ liệu chat dùng cho fine-tuning mô hình chat. Nó kiểm tra lỗi định dạng, cung cấp thống kê cơ bản và ước tính số token để tính chi phí fine-tuning. Xem thêm: [Phương pháp fine-tuning cho gpt-3.5-turbo](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst).                                                                                                                     |
| **OpenAI Cookbook**: [Fine-Tuning cho Retrieval Augmented Generation (RAG) với Qdrant](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst)         | Mục tiêu của notebook này là hướng dẫn một ví dụ toàn diện về cách fine-tune các mô hình OpenAI cho Retrieval Augmented Generation (RAG). Chúng ta cũng sẽ tích hợp Qdrant và Few-Shot Learning để tăng hiệu suất mô hình và giảm các kết quả sai lệch.                                                                                                                                                                                                                  |
| **OpenAI Cookbook**: [Fine-tuning GPT với Weights & Biases](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                                 | Weights & Biases (W&B) là nền tảng dành cho nhà phát triển AI, cung cấp công cụ huấn luyện mô hình, fine-tuning và tận dụng các mô hình nền tảng. Đọc trước hướng dẫn [OpenAI Fine-Tuning](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst), sau đó thử bài tập trong Cookbook.                                                                                                                                                                               |
| **Community Tutorial** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - fine-tuning cho các Mô hình Ngôn ngữ Nhỏ                                                      | Gặp gỡ [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst), mô hình nhỏ mới của Microsoft, mạnh mẽ nhưng gọn nhẹ. Hướng dẫn này sẽ chỉ bạn cách fine-tune Phi-2, trình bày cách xây dựng bộ dữ liệu riêng và fine-tune mô hình sử dụng QLoRA.                                                                                                                                                                         |
| **Hugging Face Tutorial** [Cách Fine-Tune LLMs năm 2024 với Hugging Face](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                                     | Bài viết này hướng dẫn bạn cách fine-tune các LLM mã nguồn mở bằng Hugging Face TRL, Transformers & datasets trong năm 2024. Bạn sẽ xác định trường hợp sử dụng, thiết lập môi trường phát triển, chuẩn bị bộ dữ liệu, fine-tune mô hình, đánh giá thử nghiệm và triển khai vào sản xuất.                                                                                                                                                                  |
| **Hugging Face: [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)**                                                                                                | Mang đến việc huấn luyện và triển khai nhanh hơn, dễ dàng hơn các [mô hình học máy tiên tiến](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst). Repo có các hướng dẫn thân thiện với Colab kèm video YouTube, dành cho fine-tuning. **Phản ánh cập nhật gần đây theo hướng [local-first](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)**. Đọc tài liệu [AutoTrain](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst) |
|                                                                                                                                                                                                                             |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |

**Tuyên bố từ chối trách nhiệm**:  
Tài liệu này đã được dịch bằng dịch vụ dịch thuật AI [Co-op Translator](https://github.com/Azure/co-op-translator). Mặc dù chúng tôi cố gắng đảm bảo độ chính xác, xin lưu ý rằng bản dịch tự động có thể chứa lỗi hoặc không chính xác. Tài liệu gốc bằng ngôn ngữ gốc của nó nên được coi là nguồn chính xác và đáng tin cậy. Đối với các thông tin quan trọng, nên sử dụng dịch vụ dịch thuật chuyên nghiệp do con người thực hiện. Chúng tôi không chịu trách nhiệm về bất kỳ sự hiểu lầm hay giải thích sai nào phát sinh từ việc sử dụng bản dịch này.