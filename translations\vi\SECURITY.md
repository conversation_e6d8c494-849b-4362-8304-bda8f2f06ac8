<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:54:52+00:00",
  "source_file": "SECURITY.md",
  "language_code": "vi"
}
-->
## B<PERSON><PERSON> mật

Microsoft rất coi trọng vấn đề bảo mật cho các sản phẩm và dịch vụ phần mềm của mình, bao gồm tất cả các kho mã nguồn được quản lý thông qua các tổ chức GitHub của chúng tôi, trong đó có [Microsoft](https://github.com/microsoft), [Azure](https://github.com/Azure), [DotNet](https://github.com/dotnet), [AspNet](https://github.com/aspnet), [X<PERSON>rin](https://github.com/xamarin), và [các tổ chức GitHub của chúng tôi](https://opensource.microsoft.com/).

Nếu bạn cho rằng đã phát hiện ra một lỗ hổng bảo mật trong bất kỳ kho mã nào thuộc sở hữu của Microsoft và đáp ứng [định nghĩa về lỗ hổng bảo mật của Microsoft](https://aka.ms/opensource/security/definition), vui lòng báo cáo cho chúng tôi theo hướng dẫn dưới đây.

## Báo cáo các vấn đề bảo mật

**Vui lòng không báo cáo lỗ hổng bảo mật qua các issue công khai trên GitHub.**

Thay vào đó, hãy gửi báo cáo đến Trung tâm Phản hồi Bảo mật Microsoft (MSRC) tại [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report).

Nếu bạn muốn gửi báo cáo mà không cần đăng nhập, hãy gửi email đến [<EMAIL>](mailto:<EMAIL>). Nếu có thể, hãy mã hóa tin nhắn của bạn bằng khóa PGP của chúng tôi; bạn có thể tải khóa này từ [trang Khóa PGP của Trung tâm Phản hồi Bảo mật Microsoft](https://aka.ms/opensource/security/pgpkey).

Bạn sẽ nhận được phản hồi trong vòng 24 giờ. Nếu vì lý do nào đó không nhận được phản hồi, vui lòng theo dõi qua email để đảm bảo chúng tôi đã nhận được tin nhắn ban đầu của bạn. Thông tin thêm có thể được tìm thấy tại [microsoft.com/msrc](https://aka.ms/opensource/security/msrc).

Vui lòng cung cấp các thông tin yêu cầu dưới đây (càng đầy đủ càng tốt) để giúp chúng tôi hiểu rõ hơn về bản chất và phạm vi của vấn đề có thể xảy ra:

  * Loại vấn đề (ví dụ: tràn bộ đệm, SQL injection, cross-site scripting, v.v.)
  * Đường dẫn đầy đủ của các tệp mã nguồn liên quan đến sự cố
  * Vị trí của mã nguồn bị ảnh hưởng (tag/branch/commit hoặc URL trực tiếp)
  * Bất kỳ cấu hình đặc biệt nào cần thiết để tái hiện sự cố
  * Hướng dẫn từng bước để tái hiện sự cố
  * Mã chứng minh khái niệm hoặc mã khai thác (nếu có thể)
  * Tác động của vấn đề, bao gồm cách kẻ tấn công có thể lợi dụng lỗ hổng

Thông tin này sẽ giúp chúng tôi xử lý báo cáo của bạn nhanh hơn.

Nếu bạn đang báo cáo để nhận thưởng bug bounty, các báo cáo càng đầy đủ sẽ góp phần tăng mức thưởng. Vui lòng truy cập trang [Microsoft Bug Bounty Program](https://aka.ms/opensource/security/bounty) để biết thêm chi tiết về các chương trình hiện có.

## Ngôn ngữ ưu tiên

Chúng tôi ưu tiên tất cả các trao đổi bằng tiếng Anh.

## Chính sách

Microsoft tuân theo nguyên tắc [Công bố Lỗ hổng Bảo mật Có phối hợp](https://aka.ms/opensource/security/cvd).

**Tuyên bố từ chối trách nhiệm**:  
Tài liệu này đã được dịch bằng dịch vụ dịch thuật AI [Co-op Translator](https://github.com/Azure/co-op-translator). Mặc dù chúng tôi cố gắng đảm bảo độ chính xác, xin lưu ý rằng các bản dịch tự động có thể chứa lỗi hoặc không chính xác. Tài liệu gốc bằng ngôn ngữ gốc của nó nên được coi là nguồn chính xác và đáng tin cậy. Đối với các thông tin quan trọng, nên sử dụng dịch vụ dịch thuật chuyên nghiệp do con người thực hiện. Chúng tôi không chịu trách nhiệm về bất kỳ sự hiểu lầm hoặc giải thích sai nào phát sinh từ việc sử dụng bản dịch này.