<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "b5466bcedc3c75aa35476270362f626a",
  "translation_date": "2025-07-09T16:26:35+00:00",
  "source_file": "15-rag-and-vector-databases/data/frameworks.md",
  "language_code": "zh"
}
-->
# 神经网络框架

正如我们已经学习的，要高效训练神经网络，我们需要做两件事：

* 对张量进行操作，例如乘法、加法，以及计算一些函数如 sigmoid 或 softmax
* 计算所有表达式的梯度，以便执行梯度下降优化

虽然 `numpy` 库可以完成第一部分，但我们需要某种机制来计算梯度。在我们之前开发的框架中，必须手动在 `backward` 方法中编写所有导数函数来实现反向传播。理想情况下，框架应该能让我们计算*任何表达式*的梯度。

另一个重要的方面是能够在 GPU 或其他专用计算单元（如 TPU）上执行计算。深度神经网络训练需要*大量*计算，能够在 GPU 上并行处理这些计算非常关键。

> ✅ “并行化”一词指的是将计算分布到多个设备上。

目前，最流行的两个神经网络框架是 TensorFlow 和 PyTorch。它们都提供了在 CPU 和 GPU 上操作张量的低级 API。在低级 API 之上，还有更高级的 API，分别是 Keras 和 PyTorch Lightning。

低级 API | TensorFlow | PyTorch
---------|-------------|---------
高级 API | Keras       | PyTorch Lightning

这两个框架的**低级 API**允许你构建所谓的**计算图**。该图定义了如何用给定的输入参数计算输出（通常是损失函数），并且如果有 GPU 可用，可以将计算推送到 GPU 上。框架提供了对计算图求导和计算梯度的函数，这些梯度随后可用于优化模型参数。

**高级 API**则更倾向于将神经网络视为**层的序列**，使构建大多数神经网络变得更加简单。训练模型通常只需准备数据，然后调用 `fit` 函数即可完成。

高级 API 让你能够快速构建典型的神经网络，而无需关注太多细节。与此同时，低级 API 提供了对训练过程的更多控制，因此在研究新型神经网络架构时被广泛使用。

还需要理解的是，你可以同时使用这两种 API。例如，你可以用低级 API 开发自己的网络层架构，然后在用高级 API 构建和训练的更大网络中使用它。或者，你可以用高级 API 定义一个层序列的网络，然后用自己的低级训练循环来执行优化。两种 API 共享相同的基本概念，并设计为能够很好地协同工作。

## 学习

在本课程中，我们为 PyTorch 和 TensorFlow 提供了大部分内容。你可以选择自己喜欢的框架，只学习对应的笔记本。如果不确定选哪个框架，可以在网上查阅关于 **PyTorch vs. TensorFlow** 的讨论，也可以同时了解两个框架以加深理解。

在可能的情况下，我们会使用高级 API 以简化学习过程。但我们认为从底层理解神经网络的工作原理非常重要，因此一开始会先使用低级 API 和张量进行学习。如果你想快速入门，不想花太多时间学习细节，可以跳过这些内容，直接进入高级 API 的笔记本。

## ✍️ 练习：框架

继续学习以下笔记本：

低级 API | TensorFlow+Keras 笔记本 | PyTorch
---------|----------------------------|---------
高级 API | Keras                     | *PyTorch Lightning*

掌握框架后，我们来回顾一下过拟合的概念。

# 过拟合

过拟合是机器学习中极其重要的概念，理解它非常关键！

考虑下面用 5 个点（图中用 `x` 表示）进行拟合的问题：

!linear | overfit
-------------------------|--------------------------
**线性模型，2 个参数** | **非线性模型，7 个参数**
训练误差 = 5.3 | 训练误差 = 0
验证误差 = 5.1 | 验证误差 = 20

* 左图显示了一个较好的直线拟合。由于参数数量合适，模型正确捕捉到了点的分布规律。
* 右图中模型过于复杂。因为只有 5 个点，而模型有 7 个参数，它可以调整到通过所有点，使训练误差为 0。但这阻碍了模型理解数据背后的正确模式，因此验证误差非常高。

在模型复杂度（参数数量）和训练样本数量之间找到合适的平衡非常重要。

## 过拟合产生的原因

  * 训练数据不足
  * 模型过于复杂
  * 输入数据中噪声过多

## 如何检测过拟合

如上图所示，过拟合可以通过训练误差很低而验证误差很高来检测。通常训练过程中，训练误差和验证误差都会下降，但某个时刻验证误差可能停止下降并开始上升。这是过拟合的信号，表明我们应该停止训练（或者至少保存当前模型快照）。

过拟合示意图

## 如何防止过拟合

如果发现过拟合，可以采取以下措施：

 * 增加训练数据量
 * 降低模型复杂度
 * 使用正则化技术，比如后面会介绍的 Dropout

## 过拟合与偏差-方差权衡

过拟合实际上是统计学中更通用问题——偏差-方差权衡的一个表现。如果考虑模型误差的来源，可以分为两类：

* **偏差误差** 是由于算法无法正确捕捉训练数据之间的关系，通常是模型能力不足导致的（**欠拟合**）。
* **方差误差** 是模型拟合了输入数据中的噪声而非有意义的关系（**过拟合**）。

训练过程中，偏差误差会下降（模型逐渐学会拟合数据），而方差误差会增加。重要的是要在合适时机停止训练——无论是手动检测到过拟合时，还是通过引入正则化自动控制——以防止过拟合。

## 总结

本课你了解了两个最流行 AI 框架 TensorFlow 和 PyTorch 的不同 API，以及一个非常重要的话题——过拟合。

## 🚀 挑战

在配套笔记本的底部，你会看到“任务”；请完成这些任务以巩固学习。

## 复习与自学

请自行查阅以下主题：

- TensorFlow
- PyTorch
- 过拟合

思考以下问题：

- TensorFlow 和 PyTorch 有什么区别？
- 过拟合和欠拟合有什么区别？

## 作业

本实验要求你使用 PyTorch 或 TensorFlow，利用单层和多层全连接网络解决两个分类问题。

**免责声明**：  
本文件使用 AI 翻译服务 [Co-op Translator](https://github.com/Azure/co-op-translator) 进行翻译。虽然我们力求准确，但请注意自动翻译可能包含错误或不准确之处。原始文件的母语版本应被视为权威来源。对于重要信息，建议采用专业人工翻译。对于因使用本翻译而产生的任何误解或误释，我们不承担任何责任。