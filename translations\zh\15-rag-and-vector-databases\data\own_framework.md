<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "df98b2c59f87d8543135301e87969f70",
  "translation_date": "2025-07-09T16:41:46+00:00",
  "source_file": "15-rag-and-vector-databases/data/own_framework.md",
  "language_code": "zh"
}
-->
# 神经网络简介：多层感知机

在上一节中，你学习了最简单的神经网络模型——单层感知机，一种线性二分类模型。

本节我们将把该模型扩展为更灵活的框架，使我们能够：

* 除了二分类，还能进行**多分类**
* 除了分类，还能解决**回归问题**
* 分离非线性可分的类别

我们还将用 Python 开发自己的模块化框架，方便构建不同的神经网络结构。

## 机器学习的形式化

首先对机器学习问题进行形式化。假设我们有带标签的训练数据集 **X** 和 **Y**，需要构建一个模型 *f*，使预测尽可能准确。预测的质量用**损失函数** ℒ 来衡量。常用的损失函数有：

* 对于回归问题（预测数值），可以使用**绝对误差** ∑<sub>i</sub>|f(x<sup>(i)</sup>) - y<sup>(i)</sup>|，或**平方误差** ∑<sub>i</sub>(f(x<sup>(i)</sup>) - y<sup>(i)</sup>)²
* 对于分类问题，使用**0-1 损失**（本质上等同于模型的**准确率**），或**逻辑损失**

对于单层感知机，函数 *f* 定义为线性函数 *f(x) = wx + b*（其中 *w* 是权重矩阵，*x* 是输入特征向量，*b* 是偏置向量）。不同的神经网络结构中，这个函数可以更复杂。

> 在分类问题中，通常希望网络输出对应类别的概率。为了将任意数值转换为概率（例如归一化输出），我们常用**softmax** 函数 σ，函数 *f* 变为 *f(x) = σ(wx + b)*

在上述 *f* 的定义中，*w* 和 *b* 称为**参数** θ=⟨*w,b*⟩。给定数据集 ⟨**X**,**Y**⟩，我们可以将整体误差视为参数 θ 的函数。

> ✅ **神经网络训练的目标是通过调整参数 θ 来最小化误差**

## 梯度下降优化

有一种著名的函数优化方法叫做**梯度下降**。其思想是计算损失函数关于参数的导数（多维情况下称为**梯度**），并沿着梯度下降的方向调整参数，使误差减小。形式化如下：

* 用随机值初始化参数 w<sup>(0)</sup>, b<sup>(0)</sup>
* 重复执行以下步骤：
    - w<sup>(i+1)</sup> = w<sup>(i)</sup> - η ∂ℒ/∂w
    - b<sup>(i+1)</sup> = b<sup>(i)</sup> - η ∂ℒ/∂b

训练时，优化步骤通常基于整个数据集计算（损失是所有训练样本的总和）。但实际中，我们取数据集的小批量，称为**小批量（minibatch）**，基于子集计算梯度。由于每次随机抽取子集，这种方法称为**随机梯度下降**（SGD）。

## 多层感知机与反向传播

如上所述，单层网络只能分类线性可分的类别。为了构建更复杂的模型，我们可以组合多层网络。数学上，函数 *f* 变得更复杂，分多步计算：

* z<sub>1</sub> = w<sub>1</sub>x + b<sub>1</sub>
* z<sub>2</sub> = w<sub>2</sub> α(z<sub>1</sub>) + b<sub>2</sub>
* f = σ(z<sub>2</sub>)

其中，α 是**非线性激活函数**，σ 是 softmax 函数，参数 θ = ⟨*w<sub>1</sub>, b<sub>1</sub>, w<sub>2</sub>, b<sub>2</sub>*⟩。

梯度下降算法保持不变，但计算梯度更复杂。利用链式法则，导数计算为：

* ∂ℒ/∂w<sub>2</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂w<sub>2</sub>)
* ∂ℒ/∂w<sub>1</sub> = (∂ℒ/∂σ)(∂σ/∂z<sub>2</sub>)(∂z<sub>2</sub>/∂α)(∂α/∂z<sub>1</sub>)(∂z<sub>1</sub>/∂w<sub>1</sub>)

> ✅ 链式法则用于计算损失函数关于参数的导数。

注意，这些表达式的最左部分相同，因此我们可以从损失函数开始，沿计算图“反向”高效计算导数。训练多层感知机的方法称为**反向传播**，简称“backprop”。



> TODO: 图片引用

> ✅ 我们将在配套的笔记本示例中更详细地讲解反向传播。

## 总结

本节中，我们构建了自己的神经网络库，并用它完成了一个简单的二维分类任务。

## 🚀 挑战

在配套笔记本中，你将实现自己的多层感知机构建和训练框架，深入了解现代神经网络的工作原理。

请前往 OwnFramework 笔记本并完成练习。

## 复习与自学

反向传播是 AI 和机器学习中常用的算法，值得深入学习。

## 作业

本实验要求你使用本节构建的框架，完成 MNIST 手写数字分类任务。

* 说明
* 笔记本

**免责声明**：  
本文件使用 AI 翻译服务 [Co-op Translator](https://github.com/Azure/co-op-translator) 进行翻译。虽然我们力求准确，但请注意，自动翻译可能包含错误或不准确之处。原始文件的母语版本应被视为权威来源。对于重要信息，建议使用专业人工翻译。对于因使用本翻译而产生的任何误解或误释，我们不承担任何责任。