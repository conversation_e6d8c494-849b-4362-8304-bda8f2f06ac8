<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "68664f7e754a892ae1d8d5e2b7bd2081",
  "translation_date": "2025-07-09T17:36:45+00:00",
  "source_file": "18-fine-tuning/README.md",
  "language_code": "zh"
}
-->
[![Open Source Models](../../../translated_images/18-lesson-banner.f30176815b1a5074fce9cceba317720586caa99e24001231a92fd04eeb54a121.zh.png)](https://aka.ms/gen-ai-lesson18-gh?WT.mc_id=academic-105485-koreyst)

# 微调您的大型语言模型

使用大型语言模型构建生成式 AI 应用带来了新的挑战。一个关键问题是确保模型针对特定用户请求生成的内容具有高质量（准确性和相关性）。在之前的课程中，我们讨论了通过_修改输入提示_来解决该问题的技术，如提示工程和检索增强生成。

在今天的课程中，我们将讨论第三种技术，**微调**，它通过_使用额外数据重新训练模型本身_来应对这一挑战。让我们深入了解细节。

## 学习目标

本课介绍了预训练语言模型的微调概念，探讨了这种方法的优势和挑战，并提供了何时以及如何使用微调来提升生成式 AI 模型性能的指导。

完成本课后，您应能回答以下问题：

- 什么是语言模型的微调？
- 何时以及为何微调有用？
- 如何微调预训练模型？
- 微调有哪些局限性？

准备好了吗？我们开始吧。

## 图解指南

想在深入学习前先了解本课的整体内容吗？请查看这份图解指南，介绍了本课的学习旅程——从理解微调的核心概念和动机，到掌握微调过程及最佳实践。这是一个非常有趣的探索主题，别忘了访问[资源](./RESOURCES.md?WT.mc_id=academic-105485-koreyst)页面，获取更多支持您自主学习的链接！

![微调语言模型的图解指南](../../../translated_images/18-fine-tuning-sketchnote.11b21f9ec8a703467a120cb79a28b5ac1effc8d8d9d5b31bbbac6b8640432e14.zh.png)

## 什么是语言模型的微调？

大型语言模型本质上是基于来自互联网等多样化来源的大量文本进行_预训练_的。正如我们在之前课程中学到的，我们需要诸如_提示工程_和_检索增强生成_等技术来提升模型对用户提问（“提示”）的回答质量。

一种常见的提示工程技术是通过提供_指令_（显式指导）或_给出几个示例_（隐式指导）来引导模型生成期望的回答，这被称为_少样本学习_，但它有两个限制：

- 模型的令牌限制会限制您能提供的示例数量，从而影响效果。
- 模型令牌成本可能使得每次提示都添加示例变得昂贵，限制了灵活性。

微调是机器学习系统中的一种常见做法，我们在已有的预训练模型基础上，使用新的数据重新训练它，以提升其在特定任务上的表现。在语言模型的背景下，我们可以用针对特定任务或应用领域_精心挑选的示例集_对预训练模型进行微调，从而创建一个**定制模型**，使其在该任务或领域上更准确、更相关。微调的一个额外好处是，它还能减少少样本学习所需的示例数量，从而降低令牌使用和相关成本。

## 何时以及为何要微调模型？

在_这里_，我们所说的微调是指**监督式**微调，即通过**添加原始训练数据中未包含的新数据**来重新训练模型。这不同于无监督微调，后者是在原始数据上但使用不同超参数进行重新训练。

需要记住的关键是，微调是一项高级技术，需要一定的专业知识才能获得理想效果。如果操作不当，可能无法带来预期的提升，甚至会降低模型在目标领域的表现。

因此，在学习“如何”微调语言模型之前，您需要先了解“为什么”要走这条路，以及“何时”开始微调。先问自己以下问题：

- **用例**：您的微调_用例_是什么？您希望改进当前预训练模型的哪些方面？
- **替代方案**：您是否尝试过_其他技术_来实现目标？用它们建立基线以便比较。
  - 提示工程：尝试少样本提示，提供相关示例，评估回答质量。
  - 检索增强生成：尝试用检索到的数据结果增强提示，评估回答质量。
- **成本**：您是否评估过微调的成本？
  - 可调性——预训练模型是否支持微调？
  - 工作量——准备训练数据、评估和优化模型所需的努力。
  - 计算资源——运行微调任务和部署微调模型所需的计算资源。
  - 数据——是否有足够高质量的示例以确保微调效果。
- **收益**：您是否确认了微调的收益？
  - 质量——微调后的模型是否优于基线？
  - 成本——是否通过简化提示减少了令牌使用？
  - 可扩展性——是否能将基础模型用于新的领域？

通过回答这些问题，您应能判断微调是否适合您的用例。理想情况下，只有当收益大于成本时，这种方法才是合理的。一旦决定继续，就可以考虑_如何_微调预训练模型。

想了解更多决策过程的见解？观看[To fine-tune or not to fine-tune](https://www.youtube.com/watch?v=0Jo-z-MFxJs)

## 如何微调预训练模型？

要微调预训练模型，您需要具备：

- 一个可供微调的预训练模型
- 用于微调的数据集
- 运行微调任务的训练环境
- 部署微调模型的托管环境

## 微调实战

以下资源提供了逐步教程，带您通过精选模型和精心准备的数据集完成实际示例。要完成这些教程，您需要在相应提供商处拥有账户，并能访问相关模型和数据集。

| 提供商       | 教程链接                                                                                                                                                                      | 描述                                                                                                                                                                                                                                                                                                                                                                                                                             |
| ------------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| OpenAI       | [如何微调聊天模型](https://github.com/openai/openai-cookbook/blob/main/examples/How_to_finetune_chat_models.ipynb?WT.mc_id=academic-105485-koreyst)                            | 学习如何针对特定领域（“食谱助手”）微调 `gpt-35-turbo`，包括准备训练数据、运行微调任务以及使用微调模型进行推理。                                                                                                                                                                                                                                                                                                                   |
| Azure OpenAI | [GPT 3.5 Turbo 微调教程](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python-new%2Ccommand-line?WT.mc_id=academic-105485-koreyst)              | 学习如何在 **Azure** 上微调 `gpt-35-turbo-0613` 模型，包括创建和上传训练数据、运行微调任务，以及部署和使用新模型。                                                                                                                                                                                                                                                                                                             |
| Hugging Face | [使用 Hugging Face 微调大型语言模型](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                             | 本博客介绍如何使用 [transformers](https://huggingface.co/docs/transformers/index?WT.mc_id=academic-105485-koreyst) 库和 [Transformer Reinforcement Learning (TRL)](https://huggingface.co/docs/trl/index?WT.mc_id=academic-105485-koreyst) 对开源大型语言模型（如 `CodeLlama 7B`）进行微调，使用 Hugging Face 上的开源[数据集](https://huggingface.co/docs/datasets/index?WT.mc_id=academic-105485-koreyst)。 |
|              |                                                                                                                                                                               |                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| 🤗 AutoTrain | [使用 AutoTrain 微调大型语言模型](https://github.com/huggingface/autotrain-advanced/?WT.mc_id=academic-105485-koreyst)                                                        | AutoTrain（或 AutoTrain Advanced）是 Hugging Face 开发的 Python 库，支持多种任务的微调，包括大型语言模型微调。AutoTrain 是无代码解决方案，微调可在您自己的云端、Hugging Face Spaces 或本地完成。支持基于网页的 GUI、命令行界面以及通过 yaml 配置文件进行训练。                                                                                                                                                             |
|              |                                                                                                                                                                               |                                                                                                                                                                                                                                                                                                                                                                                                                                 |

## 练习任务

请选择上述教程中的一个，跟随步骤完成学习。_我们可能会在本仓库中以 Jupyter Notebook 形式复现这些教程，仅供参考。请直接使用原始资源以获取最新版本。_

## 干得好！继续学习吧。

完成本课后，请访问我们的[生成式 AI 学习合集](https://aka.ms/genai-collection?WT.mc_id=academic-105485-koreyst)，继续提升您的生成式 AI 知识！

恭喜您完成本课程 v2 系列的最后一课！别停止学习和实践。**请查看[资源](RESOURCES.md?WT.mc_id=academic-105485-koreyst)页面，获取本主题的更多推荐内容。**

我们的 v1 系列课程也已更新，增加了更多练习和概念。花点时间复习巩固吧——并请[分享您的问题和反馈](https://github.com/microsoft/generative-ai-for-beginners/issues?WT.mc_id=academic-105485-koreyst)，帮助我们为社区改进这些课程。

**免责声明**：  
本文件使用 AI 翻译服务 [Co-op Translator](https://github.com/Azure/co-op-translator) 进行翻译。虽然我们力求准确，但请注意，自动翻译可能包含错误或不准确之处。原始文件的母语版本应被视为权威来源。对于重要信息，建议使用专业人工翻译。对于因使用本翻译而产生的任何误解或误释，我们不承担任何责任。