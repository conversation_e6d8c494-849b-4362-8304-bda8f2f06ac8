<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "c2f423d1402f71ca3869ec135bb77d16",
  "translation_date": "2025-07-09T17:55:40+00:00",
  "source_file": "18-fine-tuning/RESOURCES.md",
  "language_code": "zh"
}
-->
# 自学资源

本课程参考了 OpenAI 和 Azure OpenAI 的多个核心资源，涵盖术语和教程。以下是一个非详尽的列表，供您自学参考。

## 1. 主要资源

| 标题/链接                                                                                                                                                                                                                   | 说明                                                                                                                                                                                                                                                                                                                   |
| :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Fine-tuning with OpenAI Models](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                                       | 微调通过训练更多示例，超出提示中能容纳的数量，提升了少样本学习的效果，帮助节省成本、提高响应质量，并实现更低延迟的请求。**了解 OpenAI 微调的概述。**                                                                                    |
| [What is Fine-Tuning with Azure OpenAI?](https://learn.microsoft.com/azure/ai-services/openai/concepts/fine-tuning-considerations#what-is-fine-tuning-with-azure-openai?WT.mc_id=academic-105485-koreyst)                   | 了解**什么是微调（概念）**，为什么要关注它（动机问题）、使用什么数据（训练）以及如何衡量质量。                                                                                                                                                                           |
| [Customize a model with fine-tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst) | Azure OpenAI 服务允许您通过微调将模型定制到您的个人数据集。学习**如何微调（流程）**，使用 Azure AI Studio、Python SDK 或 REST API 选择模型。                                                                                                                                |
| [Recommendations for LLM fine-tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                    | 大型语言模型（LLM）在特定领域、任务或数据集上可能表现不佳，或产生不准确或误导性的输出。**何时应考虑微调**作为解决方案？                                                                                                                                  |
| [Continuous Fine Tuning](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning?tabs=turbo%2Cpython&pivots=programming-language-studio#continuous-fine-tuning?WT.mc_id=academic-105485-koreyst)             | 持续微调是一个迭代过程，选择已微调的模型作为基础模型，并**在新的训练示例集上进一步微调**。                                                                                                                                                     |
| [Fine-tuning and function calling](https://learn.microsoft.com/azure/ai-services/openai/how-to/fine-tuning-functions?WT.mc_id=academic-105485-koreyst)                                                                       | 通过**带有函数调用示例的微调**，可以提升模型输出的准确性和一致性，获得格式相似的响应并节省成本。                                                                                                                                        |
| [Fine-tuning Models: Azure OpenAI Guidance](https://learn.microsoft.com/azure/ai-services/openai/concepts/models#fine-tuning-models?WT.mc_id=academic-105485-koreyst)                                                        | 查阅此表了解**哪些模型可以在 Azure OpenAI 中微调**，以及它们在哪些区域可用。必要时查看它们的令牌限制和训练数据过期日期。                                                                                                                            |
| [To Fine Tune or Not To Fine Tune? That is the Question](https://learn.microsoft.com/shows/ai-show/to-fine-tune-or-not-fine-tune-that-is-the-question?WT.mc_id=academic-105485-koreyst)                                      | 这期 2023 年 10 月的 AI Show 30 分钟节目讨论了微调的优缺点和实用见解，帮助您做出决策。                                                                                                                                                                                        |
| [Getting Started With LLM Fine-Tuning](https://learn.microsoft.com/ai/playbook/technology-guidance/generative-ai/working-with-llms/fine-tuning-recommend?WT.mc_id=academic-105485-koreyst)                                             | 这份**AI 操作手册**资源引导您了解数据需求、格式、超参数微调以及您应知晓的挑战和限制。                                                                                                                                                                         |
| **教程**: [Azure OpenAI GPT3.5 Turbo Fine-Tuning](https://learn.microsoft.com/azure/ai-services/openai/tutorials/fine-tune?tabs=python%2Ccommand-line?WT.mc_id=academic-105485-koreyst)                                  | 学习如何创建示例微调数据集、准备微调、创建微调任务，并在 Azure 上部署微调模型。                                                                                                                                                                                    |
| **教程**: [在 Azure AI Studio 中微调 Llama 2 模型](https://learn.microsoft.com/azure/ai-studio/how-to/fine-tune-model-llama?WT.mc_id=academic-105485-koreyst)                                                      | Azure AI Studio 允许您通过基于 UI 的工作流程将大型语言模型定制到您的个人数据集，适合低代码开发者。查看此示例。                                                                                                                                                               |
| **教程**:[在 Azure 上为单 GPU 微调 Hugging Face 模型](https://learn.microsoft.com/azure/databricks/machine-learning/train-model/huggingface/fine-tune-model?WT.mc_id=academic-105485-koreyst)               | 本文介绍如何使用 Hugging Face transformers 库，在 Azure DataBricks 和 Hugging Face Trainer 库支持下，使用单 GPU 微调 Hugging Face 模型。                                                                                                                                                |
| **培训:** [使用 Azure 机器学习微调基础模型](https://learn.microsoft.com/training/modules/finetune-foundation-model-with-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)         | Azure 机器学习模型目录提供了许多开源模型，您可以针对特定任务进行微调。尝试此模块，来自 [AzureML 生成式 AI 学习路径](https://learn.microsoft.com/training/paths/work-with-generative-models-azure-machine-learning/?WT.mc_id=academic-105485-koreyst)。 |
| **教程:** [Azure OpenAI 微调](https://docs.wandb.ai/guides/integrations/azure-openai-fine-tuning?WT.mc_id=academic-105485-koreyst)                                                                                | 在 Microsoft Azure 上使用 W&B 对 GPT-3.5 或 GPT-4 模型进行微调，可以详细跟踪和分析模型性能。本指南扩展了 OpenAI 微调指南的概念，提供了 Azure OpenAI 的具体步骤和功能。                                                                         |
|                                                                                                                                                                                                                              |                                                                                                                                                                                                                                                                                                                               |

## 2. 次要资源

本节收录了值得探索的额外资源，但本课程未能涵盖。它们可能会在未来课程或作为次要作业选项中涉及。现在，您可以利用它们来提升自己在该主题上的专业知识和理解。

| 标题/链接                                                                                                                                                                                                            | 说明                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
| :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **OpenAI Cookbook**: [聊天模型微调的数据准备与分析](https://cookbook.openai.com/examples/chat_finetuning_data_prep?WT.mc_id=academic-105485-koreyst)                                      | 该笔记本用于预处理和分析用于微调聊天模型的聊天数据集。它检查格式错误，提供基本统计信息，并估算微调成本的令牌数量。参见：[gpt-3.5-turbo 的微调方法](https://platform.openai.com/docs/guides/fine-tuning?WT.mc_id=academic-105485-koreyst)。                                                                                                                                                                   |
| **OpenAI Cookbook**: [使用 Qdrant 进行检索增强生成（RAG）微调](https://cookbook.openai.com/examples/fine-tuned_qa/ft_retrieval_augmented_generation_qdrant?WT.mc_id=academic-105485-koreyst) | 本笔记本旨在演示如何微调 OpenAI 模型以实现检索增强生成（RAG）。我们还将集成 Qdrant 和少样本学习，以提升模型性能并减少虚假信息。                                                                                                                                                                                                                                                                |
| **OpenAI Cookbook**: [使用 Weights & Biases 微调 GPT](https://cookbook.openai.com/examples/third_party/gpt_finetuning_with_wandb?WT.mc_id=academic-105485-koreyst)                                             | Weights & Biases (W&B) 是 AI 开发平台，提供训练模型、微调模型和利用基础模型的工具。先阅读他们的 [OpenAI 微调](https://docs.wandb.ai/guides/integrations/openai-fine-tuning/?WT.mc_id=academic-105485-koreyst) 指南，然后尝试 Cookbook 练习。                                                                                                                                                                                                                  |
| **社区教程** [Phinetuning 2.0](https://huggingface.co/blog/g-ronimo/phinetuning?WT.mc_id=academic-105485-koreyst) - 小型语言模型微调                                                   | 认识微软新推出的强大且紧凑的小型模型 [Phi-2](https://www.microsoft.com/research/blog/phi-2-the-surprising-power-of-small-language-models/?WT.mc_id=academic-105485-koreyst)。本教程将引导您如何微调 Phi-2，展示如何构建独特数据集并使用 QLoRA 进行微调。                                                                                                                                                                       |
| **Hugging Face 教程** [2024 年如何用 Hugging Face 微调 LLM](https://www.philschmid.de/fine-tune-llms-in-2024-with-trl?WT.mc_id=academic-105485-koreyst)                                               | 本博客介绍如何使用 Hugging Face TRL、Transformers 和 datasets 在 2024 年微调开源大型语言模型。您将定义用例、搭建开发环境、准备数据集、微调模型、测试评估并部署到生产环境。                                                                                                                                                                                                                                                                |
| **Hugging Face:** [AutoTrain Advanced](https://github.com/huggingface/autotrain-advanced?WT.mc_id=academic-105485-koreyst)                                                                                            | 提供更快更简便的[最先进机器学习模型](https://twitter.com/abhi1thakur/status/1755167674894557291?WT.mc_id=academic-105485-koreyst)训练和部署。仓库包含适合 Colab 的教程和 YouTube 视频指导，支持微调。**反映了近期的[本地优先](https://twitter.com/abhi1thakur/status/1750828141805777057?WT.mc_id=academic-105485-koreyst)更新**。阅读 [AutoTrain 文档](https://huggingface.co/autotrain?WT.mc_id=academic-105485-koreyst)。 |
|                                                                                                                                                                                                                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |

**免责声明**：  
本文件使用 AI 翻译服务 [Co-op Translator](https://github.com/Azure/co-op-translator) 进行翻译。虽然我们力求准确，但请注意，自动翻译可能包含错误或不准确之处。原始语言的原文应被视为权威来源。对于重要信息，建议采用专业人工翻译。对于因使用本翻译而产生的任何误解或误释，我们不承担任何责任。