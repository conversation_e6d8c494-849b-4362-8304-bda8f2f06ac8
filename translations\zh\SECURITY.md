<!--
CO_OP_TRANSLATOR_METADATA:
{
  "original_hash": "2d33a71bed73d6daee78e2d473ece975",
  "translation_date": "2025-07-09T06:51:14+00:00",
  "source_file": "SECURITY.md",
  "language_code": "zh"
}
-->
## 安全

微软非常重视我们软件产品和服务的安全性，这包括通过我们的 GitHub 组织管理的所有源代码仓库，这些组织包括 [Microsoft](https://github.com/microsoft)、[Azure](https://github.com/Azure)、[DotNet](https://github.com/dotnet)、[AspNet](https://github.com/aspnet)、[Xamarin](https://github.com/xamarin) 以及 [我们的 GitHub 组织](https://opensource.microsoft.com/)。

如果您认为在任何微软拥有的仓库中发现了符合 [微软安全漏洞定义](https://aka.ms/opensource/security/definition) 的安全漏洞，请按照以下说明向我们报告。

## 报告安全问题

**请不要通过公开的 GitHub issue 报告安全漏洞。**

请改为通过微软安全响应中心 (MSRC) 在 [https://msrc.microsoft.com/create-report](https://aka.ms/opensource/security/create-report) 报告。

如果您不想登录提交，也可以发送邮件至 [<EMAIL>](mailto:<EMAIL>)。如果可能，请使用我们的 PGP 密钥加密您的邮件；您可以从 [微软安全响应中心 PGP 密钥页面](https://aka.ms/opensource/security/pgpkey) 下载该密钥。

您应在 24 小时内收到回复。如果因某种原因未收到回复，请通过邮件跟进，确保我们已收到您的原始信息。更多信息请访问 [microsoft.com/msrc](https://aka.ms/opensource/security/msrc)。

请尽可能提供以下信息，以帮助我们更好地了解问题的性质和范围：

  * 问题类型（例如缓冲区溢出、SQL 注入、跨站脚本等）
  * 与问题表现相关的源文件完整路径
  * 受影响源代码的位置（标签/分支/提交或直接 URL）
  * 复现问题所需的特殊配置
  * 复现问题的详细步骤
  * 概念验证或利用代码（如果可能）
  * 问题的影响，包括攻击者可能如何利用该问题

这些信息将帮助我们更快地处理您的报告。

如果您是为漏洞赏金计划报告，提供更完整的报告有助于获得更高的赏金奖励。请访问我们的 [微软漏洞赏金计划](https://aka.ms/opensource/security/bounty) 页面，了解我们正在进行的计划详情。

## 首选语言

我们希望所有沟通均使用英语。

## 政策

微软遵循 [协调漏洞披露](https://aka.ms/opensource/security/cvd) 原则。

**免责声明**：  
本文件使用 AI 翻译服务 [Co-op Translator](https://github.com/Azure/co-op-translator) 进行翻译。虽然我们力求准确，但请注意，自动翻译可能包含错误或不准确之处。原始文件的母语版本应被视为权威来源。对于重要信息，建议采用专业人工翻译。对于因使用本翻译而产生的任何误解或误释，我们不承担任何责任。