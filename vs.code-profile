{"name": "Generative AI For Beginners", "settings": "{\"settings\":\"{\\r\\n    \\\"[python]\\\": {\\r\\n        \\\"editor.defaultFormatter\\\": \\\"ms-python.black-formatter\\\",\\r\\n        \\\"editor.formatOnType\\\": true,\\r\\n        \\\"editor.formatOnSave\\\": true\\r\\n    },\\r\\n    \\\"editor.inlineSuggest.enabled\\\": true,\\r\\n    \\\"editor.lineHeight\\\": 17,\\r\\n    \\\"breadcrumbs.enabled\\\": false,\\r\\n    \\\"files.autoSave\\\": \\\"afterDelay\\\",\\r\\n    \\\"jupyter.themeMatplotlibPlots\\\": true,\\r\\n    \\\"jupyter.widgetScriptSources\\\": [\\r\\n        \\\"unpkg.com\\\",\\r\\n        \\\"jsdelivr.com\\\"\\r\\n    ],\\r\\n    \\\"notebook.experimental.outputScrolling\\\": true,\\r\\n    \\\"files.exclude\\\": {\\r\\n        \\\"**/.csv\\\": true,\\r\\n        \\\"**/.parquet\\\": true,\\r\\n        \\\"**/.pkl\\\": true,\\r\\n        \\\"**/.xls\\\": true\\r\\n    }\\r\\n}\"}", "snippets": "{\"snippets\":{\"python.json\":\"{\\n\\t\\\"Read in File as Pandas DataFrame\\\": {\\n\\t\\t\\\"prefix\\\": \\\"pdr\\\",\\n\\t\\t\\\"body\\\": [\\\"${1:df} = pd.read_${2:csv}('${3:file_name}.${2:csv}')\\\", \\\"${1:df}.info()$0\\\"],\\n\\t\\t\\\"description\\\": \\\"Read in a flat file as pandas dataframe.\\\"\\n\\t},\\n\\t\\\"Read in from Clipboard as Pandas DataFrame\\\": {\\n\\t\\t\\\"prefix\\\": \\\"pdrc\\\",\\n\\t\\t\\\"body\\\": [\\\"${1:df} = pd.read_clipboard()\\\", \\\"${1:df}.info()$0\\\"],\\n\\t\\t\\\"description\\\": \\\"Read in copied tabular data on clipboard as pandas dataframe.\\\"\\n\\t},\\n\\t\\\"Pandas DataFrame Profiling\\\": {\\n\\t\\t\\\"prefix\\\": \\\"pdprofiling\\\",\\n\\t\\t\\\"body\\\": [\\\"pandas_profiling.ProfileReport(${1:df})$0\\\"],\\n\\t\\t\\\"description\\\": \\\"Visualize pandas dataframe using pandas-profiling.\\\"\\n\\t},\\n\\t\\\"Save Pandas DataFrame to File\\\": {\\n\\t\\t\\\"prefix\\\": \\\"dfsave\\\",\\n\\t\\t\\\"body\\\": [\\\"${1:df}.to_${2:csv}('${3:file_name}.${2:csv}', index=${3|False, True|})$0\\\"],\\n\\t\\t\\\"description\\\": \\\"Save dataframe to a flat file.\\\"\\n\\t},\\n\\t\\\"Save Pandas DataFrame to Compressed File\\\": {\\n\\t\\t\\\"prefix\\\": \\\"dfsavezip\\\",\\n\\t\\t\\\"body\\\": [\\\"${1:df}.to_csv('${2:file_name}.csv.zip', index=${3|False, True|}, compression='zip')$0\\\"],\\n\\t\\t\\\"description\\\": \\\"Compress and save dataframe to a zipped flat file (.csv.zip).\\\"\\n\\t},\\n\\t\\\"Modify Pandas DataFrame Column Name\\\": {\\n\\t\\t\\\"prefix\\\": \\\"dfmc\\\",\\n\\t\\t\\\"body\\\": [\\\"${1:df}['${2:column}'] = ${1:df}['${2:column}']$0\\\"],\\n\\t\\t\\\"description\\\": \\\"Modify a pandas column name.\\\"\\n\\t},\\n\\t\\\"if __name__ == \\\\\\\"__main__\\\\\\\"\\\": {\\n\\t\\t\\\"prefix\\\": \\\"ifmain\\\",\\n\\t\\t\\\"body\\\": [\\\"if __name__ == \\\\\\\"__main__\\\\\\\":\\\", \\\"\\\\t$0\\\"],\\n\\t\\t\\\"description\\\": \\\"if __name__ == \\\\\\\"__main__\\\\\\\" structurre (Python Language Basics).\\\"\\n\\t},\\n\\t\\\"If Statement\\\": {\\n\\t\\t\\\"prefix\\\": \\\"if\\\",\\n\\t\\t\\\"body\\\": [\\\"if ${1:condition}:\\\", \\\"\\\\t${2:pass}$0\\\"],\\n\\t\\t\\\"description\\\" : \\\"If statement structure (Python Language Basics).\\\"\\n\\t},\\n\\t\\\"For Loop\\\": {\\n\\t\\t\\\"prefix\\\": \\\"for\\\",\\n\\t\\t\\\"body\\\": [\\\"for ${1:value} in ${2:iterable}:\\\", \\\"\\\\t${3:pass}$0\\\"],\\n\\t\\t\\\"description\\\" : \\\"For loop structure (Python Language Basics).\\\"\\n\\t},\\n\\t\\\"While Loop\\\": {\\n\\t\\t\\\"prefix\\\": \\\"while\\\",\\n\\t\\t\\\"body\\\": [\\\"while ${1:condition}:\\\", \\\"\\\\t${2:pass}$0\\\"],\\n\\t\\t\\\"description\\\" : \\\"While loop structure (Python Language Basics).\\\"\\n\\t},\\n\\t\\\"New Function\\\": {\\n\\t\\t\\\"prefix\\\": \\\"def\\\",\\n\\t\\t\\\"body\\\": [\\\"def ${1:function_name}(${2:args}):\\\", \\\"\\\\t$0\\\"],\\n\\t\\t\\\"description\\\" : \\\"Function structure (Python Language Basics).\\\"\\n\\t},\\n\\t\\\"New Method\\\": {\\n\\t\\t\\\"prefix\\\": \\\"defs\\\",\\n\\t\\t\\\"body\\\": [\\\"def ${1:method_name}(self, ${2:args}):\\\", \\\"\\\\t$0\\\"],\\n\\t\\t\\\"description\\\" : \\\"Method structure (Python Language Basics).\\\"\\n\\t}\\n}\"}}", "extensions": "[{\"identifier\":{\"id\":\"ms-toolsai.jupyter-keymap\",\"uuid\":\"9f6dc8db-620c-4844-b8c5-e74914f1be27\"},\"displayName\":\"<PERSON><PERSON>ter Keymap\",\"preRelease\":true},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-containers\",\"uuid\":\"93ce222b-5f6f-49b7-9ab1-a0463c6238df\"},\"displayName\":\"Dev Containers\",\"preRelease\":true},{\"identifier\":{\"id\":\"github.copilot\",\"uuid\":\"23c4aeee-f844-43cd-b53e-1113e483f1a6\"},\"displayName\":\"GitHub Copilot\",\"preRelease\":true},{\"identifier\":{\"id\":\"github.copilot-chat\",\"uuid\":\"7ec7d6e6-b89e-4cc5-a59b-d6c4d238246f\"},\"displayName\":\"GitHub Copilot Chat\",\"preRelease\":true},{\"identifier\":{\"id\":\"github.vscode-pull-request-github\",\"uuid\":\"69ddd764-339a-4ecc-97c1-9c4ece58e36d\"},\"displayName\":\"GitHub Pull Requests and Issues\"},{\"identifier\":{\"id\":\"ms-dotnettools.vscode-dotnet-runtime\",\"uuid\":\"1aab81a1-b3d9-4aef-976b-577d5d90fe3f\"},\"displayName\":\".NET Runtime Install Tool\"},{\"identifier\":{\"id\":\"ms-python.black-formatter\",\"uuid\":\"859e640c-c157-47da-8699-9080b81c8371\"},\"displayName\":\"Black Formatter\",\"preRelease\":true},{\"identifier\":{\"id\":\"ms-python.isort\",\"uuid\":\"4ad0ce32-ff3f-49f0-83b5-93e5dc00cfff\"},\"displayName\":\"isort\"},{\"identifier\":{\"id\":\"ms-python.python\",\"uuid\":\"f1f59ae4-9318-4f3c-a9b5-81b2eaa5f8a5\"},\"displayName\":\"Python\",\"preRelease\":true},{\"identifier\":{\"id\":\"ms-python.vscode-pylance\",\"uuid\":\"364d2426-116a-433a-a5d8-a5098dc3afbd\"},\"displayName\":\"Pylance\",\"preRelease\":true},{\"identifier\":{\"id\":\"ms-toolsai.datawrangler\",\"uuid\":\"de3960e6-b262-4565-8a1c-9870629f1ed9\"},\"displayName\":\"Data Wrangler (Preview)\"},{\"identifier\":{\"id\":\"ms-toolsai.jupyter\",\"uuid\":\"6c2f1801-1e7f-45b2-9b5c-7782f1e076e8\"},\"displayName\":\"Jupyter\"},{\"identifier\":{\"id\":\"ms-toolsai.jupyter-keymap\",\"uuid\":\"9f6dc8db-620c-4844-b8c5-e74914f1be27\"},\"displayName\":\"Jupyter Keymap\"},{\"identifier\":{\"id\":\"ms-toolsai.jupyter-renderers\",\"uuid\":\"b15c72f8-d5fe-421a-a4f7-27ed9f6addbf\"},\"displayName\":\"Jupyter Notebook Renderers\",\"preRelease\":true},{\"identifier\":{\"id\":\"ms-toolsai.vscode-jupyter-cell-tags\",\"uuid\":\"ab4fb32a-befb-4102-adf9-1652d0cd6a5e\"},\"displayName\":\"Jupyter Cell Tags\",\"preRelease\":true},{\"identifier\":{\"id\":\"ms-toolsai.vscode-jupyter-slideshow\",\"uuid\":\"e153ca70-b543-4865-b4c5-b31d34185948\"},\"displayName\":\"Jupyter Slide Show\",\"preRelease\":true}]", "globalState": "{\"storage\":{\"workbench.panel.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.panel.markers\\\",\\\"name\\\":\\\"Problems\\\",\\\"pinned\\\":true,\\\"order\\\":0,\\\"visible\\\":true},{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"name\\\":\\\"Output\\\",\\\"pinned\\\":true,\\\"order\\\":1,\\\"visible\\\":true},{\\\"id\\\":\\\"workbench.panel.repl\\\",\\\"name\\\":\\\"Debug Console\\\",\\\"pinned\\\":true,\\\"order\\\":2,\\\"visible\\\":true},{\\\"id\\\":\\\"workbench.panel.testResults\\\",\\\"name\\\":\\\"Test Results\\\",\\\"pinned\\\":true,\\\"order\\\":3,\\\"visible\\\":false},{\\\"id\\\":\\\"terminal\\\",\\\"name\\\":\\\"Terminal\\\",\\\"pinned\\\":true,\\\"order\\\":3,\\\"visible\\\":true},{\\\"id\\\":\\\"~remote.forwardedPortsContainer\\\",\\\"name\\\":\\\"Ports\\\",\\\"pinned\\\":true,\\\"order\\\":5,\\\"visible\\\":true},{\\\"id\\\":\\\"workbench.view.extension.sqltoolsPanelContainer\\\",\\\"name\\\":\\\"SQL Console\\\",\\\"pinned\\\":true,\\\"order\\\":6,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.extension.azurePanel\\\",\\\"name\\\":\\\"Azure\\\",\\\"pinned\\\":true,\\\"order\\\":6,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.extension.jupyter-variables\\\",\\\"name\\\":\\\"Jupyter\\\",\\\"pinned\\\":true,\\\"order\\\":7,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.extension.AskJarvisPanel\\\",\\\"name\\\":\\\"AskJarvis\\\",\\\"pinned\\\":true,\\\"order\\\":8,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.extension.gitlensPanel\\\",\\\"name\\\":\\\"GitLens\\\",\\\"pinned\\\":true,\\\"order\\\":6,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.extension.data-wrangler-secondary\\\",\\\"name\\\":\\\"Data Wrangler\\\",\\\"pinned\\\":true,\\\"order\\\":6,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"name\\\":\\\"Comments\\\",\\\"pinned\\\":true,\\\"order\\\":10,\\\"visible\\\":false},{\\\"id\\\":\\\"refactorPreview\\\",\\\"name\\\":\\\"Refactor Preview\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\",\"workbench.view.extension.jupyter.state.hidden\":\"[{\\\"id\\\":\\\"cell-tag\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jupyterKernelsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jupyterContextualHelp\\\",\\\"isHidden\\\":false}]\",\"workbench.explorer.views.state.hidden\":\"[{\\\"id\\\":\\\"workbench.explorer.openEditorsView\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.explorer.fileView\\\",\\\"isHidden\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"jupyter-data-wrangler-code-preview-view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jupyterViewVariables\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"npm\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"liveshare.session.explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"pythonEnvironments\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerImages\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerRegistries\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerNetworks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerVolumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.dockerContexts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.help\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jupyter-data-wrangler-operations-view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jupyter-data-wrangler-summary-view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jupyter-data-wrangler-cleaning-steps-view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cell-tag\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jupyterKernelsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jupyterContextualHelp\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"AzureMLExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.session\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.contacts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.help\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.devtools\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.emptyView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dataworkspace.views.main\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"objectExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"queryHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"angular-schematics\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-edge-devtools-view.targets\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-edge-devtools-view.help-links\\\",\\\"isHidden\\\":false}]\",\"workbench.activity.pinnedViewlets2\":\"[{\\\"id\\\":\\\"workbench.view.explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.view.search\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.view.extension.acrolinx-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.scm\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.view.debug\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.extension.npm\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.panel.aiSidebar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.view.extension.bookmarks\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extensions\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.remote\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extension.mongoDB\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.testing\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":5},{\\\"id\\\":\\\"workbench.view.extension.todo-tree-container\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.vscode-edge-devtools-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.test\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.azuresphere\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.copilot-sidebar-webview\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.ts-playground\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.teamsfx\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.ObjectScriptView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.frontmatter-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.view.extension.openapi-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.copilot\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.42crunch-platform\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.thunder-client\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.PowerShell\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":23},{\\\"id\\\":\\\"workbench.view.extension.wallaby\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.hexExplorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.github-actions\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.jsonEditor-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.references-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.stripe\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.cmake__viewContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.browser-preview\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.PowerShellCommandExplorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.nuget-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.graalvm-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.sqltoolsActivityBarContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.github-pull-requests\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.cspell-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.github-pull-request\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.kubernetesView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.nx-console\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.node-notebook\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.workspaceViewer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.flutter\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.gitlens\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.dockerView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.music-time\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.actions\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.powerShellProTools\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.view.extension.githd-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.code-time\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.platformio\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.angular\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.jupyter\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.panel.interactiveSessionSidebar.copilot\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100},{\\\"id\\\":\\\"workbench.view.extension.package-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.data-wrangler-primary\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.project-manager\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.azure\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.panel.markers\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.view.extension.liveshare\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.sln_explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.panel.terminal\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":22},{\\\"id\\\":\\\"workbench.view.extension.testExplorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.panel.repl\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":24},{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.view.extension.blockchain-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":18},{\\\"id\\\":\\\"workbench.view.extension.CppRenameActivityBar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":20},{\\\"id\\\":\\\"workbench.view.extension.VirtualGistsContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.opensshremotesexplorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":21},{\\\"id\\\":\\\"workbench.view.extension.l13Projects\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":20},{\\\"id\\\":\\\"workbench.view.extension.unotes\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.latex\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.todo\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":16},{\\\"id\\\":\\\"workbench.view.extension.pwa\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.view.extension.dataworkspace\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.objectExplorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.codestream-activitybar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.copilot-labs\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.panel.chatSidebar.copilot\\\",\\\"pinned\\\":true,\\\"visible\\\":true,\\\"order\\\":100},{\\\"id\\\":\\\"workbench.view.extension.snippet-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.tree-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.1-kubernetesContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.latex-workshop-activitybar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.favorites-bar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":20},{\\\"id\\\":\\\"workbench.view.extension.log-viewer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.view.extension.favorites-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":24},{\\\"id\\\":\\\"workbench.view.extension.gitlab-workflow\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.chatActivityViewSlack\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":17},{\\\"id\\\":\\\"workbench.view.extension.chatActivityViewDiscord\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":18},{\\\"id\\\":\\\"workbench.view.extension.appmap\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.azdo-pull-requests\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":32},{\\\"id\\\":\\\"workbench.view.extension.2-cloudRunContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.azure-policy\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.thunder-client-sidebar-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":27},{\\\"id\\\":\\\"workbench.view.extension.snippView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":29},{\\\"id\\\":\\\"workbench.view.extension.ionide-fsharp\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":29},{\\\"id\\\":\\\"workbench.view.extension.swaggerhub\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":29},{\\\"id\\\":\\\"workbench.view.extension.taskActivity\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":26},{\\\"id\\\":\\\"workbench.view.extension.gistpad\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":29},{\\\"id\\\":\\\"workbench.view.extension.VirtualRepositories\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.Repositories\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.VirtualRepositoriesContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.3-cloudApiContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.workspace-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":28},{\\\"id\\\":\\\"workbench.view.extension.snippetsManager\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.iridium\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.gitops\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":30},{\\\"id\\\":\\\"workbench.view.extension.4-cloudSecretsContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.view.extension.bloop-search-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":34},{\\\"id\\\":\\\"workbench.view.extension.microsoft-todo-unoffcial\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":29},{\\\"id\\\":\\\"workbench.view.extension.5-apigeeContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.spring\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.editSessions\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.sync\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"userDataProfiles\\\",\\\"pinned\\\":true,\\\"visible\\\":true}]\",\"memento/gettingStartedService\":\"{\\\"settings\\\":{\\\"done\\\":true},\\\"commandPaletteTask\\\":{\\\"done\\\":true},\\\"settingsSync\\\":{\\\"done\\\":true},\\\"quickOpen\\\":{\\\"done\\\":true},\\\"installGit\\\":{\\\"done\\\":true},\\\"commandPaletteTaskWeb\\\":{\\\"done\\\":true},\\\"pickColorTheme\\\":{\\\"done\\\":true},\\\"pickColorThemeWeb\\\":{\\\"done\\\":true},\\\"ms-toolsai.jupyter#jupyterWelcome#ipynb.newUntitledIpynb\\\":{\\\"done\\\":true},\\\"ms-toolsai.datawrangler#dataWranglerWelcome#openDataWrangler\\\":{\\\"done\\\":true},\\\"GitHub.copilot#copilotWelcome#copilot.signin\\\":{\\\"done\\\":true}}\",\"workbench.view.extensions.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.extensions.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchOutdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.workspaceRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.popular\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchRecentlyUpdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.otherRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"extensions.recommendedList\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.enabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.disabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.marketplace\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchInstalled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchEnabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchDisabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchBuiltin\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchWorkspaceUnsupported\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinFeatureExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinThemeExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinProgrammingLanguageExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.deprecatedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.local.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.remote.installed\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.repl.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.repl.view\\\",\\\"isHidden\\\":false}]\",\"colorThemeData\":\"{\\\"id\\\":\\\"vs-dark vscode-theme-defaults-themes-dark_modern-json\\\",\\\"label\\\":\\\"Dark Modern\\\",\\\"settingsId\\\":\\\"Default Dark Modern\\\",\\\"themeTokenColors\\\":[{\\\"settings\\\":{\\\"foreground\\\":\\\"#D4D4D4\\\"},\\\"scope\\\":[\\\"meta.embedded\\\",\\\"source.groovy.embedded\\\",\\\"string meta.image.inline.markdown\\\",\\\"variable.legacy.builtin.python\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"emphasis\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":\\\"strong\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000080\\\"},\\\"scope\\\":\\\"header\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6A9955\\\"},\\\"scope\\\":\\\"comment\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"constant.language\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":[\\\"constant.numeric\\\",\\\"variable.other.enummember\\\",\\\"keyword.operator.plus.exponent\\\",\\\"keyword.operator.minus.exponent\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#646695\\\"},\\\"scope\\\":\\\"constant.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"entity.name.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":\\\"entity.name.tag.css\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"entity.other.attribute-name\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":[\\\"entity.other.attribute-name.class.css\\\",\\\"entity.other.attribute-name.class.mixin.css\\\",\\\"entity.other.attribute-name.id.css\\\",\\\"entity.other.attribute-name.parent-selector.css\\\",\\\"entity.other.attribute-name.pseudo-class.css\\\",\\\"entity.other.attribute-name.pseudo-element.css\\\",\\\"source.css.less entity.other.attribute-name.id\\\",\\\"entity.other.attribute-name.scss\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#f44747\\\"},\\\"scope\\\":\\\"invalid\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"underline\\\"},\\\"scope\\\":\\\"markup.underline\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.bold\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.heading\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"markup.italic\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"strikethrough\\\"},\\\"scope\\\":\\\"markup.strikethrough\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"markup.inserted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"markup.deleted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.changed\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6A9955\\\"},\\\"scope\\\":\\\"punctuation.definition.quote.begin.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6796e6\\\"},\\\"scope\\\":\\\"punctuation.definition.list.begin.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"markup.inline.raw\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#808080\\\"},\\\"scope\\\":\\\"punctuation.definition.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"meta.preprocessor\\\",\\\"entity.name.function.preprocessor\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"meta.preprocessor.string\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"meta.preprocessor.numeric\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"meta.structure.dictionary.key.python\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"meta.diff.header\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"storage\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"storage.type\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"storage.modifier\\\",\\\"keyword.operator.noexcept\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":[\\\"string\\\",\\\"meta.embedded.assembly\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"string.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"string.value\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d16969\\\"},\\\"scope\\\":\\\"string.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"punctuation.definition.template-expression.begin\\\",\\\"punctuation.definition.template-expression.end\\\",\\\"punctuation.section.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":[\\\"meta.template.expression\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":[\\\"support.type.vendored.property-name\\\",\\\"support.type.property-name\\\",\\\"variable.css\\\",\\\"variable.scss\\\",\\\"variable.other.less\\\",\\\"source.coffee.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"keyword\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"keyword.control\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":\\\"keyword.operator\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"keyword.operator.new\\\",\\\"keyword.operator.expression\\\",\\\"keyword.operator.cast\\\",\\\"keyword.operator.sizeof\\\",\\\"keyword.operator.alignof\\\",\\\"keyword.operator.typeid\\\",\\\"keyword.operator.alignas\\\",\\\"keyword.operator.instanceof\\\",\\\"keyword.operator.logical.python\\\",\\\"keyword.operator.wordlike\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"keyword.other.unit\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"punctuation.section.embedded.begin.php\\\",\\\"punctuation.section.embedded.end.php\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"support.function.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"constant.sha.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":[\\\"storage.modifier.import.java\\\",\\\"variable.language.wildcard.java\\\",\\\"storage.modifier.package.java\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"variable.language\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#DCDCAA\\\"},\\\"scope\\\":[\\\"entity.name.function\\\",\\\"support.function\\\",\\\"support.constant.handlebars\\\",\\\"source.powershell variable.other.member\\\",\\\"entity.name.operator.custom-literal\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#4EC9B0\\\"},\\\"scope\\\":[\\\"support.class\\\",\\\"support.type\\\",\\\"entity.name.type\\\",\\\"entity.name.namespace\\\",\\\"entity.other.attribute\\\",\\\"entity.name.scope-resolution\\\",\\\"entity.name.class\\\",\\\"storage.type.numeric.go\\\",\\\"storage.type.byte.go\\\",\\\"storage.type.boolean.go\\\",\\\"storage.type.string.go\\\",\\\"storage.type.uintptr.go\\\",\\\"storage.type.error.go\\\",\\\"storage.type.rune.go\\\",\\\"storage.type.cs\\\",\\\"storage.type.generic.cs\\\",\\\"storage.type.modifier.cs\\\",\\\"storage.type.variable.cs\\\",\\\"storage.type.annotation.java\\\",\\\"storage.type.generic.java\\\",\\\"storage.type.java\\\",\\\"storage.type.object.array.java\\\",\\\"storage.type.primitive.array.java\\\",\\\"storage.type.primitive.java\\\",\\\"storage.type.token.java\\\",\\\"storage.type.groovy\\\",\\\"storage.type.annotation.groovy\\\",\\\"storage.type.parameters.groovy\\\",\\\"storage.type.generic.groovy\\\",\\\"storage.type.object.array.groovy\\\",\\\"storage.type.primitive.array.groovy\\\",\\\"storage.type.primitive.groovy\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#4EC9B0\\\"},\\\"scope\\\":[\\\"meta.type.cast.expr\\\",\\\"meta.type.new.expr\\\",\\\"support.constant.math\\\",\\\"support.constant.dom\\\",\\\"support.constant.json\\\",\\\"entity.other.inherited-class\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C586C0\\\"},\\\"scope\\\":[\\\"keyword.control\\\",\\\"source.cpp keyword.operator.new\\\",\\\"keyword.operator.delete\\\",\\\"keyword.other.using\\\",\\\"keyword.other.directive.using\\\",\\\"keyword.other.operator\\\",\\\"entity.name.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9CDCFE\\\"},\\\"scope\\\":[\\\"variable\\\",\\\"meta.definition.variable.name\\\",\\\"support.variable\\\",\\\"entity.name.variable\\\",\\\"constant.other.placeholder\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#4FC1FF\\\"},\\\"scope\\\":[\\\"variable.other.constant\\\",\\\"variable.other.enummember\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9CDCFE\\\"},\\\"scope\\\":[\\\"meta.object-literal.key\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#CE9178\\\"},\\\"scope\\\":[\\\"support.constant.property-value\\\",\\\"support.constant.font-name\\\",\\\"support.constant.media-type\\\",\\\"support.constant.media\\\",\\\"constant.other.color.rgb-value\\\",\\\"constant.other.rgb-value\\\",\\\"support.constant.color\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#CE9178\\\"},\\\"scope\\\":[\\\"punctuation.definition.group.regexp\\\",\\\"punctuation.definition.group.assertion.regexp\\\",\\\"punctuation.definition.character-class.regexp\\\",\\\"punctuation.character.set.begin.regexp\\\",\\\"punctuation.character.set.end.regexp\\\",\\\"keyword.operator.negation.regexp\\\",\\\"support.other.parenthesis.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d16969\\\"},\\\"scope\\\":[\\\"constant.character.character-class.regexp\\\",\\\"constant.other.character-class.set.regexp\\\",\\\"constant.other.character-class.regexp\\\",\\\"constant.character.set.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#DCDCAA\\\"},\\\"scope\\\":[\\\"keyword.operator.or.regexp\\\",\\\"keyword.control.anchor.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":\\\"keyword.operator.quantifier.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"constant.character\\\",\\\"constant.other.option\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":\\\"constant.character.escape\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C8C8C8\\\"},\\\"scope\\\":\\\"entity.name.label\\\"}],\\\"semanticTokenRules\\\":[{\\\"_selector\\\":\\\"newOperator\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#d4d4d4\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"stringLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ce9178\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"customLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#d4d4d4\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"numberLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#b5cea8\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"newOperator\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#c586c0\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"stringLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ce9178\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"customLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#dcdcaa\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"numberLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#b5cea8\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}}],\\\"extensionData\\\":{\\\"_extensionId\\\":\\\"vscode.theme-defaults\\\",\\\"_extensionIsBuiltin\\\":true,\\\"_extensionName\\\":\\\"theme-defaults\\\",\\\"_extensionPublisher\\\":\\\"vscode\\\"},\\\"themeSemanticHighlighting\\\":true,\\\"colorMap\\\":{\\\"checkbox.border\\\":\\\"#3c3c3c\\\",\\\"editor.background\\\":\\\"#1f1f1f\\\",\\\"editor.foreground\\\":\\\"#cccccc\\\",\\\"editor.inactiveSelectionBackground\\\":\\\"#3a3d41\\\",\\\"editorIndentGuide.background\\\":\\\"#404040\\\",\\\"editorIndentGuide.activeBackground\\\":\\\"#707070\\\",\\\"editor.selectionHighlightBackground\\\":\\\"#add6ff26\\\",\\\"list.dropBackground\\\":\\\"#383b3d\\\",\\\"activityBarBadge.background\\\":\\\"#0078d4\\\",\\\"sideBarTitle.foreground\\\":\\\"#cccccc\\\",\\\"input.placeholderForeground\\\":\\\"#9d9d9d\\\",\\\"menu.background\\\":\\\"#1f1f1f\\\",\\\"menu.foreground\\\":\\\"#cccccc\\\",\\\"menu.separatorBackground\\\":\\\"#454545\\\",\\\"menu.border\\\":\\\"#454545\\\",\\\"statusBarItem.remoteForeground\\\":\\\"#ffffff\\\",\\\"statusBarItem.remoteBackground\\\":\\\"#0078d4\\\",\\\"ports.iconRunningProcessForeground\\\":\\\"#369432\\\",\\\"sideBarSectionHeader.background\\\":\\\"#181818\\\",\\\"sideBarSectionHeader.border\\\":\\\"#2b2b2b\\\",\\\"tab.lastPinnedBorder\\\":\\\"#cccccc33\\\",\\\"list.activeSelectionIconForeground\\\":\\\"#ffffff\\\",\\\"terminal.inactiveSelectionBackground\\\":\\\"#3a3d41\\\",\\\"widget.border\\\":\\\"#313131\\\",\\\"actionBar.toggledBackground\\\":\\\"#383a49\\\",\\\"activityBar.activeBorder\\\":\\\"#0078d4\\\",\\\"activityBar.background\\\":\\\"#181818\\\",\\\"activityBar.border\\\":\\\"#2b2b2b\\\",\\\"activityBar.foreground\\\":\\\"#d7d7d7\\\",\\\"activityBar.inactiveForeground\\\":\\\"#868686\\\",\\\"activityBarBadge.foreground\\\":\\\"#ffffff\\\",\\\"badge.background\\\":\\\"#616161\\\",\\\"badge.foreground\\\":\\\"#f8f8f8\\\",\\\"button.background\\\":\\\"#0078d4\\\",\\\"button.border\\\":\\\"#ffffff12\\\",\\\"button.foreground\\\":\\\"#ffffff\\\",\\\"button.hoverBackground\\\":\\\"#026ec1\\\",\\\"button.secondaryBackground\\\":\\\"#313131\\\",\\\"button.secondaryForeground\\\":\\\"#cccccc\\\",\\\"button.secondaryHoverBackground\\\":\\\"#3c3c3c\\\",\\\"chat.slashCommandBackground\\\":\\\"#34414b\\\",\\\"chat.slashCommandForeground\\\":\\\"#40a6ff\\\",\\\"checkbox.background\\\":\\\"#313131\\\",\\\"debugToolBar.background\\\":\\\"#181818\\\",\\\"descriptionForeground\\\":\\\"#9d9d9d\\\",\\\"dropdown.background\\\":\\\"#313131\\\",\\\"dropdown.border\\\":\\\"#3c3c3c\\\",\\\"dropdown.foreground\\\":\\\"#cccccc\\\",\\\"dropdown.listBackground\\\":\\\"#1f1f1f\\\",\\\"editor.findMatchBackground\\\":\\\"#9e6a03\\\",\\\"editorGroup.border\\\":\\\"#ffffff17\\\",\\\"editorGroupHeader.tabsBackground\\\":\\\"#181818\\\",\\\"editorGroupHeader.tabsBorder\\\":\\\"#2b2b2b\\\",\\\"editorGutter.addedBackground\\\":\\\"#2ea043\\\",\\\"editorGutter.deletedBackground\\\":\\\"#f85149\\\",\\\"editorGutter.modifiedBackground\\\":\\\"#0078d4\\\",\\\"editorLineNumber.activeForeground\\\":\\\"#cccccc\\\",\\\"editorLineNumber.foreground\\\":\\\"#6e7681\\\",\\\"editorOverviewRuler.border\\\":\\\"#010409\\\",\\\"editorWidget.background\\\":\\\"#202020\\\",\\\"errorForeground\\\":\\\"#f85149\\\",\\\"focusBorder\\\":\\\"#0078d4\\\",\\\"foreground\\\":\\\"#cccccc\\\",\\\"icon.foreground\\\":\\\"#cccccc\\\",\\\"input.background\\\":\\\"#313131\\\",\\\"input.border\\\":\\\"#3c3c3c\\\",\\\"input.foreground\\\":\\\"#cccccc\\\",\\\"inputOption.activeBackground\\\":\\\"#2489db82\\\",\\\"inputOption.activeBorder\\\":\\\"#2488db\\\",\\\"keybindingLabel.foreground\\\":\\\"#cccccc\\\",\\\"notificationCenterHeader.background\\\":\\\"#1f1f1f\\\",\\\"notificationCenterHeader.foreground\\\":\\\"#cccccc\\\",\\\"notifications.background\\\":\\\"#1f1f1f\\\",\\\"notifications.border\\\":\\\"#2b2b2b\\\",\\\"notifications.foreground\\\":\\\"#cccccc\\\",\\\"panel.background\\\":\\\"#181818\\\",\\\"panel.border\\\":\\\"#2b2b2b\\\",\\\"panelInput.border\\\":\\\"#2b2b2b\\\",\\\"panelTitle.activeBorder\\\":\\\"#0078d4\\\",\\\"panelTitle.activeForeground\\\":\\\"#cccccc\\\",\\\"panelTitle.inactiveForeground\\\":\\\"#9d9d9d\\\",\\\"peekViewEditor.background\\\":\\\"#1f1f1f\\\",\\\"peekViewEditor.matchHighlightBackground\\\":\\\"#bb800966\\\",\\\"peekViewResult.background\\\":\\\"#1f1f1f\\\",\\\"peekViewResult.matchHighlightBackground\\\":\\\"#bb800966\\\",\\\"pickerGroup.border\\\":\\\"#3c3c3c\\\",\\\"progressBar.background\\\":\\\"#0078d4\\\",\\\"quickInput.background\\\":\\\"#222222\\\",\\\"quickInput.foreground\\\":\\\"#cccccc\\\",\\\"settings.dropdownBackground\\\":\\\"#313131\\\",\\\"settings.dropdownBorder\\\":\\\"#3c3c3c\\\",\\\"settings.headerForeground\\\":\\\"#ffffff\\\",\\\"settings.modifiedItemIndicator\\\":\\\"#bb800966\\\",\\\"sideBar.background\\\":\\\"#181818\\\",\\\"sideBar.border\\\":\\\"#2b2b2b\\\",\\\"sideBar.foreground\\\":\\\"#cccccc\\\",\\\"sideBarSectionHeader.foreground\\\":\\\"#cccccc\\\",\\\"statusBar.background\\\":\\\"#181818\\\",\\\"statusBar.border\\\":\\\"#2b2b2b\\\",\\\"statusBar.debuggingBackground\\\":\\\"#0078d4\\\",\\\"statusBar.debuggingForeground\\\":\\\"#ffffff\\\",\\\"statusBar.focusBorder\\\":\\\"#0078d4\\\",\\\"statusBar.foreground\\\":\\\"#cccccc\\\",\\\"statusBar.noFolderBackground\\\":\\\"#1f1f1f\\\",\\\"statusBarItem.focusBorder\\\":\\\"#0078d4\\\",\\\"statusBarItem.prominentBackground\\\":\\\"#6e768166\\\",\\\"tab.activeBackground\\\":\\\"#1f1f1f\\\",\\\"tab.activeBorder\\\":\\\"#1f1f1f\\\",\\\"tab.activeBorderTop\\\":\\\"#0078d4\\\",\\\"tab.activeForeground\\\":\\\"#ffffff\\\",\\\"tab.border\\\":\\\"#2b2b2b\\\",\\\"tab.hoverBackground\\\":\\\"#1f1f1f\\\",\\\"tab.inactiveBackground\\\":\\\"#181818\\\",\\\"tab.inactiveForeground\\\":\\\"#9d9d9d\\\",\\\"tab.unfocusedActiveBorder\\\":\\\"#1f1f1f\\\",\\\"tab.unfocusedActiveBorderTop\\\":\\\"#2b2b2b\\\",\\\"tab.unfocusedHoverBackground\\\":\\\"#1f1f1f\\\",\\\"terminal.foreground\\\":\\\"#cccccc\\\",\\\"terminal.tab.activeBorder\\\":\\\"#0078d4\\\",\\\"textBlockQuote.background\\\":\\\"#2b2b2b\\\",\\\"textBlockQuote.border\\\":\\\"#616161\\\",\\\"textCodeBlock.background\\\":\\\"#2b2b2b\\\",\\\"textLink.activeForeground\\\":\\\"#40a6ff\\\",\\\"textLink.foreground\\\":\\\"#40a6ff\\\",\\\"textSeparator.foreground\\\":\\\"#21262d\\\",\\\"titleBar.activeBackground\\\":\\\"#181818\\\",\\\"titleBar.activeForeground\\\":\\\"#cccccc\\\",\\\"titleBar.border\\\":\\\"#2b2b2b\\\",\\\"titleBar.inactiveBackground\\\":\\\"#1f1f1f\\\",\\\"titleBar.inactiveForeground\\\":\\\"#9d9d9d\\\",\\\"welcomePage.tileBackground\\\":\\\"#2b2b2b\\\",\\\"welcomePage.progress.foreground\\\":\\\"#0078d4\\\"},\\\"watch\\\":false}\",\"userDataProfiles.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.profiles.export.preview\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.profiles.import.preview\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.jupyter-variables.state.hidden\":\"[{\\\"id\\\":\\\"jupyterViewVariables\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cell-tag\\\",\\\"isHidden\\\":false}]\",\"workbench.welcomePage.walkthroughMetadata\":\"[[\\\"ms-azuretools.vscode-azurefunctions#functionsStart\\\",{\\\"firstSeen\\\":1689881204923,\\\"stepIDs\\\":[\\\"scenarios\\\",\\\"create\\\",\\\"initialize\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-azuretools.vscode-azureresourcegroups#azure-get-started\\\",{\\\"firstSeen\\\":1689881204923,\\\"stepIDs\\\":[\\\"sign-in\\\",\\\"azure-view\\\",\\\"grouping\\\",\\\"activity-log\\\",\\\"create-resource\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-azuretools.azure-dev#azd.start\\\",{\\\"firstSeen\\\":1690392923123,\\\"stepIDs\\\":[\\\"azd.install\\\",\\\"azd.login\\\",\\\"azd.scaffold\\\",\\\"azd.up\\\",\\\"azd.explore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-azuretools.vscode-docker#dockerStart\\\",{\\\"firstSeen\\\":1690989084571,\\\"stepIDs\\\":[\\\"openFolder\\\",\\\"openFolderMac\\\",\\\"scaffold\\\",\\\"buildImage\\\",\\\"runContainer\\\",\\\"dockerExplorer\\\",\\\"pushImage\\\",\\\"azDeploy\\\",\\\"learn\\\"],\\\"manaullyOpened\\\":false}],[\\\"GitHub.copilot#copilotWelcome\\\",{\\\"firstSeen\\\":1694502767737,\\\"stepIDs\\\":[\\\"copilot.signin\\\",\\\"copilot.firstsuggest\\\",\\\"copilot.realfiles.openrecent\\\",\\\"copilot.realfiles.quickopen\\\",\\\"copilot.iterate\\\",\\\"copilot.creativity\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-azuretools.vscode-bicep#gettingStarted\\\",{\\\"firstSeen\\\":1694502786219,\\\"stepIDs\\\":[\\\"openBicepFile\\\",\\\"addParameters\\\",\\\"addResources\\\",\\\"visualizeYourResources\\\",\\\"deployToAzure\\\",\\\"learnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-toolsai.jupyter#jupyterWelcome\\\",{\\\"firstSeen\\\":1695676040318,\\\"stepIDs\\\":[\\\"ipynb.newUntitledIpynb\\\",\\\"jupyter.selectKernel\\\",\\\"jupyter.exploreAndDebug\\\",\\\"jupyter.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonWelcome\\\",{\\\"firstSeen\\\":1695676057793,\\\"stepIDs\\\":[\\\"python.createPythonFile\\\",\\\"python.installPythonWin8\\\",\\\"python.installPythonMac\\\",\\\"python.installPythonLinux\\\",\\\"python.selectInterpreter\\\",\\\"python.createEnvironment\\\",\\\"python.runAndDebug\\\",\\\"python.learnMoreWithDS\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonWelcome2\\\",{\\\"firstSeen\\\":1695676057793,\\\"stepIDs\\\":[\\\"python.createPythonFolder\\\",\\\"python.createPythonFile\\\",\\\"python.installPythonWin8\\\",\\\"python.installPythonMac\\\",\\\"python.installPythonLinux\\\",\\\"python.createEnvironment2\\\",\\\"python.runAndDebug\\\",\\\"python.learnMoreWithDS2\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonDataScienceWelcome\\\",{\\\"firstSeen\\\":1695676057793,\\\"stepIDs\\\":[\\\"python.installJupyterExt\\\",\\\"python.createNewNotebook\\\",\\\"python.openInteractiveWindow\\\",\\\"python.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-toolsai.datawrangler#dataWranglerWelcome\\\",{\\\"firstSeen\\\":1695676104936,\\\"stepIDs\\\":[\\\"openDataWrangler\\\",\\\"cleanDataUsingOperations\\\",\\\"applyYourChanges\\\",\\\"exportYourCode\\\",\\\"openTitanicCSV\\\"],\\\"manaullyOpened\\\":false}]]\",\"workbench.panel.alignment\":\"center\",\"workbench.panel.markers.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.markers.view\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.output.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"isHidden\\\":false}]\",\"terminal.hidden\":\"[{\\\"id\\\":\\\"terminal\\\",\\\"isHidden\\\":false}]\",\"workbench.scm.views.state.hidden\":\"[{\\\"id\\\":\\\"workbench.scm.repositories\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.scm\\\",\\\"isHidden\\\":false}]\",\"workbench.view.search.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.search\\\",\\\"isHidden\\\":false}]\",\"workbench.view.debug.state.hidden\":\"[{\\\"id\\\":\\\"workbench.debug.welcome\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.variablesView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.watchExpressionsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.callStackView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.loadedScriptsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.breakPointsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsBrowserBreakpoints\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"jsExcludedCallers\\\",\\\"isHidden\\\":false}]\",\"memento/notebookGettingStarted2\":\"{\\\"hasOpenedNotebook\\\":true}\",\"workbench.telemetryOptOutShown\":\"true\",\"workbench.statusbar.hidden\":\"[\\\"status.workspaceTrust.d3e3461468b759049f655b213a910d5e\\\",\\\"status.workspaceTrust.1677516703016\\\",\\\"status.workspaceTrust.ad04817c9270b79a5e9c06a25ea6d2ac\\\",\\\"status.workspaceTrust.ac999970318a23058aeef3b42c12abe1\\\",\\\"status.workspaceTrust.72ed9eb713f8964080bce09c1c8790a2\\\"]\",\"fileBasedRecommendations/promptedRecommendations\":\"{\\\"python\\\":[\\\"ms-python.python\\\"]}\",\"workbench.view.extension.test.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.testing\\\",\\\"isHidden\\\":false}]\",\"workbench.view.remote.state.hidden\":\"[{\\\"id\\\":\\\"targetsContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"detailsContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"devVolumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"targetsWsl\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"remoteTargets\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github.codespaces.explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github.codespaces.warnExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github.codespaces.performanceExplorer\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-pull-requests.state.hidden\":\"[{\\\"id\\\":\\\"github:login\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"pr:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"issues:github\\\",\\\"isHidden\\\":false}]\",\"Comments.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-pull-request.state.hidden\":\"[{\\\"id\\\":\\\"github:createPullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChanges\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"prStatus:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest:welcome\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:createPullRequestWebview\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChangesFiles\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChangesCommits\\\",\\\"isHidden\\\":false}]\",\"commandPalette.mru.cache\":\"{\\\"usesLRU\\\":true,\\\"entries\\\":[{\\\"key\\\":\\\"workbench.action.selectTheme\\\",\\\"value\\\":10},{\\\"key\\\":\\\"workbench.action.openSettings2\\\",\\\"value\\\":11},{\\\"key\\\":\\\"workbench.action.toggleAutoSave\\\",\\\"value\\\":13},{\\\"key\\\":\\\"workbench.action.reloadWindow\\\",\\\"value\\\":15},{\\\"key\\\":\\\"editor.action.formatDocument.multiple\\\",\\\"value\\\":19},{\\\"key\\\":\\\"editor.action.formatDocument\\\",\\\"value\\\":20},{\\\"key\\\":\\\"mssql.manageProfiles\\\",\\\"value\\\":21},{\\\"key\\\":\\\"workbench.action.openSnippets\\\",\\\"value\\\":23},{\\\"key\\\":\\\"workbench.action.openApplicationSettingsJson\\\",\\\"value\\\":24},{\\\"key\\\":\\\"workbench.action.openSettingsJson\\\",\\\"value\\\":28},{\\\"key\\\":\\\"jupyter.openOutlineView\\\",\\\"value\\\":29}]}\",\"commandPalette.mru.counter\":\"30\",\"workbench.auxiliarybar.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.views.service.auxiliarybar.e9a1d7e9-545b-47af-baa7-a74cbb7b332e\\\",\\\"name\\\":\\\"Outline\\\",\\\"pinned\\\":true,\\\"visible\\\":true}]\",\"views.customizations\":\"{\\\"viewContainerLocations\\\":{\\\"workbench.views.service.auxiliarybar.e9a1d7e9-545b-47af-baa7-a74cbb7b332e\\\":2},\\\"viewLocations\\\":{\\\"outline\\\":\\\"workbench.views.service.auxiliarybar.e9a1d7e9-545b-47af-baa7-a74cbb7b332e\\\"},\\\"viewContainerBadgeEnablementStates\\\":{}}\",\"workbench.views.service.auxiliarybar.e9a1d7e9-545b-47af-baa7-a74cbb7b332e.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.objectExplorer.state.hidden\":\"[{\\\"id\\\":\\\"objectExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"queryHistory\\\",\\\"isHidden\\\":false}]\",\"workbench.views.service.auxiliarybar.4431b602-6cfa-4ff9-81b6-8d16f8780f32.state.hidden\":\"[{\\\"id\\\":\\\"spring.beans\\\",\\\"isHidden\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"spring.mappings\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.spring.state.hidden\":\"[{\\\"id\\\":\\\"spring.apps\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"spring.beans\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"spring.mappings\\\",\\\"isHidden\\\":false}]\",\"remote.tunnels.toRestore.dev-container+7b22686f737450617468223a222f55736572732f6e697479612f4769744875622f6e697479612f67656e657261746976652d61692d666f722d626567696e6e657273222c226c6f63616c446f636b6572223a66616c73652c2273657474696e6773223a7b22636f6e74657874223a226465736b746f702d6c696e7578227d2c22636f6e66696746696c65223a7b22246d6964223a312c22667350617468223a222f55736572732f6e697479612f4769744875622f6e697479612f67656e657261746976652d61692d666f722d626567696e6e6572732f2e646576636f6e7461696e65722f646576636f6e7461696e65722e6a736f6e222c2265787465726e616c223a2266696c653a2f2f2f55736572732f6e697479612f4769744875622f6e697479612f67656e657261746976652d61692d666f722d626567696e6e6572732f2e646576636f6e7461696e65722f646576636f6e7461696e65722e6a736f6e222c2270617468223a222f55736572732f6e697479612f4769744875622f6e697479612f67656e657261746976652d61692d666f722d626567696e6e6572732f2e646576636f6e7461696e65722f646576636f6e7461696e65722e6a736f6e222c22736368656d65223a2266696c65227d7d.70617042\":\"[{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":38227,\\\"localPort\\\":38227,\\\"closeable\\\":true,\\\"localAddress\\\":\\\"127.0.0.1:38227\\\",\\\"protocol\\\":\\\"http\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:38227\\\"},\\\"runningProcess\\\":\\\"/vscode/vscode-server-insiders/bin/linux-x64/bccfade64adb249f57c8fcf03cba41609f76ce5c-insider/node\\\\u0000--dns-result-order=ipv4first\\\\u0000/vscode/vscode-server-insiders/bin/linux-x64/bccfade64adb249f57c8fcf03cba41609f76ce5c-insider/out/bootstrap-fork\\\\u0000--type=extensionHost\\\\u0000--transformURIs\\\\u0000--useHostProxy=false\\\\u0000\\\",\\\"hasRunningProcess\\\":true,\\\"pid\\\":2536,\\\"source\\\":{\\\"source\\\":1,\\\"description\\\":\\\"Auto Forwarded\\\"},\\\"privacy\\\":\\\"private\\\"}]\",\"workbench.panel.chatSidebar.copilot.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false}]\"}}"}